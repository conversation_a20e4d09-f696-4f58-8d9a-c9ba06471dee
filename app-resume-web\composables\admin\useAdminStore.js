import { ref, computed, readonly } from 'vue'

/**
 * 管理员状态管理
 * 用于管理后台管理系统的状态数据
 */
export const useAdminStore = () => {
  // 管理员信息状态
  const adminInfo = ref(null)
  
  // 侧边栏折叠状态
  const sidebarCollapsed = ref(false)
  
  // 当前选中的菜单项
  const activeMenuItem = ref('')

  /**
   * 设置管理员信息
   */
  const setAdminInfo = (info) => {
    adminInfo.value = info
  }

  /**
   * 清除管理员信息
   */
  const clearAdminInfo = () => {
    adminInfo.value = null
  }

  /**
   * 切换侧边栏折叠状态
   */
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  /**
   * 设置侧边栏状态
   */
  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
  }

  /**
   * 设置当前活跃菜单项
   */
  const setActiveMenuItem = (menuKey) => {
    activeMenuItem.value = menuKey
  }

  /**
   * 检查是否为超级管理员
   */
  const isSuperAdmin = computed(() => {
    return adminInfo.value?.roles?.includes('SUPER_ADMIN') || false
  })

  /**
   * 检查是否有指定权限
   */
  const hasPermission = (permission) => {
    if (!adminInfo.value?.permissions) return false
    return adminInfo.value.permissions.includes(permission)
  }

  /**
   * 检查是否有指定角色
   */
  const hasRole = (role) => {
    if (!adminInfo.value?.roles) return false
    return adminInfo.value.roles.includes(role)
  }

  return {
    // 状态
    adminInfo: readonly(adminInfo),
    sidebarCollapsed, // 移除readonly，允许直接修改
    activeMenuItem: readonly(activeMenuItem),
    
    // 计算属性
    isSuperAdmin,
    
    // 方法
    setAdminInfo,
    clearAdminInfo,
    toggleSidebar,
    setSidebarCollapsed,
    setActiveMenuItem,
    hasPermission,
    hasRole
  }
} 