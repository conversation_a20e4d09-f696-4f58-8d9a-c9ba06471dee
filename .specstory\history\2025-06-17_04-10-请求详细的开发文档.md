<!-- Generated by SpecStory -->

# 请求详细的开发文档 (2025-06-17 12:10:05)

_**User**_

这个项目是前后端分离的，后端使用java+springboot，前端分为web端和app/小程序端，web端使用vue开发，app/小程序端使用uniapp端开发。你给一个详细的开发文档，我先看看

---

_**Assistant**_



---

基于您提供的数据库设计和项目结构，我来为您制作一个详细的开发文档。

---

<details>
            <summary>Listed directory app-resume-backend • **10** results</summary>
        
| Name |
|-------|
| 📁 `target` |
| 📁 `.idea` |
| 📄 `pom.xml` |
| 📁 `src` |
| 📄 `.gitattributes` |
| 📄 `mvnw` |
| 📁 `.mvn` |
| 📄 `HELP.md` |
| 📄 `.gitignore` |
| 📄 `mvnw.cmd` |

</details>

---

<details>
            <summary>Listed directory app-resume-web • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Listed directory app-resume-uniapp • **0** results</summary>
        
No results found
</details>

---

Read file: app-resume-backend/pom.xml

---

基于您的项目结构和数据库设计，我为您制作了一个详细的开发文档：

# 简历制作应用开发文档

## 项目概述

### 项目描述
一个功能完善的在线简历制作平台，支持多端访问（Web端、小程序、App），提供模板选择、在线编辑、导出分享等功能。

### 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 前端      │    │  UniApp 前端    │    │   管理后台      │
│   (Vue.js)      │    │ (小程序/App)    │    │   (Vue.js)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                           │
                           ▼
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Nginx)       │
                    └─────────────────┘
                           │
                           ▼
                    ┌─────────────────┐
                    │   后端服务      │
                    │ (Spring Boot)   │
                    └─────────────────┘
                           │
                           ▼
                    ┌─────────────────┐
                    │     数据库      │
                    │    (MySQL)      │
                    └─────────────────┘
```

## 技术栈详情

### 后端技术栈
- **框架**: Spring Boot 3.2.12
- **Java版本**: Java 17
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis-Plus
- **缓存**: Redis
- **安全**: Spring Security + JWT
- **文档**: Swagger/Knife4j
- **工具**: Lombok、MapStruct
- **云存储**: 阿里云OSS/腾讯云COS
- **支付**: 微信支付、支付宝

### 前端技术栈
#### Web端 (Vue)
- **框架**: Vue 3 + Vite
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **工具**: TypeScript、ESLint、Prettier

#### UniApp端
- **框架**: UniApp + Vue 3
- **UI组件**: uView Plus
- **状态管理**: Pinia
- **HTTP客户端**: uni.request 封装

## 项目结构

### 后端项目结构 (app-resume-backend)
```
app-resume-backend/
├── src/
│   ├── main/
│   │   ├── java/com/alan6/resume/
│   │   │   ├── AppResumeApplication.java          # 启动类
│   │   │   ├── config/                            # 配置类
│   │   │   │   ├── SwaggerConfig.java
│   │   │   │   ├── SecurityConfig.java
│   │   │   │   ├── RedisConfig.java
│   │   │   │   └── CorsConfig.java
│   │   │   ├── controller/                        # 控制器
│   │   │   │   ├── auth/                          # 认证相关
│   │   │   │   ├── user/                          # 用户管理
│   │   │   │   ├── resume/                        # 简历管理
│   │   │   │   ├── template/                      # 模板管理
│   │   │   │   ├── payment/                       # 支付相关
│   │   │   │   └── system/                        # 系统管理
│   │   │   ├── service/                           # 业务层
│   │   │   ├── mapper/                            # 数据访问层
│   │   │   ├── entity/                            # 实体类
│   │   │   ├── dto/                               # 数据传输对象
│   │   │   ├── vo/                                # 视图对象
│   │   │   ├── common/                            # 公共类
│   │   │   │   ├── result/                        # 统一返回结果
│   │   │   │   ├── exception/                     # 异常处理
│   │   │   │   ├── utils/                         # 工具类
│   │   │   │   └── constants/                     # 常量定义
│   │   │   └── security/                          # 安全相关
│   │   └── resources/
│   │       ├── application.yml                    # 配置文件
│   │       ├── application-dev.yml                # 开发环境配置
│   │       ├── application-prod.yml               # 生产环境配置
│   │       └── mapper/                            # MyBatis映射文件
│   └── test/
└── pom.xml
```

### 前端项目结构 (app-resume-web)
```
app-resume-web/
├── src/
│   ├── components/                    # 公共组件
│   │   ├── Layout/                    # 布局组件
│   │   ├── Resume/                    # 简历相关组件
│   │   └── Common/                    # 通用组件
│   ├── views/                         # 页面组件
│   │   ├── auth/                      # 登录注册
│   │   ├── dashboard/                 # 用户中心
│   │   ├── resume/                    # 简历制作
│   │   ├── template/                  # 模板中心
│   │   └── payment/                   # 支付相关
│   ├── router/                        # 路由配置
│   ├── store/                         # 状态管理
│   ├── api/                           # API接口
│   ├── utils/                         # 工具函数
│   ├── assets/                        # 静态资源
│   └── styles/                        # 样式文件
├── public/
├── package.json
└── vite.config.ts
```

### UniApp项目结构 (app-resume-uniapp)
```
app-resume-uniapp/
├── pages/                             # 页面
│   ├── index/                         # 首页
│   ├── auth/                          # 登录注册
│   ├── resume/                        # 简历制作
│   ├── template/                      # 模板选择
│   ├── profile/                       # 个人中心
│   └── payment/                       # 支付页面
├── components/                        # 组件
├── static/                            # 静态资源
├── store/                             # 状态管理
├── utils/                             # 工具函数
├── manifest.json                      # 应用配置
├── pages.json                         # 页面配置
└── uni.scss                           # 全局样式
```

## 核心功能模块

### 1. 用户管理模块
#### 功能特性
- 多种注册方式（邮箱、手机、第三方授权）
- 第三方登录（微信、QQ、支付宝等）
- 账号合并机制
- 用户信息管理
- 多语言支持

#### 主要接口
```java
// 用户认证相关
POST /api/auth/register           # 用户注册
POST /api/auth/login              # 用户登录
POST /api/auth/logout             # 用户登出
POST /api/auth/refresh            # 刷新Token
POST /api/auth/send-code          # 发送验证码
POST /api/auth/third-party        # 第三方登录

// 用户信息相关
GET  /api/user/profile            # 获取用户信息
PUT  /api/user/profile            # 更新用户信息
POST /api/user/avatar             # 上传头像
PUT  /api/user/password           # 修改密码
```

### 2. 简历管理模块
#### 功能特性
- 简历创建与编辑
- 多种简历模块（基本信息、教育经历、工作经验等）
- 模块显示/隐藏配置
- 简历预览与导出
- 多语言简历支持

#### 主要接口
```java
// 简历管理
GET    /api/resume/list           # 获取简历列表
POST   /api/resume                # 创建简历
GET    /api/resume/{id}           # 获取简历详情
PUT    /api/resume/{id}           # 更新简历
DELETE /api/resume/{id}           # 删除简历

// 简历模块管理
GET    /api/resume/{id}/modules   # 获取简历模块
PUT    /api/resume/{id}/modules   # 更新模块配置
POST   /api/resume/{id}/basic     # 更新基本信息
POST   /api/resume/{id}/education # 添加教育经历
PUT    /api/resume/{id}/education/{eduId} # 更新教育经历
// ... 其他模块接口

// 简历导出
POST   /api/resume/{id}/export    # 导出简历
GET    /api/resume/{id}/preview   # 预览简历
```

### 3. 模板管理模块
#### 功能特性
- 模板分类管理
- 免费/付费模板
- 模板预览与使用
- 模板收藏功能

#### 主要接口
```java
// 模板相关
GET  /api/template/categories     # 获取模板分类
GET  /api/template/list           # 获取模板列表
GET  /api/template/{id}           # 获取模板详情
POST /api/template/{id}/use       # 使用模板创建简历

// 收藏相关
POST /api/favorite/template/{id}  # 收藏模板
DELETE /api/favorite/template/{id} # 取消收藏
```

### 4. 会员体系模块
#### 功能特性
- 会员套餐管理
- 权限控制（简历数量、导出次数、付费模板）
- 会员到期提醒

#### 主要接口
```java
// 会员相关
GET  /api/membership/packages     # 获取会员套餐
GET  /api/membership/current      # 获取当前会员信息
POST /api/membership/purchase     # 购买会员
```

### 5. 支付订单模块
#### 功能特性
- 微信支付集成
- 支付宝支付集成
- 订单管理
- 支付回调处理

#### 主要接口
```java
// 支付相关
POST /api/payment/create          # 创建支付订单
GET  /api/payment/status/{orderNo} # 查询支付状态
POST /api/payment/callback/wechat # 微信支付回调
POST /api/payment/callback/alipay # 支付宝支付回调
```

## 数据库设计说明

### 核心表结构
根据您提供的数据库设计，主要包含以下表组：

1. **用户管理**: `users`, `user_third_party_auths`, `user_merge_records`
2. **会员体系**: `membership_packages`, `user_memberships`
3. **模板系统**: `template_categories`, `resume_templates`
4. **简历数据**: `resumes`, `resume_*` 系列表
5. **用户行为**: `user_favorites`, `share_records`, `resume_export_records`
6. **支付订单**: `orders`
7. **系统配置**: `system_configs`, `supported_languages`

### 关键设计特点
- 支持多语言（中文、英文、日文、韩文）
- 第三方授权登录支持
- 账号合并机制
- 会员权限控制
- 详细的用户行为记录

## 开发规范

### 代码规范
1. **命名规范**
   - 类名：PascalCase
   - 方法名：camelCase
   - 常量：UPPER_SNAKE_CASE
   - 包名：全小写，用点分隔

2. **注释规范**
   - 类和方法必须有Javadoc注释
   - 复杂业务逻辑添加行内注释

3. **异常处理**
   - 统一异常处理机制
   - 自定义业务异常
   - 规范错误码定义

### API设计规范
1. **RESTful设计**
   - GET: 查询操作
   - POST: 创建操作
   - PUT: 更新操作
   - DELETE: 删除操作

2. **统一返回格式**
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1234567890
}
```

3. **分页查询格式**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [],
    "total": 100,
    "current": 1,
    "size": 10
  }
}
```

## 部署配置

### 开发环境
1. **后端启动**
```bash
cd app-resume-backend
mvn spring-boot:run
```

2. **Web前端启动**
```bash
cd app-resume-web
npm install
npm run dev
```

3. **UniApp启动**
```bash
cd app-resume-uniapp
npm install
# 使用HBuilderX打开项目运行
```

### 生产环境
1. **Docker部署**
2. **Nginx配置**
3. **数据库配置**
4. **Redis配置**

## 安全考虑

### 数据安全
- 密码加密存储（BCrypt）
- 敏感信息脱敏
- SQL注入防护
- XSS攻击防护

### 接口安全
- JWT Token认证
- 接口权限控制
- 请求频率限制
- 跨域配置

## 性能优化

### 后端优化
- 数据库索引优化
- Redis缓存策略
- 异步处理机制
- 接口响应压缩

### 前端优化
- 组件懒加载
- 图片懒加载
- 代码分割
- CDN加速

这个开发文档提供了项目的整体架构和开发指导。您可以根据实际需求调整和完善具体的实现细节。需要我详细说明某个特定模块的实现方案吗？

---

_**User**_

执行操作：
1、将以上文档内容丰富，写详细些；
2、接口url中的变量放到url最后，比如{id}放在url最后，比如/api/resume/{id}/modules 修改为 /api/resume/modules/{id}；
3、将写好的开发文档写入app-resume-backend目录下的files目录里面，命名为开发文档1_0.md；

---

_**Assistant**_



---

我将为您创建一个更详细的开发文档，并按照您的要求修改接口URL格式，然后保存到指定位置。

---

```bash
mkdir -p app-resume-backend/files
```

```
PS E:\workspace\resume> mkdir -p app-resume-backend/files
mkdir : 具有指定名称 E:\workspace\resume\app-resume-backend\files 的项已存在。
所在位置 行:1 字符: 1
+ mkdir -p app-resume-backend/files
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ResourceExists: (E:\workspace\re...e-backend\fil 
   es:String) [New-Item], IOException
    + FullyQualifiedErrorId : DirectoryExist,Microsoft.PowerShell.Commands.New 
   ItemCommand
 
PS E:\workspace\resume>
```

---

Error calling tool 'edit_file'.

---

让我分部分创建这个详细的开发文档：

---

Cancelled

---

_**User**_

你怎么卡住了

---

_**Assistant**_



---

抱歉刚才卡住了，是因为文档内容太长超过了限制。让我重新创建这个详细的开发文档，分段处理：

---

Cancelled

---

_**User**_

不用写入实例代码

---

_**Assistant**_



---

好的，我现在创建一个详细的开发文档，不包含实例代码：

---

Error calling tool 'edit_file'.

---

让我分段创建文档，先创建基础框架：

---

Cancelled

---

_**User**_

怎么回事啊，使用edit_file工具啊

---

_**Assistant**_



---

好的，我使用edit_file工具来创建文档：

---

Cancelled

---

_**User**_

算了，你直接输出就可以了，我来写入

---

_**Assistant**_



---

好的，我直接输出详细的开发文档内容，您可以复制保存：

# 简历制作应用开发文档 v1.0

## 1. 项目概述

### 1.1 项目介绍
本项目是一个功能完善的在线简历制作平台，采用前后端分离架构，支持多端访问。用户可以通过Web端、小程序、App等多种方式访问平台，选择专业模板，在线编辑个人简历，并支持多种格式导出和分享功能。

### 1.2 项目特色
- **多端支持**: Web端、微信小程序、支付宝小程序、App等全平台覆盖
- **国际化**: 支持中文、英文、日文、韩文等多语言
- **多样化模板**: 提供丰富的免费和付费简历模板
- **智能编辑**: 所见即所得的在线编辑体验
- **会员体系**: 完善的付费会员机制和权限控制
- **云端同步**: 数据云端存储，多设备同步访问
- **安全可靠**: 完善的用户认证和数据安全保护

### 1.3 目标用户
- 求职者：应届毕业生、职场人士、转行人群
- 学生群体：在校学生、实习生
- 自由职业者：设计师、程序员、咨询师等
- HR招聘人员：查看和评估候选人简历

## 2. 技术架构

### 2.1 整体架构图
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│   Web前端   │  │ 小程序前端  │  │   App前端   │  │  管理后台   │
│  (Vue.js)   │  │  (UniApp)   │  │  (UniApp)   │  │  (Vue.js)   │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘
       │                │                │                │
       └────────────────┼────────────────┼────────────────┘
                        │                │
                        ▼                ▼
                 ┌─────────────────────────────┐
                 │        API网关             │
                 │       (Nginx)              │
                 └─────────────────────────────┘
                              │
                              ▼
                 ┌─────────────────────────────┐
                 │       后端服务集群          │
                 │    (Spring Boot)           │
                 │  ┌─────────┐ ┌─────────┐   │
                 │  │用户服务 │ │简历服务 │   │
                 │  └─────────┘ └─────────┘   │
                 │  ┌─────────┐ ┌─────────┐   │
                 │  │支付服务 │ │文件服务 │   │
                 │  └─────────┘ └─────────┘   │
                 └─────────────────────────────┘
                              │
                    ┌─────────┼─────────┐
                    ▼         ▼         ▼
            ┌─────────┐ ┌─────────┐ ┌─────────┐
            │  MySQL  │ │  Redis  │ │   OSS   │
            │  数据库  │ │  缓存   │ │ 文件存储 │
            └─────────┘ └─────────┘ └─────────┘
```

### 2.2 微服务划分
- **用户服务**: 用户注册、登录、认证、权限管理
- **简历服务**: 简历创建、编辑、模板管理、导出
- **支付服务**: 订单管理、支付处理、会员管理
- **文件服务**: 文件上传、存储、CDN分发
- **通知服务**: 消息推送、邮件发送、短信发送

## 3. 技术栈详情

### 3.1 后端技术栈
- **开发框架**: Spring Boot 3.2.12
- **开发语言**: Java 17
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **持久层框架**: MyBatis-Plus 3.5+
- **安全框架**: Spring Security 6.0 + JWT
- **API文档**: Swagger 3 + Knife4j
- **对象映射**: MapStruct
- **工具库**: Lombok、Hutool
- **消息队列**: RabbitMQ 3.12+
- **搜索引擎**: Elasticsearch 8.0+
- **文件存储**: 阿里云OSS / 腾讯云COS
- **支付集成**: 微信支付v3 + 支付宝开放平台
- **容器化**: Docker + Docker Compose
- **监控**: Spring Boot Actuator + Micrometer

### 3.2 前端技术栈

#### 3.2.1 Web端技术栈
- **开发框架**: Vue 3.4+ + TypeScript
- **构建工具**: Vite 5.0+
- **UI组件库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.6+
- **CSS预处理器**: Sass/SCSS
- **代码规范**: ESLint + Prettier
- **打包优化**: Rollup
- **图表库**: ECharts 5.4+
- **富文本编辑器**: TinyMCE / Quill
- **PDF生成**: html2canvas + jsPDF

#### 3.2.2 UniApp端技术栈
- **开发框架**: UniApp + Vue 3 + TypeScript
- **UI组件库**: uView Plus 3.0+
- **状态管理**: Pinia
- **HTTP请求**: uni.request封装
- **小程序SDK**: 微信小程序SDK、支付宝小程序SDK
- **支付集成**: 微信支付、支付宝支付
- **地图服务**: 腾讯地图、高德地图
- **统计分析**: 微信小程序统计、友盟统计

### 3.3 DevOps工具链
- **版本控制**: Git + GitLab
- **CI/CD**: GitLab CI / Jenkins
- **容器编排**: Docker Swarm / Kubernetes
- **服务网格**: Istio
- **日志收集**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **监控告警**: Prometheus + Grafana + AlertManager
- **链路追踪**: Jaeger / SkyWalking

## 4. 数据库设计详解

### 4.1 数据库设计原则
- **标准化设计**: 遵循第三范式，避免数据冗余
- **性能优化**: 合理设置索引，优化查询性能
- **扩展性**: 预留扩展字段，支持业务发展
- **安全性**: 敏感数据加密存储，设置访问权限
- **国际化**: 支持UTF8mb4字符集，兼容多语言

### 4.2 核心表组说明

#### 4.2.1 用户管理表组
- **users**: 用户基础信息表，存储用户账号、个人信息、偏好设置
- **user_third_party_auths**: 第三方授权表，支持微信、QQ、支付宝等平台登录
- **user_merge_records**: 账号合并记录表，处理用户多账号合并场景

#### 4.2.2 会员体系表组
- **membership_packages**: 会员套餐表，定义不同会员等级和权益
- **user_memberships**: 用户会员表，记录用户会员状态和使用情况

#### 4.2.3 简历模板表组
- **template_categories**: 模板分类表，按行业、风格等维度分类
- **resume_templates**: 简历模板表，存储模板信息和配置数据

#### 4.2.4 简历数据表组
- **resumes**: 简历主表，存储简历基本信息和配置
- **resume_basic_info**: 基本信息表，个人联系方式和求职意向
- **resume_educations**: 教育经历表，学校、专业、学历信息
- **resume_work_experiences**: 工作经历表，公司、职位、工作描述
- **resume_projects**: 项目经验表，项目名称、技术栈、个人贡献
- **resume_skills**: 技能标签表，技能名称、熟练程度、分类
- **resume_certificates**: 证书资质表，证书名称、颁发机构、有效期
- **resume_awards**: 获奖荣誉表，奖项名称、级别、颁发机构
- **resume_languages**: 语言能力表，语言种类、水平、证书成绩
- **其他简历模块表**: 实习经历、研究经历、论文专利、培训经历、志愿服务、作品集、自我评价、推荐人等

#### 4.2.5 用户行为表组
- **user_favorites**: 收藏表，用户收藏的模板和简历
- **share_records**: 分享记录表，简历分享链接和统计
- **resume_export_records**: 导出记录表，简历导出历史和格式
- **user_operation_logs**: 操作日志表，用户行为轨迹记录
- **user_access_logs**: 访问日志表，IP地理位置和语言检测

#### 4.2.6 支付订单表组
- **orders**: 订单表，会员充值和模板购买订单
- **payment_records**: 支付记录表，支付流水和状态

#### 4.2.7 系统配置表组
- **system_configs**: 系统配置表，全局参数配置
- **supported_languages**: 支持语言表，多语言配置信息

### 4.3 索引设计策略
- **主键索引**: 所有表使用BIGINT自增主键
- **唯一索引**: 用户邮箱、手机号、第三方OpenID等唯一标识
- **复合索引**: 常用查询条件组合，如用户ID+时间范围
- **覆盖索引**: 避免回表查询，提升查询性能
- **前缀索引**: 长字符串字段使用前缀索引节省空间

## 5. 项目结构详解

### 5.1 后端项目结构 (app-resume-backend)
```
app-resume-backend/
├── src/main/java/com/alan6/resume/
│   ├── AppResumeApplication.java              # Spring Boot启动类
│   ├── config/                                # 配置类包
│   │   ├── security/                          # 安全配置
│   │   │   ├── SecurityConfig.java            # Spring Security配置
│   │   │   ├── JwtConfig.java                 # JWT配置
│   │   │   └── AuthenticationEntryPointImpl.java # 认证入口点
│   │   ├── web/                               # Web配置
│   │   │   ├── CorsConfig.java                # 跨域配置
│   │   │   ├── WebMvcConfig.java              # MVC配置
│   │   │   └── InterceptorConfig.java         # 拦截器配置
│   │   ├── database/                          # 数据库配置
│   │   │   ├── MybatisPlusConfig.java         # MyBatis-Plus配置
│   │   │   └── DruidConfig.java               # 数据源配置
│   │   ├── cache/                             # 缓存配置
│   │   │   └── RedisConfig.java               # Redis配置
│   │   ├── swagger/                           # API文档配置
│   │   │   └── SwaggerConfig.java             # Swagger配置
│   │   ├── payment/                           # 支付配置
│   │   │   ├── WechatPayConfig.java           # 微信支付配置
│   │   │   └── AlipayConfig.java              # 支付宝配置
│   │   └── storage/                           # 存储配置
│   │       ├── OssConfig.java                 # OSS配置
│       │   └── MinioConfig.java               # MinIO配置
│   ├── controller/                            # 控制器层
│   │   ├── common/                            # 通用控制器
│   │   │   ├── BaseController.java            # 基础控制器
│   │   │   ├── FileController.java            # 文件上传控制器
│   │   │   └── CaptchaController.java         # 验证码控制器
│   │   ├── auth/                              # 认证授权
│   │   │   ├── AuthController.java            # 认证控制器
│   │   │   ├── ThirdPartyAuthController.java  # 第三方登录控制器
│   │   │   └── PasswordController.java        # 密码管理控制器
│   │   ├── user/                              # 用户管理
│   │   │   ├── UserController.java            # 用户信息控制器
│   │   │   ├── UserProfileController.java     # 用户资料控制器
│   │   │   └── UserSettingsController.java    # 用户设置控制器
│   │   ├── resume/                            # 简历管理
│   │   │   ├── ResumeController.java          # 简历主控制器
│   │   │   ├── ResumeModuleController.java    # 简历模块控制器
│   │   │   ├── ResumeExportController.java    # 简历导出控制器
│   │   │   └── ResumeShareController.java     # 简历分享控制器
│   │   ├── template/                          # 模板管理
│   │   │   ├── TemplateController.java        # 模板控制器
│   │   │   ├── TemplateCategoryController.java # 模板分类控制器
│   │   │   └── TemplateFavoriteController.java # 模板收藏控制器
│   │   ├── membership/                        # 会员管理
│   │   │   ├── MembershipController.java      # 会员控制器
│   │   │   └── MembershipPackageController.java # 会员套餐控制器
│   │   ├── payment/                           # 支付管理
│   │   │   ├── OrderController.java           # 订单控制器
│   │   │   ├── PaymentController.java         # 支付控制器
│   │   │   └── PaymentCallbackController.java # 支付回调控制器
│   │   └── admin/                             # 管理后台
│   │       ├── AdminUserController.java       # 用户管理
│   │       ├── AdminResumeController.java     # 简历管理
│   │       ├── AdminTemplateController.java   # 模板管理
│   │       └── AdminSystemController.java     # 系统管理
│   ├── service/                               # 业务服务层
│   │   ├── impl/                              # 服务实现
│   │   └── [各业务模块服务接口]
│   ├── mapper/                                # 数据访问层
│   │   └── [各业务模块Mapper接口]
│   ├── entity/                                # 实体类
│   │   ├── user/                              # 用户相关实体
│   │   ├── resume/                            # 简历相关实体
│   │   ├── template/                          # 模板相关实体
│   │   ├── membership/                        # 会员相关实体
│   │   ├── payment/                           # 支付相关实体
│   │   └── system/                            # 系统相关实体
│   ├── dto/                                   # 数据传输对象
│   │   ├── request/                           # 请求DTO
│   │   ├── response/                          # 响应DTO
│   │   └── [各业务模块DTO]
│   ├── vo/                                    # 视图对象
│   │   └── [各业务模块VO]
│   ├── converter/                             # 对象转换器
│   │   └── [各业务模块Converter]
│   ├── common/                                # 公共组件
│   │   ├── result/                            # 统一返回结果
│   │   │   ├── Result.java                    # 统一返回格式
│   │   │   ├── ResultCode.java                # 返回码枚举
│   │   │   └── PageResult.java                # 分页返回格式
│   │   ├── exception/                         # 异常处理
│   │   │   ├── GlobalExceptionHandler.java    # 全局异常处理器
│   │   │   ├── BusinessException.java         # 业务异常
│   │   │   └── [其他自定义异常]
│   │   ├── utils/                             # 工具类
│   │   │   ├── JwtUtils.java                  # JWT工具类
│   │   │   ├── PasswordUtils.java             # 密码工具类
│   │   │   ├── FileUtils.java                 # 文件工具类
│   │   │   ├── HttpUtils.java                 # HTTP工具类
│   │   │   ├── DateUtils.java                 # 日期工具类
│   │   │   └── ValidationUtils.java           # 验证工具类
│   │   ├── constants/                         # 常量定义
│   │   │   ├── CommonConstants.java           # 通用常量
│   │   │   ├── RedisConstants.java            # Redis常量
│   │   │   ├── SecurityConstants.java         # 安全常量
│   │   │   └── [其他业务常量]
│   │   ├── enums/                             # 枚举定义
│   │   │   ├── UserStatusEnum.java            # 用户状态枚举
│   │   │   ├── OrderStatusEnum.java           # 订单状态枚举
│   │   │   └── [其他业务枚举]
│   │   └── base/                              # 基础类
│   │       ├── BaseEntity.java                # 基础实体类
│   │       ├── BaseService.java               # 基础服务类
│   │       └── BaseMapper.java                # 基础Mapper类
│   ├── security/                              # 安全相关
│   │   ├── filter/                            # 安全过滤器
│   │   │   ├── JwtAuthenticationFilter.java   # JWT认证过滤器
│   │   │   └── CorsFilter.java                # 跨域过滤器
│   │   ├── handler/                           # 安全处理器
│   │   │   ├── AuthenticationSuccessHandler.java # 认证成功处理器
│   │   │   ├── AuthenticationFailureHandler.java # 认证失败处理器
│   │   │   └── AccessDeniedHandler.java       # 访问拒绝处理器
│   │   └── userdetails/                       # 用户详情
│   │       └── UserDetailsServiceImpl.java    # 用户详情服务实现
│   ├── aspect/                                # 切面
│   │   ├── LogAspect.java                     # 日志切面
│   │   ├── PermissionAspect.java              # 权限切面
│   │   └── RateLimitAspect.java               # 限流切面
│   ├── task/                                  # 定时任务
│   │   ├── MembershipExpiredTask.java         # 会员到期任务
│   │   ├── DataCleanupTask.java               # 数据清理任务
│   │   └── StatisticsTask.java                # 统计任务
│   └── listener/                              # 事件监听器
│       ├── UserRegistrationListener.java      # 用户注册监听器
│       ├── PaymentSuccessListener.java        # 支付成功监听器
│       └── ResumeExportListener.java          # 简历导出监听器
├── src/main/resources/
│   ├── application.yml                        # 主配置文件
│   ├── application-dev.yml                    # 开发环境配置
│   ├── application-test.yml                   # 测试环境配置
│   ├── application-prod.yml                   # 生产环境配置
│   ├── mapper/                                # MyBatis映射文件
│   │   └── [各模块XML映射文件]
│   ├── static/                                # 静态资源
│   ├── templates/                             # 模板文件
│   ├── db/                                    # 数据库脚本
│   │   ├── migration/                         # 数据库迁移脚本
│   │   └── data/                              # 初始化数据脚本
│   ├── i18n/                                  # 国际化文件
│   │   ├── messages_zh_CN.properties          # 中文
│   │   ├── messages_en.properties             # 英文
│   │   ├── messages_ja.properties             # 日文
│   │   └── messages_ko.properties             # 韩文
│   └── logback-spring.xml                     # 日志配置
├── src/test/java/                             # 测试代码
│   ├── unit/                                  # 单元测试
│   ├── integration/                           # 集成测试
│   └── performance/                           # 性能测试
├── docker/                                    # Docker相关文件
│   ├── Dockerfile                             # Docker镜像构建文件
│   ├── docker-compose.yml                     # Docker Compose配置
│   └── nginx.conf                             # Nginx配置
├── docs/                                      # 项目文档
│   ├── api/                                   # API文档
│   ├── deployment/                            # 部署文档
│   └── design/                                # 设计文档
├── scripts/                                   # 脚本文件
│   ├── build.sh                               # 构建脚本
│   ├── deploy.sh                              # 部署脚本
│   └── backup.sh                              # 备份脚本
├── pom.xml                                    # Maven配置文件
├── README.md                                  # 项目说明
└── .gitignore                                 # Git忽略文件
```

### 5.2 Web前端项目结构 (app-resume-web)
```
app-resume-web/
├── src/
│   ├── main.ts                                # 应用入口文件
│   ├── App.vue                                # 根组件
│   ├── components/                            # 公共组件
│   │   ├── Layout/                            # 布局组件
│   │   │   ├── Header/                        # 头部组件
│   │   │   │   ├── AppHeader.vue              # 应用头部
│   │   │   │   ├── UserMenu.vue               # 用户菜单
│   │   │   │   └── LanguageSelector.vue       # 语言选择器
│   │   │   ├── Sidebar/                       # 侧边栏组件
│   │   │   │   ├── AppSidebar.vue             # 应用侧边栏
│   │   │   │   └── MenuTree.vue               # 菜单树
│   │   │   ├── Footer/                        # 底部组件
│   │   │   │   └── AppFooter.vue              # 应用底部
│   │   │   └── PageLayout.vue                 # 页面布局
│   │   ├── Resume/                            # 简历相关组件
│   │   │   ├── Editor/                        # 编辑器组件
│   │   │   │   ├── ResumeEditor.vue           # 简历编辑器
│   │   │   │   ├── ModuleEditor.vue           # 模块编辑器
│   │   │   │   ├── BasicInfoEditor.vue        # 基本信息编辑
│   │   │   │   ├── EducationEditor.vue        # 教育经历编辑
│   │   │   │   ├── WorkExperienceEditor.vue   # 工作经历编辑
│   │   │   │   ├── ProjectEditor.vue          # 项目经验编辑
│   │   │   │   ├── SkillEditor.vue            # 技能编辑
│   │   │   │   └── [其他模块编辑器]
│   │   │   ├── Preview/                       # 预览组件
│   │   │   │   ├── ResumePreview.vue          # 简历预览
│   │   │   │   ├── TemplateRenderer.vue       # 模板渲染器
│   │   │   │   └── PrintPreview.vue           # 打印预览
│   │   │   ├── Export/                        # 导出组件
│   │   │   │   ├── ExportDialog.vue           # 导出对话框
│   │   │   │   ├── PdfExporter.vue            # PDF导出器
│   │   │   │   └── WordExporter.vue           # Word导出器
│   │   │   └── Share/                         # 分享组件
│   │   │       ├── ShareDialog.vue            # 分享对话框
│   │   │       ├── QrCodeGenerator.vue        # 二维码生成器
│   │   │       └── LinkShare.vue              # 链接分享
│   │   ├── Template/                          # 模板相关组件
│   │   │   ├── TemplateCard.vue               # 模板卡片
│   │   │   ├── TemplatePreview.vue            # 模板预览
│   │   │   ├── TemplateFilter.vue             # 模板筛选
│   │   │   └── TemplateSearch.vue             # 模板搜索
│   │   ├── Common/                            # 通用组件
│   │   │   ├── Form/                          # 表单组件
│   │   │   │   ├── FormBuilder.vue            # 表单构建器
│   │   │   │   ├── ValidatedInput.vue         # 验证输入框
│   │   │   │   ├── DatePicker.vue             # 日期选择器
│   │   │   │   └── FileUploader.vue           # 文件上传器
│   │   │   ├── Modal/                         # 模态框组件
│   │   │   │   ├── ConfirmDialog.vue          # 确认对话框
│   │   │   │   ├── InfoDialog.vue             # 信息对话框
│   │   │   │   └── LoadingDialog.vue          # 加载对话框
│   │   │   ├── Navigation/                    # 导航组件
│   │   │   │   ├── Breadcrumb.vue             # 面包屑导航
│   │   │   │   ├── Pagination.vue             # 分页组件
│   │   │   │   └── TabNav.vue                 # 标签导航
│   │   │   ├── Display/                       # 展示组件
│   │   │   │   ├── DataTable.vue              # 数据表格
│   │   │   │   ├── StatCard.vue               # 统计卡片
│   │   │   │   ├── Timeline.vue               # 时间线
│   │   │   │   └── Progress.vue               # 进度条
│   │   │   └── Feedback/                      # 反馈组件
│   │   │       ├── Loading.vue                # 加载动画
│   │   │       ├── Empty.vue                  # 空状态
│   │   │       └── Error.vue                  # 错误状态
│   │   └── Payment/                           # 支付相关组件
│   │       ├── PaymentDialog.vue              # 支付对话框
│   │       ├── PricingTable.vue               # 价格表
│   │       └── OrderHistory.vue               # 订单历史
│   ├── views/                                 # 页面组件
│   │   ├── auth/                              # 认证页面
│   │   │   ├── Login.vue                      # 登录页面
│   │   │   ├── Register.vue                   # 注册页面
│   │   │   ├── ForgotPassword.vue             # 忘记密码
│   │   │   ├── ResetPassword.vue              # 重置密码
│   │   │   └── ThirdPartyAuth.vue             # 第三方授权
│   │   ├── home/                              # 首页
│   │   │   ├── Home.vue                       # 首页
│   │   │   ├── Features.vue                   # 功能介绍
│   │   │   └── Pricing.vue                    # 价格方案
│   │   ├── dashboard/                         # 用户中心
│   │   │   ├── Dashboard.vue                  # 控制台首页
│   │   │   ├── Profile.vue                    # 个人资料
│   │   │   ├── Settings.vue                   # 账号设置
│   │   │   ├── Security.vue                   # 安全设置
│   │   │   ├── Membership.vue                 # 会员中心
│   │   │   └── Usage.vue                      # 使用统计
│   │   ├── resume/                            # 简历管理
│   │   │   ├── ResumeList.vue                 # 简历列表
│   │   │   ├── ResumeDetail.vue               # 简历详情
│   │   │   ├── ResumeEdit.vue                 # 简历编辑
│   │   │   ├── ResumeCreate.vue               # 简历创建
│   │   │   ├── ResumePreview.vue              # 简历预览
│   │   │   └── ResumeShare.vue                # 简历分享
│   │   ├── template/                          # 模板中心
│   │   │   ├── TemplateCenter.vue             # 模板中心
│   │   │   ├── TemplateDetail.vue             # 模板详情
│   │   │   ├── TemplateCategory.vue           # 模板分类
│   │   │   └── TemplateFavorites.vue          # 收藏模板
│   │   ├── payment/                           # 支付相关
│   │   │   ├── Upgrade.vue                    # 升级会员
│   │   │   ├── PaymentResult.vue              # 支付结果
│   │   │   └── OrderList.vue                  # 订单列表
│   │   ├── help/                              # 帮助中心
│   │   │   ├── Help.vue                       # 帮助首页
│   │   │   ├── FAQ.vue                        # 常见问题
│   │   │   ├── Tutorial.vue                   # 使用教程
│   │   │   └── Contact.vue                    # 联系我们
│   │   └── error/                             # 错误页面
│   │       ├── 404.vue                        # 404页面
│   │       ├── 403.vue                        # 403页面
│   │       └── 500.vue                        # 500页面
│   ├── router/                                # 路由配置
│   │   ├── index.ts                           # 路由主文件
│   │   ├── routes.ts                          # 路由配置
│   │   ├── guards.ts                          # 路由守卫
│   │   └── middleware.ts                      # 路由中间件
│   ├── store/                                 # 状态管理
│   │   ├── index.ts                           # Store主文件
│   │   ├── modules/                           # Store模块
│   │   │   ├── auth.ts                        # 认证状态
│   │   │   ├── user.ts                        # 用户状态
│   │   │   ├── resume.ts                      # 简历状态
│   │   │   ├── template.ts                    # 模板状态
│   │   │   ├── app.ts                         # 应用状态
│   │   │   └── payment.ts                     # 支付状态
│   │   └── types.ts                           # 类型定义
│   ├── api/                                   # API接口
│   │   ├── index.ts                           # API主文件
│   │   ├── request.ts                         # 请求封装
│   │   ├── auth.ts                            # 认证API
│   │   ├── user.ts                            # 用户API
│   │   ├── resume.ts                          # 简历API
│   │   ├── template.ts                        # 模板API
│   │   ├── payment.ts                         # 支付API
│   │   └── upload.ts                          # 上传API
│   ├── utils/                                 # 工具函数
│   │   ├── index.ts                           # 工具主文件
│   │   ├── auth.ts                            # 认证工具
│   │   ├── storage.ts                         # 存储工具
│   │   ├── format.ts                          # 格式化工具
│   │   ├── validation.ts                      # 验证工具
│   │   ├── date.ts                            # 日期工具
│   │   ├── file.ts                            # 文件工具
│   │   └── export.ts                          # 导出工具
│   ├── composables/                           # 组合式函数
│   │   ├── useAuth.ts                         # 认证逻辑
│   │   ├── useResume.ts                       # 简历逻辑
│   │   ├── useTemplate.ts                     # 模板逻辑
│   │   ├── usePayment.ts                      # 支付逻辑
│   │   └── useI18n.ts                         # 国际化逻辑
│   ├── assets/                                # 静态资源
│   │   ├── images/                            # 图片资源
│   │   ├── icons/                             # 图标资源
│   │   ├── fonts/                             # 字体资源
│   │   └── videos/                            # 视频资源
│   ├── styles/                                # 样式文件
│   │   ├── index.scss                         # 主样式文件
│   │   ├── variables.scss                     # 变量定义
│   │   ├── mixins.scss                        # 混入定义
│   │   ├── reset.scss                         # 重置样式
│   │   ├── common.scss                        # 通用样式
│   │   ├── components.scss                    # 组件样式
│   │   └── themes/                            # 主题样式
│   │       ├── default.scss                   # 默认主题
│   │       └── dark.scss                      # 暗色主题
│   ├── locales/                               # 国际化文件
│   │   ├── zh-CN.json                         # 中文
│   │   ├── en.json                            # 英文
│   │   ├── ja.json                            # 日文
│   │   └── ko.json                            # 韩文
│   └── types/                                 # 类型定义
│       ├── api.ts                             # API类型
│       ├── user.ts                            # 用户类型
│       ├── resume.ts                          # 简历类型
│       ├── template.ts                        # 模板类型
│       └── common.ts                          # 通用类型
├── public/                                    # 公共资源
│   ├── index.html                             # HTML模板
│   ├── favicon.ico                            # 网站图标
│   ├── robots.txt                             # 爬虫协议
│   └── manifest.json                          # PWA配置
├── tests/                                     # 测试文件
│   ├── unit/                                  # 单元测试
│   ├── e2e/                                   # 端到端测试
│   └── fixtures/                              # 测试数据
├── docs/                                      # 项目文档
├── package.json                               # 项目配置
├── vite.config.ts                             # Vite配置
├── tsconfig.json                              # TypeScript配置
├── .env                                       # 环境变量
├── .env.development                           # 开发环境变量
├── .env.production                            # 生产环境变量
├── .eslintrc.js                               # ESLint配置
├── .prettierrc                                # Prettier配置
├── .gitignore                                 # Git忽略文件
└── README.md                                  # 项目说明
```

### 5.3 UniApp项目结构 (app-resume-uniapp)
```
app-resume-uniapp/
├── pages/                                     # 页面文件
│   ├── index/                                 # 首页
│   │   └── index.vue                          # 首页
│   ├── auth/                                  # 认证页面
│   │   ├── login/                             # 登录页面
│   │   │   └── login.vue
│   │   ├── register/                          # 注册页面
│   │   │   └── register.vue
│   │   └── forgot-password/                   # 忘记密码
│   │       └── forgot-password.vue
│   ├── resume/                                # 简历管理
│   │   ├── list/                              # 简历列表
│   │   │   └── list.vue
│   │   ├── detail/                            # 简历详情
│   │   │   └── detail.vue
│   │   ├── edit/                              # 简历编辑
│   │   │   └── edit.vue
│   │   ├── create/                            # 创建简历
│   │   │   └── create.vue
│   │   ├── preview/                           # 简历预览
│   │   │   └── preview.vue
│   │   └── export/                            # 简历导出
│   │       └── export.vue
│   ├── template/                              # 模板中心
│   │   ├── center/                            # 模板中心
│   │   │   └── center.vue
│   │   ├── detail/                            # 模板详情
│   │   │   └── detail.vue
│   │   ├── category/                          # 模板分类
│   │   │   └── category.vue
│   │   └── favorites/                         # 收藏模板
│   │       └── favorites.vue
│   ├── profile/                               # 个人中心
│   │   ├── index/                             # 个人中心首页
│   │   │   └── index.vue
│   │   ├── info/                              # 个人信息
│   │   │   └── info.vue
│   │   ├── settings/                          # 设置
│   │   │   └── settings.vue
│   │   ├── membership/                        # 会员中心
│   │   │   └── membership.vue
│   │   └── orders/                            # 订单列表
│   │       └── orders.vue
│   ├── payment/                               # 支付相关
│   │   ├── upgrade/                           # 升级会员
│   │   │   └── upgrade.vue
│   │   ├── result/                            # 支付结果
│   │   │   └── result.vue
│   │   └── order-detail/                      # 订单详情
│   │       └── order-detail.vue
│   ├── help/                                  # 帮助中心
│   │   ├── index/                             # 帮助首页
│   │   │   └── index.vue
│   │   ├── faq/                               # 常见问题
│   │   │   └── faq.vue
│   │   └── contact/                           # 联系客服
│   │       └── contact.vue
│   └── share/                                 # 分享页面
│       └── resume/                            # 简历分享
│           └── resume.vue
├── components/                                # 组件文件
│   ├── common/                                # 通用组件
│   │   ├── NavBar/                            # 导航栏
│   │   │   └── NavBar.vue
│   │   ├── TabBar/                            # 底部标签栏
│   │   │   └── TabBar.vue
│   │   ├── Loading/                           # 加载组件
│   │   │   └── Loading.vue
│   │   ├── Empty/                             # 空状态
│   │   │   └── Empty.vue
│   │   ├── Modal/                             # 模态框
│   │   │   └── Modal.vue
│   │   └── Form/                              # 表单组件
│   │       ├── FormInput.vue                  # 输入框
│   │       ├── FormTextarea.vue               # 文本域
│   │       ├── FormSelect.vue                 # 选择器
│   │       └── FormUpload.vue                 # 文件上传
│   ├── resume/                                # 简历组件
│   │   ├── ResumeCard/                        # 简历卡片
│   │   │   └── ResumeCard.vue
│   │   ├── ModuleEditor/                      # 模块编辑器
│   │   │   ├── BasicEditor.vue                # 基本信息编辑
│   │   │   ├── EducationEditor.vue            # 教育经历编辑
│   │   │   ├── WorkEditor.vue                 # 工作经历编辑
│   │   │   └── ProjectEditor.vue              # 项目经验编辑
│   │   └── TemplatePreview/                   # 模板预览
│   │       └── TemplatePreview.vue
│   └── template/                              # 模板组件
│       ├── TemplateCard/                      # 模板卡片
│       │   └

---

_**User**_

不行，我拷贝太麻烦了，你直接写入当前目录的files目录下的开发文档1_0.md文件中

---

_**Assistant**_



---

```bash
mkdir -p files
```

```

mkdir : 具有指定名称 E:\workspace\resume\files 的项已存在。
所在位置 行:1 字符: 1
+ mkdir -p files
+ ~~~~~~~~~~~~~~
    + CategoryInfo          : ResourceExists: (E:\workspace\resume\files:String) [New-Item], IOException
    + FullyQualifiedErrorId : DirectoryExist,Microsoft.PowerShell.Commands.NewItemCommand

PS E:\workspace\resume>
```

---

```diff
- 
+ # 简历制作应用开发文档 v1.0
+ 
+ ## 1. 项目概述
+ 
+ ### 1.1 项目介绍
+ 本项目是一个功能完善的在线简历制作平台，采用前后端分离架构，支持多端访问。用户可以通过Web端、小程序、App等多种方式访问平台，选择专业模板，在线编辑个人简历，并支持多种格式导出和分享功能。
+ 
+ ### 1.2 项目特色
+ - **多端支持**: Web端、微信小程序、支付宝小程序、App等全平台覆盖
+ - **国际化**: 支持中文、英文、日文、韩文等多语言
+ - **多样化模板**: 提供丰富的免费和付费简历模板
+ - **智能编辑**: 所见即所得的在线编辑体验
+ - **会员体系**: 完善的付费会员机制和权限控制
+ - **云端同步**: 数据云端存储，多设备同步访问
+ - **安全可靠**: 完善的用户认证和数据安全保护
+ 
+ ### 1.3 目标用户
+ - 求职者：应届毕业生、职场人士、转行人群
+ - 学生群体：在校学生、实习生
+ - 自由职业者：设计师、程序员、咨询师等
+ - HR招聘人员：查看和评估候选人简历
+ 
+ ## 2. 技术架构
+ 
+ ### 2.1 整体架构
+ ```
+ ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
+ │   Web前端   │  │ 小程序前端  │  │   App前端   │  │  管理后台   │
+ │  (Vue.js)   │  │  (UniApp)   │  │  (UniApp)   │  │  (Vue.js)   │
+ └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘
+        │                │                │                │
+        └────────────────┼────────────────┼────────────────┘
+                         │                │
+                         ▼                ▼
+                  ┌─────────────────────────────┐
+                  │        API网关             │
+                  │       (Nginx)              │
+                  └─────────────────────────────┘
+                               │
+                               ▼
+                  ┌─────────────────────────────┐
+                  │       后端服务集群          │
+                  │    (Spring Boot)           │
+                  │  ┌─────────┐ ┌─────────┐   │
+                  │  │用户服务 │ │简历服务 │   │
+                  │  └─────────┘ └─────────┘   │
+                  │  ┌─────────┐ ┌─────────┐   │
+                  │  │支付服务 │ │文件服务 │   │
+                  │  └─────────┘ └─────────┘   │
+                  └─────────────────────────────┘
+                               │
+                     ┌─────────┼─────────┐
+                     ▼         ▼         ▼
+             ┌─────────┐ ┌─────────┐ ┌─────────┐
+             │  MySQL  │ │  Redis  │ │   OSS   │
+             │  数据库  │ │  缓存   │ │ 文件存储 │
+             └─────────┘ └─────────┘ └─────────┘
+ ```
+ 
+ ### 2.2 微服务划分
+ - **用户服务**: 用户注册、登录、认证、权限管理
+ - **简历服务**: 简历创建、编辑、模板管理、导出
+ - **支付服务**: 订单管理、支付处理、会员管理
+ - **文件服务**: 文件上传、存储、CDN分发
+ - **通知服务**: 消息推送、邮件发送、短信发送
+ 
+ ## 3. 技术栈详情
+ 
+ ### 3.1 后端技术栈
+ - **开发框架**: Spring Boot 3.2.12
+ - **开发语言**: Java 17
+ - **数据库**: MySQL 8.0+
+ - **缓存**: Redis 7.0+
+ - **持久层框架**: MyBatis-Plus 3.5+
+ - **安全框架**: Spring Security 6.0 + JWT
+ - **API文档**: Swagger 3 + Knife4j
+ - **对象映射**: MapStruct
+ - **工具库**: Lombok、Hutool
+ - **消息队列**: RabbitMQ 3.12+
+ - **搜索引擎**: Elasticsearch 8.0+
+ - **文件存储**: 阿里云OSS / 腾讯云COS
+ - **支付集成**: 微信支付v3 + 支付宝开放平台
+ - **容器化**: Docker + Docker Compose
+ - **监控**: Spring Boot Actuator + Micrometer
+ 
+ ### 3.2 前端技术栈
+ 
+ #### 3.2.1 Web端技术栈
+ - **开发框架**: Vue 3.4+ + TypeScript
+ - **构建工具**: Vite 5.0+
+ - **UI组件库**: Element Plus 2.4+
+ - **状态管理**: Pinia 2.1+
+ - **路由管理**: Vue Router 4.2+
+ - **HTTP客户端**: Axios 1.6+
+ - **CSS预处理器**: Sass/SCSS
+ - **代码规范**: ESLint + Prettier
+ - **打包优化**: Rollup
+ - **图表库**: ECharts 5.4+
+ - **富文本编辑器**: TinyMCE / Quill
+ - **PDF生成**: html2canvas + jsPDF
+ 
+ #### 3.2.2 UniApp端技术栈
+ - **开发框架**: UniApp + Vue 3 + TypeScript
+ - **UI组件库**: uView Plus 3.0+
+ - **状态管理**: Pinia
+ - **HTTP请求**: uni.request封装
+ - **小程序SDK**: 微信小程序SDK、支付宝小程序SDK
+ - **支付集成**: 微信支付、支付宝支付
+ - **地图服务**: 腾讯地图、高德地图
+ - **统计分析**: 微信小程序统计、友盟统计
+ 
+ ### 3.3 DevOps工具链
+ - **版本控制**: Git + GitLab
+ - **CI/CD**: GitLab CI / Jenkins
+ - **容器编排**: Docker Swarm / Kubernetes
+ - **服务网格**: Istio
+ - **日志收集**: ELK Stack (Elasticsearch + Logstash + Kibana)
+ - **监控告警**: Prometheus + Grafana + AlertManager
+ - **链路追踪**: Jaeger / SkyWalking
+ 
+ ## 4. 数据库设计详解
+ 
+ ### 4.1 数据库设计原则
+ - **标准化设计**: 遵循第三范式，避免数据冗余
+ - **性能优化**: 合理设置索引，优化查询性能
+ - **扩展性**: 预留扩展字段，支持业务发展
+ - **安全性**: 敏感数据加密存储，设置访问权限
+ - **国际化**: 支持UTF8mb4字符集，兼容多语言
+ 
+ ### 4.2 核心表组说明
+ 
+ #### 4.2.1 用户管理表组
+ - **users**: 用户基础信息表，存储用户账号、个人信息、偏好设置
+ - **user_third_party_auths**: 第三方授权表，支持微信、QQ、支付宝等平台登录
+ - **user_merge_records**: 账号合并记录表，处理用户多账号合并场景
+ 
+ #### 4.2.2 会员体系表组
+ - **membership_packages**: 会员套餐表，定义不同会员等级和权益
+ - **user_memberships**: 用户会员表，记录用户会员状态和使用情况
+ 
+ #### 4.2.3 简历模板表组
+ - **template_categories**: 模板分类表，按行业、风格等维度分类
+ - **resume_templates**: 简历模板表，存储模板信息和配置数据
+ 
+ #### 4.2.4 简历数据表组
+ - **resumes**: 简历主表，存储简历基本信息和配置
+ - **resume_basic_info**: 基本信息表，个人联系方式和求职意向
+ - **resume_educations**: 教育经历表，学校、专业、学历信息
+ - **resume_work_experiences**: 工作经历表，公司、职位、工作描述
+ - **resume_projects**: 项目经验表，项目名称、技术栈、个人贡献
+ - **resume_skills**: 技能标签表，技能名称、熟练程度、分类
+ - **resume_certificates**: 证书资质表，证书名称、颁发机构、有效期
+ - **resume_awards**: 获奖荣誉表，奖项名称、级别、颁发机构
+ - **resume_languages**: 语言能力表，语言种类、水平、证书成绩
+ - **其他简历模块表**: 实习经历、研究经历、论文专利、培训经历、志愿服务、作品集、自我评价、推荐人等
+ 
+ #### 4.2.5 用户行为表组
+ - **user_favorites**: 收藏表，用户收藏的模板和简历
+ - **share_records**: 分享记录表，简历分享链接和统计
+ - **resume_export_records**: 导出记录表，简历导出历史和格式
+ - **user_operation_logs**: 操作日志表，用户行为轨迹记录
+ - **user_access_logs**: 访问日志表，IP地理位置和语言检测
+ 
+ #### 4.2.6 支付订单表组
+ - **orders**: 订单表，会员充值和模板购买订单
+ 
+ #### 4.2.7 系统配置表组
+ - **system_configs**: 系统配置表，全局参数配置
+ - **supported_languages**: 支持语言表，多语言配置信息
+ 
+ ### 4.3 索引设计策略
+ - **主键索引**: 所有表使用BIGINT自增主键
+ - **唯一索引**: 用户邮箱、手机号、第三方OpenID等唯一标识
+ - **复合索引**: 常用查询条件组合，如用户ID+时间范围
+ - **覆盖索引**: 避免回表查询，提升查询性能
+ - **前缀索引**: 长字符串字段使用前缀索引节省空间
+ 
+ ## 5. 核心功能模块
+ 
+ ### 5.1 用户管理模块
+ 
+ #### 5.1.1 功能特性
+ - 多种注册方式（邮箱、手机、第三方授权）
+ - 第三方登录（微信、QQ、支付宝等）
+ - 账号合并机制
+ - 用户信息管理
+ - 多语言支持
+ - 用户权限控制
+ - 登录状态管理
+ - 密码安全策略
+ 
+ #### 5.1.2 主要接口
+ - 用户认证相关
+   - POST /api/auth/register - 用户注册
+   - POST /api/auth/login - 用户登录
+   - POST /api/auth/logout - 用户登出
+   - POST /api/auth/refresh - 刷新Token
+   - POST /api/auth/send-code - 发送验证码
+   - POST /api/auth/third-party - 第三方登录
+ 
+ - 用户信息相关
+   - GET /api/user/profile - 获取用户信息
+   - PUT /api/user/profile - 更新用户信息
+   - POST /api/user/avatar - 上传头像
+   - PUT /api/user/password - 修改密码
+   - GET /api/user/settings - 获取用户设置
+   - PUT /api/user/settings - 更新用户设置
+ 
+ ### 5.2 简历管理模块
+ 
+ #### 5.2.1 功能特性
+ - 简历创建与编辑
+ - 多种简历模块（基本信息、教育经历、工作经验等）
+ - 模块显示/隐藏配置
+ - 简历预览与导出
+ - 多语言简历支持
+ - 简历版本管理
+ - 简历模板应用
+ - 智能内容推荐
+ 
+ #### 5.2.2 主要接口
+ - 简历管理
+   - GET /api/resume/list - 获取简历列表
+   - POST /api/resume - 创建简历
+   - GET /api/resume/detail/{id} - 获取简历详情
+   - PUT /api/resume/update/{id} - 更新简历
+   - DELETE /api/resume/delete/{id} - 删除简历
+   - POST /api/resume/copy/{id} - 复制简历
+ 
+ - 简历模块管理
+   - GET /api/resume/modules/{id} - 获取简历模块
+   - PUT /api/resume/modules/{id} - 更新模块配置
+   - POST /api/resume/basic/{id} - 更新基本信息
+   - POST /api/resume/education/{id} - 添加教育经历
+   - PUT /api/resume/education/{id}/{eduId} - 更新教育经历
+   - DELETE /api/resume/education/{eduId} - 删除教育经历
+   - POST /api/resume/work/{id} - 添加工作经历
+   - PUT /api/resume/work/{id}/{workId} - 更新工作经历
+   - DELETE /api/resume/work/{workId} - 删除工作经历
+   - POST /api/resume/project/{id} - 添加项目经验
+   - PUT /api/resume/project/{id}/{projectId} - 更新项目经验
+   - DELETE /api/resume/project/{projectId} - 删除项目经验
+   - POST /api/resume/skill/{id} - 添加技能
+   - PUT /api/resume/skill/{id}/{skillId} - 更新技能
+   - DELETE /api/resume/skill/{skillId} - 删除技能
+ 
+ - 简历导出分享
+   - POST /api/resume/export/{id} - 导出简历
+   - GET /api/resume/preview/{id} - 预览简历
+   - POST /api/resume/share/{id} - 创建分享链接
+   - GET /api/resume/share/view/{code} - 访问分享简历
+ 
+ ### 5.3 模板管理模块
+ 
+ #### 5.3.1 功能特性
+ - 模板分类管理
+ - 免费/付费模板
+ - 模板预览与使用
+ - 模板收藏功能
+ - 模板搜索筛选
+ - 模板使用统计
+ - 模板个性化定制
+ - 模板评价系统
+ 
+ #### 5.3.2 主要接口
+ - 模板相关
+   - GET /api/template/categories - 获取模板分类
+   - GET /api/template/list - 获取模板列表
+   - GET /api/template/detail/{id} - 获取模板详情
+   - POST /api/template/use/{id} - 使用模板创建简历
+   - GET /api/template/search - 搜索模板
+   - GET /api/template/recommend - 推荐模板
+ 
+ - 收藏相关
+   - POST /api/favorite/template/{id} - 收藏模板
+   - DELETE /api/favorite/template/{id} - 取消收藏
+   - GET /api/favorite/templates - 获取收藏模板列表
+ 
+ ### 5.4 会员体系模块
+ 
+ #### 5.4.1 功能特性
+ - 会员套餐管理
+ - 权限控制（简历数量、导出次数、付费模板）
+ - 会员到期提醒
+ - 使用量统计
+ - 会员特权展示
+ - 升级引导流程
+ 
+ #### 5.4.2 主要接口
+ - 会员相关
+   - GET /api/membership/packages - 获取会员套餐
+   - GET /api/membership/current - 获取当前会员信息
+   - POST /api/membership/purchase - 购买会员
+   - GET /api/membership/usage - 获取使用统计
+   - GET /api/membership/benefits - 获取会员权益
+ 
+ ### 5.5 支付订单模块
+ 
+ #### 5.5.1 功能特性
+ - 微信支付集成
+ - 支付宝支付集成
+ - 订单管理
+ - 支付回调处理
+ - 退款处理
+ - 发票管理
+ - 支付安全保障
+ 
+ #### 5.5.2 主要接口
+ - 支付相关
+   - POST /api/payment/create - 创建支付订单
+   - GET /api/payment/status/{orderNo} - 查询支付状态
+   - POST /api/payment/callback/wechat - 微信支付回调
+   - POST /api/payment/callback/alipay - 支付宝支付回调
+   - POST /api/payment/refund/{orderNo} - 申请退款
+ 
+ - 订单相关
+   - GET /api/order/list - 获取订单列表
+   - GET /api/order/detail/{orderNo} - 获取订单详情
+   - PUT /api/order/cancel/{orderNo} - 取消订单
+ 
+ ### 5.6 文件管理模块
+ 
+ #### 5.6.1 功能特性
+ - 文件上传下载
+ - 图片压缩处理
+ - 文件格式验证
+ - 存储空间管理
+ - CDN加速分发
+ - 文件安全扫描
+ 
+ #### 5.6.2 主要接口
+ - 文件相关
+   - POST /api/file/upload - 文件上传
+   - GET /api/file/download/{fileId} - 文件下载
+   - DELETE /api/file/delete/{fileId} - 删除文件
+   - GET /api/file/list - 获取文件列表
+ 
+ ## 6. 开发规范
+ 
+ ### 6.1 代码规范
+ 
+ #### 6.1.1 命名规范
+ - **类名**: PascalCase（如：UserController、ResumeService）
+ - **方法名**: camelCase（如：getUserInfo、createResume）
+ - **变量名**: camelCase（如：userId、resumeList）
+ - **常量**: UPPER_SNAKE_CASE（如：MAX_FILE_SIZE、DEFAULT_PAGE_SIZE）
+ - **包名**: 全小写，用点分隔（如：com.alan6.resume.service）
+ - **数据库表名**: 小写下划线（如：user_profiles、resume_templates）
+ - **数据库字段名**: 小写下划线（如：user_id、created_time）
+ 
+ #### 6.1.2 注释规范
+ - 类和公共方法必须有Javadoc注释
+ - 复杂业务逻辑添加行内注释
+ - 注释内容要准确描述功能和参数
+ - 及时更新注释内容，保持与代码同步
+ 
+ #### 6.1.3 异常处理
+ - 统一异常处理机制
+ - 自定义业务异常
+ - 规范错误码定义
+ - 记录详细错误日志
+ 
+ #### 6.1.4 日志规范
+ - 使用统一的日志格式
+ - 合理设置日志级别
+ - 敏感信息脱敏处理
+ - 性能关键点记录耗时
+ 
+ ### 6.2 API设计规范
+ 
+ #### 6.2.1 RESTful设计
+ - **GET**: 查询操作，幂等操作
+ - **POST**: 创建操作，非幂等操作
+ - **PUT**: 更新操作，幂等操作
+ - **DELETE**: 删除操作，幂等操作
+ - **PATCH**: 部分更新操作
+ 
+ #### 6.2.2 URL设计规范
+ - 使用名词而非动词
+ - 资源层级不超过3层
+ - 使用复数形式表示集合
+ - 路径参数置于URL末尾
+ - 使用连字符分隔单词
+ 
+ #### 6.2.3 统一返回格式
+ ```json
+ {
+   "code": 200,
+   "message": "success",
+   "data": {},
+   "timestamp": 1234567890
+ }
+ ```
+ 
+ #### 6.2.4 分页查询格式
+ ```json
+ {
+   "code": 200,
+   "message": "success",
+   "data": {
+     "records": [],
+     "total": 100,
+     "current": 1,
+     "size": 10,
+     "pages": 10
+   },
+   "timestamp": 1234567890
+ }
+ ```
+ 
+ #### 6.2.5 错误码定义
+ - 200: 成功
+ - 400: 请求参数错误
+ - 401: 未授权
+ - 403: 禁止访问
+ - 404: 资源不存在
+ - 500: 服务器内部错误
+ - 1001-1999: 用户相关错误
+ - 2001-2999: 简历相关错误
+ - 3001-3999: 模板相关错误
+ - 4001-4999: 支付相关错误
+ 
+ ### 6.3 数据库规范
+ 
+ #### 6.3.1 表设计规范
+ - 表名使用小写下划线命名
+ - 主键统一使用id，类型为BIGINT
+ - 创建时间字段命名为create_time
+ - 更新时间字段命名为update_time
+ - 软删除字段命名为is_deleted
+ - 状态字段使用TINYINT类型
+ 
+ #### 6.3.2 索引规范
+ - 主键自动创建聚集索引
+ - 外键字段创建普通索引
+ - 查询条件字段创建索引
+ - 复合索引遵循最左前缀原则
+ - 避免在小表上创建索引
+ 
+ #### 6.3.3 SQL编写规范
+ - 禁止使用SELECT *
+ - 合理使用LIMIT限制返回记录数
+ - 复杂查询拆分为多个简单查询
+ - 使用预编译语句防止SQL注入
+ 
+ ### 6.4 安全规范
+ 
+ #### 6.4.1 认证授权
+ - 使用JWT Token进行身份认证
+ - Token设置合理的过期时间
+ - 敏感操作要求重新认证
+ - 实现细粒度的权限控制
+ 
+ #### 6.4.2 数据安全
+ - 密码使用BCrypt加密存储
+ - 敏感信息传输使用HTTPS
+ - 个人信息脱敏处理
+ - 定期备份重要数据
+ 
+ #### 6.4.3 接口安全
+ - 实现接口访问频率限制
+ - 验证请求参数的合法性
+ - 防止SQL注入和XSS攻击
+ - 记录安全相关的操作日志
+ 
+ ## 7. 部署配置
+ 
+ ### 7.1 开发环境
+ 
+ #### 7.1.1 环境要求
+ - JDK 17+
+ - Maven 3.6+
+ - MySQL 8.0+
+ - Redis 7.0+
+ - Node.js 18+
+ - Git
+ 
+ #### 7.1.2 启动步骤
+ 1. **数据库初始化**
+    - 创建数据库
+    - 执行建表脚本
+    - 导入初始数据
+ 
+ 2. **后端启动**
+    - 配置数据库连接
+    - 配置Redis连接
+    - 启动Spring Boot应用
+ 
+ 3. **Web前端启动**
+    - 安装依赖包
+    - 配置API接口地址
+    - 启动开发服务器
+ 
+ 4. **UniApp启动**
+    - 使用HBuilderX导入项目
+    - 配置小程序AppID
+    - 运行到相应平台
+ 
+ ### 7.2 测试环境
+ 
+ #### 7.2.1 环境配置
+ - 使用Docker容器化部署
+ - 配置CI/CD自动部署
+ - 集成自动化测试
+ - 配置监控告警
+ 
+ #### 7.2.2 测试数据
+ - 准备完整的测试数据集
+ - 模拟各种业务场景
+ - 压力测试和性能测试
+ - 安全漏洞扫描
+ 
+ ### 7.3 生产环境
+ 
+ #### 7.3.1 基础架构
+ - 负载均衡配置
+ - 数据库主从复制
+ - Redis集群部署
+ - CDN加速配置
+ - 备份恢复策略
+ 
+ #### 7.3.2 监控运维
+ - 应用性能监控
+ - 数据库性能监控
+ - 服务器资源监控
+ - 日志收集分析
+ - 告警通知机制
+ 
+ ## 8. 性能优化
+ 
+ ### 8.1 后端优化
+ 
+ #### 8.1.1 数据库优化
+ - 合理设计索引
+ - 优化SQL查询语句
+ - 使用连接池管理连接
+ - 读写分离配置
+ - 分库分表策略
+ 
+ #### 8.1.2 缓存优化
+ - Redis缓存热点数据
+ - 多级缓存架构
+ - 缓存更新策略
+ - 缓存穿透防护
+ - 缓存雪崩防护
+ 
+ #### 8.1.3 接口优化
+ - 异步处理耗时操作
+ - 接口响应数据压缩
+ - 分页查询优化
+ - 批量操作接口
+ - 接口版本管理
+ 
+ ### 8.2 前端优化
+ 
+ #### 8.2.1 加载优化
+ - 代码分割和懒加载
+ - 图片懒加载
+ - 资源预加载
+ - CDN加速
+ - 浏览器缓存策略
+ 
+ #### 8.2.2 渲染优化
+ - 虚拟滚动
+ - 防抖和节流
+ - 避免频繁DOM操作
+ - 优化动画性能
+ - 减少重绘重排
+ 
+ #### 8.2.3 体验优化
+ - 骨架屏加载
+ - 离线缓存
+ - 错误边界处理
+ - 用户操作反馈
+ - 无障碍访问支持
+ 
+ ## 9. 测试策略
+ 
+ ### 9.1 单元测试
+ - 使用JUnit进行Java单元测试
+ - 使用Jest进行前端单元测试
+ - 测试覆盖率要求达到80%以上
+ - 核心业务逻辑必须有单元测试
+ 
+ ### 9.2 集成测试
+ - API接口集成测试
+ - 数据库集成测试
+ - 第三方服务集成测试
+ - 端到端业务流程测试
+ 
+ ### 9.3 性能测试
+ - 接口压力测试
+ - 数据库性能测试
+ - 前端页面性能测试
+ - 并发用户测试
+ 
+ ### 9.4 安全测试
+ - SQL注入测试
+ - XSS攻击测试
+ - CSRF攻击测试
+ - 权限越权测试
+ - 敏感信息泄露测试
+ 
+ ## 10. 版本管理
+ 
+ ### 10.1 版本号规范
+ - 使用语义化版本号：MAJOR.MINOR.PATCH
+ - MAJOR：不兼容的API修改
+ - MINOR：向下兼容的功能新增
+ - PATCH：向下兼容的问题修正
+ 
+ ### 10.2 分支管理
+ - master分支：生产环境代码
+ - develop分支：开发环境代码
+ - feature分支：功能开发分支
+ - hotfix分支：紧急修复分支
+ - release分支：版本发布分支
+ 
+ ### 10.3 发布流程
+ 1. 功能开发完成合并到develop分支
+ 2. 创建release分支进行测试
+ 3. 测试通过后合并到master分支
+ 4. 打标签发布新版本
+ 5. 部署到生产环境
+ 
+ ## 11. 常见问题
+ 
+ ### 11.1 开发问题
+ - **跨域问题**: 配置CORS允许跨域访问
+ - **文件上传大小限制**: 调整服务器和应用配置
+ - **数据库连接超时**: 优化连接池配置
+ - **内存溢出**: 调整JVM参数和优化代码
+ 
+ ### 11.2 部署问题
+ - **端口冲突**: 检查并修改端口配置
+ - **权限不足**: 设置正确的文件和目录权限
+ - **服务启动失败**: 查看日志文件排查错误
+ - **数据库连接失败**: 检查网络和账号权限
+ 
+ ### 11.3 性能问题
+ - **接口响应慢**: 优化SQL查询和业务逻辑
+ - **页面加载慢**: 优化前端资源和缓存策略
+ - **并发处理能力不足**: 增加服务器资源和优化架构
+ - **数据库查询慢**: 添加索引和优化查询语句
+ 
+ ## 12. 联系方式
+ 
+ ### 12.1 开发团队
+ - **技术负责人**: [姓名] - [邮箱]
+ - **后端开发**: [姓名] - [邮箱]
+ - **前端开发**: [姓名] - [邮箱]
+ - **UI设计**: [姓名] - [邮箱]
+ - **测试工程师**: [姓名] - [邮箱]
+ 
+ ### 12.2 技术支持
+ - **项目仓库**: [Git仓库地址]
+ - **文档中心**: [文档地址]
+ - **问题反馈**: [问题跟踪地址]
+ - **技术交流群**: [群号/二维码]
+ 
+ ---
+ 
+ **文档版本**: v1.0  
+ **最后更新**: 2024年12月  
+ **更新人员**: 开发团队  
+ **下次更新**: 根据项目进展更新
+ 
```

---

完成！我已经成功创建了详细的开发文档，并保存到 `files/开发文档1_0.md` 文件中。

这个开发文档包含了以下主要内容：

## 主要特点：

1. **项目概述** - 详细的项目介绍、特色功能和目标用户
2. **技术架构** - 完整的系统架构图和微服务划分
3. **技术栈详情** - 后端、前端、UniApp和DevOps工具链
4. **数据库设计** - 详细的表结构说明和索引策略
5. **核心功能模块** - 6大核心模块的功能特性和API接口
6. **开发规范** - 代码规范、API设计、数据库规范、安全规范
7. **部署配置** - 开发、测试、生产环境的配置说明
8. **性能优化** - 后端和前端的优化策略
9. **测试策略** - 单元测试、集成测试、性能测试、安全测试
10. **版本管理** - 版本号规范、分支管理、发布流程
11. **常见问题** - 开发、部署、性能问题的解决方案
12. **联系方式** - 团队信息和技术支持

## 接口URL格式修改：

按照您的要求，所有接口URL中的变量都已经放到最后，例如：
- `GET /api/resume/detail/{id}` 
- `PUT /api/resume/update/{id}`
- `GET /api/resume/modules/{id}`
- `POST /api/resume/education/{id}/{eduId}`

这份文档现在已经保存在 `files/开发文档1_0.md` 文件中，您可以直接使用了！

---

_**User**_

给出一个详细的后端接口文档，不要写入文件，我先看看

---

_**Assistant**_



---

# 简历应用后端API接口文档

## 1. 接口概述

### 1.1 基础信息
- **Base URL**: `https://api.resume.com`
- **API版本**: v1.0
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用规范

#### 1.2.1 请求头
```
Content-Type: application/json
Authorization: Bearer {token}
Accept-Language: zh-CN,en,ja,ko
```

#### 1.2.2 统一响应格式
```json
{
  "code": 200,
  "message": "success", 
  "data": {},
  "timestamp": 1703001234567
}
```

#### 1.2.3 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  },
  "timestamp": 1703001234567
}
```

#### 1.2.4 错误码定义
- **200**: 成功
- **400**: 请求参数错误
- **401**: 未授权
- **403**: 禁止访问  
- **404**: 资源不存在
- **429**: 请求过于频繁
- **500**: 服务器内部错误
- **1001-1999**: 用户相关错误
- **2001-2999**: 简历相关错误
- **3001-3999**: 模板相关错误
- **4001-4999**: 支付相关错误

---

## 2. 认证授权模块

### 2.1 用户注册
**接口**: `POST /api/auth/register`

**请求参数**:
```json
{
  "registerType": 1,
  "email": "<EMAIL>",
  "phone": "13800138000", 
  "password": "password123",
  "confirmPassword": "password123",
  "verificationCode": "123456",
  "nickname": "张三",
  "agreeTerms": true,
  "platform": "web"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": 1001,
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1001,
      "nickname": "张三",
      "email": "<EMAIL>",
      "avatar": null,
      "membershipLevel": 0
    }
  }
}
```

### 2.2 用户登录
**接口**: `POST /api/auth/login`

**请求参数**:
```json
{
  "loginType": 1,
  "account": "<EMAIL>",
  "password": "password123",
  "verificationCode": "123456",
  "rememberMe": true,
  "platform": "web"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1001,
      "nickname": "张三",
      "email": "<EMAIL>",
      "avatar": "https://cdn.example.com/avatar/1001.jpg",
      "membershipLevel": 1,
      "membershipExpireTime": "2024-12-31 23:59:59"
    }
  }
}
```

### 2.3 第三方登录
**接口**: `POST /api/auth/third-party`

**请求参数**:
```json
{
  "platform": "wechat_mp",
  "code": "authorization_code",
  "state": "random_state",
  "userInfo": {
    "nickname": "微信用户",
    "avatar": "https://wx.qlogo.cn/mmopen/..."
  }
}
```

### 2.4 刷新Token
**接口**: `POST /api/auth/refresh`

**请求参数**:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 2.5 发送验证码
**接口**: `POST /api/auth/send-code`

**请求参数**:
```json
{
  "type": 1,
  "target": "<EMAIL>",
  "scene": "register"
}
```

### 2.6 退出登录
**接口**: `POST /api/auth/logout`

**请求参数**: 无

**响应数据**:
```json
{
  "code": 200,
  "message": "退出成功",
  "data": null
}
```

---

## 3. 用户管理模块

### 3.1 获取用户信息
**接口**: `GET /api/user/profile`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,
    "username": "user001",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "nickname": "张三",
    "avatar": "https://cdn.example.com/avatar/1001.jpg",
    "gender": 1,
    "birthday": "1995-05-20",
    "preferredLanguage": "zh-CN",
    "registerTime": "2024-01-01 10:00:00",
    "lastLoginTime": "2024-12-01 15:30:00",
    "membershipLevel": 1,
    "membershipExpireTime": "2024-12-31 23:59:59"
  }
}
```

### 3.2 更新用户信息
**接口**: `PUT /api/user/profile`

**请求参数**:
```json
{
  "nickname": "张三三",
  "gender": 1,
  "birthday": "1995-05-20",
  "preferredLanguage": "zh-CN"
}
```

### 3.3 上传头像
**接口**: `POST /api/user/avatar`

**请求参数**: multipart/form-data
- `file`: 头像文件

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "avatarUrl": "https://cdn.example.com/avatar/1001.jpg"
  }
}
```

### 3.4 修改密码
**接口**: `PUT /api/user/password`

**请求参数**:
```json
{
  "oldPassword": "oldpassword123",
  "newPassword": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

### 3.5 获取用户设置
**接口**: `GET /api/user/settings`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "preferredLanguage": "zh-CN",
    "emailNotification": true,
    "smsNotification": false,
    "privacySettings": {
      "allowSearch": true,
      "showOnlineStatus": false
    }
  }
}
```

### 3.6 更新用户设置
**接口**: `PUT /api/user/settings`

**请求参数**:
```json
{
  "preferredLanguage": "en",
  "emailNotification": false,
  "smsNotification": true,
  "privacySettings": {
    "allowSearch": false,
    "showOnlineStatus": true
  }
}
```

---

## 4. 简历管理模块

### 4.1 获取简历列表
**接口**: `GET /api/resume/list`

**请求参数**:
- `current`: 当前页码 (默认: 1)
- `size`: 每页大小 (默认: 10)
- `keyword`: 搜索关键词
- `status`: 简历状态 (0:草稿, 1:完成)
- `language`: 简历语言

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 2001,
        "name": "前端开发工程师简历",
        "templateId": 3001,
        "templateName": "简约商务模板",
        "language": "zh-CN",
        "status": 1,
        "isPublic": 0,
        "viewCount": 15,
        "downloadCount": 3,
        "createTime": "2024-11-01 10:00:00",
        "updateTime": "2024-11-15 16:30:00"
      }
    ],
    "total": 5,
    "current": 1,
    "size": 10,
    "pages": 1
  }
}
```

### 4.2 创建简历
**接口**: `POST /api/resume`

**请求参数**:
```json
{
  "name": "我的简历",
  "templateId": 3001,
  "language": "zh-CN",
  "fromResumeId": null
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "resumeId": 2002,
    "name": "我的简历",
    "templateId": 3001,
    "language": "zh-CN",
    "status": 0,
    "createTime": "2024-12-01 10:00:00"
  }
}
```

### 4.3 获取简历详情
**接口**: `GET /api/resume/detail/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "resumeInfo": {
      "id": 2001,
      "name": "前端开发工程师简历",
      "templateId": 3001,
      "language": "zh-CN",
      "status": 1,
      "customStyles": {},
      "createTime": "2024-11-01 10:00:00",
      "updateTime": "2024-11-15 16:30:00"
    },
    "moduleConfig": [
      {
        "moduleType": "basic_info",
        "moduleName": "基本信息",
        "isVisible": true,
        "sortOrder": 1,
        "customTitle": null
      },
      {
        "moduleType": "education",
        "moduleName": "教育经历", 
        "isVisible": true,
        "sortOrder": 2,
        "customTitle": null
      }
    ],
    "resumeData": {
      "basicInfo": {
        "realName": "张三",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "address": "北京市海淀区",
        "birthday": "1995-05-20",
        "gender": 1,
        "jobIntention": "前端开发工程师",
        "expectedSalary": "15K-25K",
        "workYears": 3,
        "personalWebsite": "https://zhangsan.dev",
        "githubUrl": "https://github.com/zhangsan"
      },
      "educations": [
        {
          "id": 5001,
          "schoolName": "北京大学",
          "major": "计算机科学与技术",
          "degree": "本科",
          "startDate": "2016-09",
          "endDate": "2020-06",
          "isCurrent": false,
          "gpa": "3.8/4.0",
          "description": "主修课程：数据结构、算法、计算机网络等"
        }
      ],
      "workExperiences": [],
      "projects": [],
      "skills": [],
      "certificates": [],
      "awards": [],
      "languages": []
    }
  }
}
```

### 4.4 更新简历基本信息
**接口**: `PUT /api/resume/update/{id}`

**请求参数**:
```json
{
  "name": "高级前端开发简历",
  "language": "zh-CN",
  "status": 1,
  "customStyles": {
    "primaryColor": "#007bff",
    "fontSize": "14px"
  }
}
```

### 4.5 删除简历
**接口**: `DELETE /api/resume/delete/{id}`

### 4.6 复制简历
**接口**: `POST /api/resume/copy/{id}`

**请求参数**:
```json
{
  "name": "简历副本",
  "language": "en"
}
```

### 4.7 获取简历模块配置
**接口**: `GET /api/resume/modules/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "moduleType": "basic_info",
      "moduleName": "基本信息",
      "isVisible": true,
      "sortOrder": 1,
      "customTitle": null
    },
    {
      "moduleType": "education", 
      "moduleName": "教育经历",
      "isVisible": true,
      "sortOrder": 2,
      "customTitle": "学习经历"
    }
  ]
}
```

### 4.8 更新模块配置
**接口**: `PUT /api/resume/modules/{id}`

**请求参数**:
```json
{
  "modules": [
    {
      "moduleType": "basic_info",
      "isVisible": true,
      "sortOrder": 1,
      "customTitle": "个人信息"
    },
    {
      "moduleType": "education",
      "isVisible": true, 
      "sortOrder": 2,
      "customTitle": "教育背景"
    }
  ]
}
```

### 4.9 更新基本信息
**接口**: `POST /api/resume/basic/{id}`

**请求参数**:
```json
{
  "realName": "张三",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "address": "北京市海淀区中关村",
  "birthday": "1995-05-20",
  "gender": 1,
  "jobIntention": "高级前端开发工程师",
  "expectedSalary": "20K-30K",
  "workYears": 5,
  "personalWebsite": "https://zhangsan.dev",
  "githubUrl": "https://github.com/zhangsan"
}
```

### 4.10 添加教育经历
**接口**: `POST /api/resume/education/{id}`

**请求参数**:
```json
{
  "schoolName": "清华大学",
  "major": "软件工程",
  "degree": "硕士",
  "startDate": "2020-09",
  "endDate": "2022-06",
  "isCurrent": false,
  "gpa": "3.9/4.0",
  "description": "研究方向：前端工程化、Web性能优化"
}
```

### 4.11 更新教育经历
**接口**: `PUT /api/resume/education/{id}/{eduId}`

### 4.12 删除教育经历
**接口**: `DELETE /api/resume/education/{eduId}`

### 4.13 添加工作经历
**接口**: `POST /api/resume/work/{id}`

**请求参数**:
```json
{
  "companyName": "字节跳动",
  "position": "前端开发工程师",
  "startDate": "2022-07",
  "endDate": "2024-11",
  "isCurrent": false,
  "jobDescription": "负责抖音创作者平台的前端开发工作",
  "achievements": "优化页面加载速度30%，提升用户体验"
}
```

### 4.14 更新工作经历
**接口**: `PUT /api/resume/work/{id}/{workId}`

### 4.15 删除工作经历
**接口**: `DELETE /api/resume/work/{workId}`

### 4.16 添加项目经验
**接口**: `POST /api/resume/project/{id}`

**请求参数**:
```json
{
  "projectName": "电商管理系统",
  "projectRole": "前端负责人",
  "startDate": "2023-01",
  "endDate": "2023-06",
  "projectDescription": "基于Vue3+TypeScript开发的电商后台管理系统",
  "responsibilities": "负责项目架构设计、核心功能开发、团队协作",
  "technologies": "Vue3, TypeScript, Element Plus, Vite",
  "projectUrl": "https://github.com/zhangsan/ecommerce-admin"
}
```

### 4.17 更新项目经验
**接口**: `PUT /api/resume/project/{id}/{projectId}`

### 4.18 删除项目经验
**接口**: `DELETE /api/resume/project/{projectId}`

### 4.19 添加技能
**接口**: `POST /api/resume/skill/{id}`

**请求参数**:
```json
{
  "skillName": "Vue.js",
  "skillLevel": 4,
  "skillCategory": "前端框架"
}
```

### 4.20 更新技能
**接口**: `PUT /api/resume/skill/{id}/{skillId}`

### 4.21 删除技能
**接口**: `DELETE /api/resume/skill/{skillId}`

### 4.22 导出简历
**接口**: `POST /api/resume/export/{id}`

**请求参数**:
```json
{
  "format": "pdf",
  "templateStyle": "default",
  "watermark": false
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "downloadUrl": "https://cdn.example.com/export/resume_2001_20241201.pdf",
    "fileName": "张三_前端开发工程师.pdf",
    "fileSize": 1024576,
    "expiresIn": 3600
  }
}
```

### 4.23 预览简历
**接口**: `GET /api/resume/preview/{id}`

**请求参数**:
- `templateId`: 模板ID (可选)
- `format`: 预览格式 (html/image)

### 4.24 创建分享链接
**接口**: `POST /api/resume/share/{id}`

**请求参数**:
```json
{
  "shareType": 1,
  "expireTime": "2024-12-31 23:59:59",
  "password": "123456"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "分享链接创建成功",
  "data": {
    "shareCode": "abc123def456",
    "shareUrl": "https://resume.com/share/abc123def456",
    "qrCodeUrl": "https://cdn.example.com/qr/abc123def456.png",
    "expireTime": "2024-12-31 23:59:59"
  }
}
```

### 4.25 访问分享简历
**接口**: `GET /api/resume/share/view/{code}`

**请求参数**:
- `password`: 访问密码 (如果设置了密码)

---

## 5. 模板管理模块

### 5.1 获取模板分类
**接口**: `GET /api/template/categories`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "商务风格",
      "description": "适合商务、金融、咨询等行业",
      "iconUrl": "https://cdn.example.com/icons/business.svg",
      "parentId": 0,
      "sortOrder": 1,
      "templateCount": 12
    },
    {
      "id": 2,
      "name": "创意设计",
      "description": "适合设计师、创意工作者",
      "iconUrl": "https://cdn.example.com/icons/creative.svg",
      "parentId": 0,
      "sortOrder": 2,
      "templateCount": 8
    }
  ]
}
```

### 5.2 获取模板列表
**接口**: `GET /api/template/list`

**请求参数**:
- `current`: 当前页码
- `size`: 每页大小
- `categoryId`: 分类ID
- `industry`: 适用行业
- `style`: 模板风格
- `isPremium`: 是否付费 (0:免费, 1:付费)
- `keyword`: 搜索关键词
- `sortBy`: 排序方式 (default:默认, popular:热门, latest:最新)

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 3001,
        "name": "简约商务模板",
        "templateCode": "business_simple_001",
        "description": "适合商务人士的简约风格模板",
        "previewImageUrl": "https://cdn.example.com/templates/3001/preview.jpg",
        "categoryId": 1,
        "categoryName": "商务风格",
        "industry": "商务",
        "style": "简约",
        "colorScheme": "蓝色",
        "isPremium": 0,
        "price": 0.00,
        "useCount": 1234,
        "isFavorited": false,
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 50,
    "current": 1,
    "size": 12,
    "pages": 5
  }
}
```

### 5.3 获取模板详情
**接口**: `GET /api/template/detail/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 3001,
    "name": "简约商务模板",
    "templateCode": "business_simple_001",
    "description": "这是一款专为商务人士设计的简约风格简历模板...",
    "previewImageUrl": "https://cdn.example.com/templates/3001/preview.jpg",
    "templateData": {
      "sections": [
        {
          "type": "basic_info",
          "layout": "left_aligned",
          "styles": {}
        }
      ]
    },
    "categoryId": 1,
    "categoryName": "商务风格",
    "industry": "商务",
    "style": "简约",
    "colorScheme": "蓝色",
    "isPremium": 0,
    "price": 0.00,
    "useCount": 1234,
    "isFavorited": false,
    "createTime": "2024-01-01 10:00:00",
    "relatedTemplates": [
      {
        "id": 3002,
        "name": "经典商务模板",
        "previewImageUrl": "https://cdn.example.com/templates/3002/preview.jpg",
        "isPremium": 1,
        "price": 9.90
      }
    ]
  }
}
```

### 5.4 使用模板创建简历
**接口**: `POST /api/template/use/{id}`

**请求参数**:
```json
{
  "resumeName": "基于模板的简历",
  "language": "zh-CN"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "简历创建成功",
  "data": {
    "resumeId": 2003,
    "resumeName": "基于模板的简历",
    "templateId": 3001,
    "redirectUrl": "/resume/edit/2003"
  }
}
```

### 5.5 搜索模板
**接口**: `GET /api/template/search`

**请求参数**:
- `keyword`: 搜索关键词
- `current`: 当前页码
- `size`: 每页大小

### 5.6 推荐模板
**接口**: `GET /api/template/recommend`

**请求参数**:
- `limit`: 推荐数量 (默认: 6)
- `excludeIds`: 排除的模板ID列表

### 5.7 收藏模板
**接口**: `POST /api/favorite/template/{id}`

### 5.8 取消收藏模板
**接口**: `DELETE /api/favorite/template/{id}`

### 5.9 获取收藏模板列表
**接口**: `GET /api/favorite/templates`

**请求参数**:
- `current`: 当前页码
- `size`: 每页大小

---

## 6. 会员体系模块

### 6.1 获取会员套餐
**接口**: `GET /api/membership/packages`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "packageName": "月度会员",
      "packageCode": "monthly",
      "description": "享受高级模板、无限导出等特权",
      "durationType": 2,
      "durationValue": 1,
      "originalPrice": 29.90,
      "currentPrice": 19.90,
      "maxResumeCount": -1,
      "maxExportCount": -1,
      "premiumTemplates": true,
      "isRecommended": false,
      "features": [
        "无限制创建简历",
        "使用所有付费模板",
        "无限制导出次数",
        "优先客服支持"
      ]
    },
    {
      "id": 2,
      "packageName": "年度会员",
      "packageCode": "yearly",
      "description": "最优惠的选择，享受全部会员特权",
      "durationType": 4,
      "durationValue": 1,
      "originalPrice": 299.90,
      "currentPrice": 199.90,
      "maxResumeCount": -1,
      "maxExportCount": -1,
      "premiumTemplates": true,
      "isRecommended": true,
      "features": [
        "包含月度会员所有特权",
        "专属客服一对一服务",
        "优先体验新功能",
        "专属会员标识"
      ]
    }
  ]
}
```

### 6.2 获取当前会员信息
**接口**: `GET /api/membership/current`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "membershipLevel": 1,
    "packageName": "月度会员",
    "startTime": "2024-11-01 00:00:00",
    "endTime": "2024-12-01 23:59:59",
    "remainingDays": 15,
    "maxResumeCount": -1,
    "usedResumeCount": 3,
    "maxExportCount": -1,
    "usedExportCount": 8,
    "premiumTemplates": true,
    "autoRenewal": false,
    "benefits": [
      "无限制创建简历",
      "使用所有付费模板", 
      "无限制导出次数",
      "优先客服支持"
    ]
  }
}
```

### 6.3 购买会员
**接口**: `POST /api/membership/purchase`

**请求参数**:
```json
{
  "packageId": 1,
  "paymentMethod": 1,
  "autoRenewal": false,
  "couponCode": "SAVE10"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": 4001,
    "orderNo": "ORD202412010001",
    "packageName": "月度会员",
    "originalAmount": 29.90,
    "discountAmount": 3.00,
    "finalAmount": 26.90,
    "paymentUrl": "https://pay.example.com/order/4001",
    "expireTime": "2024-12-01 15:30:00"
  }
}
```

### 6.4 获取使用统计
**接口**: `GET /api/membership/usage`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "resumeStats": {
      "maxCount": -1,
      "usedCount": 5,
      "remainingCount": -1
    },
    "exportStats": {
      "maxMonthlyCount": -1,
      "usedMonthlyCount": 12,
      "remainingMonthlyCount": -1,
      "lastResetTime": "2024-12-01 00:00:00"
    },
    "templateStats": {
      "premiumAccessible": true,
      "usedPremiumCount": 3
    }
  }
}
```

### 6.5 获取会员权益
**接口**: `GET /api/membership/benefits`

---

## 7. 支付订单模块

### 7.1 创建支付订单
**接口**: `POST /api/payment/create`

**请求参数**:
```json
{
  "orderType": 1,
  "productId": 1,
  "paymentMethod": 1,
  "returnUrl": "https://resume.com/payment/success",
  "platform": "web"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderNo": "ORD202412010001",
    "paymentUrl": "https://api.pay.weixin.qq.com/pay/unifiedorder",
    "qrCodeUrl": "https://cdn.example.com/qr/payment_4001.png",
    "finalAmount": 19.90,
    "expireTime": "2024-12-01 15:30:00"
  }
}
```

### 7.2 查询支付状态
**接口**: `GET /api/payment/status/{orderNo}`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "orderNo": "ORD202412010001",
    "orderStatus": 2,
    "payTime": "2024-12-01 14:30:00",
    "tradeNo": "wx_123456789",
    "finalAmount": 19.90
  }
}
```

### 7.3 微信支付回调
**接口**: `POST /api/payment/callback/wechat`

### 7.4 支付宝支付回调
**接口**: `POST /api/payment/callback/alipay`

### 7.5 申请退款
**接口**: `POST /api/payment/refund/{orderNo}`

**请求参数**:
```json
{
  "refundReason": "不满意服务质量",
  "refundAmount": 19.90
}
```

### 7.6 获取订单列表
**接口**: `GET /api/order/list`

**请求参数**:
- `current`: 当前页码
- `size`: 每页大小
- `orderType`: 订单类型
- `orderStatus`: 订单状态
- `startTime`: 开始时间
- `endTime`: 结束时间

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 4001,
        "orderNo": "ORD202412010001",
        "orderType": 1,
        "orderTypeName": "会员充值",
        "productName": "月度会员",
        "originalAmount": 29.90,
        "discountAmount": 10.00,
        "finalAmount": 19.90,
        "orderStatus": 2,
        "orderStatusName": "已支付",
        "paymentMethod": 1,
        "paymentMethodName": "微信支付",
        "payTime": "2024-12-01 14:30:00",
        "createTime": "2024-12-01 14:25:00"
      }
    ],
    "total": 10,
    "current": 1,
    "size": 10,
    "pages": 1
  }
}
```

### 7.7 获取订单详情
**接口**: `GET /api/order/detail/{orderNo}`

### 7.8 取消订单
**接口**: `PUT /api/order/cancel/{orderNo}`

---

## 8. 文件管理模块

### 8.1 文件上传
**接口**: `POST /api/file/upload`

**请求参数**: multipart/form-data
- `file`: 文件
- `type`: 文件类型 (avatar:头像, resume:简历附件, template:模板)

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "fileId": "F001",
    "fileName": "avatar.jpg",
    "originalName": "我的头像.jpg",
    "fileUrl": "https://cdn.example.com/files/avatar.jpg",
    "fileSize": 102400,
    "fileType": "image/jpeg"
  }
}
```

### 8.2 文件下载
**接口**: `GET /api/file/download/{fileId}`

### 8.3 删除文件
**接口**: `DELETE /api/file/delete/{fileId}`

### 8.4 获取文件列表
**接口**: `GET /api/file/list`

**请求参数**:
- `current`: 当前页码
- `size`: 每页大小
- `fileType`: 文件类型

---

## 9. 系统管理模块

### 9.1 获取系统配置
**接口**: `GET /api/system/config`

**请求参数**:
- `group`: 配置分组 (可选)

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "siteInfo": {
      "siteName": "简历制作平台",
      "siteDescription": "专业的在线简历制作工具",
      "version": "1.0.0"
    },
    "features": {
      "maxResumeCount": 5,
      "maxExportPerMonth": 3,
      "supportedLanguages": ["zh-CN", "en", "ja", "ko"]
    }
  }
}
```

### 9.2 获取支持的语言
**接口**: `GET /api/system/languages`

**响应数据**:
```json
{
  "code": 200,
  "message": "success", 
  "data": [
    {
      "languageCode": "zh-CN",
      "languageName": "Chinese (Simplified)",
      "nativeName": "简体中文",
      "isActive": true
    },
    {
      "languageCode": "en",
      "languageName": "English", 
      "nativeName": "English",
      "isActive": true
    }
  ]
}
```

### 9.3 意见反馈
**接口**: `POST /api/system/feedback`

**请求参数**:
```json
{
  "type": 1,
  "title": "建议增加更多模板",
  "content": "希望能够增加更多行业的模板选择",
  "contact": "<EMAIL>",
  "attachments": ["F001", "F002"]
}
```

### 9.4 获取帮助文档
**接口**: `GET /api/system/help`

**请求参数**:
- `category`: 帮助分类
- `keyword`: 搜索关键词

---

## 10. 错误处理

### 10.1 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1001 | 用户不存在 | 用户ID不存在 |
| 1002 | 密码错误 | 登录密码不正确 |
| 1003 | 账号已被禁用 | 用户账号被管理员禁用 |
| 1004 | Token已过期 | 需要重新登录 |
| 1005 | 邮箱已被注册 | 注册邮箱已存在 |
| 2001 | 简历不存在 | 简历ID不存在 |
| 2002 | 简历创建失败 | 简历创建过程中出错 |
| 2003 | 简历数量超限 | 超过用户简历创建数量限制 |
| 2004 | 导出次数超限 | 超过用户月度导出次数限制 |
| 3001 | 模板不存在 | 模板ID不存在 |
| 3002 | 付费模板需要会员 | 需要购买会员才能使用付费模板 |
| 4001 | 订单不存在 | 订单号不存在 |
| 4002 | 支付失败 | 第三方支付接口返回失败 |
| 4003 | 订单已过期 | 超过订单支付时限 |

### 10.2 错误响应示例
```json
{
  "code": 1002,
  "message": "密码错误",
  "data": null,
  "timestamp": 1703001234567
}
```

---

## 11. 接口测试

### 11.1 测试环境
- **测试地址**: `https://test-api.resume.com`
- **测试账号**: <EMAIL>
- **测试密码**: test123456

### 11.2 Postman Collection
提供完整的Postman接口集合，包含所有接口的示例请求和响应。

### 11.3 接口性能要求
- **响应时间**: 95%的接口响应时间 < 500ms
- **并发量**: 支持1000+并发用户
- **可用性**: 99.9%服务可用性

---

这是一个完整的后端API接口文档，涵盖了简历应用的所有核心功能模块。每个接口都包含了详细的请求参数、响应格式和错误处理说明。您觉得这个接口文档如何？需要我补充或调整哪些部分吗？

---

_**User**_

执行操作：
1、给模板管理模块增加一个分享模板的接口；
2、将接口文档写入当前目录下的files目录下的“后端接口文档1_0.md”中；

---

_**Assistant**_



---

我来为模板管理模块增加分享模板的接口，然后将完整的接口文档写入文件。

---



---



---

让我直接创建一个新的文件：

---

```bash
echo "# 简历应用后端API接口文档 v1.0" > "files/后端接口文档1_0.md"
```

```
PS E:\workspace\resume> echo "# 简历应用后端API接口文档 v1.0" > "files/后端接口文档1_0.md"
PS E:\workspace\resume>
```

---

现在让我编辑这个文件，添加完整的接口文档内容：

---



---



---

