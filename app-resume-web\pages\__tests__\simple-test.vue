<template>
  <div class="simple-test">
    <h1>简单数据流测试</h1>
    
    <div class="test-section">
      <h2>输入测试</h2>
      <input v-model="testInput" @input="updateData" placeholder="输入姓名" />
      <p>当前输入: {{ testInput }}</p>
    </div>
    
    <div class="test-section">
      <h2>Store 状态</h2>
      <p><strong>EditorStore 是否存在:</strong> {{ editorStore ? '✅ 是' : '❌ 否' }}</p>
      <p><strong>currentResumeData 是否存在:</strong> {{ currentResumeData ? '✅ 是' : '❌ 否' }}</p>
      <p><strong>modules 是否存在:</strong> {{ currentResumeData?.modules ? '✅ 是' : '❌ 否' }}</p>
      <p><strong>modules 类型:</strong> {{ typeof currentResumeData?.modules }}</p>
      <p><strong>modules 内容:</strong> {{ JSON.stringify(currentResumeData?.modules) }}</p>
      <p><strong>basic_info 模块:</strong> {{ JSON.stringify(currentResumeData?.modules?.basic_info) }}</p>
      <p><strong>所有模块:</strong> {{ Object.keys(currentResumeData?.modules || {}) }}</p>
    </div>
    
    <div class="test-section">
      <h2>操作测试</h2>
      <button @click="testUpdate" class="test-btn">测试更新</button>
      <button @click="logStore" class="test-btn">打印Store状态</button>
      <p><strong>更新次数:</strong> {{ updateCount }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'

const editorStore = useEditorStore()
const testInput = ref('')
const updateCount = ref(0)

// 创建计算属性来正确访问响应式数据
const currentResumeData = computed(() => editorStore.currentResumeData)

const updateData = () => {
  console.log('🔄 更新数据:', testInput.value)
  
  const basicInfo = {
    name: testInput.value,
    title: '测试职位',
    phone: '13800138000',
    email: '<EMAIL>'
  }
  
  try {
    editorStore.updateModuleData('basic_info', basicInfo)
    updateCount.value++
    console.log('✅ 数据更新完成')
  } catch (error) {
    console.error('❌ 数据更新失败:', error)
  }
}

const testUpdate = () => {
  testInput.value = '测试用户' + Math.random().toString(36).substr(2, 3)
  updateData()
}

const logStore = () => {
  console.log('📊 当前Store状态:', {
    editorStore: editorStore,
    currentResumeData: editorStore?.currentResumeData,
    modules: editorStore?.currentResumeData?.modules,
    basicInfo: editorStore?.currentResumeData?.modules?.basic_info
  })
}

onMounted(async () => {
  console.log('🚀 初始化测试')
  console.log('📊 初始化前的状态:', {
    editorStore: editorStore,
    currentResumeData: editorStore?.currentResumeData,
    modules: editorStore?.currentResumeData?.modules
  })
  
  try {
    await editorStore.createNewResume()
    console.log('✅ 简历创建完成')
    console.log('📊 初始化后的状态:', {
      currentResumeData: editorStore?.currentResumeData,
      modules: editorStore?.currentResumeData?.modules
    })
    testInput.value = '张三'
  } catch (error) {
    console.error('❌ 初始化失败:', error)
    console.log('📊 初始化失败后的状态:', {
      currentResumeData: editorStore?.currentResumeData,
      modules: editorStore?.currentResumeData?.modules
    })
  }
})
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h2 {
  margin-bottom: 15px;
  color: #333;
}

.test-section input {
  width: 300px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 10px;
}

.test-btn {
  padding: 8px 16px;
  margin-right: 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:hover {
  background: #0056b3;
}
</style> 