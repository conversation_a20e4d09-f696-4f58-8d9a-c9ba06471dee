package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 会员权限检查响应DTO
 * 
 * 主要功能：
 * 1. 返回权限检查结果
 * 2. 提供使用量统计信息
 * 3. 包含升级建议和限制说明
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "会员权限检查响应", description = "权限检查结果和相关信息")
public class MembershipCheckResponse {

    /**
     * 是否允许操作
     */
    @Schema(description = "是否允许进行该操作", example = "true")
    private Boolean allowed;

    /**
     * 当前使用数量
     */
    @Schema(description = "当前已使用数量", example = "5")
    private Integer currentCount;

    /**
     * 最大允许数量
     * -1表示无限制
     */
    @Schema(description = "最大允许数量，-1表示无限制", example = "-1")
    private Integer maxCount;

    /**
     * 剩余可用数量
     * -1表示无限制
     */
    @Schema(description = "剩余可用数量，-1表示无限制", example = "-1")
    private Integer remaining;

    /**
     * 重置时间（仅导出权限检查）
     */
    @Schema(description = "使用量重置时间", example = "2024-07-01 00:00:00")
    private LocalDateTime resetTime;

    /**
     * 是否需要会员权限
     */
    @Schema(description = "该操作是否需要会员权限", example = "false")
    private Boolean membershipRequired;

    /**
     * 升级建议
     */
    @Schema(description = "升级建议信息")
    private UpgradeRecommendation upgradeRecommendation;

    /**
     * 模板信息（仅模板权限检查）
     */
    @Schema(description = "模板信息")
    private TemplateInfo templateInfo;

    /**
     * 当前会员信息（仅模板权限检查）
     */
    @Schema(description = "当前会员信息")
    private CurrentMembership currentMembership;

    /**
     * 升级建议内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "升级建议", description = "会员升级建议信息")
    public static class UpgradeRecommendation {

        /**
         * 建议套餐ID
         */
        @Schema(description = "建议升级的套餐ID", example = "2")
        private Long packageId;

        /**
         * 建议套餐名称
         */
        @Schema(description = "建议升级的套餐名称", example = "年度会员")
        private String packageName;

        /**
         * 升级理由
         */
        @Schema(description = "升级理由", example = "升级后可无限制创建简历")
        private String reason;

        /**
         * 优惠信息
         */
        @Schema(description = "优惠信息", example = "首次购买8折优惠")
        private String discount;
    }

    /**
     * 模板信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "模板信息", description = "模板基本信息")
    public static class TemplateInfo {

        /**
         * 模板ID
         */
        @Schema(description = "模板ID", example = "123")
        private Long id;

        /**
         * 模板名称
         */
        @Schema(description = "模板名称", example = "商务简约模板")
        private String name;

        /**
         * 是否为付费模板
         */
        @Schema(description = "是否为付费模板", example = "true")
        private Boolean isPremium;
    }

    /**
     * 当前会员信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "当前会员信息", description = "用户当前会员状态")
    public static class CurrentMembership {

        /**
         * 是否为会员
         */
        @Schema(description = "是否为会员", example = "true")
        private Boolean isMember;

        /**
         * 套餐名称
         */
        @Schema(description = "套餐名称", example = "年度会员")
        private String packageName;

        /**
         * 是否可用付费模板
         */
        @Schema(description = "是否可以使用付费模板", example = "true")
        private Boolean premiumTemplates;
    }

    /**
     * 创建简历权限检查响应
     * 
     * @param allowed 是否允许
     * @param currentCount 当前数量
     * @param maxCount 最大数量
     * @param membershipRequired 是否需要会员
     * @return 权限检查响应
     */
    public static MembershipCheckResponse createResumeCheckResponse(Boolean allowed, Integer currentCount, 
            Integer maxCount, Boolean membershipRequired) {
        Integer remaining = maxCount == -1 ? -1 : maxCount - currentCount;
        
        return MembershipCheckResponse.builder()
                .allowed(allowed)
                .currentCount(currentCount)
                .maxCount(maxCount)
                .remaining(remaining)
                .membershipRequired(membershipRequired)
                .upgradeRecommendation(membershipRequired && !allowed ? 
                    createDefaultUpgradeRecommendation() : null)
                .build();
    }

    /**
     * 创建导出权限检查响应
     * 
     * @param allowed 是否允许
     * @param currentCount 当前数量
     * @param maxCount 最大数量
     * @param resetTime 重置时间
     * @param membershipRequired 是否需要会员
     * @return 权限检查响应
     */
    public static MembershipCheckResponse createExportCheckResponse(Boolean allowed, Integer currentCount, 
            Integer maxCount, LocalDateTime resetTime, Boolean membershipRequired) {
        Integer remaining = maxCount == -1 ? -1 : maxCount - currentCount;
        
        return MembershipCheckResponse.builder()
                .allowed(allowed)
                .currentCount(currentCount)
                .maxCount(maxCount)
                .remaining(remaining)
                .resetTime(resetTime)
                .membershipRequired(membershipRequired)
                .upgradeRecommendation(membershipRequired && !allowed ? 
                    createDefaultUpgradeRecommendation() : null)
                .build();
    }

    /**
     * 创建模板权限检查响应
     * 
     * @param allowed 是否允许
     * @param templateInfo 模板信息
     * @param currentMembership 当前会员信息
     * @param membershipRequired 是否需要会员
     * @return 权限检查响应
     */
    public static MembershipCheckResponse createTemplateCheckResponse(Boolean allowed, TemplateInfo templateInfo, 
            CurrentMembership currentMembership, Boolean membershipRequired) {
        return MembershipCheckResponse.builder()
                .allowed(allowed)
                .templateInfo(templateInfo)
                .currentMembership(currentMembership)
                .membershipRequired(membershipRequired)
                .upgradeRecommendation(membershipRequired && !allowed ? 
                    createTemplateUpgradeRecommendation() : null)
                .build();
    }

    /**
     * 创建默认升级建议
     * 
     * @return 升级建议
     */
    private static UpgradeRecommendation createDefaultUpgradeRecommendation() {
        return UpgradeRecommendation.builder()
                .packageId(2L)
                .packageName("年度会员")
                .reason("升级后可享受更多权益")
                .discount("首次购买8折优惠")
                .build();
    }

    /**
     * 创建模板相关升级建议
     * 
     * @return 升级建议
     */
    private static UpgradeRecommendation createTemplateUpgradeRecommendation() {
        return UpgradeRecommendation.builder()
                .packageId(2L)
                .packageName("年度会员")
                .reason("升级后可使用所有付费模板")
                .discount("首次购买8折优惠")
                .build();
    }
} 