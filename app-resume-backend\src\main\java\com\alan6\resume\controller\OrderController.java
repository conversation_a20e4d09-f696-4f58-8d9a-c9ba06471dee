package com.alan6.resume.controller;

import com.alan6.resume.common.utils.BusinessLogUtils;
import com.alan6.resume.core.R;
import com.alan6.resume.dto.order.OrderDetailResponse;
import com.alan6.resume.dto.order.OrderListResponse;
import com.alan6.resume.service.IOrdersService;
import com.alan6.resume.security.UserPrincipal;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * <p>
 * 订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/order")
@Tag(name = "订单管理", description = "订单相关接口")
public class OrderController {

    @Autowired
    private IOrdersService ordersService;

    /**
     * 获取订单列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取订单列表", description = "分页获取用户的订单列表，支持多种筛选条件")
    public R<OrderListResponse> getOrderList(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "订单状态筛选") @RequestParam(required = false) Byte status,
            @Parameter(description = "支付方式筛选") @RequestParam(required = false) Byte paymentMethod,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "订单类型") @RequestParam(required = false) Byte orderType) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userPrincipal.getUsername(), "获取订单列表");
        
        try {
            Long userId = userPrincipal.getUserId();
            OrderListResponse response = ordersService.getUserOrderList(userId, page, size, status, 
                                                                       paymentMethod, startTime, endTime, orderType);
            
            BusinessLogUtils.logSuccess("获取订单列表", "userId", userId, "page", page, "size", size, 
                                      "total", response.getTotal());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderNo}")
    @Operation(summary = "获取订单详情", description = "根据订单号获取订单的详细信息")
    public R<OrderDetailResponse> getOrderDetail(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "订单号", required = true) @PathVariable String orderNo) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userPrincipal.getUsername(), "获取订单详情");
        
        try {
            Long userId = userPrincipal.getUserId();
            OrderDetailResponse response = ordersService.getOrderDetail(orderNo, userId);
            
            BusinessLogUtils.logSuccess("获取订单详情", "userId", userId, "orderNo", orderNo, 
                                      "orderStatus", response.getStatus());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 取消订单
     */
    @PutMapping("/cancel/{orderNo}")
    @Operation(summary = "取消订单", description = "取消指定的订单")
    public R<Boolean> cancelOrder(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "订单号", required = true) @PathVariable String orderNo,
            @Parameter(description = "取消原因", required = true) @RequestParam String cancelReason,
            @Parameter(description = "取消原因代码") @RequestParam(defaultValue = "USER_CANCEL") String cancelReasonCode) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userPrincipal.getUsername(), "取消订单");
        
        try {
            Long userId = userPrincipal.getUserId();
            Boolean result = ordersService.cancelOrder(orderNo, userId, cancelReason, cancelReasonCode);
            
            BusinessLogUtils.logSuccess("取消订单", "userId", userId, "orderNo", orderNo, 
                                      "cancelReason", cancelReason, "result", result);
            return R.ok(result);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }
}
