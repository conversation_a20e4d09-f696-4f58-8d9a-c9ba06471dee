<template>
  <div class="hidden md:flex md:w-2/5 bg-gradient-to-br from-primary-500 via-primary-600 to-orange-600 relative overflow-hidden">
    <!-- 装饰性几何图形 -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute top-10 left-10 w-32 h-32 border-2 border-white rounded-full"></div>
      <div class="absolute top-32 right-16 w-20 h-20 bg-white/20 rounded-lg rotate-45"></div>
      <div class="absolute bottom-20 left-20 w-16 h-16 bg-white/15 rounded-full"></div>
      <div class="absolute bottom-32 right-32 w-24 h-24 border border-white rounded-lg rotate-12"></div>
    </div>

    <!-- 主要内容 -->
    <div class="relative z-10 flex flex-col justify-center p-12 text-white">
      <!-- Logo和品牌名 -->
      <div class="mb-8">
        <div class="flex items-center mb-4">
          <!-- 火花Logo -->
          <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
            <svg class="w-7 h-7 text-white" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.19 0 2.34-.21 3.41-.6.3-.11.49-.4.49-.72 0-.43-.35-.78-.78-.78-.17 0-.33.06-.46.14-.82.29-1.69.44-2.58.44-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6c0 .89-.15 1.76-.44 2.58-.08.13-.14.29-.14.46 0 .43.35.78.78.78.32 0 .61-.19.72-.49.39-1.07.6-2.22.6-3.41C22 6.48 17.52 2 12 2z"/>
              <path d="M12 8l-2 6h4l-2-6z"/>
            </svg>
          </div>
          <div>
            <h1 class="text-2xl font-bold">火花简历</h1>
            <p class="text-white/80 text-sm">点燃职场新可能</p>
          </div>
        </div>
        <p class="text-lg text-white/90 mb-8">专业简历制作平台，助您脱颖而出</p>
      </div>

      <!-- 产品特色 -->
      <div class="space-y-4">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
          <span class="text-white/90">1000+ 专业简历模板</span>
        </div>
        <div class="flex items-center">
          <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
          <span class="text-white/90">AI 智能内容优化</span>
        </div>
        <div class="flex items-center">
          <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
          <span class="text-white/90">一键导出多种格式</span>
        </div>
        <div class="flex items-center">
          <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
          <span class="text-white/90">安全云端同步</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 这个组件是纯展示组件，不需要额外的逻辑
</script> 