import{_ as y}from"./DlAUqK2U.js";import{r as _,k as m,i as $,c as d,a as s,q as w,s as l,t as a,F as I,g as A,o as v}from"./CURHyiUL.js";const S={class:"auth-test-page"},C={class:"container"},z={class:"auth-status"},N={class:"status-grid"},B={class:"status-item"},D={class:"status-item"},L={class:"status-item"},P={class:"status-item"},x={key:0,class:"user-info"},J={class:"test-results"},O={class:"log-container"},V={class:"log-time"},E={class:"log-message"},F={__name:"auth-test",setup(U){const o=useAuthService(),h=useDownloadService(),r=_([]),u=_(null),f=m(()=>o.isLoggedIn.value),p=m(()=>o.isInitialized.value),c=m(()=>o.currentUser.value),n=(t,e="info")=>{const g=new Date().toLocaleTimeString();r.value.unshift({time:g,message:t,type:e}),r.value.length>50&&(r.value=r.value.slice(0,50))},k=async()=>{try{n("开始刷新Token...","info");const t=o.getToken();u.value=t,n(`Token获取结果: ${t?"成功":"失败"}`,t?"success":"error"),t&&n(`Token内容: ${t.substring(0,50)}...`,"info");const e=await o.validateToken();n(`Token验证结果: ${e?"有效":"无效"}`,e?"success":"error")}catch(t){n(`刷新Token失败: ${t.message}`,"error")}},T=async()=>{try{n("开始测试API调用...","info");const t=o.getToken();n(`使用Token: ${t?"Yes":"No"}`,t?"success":"warning");const e={"Content-Type":"application/json"};t&&(e.Authorization=`Bearer ${t}`,n(`添加Authorization头: Bearer ${t.substring(0,20)}...`,"info"));const i=await $fetch("/api/user/profile",{method:"GET",headers:e});n("API调用成功","success"),n(`响应数据: ${JSON.stringify(i)}`,"info")}catch(t){n(`API调用失败: ${t.message}`,"error"),n(`错误详情: ${JSON.stringify(t)}`,"error")}},b=async()=>{try{n("开始测试下载功能...","info");const t=o.getToken();if(n(`当前Token状态: ${t?"存在":"不存在"}`,t?"success":"error"),!t){n("没有Token，无法进行下载测试","error");return}await h.downloadResume({resumeId:"1001",format:"pdf",resumeData:{basicInfo:{name:"测试用户",title:"前端工程师"}},settings:{}}),n("下载测试完成","success")}catch(t){n(`下载测试失败: ${t.message}`,"error")}};return $(async()=>{n("页面加载完成","info"),o.isInitialized.value||(n("开始初始化认证状态...","info"),await o.initAuth(),n("认证状态初始化完成","success")),await k()}),(t,e)=>(v(),d("div",S,[s("div",C,[e[7]||(e[7]=s("h1",{class:"title"},"认证状态测试",-1)),s("div",z,[e[4]||(e[4]=s("h2",null,"当前认证状态",-1)),s("div",N,[s("div",B,[e[0]||(e[0]=s("label",null,"登录状态:",-1)),s("span",{class:l(f.value?"status-success":"status-error")},a(f.value?"已登录":"未登录"),3)]),s("div",D,[e[1]||(e[1]=s("label",null,"初始化状态:",-1)),s("span",{class:l(p.value?"status-success":"status-warning")},a(p.value?"已初始化":"未初始化"),3)]),s("div",L,[e[2]||(e[2]=s("label",null,"Token:",-1)),s("span",{class:l(u.value?"status-success":"status-error")},a(u.value?`${u.value.substring(0,20)}...`:"无Token"),3)]),s("div",P,[e[3]||(e[3]=s("label",null,"用户信息:",-1)),s("span",{class:l(c.value?"status-success":"status-error")},a(c.value?"已获取":"未获取"),3)])])]),c.value?(v(),d("div",x,[e[5]||(e[5]=s("h2",null,"用户信息",-1)),s("pre",null,a(JSON.stringify(c.value,null,2)),1)])):w("",!0),s("div",{class:"actions"},[s("button",{onClick:k,class:"btn btn-primary"}," 刷新Token "),s("button",{onClick:T,class:"btn btn-secondary"}," 测试API调用 "),s("button",{onClick:b,class:"btn btn-success"}," 测试下载功能 ")]),s("div",J,[e[6]||(e[6]=s("h2",null,"测试结果",-1)),s("div",O,[(v(!0),d(I,null,A(r.value,(i,g)=>(v(),d("div",{key:g,class:l(["log-item",`log-${i.type}`])},[s("span",V,a(i.time),1),s("span",E,a(i.message),1)],2))),128))])])])]))}},G=y(F,[["__scopeId","data-v-32b1005c"]]);export{G as default};
