package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.common.utils.ShareCodeUtils;
import com.alan6.resume.dto.share.ShareAccessRequest;
import com.alan6.resume.dto.share.ShareCreateRequest;
import com.alan6.resume.dto.share.ShareResponse;
import com.alan6.resume.entity.ShareRecords;
import com.alan6.resume.entity.Resumes;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.mapper.ShareRecordsMapper;
import com.alan6.resume.service.IShareRecordsService;
import com.alan6.resume.service.IResumeService;
import com.alan6.resume.service.IResumeTemplatesService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 分享记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShareRecordsServiceImpl extends ServiceImpl<ShareRecordsMapper, ShareRecords> implements IShareRecordsService {

    private final IResumeService resumeService;
    private final IResumeTemplatesService resumeTemplatesService;

    /**
     * 创建简历分享链接
     *
     * @param resumeId 简历ID
     * @param request  创建请求
     * @param userId   用户ID
     * @return 分享响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShareResponse createResumeShare(Long resumeId, ShareCreateRequest request, Long userId) {
        log.info("开始创建简历分享链接，简历ID: {}, 用户ID: {}", resumeId, userId);
        
        // 验证简历是否存在且属于当前用户
        Resumes resume = resumeService.getById(resumeId);
        if (resume == null) {
            throw new BusinessException("简历不存在");
        }
        if (!resume.getUserId().equals(userId)) {
            throw new BusinessException("无权限分享该简历");
        }
        
        // 创建分享记录
        ShareRecords shareRecord = new ShareRecords();
        shareRecord.setResourceType("resume");
        shareRecord.setResourceId(resumeId);
        shareRecord.setUserId(userId);
        shareRecord.setShareCode(ShareCodeUtils.generateResumeShareCode(resumeId));
        shareRecord.setShareType(request.getShareType());
        shareRecord.setExpireTime(request.getExpireTime());
        shareRecord.setPassword(request.getPassword());
        shareRecord.setAllowDownload(request.getAllowDownload() != null ? (byte)(request.getAllowDownload() ? 1 : 0) : (byte)1);
        shareRecord.setViewLimit(request.getViewLimit() != null ? request.getViewLimit() : -1);
        shareRecord.setShareScope(request.getShareScope() != null ? request.getShareScope() : (byte) 1);
        shareRecord.setAllowedDomains(request.getAllowedDomains() != null ? String.join(",", request.getAllowedDomains()) : null);
        shareRecord.setDescription(request.getDescription());
        shareRecord.setViewCount(0);
        shareRecord.setDownloadCount(0);
        shareRecord.setIsActive((byte) 1);
        shareRecord.setCreateTime(LocalDateTime.now());
        shareRecord.setUpdateTime(LocalDateTime.now());
        
        // 保存分享记录
        if (!save(shareRecord)) {
            throw new BusinessException("创建分享链接失败");
        }
        
        // 构建响应
        ShareResponse response = new ShareResponse();
        BeanUtils.copyProperties(shareRecord, response);
        response.setShareUrl(ShareCodeUtils.generateShareUrl("https://resume.com", shareRecord.getShareCode()));
        response.setQrCodeUrl(ShareCodeUtils.generateQrCodeUrl("https://cdn.example.com", shareRecord.getShareCode()));
        
        log.info("成功创建简历分享链接，分享码: {}", shareRecord.getShareCode());
        return response;
    }

    /**
     * 创建模板分享链接
     *
     * @param templateId 模板ID
     * @param request    创建请求
     * @param userId     用户ID
     * @return 分享响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShareResponse createTemplateShare(Long templateId, ShareCreateRequest request, Long userId) {
        log.info("开始创建模板分享链接，模板ID: {}, 用户ID: {}", templateId, userId);
        
        // 验证模板是否存在
        ResumeTemplates template = resumeTemplatesService.getById(templateId);
        if (template == null) {
            throw new BusinessException("模板不存在");
        }
        
        // 创建分享记录
        ShareRecords shareRecord = new ShareRecords();
        shareRecord.setResourceType("template");
        shareRecord.setResourceId(templateId);
        shareRecord.setUserId(userId);
        shareRecord.setShareCode(ShareCodeUtils.generateTemplateShareCode(templateId));
        shareRecord.setShareType(request.getShareType());
        shareRecord.setExpireTime(request.getExpireTime());
        shareRecord.setPassword(request.getPassword());
        shareRecord.setAllowDownload(request.getAllowDownload() != null ? (byte)(request.getAllowDownload() ? 1 : 0) : (byte)1);
        shareRecord.setViewLimit(request.getViewLimit() != null ? request.getViewLimit() : -1);
        shareRecord.setShareScope(request.getShareScope() != null ? request.getShareScope() : (byte) 1);
        shareRecord.setAllowedDomains(request.getAllowedDomains() != null ? String.join(",", request.getAllowedDomains()) : null);
        shareRecord.setDescription(request.getDescription());
        shareRecord.setViewCount(0);
        shareRecord.setDownloadCount(0);
        shareRecord.setIsActive((byte) 1);
        shareRecord.setCreateTime(LocalDateTime.now());
        shareRecord.setUpdateTime(LocalDateTime.now());
        
        // 保存分享记录
        if (!save(shareRecord)) {
            throw new BusinessException("创建分享链接失败");
        }
        
        // 构建响应
        ShareResponse response = new ShareResponse();
        BeanUtils.copyProperties(shareRecord, response);
        response.setShareUrl(ShareCodeUtils.generateShareUrl("https://resume.com", shareRecord.getShareCode()));
        response.setQrCodeUrl(ShareCodeUtils.generateQrCodeUrl("https://cdn.example.com", shareRecord.getShareCode()));
        
        log.info("成功创建模板分享链接，分享码: {}", shareRecord.getShareCode());
        return response;
    }

    /**
     * 访问分享内容
     *
     * @param shareCode 分享码
     * @param request   访问请求
     * @return 分享内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> accessShare(String shareCode, ShareAccessRequest request) {
        log.info("开始访问分享内容，分享码: {}", shareCode);
        
        // 查找分享记录
        QueryWrapper<ShareRecords> wrapper = new QueryWrapper<>();
        wrapper.eq("share_code", shareCode);
        wrapper.eq("is_active", 1);
        ShareRecords shareRecord = getOne(wrapper);
        
        if (shareRecord == null) {
            throw new BusinessException("分享链接不存在或已失效");
        }
        
        // 检查过期时间
        if (shareRecord.getExpireTime() != null && shareRecord.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("分享链接已过期");
        }
        
        // 检查访问次数限制
        if (shareRecord.getViewLimit() != null && shareRecord.getViewLimit() > 0 
            && shareRecord.getViewCount() >= shareRecord.getViewLimit()) {
            throw new BusinessException("分享链接访问次数已达上限");
        }
        
        // 验证密码
        if (shareRecord.getShareType() == 2 || shareRecord.getShareType() == 4) {
            if (request.getPassword() == null || !request.getPassword().equals(shareRecord.getPassword())) {
                throw new BusinessException("访问密码错误");
            }
        }
        
        // 更新访问次数
        shareRecord.setViewCount(shareRecord.getViewCount() + 1);
        shareRecord.setUpdateTime(LocalDateTime.now());
        updateById(shareRecord);
        
        // 构建响应数据
        Map<String, Object> result = new HashMap<>();
        result.put("resourceType", shareRecord.getResourceType());
        
        // 根据资源类型获取不同的资源数据
        if ("resume".equals(shareRecord.getResourceType())) {
            Resumes resume = resumeService.getById(shareRecord.getResourceId());
            if (resume != null) {
                Map<String, Object> resourceData = new HashMap<>();
                resourceData.put("id", resume.getId());
                resourceData.put("name", resume.getName());
                resourceData.put("templateId", resume.getTemplateId());
                resourceData.put("language", resume.getLanguage());
                resourceData.put("viewCount", shareRecord.getViewCount());
                result.put("resourceData", resourceData);
            }
        } else if ("template".equals(shareRecord.getResourceType())) {
            ResumeTemplates template = resumeTemplatesService.getById(shareRecord.getResourceId());
            if (template != null) {
                Map<String, Object> resourceData = new HashMap<>();
                resourceData.put("id", template.getId());
                resourceData.put("name", template.getName());
                resourceData.put("description", template.getDescription());
                resourceData.put("previewImageUrl", template.getPreviewImageUrl());
                resourceData.put("industry", template.getIndustry());
                resourceData.put("style", template.getStyle());
                resourceData.put("colorScheme", template.getColorScheme());
                resourceData.put("isPremium", template.getIsPremium());
                resourceData.put("price", template.getPrice());
                result.put("resourceData", resourceData);
            }
        }
        
        // 分享信息
        Map<String, Object> shareInfo = new HashMap<>();
        shareInfo.put("shareCode", shareRecord.getShareCode());
        shareInfo.put("allowDownload", shareRecord.getAllowDownload() == 1);
        shareInfo.put("expireTime", shareRecord.getExpireTime());
        shareInfo.put("remainingViews", shareRecord.getViewLimit() > 0 ? 
            shareRecord.getViewLimit() - shareRecord.getViewCount() : -1);
        shareInfo.put("viewCount", shareRecord.getViewCount());
        shareInfo.put("description", shareRecord.getDescription());
        result.put("shareInfo", shareInfo);
        
        log.info("成功访问分享内容，分享码: {}, 访问次数: {}", shareCode, shareRecord.getViewCount());
        return result;
    }

    /**
     * 获取分享记录列表
     *
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     * @param current      当前页
     * @param size         每页大小
     * @param userId       用户ID
     * @return 分享记录分页
     */
    @Override
    public Page<ShareRecords> getShareRecords(String resourceType, Long resourceId, Integer current, Integer size, Long userId) {
        log.info("开始获取分享记录列表，用户ID: {}, 资源类型: {}, 资源ID: {}", userId, resourceType, resourceId);
        
        QueryWrapper<ShareRecords> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        
        if (resourceType != null && !resourceType.isEmpty()) {
            wrapper.eq("resource_type", resourceType);
        }
        
        if (resourceId != null) {
            wrapper.eq("resource_id", resourceId);
        }
        
        wrapper.orderByDesc("create_time");
        
        Page<ShareRecords> page = new Page<>(current, size);
        Page<ShareRecords> result = page(page, wrapper);
        
        log.info("成功获取分享记录列表，用户ID: {}, 记录数: {}", userId, result.getRecords().size());
        return result;
    }

    /**
     * 更新分享配置
     *
     * @param shareId 分享ID
     * @param request 更新请求
     * @param userId  用户ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateShareConfig(Long shareId, ShareCreateRequest request, Long userId) {
        log.info("开始更新分享配置，分享ID: {}, 用户ID: {}", shareId, userId);
        
        ShareRecords shareRecord = getById(shareId);
        if (shareRecord == null) {
            throw new BusinessException("分享记录不存在");
        }
        
        if (!shareRecord.getUserId().equals(userId)) {
            throw new BusinessException("无权限修改该分享");
        }
        
        // 更新配置
        if (request.getShareType() != null) {
            shareRecord.setShareType(request.getShareType());
        }
        if (request.getExpireTime() != null) {
            shareRecord.setExpireTime(request.getExpireTime());
        }
        if (request.getPassword() != null) {
            shareRecord.setPassword(request.getPassword());
        }
        if (request.getAllowDownload() != null) {
            shareRecord.setAllowDownload((byte)(request.getAllowDownload() ? 1 : 0));
        }
        if (request.getViewLimit() != null) {
            shareRecord.setViewLimit(request.getViewLimit());
        }
        if (request.getShareScope() != null) {
            shareRecord.setShareScope(request.getShareScope());
        }
        if (request.getAllowedDomains() != null) {
            shareRecord.setAllowedDomains(String.join(",", request.getAllowedDomains()));
        }
        if (request.getDescription() != null) {
            shareRecord.setDescription(request.getDescription());
        }
        
        shareRecord.setUpdateTime(LocalDateTime.now());
        
        boolean success = updateById(shareRecord);
        log.info("更新分享配置结果，分享ID: {}, 成功: {}", shareId, success);
        return success;
    }

    /**
     * 删除分享链接
     *
     * @param shareId 分享ID
     * @param userId  用户ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteShare(Long shareId, Long userId) {
        log.info("开始删除分享链接，分享ID: {}, 用户ID: {}", shareId, userId);
        
        ShareRecords shareRecord = getById(shareId);
        if (shareRecord == null) {
            throw new BusinessException("分享记录不存在");
        }
        
        if (!shareRecord.getUserId().equals(userId)) {
            throw new BusinessException("无权限删除该分享");
        }
        
        boolean success = removeById(shareId);
        log.info("删除分享链接结果，分享ID: {}, 成功: {}", shareId, success);
        return success;
    }

    /**
     * 批量删除分享链接
     *
     * @param shareIds 分享ID列表
     * @param userId   用户ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchDeleteShares(List<Long> shareIds, Long userId) {
        log.info("开始批量删除分享链接，分享ID列表: {}, 用户ID: {}", shareIds, userId);
        
        if (shareIds == null || shareIds.isEmpty()) {
            throw new BusinessException("分享ID列表不能为空");
        }
        
        // 验证权限
        QueryWrapper<ShareRecords> wrapper = new QueryWrapper<>();
        wrapper.in("id", shareIds);
        wrapper.eq("user_id", userId);
        
        List<ShareRecords> shareRecords = list(wrapper);
        if (shareRecords.size() != shareIds.size()) {
            throw new BusinessException("部分分享记录不存在或无权限删除");
        }
        
        // 执行删除
        boolean success = removeByIds(shareIds);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("deletedCount", success ? shareIds.size() : 0);
        
        log.info("批量删除分享链接结果，成功: {}, 删除数量: {}", success, shareIds.size());
        return result;
    }

    /**
     * 获取分享统计信息
     *
     * @param resourceType 资源类型
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param granularity  时间粒度
     * @param userId       用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getShareStatistics(String resourceType, LocalDateTime startDate, 
                                                  LocalDateTime endDate, String granularity, Long userId) {
        log.info("开始获取分享统计信息，用户ID: {}, 资源类型: {}", userId, resourceType);
        
        QueryWrapper<ShareRecords> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        
        if (resourceType != null && !resourceType.isEmpty()) {
            wrapper.eq("resource_type", resourceType);
        }
        
        if (startDate != null) {
            wrapper.ge("create_time", startDate);
        }
        
        if (endDate != null) {
            wrapper.le("create_time", endDate);
        }
        
        List<ShareRecords> records = list(wrapper);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalShares", records.size());
        statistics.put("totalViews", records.stream().mapToInt(ShareRecords::getViewCount).sum());
        statistics.put("totalDownloads", records.stream().mapToInt(ShareRecords::getDownloadCount).sum());
        statistics.put("activeShares", records.stream().filter(r -> r.getIsActive() == 1).count());
        
        // 按类型统计
        Map<String, Long> typeStats = records.stream()
            .collect(Collectors.groupingBy(ShareRecords::getResourceType, Collectors.counting()));
        statistics.put("typeStatistics", typeStats);
        
        // 按日期统计（简化版本）
        Map<String, Long> dateStats = records.stream()
            .collect(Collectors.groupingBy(
                r -> r.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                Collectors.counting()
            ));
        statistics.put("dateStatistics", dateStats);
        
        log.info("成功获取分享统计信息，用户ID: {}, 总分享数: {}", userId, records.size());
        return statistics;
    }

    /**
     * 获取分享详情
     *
     * @param shareId 分享ID
     * @param userId  用户ID
     * @return 分享详情
     */
    @Override
    public ShareRecords getShareDetail(Long shareId, Long userId) {
        log.info("开始获取分享详情，分享ID: {}, 用户ID: {}", shareId, userId);
        
        ShareRecords shareRecord = getById(shareId);
        if (shareRecord == null) {
            throw new BusinessException("分享记录不存在");
        }
        
        if (!shareRecord.getUserId().equals(userId)) {
            throw new BusinessException("无权限查看该分享");
        }
        
        log.info("成功获取分享详情，分享ID: {}", shareId);
        return shareRecord;
    }

    /**
     * 切换分享状态
     *
     * @param shareId  分享ID
     * @param isActive 是否激活
     * @param userId   用户ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean toggleShareStatus(Long shareId, Boolean isActive, Long userId) {
        log.info("开始切换分享状态，分享ID: {}, 状态: {}, 用户ID: {}", shareId, isActive, userId);
        
        ShareRecords shareRecord = getById(shareId);
        if (shareRecord == null) {
            throw new BusinessException("分享记录不存在");
        }
        
        if (!shareRecord.getUserId().equals(userId)) {
            throw new BusinessException("无权限修改该分享");
        }
        
        shareRecord.setIsActive(isActive ? (byte) 1 : (byte) 0);
        shareRecord.setUpdateTime(LocalDateTime.now());
        
        boolean success = updateById(shareRecord);
        log.info("切换分享状态结果，分享ID: {}, 成功: {}", shareId, success);
        return success;
    }

    /**
     * 预览分享内容（不计入访问次数）
     *
     * @param shareCode 分享码
     * @param password  访问密码（可选）
     * @return 预览信息
     */
    @Override
    public Map<String, Object> previewShare(String shareCode, String password) {
        log.info("开始预览分享内容，分享码: {}", shareCode);
        
        // 查找分享记录
        QueryWrapper<ShareRecords> wrapper = new QueryWrapper<>();
        wrapper.eq("share_code", shareCode);
        wrapper.eq("is_active", 1);
        ShareRecords shareRecord = getOne(wrapper);
        
        Map<String, Object> result = new HashMap<>();
        
        if (shareRecord == null) {
            result.put("isValid", false);
            result.put("message", "分享链接不存在或已失效");
            log.warn("预览失败，分享码不存在: {}", shareCode);
            return result;
        }
        
        // 检查过期时间
        boolean isExpired = shareRecord.getExpireTime() != null && 
                           shareRecord.getExpireTime().isBefore(LocalDateTime.now());
        
        // 检查访问次数限制
        boolean isOverLimit = shareRecord.getViewLimit() != null && 
                             shareRecord.getViewLimit() > 0 && 
                             shareRecord.getViewCount() >= shareRecord.getViewLimit();
        
        // 检查是否需要密码
        boolean requirePassword = shareRecord.getShareType() == 2 || shareRecord.getShareType() == 4;
        boolean passwordCorrect = !requirePassword || 
                                 (password != null && password.equals(shareRecord.getPassword()));
        
        // 构建预览响应
        result.put("isValid", !isExpired && !isOverLimit);
        result.put("requirePassword", requirePassword);
        result.put("passwordCorrect", passwordCorrect);
        result.put("resourceType", shareRecord.getResourceType());
        
        // 获取资源名称
        String resourceName = getResourceName(shareRecord.getResourceType(), shareRecord.getResourceId());
        result.put("resourceName", resourceName);
        
        // 分享信息
        Map<String, Object> shareInfo = new HashMap<>();
        shareInfo.put("shareCode", shareRecord.getShareCode());
        shareInfo.put("shareType", shareRecord.getShareType());
        shareInfo.put("hasPassword", requirePassword);
        shareInfo.put("expireTime", shareRecord.getExpireTime());
        shareInfo.put("allowDownload", shareRecord.getAllowDownload() == 1);
        shareInfo.put("description", shareRecord.getDescription());
        shareInfo.put("viewCount", shareRecord.getViewCount());
        shareInfo.put("remainingViews", shareRecord.getViewLimit() > 0 ? 
            shareRecord.getViewLimit() - shareRecord.getViewCount() : -1);
        shareInfo.put("isExpired", isExpired);
        shareInfo.put("isOverLimit", isOverLimit);
        result.put("shareInfo", shareInfo);
        
        log.info("成功预览分享内容，分享码: {}, 是否有效: {}", shareCode, result.get("isValid"));
        return result;
    }

    /**
     * 验证分享访问权限
     *
     * @param shareCode 分享码
     * @param password  访问密码（可选）
     * @return 验证结果
     */
    @Override
    public Map<String, Object> verifyShareAccess(String shareCode, String password) {
        log.info("开始验证分享访问权限，分享码: {}", shareCode);
        
        // 查找分享记录
        QueryWrapper<ShareRecords> wrapper = new QueryWrapper<>();
        wrapper.eq("share_code", shareCode);
        wrapper.eq("is_active", 1);
        ShareRecords shareRecord = getOne(wrapper);
        
        Map<String, Object> result = new HashMap<>();
        
        if (shareRecord == null) {
            result.put("verified", false);
            result.put("message", "分享链接不存在或已失效");
            log.warn("验证失败，分享码不存在: {}", shareCode);
            return result;
        }
        
        // 检查过期时间
        if (shareRecord.getExpireTime() != null && shareRecord.getExpireTime().isBefore(LocalDateTime.now())) {
            result.put("verified", false);
            result.put("message", "分享链接已过期");
            log.warn("验证失败，分享已过期: {}", shareCode);
            return result;
        }
        
        // 检查访问次数限制
        if (shareRecord.getViewLimit() != null && shareRecord.getViewLimit() > 0 
            && shareRecord.getViewCount() >= shareRecord.getViewLimit()) {
            result.put("verified", false);
            result.put("message", "分享链接访问次数已达上限");
            log.warn("验证失败，访问次数已达上限: {}", shareCode);
            return result;
        }
        
        // 验证密码
        if (shareRecord.getShareType() == 2 || shareRecord.getShareType() == 4) {
            if (password == null || !password.equals(shareRecord.getPassword())) {
                result.put("verified", false);
                result.put("message", "访问密码错误");
                log.warn("验证失败，密码错误: {}", shareCode);
                return result;
            }
        }
        
        // 验证成功
        result.put("verified", true);
        result.put("message", "验证成功");
        result.put("shareId", shareRecord.getId());
        result.put("resourceType", shareRecord.getResourceType());
        result.put("resourceId", shareRecord.getResourceId());
        result.put("allowDownload", shareRecord.getAllowDownload() == 1);
        
        log.info("成功验证分享访问权限，分享码: {}", shareCode);
        return result;
    }

    /**
     * 获取分享访问日志
     *
     * @param shareId   分享ID
     * @param current   当前页
     * @param size      每页大小
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param userId    用户ID
     * @return 访问日志分页
     */
    @Override
    public Page<Map<String, Object>> getShareAccessLogs(Long shareId, Integer current, Integer size, 
                                                        LocalDateTime startDate, LocalDateTime endDate, Long userId) {
        log.info("开始获取分享访问日志，分享ID: {}, 用户ID: {}", shareId, userId);
        
        // 验证分享记录是否存在且属于当前用户
        ShareRecords shareRecord = getById(shareId);
        if (shareRecord == null) {
            throw new BusinessException("分享记录不存在");
        }
        
        if (!shareRecord.getUserId().equals(userId)) {
            throw new BusinessException("无权限查看该分享的访问日志");
        }
        
        // 由于没有专门的访问日志表，这里模拟返回访问日志数据
        // 在实际项目中，应该有专门的 share_access_logs 表来记录访问日志
        Page<Map<String, Object>> page = new Page<>(current, size);
        
        // 模拟访问日志数据
        List<Map<String, Object>> records = generateMockAccessLogs(shareId, shareRecord, startDate, endDate);
        
        // 模拟分页
        int start = (current - 1) * size;
        int end = Math.min(start + size, records.size());
        List<Map<String, Object>> pageRecords = records.subList(start, end);
        
        page.setRecords(pageRecords);
        page.setTotal(records.size());
        page.setCurrent(current);
        page.setSize(size);
        page.setPages((records.size() + size - 1) / size);
        
        log.info("成功获取分享访问日志，分享ID: {}, 日志数量: {}", shareId, pageRecords.size());
        return page;
    }

    /**
     * 获取资源名称
     *
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     * @return 资源名称
     */
    private String getResourceName(String resourceType, Long resourceId) {
        if ("resume".equals(resourceType)) {
            Resumes resume = resumeService.getById(resourceId);
            return resume != null ? resume.getName() : "未知简历";
        } else if ("template".equals(resourceType)) {
            ResumeTemplates template = resumeTemplatesService.getById(resourceId);
            return template != null ? template.getName() : "未知模板";
        }
        return "未知资源";
    }

    /**
     * 生成模拟访问日志数据
     * 注意：在实际项目中应该有专门的访问日志表
     *
     * @param shareId    分享ID
     * @param shareRecord 分享记录
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 模拟访问日志列表
     */
    private List<Map<String, Object>> generateMockAccessLogs(Long shareId, ShareRecords shareRecord, 
                                                             LocalDateTime startDate, LocalDateTime endDate) {
        List<Map<String, Object>> logs = new ArrayList<>();
        
        // 基于访问次数生成模拟日志
        int viewCount = shareRecord.getViewCount();
        LocalDateTime baseTime = shareRecord.getCreateTime();
        
        for (int i = 0; i < Math.min(viewCount, 50); i++) { // 最多返回50条模拟数据
            Map<String, Object> log = new HashMap<>();
            log.put("id", 10000L + i);
            log.put("shareId", shareId);
            log.put("accessTime", baseTime.plusHours(i).plusMinutes(i * 15));
            log.put("clientIp", "192.168.1." + (100 + i % 155)); // 模拟不同IP
            log.put("userAgent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            log.put("referer", i % 3 == 0 ? "https://example.com" : "");
            log.put("country", i % 5 == 0 ? "美国" : "中国");
            log.put("region", i % 4 == 0 ? "加利福尼亚州" : "广东省");
            log.put("city", i % 4 == 0 ? "旧金山" : "深圳市");
            log.put("accessType", "view");
            log.put("isSuccess", true);
            
            // 应用日期过滤
            LocalDateTime accessTime = (LocalDateTime) log.get("accessTime");
            if (startDate != null && accessTime.isBefore(startDate)) {
                continue;
            }
            if (endDate != null && accessTime.isAfter(endDate)) {
                continue;
            }
            
            logs.add(log);
        }
        
        // 按时间倒序排列
        logs.sort((a, b) -> ((LocalDateTime) b.get("accessTime")).compareTo((LocalDateTime) a.get("accessTime")));
        
        return logs;
    }

    /**
     * 批量操作分享状态
     *
     * @param shareIds  分享ID列表
     * @param isActive  目标状态
     * @param userId    用户ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchToggleShareStatus(List<Long> shareIds, Boolean isActive, Long userId) {
        log.info("开始批量操作分享状态，分享ID列表: {}, 目标状态: {}, 用户ID: {}", shareIds, isActive, userId);
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;
        List<Map<String, Object>> failedItems = new ArrayList<>();
        
        for (Long shareId : shareIds) {
            try {
                // 验证分享记录是否存在且属于当前用户
                ShareRecords shareRecord = getById(shareId);
                if (shareRecord == null) {
                    failedCount++;
                    failedItems.add(Map.of(
                        "shareId", shareId,
                        "reason", "分享记录不存在"
                    ));
                    continue;
                }
                
                if (!shareRecord.getUserId().equals(userId)) {
                    failedCount++;
                    failedItems.add(Map.of(
                        "shareId", shareId,
                        "reason", "无权限操作该分享"
                    ));
                    continue;
                }
                
                // 更新状态
                shareRecord.setIsActive(isActive ? (byte) 1 : (byte) 0);
                shareRecord.setUpdateTime(LocalDateTime.now());
                
                boolean success = updateById(shareRecord);
                if (success) {
                    successCount++;
                    log.debug("成功更新分享状态，分享ID: {}, 状态: {}", shareId, isActive);
                } else {
                    failedCount++;
                    failedItems.add(Map.of(
                        "shareId", shareId,
                        "reason", "数据库更新失败"
                    ));
                }
                
            } catch (Exception e) {
                failedCount++;
                failedItems.add(Map.of(
                    "shareId", shareId,
                    "reason", "操作异常: " + e.getMessage()
                ));
                log.error("批量操作分享状态失败，分享ID: {}", shareId, e);
            }
        }
        
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("failedItems", failedItems);
        
        log.info("批量操作分享状态完成，成功: {}, 失败: {}", successCount, failedCount);
        return result;
    }

    /**
     * 批量设置过期时间
     *
     * @param shareIds   分享ID列表
     * @param expireTime 过期时间
     * @param userId     用户ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchSetExpireTime(List<Long> shareIds, LocalDateTime expireTime, Long userId) {
        log.info("开始批量设置过期时间，分享ID列表: {}, 过期时间: {}, 用户ID: {}", shareIds, expireTime, userId);
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;
        List<Map<String, Object>> failedItems = new ArrayList<>();
        
        for (Long shareId : shareIds) {
            try {
                // 验证分享记录是否存在且属于当前用户
                ShareRecords shareRecord = getById(shareId);
                if (shareRecord == null) {
                    failedCount++;
                    failedItems.add(Map.of(
                        "shareId", shareId,
                        "reason", "分享记录不存在"
                    ));
                    continue;
                }
                
                if (!shareRecord.getUserId().equals(userId)) {
                    failedCount++;
                    failedItems.add(Map.of(
                        "shareId", shareId,
                        "reason", "无权限操作该分享"
                    ));
                    continue;
                }
                
                // 验证过期时间是否合理
                if (expireTime.isBefore(LocalDateTime.now())) {
                    failedCount++;
                    failedItems.add(Map.of(
                        "shareId", shareId,
                        "reason", "过期时间不能早于当前时间"
                    ));
                    continue;
                }
                
                // 更新过期时间
                shareRecord.setExpireTime(expireTime);
                shareRecord.setUpdateTime(LocalDateTime.now());
                
                boolean success = updateById(shareRecord);
                if (success) {
                    successCount++;
                    log.debug("成功更新过期时间，分享ID: {}, 过期时间: {}", shareId, expireTime);
                } else {
                    failedCount++;
                    failedItems.add(Map.of(
                        "shareId", shareId,
                        "reason", "数据库更新失败"
                    ));
                }
                
            } catch (Exception e) {
                failedCount++;
                failedItems.add(Map.of(
                    "shareId", shareId,
                    "reason", "操作异常: " + e.getMessage()
                ));
                log.error("批量设置过期时间失败，分享ID: {}", shareId, e);
            }
        }
        
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("failedItems", failedItems);
        
        log.info("批量设置过期时间完成，成功: {}, 失败: {}", successCount, failedCount);
        return result;
    }

    /**
     * 获取热门分享
     *
     * @param resourceType 资源类型
     * @param period       统计周期
     * @param limit        返回数量限制
     * @param userId       用户ID
     * @return 热门分享列表
     */
    @Override
    public Map<String, Object> getTrendingShares(String resourceType, String period, Integer limit, Long userId) {
        log.info("开始获取热门分享，资源类型: {}, 统计周期: {}, 限制数量: {}, 用户ID: {}", resourceType, period, limit, userId);
        
        // 参数校验和默认值设置
        if (period == null) {
            period = "30d";
        }
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (limit > 50) {
            limit = 50; // 最大限制50条
        }
        
        // 计算统计开始时间
        LocalDateTime startTime = calculatePeriodStartTime(period);
        LocalDateTime endTime = LocalDateTime.now();
        
        // 构建查询条件
        QueryWrapper<ShareRecords> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("is_active", 1);
        wrapper.ge("create_time", startTime);
        wrapper.le("create_time", endTime);
        
        if (resourceType != null && !resourceType.isEmpty()) {
            wrapper.eq("resource_type", resourceType);
        }
        
        // 按访问次数排序
        wrapper.orderByDesc("view_count");
        wrapper.last("LIMIT " + limit);
        
        List<ShareRecords> shareRecords = list(wrapper);
        
        // 构建热门分享列表
        List<Map<String, Object>> trending = new ArrayList<>();
        for (ShareRecords record : shareRecords) {
            Map<String, Object> item = new HashMap<>();
            item.put("shareId", record.getId());
            item.put("resourceType", record.getResourceType());
            item.put("resourceId", record.getResourceId());
            item.put("shareCode", record.getShareCode());
            item.put("viewCount", record.getViewCount());
            item.put("downloadCount", record.getDownloadCount());
            item.put("shareTime", record.getCreateTime());
            
            // 获取资源名称
            String resourceName = getResourceName(record.getResourceType(), record.getResourceId());
            item.put("resourceName", resourceName);
            
            // 计算增长率（模拟数据，实际应该基于历史数据计算）
            String growth = calculateGrowthRate(record.getViewCount(), period);
            item.put("growth", growth);
            
            trending.add(item);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("period", period);
        result.put("updateTime", endTime);
        result.put("trending", trending);
        
        log.info("成功获取热门分享，返回数量: {}", trending.size());
        return result;
    }

    /**
     * 计算统计周期开始时间
     *
     * @param period 统计周期
     * @return 开始时间
     */
    private LocalDateTime calculatePeriodStartTime(String period) {
        LocalDateTime now = LocalDateTime.now();
        switch (period) {
            case "7d":
                return now.minusDays(7);
            case "30d":
                return now.minusDays(30);
            case "90d":
                return now.minusDays(90);
            default:
                return now.minusDays(30); // 默认30天
        }
    }

    /**
     * 计算增长率（模拟实现）
     * 注意：在实际项目中应该基于历史数据进行计算
     *
     * @param currentViews 当前访问次数
     * @param period       统计周期
     * @return 增长率字符串
     */
    private String calculateGrowthRate(Integer currentViews, String period) {
        if (currentViews == null || currentViews == 0) {
            return "0%";
        }
        
        // 模拟增长率计算（实际应该基于历史数据）
        double baseRate = 0.1; // 基础增长率
        double randomFactor = Math.random() * 0.3; // 随机因子
        double growthRate = (baseRate + randomFactor) * 100;
        
        // 根据访问次数调整增长率
        if (currentViews > 1000) {
            growthRate *= 1.5;
        } else if (currentViews > 500) {
            growthRate *= 1.2;
        }
        
        return String.format("+%.1f%%", growthRate);
    }
}
