<template>
  <div class="pt-16 min-h-screen bg-gray-50">
    <!-- 顶部横条幅 -->
    <HeroBanner
      @nav-click="handleNavClick"
    />
    
    <!-- 搜索筛选组件 -->
    <SearchFilter
      @main-category-change="handleMainCategoryChange"
      @sub-category-change="handleSubCategoryChange"
      @search="handleSearch"
    />
    
    <!-- 模板网格 -->
    <TemplateGrid
      :templates="templateService.templates.value"
      :loading="templateService.loading.value"
      :page-size="pageSize"
      @preview="handlePreview"
      @use-template="handleUseTemplate"
      @page-change="handlePageChange"
    />

    <!-- 加载状态 -->
    <div v-if="templateService.loading.value" class="flex justify-center items-center py-20">
      <div class="flex flex-col items-center">
        <div class="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mb-4"></div>
        <p class="text-secondary-600">正在加载模板...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="templateService.error.value" class="flex justify-center items-center py-20">
      <div class="text-center">
        <div class="w-16 h-16 bg-danger-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-danger-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-900 mb-2">加载失败</h3>
        <p class="text-secondary-600 mb-4">{{ templateService.error.value }}</p>
        <button 
          @click="retryLoadTemplates"
          class="btn-primary"
        >
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTemplateService } from '~/composables/template/useTemplateService'
import HeroBanner from '~/components/template/HeroBanner.vue'
import SearchFilter from '~/components/template/SearchFilter.vue'
import TemplateGrid from '~/components/template/TemplateGrid.vue'

// ================================
// 页面配置
// ================================

// SEO Meta
useSeoMeta({
  title: '简历模板库 - 1000+ 专业简历模板 - 火花简历',
  description: '提供1000+专业简历模板，涵盖各行各业。商务、创意、技术、文艺等多种风格，5分钟快速制作专业简历。',
  keywords: '简历模板,免费简历模板,简历设计,职场简历,求职简历模板,火花简历,在线制作简历'
})

// ================================
// 响应式数据
// ================================
const router = useRouter()
const templateService = useTemplateService()
const pageSize = ref(20)

// ================================
// 生命周期
// ================================
onMounted(async () => {
  await initializePage()
})

// ================================
// 初始化方法
// ================================
const initializePage = async () => {
  try {
    await templateService.fetchTemplates()
  } catch (error) {
    console.error('初始化页面失败:', error)
  }
}

// ================================
// 事件处理方法
// ================================

/**
 * 处理导航卡片点击
 * @param {Object} nav - 导航对象
 */
const handleNavClick = (nav) => {
  // TODO: 根据导航类型处理不同逻辑
  console.log('导航点击:', nav)
  
  switch (nav.id) {
    case 'import':
      // 导入简历
      router.push('/resume/import')
      break
    case 'optimize':
      // 简历优化
      router.push('/resume/optimize')
      break
    case 'ai-resume':
      // AI简历
      router.push('/resume/ai-create')
      break
    case 'score':
      // 简历打分
      router.push('/resume/score')
      break
    case 'track':
      // 简历追踪
      router.push('/resume/track')
      break
    default:
      console.log('未知导航:', nav)
  }
}

/**
 * 处理主分类变更
 * @param {Object} category - 主分类对象
 */
const handleMainCategoryChange = (category) => {
  if (category && category.id) {
    templateService.setCategoryFilter(category.id)
  }
}

/**
 * 处理副分类变更
 * @param {Object} filterData - 包含主分类和副分类的对象
 */
const handleSubCategoryChange = (filterData) => {
  if (filterData && filterData.subCategory) {
    if (filterData.subCategory.industry) {
      templateService.setIndustryFilter(filterData.subCategory.industry)
    } else if (filterData.subCategory.style) {
      templateService.setStyleFilter(filterData.subCategory.style)
    }
  }
}

/**
 * 处理搜索
 * @param {string} keyword - 搜索关键词
 */
const handleSearch = (keyword) => {
  templateService.setKeywordFilter(keyword)
}

/**
 * 处理模板预览
 * @param {Object} template - 模板对象
 */
const handlePreview = (template) => {
  // TODO: 实现模板预览逻辑
  console.log('预览模板:', template)
  
  // 可以打开模态框或跳转到预览页面
  // router.push(`/templates/${template.id}/preview`)
}

/**
 * 处理使用模板
 * @param {Object} template - 模板对象
 */
const handleUseTemplate = async (template) => {
  try {
    // 构建使用模板的请求参数
    const useRequest = {
      resumeName: `基于${template.name}的简历`,
      useDefaultContent: true
    }
    
    // 使用模板创建简历
    const result = await templateService.useTemplate(template.id, useRequest)
    
    if (result.success) {
      // 跳转到简历编辑页
      router.push(`/resume/edit/${result.data.resumeId}`)
    } else {
      throw new Error(result.error || '使用模板失败')
    }
  } catch (error) {
    console.error('使用模板失败:', error)
    // TODO: 显示错误提示，比如用户未登录等
    if (error.message.includes('登录')) {
      // 跳转到登录页
      router.push('/login')
    }
  }
}

/**
 * 处理分页变更
 * @param {number} page - 页码
 */
const handlePageChange = (page) => {
  // 分页逻辑已在 TemplateGrid 组件中处理
  console.log('分页变更:', page)
}

/**
 * 重试加载模板
 */
const retryLoadTemplates = async () => {
  await templateService.fetchTemplates()
}
</script>

<style scoped>
/* 页面特定样式 */
.template-page {
  min-height: calc(100vh - 4rem);
}

/* 加载动画优化 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .template-page {
    padding-top: 1rem;
  }
}
</style> 