package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 模板列表响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板列表响应DTO")
public class TemplateListResponse {

    /**
     * 模板列表
     */
    @Schema(description = "模板列表")
    private List<TemplateResponse> records;

    /**
     * 总数
     */
    @Schema(description = "总数")
    private Long total;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码")
    private Integer current;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小")
    private Integer size;

    /**
     * 总页数
     */
    @Schema(description = "总页数")
    private Integer pages;

    /**
     * 当前筛选条件
     */
    @Schema(description = "当前筛选条件")
    private Map<String, Object> filters;

    /**
     * 可用筛选选项
     */
    @Schema(description = "可用筛选选项")
    private Map<String, Object> availableFilters;
} 