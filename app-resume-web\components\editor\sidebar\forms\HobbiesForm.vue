<template>
  <div class="hobbies-form">
    <div class="form-header">
      <h4 class="form-title">兴趣爱好</h4>
      <p class="form-desc">展示您的个人兴趣和业余爱好</p>
    </div>
    
    <div v-if="formData.hobbies && formData.hobbies.length > 0" class="hobbies-list">
      <div v-for="(hobby, index) in formData.hobbies" :key="index" class="hobby-item">
        <div class="hobby-header">
          <input
            v-model="hobby.name"
            type="text"
            class="hobby-name-input"
            placeholder="兴趣爱好名称"
            @input="handleHobbyChange"
          />
          <select
            v-model="hobby.level"
            class="hobby-level-select"
            @change="handleHobbyChange"
          >
            <option value="">选择水平</option>
            <option value="初学者">初学者</option>
            <option value="爱好者">爱好者</option>
            <option value="熟练">熟练</option>
            <option value="专业">专业</option>
          </select>
          <button 
            class="delete-btn"
            @click="removeHobby(index)"
            title="删除兴趣爱好"
          >
            ×
          </button>
        </div>
        
        <RichTextEditor
          v-model="hobby.description"
          placeholder="详细描述（如：参与的活动、获得的成就等）"
          min-height="120px"
          @update:modelValue="handleHobbyChange"
        />
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无兴趣爱好信息</p>
      <p class="empty-hint">点击下方按钮添加您的兴趣爱好</p>
    </div>
    
    <div class="form-actions">
      <button class="add-btn" @click="addHobby">
        <Icon name="plus" size="sm" />
        <span>添加兴趣爱好</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ hobbies: [] })
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = reactive({
  hobbies: []
})

// 防抖定时器
let debounceTimer = null

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.hobbies) {
    formData.hobbies = [...newData.hobbies]
  } else {
    formData.hobbies = []
  }
}, { immediate: true, deep: true })

/**
 * 处理兴趣爱好变化 - 实时更新
 */
const handleHobbyChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的定时器，300ms后触发更新
  debounceTimer = setTimeout(() => {
    emit('update', { hobbies: [...formData.hobbies] })
  }, 300)
}

/**
 * 添加兴趣爱好
 */
const addHobby = () => {
  formData.hobbies.push({
    name: '',
    level: '',
    description: ''
  })
  handleHobbyChange()
}

/**
 * 删除兴趣爱好
 */
const removeHobby = (index) => {
  formData.hobbies.splice(index, 1)
  handleHobbyChange()
}
</script>

<style scoped>
.hobbies-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.hobbies-list {
  @apply space-y-4 mb-6;
}

.hobby-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-3;
}

.hobby-header {
  @apply flex items-center gap-2;
}

.hobby-name-input {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.hobby-level-select {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
  min-width: 100px;
}

.delete-btn {
  @apply w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold;
}

.description-textarea {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-xs text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}
</style> 