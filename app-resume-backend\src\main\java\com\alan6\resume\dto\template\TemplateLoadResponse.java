package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 模板加载响应DTO
 * @description 返回给前端的Vue模板文件内容、默认JSON内容和相关信息
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "TemplateLoadResponse", description = "模板加载响应")
public class TemplateLoadResponse {

    /**
     * 模板ID
     * @description 模板的唯一标识ID
     */
    @Schema(description = "模板ID", example = "1")
    private Long templateId;

    /**
     * 模板代码
     * @description 模板的唯一标识代码
     */
    @Schema(description = "模板代码", example = "modern-simple")
    private String templateCode;

    /**
     * 模板名称
     * @description 模板的显示名称
     */
    @Schema(description = "模板名称", example = "现代简约模板")
    private String templateName;

    /**
     * Vue模板内容
     * @description 完整的Vue单文件组件内容
     */
    @Schema(description = "Vue模板内容")
    private String templateContent;

    /**
     * 默认简历内容
     * @description 模板对应的默认简历内容JSON数据，如果模板没有专用内容则为null
     */
    @Schema(description = "默认简历内容JSON数据")
    private String defaultContent;

    /**
     * 模板配置数据
     * @description 模板的配置信息，包括支持的功能、默认主题等
     */
    @Schema(description = "模板配置数据")
    private Map<String, Object> configData;

    /**
     * 支持的功能特性
     * @description 模板支持的功能特性列表
     */
    @Schema(description = "支持的功能特性")
    private List<String> features;

    /**
     * 模板标签
     * @description 模板的标签信息
     */
    @Schema(description = "模板标签")
    private List<String> tags;

    /**
     * 数据来源
     * @description 数据的实际来源：redis、minio、local
     */
    @Schema(description = "数据来源", example = "redis")
    private String dataSource;

    /**
     * 加载时间
     * @description 模板加载耗时（毫秒）
     */
    @Schema(description = "加载时间（毫秒）", example = "15")
    private Long loadTime;

    /**
     * 缓存时间
     * @description 模板在当前存储层的缓存时间
     */
    @Schema(description = "缓存时间")
    private LocalDateTime cacheTime;

    /**
     * 文件大小
     * @description 模板文件的大小（字节）
     */
    @Schema(description = "文件大小（字节）", example = "12345")
    private Long fileSize;

    /**
     * 文件哈希值
     * @description 用于验证文件完整性的哈希值
     */
    @Schema(description = "文件哈希值")
    private String fileHash;

    /**
     * 是否为付费模板
     * @description 标识模板是否需要付费使用
     */
    @Schema(description = "是否为付费模板", example = "false")
    private Boolean isPremium;

    /**
     * 版本信息
     * @description 模板的版本信息
     */
    @Schema(description = "版本信息", example = "1.0.0")
    private String version;

    /**
     * 最后更新时间
     * @description 模板最后更新的时间
     */
    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;

    /**
     * 是否包含默认内容
     * @description 标识此次响应是否包含默认内容数据
     */
    @Schema(description = "是否包含默认内容", example = "true")
    private Boolean hasDefaultContent;

    /**
     * 内容ID
     * @description 关联的模板内容ID
     */
    @Schema(description = "内容ID", example = "1001")
    private Long contentId;
} 