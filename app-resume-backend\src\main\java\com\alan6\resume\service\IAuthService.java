package com.alan6.resume.service;

import com.alan6.resume.dto.auth.AdminLoginRequest;
import com.alan6.resume.dto.auth.AdminLoginResponse;
import com.alan6.resume.dto.auth.LoginRequest;
import com.alan6.resume.dto.auth.LoginResponse;
import com.alan6.resume.dto.auth.PasswordLoginRequest;
import com.alan6.resume.dto.auth.RegisterRequest;
import com.alan6.resume.dto.auth.RegisterResponse;

/**
 * 认证服务接口
 * 
 * 主要功能：
 * 1. 处理用户登录逻辑
 * 2. 支持多种登录方式（手机号、微信、小程序）
 * 3. 自动注册新用户
 * 4. 管理用户Token
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IAuthService {

    /**
     * 用户注册
     * 
     * 处理用户注册的完整流程：
     * 1. 验证注册参数的有效性
     * 2. 检查手机号是否已被注册
     * 3. 验证短信验证码的正确性
     * 4. 创建新用户账户
     * 5. 生成访问Token和刷新Token
     * 6. 返回注册成功响应
     * 
     * 注册成功后，用户可以直接使用返回的Token进行后续操作，
     * 无需再次登录。
     * 
     * @param registerRequest 用户注册请求参数
     * @return 注册响应数据，包含Token和用户信息
     */
    RegisterResponse register(RegisterRequest registerRequest);

    /**
     * 用户密码登录
     * 
     * 专门用于已注册用户的密码登录：
     * 1. 验证手机号和密码
     * 2. 检查用户是否存在
     * 3. 验证密码正确性
     * 4. 生成Token并存储到Redis
     * 5. 返回登录结果
     * 
     * 注意：此方法不会创建新用户，仅用于已注册用户登录
     * 
     * @param passwordLoginRequest 密码登录请求参数
     * @return 登录响应数据
     */
    LoginResponse passwordLogin(PasswordLoginRequest passwordLoginRequest);

    /**
     * 用户登录（短信验证码）
     * 
     * 支持多种登录方式：
     * 1. 手机号+验证码登录
     * 2. 微信授权登录
     * 3. 小程序登录
     * 
     * 登录流程：
     * 1. 验证登录参数
     * 2. 根据登录类型进行相应验证
     * 3. 查找或创建用户
     * 4. 生成Token并存储到Redis
     * 5. 返回登录结果
     * 
     * @param loginRequest 登录请求参数
     * @return 登录响应数据
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 用户登出
     * 
     * 清除用户的Token缓存，使Token失效
     * 
     * @param token 用户Token
     * @return true-登出成功，false-登出失败
     */
    boolean logout(String token);

    /**
     * 刷新Token
     * 
     * 延长Token的有效期，提升用户体验
     * 
     * @param token 当前Token
     * @return true-刷新成功，false-刷新失败
     */
    boolean refreshToken(String token);

    /**
     * 验证Token有效性
     * 
     * 检查Token是否存在且未过期
     * 
     * @param token Token字符串
     * @return true-有效，false-无效
     */
    boolean validateToken(String token);

    /**
     * 发送短信验证码
     * 
     * 用于手机号登录时发送验证码
     * 
     * @param phone 手机号
     * @param platform 平台标识
     * @return true-发送成功，false-发送失败
     */
    boolean sendSmsCode(String phone, String platform);

    /**
     * 管理员登录
     * 
     * 专门用于后台管理系统的登录接口
     * 只有具有管理员角色的用户才能登录成功
     * 
     * 登录流程：
     * 1. 验证手机号和密码
     * 2. 检查用户是否存在
     * 3. 验证用户是否具有管理员权限
     * 4. 生成Token并存储到Redis
     * 5. 获取用户权限和菜单信息
     * 6. 返回完整的管理员登录响应
     * 
     * @param adminLoginRequest 管理员登录请求参数
     * @param clientIp 客户端IP地址
     * @return 管理员登录响应数据，包含权限和菜单信息
     */
    AdminLoginResponse adminLogin(AdminLoginRequest adminLoginRequest, String clientIp);
} 