<template>
  <div class="simple-route-test">
    <h1>简单路由测试页面</h1>
    
    <div class="current-route">
      <h2>当前路由信息</h2>
      <ul>
        <li>路径: {{ $route.path }}</li>
        <li>名称: {{ $route.name }}</li>
        <li>参数: {{ JSON.stringify($route.params) }}</li>
        <li>查询: {{ JSON.stringify($route.query) }}</li>
      </ul>
    </div>
    
    <div class="navigation-test">
      <h2>导航测试（不需要认证）</h2>
      <div class="nav-buttons">
        <button @click="navigateTo('/')" class="btn">首页</button>
        <button @click="navigateTo('/login')" class="btn">登录页</button>
        <button @click="navigateTo('/register')" class="btn">注册页</button>
      </div>
    </div>
    
    <div class="admin-navigation-test">
      <h2>管理后台导航测试（需要认证）</h2>
      <div class="nav-buttons">
        <button @click="navigateToAdmin('/admin')" class="btn">管理首页</button>
        <button @click="navigateToAdmin('/admin/template/list')" class="btn">模板列表</button>
        <button @click="navigateToAdmin('/admin/template/converter')" class="btn">模板转换</button>
        <button @click="navigateToAdmin('/admin/template/upload')" class="btn">模板上传</button>
      </div>
    </div>
    
    <div class="direct-links">
      <h2>直接链接测试</h2>
      <ul>
        <li><NuxtLink to="/">首页</NuxtLink></li>
        <li><NuxtLink to="/login">登录页</NuxtLink></li>
        <li><NuxtLink to="/register">注册页</NuxtLink></li>
      </ul>
    </div>
    
    <div class="route-info">
      <h2>路由信息</h2>
      <p>当前页面不需要认证，可以正常访问所有公开路由。</p>
      <p>管理后台路由需要登录后才能访问。</p>
    </div>
  </div>
</template>

<script setup>
const router = useRouter()

const navigateTo = async (path) => {
  console.log('导航到:', path)
  try {
    await router.push(path)
  } catch (error) {
    console.error('导航失败:', error)
  }
}

const navigateToAdmin = async (path) => {
  console.log('尝试导航到管理后台:', path)
  try {
    await router.push(path)
  } catch (error) {
    console.error('管理后台导航失败:', error)
    console.log('这是正常的，因为需要登录认证')
  }
}
</script>

<style scoped>
.simple-route-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.current-route, .navigation-test, .admin-navigation-test, .direct-links, .route-info {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.nav-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn:hover {
  background: #2563eb;
}

ul {
  list-style-type: disc;
  padding-left: 20px;
}

li {
  margin: 5px 0;
}

a {
  color: #3b82f6;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style> 