package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 用户信息更新请求DTO
 * 
 * 主要功能：
 * 1. 接收用户更新个人信息的请求参数
 * 2. 参数校验和格式化
 * 3. 确保数据安全性和完整性
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Schema(name = "用户信息更新请求", description = "用户更新个人信息的请求参数")
public class UserProfileRequest {

    /**
     * 昵称
     * 用户的显示昵称，长度限制2-50个字符
     */
    @Schema(description = "用户的显示昵称，长度2-50个字符", example = "张三")
    @Size(min = 2, max = 50, message = "昵称长度必须在2-50个字符之间")
    private String nickname;

    /**
     * 性别
     * 0-未知，1-男，2-女
     */
    @Schema(description = "性别：0-未知，1-男，2-女", example = "1")
    private Byte gender;

    /**
     * 生日
     * 用户的生日，格式：YYYY-MM-DD
     */
    @Schema(description = "用户的生日，格式：YYYY-MM-DD", example = "1995-05-20")
    private LocalDate birthday;

    /**
     * 界面语言偏好
     * 支持的语言：zh-CN, en, ja, ko
     */
    @Schema(description = "界面语言偏好，支持的语言：zh-CN, en, ja, ko", example = "zh-CN")
    @Pattern(regexp = "^(zh-CN|en|ja|ko)$", message = "不支持的语言类型")
    private String preferredLanguage;

    /**
     * 验证性别值是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isGenderValid() {
        return gender == null || (gender >= 0 && gender <= 2);
    }

    /**
     * 验证生日是否有效（不能是未来日期）
     * 
     * @return true-有效，false-无效
     */
    public boolean isBirthdayValid() {
        if (birthday == null) {
            return true;
        }
        return !birthday.isAfter(LocalDate.now());
    }

    /**
     * 检查是否有任何字段需要更新
     * 
     * @return true-有字段需要更新，false-没有字段需要更新
     */
    public boolean hasFieldsToUpdate() {
        return nickname != null || gender != null || birthday != null || preferredLanguage != null;
    }
} 