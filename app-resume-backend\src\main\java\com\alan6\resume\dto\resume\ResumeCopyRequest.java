package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 简历复制请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "简历复制请求")
public class ResumeCopyRequest {

    /**
     * 新简历名称
     */
    @Schema(description = "新简历名称", example = "复制的简历", required = true)
    @NotBlank(message = "简历名称不能为空")
    @Size(min = 1, max = 100, message = "简历名称长度必须在1-100字符之间")
    private String name;
} 