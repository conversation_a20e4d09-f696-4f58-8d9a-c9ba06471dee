package com.alan6.resume.service.impl;

import com.alan6.resume.config.MinioConfig;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.service.ITemplateFileService;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.StatObjectArgs;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 模板文件服务实现类
 * 
 * @description 处理MinIO和本地文件的模板文件读取操作
 * 支持新的按模板分组的目录结构：resume-templates/{template-code}/
 * <AUTHOR>
 * @since 2025-07-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateFileServiceImpl implements ITemplateFileService {

    private final MinioClient minioClient;
    private final MinioConfig.MinioProperties minioProperties;

    /**
     * 加载模板内容
     * 
     * @param templateId 模板ID
     * @return 模板内容，如果加载失败则返回null
     */
    @Override
    public String loadTemplateContent(Long templateId) {
        log.debug("加载模板内容，模板ID: {}", templateId);
        
        try {
            // 根据模板ID构建模板代码（这里简化处理，实际应该从数据库获取）
            String templateCode = "template-" + templateId;
            return loadTemplateContent(templateCode);
        } catch (Exception e) {
            log.error("加载模板内容失败，模板ID: {}, 错误: {}", templateId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 加载模板内容（通过模板代码）
     * 
     * @param templateCode 模板代码
     * @return 模板内容，如果加载失败则返回null
     */
    @Override
    public String loadTemplateContent(String templateCode) {
        log.debug("加载模板内容，模板代码: {}", templateCode);
        
        try {
            // 构建Vue文件路径：resume-templates/{templateCode}/{templateCode}.vue
            String vueFilePath = buildFilePath(templateCode, "vue");
            
            // 从MinIO加载Vue文件内容
            return loadFileFromMinIO(vueFilePath);
        } catch (Exception e) {
            log.error("加载模板内容失败，模板代码: {}, 错误: {}", templateCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从MinIO加载模板内容
     * 
     * @param template 模板实体对象
     * @return 模板内容，如果加载失败则返回null
     */
    @Override
    public String loadFromMinIO(ResumeTemplates template) {
        log.debug("从MinIO加载模板，模板ID: {}", template != null ? template.getId() : "null");
        
        try {
            if (template == null || !StringUtils.hasText(template.getTemplateCode())) {
                log.warn("模板对象为空或模板代码为空");
                return null;
            }
            
            // 使用模板代码加载Vue文件
            return loadTemplateContent(template.getTemplateCode());
        } catch (Exception e) {
            log.warn("从MinIO加载模板失败，模板ID: {}, 错误: {}", 
                template != null ? template.getId() : "null", e.getMessage());
            return null;
        }
    }

    /**
     * 从本地文件系统加载模板内容
     * 
     * @param template 模板实体对象
     * @return 模板内容，如果加载失败则返回null
     */
    @Override
    public String loadFromLocal(ResumeTemplates template) {
        log.debug("从本地文件系统加载模板，模板ID: {}", template != null ? template.getId() : "null");
        
        try {
            // TODO: 实现从本地文件系统加载模板的逻辑
            // 这里应该根据模板信息查找本地文件并读取内容
            // 目前返回null，表示本地文件系统中没有模板
            return null;
        } catch (Exception e) {
            log.warn("从本地文件系统加载模板失败，模板ID: {}, 错误: {}", 
                template != null ? template.getId() : "null", e.getMessage());
            return null;
        }
    }

    /**
     * 保存模板内容到MinIO
     * 
     * @param templateId 模板ID
     * @param templateContent 模板内容
     * @return 是否保存成功
     */
    @Override
    public boolean saveToMinIO(Long templateId, String templateContent) {
        log.debug("保存模板到MinIO，模板ID: {}", templateId);
        
        try {
            if (templateId == null || !StringUtils.hasText(templateContent)) {
                log.warn("模板ID或内容为空");
                return false;
            }
            
            // 构建模板代码（这里简化处理，实际应该从数据库获取）
            String templateCode = "template-" + templateId;
            
            // 保存Vue文件
            return saveVueFile(templateCode, templateContent);
        } catch (Exception e) {
            log.error("保存模板到MinIO失败，模板ID: {}, 错误: {}", templateId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存模板内容到本地文件系统
     * 
     * @param templateId 模板ID
     * @param templateContent 模板内容
     * @return 是否保存成功
     */
    @Override
    public boolean saveToLocal(Long templateId, String templateContent) {
        log.debug("保存模板到本地文件系统，模板ID: {}", templateId);
        
        try {
            // TODO: 实现保存模板到本地文件系统的逻辑
            // 这里应该将模板内容写入本地文件
            // 目前返回false，表示保存失败
            return false;
        } catch (Exception e) {
            log.error("保存模板到本地文件系统失败，模板ID: {}, 错误: {}", templateId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查模板文件是否存在于MinIO
     * 
     * @param template 模板实体对象
     * @return 是否存在
     */
    @Override
    public boolean existsInMinIO(ResumeTemplates template) {
        log.debug("检查MinIO中是否存在模板，模板ID: {}", template != null ? template.getId() : "null");
        
        try {
            if (template == null || !StringUtils.hasText(template.getTemplateCode())) {
                log.warn("模板对象为空或模板代码为空");
                return false;
            }
            
            // 检查Vue文件是否存在
            String vueFilePath = buildFilePath(template.getTemplateCode(), "vue");
            return fileExistsInMinIO(vueFilePath);
        } catch (Exception e) {
            log.warn("检查MinIO模板存在性失败，模板ID: {}, 错误: {}", 
                template != null ? template.getId() : "null", e.getMessage());
            return false;
        }
    }

    /**
     * 检查模板文件是否存在于本地
     * 
     * @param template 模板实体对象
     * @return 是否存在
     */
    @Override
    public boolean existsInLocal(ResumeTemplates template) {
        log.debug("检查本地文件系统中是否存在模板，模板ID: {}", template != null ? template.getId() : "null");
        
        try {
            // TODO: 实现检查本地文件系统中模板文件是否存在的逻辑
            // 目前返回false，表示本地文件系统中不存在模板
            return false;
        } catch (Exception e) {
            log.warn("检查本地文件模板存在性失败，模板ID: {}, 错误: {}", 
                template != null ? template.getId() : "null", e.getMessage());
            return false;
        }
    }

    /**
     * 获取模板文件大小
     * 
     * @param templateId 模板ID
     * @return 文件大小，如果获取失败则返回-1
     */
    @Override
    public long getTemplateFileSize(Long templateId) {
        log.debug("获取模板文件大小，模板ID: {}", templateId);
        
        try {
            // TODO: 实现获取模板文件大小的逻辑
            // 目前返回-1，表示获取失败
            return -1;
        } catch (Exception e) {
            log.error("获取模板文件大小失败，模板ID: {}, 错误: {}", templateId, e.getMessage(), e);
            return -1;
        }
    }

    /**
     * 计算模板文件哈希值
     * 
     * @param templateContent 模板内容
     * @return 哈希值
     */
    @Override
    public String calculateFileHash(String templateContent) {
        if (templateContent == null || templateContent.isEmpty()) {
            return "";
        }
        
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(templateContent.getBytes("UTF-8"));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException | java.io.UnsupportedEncodingException e) {
            log.error("计算文件哈希值失败，错误: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 同步模板文件
     * 
     * @param templateId 模板ID
     * @param sourceType 源类型（minio或local）
     * @return 是否同步成功
     */
    @Override
    public boolean syncTemplateFile(Long templateId, String sourceType) {
        log.debug("同步模板文件，模板ID: {}, 源类型: {}", templateId, sourceType);
        
        try {
            // TODO: 实现模板文件同步逻辑
            // 这里应该根据sourceType在MinIO和本地之间同步文件
            // 目前返回false，表示同步失败
            return false;
        } catch (Exception e) {
            log.error("同步模板文件失败，模板ID: {}, 源类型: {}, 错误: {}", 
                templateId, sourceType, e.getMessage(), e);
            return false;
        }
    }

    // ================================
    // 私有辅助方法
    // ================================

    /**
     * 构建文件路径
     * 
     * @param templateCode 模板代码
     * @param fileType 文件类型（vue, json, html）
     * @return 文件路径
     */
    private String buildFilePath(String templateCode, String fileType) {
        return String.format("%s/%s.%s", templateCode, templateCode, fileType);
    }

    /**
     * 从MinIO加载文件内容
     * 
     * @param filePath 文件路径
     * @return 文件内容，如果加载失败则返回null
     */
    private String loadFileFromMinIO(String filePath) {
        try {
            log.debug("从MinIO加载文件: {}", filePath);
            
            InputStream inputStream = minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket(minioProperties.getTemplateBucketName())
                    .object(filePath)
                    .build()
            );
            
            // 读取输入流内容
            byte[] content = inputStream.readAllBytes();
            inputStream.close();
            
            String result = new String(content, StandardCharsets.UTF_8);
            log.debug("从MinIO加载文件成功，文件大小: {} bytes", content.length);
            return result;
            
        } catch (Exception e) {
            log.warn("从MinIO加载文件失败，文件路径: {}, 错误: {}", filePath, e.getMessage());
            return null;
        }
    }

    /**
     * 保存文件到MinIO
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @return 是否保存成功
     */
    private boolean saveFileToMinIO(String filePath, String content) {
        try {
            log.debug("保存文件到MinIO: {}", filePath);
            
            byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(contentBytes);
            
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(minioProperties.getTemplateBucketName())
                    .object(filePath)
                    .stream(inputStream, contentBytes.length, -1)
                    .contentType("text/plain")
                    .build()
            );
            
            inputStream.close();
            log.debug("保存文件到MinIO成功，文件大小: {} bytes", contentBytes.length);
            return true;
            
        } catch (Exception e) {
            log.error("保存文件到MinIO失败，文件路径: {}, 错误: {}", filePath, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查文件是否存在于MinIO
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    private boolean fileExistsInMinIO(String filePath) {
        try {
            minioClient.statObject(
                StatObjectArgs.builder()
                    .bucket(minioProperties.getTemplateBucketName())
                    .object(filePath)
                    .build()
            );
            return true;
        } catch (Exception e) {
            log.debug("检查文件存在性失败，文件路径: {}, 错误: {}", filePath, e.getMessage());
            return false;
        }
    }

    /**
     * 加载JSON内容文件
     * 
     * @param templateCode 模板代码
     * @return JSON内容，如果加载失败则返回null
     */
    public String loadJsonContent(String templateCode) {
        try {
            String jsonFilePath = buildFilePath(templateCode, "json");
            return loadFileFromMinIO(jsonFilePath);
        } catch (Exception e) {
            log.warn("加载JSON内容失败，模板代码: {}, 错误: {}", templateCode, e.getMessage());
            return null;
        }
    }

    /**
     * 保存Vue文件
     * 
     * @param templateCode 模板代码
     * @param vueContent Vue文件内容
     * @return 是否保存成功
     */
    public boolean saveVueFile(String templateCode, String vueContent) {
        try {
            String vueFilePath = buildFilePath(templateCode, "vue");
            return saveFileToMinIO(vueFilePath, vueContent);
        } catch (Exception e) {
            log.error("保存Vue文件失败，模板代码: {}, 错误: {}", templateCode, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存JSON文件
     * 
     * @param templateCode 模板代码
     * @param jsonContent JSON文件内容
     * @return 是否保存成功
     */
    public boolean saveJsonFile(String templateCode, String jsonContent) {
        try {
            String jsonFilePath = buildFilePath(templateCode, "json");
            return saveFileToMinIO(jsonFilePath, jsonContent);
        } catch (Exception e) {
            log.error("保存JSON文件失败，模板代码: {}, 错误: {}", templateCode, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存HTML预览文件
     * 
     * @param templateCode 模板代码
     * @param htmlContent HTML文件内容
     * @return 是否保存成功
     */
    public boolean saveHtmlFile(String templateCode, String htmlContent) {
        try {
            String htmlFilePath = buildFilePath(templateCode, "html");
            return saveFileToMinIO(htmlFilePath, htmlContent);
        } catch (Exception e) {
            log.error("保存HTML文件失败，模板代码: {}, 错误: {}", templateCode, e.getMessage(), e);
            return false;
        }
    }


} 