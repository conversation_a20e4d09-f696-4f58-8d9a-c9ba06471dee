package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付状态查询响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "PaymentStatusResponse", description = "支付状态查询响应")
public class PaymentStatusResponse {

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "ORD20241222123456789")
    private String orderNo;

    /**
     * 支付ID
     */
    @Schema(description = "支付ID", example = "PAY20241222123456789")
    private String paymentId;

    /**
     * 支付状态
     */
    @Schema(description = "支付状态", example = "paid", 
            allowableValues = {"unpaid", "paying", "paid", "failed", "timeout", "refunding", "refunded", "closed"})
    private String paymentStatus;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态", example = "completed")
    private String orderStatus;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式", example = "wechat")
    private String paymentMethod;

    /**
     * 订单金额
     */
    @Schema(description = "订单金额", example = "199.90")
    private BigDecimal amount;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额", example = "199.90")
    private BigDecimal paidAmount;

    /**
     * 货币类型
     */
    @Schema(description = "货币类型", example = "CNY")
    private String currency;

    /**
     * 第三方交易号
     */
    @Schema(description = "第三方交易号", example = "4200001234567890123456789")
    private String transactionId;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间", example = "2024-12-22 15:10:30")
    private LocalDateTime paymentTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-22 15:00:00")
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间", example = "2024-12-22 15:30:00")
    private LocalDateTime expireTime;

    /**
     * 支付详细信息
     */
    @Schema(description = "支付详细信息")
    private PaymentDetail paymentInfo;

    /**
     * 支付详细信息内部类
     */
    @Data
    @Schema(name = "PaymentDetail", description = "支付详细信息")
    public static class PaymentDetail {
        
        /**
         * 付款账户
         */
        @Schema(description = "付款账户", example = "wx_user_123456")
        private String payerAccount;

        /**
         * 银行类型
         */
        @Schema(description = "银行类型", example = "CFT")
        private String bankType;

        /**
         * 货币类型
         */
        @Schema(description = "货币类型", example = "CNY")
        private String feeType;
    }
} 