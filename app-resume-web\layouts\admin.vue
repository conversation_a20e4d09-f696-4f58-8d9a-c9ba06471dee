<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <AdminSidebar />
    
    <!-- 主内容区域 -->
    <div class="admin-main" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- 顶部导航栏 -->
      <AdminHeader />
      
      <!-- 页面内容 -->
      <div class="admin-content">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAdminStore } from '~/composables/admin/useAdminStore'

// 使用管理员状态
const { sidebarCollapsed } = useAdminStore()

// 设置页面标题
useHead({
  title: '后台管理 - 火花简历',
  meta: [
    { name: 'description', content: '火花简历后台管理系统' }
  ]
})
</script>

<style scoped>
.admin-layout {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

.admin-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
  margin-left: 280px;
}

.admin-main.sidebar-collapsed {
  margin-left: 80px;
}

.admin-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-main {
    margin-left: 0;
  }
  
  .admin-main.sidebar-collapsed {
    margin-left: 0;
  }
}
</style> 