package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 使用模板响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "使用模板响应DTO")
public class TemplateUseResponse {

    /**
     * 简历ID
     */
    @Schema(description = "简历ID")
    private Long resumeId;

    /**
     * 简历名称
     */
    @Schema(description = "简历名称")
    private String resumeName;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    private Long templateId;

    /**
     * 重定向URL
     */
    @Schema(description = "重定向URL")
    private String redirectUrl;
} 