package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.common.utils.RedisUtils;
import com.alan6.resume.dto.system.CacheClearRequest;
import com.alan6.resume.dto.system.CacheClearResponse;
import com.alan6.resume.dto.system.MaintenanceRequest;
import com.alan6.resume.dto.system.MaintenanceResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SystemMaintenanceServiceImpl 测试类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("系统维护服务测试")
class SystemMaintenanceServiceImplTest {

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private RedisUtils redisUtils;

    @InjectMocks
    private SystemMaintenanceServiceImpl systemMaintenanceService;

    private CacheClearRequest cacheClearRequest;
    private MaintenanceRequest maintenanceRequest;

    /**
     * 测试数据初始化
     */
    @BeforeEach
    void setUp() {
        // 初始化缓存清理请求
        cacheClearRequest = new CacheClearRequest();
        cacheClearRequest.setCacheType("user");
        cacheClearRequest.setKeys(Arrays.asList("user:*", "session:*"));

        // 初始化维护模式请求
        maintenanceRequest = new MaintenanceRequest();
        maintenanceRequest.setEnabled(true);
        maintenanceRequest.setMessage("系统维护中，预计30分钟后恢复");
        maintenanceRequest.setAllowedIps(Arrays.asList("*************", "*************"));
        maintenanceRequest.setStartTime(LocalDateTime.now());
        maintenanceRequest.setEndTime(LocalDateTime.now().plusMinutes(30));
    }

    /**
     * 测试清理所有缓存 - 成功
     */
    @Test
    @DisplayName("清理所有缓存 - 成功")
    void testClearCache_All_Success() {
        // 准备测试数据
        Set<String> allKeys = Set.of("user:123", "user:456", "resume:789", "system:config");
        Set<String> keysToDelete = Set.of("user:123", "user:456", "resume:789");

        // 模拟Redis调用
        when(redisTemplate.keys("*")).thenReturn(allKeys);
        when(redisTemplate.delete(keysToDelete)).thenReturn(3L);

        // 执行测试
        CacheClearResponse result = systemMaintenanceService.clearCache(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getClearedKeys());
        assertTrue(result.getFreedMemory().contains("KB"));
        assertTrue(result.getOperationTime().contains("ms"));

        // 验证Redis调用
        verify(redisTemplate).keys("*");
        verify(redisTemplate).delete(any(Set.class));
    }

    /**
     * 测试按类型清理缓存 - 成功
     */
    @Test
    @DisplayName("按类型清理缓存 - 成功")
    void testClearCache_ByType_Success() {
        // 准备测试数据
        Set<String> userKeys = Set.of("user:123", "user:456");

        // 模拟Redis调用
        when(redisTemplate.keys("user:*")).thenReturn(userKeys);
        when(redisTemplate.delete(userKeys)).thenReturn(2L);

        // 执行测试
        CacheClearResponse result = systemMaintenanceService.clearCache(cacheClearRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getClearedKeys());
        assertTrue(result.getFreedMemory().contains("KB"));

        // 验证Redis调用
        verify(redisTemplate).keys("user:*");
        verify(redisTemplate).delete(userKeys);
    }

    /**
     * 测试按指定键清理缓存 - 成功
     */
    @Test
    @DisplayName("按指定键清理缓存 - 成功")
    void testClearCache_ByKeys_Success() {
        // 准备测试数据
        Set<String> userKeys = Set.of("user:123", "user:456");
        Set<String> sessionKeys = Set.of("session:abc", "session:def");

        // 模拟Redis调用
        when(redisTemplate.keys("user:*")).thenReturn(userKeys);
        when(redisTemplate.keys("session:*")).thenReturn(sessionKeys);
        when(redisTemplate.delete(userKeys)).thenReturn(2L);
        when(redisTemplate.delete(sessionKeys)).thenReturn(2L);

        // 执行测试
        CacheClearResponse result = systemMaintenanceService.clearCache(cacheClearRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.getClearedKeys());

        // 验证Redis调用
        verify(redisTemplate).keys("user:*");
        verify(redisTemplate).keys("session:*");
        verify(redisTemplate).delete(userKeys);
        verify(redisTemplate).delete(sessionKeys);
    }

    /**
     * 测试清理缓存 - 无键可删除
     */
    @Test
    @DisplayName("清理缓存 - 无键可删除")
    void testClearCache_NoKeysToDelete() {
        // 模拟Redis调用 - 返回空集合
        when(redisTemplate.keys("user:*")).thenReturn(Collections.emptySet());

        // 执行测试
        CacheClearResponse result = systemMaintenanceService.clearCache(cacheClearRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getClearedKeys());
        assertEquals("0B", result.getFreedMemory());

        // 验证Redis调用
        verify(redisTemplate).keys("user:*");
        verify(redisTemplate, never()).delete(any(Set.class));
    }

    /**
     * 测试清理缓存 - Redis异常
     */
    @Test
    @DisplayName("清理缓存 - Redis异常")
    void testClearCache_RedisException() {
        // 模拟Redis异常
        when(redisTemplate.keys("user:*")).thenThrow(new RuntimeException("Redis connection error"));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> systemMaintenanceService.clearCache(cacheClearRequest));
        
        assertTrue(exception.getMessage().contains("清理缓存失败"));

        // 验证Redis调用
        verify(redisTemplate).keys("user:*");
        verify(redisTemplate, never()).delete(any(Set.class));
    }

    /**
     * 测试启用维护模式 - 成功
     */
    @Test
    @DisplayName("启用维护模式 - 成功")
    void testSetMaintenanceMode_Enable_Success() {
        // 模拟RedisUtils调用
        doNothing().when(redisUtils).set(anyString(), any(MaintenanceResponse.class), anyLong());

        // 执行测试
        MaintenanceResponse result = systemMaintenanceService.setMaintenanceMode(maintenanceRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getEnabled());
        assertEquals("系统维护中，预计30分钟后恢复", result.getMessage());
        assertEquals("*************,*************", result.getAllowedIps());
        assertNotNull(result.getStartTime());
        assertNotNull(result.getEndTime());
        assertEquals("ADMIN", result.getOperator());

        // 验证RedisUtils调用
        verify(redisUtils).set(eq("system:maintenance:config"), any(MaintenanceResponse.class), anyLong());
    }

    /**
     * 测试禁用维护模式 - 成功
     */
    @Test
    @DisplayName("禁用维护模式 - 成功")
    void testSetMaintenanceMode_Disable_Success() {
        // 准备测试数据 - 禁用维护模式
        maintenanceRequest.setEnabled(false);
        maintenanceRequest.setEndTime(null);

        // 模拟RedisUtils调用
        doNothing().when(redisUtils).set(anyString(), any(MaintenanceResponse.class));

        // 执行测试
        MaintenanceResponse result = systemMaintenanceService.setMaintenanceMode(maintenanceRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getEnabled());
        assertNotNull(result.getEndTime()); // 应该设置为当前时间

        // 验证RedisUtils调用
        verify(redisUtils).set(eq("system:maintenance:config"), any(MaintenanceResponse.class));
    }

    /**
     * 测试设置维护模式 - Redis异常
     */
    @Test
    @DisplayName("设置维护模式 - Redis异常")
    void testSetMaintenanceMode_RedisException() {
        // 模拟Redis异常
        doThrow(new RuntimeException("Redis connection error"))
                .when(redisUtils).set(anyString(), any(MaintenanceResponse.class), anyLong());

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> systemMaintenanceService.setMaintenanceMode(maintenanceRequest));
        
        assertTrue(exception.getMessage().contains("设置维护模式失败"));

        // 验证RedisUtils调用
        verify(redisUtils).set(anyString(), any(MaintenanceResponse.class), anyLong());
    }

    /**
     * 测试获取维护状态 - 有配置
     */
    @Test
    @DisplayName("获取维护状态 - 有配置")
    void testGetMaintenanceStatus_WithConfig() {
        // 准备测试数据
        MaintenanceResponse existingConfig = new MaintenanceResponse();
        existingConfig.setEnabled(true);
        existingConfig.setMessage("系统维护中");

        // 模拟RedisUtils调用
        when(redisUtils.get("system:maintenance:config")).thenReturn(existingConfig);

        // 执行测试
        MaintenanceResponse result = systemMaintenanceService.getMaintenanceStatus();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getEnabled());
        assertEquals("系统维护中", result.getMessage());

        // 验证RedisUtils调用
        verify(redisUtils).get("system:maintenance:config");
    }

    /**
     * 测试获取维护状态 - 无配置
     */
    @Test
    @DisplayName("获取维护状态 - 无配置")
    void testGetMaintenanceStatus_NoConfig() {
        // 模拟RedisUtils调用 - 返回null
        when(redisUtils.get("system:maintenance:config")).thenReturn(null);

        // 执行测试
        MaintenanceResponse result = systemMaintenanceService.getMaintenanceStatus();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getEnabled());
        assertEquals("系统正常运行中", result.getMessage());

        // 验证RedisUtils调用
        verify(redisUtils).get("system:maintenance:config");
    }

    /**
     * 测试获取维护状态 - Redis异常
     */
    @Test
    @DisplayName("获取维护状态 - Redis异常")
    void testGetMaintenanceStatus_RedisException() {
        // 模拟Redis异常
        when(redisUtils.get("system:maintenance:config")).thenThrow(new RuntimeException("Redis connection error"));

        // 执行测试 - 不应该抛异常，应该返回默认状态
        MaintenanceResponse result = systemMaintenanceService.getMaintenanceStatus();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getEnabled());
        assertEquals("系统正常运行中", result.getMessage());

        // 验证RedisUtils调用
        verify(redisUtils).get("system:maintenance:config");
    }

    /**
     * 测试检查维护模式 - 启用
     */
    @Test
    @DisplayName("检查维护模式 - 启用")
    void testIsMaintenanceMode_Enabled() {
        // 准备测试数据
        MaintenanceResponse config = new MaintenanceResponse();
        config.setEnabled(true);

        // 模拟RedisUtils调用
        when(redisUtils.get("system:maintenance:config")).thenReturn(config);

        // 执行测试
        boolean result = systemMaintenanceService.isMaintenanceMode();

        // 验证结果
        assertTrue(result);

        // 验证RedisUtils调用
        verify(redisUtils).get("system:maintenance:config");
    }

    /**
     * 测试检查维护模式 - 禁用
     */
    @Test
    @DisplayName("检查维护模式 - 禁用")
    void testIsMaintenanceMode_Disabled() {
        // 准备测试数据
        MaintenanceResponse config = new MaintenanceResponse();
        config.setEnabled(false);

        // 模拟RedisUtils调用
        when(redisUtils.get("system:maintenance:config")).thenReturn(config);

        // 执行测试
        boolean result = systemMaintenanceService.isMaintenanceMode();

        // 验证结果
        assertFalse(result);

        // 验证RedisUtils调用
        verify(redisUtils).get("system:maintenance:config");
    }

    /**
     * 测试检查维护模式 - 异常时返回false
     */
    @Test
    @DisplayName("检查维护模式 - 异常时返回false")
    void testIsMaintenanceMode_ExceptionReturnsFalse() {
        // 模拟Redis异常
        when(redisUtils.get("system:maintenance:config")).thenThrow(new RuntimeException("Redis connection error"));

        // 执行测试
        boolean result = systemMaintenanceService.isMaintenanceMode();

        // 验证结果
        assertFalse(result);

        // 验证RedisUtils调用
        verify(redisUtils).get("system:maintenance:config");
    }
} 