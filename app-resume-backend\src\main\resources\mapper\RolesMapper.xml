<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alan6.resume.mapper.RolesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.alan6.resume.entity.Roles">
        <id column="id" property="id" />
        <result column="role_name" property="roleName" />
        <result column="role_code" property="roleCode" />
        <result column="description" property="description" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_name, role_code, description, sort_order, status, is_deleted, create_time, update_time
    </sql>

    <!-- 根据用户ID获取角色列表 -->
    <select id="selectRolesByUserId" resultMap="BaseResultMap">
        SELECT r.id, r.role_name, r.role_code, r.description, r.sort_order, r.status, r.is_deleted, r.create_time, r.update_time
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.is_deleted = 0
          AND r.status = 1
          AND ur.is_deleted = 0
        ORDER BY r.sort_order ASC
    </select>

    <!-- 根据角色代码获取角色 -->
    <select id="selectByRoleCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM roles
        WHERE role_code = #{roleCode}
          AND is_deleted = 0
    </select>

    <!-- 获取所有启用的角色 -->
    <select id="selectEnabledRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM roles
        WHERE status = 1
          AND is_deleted = 0
        ORDER BY sort_order ASC
    </select>

</mapper> 