/**
 * 简历数据管理
 * @description 处理简历数据的获取、保存等与后端API的交互
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref } from 'vue'

// ================================
// API接口地址
// ================================
const API_ENDPOINTS = {
  RESUME: {
    LIST: '/api/resume/list',
    DETAIL: '/api/resume/detail',
    CREATE: '/api/resume/create',
    UPDATE: '/api/resume/update',
    DELETE: '/api/resume/delete',
    EXPORT: '/api/resume/export'
  },
  TEMPLATE: {
    LIST: '/api/template/list',
    DETAIL: '/api/template/detail'
  }
}

/**
 * 简历数据管理Hook
 * @returns {Object} 数据管理方法
 */
export const useResumeData = () => {
  // ================================
  // 响应式数据
  // ================================
  const loading = ref(false)
  const error = ref(null)

  // ================================
  // 请求工具方法
  // ================================

  /**
   * 发送HTTP请求
   * @param {string} url - 请求地址
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  const request = async (url, options = {}) => {
    try {
      const { $fetch } = useNuxtApp()
      
      const response = await $fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      })

      return response
    } catch (err) {
      console.error('请求失败:', err)
      throw new Error(err.data?.message || err.message || '网络请求失败')
    }
  }

  // ================================
  // 简历相关API方法
  // ================================

  /**
   * 获取简历列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 简历列表
   */
  const fetchResumeList = async (params = {}) => {
    try {
      loading.value = true
      error.value = null

      const response = await request(API_ENDPOINTS.RESUME.LIST, {
        method: 'GET',
        query: params
      })

      return response.data || []
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取简历详情
   * @param {string} resumeId - 简历ID
   * @returns {Promise<Object>} 简历数据
   */
  const fetchResume = async (resumeId) => {
    // 开发环境返回空数据
    if (process.env.NODE_ENV === 'development') {
      return null
    }

    try {
      loading.value = true
      error.value = null

      const response = await request(`${API_ENDPOINTS.RESUME.DETAIL}/${resumeId}`, {
        method: 'GET'
      })

      if (!response.data) {
        throw new Error('简历数据不存在')
      }

      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建简历
   * @param {Object} resumeData - 简历数据
   * @returns {Promise<Object>} 创建结果
   */
  const createResume = async (resumeData) => {
    try {
      loading.value = true
      error.value = null

      const response = await request(API_ENDPOINTS.RESUME.CREATE, {
        method: 'POST',
        body: resumeData
      })

      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 保存简历
   * @param {Object} resumeData - 简历数据
   * @returns {Promise<Object>} 保存结果
   */
  const saveResume = async (resumeData) => {
    // 暂时模拟保存，后续对接真实API
    if (process.env.NODE_ENV === 'development') {
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log('模拟保存简历:', resumeData.id || '新简历')
          resolve({ success: true, id: resumeData.id || generateId() })
        }, 1000)
      })
    }

    try {
      loading.value = true
      error.value = null

      const isCreate = !resumeData.id
      const url = isCreate ? API_ENDPOINTS.RESUME.CREATE : API_ENDPOINTS.RESUME.UPDATE
      const method = isCreate ? 'POST' : 'PUT'

      const response = await request(url, {
        method,
        body: resumeData
      })

      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除简历
   * @param {string} resumeId - 简历ID
   * @returns {Promise<boolean>} 删除结果
   */
  const deleteResume = async (resumeId) => {
    try {
      loading.value = true
      error.value = null

      await request(`${API_ENDPOINTS.RESUME.DELETE}/${resumeId}`, {
        method: 'DELETE'
      })

      return true
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 导出简历
   * @param {string} resumeId - 简历ID
   * @param {string} format - 导出格式 (pdf/word/image)
   * @returns {Promise<Blob>} 文件blob
   */
  const exportResume = async (resumeId, format = 'pdf') => {
    try {
      loading.value = true
      error.value = null

      const response = await request(`${API_ENDPOINTS.RESUME.EXPORT}/${resumeId}`, {
        method: 'POST',
        body: { format },
        responseType: 'blob'
      })

      return response
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // ================================
  // 模板相关API方法
  // ================================

  /**
   * 获取模板列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 模板列表
   */
  const fetchTemplateList = async (params = {}) => {
    try {
      loading.value = true
      error.value = null

      const response = await request(API_ENDPOINTS.TEMPLATE.LIST, {
        method: 'GET',
        query: params
      })

      return response.data || []
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取模板详情
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 模板数据
   */
  const fetchTemplate = async (templateId) => {
    try {
      loading.value = true
      error.value = null

      const response = await request(`${API_ENDPOINTS.TEMPLATE.DETAIL}/${templateId}`, {
        method: 'GET'
      })

      if (!response.data) {
        throw new Error('模板数据不存在')
      }

      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // ================================
  // 工具方法
  // ================================

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 验证简历数据
   * @param {Object} resumeData - 简历数据
   * @returns {Object} 验证结果
   */
  const validateResumeData = (resumeData) => {
    const errors = []

    // 检查必填字段
    if (!resumeData.title?.trim()) {
      errors.push('简历标题不能为空')
    }

    if (!resumeData.modules?.length) {
      errors.push('简历必须包含至少一个模块')
    }

    // 检查基本信息模块
    const basicInfoModule = resumeData.modules?.find(m => m.type === 'basic-info')
    if (!basicInfoModule) {
      errors.push('简历必须包含基本信息模块')
    } else {
      const { name, phone, email } = basicInfoModule.data || {}
      if (!name?.trim()) errors.push('姓名不能为空')
      if (!phone?.trim()) errors.push('联系电话不能为空')
      if (!email?.trim()) errors.push('邮箱不能为空')
      else if (!isValidEmail(email)) errors.push('邮箱格式不正确')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // ================================
  // 返回方法
  // ================================
  return {
    // 状态
    loading,
    error,

    // 简历相关方法
    fetchResumeList,
    fetchResume,
    createResume,
    saveResume,
    deleteResume,
    exportResume,

    // 模板相关方法
    fetchTemplateList,
    fetchTemplate,

    // 工具方法
    clearError,
    validateResumeData
  }
}

// ================================
// 工具函数
// ================================

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否有效
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 获取模拟简历数据
 * @param {string} resumeId - 简历ID
 * @returns {Object} 模拟数据
 */
function getMockResumeData(resumeId) {
  return {
    id: resumeId,
    templateId: 'template-001',
    title: '前端开发工程师简历',
    lastSaved: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    modules: {
      basic_info: {
        name: '张三',
        title: '前端开发工程师',
        phone: '13800138000',
        email: '<EMAIL>',
        address: '北京市朝阳区',
        website: 'https://zhangsan.dev',
        linkedin: '',
        github: 'https://github.com/zhangsan',
        photo: ''
      }
    },
    settings: {
      theme: {
        primaryColor: '#3B82F6',
        fontFamily: 'system-ui',
        fontSize: '14px',
        lineHeight: '1.6'
      },
      layout: {
        marginTop: '20px',
        marginBottom: '20px',
        marginLeft: '20px',
        marginRight: '20px',
        spacing: '16px'
      }
    }
  }
} 