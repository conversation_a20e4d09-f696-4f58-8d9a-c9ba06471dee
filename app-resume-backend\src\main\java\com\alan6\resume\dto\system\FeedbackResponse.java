package com.alan6.resume.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 反馈响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(description = "反馈响应DTO")
public class FeedbackResponse {

    /**
     * 反馈ID
     */
    @Schema(description = "反馈ID")
    private Long id;

    /**
     * 反馈编号
     */
    @Schema(description = "反馈编号", example = "FB1703242800001")
    private String feedbackId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 反馈类型
     */
    @Schema(description = "反馈类型", example = "bug", allowableValues = {"bug", "feature", "complaint", "suggestion"})
    private String type;

    /**
     * 反馈标题
     */
    @Schema(description = "反馈标题", example = "文件上传失败")
    private String title;

    /**
     * 反馈内容
     */
    @Schema(description = "反馈内容")
    private String content;

    /**
     * 联系邮箱
     */
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "13800138000")
    private String phone;

    /**
     * 优先级
     */
    @Schema(description = "优先级", example = "medium", allowableValues = {"low", "medium", "high", "urgent"})
    private String priority;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态", example = "pending", allowableValues = {"pending", "processing", "resolved", "closed"})
    private String status;

    /**
     * 处理人ID
     */
    @Schema(description = "处理人ID")
    private Long handlerId;

    /**
     * 处理回复
     */
    @Schema(description = "处理回复")
    private String handlerReply;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间", example = "2024-12-22 17:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleTime;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间", example = "2024-12-22 17:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-12-22 17:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 