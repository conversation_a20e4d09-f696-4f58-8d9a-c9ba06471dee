import{_ as o}from"./BXnw39BI.js";import{e as a,c as n,a as e,f as r,b as l,w as c,o as d,d as i}from"./CURHyiUL.js";const m={class:"pt-16 min-h-screen bg-gray-50"},p={class:"max-w-screen-2xl mx-auto px-6 lg:px-8 py-8"},x={class:"text-center py-16"},h={__name:"my-resumes",setup(_){return a({title:"我的简历 - 火花简历",description:"管理您的简历文档"}),(u,t)=>{const s=o;return d(),n("div",m,[e("div",p,[t[2]||(t[2]=e("h1",{class:"text-3xl font-display font-bold text-secondary-900 mb-8"},"我的简历",-1)),e("div",x,[t[1]||(t[1]=r('<div class="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-6"><svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><h2 class="text-xl font-semibold text-secondary-900 mb-4">您还没有创建任何简历</h2><p class="text-secondary-600 mb-8">开始制作您的第一份专业简历吧！</p>',3)),l(s,{to:"/templates",class:"btn-primary"},{default:c(()=>t[0]||(t[0]=[i(" 选择模板开始制作 ")])),_:1,__:[0]})])])])}}};export{h as default};
