package com.alan6.resume.service;

import com.alan6.resume.dto.resume.ResumeExportRequest;

/**
 * PDF导出服务接口
 * 
 * @description 定义PDF导出相关的业务方法，支持Vue组件简历模板的PDF转换
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IPdfExportService {

    /**
     * 导出简历为PDF
     * 
     * @param request 导出请求参数
     * @return PDF文件的存储路径
     */
    String exportToPdf(ResumeExportRequest request);

    /**
     * 生成简历预览HTML
     * 
     * @param request 导出请求参数
     * @return 预览HTML内容
     */
    String generatePreviewHtml(ResumeExportRequest request);

    /**
     * 将HTML转换为PDF
     * 
     * @param htmlContent HTML内容
     * @param outputPath 输出路径
     * @param options 转换选项
     * @return 是否转换成功
     */
    boolean convertHtmlToPdf(String htmlContent, String outputPath, PdfExportOptions options);

    /**
     * 获取PDF导出配置
     * 
     * @param format 导出格式
     * @return 导出配置
     */
    PdfExportOptions getExportOptions(String format);

    /**
     * 验证PDF文件
     * 
     * @param filePath 文件路径
     * @return 是否有效
     */
    boolean validatePdfFile(String filePath);

    /**
     * PDF导出选项配置类
     */
    class PdfExportOptions {
        /**
         * 页面大小（A4, Letter等）
         */
        private String pageSize = "A4";
        
        /**
         * 页面方向（portrait, landscape）
         */
        private String orientation = "portrait";
        
        /**
         * 页面边距（毫米）
         */
        private PageMargins margins = new PageMargins();
        
        /**
         * 是否显示页眉
         */
        private boolean showHeader = false;
        
        /**
         * 是否显示页脚
         */
        private boolean showFooter = false;
        
        /**
         * 打印背景
         */
        private boolean printBackground = true;
        
        /**
         * 缩放比例
         */
        private double scale = 1.0;
        
        /**
         * 等待时间（毫秒）
         */
        private int waitTime = 3000;
        
        /**
         * 导出质量
         */
        private String quality = "high";

        // 构造函数
        public PdfExportOptions() {}

        // Getter和Setter方法
        public String getPageSize() { return pageSize; }
        public void setPageSize(String pageSize) { this.pageSize = pageSize; }

        public String getOrientation() { return orientation; }
        public void setOrientation(String orientation) { this.orientation = orientation; }

        public PageMargins getMargins() { return margins; }
        public void setMargins(PageMargins margins) { this.margins = margins; }

        public boolean isShowHeader() { return showHeader; }
        public void setShowHeader(boolean showHeader) { this.showHeader = showHeader; }

        public boolean isShowFooter() { return showFooter; }
        public void setShowFooter(boolean showFooter) { this.showFooter = showFooter; }

        public boolean isPrintBackground() { return printBackground; }
        public void setPrintBackground(boolean printBackground) { this.printBackground = printBackground; }

        public double getScale() { return scale; }
        public void setScale(double scale) { this.scale = scale; }

        public int getWaitTime() { return waitTime; }
        public void setWaitTime(int waitTime) { this.waitTime = waitTime; }

        public String getQuality() { return quality; }
        public void setQuality(String quality) { this.quality = quality; }
    }

    /**
     * 页面边距配置类
     */
    class PageMargins {
        /**
         * 上边距（毫米）
         */
        private int top = 20;
        
        /**
         * 下边距（毫米）
         */
        private int bottom = 20;
        
        /**
         * 左边距（毫米）
         */
        private int left = 20;
        
        /**
         * 右边距（毫米）
         */
        private int right = 20;

        // 构造函数
        public PageMargins() {}

        public PageMargins(int top, int bottom, int left, int right) {
            this.top = top;
            this.bottom = bottom;
            this.left = left;
            this.right = right;
        }

        // Getter和Setter方法
        public int getTop() { return top; }
        public void setTop(int top) { this.top = top; }

        public int getBottom() { return bottom; }
        public void setBottom(int bottom) { this.bottom = bottom; }

        public int getLeft() { return left; }
        public void setLeft(int left) { this.left = left; }

        public int getRight() { return right; }
        public void setRight(int right) { this.right = right; }
    }
} 