package com.alan6.resume.service.impl;

import com.alan6.resume.entity.RolePermissions;
import com.alan6.resume.mapper.RolePermissionsMapper;
import com.alan6.resume.service.IRolePermissionsService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色权限服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class RolePermissionsServiceImpl extends ServiceImpl<RolePermissionsMapper, RolePermissions> implements IRolePermissionsService {

    @Override
    @Transactional
    public boolean assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        // 先删除角色的所有权限
        removeAllPermissionsFromRole(roleId);
        
        // 添加新的权限关联
        if (permissionIds != null && !permissionIds.isEmpty()) {
            List<RolePermissions> rolePermissionsList = permissionIds.stream()
                    .map(permissionId -> {
                        RolePermissions rolePermission = new RolePermissions();
                        rolePermission.setRoleId(roleId);
                        rolePermission.setPermissionId(permissionId);
                        rolePermission.setCreateTime(LocalDateTime.now());
                        rolePermission.setUpdateTime(LocalDateTime.now());
                        return rolePermission;
                    })
                    .collect(Collectors.toList());
            
            return this.saveBatch(rolePermissionsList);
        }
        
        return true;
    }

    @Override
    @Transactional
    public boolean removeAllPermissionsFromRole(Long roleId) {
        QueryWrapper<RolePermissions> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", roleId);
        return this.remove(wrapper);
    }

    @Override
    public List<Long> getPermissionIdsByRoleId(Long roleId) {
        QueryWrapper<RolePermissions> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", roleId);
        wrapper.select("permission_id");
        
        return this.list(wrapper).stream()
                .map(RolePermissions::getPermissionId)
                .collect(Collectors.toList());
    }
} 