<template>
  <div class="password-login-form">
    <form @submit.prevent="handleSubmit" class="space-y-2">
      <!-- 手机号输入 -->
      <div class="form-group">
        <label class="form-label">手机号</label>
        <input 
          v-model="formData.phone"
          type="tel" 
          :class="getInputClass('phone')"
          placeholder="请输入手机号"
          @blur="validatePhone"
          @input="clearPhoneError"
          :disabled="loading"
          maxlength="11"
          autocomplete="tel"
        >
        <div class="error-message">
          {{ formErrors.phone }}
        </div>
      </div>
      
      <!-- 密码输入 -->
      <div class="form-group">
        <label class="form-label">密码</label>
        <div class="password-input-wrapper">
          <input 
            v-model="formData.password"
            :type="showPassword ? 'text' : 'password'"
            :class="getInputClass('password')"
            placeholder="请输入密码"
            @blur="validatePassword"
            @input="clearPasswordError"
            :disabled="loading"
            maxlength="20"
            autocomplete="current-password"
          >
          <button 
            type="button"
            @click="togglePasswordVisibility"
            class="password-toggle-btn"
            :disabled="loading"
            :aria-label="showPassword ? '隐藏密码' : '显示密码'"
          >
            <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.5 8.5M14.12 14.12L15.5 15.5"/>
            </svg>
            <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
          </button>
        </div>
        <div class="error-message">
          {{ formErrors.password }}
        </div>
      </div>
      
      <!-- 记住登录状态 -->
      <div class="remember-me-section">
        <label class="remember-me-label">
          <input 
            v-model="formData.rememberMe"
            type="checkbox" 
            class="remember-me-checkbox"
            :disabled="loading"
          >
          <span class="remember-me-text">记住登录状态</span>
        </label>
        
        <button 
          type="button" 
          class="forgot-password-link"
          @click="handleForgotPassword"
          :disabled="loading"
        >
          忘记密码？
        </button>
      </div>
      
      <!-- 提交按钮 -->
      <button 
        type="submit"
        :disabled="!canSubmit"
        :class="getSubmitButtonClass()"
      >
        <div v-if="loading" class="loading-spinner">
          <div class="spinner"></div>
          <span>登录中...</span>
        </div>
        <span v-else>登录</span>
      </button>
      
      <!-- 全局错误提示 -->
      <div v-if="globalError" class="global-error">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <span>{{ globalError }}</span>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { usePasswordLoginService } from '~/composables/auth/usePasswordLoginService'
import { $message } from '~/composables/shared/useGlobalMessage'

// ================================
// Props & Emits
// ================================
const props = defineProps({
  /**
   * 是否自动聚焦到第一个输入框
   */
  autoFocus: {
    type: Boolean,
    default: false
  },
  
  /**
   * 是否显示记住登录选项
   */
  showRememberMe: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits([
  'login-success',
  'login-error', 
  'forgot-password'
])

// ================================
// 服务和工具
// ================================
const passwordLoginService = usePasswordLoginService()

// ================================
// 响应式数据
// ================================
const formData = ref({
  phone: '',
  password: '',
  rememberMe: false
})

const formErrors = ref({
  phone: '',
  password: ''
})

const showPassword = ref(false)
const globalError = ref('')

// ================================
// 计算属性
// ================================
const loading = computed(() => passwordLoginService.loading.value)

const canSubmit = computed(() => {
  return formData.value.phone.trim() !== '' && 
         formData.value.password.trim() !== '' && 
         !loading.value
})

// ================================
// 表单验证方法
// ================================
const validatePhone = () => {
  const result = passwordLoginService.validateField('phone', formData.value.phone)
  formErrors.value.phone = result.message
  return result.isValid
}

const validatePassword = () => {
  const result = passwordLoginService.validateField('password', formData.value.password)
  formErrors.value.password = result.message
  return result.isValid
}

const validateForm = () => {
  const phoneValid = validatePhone()
  const passwordValid = validatePassword()
  return phoneValid && passwordValid
}

// ================================
// 错误处理方法
// ================================
const clearPhoneError = () => {
  formErrors.value.phone = ''
  clearGlobalError()
}

const clearPasswordError = () => {
  formErrors.value.password = ''
  clearGlobalError()
}

const clearGlobalError = () => {
  globalError.value = ''
  passwordLoginService.clearError()
}

const clearAllErrors = () => {
  formErrors.value = {
    phone: '',
    password: ''
  }
  clearGlobalError()
}

// ================================
// UI 交互方法
// ================================
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const getInputClass = (field) => {
  const baseClass = 'form-input'
  const errorClass = formErrors.value[field] ? 'form-input--error' : ''
  const disabledClass = loading.value ? 'form-input--disabled' : ''
  
  return [baseClass, errorClass, disabledClass].filter(Boolean).join(' ')
}

const getSubmitButtonClass = () => {
  const baseClass = 'submit-button'
  const disabledClass = !canSubmit.value ? 'submit-button--disabled' : ''
  const loadingClass = loading.value ? 'submit-button--loading' : ''
  
  return [baseClass, disabledClass, loadingClass].filter(Boolean).join(' ')
}

// ================================
// 业务逻辑方法
// ================================
const handleSubmit = async () => {
  try {
    clearAllErrors()
    
    // 表单验证
    if (!validateForm()) {
      return
    }
    
    // 执行登录
    const result = await passwordLoginService.passwordLogin({
      phone: formData.value.phone,
      password: formData.value.password
    })
    
    if (result.success) {
      // 显示成功消息
      $message.success('登录成功！欢迎回来')
      
      emit('login-success', {
        userInfo: result.data.userInfo,
        accessToken: result.data.accessToken,
        rememberMe: formData.value.rememberMe
      })
    } else {
      // 显示错误消息
      $message.error(result.error || '登录失败，请重试')
      emit('login-error', result.error)
    }
  } catch (error) {
    console.error('登录过程中发生错误:', error)
    // 显示错误消息
    $message.error('登录过程中发生错误，请重试')
    emit('login-error', error.message)
  }
}

const handleForgotPassword = () => {
  emit('forgot-password')
}

const resetForm = () => {
  formData.value = {
    phone: '',
    password: '',
    rememberMe: false
  }
  clearAllErrors()
  showPassword.value = false
}

// ================================
// 生命周期和监听
// ================================
watch(() => passwordLoginService.error.value, (newError) => {
  if (newError) {
    globalError.value = newError
  }
})

// 组件挂载后自动聚焦
onMounted(() => {
  if (props.autoFocus) {
    nextTick(() => {
      const phoneInput = document.querySelector('.password-login-form input[type="tel"]')
      if (phoneInput) {
        phoneInput.focus()
      }
    })
  }
})

// ================================
// 暴露给父组件的方法
// ================================
defineExpose({
  resetForm,
  validateForm,
  clearAllErrors
})
</script>

<style scoped>
/* ================================
   表单容器样式
   ================================ */
.password-login-form {
  @apply w-full max-w-md mx-auto;
}

/* ================================
   表单组件样式
   ================================ */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-secondary-700;
}

.form-input {
  @apply w-full px-4 py-3 border border-secondary-300 rounded-xl;
  @apply focus:ring-2 focus:ring-orange-500 focus:border-orange-500;
  @apply transition-all duration-200;
  @apply placeholder-secondary-400;
}

/* 强化橙色焦点样式，确保不被覆盖 */
.form-input:focus {
  --tw-ring-color: rgb(249 115 22) !important;
  border-color: rgb(249 115 22) !important;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
}

.form-input--error {
  @apply border-red-300 bg-red-50 focus:ring-red-500 focus:border-red-500;
}

/* 错误状态下保持红色焦点 */
.form-input--error:focus {
  --tw-ring-color: rgb(239 68 68) !important;
  border-color: rgb(239 68 68) !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.form-input--disabled {
  @apply bg-secondary-100 cursor-not-allowed opacity-60;
}

/* ================================
   密码输入框样式
   ================================ */
.password-input-wrapper {
  @apply relative;
}

.password-toggle-btn {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2;
  @apply text-secondary-400 hover:text-secondary-600;
  @apply transition-colors duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

/* ================================
   记住登录样式
   ================================ */
.remember-me-section {
  @apply flex items-center justify-between;
}

.remember-me-label {
  @apply flex items-center space-x-2 cursor-pointer;
}

.remember-me-checkbox {
  @apply w-4 h-4 text-orange-600 border-secondary-300 rounded;
  @apply focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* 强化复选框橙色样式 */
.remember-me-checkbox:focus {
  --tw-ring-color: rgb(249 115 22) !important;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
}

.remember-me-checkbox:checked {
  background-color: rgb(234 88 12) !important;
  border-color: rgb(234 88 12) !important;
}

.remember-me-text {
  @apply text-sm text-secondary-600;
}

.forgot-password-link {
  @apply text-sm text-primary-600 hover:text-primary-700;
  @apply transition-colors duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

/* ================================
   提交按钮样式
   ================================ */
.submit-button {
  @apply w-full;
  @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-xl;
  @apply bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500;
  @apply shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

/* 强化提交按钮橙色样式 */
.submit-button:not(.submit-button--disabled) {
  background-color: rgb(234 88 12) !important;
}

.submit-button:not(.submit-button--disabled):hover {
  background-color: rgb(194 65 12) !important;
}

.submit-button:not(.submit-button--disabled):focus {
  --tw-ring-color: rgb(249 115 22) !important;
  box-shadow: 0 0 0 2px white, 0 0 0 4px rgb(249 115 22) !important;
}

.submit-button--disabled {
  @apply opacity-50 cursor-not-allowed;
}

.submit-button--loading {
  @apply opacity-75;
}

.loading-spinner {
  @apply flex items-center space-x-2;
}

.spinner {
  @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* ================================
   错误提示样式
   ================================ */
.error-message {
  @apply text-sm text-red-600 h-5;
  @apply transition-all duration-200;
}

.global-error {
  @apply flex items-center space-x-2;
  @apply p-3 bg-red-50 border border-red-200 rounded-lg;
  @apply text-sm text-red-700;
}

/* ================================
   响应式样式
   ================================ */
@media (max-width: 640px) {
  .remember-me-section {
    @apply flex-col space-y-3 items-start;
  }
  
  .forgot-password-link {
    @apply self-end;
  }
}

/* ================================
   动画效果
   ================================ */
.form-input:focus {
  @apply scale-[1.02];
}

.submit-button:hover:not(:disabled) {
  @apply transform scale-105 shadow-lg;
}

.error-message:not(:empty) {
  @apply animate-pulse;
}

.global-error {
  @apply animate-fade-in;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}
</style> 