import{c as M,o as w,a as o,s as d,r as f,i as L,A as B,ae as V}from"./CURHyiUL.js";import{_ as S}from"./DlAUqK2U.js";const b={name:"RichTextEditor",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:"请输入内容..."},minHeight:{type:String,default:"100px"}},emits:["update:modelValue","focus","blur"],setup(r,{emit:t}){const a=f(null),e=f(""),m=f(!1);L(()=>{r.modelValue&&(e.value=r.modelValue,a.value.innerHTML=r.modelValue)}),B(()=>r.modelValue,l=>{l!==e.value&&(e.value=l||"",a.value&&(a.value.innerHTML=l||""))});const i=()=>{const l=a.value.innerHTML;e.value=l,t("update:modelValue",l)},n=()=>{m.value=!0,t("focus")},y=()=>{m.value=!1,t("blur")},p=l=>{l.preventDefault();const s=l.clipboardData.getData("text/plain");document.execCommand("insertText",!1,s),i()},x=l=>{if(l.key==="Tab"){l.preventDefault(),document.execCommand("insertText",!1,"	");return}if(l.ctrlKey||l.metaKey)switch(l.key){case"b":l.preventDefault(),c("bold");break;case"i":l.preventDefault(),c("italic");break;case"u":l.preventDefault(),c("underline");break;case"k":l.preventDefault(),g();break}},z=l=>{try{return document.queryCommandState(l)}catch{return!1}},k=l=>{try{return l==="left"?document.queryCommandState("justifyLeft"):l==="center"?document.queryCommandState("justifyCenter"):l==="right"?document.queryCommandState("justifyRight"):!1}catch{return!1}},c=l=>{document.execCommand(l,!1,null),a.value.focus(),i()},A=l=>{document.execCommand(l,!1,null),a.value.focus(),i()},F=l=>{let s="justifyLeft";l==="center"&&(s="justifyCenter"),l==="right"&&(s="justifyRight"),document.execCommand(s,!1,null),a.value.focus(),i()},g=()=>{if(!window.getSelection().toString()){alert("请先选择要添加链接的文本");return}const v=prompt("请输入链接地址:","https://");if(v&&v.trim()){let u=v.trim();!u.startsWith("http://")&&!u.startsWith("https://")&&!u.startsWith("mailto:")&&(u="https://"+u),document.execCommand("createLink",!1,u),a.value.focus(),i()}};return{editorContent:a,internalContent:e,isFocused:m,handleInput:i,handleFocus:n,handleBlur:y,handlePaste:p,handleKeydown:x,isFormatActive:z,isAlignActive:k,toggleFormat:c,toggleList:A,setAlignment:F,createLink:g,clearFormatting:()=>{document.execCommand("removeFormat",!1,null),a.value.focus(),i()}}}},h=()=>{V(r=>({"18dc2e88":r.minHeight}))},C=b.setup;b.setup=C?(r,t)=>(h(),C(r,t)):h;const H={class:"rich-text-editor"},T={class:"editor-toolbar"},D={class:"toolbar-group"},j={class:"toolbar-group"},I={class:"toolbar-group"},K={class:"toolbar-group"},R={class:"toolbar-group"},U=["placeholder"];function q(r,t,a,e,m,i){return w(),M("div",H,[o("div",T,[o("div",D,[o("button",{type:"button",class:d(["toolbar-btn",{active:e.isFormatActive("bold")}]),onClick:t[0]||(t[0]=n=>e.toggleFormat("bold")),title:"粗体 (Ctrl+B)"},t[15]||(t[15]=[o("strong",null,"B",-1)]),2),o("button",{type:"button",class:d(["toolbar-btn",{active:e.isFormatActive("italic")}]),onClick:t[1]||(t[1]=n=>e.toggleFormat("italic")),title:"斜体 (Ctrl+I)"},t[16]||(t[16]=[o("em",null,"I",-1)]),2),o("button",{type:"button",class:d(["toolbar-btn",{active:e.isFormatActive("underline")}]),onClick:t[2]||(t[2]=n=>e.toggleFormat("underline")),title:"下划线 (Ctrl+U)"},t[17]||(t[17]=[o("u",null,"U",-1)]),2)]),t[23]||(t[23]=o("div",{class:"toolbar-separator"},null,-1)),o("div",j,[o("button",{type:"button",class:"toolbar-btn",onClick:t[3]||(t[3]=(...n)=>e.createLink&&e.createLink(...n)),title:"超链接 (Ctrl+K)"}," 🔗 ")]),t[24]||(t[24]=o("div",{class:"toolbar-separator"},null,-1)),o("div",I,[o("button",{type:"button",class:d(["toolbar-btn",{active:e.isFormatActive("insertUnorderedList")}]),onClick:t[4]||(t[4]=n=>e.toggleList("insertUnorderedList")),title:"无序列表"},t[18]||(t[18]=[o("svg",{viewBox:"0 0 16 16",fill:"currentColor",class:"w-4 h-4"},[o("path",{d:"M2 4a1 1 0 100-2 1 1 0 000 2zM5 3h9v1H5V3zM2 8a1 1 0 100-2 1 1 0 000 2zM5 7h9v1H5V7zM2 12a1 1 0 100-2 1 1 0 000 2zM5 11h9v1H5v-1z"})],-1)]),2),o("button",{type:"button",class:d(["toolbar-btn",{active:e.isFormatActive("insertOrderedList")}]),onClick:t[5]||(t[5]=n=>e.toggleList("insertOrderedList")),title:"有序列表"},t[19]||(t[19]=[o("svg",{viewBox:"0 0 16 16",fill:"currentColor",class:"w-4 h-4"},[o("path",{d:"M2.003 2.5a.5.5 0 00-.723-.447l-1.003.5a.5.5 0 00.446.895l.28-.14V6H.5a.5.5 0 000 1h2.006a.5.5 0 100-1h-.503V2.5zM5 3.25a.75.75 0 01.75-.75h8.5a.75.75 0 010 1.5h-8.5A.75.75 0 015 3.25zM5 7.25a.75.75 0 01.75-.75h8.5a.75.75 0 010 1.5h-8.5A.75.75 0 015 7.25zM5 11.25a.75.75 0 01.75-.75h8.5a.75.75 0 010 1.5h-8.5a.75.75 0 01-.75-.75zM.924 10.32l.003-.004a.851.851 0 01.144-.153A.66.66 0 011.5 10c.195 0 .306.068.374.146a.57.57 0 01.128.376c0 .453-.269.682-.8 1.078l-.035.025C.692 11.98 0 12.495 0 13.5a.5.5 0 00.5.5h2.003a.5.5 0 000-1H1.146c.132-.197.351-.372.654-.597l.047-.035c.47-.35 1.156-.858 1.156-1.845 0-.365-.118-.744-.377-1.038-.268-.303-.658-.484-1.126-.484-.48 0-.84.202-1.068.392a1.858 1.858 0 00-.348.384l-.007.011-.002.004-.001.002-.001.001a.5.5 0 00.851.525zM.5 10.055l-.427-.26.427.26z"})],-1)]),2)]),t[25]||(t[25]=o("div",{class:"toolbar-separator"},null,-1)),o("div",K,[o("button",{type:"button",class:d(["toolbar-btn",{active:e.isAlignActive("left")}]),onClick:t[6]||(t[6]=n=>e.setAlignment("left")),title:"左对齐"},t[20]||(t[20]=[o("svg",{viewBox:"0 0 16 16",fill:"currentColor",class:"w-4 h-4"},[o("path",{d:"M2 12.5a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm0-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5zm0-3a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm0-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5z"})],-1)]),2),o("button",{type:"button",class:d(["toolbar-btn",{active:e.isAlignActive("center")}]),onClick:t[7]||(t[7]=n=>e.setAlignment("center")),title:"居中对齐"},t[21]||(t[21]=[o("svg",{viewBox:"0 0 16 16",fill:"currentColor",class:"w-4 h-4"},[o("path",{d:"M4 12.5a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm-2-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5zm2-3a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm-2-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5z"})],-1)]),2),o("button",{type:"button",class:d(["toolbar-btn",{active:e.isAlignActive("right")}]),onClick:t[8]||(t[8]=n=>e.setAlignment("right")),title:"右对齐"},t[22]||(t[22]=[o("svg",{viewBox:"0 0 16 16",fill:"currentColor",class:"w-4 h-4"},[o("path",{d:"M6 12.5a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm-4-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5zm4-3a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm-4-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5z"})],-1)]),2)]),t[26]||(t[26]=o("div",{class:"toolbar-separator"},null,-1)),o("div",R,[o("button",{type:"button",class:"toolbar-btn",onClick:t[9]||(t[9]=(...n)=>e.clearFormatting&&e.clearFormatting(...n)),title:"清除样式"}," 🗑 ")])]),o("div",{ref:"editorContent",class:"editor-content",contenteditable:"true",placeholder:a.placeholder,onInput:t[10]||(t[10]=(...n)=>e.handleInput&&e.handleInput(...n)),onBlur:t[11]||(t[11]=(...n)=>e.handleBlur&&e.handleBlur(...n)),onFocus:t[12]||(t[12]=(...n)=>e.handleFocus&&e.handleFocus(...n)),onKeydown:t[13]||(t[13]=(...n)=>e.handleKeydown&&e.handleKeydown(...n)),onPaste:t[14]||(t[14]=(...n)=>e.handlePaste&&e.handlePaste(...n))},null,40,U)])}const O=S(b,[["render",q],["__scopeId","data-v-a3caa800"]]);export{O as R};
