/**
 * 模板详情页面数据服务
 * 提供模板详情、相似模板、热门模板等数据管理功能
 */

import { ref, computed, readonly } from 'vue'
import { useTemplateService } from './useTemplateService'

export const useTemplateDetailService = () => {
  // ================================
  // 响应式数据
  // ================================
  const currentTemplate = ref(null)
  const similarTemplates = ref([])
  const hotTemplates = ref([])
  const loading = ref(false)
  const error = ref(null)

  // 使用模板服务
  const templateService = useTemplateService()

  // ================================
  // API 方法
  // ================================
  
  /**
   * 根据ID获取模板详情
   * @param {string|number} templateId - 模板ID
   * @returns {Promise<object>} 模板详情
   */
  const fetchTemplateDetail = async (templateId) => {
    loading.value = true
    error.value = null

    try {
      // 使用真实API接口
      const result = await templateService.fetchTemplateById(templateId)
      
      if (result.success) {
        currentTemplate.value = result.data
        return result.data
      } else {
        throw new Error(result.error || '获取模板详情失败')
      }
    } catch (err) {
      console.error('获取模板详情失败:', err)
      error.value = err.message || '获取模板详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取相似模板
   * @param {string|number} templateId - 当前模板ID
   * @param {number} limit - 返回数量限制
   * @returns {Promise<Array>} 相似模板列表
   */
  const fetchSimilarTemplates = async (templateId, limit = 12) => {
    try {
      // 获取当前模板信息用于相似度匹配
      const currentTemplateData = currentTemplate.value
      
      if (currentTemplateData) {
        // 根据当前模板的属性获取相似模板
        const params = {
          current: 1,
          size: limit,
          categoryId: currentTemplateData.categoryId,
          industry: currentTemplateData.industry,
          style: currentTemplateData.style,
          sortBy: 'popular'
        }
        
        // 调用模板列表接口获取相似模板
        const result = await templateService.fetchTemplates(params)
        
        if (result.success) {
          // 过滤掉当前模板
          const filteredTemplates = result.data.filter(template => 
            template.id !== parseInt(templateId)
          )
          
          similarTemplates.value = filteredTemplates.slice(0, limit)
          return similarTemplates.value
        }
      }
      
      // 如果获取失败或没有当前模板信息，返回空数组
      similarTemplates.value = []
      return []
    } catch (err) {
      console.error('获取相似模板失败:', err)
      similarTemplates.value = []
      return []
    }
  }

  /**
   * 获取热门模板
   * @param {number} limit - 返回数量限制
   * @returns {Promise<Array>} 热门模板列表
   */
  const fetchHotTemplates = async (limit = 12) => {
    try {
      // 获取热门模板（按使用次数排序）
      const params = {
        current: 1,
        size: limit,
        sortBy: 'popular'
      }
      
      const result = await templateService.fetchTemplates(params)
      
      if (result.success) {
        hotTemplates.value = result.data
        return hotTemplates.value
      }
      
      hotTemplates.value = []
      return []
    } catch (err) {
      console.error('获取热门模板失败:', err)
      hotTemplates.value = []
      return []
    }
  }

  /**
   * 记录模板使用
   * @param {string|number} templateId - 模板ID
   * @returns {Promise<void>}
   */
  const recordTemplateUsage = async (templateId) => {
    try {
      // 使用模板服务的记录使用方法
      await templateService.recordTemplateUsage(templateId)
      
      // 更新当前模板的使用次数（如果是当前模板）
      if (currentTemplate.value && currentTemplate.value.id == templateId) {
        currentTemplate.value.useCount = (currentTemplate.value.useCount || 0) + 1
      }
    } catch (err) {
      console.error('记录模板使用失败:', err)
    }
  }

  /**
   * 使用模板创建简历
   * @param {string|number} templateId - 模板ID
   * @param {Object} useRequest - 使用请求参数
   * @returns {Promise<Object>} 使用结果
   */
  const useTemplate = async (templateId, useRequest) => {
    try {
      const result = await templateService.useTemplate(templateId, useRequest)
      
      if (result.success) {
        // 记录使用成功，更新使用次数
        if (currentTemplate.value && currentTemplate.value.id == templateId) {
          currentTemplate.value.useCount = (currentTemplate.value.useCount || 0) + 1
        }
      }
      
      return result
    } catch (err) {
      console.error('使用模板失败:', err)
      throw err
    }
  }

  /**
   * 初始化页面数据
   * @param {string|number} templateId - 模板ID
   */
  const initializePageData = async (templateId) => {
    try {
      // 先获取模板详情
      await fetchTemplateDetail(templateId)
      
      // 然后并行获取相似模板和热门模板
      await Promise.all([
        fetchSimilarTemplates(templateId),
        fetchHotTemplates()
      ])
    } catch (err) {
      console.error('初始化页面数据失败:', err)
      throw err
    }
  }

  // ================================
  // 工具方法
  // ================================
  
  /**
   * 清空当前数据
   */
  const clearData = () => {
    currentTemplate.value = null
    similarTemplates.value = []
    hotTemplates.value = []
    error.value = null
  }

  /**
   * 格式化模板显示名称
   * @param {object} template - 模板对象
   * @returns {string} 格式化后的名称
   */
  const formatTemplateName = (template) => {
    if (!template) return ''
    
    const { name, industry, style } = template
    if (name) return name
    
    const industryText = industry || '通用'
    const styleText = style || '经典'
    return `${industryText}-${styleText}`
  }

  // ================================
  // 计算属性
  // ================================
  
  const templateDisplayName = computed(() => {
    return formatTemplateName(currentTemplate.value)
  })

  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)

  // ================================
  // 返回接口
  // ================================
  return {
    // 响应式数据
    currentTemplate: readonly(currentTemplate),
    similarTemplates: readonly(similarTemplates),
    hotTemplates: readonly(hotTemplates),
    loading: readonly(loading),
    error: readonly(error),
    
    // 计算属性
    templateDisplayName,
    isLoading,
    hasError,
    
    // API 方法
    fetchTemplateDetail,
    fetchSimilarTemplates,
    fetchHotTemplates,
    recordTemplateUsage,
    useTemplate,
    initializePageData,
    
    // 工具方法
    clearData,
    formatTemplateName
  }
} 