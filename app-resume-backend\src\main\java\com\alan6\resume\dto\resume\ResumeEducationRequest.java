package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 教育经历请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "教育经历请求")
public class ResumeEducationRequest {

    /**
     * 学校名称
     */
    @Schema(description = "学校名称", example = "北京大学", required = true)
    @NotBlank(message = "学校名称不能为空")
    @Size(min = 1, max = 100, message = "学校名称长度必须在1-100字符之间")
    private String schoolName;

    /**
     * 专业
     */
    @Schema(description = "专业", example = "计算机科学与技术", required = true)
    @NotBlank(message = "专业不能为空")
    @Size(min = 1, max = 100, message = "专业长度必须在1-100字符之间")
    private String major;

    /**
     * 学历
     */
    @Schema(description = "学历", example = "本科", required = true)
    @NotBlank(message = "学历不能为空")
    @Size(max = 50, message = "学历长度不能超过50字符")
    private String degree;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2013-09-01", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2017-06-30")
    private LocalDate endDate;

    /**
     * 是否在读
     */
    @Schema(description = "是否在读", example = "0", required = true)
    @NotNull(message = "是否在读不能为空")
    private Byte isCurrent;

    /**
     * GPA
     */
    @Schema(description = "GPA", example = "3.8")
    @Size(max = 20, message = "GPA长度不能超过20字符")
    private String gpa;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "主修课程包括数据结构、算法、软件工程等，获得优秀毕业生称号")
    private String description;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重", example = "1", required = true)
    @NotNull(message = "排序权重不能为空")
    private Integer sortOrder;
} 