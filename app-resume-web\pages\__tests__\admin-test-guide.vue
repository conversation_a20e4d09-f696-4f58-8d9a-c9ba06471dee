<template>
  <div class="admin-test-guide">
    <h1>管理后台测试指南</h1>
    
    <div class="guide-section">
      <h2>🔐 第一步：管理员登录</h2>
      <p>要测试模板转换功能，首先需要以管理员身份登录。</p>
      
      <div class="steps">
        <div class="step">
          <h3>方法1：使用快速登录</h3>
          <ol>
            <li>访问 <a href="/admin/login" target="_blank">/admin/login</a></li>
            <li>点击页面下方的"管理员"或"超级管理员"按钮</li>
            <li>系统会自动填入测试账号并登录</li>
          </ol>
        </div>
        
        <div class="step">
          <h3>方法2：手动输入账号</h3>
          <ol>
            <li>访问 <a href="/admin/login" target="_blank">/admin/login</a></li>
            <li>输入手机号：<code>13800138001</code>（管理员）或 <code>13800138002</code>（超级管理员）</li>
            <li>输入密码：<code>123456</code></li>
            <li>点击"登录"按钮</li>
          </ol>
        </div>
      </div>
    </div>
    
    <div class="guide-section">
      <h2>📋 第二步：访问模板转换功能</h2>
      <p>登录成功后，按以下步骤访问模板转换功能：</p>
      
      <div class="steps">
        <div class="step">
          <h3>导航方式</h3>
          <ol>
            <li>登录后会自动跳转到管理后台首页</li>
            <li>在左侧菜单中找到"模板管理"</li>
            <li>点击"模板管理"展开子菜单</li>
            <li>点击"模板转换"进入转换页面</li>
          </ol>
        </div>
        
        <div class="step">
          <h3>直接访问</h3>
          <p>也可以直接访问：<a href="/admin/template/converter" target="_blank">/admin/template/converter</a></p>
          <p class="note">注意：必须先登录管理员账号，否则会被重定向到登录页面</p>
        </div>
      </div>
    </div>
    
    <div class="guide-section">
      <h2>🔧 第三步：测试模板转换功能</h2>
      <p>进入模板转换页面后，可以按以下步骤测试：</p>
      
      <div class="steps">
        <div class="step">
          <h3>功能测试</h3>
          <ol>
            <li>上传HTML文件（支持拖拽上传）</li>
            <li>查看文件验证结果</li>
            <li>配置转换选项</li>
            <li>执行转换并查看结果</li>
          </ol>
        </div>
      </div>
    </div>
    
    <div class="guide-section">
      <h2>❓ 常见问题</h2>
      
      <div class="faq">
        <div class="faq-item">
          <h3>Q: 点击菜单没有反应？</h3>
          <p>A: 请确保已经登录管理员账号。如果没有登录，会被认证中间件拦截。</p>
        </div>
        
        <div class="faq-item">
          <h3>Q: 看不到"模板转换"菜单？</h3>
          <p>A: 请先点击"模板管理"展开子菜单，模板转换是子菜单项。</p>
        </div>
        
        <div class="faq-item">
          <h3>Q: 登录后还是无法访问？</h3>
          <p>A: 请检查后端服务是否正常运行，确保端口9311可以访问。</p>
        </div>
      </div>
    </div>
    
    <div class="guide-section">
      <h2>🚀 快速测试链接</h2>
      <div class="quick-links">
        <a href="/admin/login" class="btn primary">管理员登录</a>
        <a href="/admin" class="btn secondary">管理后台首页</a>
        <a href="/admin/template/converter" class="btn secondary">模板转换页面</a>
      </div>
    </div>
    
    <div class="guide-section">
      <h2>🔍 当前状态检查</h2>
      <div class="status-check">
        <div class="status-item">
          <span class="label">当前路由：</span>
          <span class="value">{{ $route.path }}</span>
        </div>
        <div class="status-item">
          <span class="label">认证状态：</span>
          <span class="value">{{ authStatus }}</span>
        </div>
        <div class="status-item">
          <span class="label">管理员权限：</span>
          <span class="value">{{ adminStatus }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const authStatus = ref('检查中...')
const adminStatus = ref('检查中...')

onMounted(() => {
  // 检查认证状态
  try {
    const authService = useAuthService()
    authStatus.value = authService.isLoggedIn.value ? '已登录' : '未登录'
  } catch (error) {
    authStatus.value = '检查失败'
  }
  
  // 检查管理员状态
  try {
    const adminStore = useAdminStore()
    adminStatus.value = adminStore.adminInfo ? '有管理员权限' : '无管理员权限'
  } catch (error) {
    adminStatus.value = '检查失败'
  }
})
</script>

<style scoped>
.admin-test-guide {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  color: #1f2937;
  margin-bottom: 30px;
  font-size: 2.5rem;
  font-weight: 700;
}

.guide-section {
  margin-bottom: 40px;
  padding: 25px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: #f9fafb;
}

.guide-section h2 {
  color: #374151;
  margin-bottom: 15px;
  font-size: 1.5rem;
  font-weight: 600;
}

.steps {
  margin-top: 20px;
}

.step {
  margin-bottom: 25px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.step h3 {
  color: #1f2937;
  margin-bottom: 10px;
  font-size: 1.1rem;
  font-weight: 600;
}

.step ol, .step ul {
  margin: 10px 0;
  padding-left: 20px;
}

.step li {
  margin: 8px 0;
  line-height: 1.6;
}

code {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #dc2626;
}

.note {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 10px;
  margin: 10px 0;
  color: #92400e;
  font-size: 0.9rem;
}

.faq {
  margin-top: 20px;
}

.faq-item {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.faq-item h3 {
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 1rem;
  font-weight: 600;
}

.faq-item p {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.quick-links {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-block;
}

.btn.primary {
  background: #3b82f6;
  color: white;
}

.btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn.secondary {
  background: #6b7280;
  color: white;
}

.btn.secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.status-check {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
}

.status-item {
  display: flex;
  margin-bottom: 10px;
}

.status-item .label {
  font-weight: 600;
  color: #374151;
  width: 120px;
}

.status-item .value {
  color: #6b7280;
}

a {
  color: #3b82f6;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

p {
  line-height: 1.6;
  color: #4b5563;
}
</style> 