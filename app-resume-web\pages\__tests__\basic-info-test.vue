<template>
  <div class="test-page">
    <h1>基本信息表单测试</h1>
    <div class="test-container">
      <div class="form-section">
        <BasicInfoForm :data="formData" @update="handleUpdate" />
      </div>
      <div class="debug-section">
        <h3>调试信息</h3>
        <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BasicInfoForm from '~/components/editor/sidebar/forms/BasicInfoForm.vue'

const formData = ref({
  name: '测试用户',
  phone: '13800138000',
  email: '<EMAIL>'
})

const handleUpdate = (data) => {
  console.log('收到更新:', data)
  formData.value = { ...data }
}
</script>

<style scoped>
.test-page {
  @apply p-6;
}

.test-container {
  @apply grid grid-cols-2 gap-6;
}

.form-section {
  @apply bg-white border border-gray-200 rounded-lg;
}

.debug-section {
  @apply bg-gray-50 p-4 rounded-lg;
}

.debug-section h3 {
  @apply text-lg font-medium mb-4;
}

.debug-section pre {
  @apply text-sm bg-white p-3 rounded border overflow-auto;
}
</style> 