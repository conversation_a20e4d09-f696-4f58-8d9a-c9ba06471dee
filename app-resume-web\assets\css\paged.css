/**
 * CSS分页样式
 * @description 使用CSS实现真实的页面分割显示效果
 * <AUTHOR>
 * @since 1.0.0
 */

/* ================================
 * CSS分页容器样式
 * ================================ */

/* CSS分页容器 */
.css-paged-container {
  position: relative;
  background: #f3f4f6;
  padding: 30px;
  min-height: 100vh;
  
  /* 创建页面效果 */
  background-image: 
    /* 页面阴影效果 */
    linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.1) 2%, rgba(0,0,0,0.1) 98%, transparent 100%),
    /* 页面分割线 */
    repeating-linear-gradient(
      to bottom,
      transparent 0px,
      transparent 1120px,
      #dc2626 1120px,
      #dc2626 1124px,
      transparent 1124px,
      transparent 1154px
    );
  background-size: 100% 100%, 100% 1154px;
  background-repeat: no-repeat, repeat-y;
}

/* 创建页面容器效果 */
.css-paged-container::before {
  content: '';
  position: absolute;
  top: 30px;
  left: 30px;
  right: 30px;
  bottom: 30px;
  background: 
    repeating-linear-gradient(
      to bottom,
      white 0px,
      white 1122px,
      #f3f4f6 1122px,
      #f3f4f6 1154px
    );
  box-shadow: 
    0 0 0 1px rgba(0,0,0,0.1),
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  z-index: 0;
}

/* 内容区域 */
.css-paged-container > * {
  position: relative;
  z-index: 1;
  margin: 0 auto;
  max-width: 21cm; /* A4宽度 */
  background: white;
  
  /* 分页效果 */
  background-image: 
    repeating-linear-gradient(
      to bottom,
      transparent 0px,
      transparent 1122px,
      rgba(220, 38, 38, 0.1) 1122px,
      rgba(220, 38, 38, 0.1) 1124px,
      transparent 1124px,
      transparent 1154px
    );
  
  /* 页面内边距 */
  padding: 2cm;
  box-sizing: border-box;
  
  /* 分页断点 */
  background-size: 100% 1154px;
  background-repeat: repeat-y;
}

/* 分页指示器 */
.page-break-indicator {
  position: absolute;
  width: calc(100% - 60px);
  left: 30px;
  height: 32px;
  pointer-events: none;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-break-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 100px;
  height: 3px;
  background: linear-gradient(
    90deg,
    #dc2626 0%,
    #ef4444 50%,
    #dc2626 100%
  );
  box-shadow: 
    0 2px 4px rgba(220, 38, 38, 0.3),
    0 0 8px rgba(220, 38, 38, 0.2);
  border-radius: 2px;
  transform: translateY(-50%);
}

.page-number-indicator {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  box-shadow: 
    0 4px 6px rgba(220, 38, 38, 0.3),
    0 0 12px rgba(220, 38, 38, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  
  /* 添加页面图标 */
  display: flex;
  align-items: center;
  gap: 6px;
}

.page-number-indicator::before {
  content: '📄';
  font-size: 14px;
}

/* ================================
 * 页面内容优化
 * ================================ */

/* 每个简历模块作为分页单元 */
.css-paged-container .resume-section {
  break-inside: avoid;
  page-break-inside: avoid;
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
}

.css-paged-container .resume-section:hover {
  background: rgba(255, 255, 255, 0.95);
  border-left-color: #3b82f6;
  transform: translateX(2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.css-paged-container .resume-section:last-child {
  margin-bottom: 60px; /* 最后一个模块额外间距 */
}

/* 简历头部特殊样式 */
.css-paged-container .resume-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(99, 102, 241, 0.05));
  border-left-color: #3b82f6;
  margin-bottom: 32px;
}

/* 标题避免在页面底部 */
.css-paged-container h1,
.css-paged-container h2,
.css-paged-container h3 {
  break-after: avoid;
  page-break-after: avoid;
  color: #1f2937;
}

/* 列表项目避免分页 */
.css-paged-container .skill-item,
.css-paged-container .experience-item,
.css-paged-container .education-item,
.css-paged-container .project-item {
  break-inside: avoid;
  page-break-inside: avoid;
}

/* ================================
 * 分页模式指示器
 * ================================ */

.css-paged-container::after {
  content: '📄 分页预览模式 - A4 页面';
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #1f2937, #374151);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 13px;
  font-weight: 600;
  box-shadow: 
    0 8px 16px rgba(31, 41, 55, 0.3),
    0 0 20px rgba(31, 41, 55, 0.2);
  z-index: 50;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { 
    transform: scale(1); 
    opacity: 0.9;
  }
  50% { 
    transform: scale(1.02); 
    opacity: 1;
  }
}

/* ================================
 * 页面尺寸控制
 * ================================ */

/* A4页面尺寸 */
.css-paged-container {
  max-width: 29.7cm; /* A4宽度 + 边距 */
  margin: 0 auto;
}

/* 强制分页效果 */
.css-paged-container {
  /* 每1154px创建一个页面间隔 */
  background-attachment: local;
}

/* ================================
 * 响应式适配
 * ================================ */

@media screen and (max-width: 1200px) {
  .css-paged-container {
    max-width: 95%;
    transform: scale(0.9);
    transform-origin: top center;
    margin-bottom: 50px;
  }
  
  .css-paged-container::after {
    font-size: 11px;
    padding: 8px 16px;
    top: 15px;
    right: 15px;
  }
}

@media screen and (max-width: 900px) {
  .css-paged-container {
    transform: scale(0.8);
    margin-bottom: 80px;
  }
  
  .page-number-indicator {
    font-size: 10px;
    padding: 6px 12px;
  }
  
  .page-break-line {
    height: 2px;
  }
}

@media screen and (max-width: 768px) {
  .css-paged-container {
    transform: scale(0.7);
    margin-bottom: 100px;
    padding: 20px;
  }
  
  .css-paged-container::before {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
  }
  
  .page-break-indicator {
    width: calc(100% - 40px);
    left: 20px;
  }
  
  .css-paged-container > * {
    padding: 1.5cm;
  }
}

/* ================================
 * 打印样式优化
 * ================================ */

@media print {
  .css-paged-container {
    background: white !important;
    box-shadow: none !important;
    transform: none !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .css-paged-container::before,
  .css-paged-container::after {
    display: none !important;
  }
  
  .page-break-indicator {
    page-break-before: always;
    visibility: hidden;
    height: 0;
    margin: 0;
    padding: 0;
  }
  
  .page-break-line,
  .page-number-indicator {
    display: none !important;
  }
  
  .css-paged-container > * {
    background: white !important;
    padding: 2cm !important;
    margin: 0 !important;
  }
  
  /* 确保在正确位置分页 */
  .css-paged-container .resume-section {
    break-inside: avoid;
    page-break-inside: avoid;
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
  }
}

/* ================================
 * 动画和过渡效果
 * ================================ */

.css-paged-container {
  transition: all 0.4s ease;
}

.page-break-indicator {
  animation: slideInFromRight 0.6s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.page-number-indicator {
  animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: translateY(-50%) scale(0.3);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
  70% {
    transform: translateY(-50%) scale(0.9);
  }
  100% {
    transform: translateY(-50%) scale(1);
  }
}

/* ================================
 * 特殊内容处理
 * ================================ */

/* 图片处理 */
.css-paged-container img {
  max-width: 100%;
  height: auto;
  break-inside: avoid;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格处理 */
.css-paged-container table {
  break-inside: avoid;
  width: 100%;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 引用和代码块 */
.css-paged-container blockquote,
.css-paged-container pre {
  break-inside: avoid;
  page-break-inside: avoid;
  background: rgba(243, 244, 246, 0.8);
  border-radius: 6px;
  padding: 16px;
}

/* ================================
 * 页面边界强化效果
 * ================================ */

/* 在每个页面分界处添加更明显的视觉效果 */
.css-paged-container {
  /* 页面阴影叠加 */
  box-shadow: 
    inset 0 0 0 1px rgba(59, 130, 246, 0.1),
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* 页面分割强化 */
.css-paged-container > * {
  /* 在内容区域添加页面分割线 */
  position: relative;
}

.css-paged-container > *::before {
  content: '';
  position: absolute;
  left: -2cm;
  right: -2cm;
  height: 100%;
  top: 0;
  background: 
    repeating-linear-gradient(
      to bottom,
      transparent 0px,
      transparent 1120px,
      rgba(239, 68, 68, 0.15) 1120px,
      rgba(239, 68, 68, 0.15) 1124px,
      transparent 1124px,
      transparent 1154px
    );
  background-size: 100% 1154px;
  background-repeat: repeat-y;
  pointer-events: none;
  z-index: -1;
}

/* ================================
 * 调试模式 (开发时可启用)
 * ================================ */

/* 
.css-paged-container.debug {
  background-image: 
    linear-gradient(to bottom, transparent 1121px, red 1121px, red 1123px, transparent 1123px),
    linear-gradient(to bottom, transparent 2243px, red 2243px, red 2245px, transparent 2245px);
  background-repeat: repeat-y;
}
*/ 