package com.alan6.resume.controller;

import com.alan6.resume.common.utils.BusinessLogUtils;
import com.alan6.resume.core.R;
import com.alan6.resume.dto.payment.*;
import com.alan6.resume.service.IPaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;

/**
 * <p>
 * 支付 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/payment")
@Tag(name = "支付管理", description = "支付相关接口")
public class PaymentController {

    @Autowired
    private IPaymentService paymentService;

    /**
     * 获取支付方式列表
     */
    @GetMapping("/methods")
    @Operation(summary = "获取支付方式列表", description = "获取系统支持的所有支付方式")
    public R<PaymentMethodResponse> getPaymentMethods() {
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "guest", "获取支付方式列表");
        
        try {
            PaymentMethodResponse response = paymentService.getPaymentMethods();
            BusinessLogUtils.logSuccess("获取支付方式列表", "methodsCount", response.getMethods().size());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    @Operation(summary = "创建支付订单", description = "为现有订单创建支付信息")
    public R<PaymentCreateResponse> createPayment(
            @Parameter(description = "创建支付订单请求", required = true) @Valid @RequestBody PaymentCreateRequest request,
            HttpServletRequest httpRequest) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "system", "创建支付订单");
        
        try {
            // 如果客户端IP为空，从请求中获取
            if (request.getClientIp() == null || request.getClientIp().trim().isEmpty()) {
                String clientIp = getClientIpAddress(httpRequest);
                request.setClientIp(clientIp);
            }
            
            PaymentCreateResponse response = paymentService.createPayment(request);
            
            BusinessLogUtils.logSuccess("创建支付订单", "orderNo", request.getOrderNo(), 
                                      "paymentMethod", request.getPaymentMethod(), 
                                      "paymentId", response.getPaymentId());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 查询支付状态
     */
    @GetMapping("/status/{orderNo}")
    @Operation(summary = "查询支付状态", description = "根据订单号查询支付状态")
    public R<PaymentStatusResponse> getPaymentStatus(
            @Parameter(description = "订单号", required = true) @PathVariable String orderNo) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "system", "查询支付状态");
        
        try {
            PaymentStatusResponse response = paymentService.getPaymentStatus(orderNo);
            BusinessLogUtils.logSuccess("查询支付状态", "orderNo", orderNo, "paymentStatus", response.getPaymentStatus());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 微信支付回调
     */
    @PostMapping("/callback/wechat")
    @Operation(summary = "微信支付回调", description = "接收微信支付平台的支付结果通知")
    public String handleWechatCallback(
            @RequestBody String callbackData,
            @RequestHeader(value = "Wechatpay-Signature", required = false) String signature,
            @RequestHeader(value = "Wechatpay-Timestamp", required = false) String timestamp,
            @RequestHeader(value = "Wechatpay-Nonce", required = false) String nonce,
            @RequestHeader(value = "Wechatpay-Serial", required = false) String serialNo) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "wechat", "微信支付回调");
        
        try {
            Boolean result = paymentService.handleWechatCallback(callbackData, signature, timestamp, nonce, serialNo);
            
            BusinessLogUtils.logSuccess("微信支付回调处理", "result", result);
            
            // 微信支付要求返回特定格式
            if (result) {
                return "{\"code\":\"SUCCESS\",\"message\":\"成功\"}";
            } else {
                return "{\"code\":\"FAIL\",\"message\":\"失败\"}";
            }
        } catch (Exception e) {
            log.error("微信支付回调处理失败", e);
            BusinessLogUtils.logError("微信支付回调处理", e);
            return "{\"code\":\"FAIL\",\"message\":\"处理失败\"}";
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 支付宝支付回调
     */
    @PostMapping("/callback/alipay")
    @Operation(summary = "支付宝支付回调", description = "接收支付宝支付平台的支付结果通知")
    public String handleAlipayCallback(@RequestParam Map<String, String> params) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "alipay", "支付宝支付回调");
        
        try {
            Boolean result = paymentService.handleAlipayCallback(params);
            
            BusinessLogUtils.logSuccess("支付宝支付回调处理", "result", result, "orderNo", params.get("out_trade_no"));
            
            // 支付宝要求返回success字符串
            return result ? "success" : "fail";
        } catch (Exception e) {
            log.error("支付宝支付回调处理失败", e);
            BusinessLogUtils.logError("支付宝支付回调处理", e);
            return "fail";
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 申请退款
     */
    @PostMapping("/refund/{orderNo}")
    @Operation(summary = "申请退款", description = "为指定订单申请退款")
    public R<RefundResponse> applyRefund(
            @Parameter(description = "订单号", required = true) @PathVariable String orderNo,
            @Parameter(description = "退款申请请求", required = true) @Valid @RequestBody RefundRequest request) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "system", "申请退款");
        
        try {
            RefundResponse response = paymentService.applyRefund(orderNo, request);
            
            BusinessLogUtils.logSuccess("申请退款", "orderNo", orderNo, 
                                      "refundAmount", request.getRefundAmount(), 
                                      "refundId", response.getRefundId());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 查询退款状态
     */
    @GetMapping("/refund/status/{refundId}")
    @Operation(summary = "查询退款状态", description = "根据退款ID查询退款处理状态")
    public R<RefundStatusResponse> getRefundStatus(
            @Parameter(description = "退款ID", required = true) @PathVariable String refundId) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "system", "查询退款状态");
        
        try {
            RefundStatusResponse response = paymentService.getRefundStatus(refundId);
            BusinessLogUtils.logSuccess("查询退款状态", "refundId", refundId, "refundStatus", response.getRefundStatus());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 验证支付签名
     */
    @PostMapping("/verify-signature")
    @Operation(summary = "验证支付签名", description = "验证第三方支付平台的回调签名")
    public R<SignatureVerifyResponse> verifySignature(
            @Parameter(description = "签名验证请求", required = true) @Valid @RequestBody SignatureVerifyRequest request) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "system", "验证支付签名");
        
        try {
            SignatureVerifyResponse response = paymentService.verifySignature(
                request.getPaymentMethod(), request.getSignature(), request.getTimestamp(),
                request.getNonce(), request.getBody(), request.getSerialNo());
            
            BusinessLogUtils.logSuccess("验证支付签名", "paymentMethod", request.getPaymentMethod(), 
                                      "valid", response.getValid());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    // ========== 私有方法 ==========

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xip = request.getHeader("X-Real-IP");
        String xfor = request.getHeader("X-Forwarded-For");
        
        if (xfor != null && !xfor.isEmpty() && !"unknown".equalsIgnoreCase(xfor)) {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            int index = xfor.indexOf(",");
            if (index != -1) {
                return xfor.substring(0, index);
            } else {
                return xfor;
            }
        }
        
        xfor = xip;
        if (xfor != null && !xfor.isEmpty() && !"unknown".equalsIgnoreCase(xfor)) {
            return xfor;
        }
        
        if (xfor == null || xfor.isEmpty() || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getHeader("Proxy-Client-IP");
        }
        if (xfor == null || xfor.isEmpty() || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getHeader("WL-Proxy-Client-IP");
        }
        if (xfor == null || xfor.isEmpty() || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getHeader("HTTP_CLIENT_IP");
        }
        if (xfor == null || xfor.isEmpty() || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (xfor == null || xfor.isEmpty() || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getRemoteAddr();
        }
        
        return xfor;
    }
}
