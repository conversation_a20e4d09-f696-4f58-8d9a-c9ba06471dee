package com.alan6.resume.common.exception;

import com.alan6.resume.core.R;
import com.alan6.resume.common.utils.BusinessLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;

/**
 * 全局异常处理器
 * 
 * 主要功能：
 * 1. 统一处理系统异常，返回标准格式响应
 * 2. 记录详细的异常日志信息
 * 3. 区分不同类型异常，提供友好的错误提示
 * 4. 集成结构化日志记录
 * 
 * 异常处理策略：
 * - 参数校验异常：返回400，提示具体校验失败信息
 * - 业务异常：返回400，提示业务错误信息
 * - 限流异常：返回429，提示请求过于频繁
 * - 系统异常：返回500，记录详细日志但对用户隐藏技术细节
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理参数校验异常（@RequestBody参数校验失败）
     * 
     * 当使用@Valid注解的请求体参数校验失败时触发
     * 
     * @param request HTTP请求对象
     * @param ex 方法参数验证异常
     * @return 标准错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleMethodArgumentNotValidException(HttpServletRequest request, 
                                                        MethodArgumentNotValidException ex) {
        // 获取第一个校验失败的字段错误信息
        FieldError fieldError = ex.getBindingResult().getFieldError();
        String errorMessage = fieldError != null ? fieldError.getDefaultMessage() : "参数校验失败";
        
        // 记录参数校验失败日志
        BusinessLogUtils.logWarning("参数校验失败", 
            "uri", request.getRequestURI(),
            "method", request.getMethod(),
            "field", fieldError != null ? fieldError.getField() : "unknown",
            "rejectedValue", fieldError != null ? fieldError.getRejectedValue() : "unknown",
            "errorMessage", errorMessage);
        
        return R.fail(400, "参数校验失败：" + errorMessage);
    }

    /**
     * 处理参数绑定异常（@ModelAttribute参数校验失败）
     * 
     * 当表单数据绑定并校验失败时触发
     * 
     * @param request HTTP请求对象
     * @param ex 绑定异常
     * @return 标准错误响应
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleBindException(HttpServletRequest request, BindException ex) {
        // 获取所有校验失败的字段信息
        List<FieldError> fieldErrors = ex.getFieldErrors();
        StringBuilder errorMessage = new StringBuilder();
        
        for (int i = 0; i < fieldErrors.size() && i < 3; i++) { // 最多显示3个错误
            FieldError fieldError = fieldErrors.get(i);
            if (i > 0) {
                errorMessage.append("；");
            }
            errorMessage.append(fieldError.getDefaultMessage());
        }
        
        // 记录绑定异常日志
        BusinessLogUtils.logWarning("参数绑定失败", 
            "uri", request.getRequestURI(),
            "method", request.getMethod(),
            "errorCount", fieldErrors.size(),
            "errors", errorMessage.toString());
        
        return R.fail(400, "参数错误：" + errorMessage.toString());
    }

    /**
     * 处理约束违规异常（@RequestParam参数校验失败）
     * 
     * 当使用@Valid注解的请求参数校验失败时触发
     * 
     * @param request HTTP请求对象
     * @param ex 约束违规异常
     * @return 标准错误响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleConstraintViolationException(HttpServletRequest request, 
                                                     ConstraintViolationException ex) {
        // 获取违规约束信息
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        StringBuilder errorMessage = new StringBuilder();
        
        int count = 0;
        for (ConstraintViolation<?> violation : violations) {
            if (count > 0) {
                errorMessage.append("；");
            }
            errorMessage.append(violation.getMessage());
            count++;
            if (count >= 3) break; // 最多显示3个错误
        }
        
        // 记录约束违规日志
        BusinessLogUtils.logWarning("参数约束违规", 
            "uri", request.getRequestURI(),
            "method", request.getMethod(),
            "violationCount", violations.size(),
            "violations", errorMessage.toString());
        
        return R.fail(400, "参数格式错误：" + errorMessage.toString());
    }

    /**
     * 处理业务异常
     * 
     * 业务逻辑中主动抛出的异常，通常包含用户友好的错误信息
     * 
     * @param request HTTP请求对象
     * @param ex 业务异常
     * @return 标准错误响应
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleBusinessException(HttpServletRequest request, BusinessException ex) {
        // 记录业务异常日志
        BusinessLogUtils.logWarning("业务异常", 
            "uri", request.getRequestURI(),
            "method", request.getMethod(),
            "errorCode", ex.getErrorCode(),
            "errorMessage", ex.getMessage());
        
        return R.fail(ex.getErrorCode(), ex.getMessage());
    }

    /**
     * 处理限流异常
     * 
     * 当请求触发限流规则时抛出的异常
     * 
     * @param request HTTP请求对象
     * @param ex 限流异常
     * @return 标准错误响应
     */
    @ExceptionHandler(RateLimitException.class)
    @ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
    public R<Void> handleRateLimitException(HttpServletRequest request, RateLimitException ex) {
        // 记录限流日志
        BusinessLogUtils.logWarning("触发限流", 
            "uri", request.getRequestURI(),
            "method", request.getMethod(),
            "clientIp", getClientIpAddress(request),
            "rateLimitKey", ex.getRateLimitKey(),
            "message", ex.getMessage());
        
        return R.fail(429, ex.getMessage());
    }

    /**
     * 处理运行时异常
     * 
     * 捕获未被其他处理器处理的运行时异常
     * 
     * @param request HTTP请求对象
     * @param ex 运行时异常
     * @return 标准错误响应
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Void> handleRuntimeException(HttpServletRequest request, RuntimeException ex) {
        // 记录运行时异常详细日志
        BusinessLogUtils.logError("运行时异常", ex,
            "uri", request.getRequestURI(),
            "method", request.getMethod(),
            "clientIp", getClientIpAddress(request),
            "userAgent", request.getHeader("User-Agent"),
            "exceptionType", ex.getClass().getSimpleName());
        
        // 对用户返回通用错误信息，避免泄露技术细节
        return R.fail(500, "系统繁忙，请稍后重试");
    }

    /**
     * 处理其他未捕获的异常
     * 
     * 作为最后的异常兜底处理器
     * 
     * @param request HTTP请求对象
     * @param ex 异常
     * @return 标准错误响应
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Void> handleException(HttpServletRequest request, Exception ex) {
        // 记录未知异常详细日志
        BusinessLogUtils.logError("未知异常", ex,
            "uri", request.getRequestURI(),
            "method", request.getMethod(),
            "clientIp", getClientIpAddress(request),
            "userAgent", request.getHeader("User-Agent"),
            "exceptionType", ex.getClass().getSimpleName());
        
        // 对用户返回通用错误信息
        return R.fail(500, "系统异常，请联系管理员");
    }

    /**
     * 获取客户端真实IP地址
     * 
     * 考虑代理服务器和负载均衡器的情况
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // 检查是否通过代理
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况（取第一个非unknown的IP）
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }
        
        return ipAddress != null ? ipAddress : "unknown";
    }
} 