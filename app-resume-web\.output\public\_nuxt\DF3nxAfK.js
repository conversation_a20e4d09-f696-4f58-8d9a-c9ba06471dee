import{r as v,l as O,k as m,P as z}from"./CURHyiUL.js";v(!1);v(null);class V{constructor(){this.container=null,this.init()}init(){this.container=document.createElement("div"),this.container.id="message-container",this.container.style.cssText=`
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        pointer-events: none;
      `,document.body.appendChild(this.container)}show(c,o="info",r=3e3){const l=document.createElement("div");l.style.cssText=`
      background: ${this.getBackgroundColor(o)};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      margin-bottom: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-20px) scale(0.9);
      opacity: 0;
      transition: all 0.3s ease;
      pointer-events: auto;
      max-width: 300px;
      word-wrap: break-word;
    `,l.textContent=c,this.container.appendChild(l),setTimeout(()=>{l.style.transform="translateY(0) scale(1)",l.style.opacity="1"},10),setTimeout(()=>{l.style.transform="translateY(-20px) scale(0.9)",l.style.opacity="0",setTimeout(()=>{l.parentNode&&l.parentNode.removeChild(l)},300)},r)}getBackgroundColor(c){const o={success:"#10b981",error:"#ef4444",warning:"#f59e0b",info:"#3b82f6"};return o[c]||o.info}success(c,o){this.show(c,"success",o)}error(c,o){this.show(c,"error",o)}warning(c,o){this.show(c,"warning",o)}info(c,o){this.show(c,"info",o)}}let f=null;const A=()=>(f||(f=new V),f),U=()=>{const n=A(),c=()=>localStorage.getItem("admin_token")||localStorage.getItem("token"),o=()=>{const t=c();return t?{Authorization:`Bearer ${t}`}:{}},r=O({uploading:!1,uploadProgress:0,uploadedFiles:[],validating:!1,validationResults:[],converting:!1,conversionResults:null,taskId:null,taskDirectory:null,statistics:{totalFiles:0,validFiles:0,invalidFiles:0,totalErrors:0,totalWarnings:0,validationRate:0}}),l=m(()=>r.uploadedFiles.length>0),p=m(()=>r.validationResults.length>0),w=m(()=>r.conversionResults!==null),y=m(()=>r.uploading||r.validating||r.converting),T=async t=>{console.log("开始上传HTML文件，数量:",t.length);try{r.uploading=!0,r.uploadProgress=0;const s=new FormData;Array.from(t).forEach(a=>{s.append("files",a)});const e=await $fetch("/admin/html-converter/upload",{method:"POST",body:s,headers:o(),onUploadProgress:a=>{r.uploadProgress=Math.round(a.loaded/a.total*100)}});if(e.code===200)return r.taskId=e.data.taskId,r.uploadedFiles=e.data.uploadedFiles,r.validationResults=e.data.validationResults,r.statistics=e.data.statistics,n.success(`成功上传 ${r.uploadedFiles.length} 个文件`),{success:!0,data:e.data};throw new Error(e.msg||"上传失败")}catch(s){return console.error("上传HTML文件失败:",s),n.error("上传失败: "+s.message),{success:!1,error:s.message}}finally{r.uploading=!1,r.uploadProgress=0}},$=async(t,s)=>{console.log("开始验证HTML文件:",s);try{r.validating=!0;const e=await $fetch("/admin/html-converter/validate",{method:"POST",headers:o(),body:{taskId:t,fileNames:s}});if(e.code===200){r.validationResults=e.data,M(e.data);const a=e.data.filter(u=>u.valid).length,d=e.data.length-a;return d>0?n.warning(`验证完成：${a} 个通过，${d} 个失败`):n.success(`验证完成：全部 ${a} 个文件通过`),{success:!0,data:e.data}}else throw new Error(e.msg||"验证失败")}catch(e){return console.error("验证HTML文件失败:",e),n.error("验证失败: "+e.message),{success:!1,error:e.message}}finally{r.validating=!1}},E=async t=>{console.log("开始转换HTML文件:",t);try{r.converting=!0;const s=await $fetch("/admin/html-converter/convert",{method:"POST",headers:o(),body:t});if(s.code===200){r.conversionResults=s.data;const e=s.data.statistics;return e.failedCount>0?n.warning(`转换完成：${e.successCount} 个成功，${e.failedCount} 个失败`):n.success(`转换完成：全部 ${e.successCount} 个文件成功`),{success:!0,data:s.data}}else throw new Error(s.msg||"转换失败")}catch(s){return console.error("转换HTML文件失败:",s),n.error("转换失败: "+s.message),{success:!1,error:s.message}}finally{r.converting=!1}},C=async(t,s)=>{console.log("开始转换单个文件:",s);try{r.converting=!0;const e=await $fetch("/admin/html-converter/convert/single",{method:"POST",headers:o(),body:{taskId:t,fileName:s}});if(e.code===200)return r.conversionResults=e.data,n.success(`文件 ${s} 转换成功`),{success:!0,data:e.data};throw new Error(e.msg||"转换失败")}catch(e){return console.error("转换单个文件失败:",e),n.error("转换失败: "+e.message),{success:!1,error:e.message}}finally{r.converting=!1}},F=async(t,s)=>{console.log("开始批量转换文件:",s);try{r.converting=!0;const e=await $fetch("/admin/html-converter/convert/batch",{method:"POST",headers:o(),body:{taskId:t,fileNames:s}});if(e.code===200){r.conversionResults=e.data;const a=e.data.statistics;return n.success(`批量转换完成：${a.successCount} 个成功，${a.failedCount} 个失败`),{success:!0,data:e.data}}else throw new Error(e.msg||"转换失败")}catch(e){return console.error("批量转换文件失败:",e),n.error("转换失败: "+e.message),{success:!1,error:e.message}}finally{r.converting=!1}},b=async t=>{console.log("获取转换结果:",t);try{const s=await $fetch(`/admin/html-converter/result/${t}`,{method:"GET",headers:o()});if(s.code===200)return r.taskDirectory=s.data.taskDirectory,{success:!0,data:s.data};throw new Error(s.msg||"获取结果失败")}catch(s){return console.error("获取转换结果失败:",s),n.error("获取结果失败: "+s.message),{success:!1,error:s.message}}},R=async()=>{try{const t=await $fetch("/admin/html-converter/modules",{method:"GET",headers:o()});if(t.code===200)return{success:!0,data:t.data};throw new Error(t.msg||"获取失败")}catch(t){return console.error("获取支持的模块ID失败:",t),{success:!1,error:t.message}}},k=async()=>{try{const t=await $fetch("/admin/html-converter/fields",{method:"GET",headers:o()});if(t.code===200)return{success:!0,data:t.data};throw new Error(t.msg||"获取失败")}catch(t){return console.error("获取支持的字段名称失败:",t),{success:!1,error:t.message}}},x=async(t,s)=>{console.log("生成验证报告:",s);try{const e=await $fetch("/admin/html-converter/report",{method:"POST",headers:o(),body:{taskId:t,fileNames:s}});if(e.code===200)return{success:!0,data:e.data};throw new Error(e.msg||"生成报告失败")}catch(e){return console.error("生成验证报告失败:",e),n.error("生成报告失败: "+e.message),{success:!1,error:e.message}}},P=async t=>{console.log("开始下载转换结果:",t);try{console.log("1. 测试接口连通性...");const s=await $fetch(`/admin/html-converter/test-download/${t}`,{method:"GET",headers:o()});if(console.log("测试接口响应:",s),s.code!==200)throw new Error("测试接口调用失败");console.log("2. 开始下载文件...");const e=await fetch(`/admin/html-converter/result/${t}/download`,{method:"GET",headers:{Authorization:`Bearer ${c()}`}});if(console.log("下载响应状态:",e.status,e.statusText),console.log("响应头:",Object.fromEntries(e.headers.entries())),!e.ok)throw new Error(`下载失败: ${e.status} ${e.statusText}`);const a=e.headers.get("Content-Disposition");let d=`conversion_results_${t}.zip`;if(a){console.log("Content-Disposition:",a);const g=a.match(/filename="([^"]+)"/);g&&g[1]&&(d=g[1])}console.log("3. 处理文件下载, 文件名:",d);const u=await e.blob();if(console.log("Blob大小:",u.size,"bytes"),u.size===0)throw new Error("下载的文件为空");const h=window.URL.createObjectURL(u),i=document.createElement("a");return i.href=h,i.download=d,i.style.display="none",document.body.appendChild(i),console.log("4. 触发下载..."),i.click(),setTimeout(()=>{document.body.removeChild(i),window.URL.revokeObjectURL(h),console.log("5. 清理完成")},100),setTimeout(()=>{console.log("6. 检查下载状态..."),n.info("如果没有自动下载，请检查浏览器的下载设置或弹窗拦截")},2e3),n.success(`转换结果下载完成: ${d}`),{success:!0,fileName:d,fileSize:u.size}}catch(s){return console.error("下载转换结果失败:",s),n.error("下载失败: "+s.message),{success:!1,error:s.message}}},S=async t=>{console.log("清理任务:",t);try{const s=await $fetch(`/admin/html-converter/task/${t}`,{method:"DELETE",headers:o()});if(s.code===200)return n.success("任务清理成功"),{success:!0,data:s.data};throw new Error(s.msg||"清理失败")}catch(s){return console.error("清理任务失败:",s),n.error("清理失败: "+s.message),{success:!1,error:s.message}}},H=()=>{r.uploading=!1,r.uploadProgress=0,r.uploadedFiles=[],r.validating=!1,r.validationResults=[],r.converting=!1,r.conversionResults=null,r.taskId=null,r.taskDirectory=null,r.statistics={totalFiles:0,validFiles:0,invalidFiles:0,totalErrors:0,totalWarnings:0,validationRate:0}},M=t=>{const s=t.length,e=t.filter(i=>i.valid).length,a=s-e,d=t.reduce((i,g)=>i+(g.errorCount||0),0),u=t.reduce((i,g)=>i+(g.warningCount||0),0),h=s>0?e/s*100:0;r.statistics={totalFiles:s,validFiles:e,invalidFiles:a,totalErrors:d,totalWarnings:u,validationRate:Math.round(h*100)/100}},L=(t,s,e={},a=null)=>({taskId:a||r.taskId,conversionType:t,htmlFiles:s,options:{validateHtml:e.validateHtml!==!1,generateFullComponent:e.generateFullComponent!==!1,preserveComments:e.preserveComments||!1,formatOutput:e.formatOutput!==!1,generateTypes:e.generateTypes||!1,targetVueVersion:e.targetVueVersion||"vue3",cssProcessing:e.cssProcessing||"scoped",strictMode:e.strictMode||!1,...e}}),D=t=>{const s=[],e=[];return Array.from(t).forEach(a=>{a.name.toLowerCase().endsWith(".html")?s.push(a):e.push(a)}),{valid:e.length===0,validFiles:s,invalidFiles:e,message:e.length>0?`发现 ${e.length} 个非HTML文件，将被忽略`:"所有文件都是有效的HTML文件"}};return{state:z(r),hasUploadedFiles:l,hasValidationResults:p,hasConversionResults:w,isProcessing:y,uploadHtmlFiles:T,validateHtmlFiles:$,convertHtmlFiles:E,convertSingleFile:C,convertBatchFiles:F,getConversionResult:b,getSupportedModules:R,getSupportedFields:k,generateValidationReport:x,downloadResults:P,cleanupTask:S,resetState:H,createConversionRequest:L,validateFileTypes:D}};export{A as a,U as u};
