<template>
  <div>
    <EditorLayout :resume-id="resumeId" />
  </div>
</template>

<script setup>
/**
 * 编辑指定简历页面
 * @description 根据简历ID加载并编辑指定的简历
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import EditorLayout from '~/components/editor/layout/EditorLayout.vue'

// ================================
// 页面配置
// ================================

// 不使用默认布局
definePageMeta({
  layout: false
})

// ================================
// 路由参数
// ================================
const route = useRoute()
const resumeId = computed(() => route.params.id)

// ================================
// SEO配置
// ================================
useSeoMeta({
  title: '编辑简历 - 火花简历',
  description: '编辑您的简历，实时预览编辑效果',
  keywords: '简历编辑,在线编辑,简历修改,火花简历'
})

// ================================
// 生命周期
// ================================
onMounted(() => {
  console.log(`编辑简历页面已加载，简历ID: ${resumeId.value}`)
})

// ================================
// 监听路由变化
// ================================
watch(resumeId, (newId) => {
  if (newId) {
    console.log(`切换到编辑简历: ${newId}`)
    // 这里可以添加切换简历的逻辑
  }
})
</script> 