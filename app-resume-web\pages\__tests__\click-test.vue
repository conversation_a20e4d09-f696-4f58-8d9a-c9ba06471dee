<template>
  <div class="test-page">
    <h1>按钮点击测试</h1>
    
    <div class="test-section">
      <h2>基本按钮测试</h2>
      <button @click="handleClick('basic')">基本按钮</button>
      <button @click="handleClick('styled')" class="styled-btn">样式按钮</button>
    </div>
    
    <div class="test-section">
      <h2>选项按钮测试</h2>
      <div class="field-options">
        <button
          v-for="option in options"
          :key="option.key"
          type="button"
          class="option-btn"
          :class="{ 
            active: activeFields.includes(option.key) && option.key !== 'custom',
            'custom-btn': option.key === 'custom'
          }"
          @click="handleOptionClick(option.key)"
        >
          <span class="option-icon">{{ getOptionIcon(option.key) }}</span>
          {{ option.label }}
        </button>
      </div>
    </div>
    
    <div class="test-section">
      <h2>调试信息</h2>
      <pre>{{ JSON.stringify({ activeFields, clickCount }, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const clickCount = ref(0)
const activeFields = ref([])

const options = [
  { key: 'wechat', label: '微信号' },
  { key: 'website', label: '个人网站' },
  { key: 'github', label: 'GitHub' },
  { key: 'custom', label: '自定义' }
]

const handleClick = (type) => {
  clickCount.value++
  console.log('按钮点击:', type, '点击次数:', clickCount.value)
}

const handleOptionClick = (field) => {
  console.log('选项按钮点击:', field)
  if (field === 'custom') {
    console.log('自定义按钮点击')
  } else {
    toggleField(field)
  }
}

const toggleField = (field) => {
  const index = activeFields.value.indexOf(field)
  if (index > -1) {
    activeFields.value.splice(index, 1)
    console.log('移除字段:', field)
  } else {
    activeFields.value.push(field)
    console.log('添加字段:', field)
  }
}

const getOptionIcon = (field) => {
  if (field === 'custom') {
    return '+'
  }
  return activeFields.value.includes(field) ? '✓' : '+'
}
</script>

<style scoped>
.test-page {
  @apply p-6 max-w-4xl mx-auto;
}

.test-section {
  @apply mb-8 p-4 border border-gray-200 rounded-lg;
}

.test-section h2 {
  @apply text-lg font-medium mb-4;
}

.styled-btn {
  @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600;
}

.field-options {
  @apply flex flex-wrap gap-2;
}

.option-btn {
  @apply px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 transition-colors duration-200 flex items-center gap-1;
}

.option-btn.active {
  @apply bg-blue-100 border-blue-300 text-blue-700;
}

.option-btn.custom-btn {
  @apply bg-gray-50 border-gray-300 text-gray-700;
}

.option-btn.custom-btn:hover {
  @apply bg-gray-100;
}

.option-icon {
  @apply text-xs font-bold;
}
</style> 