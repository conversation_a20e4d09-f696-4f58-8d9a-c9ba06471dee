<template>
  <div class="py-8">
    <div class="max-w-screen-2xl mx-auto px-6 lg:px-8">
      <!-- 模板网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <TemplateCard
          v-for="template in displayTemplates"
          :key="template.id"
          :template="template"
          @preview="handlePreview"
          @use-template="handleUseTemplate"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="displayTemplates.length === 0" class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-secondary-100 rounded-full flex items-center justify-center">
          <svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-secondary-900 mb-2">暂无相关模板</h3>
        <p class="text-secondary-600">请尝试其他搜索条件或联系客服获取更多模板</p>
      </div>

      <!-- 分页组件 -->
      <TemplatePagination
        v-if="totalPages > 1"
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-count="totalCount"
        @page-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'
import TemplateCard from './TemplateCard.vue'
import TemplatePagination from './TemplatePagination.vue'

// Props
const props = defineProps({
  templates: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pageSize: {
    type: Number,
    default: 20
  }
})

// Emits
const emit = defineEmits(['preview', 'use-template', 'page-change'])

// Reactive Data
const currentPage = ref(1)

// Computed Properties
const totalCount = computed(() => props.templates.length)
const totalPages = computed(() => Math.ceil(totalCount.value / props.pageSize))

const displayTemplates = computed(() => {
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return props.templates.slice(start, end)
})

// Methods
const handlePreview = (template) => {
  emit('preview', template)
}

const handleUseTemplate = (template) => {
  emit('use-template', template)
}

const handlePageChange = (page) => {
  currentPage.value = page
  emit('page-change', page)
  
  // 滚动到顶部
  scrollToTop()
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 监听模板列表变化，重置页码
watch(() => props.templates, () => {
  currentPage.value = 1
}, { deep: true })
</script> 