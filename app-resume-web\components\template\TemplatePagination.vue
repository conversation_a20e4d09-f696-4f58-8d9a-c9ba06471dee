<template>
  <div class="flex flex-col sm:flex-row justify-between items-center py-6 border-t border-secondary-100">
    <!-- 统计信息 -->
    <div class="text-sm text-secondary-600 mb-4 sm:mb-0">
      共 <span class="font-medium text-secondary-900">{{ totalCount }}</span> 个模板，
      当前第 <span class="font-medium text-secondary-900">{{ currentPage }}</span> 页，
      共 <span class="font-medium text-secondary-900">{{ totalPages }}</span> 页
    </div>

    <!-- 分页控件 -->
    <div class="flex items-center space-x-2">
      <!-- 上一页 -->
      <button
        @click="handlePageChange(currentPage - 1)"
        :disabled="currentPage <= 1"
        :class="[
          'inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
          currentPage <= 1
            ? 'text-secondary-400 cursor-not-allowed'
            : 'text-secondary-700 hover:text-primary-600 hover:bg-primary-50'
        ]"
      >
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        上一页
      </button>

      <!-- 页码列表 -->
      <div class="flex items-center space-x-1">
        <!-- 第一页 -->
        <button
          v-if="showFirstPage"
          @click="handlePageChange(1)"
          :class="pageButtonClass(1)"
        >
          1
        </button>

        <!-- 左省略号 -->
        <span v-if="showLeftEllipsis" class="px-3 py-2 text-secondary-400">...</span>

        <!-- 中间页码 -->
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="handlePageChange(page)"
          :class="pageButtonClass(page)"
        >
          {{ page }}
        </button>

        <!-- 右省略号 -->
        <span v-if="showRightEllipsis" class="px-3 py-2 text-secondary-400">...</span>

        <!-- 最后一页 -->
        <button
          v-if="showLastPage"
          @click="handlePageChange(totalPages)"
          :class="pageButtonClass(totalPages)"
        >
          {{ totalPages }}
        </button>
      </div>

      <!-- 下一页 -->
      <button
        @click="handlePageChange(currentPage + 1)"
        :disabled="currentPage >= totalPages"
        :class="[
          'inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
          currentPage >= totalPages
            ? 'text-secondary-400 cursor-not-allowed'
            : 'text-secondary-700 hover:text-primary-600 hover:bg-primary-50'
        ]"
      >
        下一页
        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'

// Props
const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  totalCount: {
    type: Number,
    required: true
  },
  maxVisiblePages: {
    type: Number,
    default: 5
  }
})

// Emits
const emit = defineEmits(['page-change'])

// 计算可见页码范围
const visiblePages = computed(() => {
  const { currentPage, totalPages, maxVisiblePages } = props
  
  if (totalPages <= maxVisiblePages) {
    return Array.from({ length: totalPages }, (_, i) => i + 1)
  }
  
  const half = Math.floor(maxVisiblePages / 2)
  let start = Math.max(currentPage - half, 1)
  let end = Math.min(start + maxVisiblePages - 1, totalPages)
  
  if (end - start + 1 < maxVisiblePages) {
    start = Math.max(end - maxVisiblePages + 1, 1)
  }
  
  return Array.from({ length: end - start + 1 }, (_, i) => start + i)
})

// 是否显示第一页
const showFirstPage = computed(() => {
  return !visiblePages.value.includes(1)
})

// 是否显示最后一页
const showLastPage = computed(() => {
  return !visiblePages.value.includes(props.totalPages)
})

// 是否显示左省略号
const showLeftEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value[0] > 2
})

// 是否显示右省略号
const showRightEllipsis = computed(() => {
  return showLastPage.value && visiblePages.value[visiblePages.value.length - 1] < props.totalPages - 1
})

// 页码按钮样式
const pageButtonClass = (page) => {
  const isActive = page === props.currentPage
  return [
    'inline-flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg transition-all duration-200',
    isActive
      ? 'bg-primary-600 text-white shadow-lg'
      : 'text-secondary-700 hover:text-primary-600 hover:bg-primary-50'
  ]
}

// Methods
const handlePageChange = (page) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-change', page)
  }
}
</script> 