package com.alan6.resume.service.impl;

import com.alan6.resume.document.ResumeData;
import com.alan6.resume.repository.ResumeDataRepository;
import com.alan6.resume.service.IResumeDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 简历数据服务实现类（MongoDB）
 * 
 * @description 实现简历数据在MongoDB中的操作
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeDataServiceImpl implements IResumeDataService {

    /**
     * MongoDB仓库
     */
    private final ResumeDataRepository resumeDataRepository;

    /**
     * MongoDB模板
     */
    private final MongoTemplate mongoTemplate;

    /**
     * 保存简历数据
     */
    @Override
    public String saveResumeData(ResumeData resumeData) {
        log.info("保存简历数据到MongoDB，简历ID：{}", resumeData.getResumeId());
        
        try {
            // 检查是否已存在
            Optional<ResumeData> existing = resumeDataRepository.findByResumeId(resumeData.getResumeId());
            
            if (existing.isPresent()) {
                // 更新现有文档
                ResumeData existingData = existing.get();
                resumeData.setId(existingData.getId());
                resumeData.setCreateTime(existingData.getCreateTime());
                resumeData.setVersion(existingData.getVersion() + 1);
            }
            
            resumeData.setUpdateTime(LocalDateTime.now());
            resumeData.setLastSaved(LocalDateTime.now());
            
            ResumeData saved = resumeDataRepository.save(resumeData);
            log.info("简历数据保存成功，文档ID：{}", saved.getId());
            
            return saved.getId();
            
        } catch (Exception e) {
            log.error("保存简历数据失败", e);
            throw new RuntimeException("保存简历数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取简历数据
     */
    @Override
    public ResumeData getResumeData(String documentId) {
        log.debug("获取简历数据，文档ID：{}", documentId);
        
        try {
            Optional<ResumeData> data = resumeDataRepository.findById(documentId);
            return data.orElse(null);
        } catch (Exception e) {
            log.error("获取简历数据失败，文档ID：{}", documentId, e);
            return null;
        }
    }

    /**
     * 根据简历ID获取数据
     */
    @Override
    public ResumeData getResumeDataByResumeId(String resumeId) {
        log.debug("根据简历ID获取数据，简历ID：{}", resumeId);
        
        try {
            Optional<ResumeData> data = resumeDataRepository.findByResumeId(resumeId);
            return data.orElse(null);
        } catch (Exception e) {
            log.error("根据简历ID获取数据失败，简历ID：{}", resumeId, e);
            return null;
        }
    }

    /**
     * 更新简历数据
     */
    @Override
    public boolean updateResumeData(String documentId, ResumeData resumeData) {
        log.info("更新简历数据，文档ID：{}", documentId);
        
        try {
            resumeData.setId(documentId);
            resumeData.setUpdateTime(LocalDateTime.now());
            resumeData.setLastSaved(LocalDateTime.now());
            
            resumeDataRepository.save(resumeData);
            log.info("简历数据更新成功");
            return true;
            
        } catch (Exception e) {
            log.error("更新简历数据失败，文档ID：{}", documentId, e);
            return false;
        }
    }

    /**
     * 删除简历数据
     */
    @Override
    public boolean deleteResumeData(String documentId) {
        log.info("删除简历数据，文档ID：{}", documentId);
        
        try {
            resumeDataRepository.deleteById(documentId);
            log.info("简历数据删除成功");
            return true;
        } catch (Exception e) {
            log.error("删除简历数据失败，文档ID：{}", documentId, e);
            return false;
        }
    }

    /**
     * 增量更新模块数据
     */
    @Override
    public boolean updateModuleData(String documentId, String modulePath, Object moduleData) {
        log.info("增量更新模块数据，文档ID：{}，模块路径：{}", documentId, modulePath);
        
        try {
            Query query = new Query(Criteria.where("_id").is(documentId));
            Update update = new Update()
                    .set(modulePath, moduleData)
                    .set("updateTime", LocalDateTime.now())
                    .set("lastSaved", LocalDateTime.now())
                    .inc("version", 1);
            
            mongoTemplate.updateFirst(query, update, ResumeData.class);
            log.info("模块数据增量更新成功");
            return true;
            
        } catch (Exception e) {
            log.error("增量更新模块数据失败", e);
            return false;
        }
    }

    /**
     * 更新全局设置
     */
    @Override
    public boolean updateGlobalSettings(String documentId, ResumeData.GlobalSettings globalSettings) {
        log.info("更新全局设置，文档ID：{}", documentId);
        
        try {
            Query query = new Query(Criteria.where("_id").is(documentId));
            Update update = new Update()
                    .set("globalSettings", globalSettings)
                    .set("updateTime", LocalDateTime.now())
                    .set("lastSaved", LocalDateTime.now())
                    .inc("version", 1);
            
            mongoTemplate.updateFirst(query, update, ResumeData.class);
            log.info("全局设置更新成功");
            return true;
            
        } catch (Exception e) {
            log.error("更新全局设置失败", e);
            return false;
        }
    }
} 