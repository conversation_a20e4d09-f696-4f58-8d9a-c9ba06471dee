<template>
  <div class="pt-16">
    <!-- Header -->
    <section class="bg-gradient-to-br from-primary-50 to-orange-50 py-16">
      <div class="container-width section-padding">
        <div class="text-center max-w-3xl mx-auto">
          <h1 class="font-display font-bold text-4xl lg:text-5xl text-secondary-900 mb-6">
            简历攻略
          </h1>
          <p class="text-xl text-secondary-600">
            专业HR和职场专家分享的简历制作技巧，助你求职成功
          </p>
        </div>
      </div>
    </section>

    <!-- Content -->
    <section class="py-20">
      <div class="container-width section-padding">
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="guide in guides" :key="guide.id" class="card-hover p-6">
            <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
            </div>
            <h3 class="font-display font-semibold text-xl text-secondary-900 mb-3">{{ guide.title }}</h3>
            <p class="text-secondary-600 mb-4">{{ guide.description }}</p>
            <div class="text-sm text-secondary-500">{{ guide.readTime }} 分钟阅读</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
useSeoMeta({
  title: '简历攻略 - 火花简历',
  description: '专业的简历制作指南和求职技巧'
})

const guides = ref([
  {
    id: 1,
    title: '简历基础结构',
    description: '了解简历的基本组成部分和最佳布局方式',
    readTime: 5
  },
  {
    id: 2,
    title: '工作经历描述',
    description: '如何用STAR法则描述工作经历，突出个人成就',
    readTime: 8
  },
  {
    id: 3,
    title: '技能关键词',
    description: '掌握ATS系统识别的关键词，提高简历通过率',
    readTime: 6
  },
  {
    id: 4,
    title: '行业简历差异',
    description: '不同行业简历的特点和注意事项',
    readTime: 10
  },
  {
    id: 5,
    title: '简历常见错误',
    description: '避免这些常见错误，让HR眼前一亮',
    readTime: 7
  },
  {
    id: 6,
    title: '面试准备',
    description: '从简历到面试的完整准备指南',
    readTime: 12
  }
])
</script> 