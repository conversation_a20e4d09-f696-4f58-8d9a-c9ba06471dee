/**
 * 消息提示插件
 * @description 提供全局消息提示功能
 */

class MessageService {
  constructor() {
    this.container = null
    this.init()
  }

  init() {
    if (process.client) {
      this.container = document.createElement('div')
      this.container.id = 'message-container'
      this.container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        pointer-events: none;
      `
      document.body.appendChild(this.container)
    }
  }

  show(message, type = 'info', duration = 3000) {
    if (!process.client) return

    const messageEl = document.createElement('div')
    messageEl.style.cssText = `
      background: ${this.getBackgroundColor(type)};
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      pointer-events: auto;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `
    messageEl.textContent = message
    
    this.container.appendChild(messageEl)
    
    // 触发动画
    setTimeout(() => {
      messageEl.style.transform = 'translateX(0)'
    }, 10)
    
    // 自动移除
    setTimeout(() => {
      messageEl.style.transform = 'translateX(100%)'
      setTimeout(() => {
        if (messageEl.parentNode) {
          messageEl.parentNode.removeChild(messageEl)
        }
      }, 300)
    }, duration)
  }

  getBackgroundColor(type) {
    const colors = {
      success: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      info: '#3b82f6'
    }
    return colors[type] || colors.info
  }

  success(message, duration) {
    this.show(message, 'success', duration)
  }

  error(message, duration) {
    this.show(message, 'error', duration)
  }

  warning(message, duration) {
    this.show(message, 'warning', duration)
  }

  info(message, duration) {
    this.show(message, 'info', duration)
  }
}

// Nuxt 3 插件导出
export default defineNuxtPlugin((nuxtApp) => {
  const messageService = new MessageService()
  
  // 正确的 Nuxt 3 provide 方式
  return {
    provide: {
      message: messageService
    }
  }
}) 