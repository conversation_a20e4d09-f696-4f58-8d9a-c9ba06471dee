<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alan6.resume.mapper.ResumeCategoriesMapper">

    <!-- 根据分类ID获取关联的模板数量 -->
    <select id="getTemplateCountByCategoryId" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT rtc.template_id) 
        FROM resume_template_categories rtc
        INNER JOIN resume_templates rt ON rtc.template_id = rt.id
        WHERE rtc.category_id = #{categoryId}
          AND rtc.is_deleted = 0 
          AND rt.is_deleted = 0
          AND rt.status = 1
    </select>

    <!-- 获取分类树（包含模板数量） -->
    <select id="getCategoryTreeWithCount" resultType="com.alan6.resume.entity.ResumeCategories">
        SELECT 
            rc.*,
            (SELECT COUNT(DISTINCT rtc.template_id) 
             FROM resume_template_categories rtc
             INNER JOIN resume_templates rt ON rtc.template_id = rt.id
             WHERE rtc.category_id = rc.id
               AND rtc.is_deleted = 0 
               AND rt.is_deleted = 0
               AND rt.status = 1
            ) as template_count
        FROM resume_categories rc
        WHERE rc.is_deleted = 0 
          AND rc.status = 1
        ORDER BY rc.sort_order ASC, rc.id ASC
    </select>

</mapper> 