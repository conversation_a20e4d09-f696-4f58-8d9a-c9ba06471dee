import{r as m,k as S,i as Ce,c as l,f as te,a as s,q as v,m as J,p as _,v as F,s as h,t as i,F as x,g as T,H as we,d as oe,o as t,y as Se}from"./CURHyiUL.js";import{u as Fe}from"./BpemGWS8.js";import{_ as Ie}from"./DlAUqK2U.js";const Ne={class:"template-upload-page"},je={class:"upload-container"},xe={class:"form-section"},Te={class:"form-row"},Ve={class:"form-group"},De={class:"input-wrapper"},Be={class:"input-status"},Me={key:0,class:"status-checking"},Oe={key:1,class:"status-success"},Ue={key:2,class:"status-error"},ze={key:0,class:"error-message"},Pe={class:"form-group"},Je={key:0,class:"error-message"},Re={key:1,class:"info-message"},$e={key:2,class:"success-message"},Le={key:3,class:"error-message"},We={class:"form-row"},Ae={class:"form-group full-width"},Ee={class:"category-selection"},Ge={key:0,class:"no-categories"},qe={class:"category-group-title"},He={class:"category-options"},Ke=["value"],Ze={class:"category-label"},Qe={class:"selected-categories"},Xe={key:0,class:"no-selection"},Ye={key:1,class:"selection-summary"},es={class:"form-row"},ss={class:"form-group"},as={key:0,class:"form-row"},ls={class:"form-group"},ts={key:0,class:"error-message"},os={class:"form-group"},ns={class:"form-row"},is={class:"form-group full-width"},us={class:"preview-upload-section"},rs={key:1,class:"preview-image-container"},cs=["src"],ds={class:"form-group full-width"},vs={class:"form-section"},ps={class:"form-row"},ms={class:"form-group"},fs={key:0,class:"info-message"},gs={key:1,class:"success-message"},hs={key:2,class:"error-message"},ys={class:"form-group"},_s={class:"storage-info"},bs={class:"form-section"},ks={key:0,class:"upload-placeholder"},Cs={key:1,class:"file-list"},ws={class:"file-list-header"},Ss={class:"file-items"},Fs={class:"file-info"},Is={class:"file-name-section"},Ns={class:"file-name"},js={class:"file-details"},xs={class:"file-size"},Ts={key:0,class:"file-status error"},Vs={key:1,class:"file-status important"},Ds={key:2,class:"file-status success"},Bs=["onClick"],Ms={class:"file-summary"},Os={class:"vue-count"},Us={class:"json-count"},zs={key:0,class:"file-validation-info"},Ps={key:0,class:"validation-warning"},Js={key:1,class:"validation-info"},Rs={key:2,class:"validation-success"},$s={class:"form-actions"},Ls=["disabled"],Ws={key:0,class:"loading-icon"},As={class:"result-body"},Es={key:0,class:"result-summary"},Gs={class:"summary-item"},qs={class:"value"},Hs={class:"summary-item"},Ks={class:"value"},Zs={class:"summary-item"},Qs={class:"value"},Xs={class:"summary-item"},Ys={class:"value success"},ea={class:"summary-item"},sa={class:"value error"},aa={key:0,class:"summary-item"},la={class:"value"},ta={key:1,class:"summary-item"},oa={class:"value"},na={key:1,class:"result-section"},ia={class:"file-results"},ua={class:"file-name"},ra={class:"file-size"},ca={key:2,class:"result-section"},da={class:"file-results"},va={class:"file-name"},pa={class:"file-reason"},ma={__name:"upload",setup(fa){const V=Fe(),n=m({name:"",templateCode:"",description:"",bucketName:"resume-templates",categoryId:null,categoryIds:[],industry:"",style:"",colorScheme:"",isPremium:0,price:null,sortOrder:0,tags:"",features:"",configData:""}),r=m([]),D=m(!1),w=m(!1),I=m(!1),c=m(null),u=m({}),f=m(""),g=m(""),y=m(""),B=m(null),A=m(null),M=m([]),O=m([]),N=m(""),j=m(null),ne=S(()=>r.value.reduce((a,e)=>a+e.size,0)),E=S(()=>r.value.filter(a=>L(a)).length),R=S(()=>r.value.filter(a=>b(a)==="vue").length),G=S(()=>r.value.filter(a=>b(a)==="json").length),q=S(()=>n.value.name&&n.value.templateCode&&r.value.length>0&&E.value>0&&R.value>0&&f.value==="available"&&g.value==="available"&&!Object.keys(u.value).length),H=S(()=>n.value.categoryIds.map(a=>{const e=O.value.find(p=>p.id===a);return e==null?void 0:e.name}).filter(Boolean));Ce(()=>{$(),K()});const K=async()=>{try{const a=localStorage.getItem("admin_token");if(!a){console.warn("未找到管理员token，跳过获取分类数据");return}const e=await $fetch("/api/admin/template/category/tree",{method:"GET",headers:{Authorization:`Bearer ${a}`}});e.code===200&&(O.value=[],M.value=[],(e.data||[]).forEach(d=>{d.children&&d.children.length>0&&(O.value.push(d),d.children.forEach(k=>{O.value.push(k)}),M.value.push({type:d.categoryType,name:d.name,options:d.children}))}))}catch(a){console.error("获取分类数据失败:",a)}},ie=async()=>{try{await K(),U("分类列表已刷新","success")}catch(a){console.error("刷新分类失败:",a),U("刷新分类失败","error")}},U=(a,e="info")=>{console.log(`[${e.toUpperCase()}] ${a}`)},Z=async()=>{const a=n.value.name.trim();if(!a){u.value.name="模板名称不能为空",g.value="";return}if(a.length>100){u.value.name="模板名称长度不能超过100个字符",g.value="";return}g.value="checking";try{await V.checkTemplateName(a)?(g.value="available",delete u.value.name):(g.value="unavailable",u.value.name="模板名称已存在")}catch{g.value="",u.value.name="检查名称可用性失败"}},ue=()=>{g.value="",delete u.value.name},Q=async()=>{const a=n.value.templateCode.trim();if(!a){u.value.templateCode="模板代码不能为空",f.value="";return}if(!/^[a-zA-Z0-9_-]+$/.test(a)){u.value.templateCode="模板代码只能包含字母、数字、下划线和连字符",f.value="";return}if(a.length>50){u.value.templateCode="模板代码长度不能超过50个字符",f.value="";return}f.value="checking";try{await V.checkTemplateCode(a)?(f.value="available",delete u.value.templateCode):(f.value="unavailable",u.value.templateCode="模板代码已存在")}catch{f.value="",u.value.templateCode="检查代码可用性失败"}},re=()=>{f.value="",delete u.value.templateCode},$=async()=>{const a=n.value.bucketName.trim();if(!a){y.value="";return}y.value="checking";try{const e=await V.checkBucketStatus(a);y.value=e?"available":"unavailable"}catch{y.value="unavailable"}},ce=()=>{var a;(a=B.value)==null||a.click()},de=a=>{const e=Array.from(a.target.files);X(e)},ve=a=>{a.preventDefault(),D.value=!1;const e=Array.from(a.dataTransfer.files);X(e)},pe=a=>{a.preventDefault(),D.value=!0},me=()=>{D.value=!1},X=a=>{const e=a.filter(p=>!r.value.some(d=>d.name===p.name&&d.size===p.size));r.value.push(...e)},fe=a=>{r.value.splice(a,1)},ge=()=>{r.value=[]},L=a=>{const e=[".vue",".js",".ts",".html",".css",".json",".txt",".jpg",".jpeg",".png",".gif",".svg"],p=a.name.toLowerCase();return e.some(d=>p.endsWith(d))},b=a=>{const e=a.name.toLowerCase();return e.endsWith(".vue")?"vue":e.endsWith(".json")?"json":e.endsWith(".css")?"css":e.endsWith(".js")||e.endsWith(".ts")?"script":e.endsWith(".html")?"html":e.match(/\.(jpg|jpeg|png|gif|svg)$/)?"image":"other"},he=a=>{const e=b(a);return{vue:"Vue组件",json:"JSON内容",css:"CSS样式",script:"JavaScript",html:"HTML文件",image:"图片文件",other:"其他文件"}[e]||"未知类型"},Y=a=>{const e=b(a);return e==="vue"||e==="json"},ee=()=>{var a;(a=A.value)==null||a.click()},ye=a=>{const e=a.target.files[0];if(!e)return;if(!["image/jpeg","image/png","image/jpg"].includes(e.type)){U("请选择 JPG 或 PNG 格式的图片","error");return}const d=5*1024*1024;if(e.size>d){U("图片大小不能超过 5MB","error");return}const k=new FileReader;k.onload=P=>{N.value=P.target.result,j.value=e},k.readAsDataURL(e),a.target.value=""},_e=()=>{N.value="",j.value=null},W=a=>{if(a===0)return"0 B";const e=1024,p=["B","KB","MB","GB"],d=Math.floor(Math.log(a)/Math.log(e));return parseFloat((a/Math.pow(e,d)).toFixed(2))+" "+p[d]},se=async()=>{if(Z(),await Q(),n.value.isPremium===1&&(!n.value.price||n.value.price<=0)){u.value.price="付费模板价格必须大于0";return}if(q.value){w.value=!0;try{console.log("🚀 开始上传模板...");let a={...n.value};j.value&&(a.previewImage=j.value);const e=await V.uploadTemplate(r.value,a,j.value);console.log("📋 上传结果:",e),e.success&&e.data?(c.value=e.data,I.value=!0,console.log("✅ 上传成功，显示结果弹窗")):(console.error("❌ 上传失败:",e.error||e.message),c.value={templateName:n.value.name,templateCode:n.value.templateCode,totalFiles:r.value.length,successCount:0,failureCount:r.value.length,uploadedFiles:[],failedFiles:r.value.map(p=>({originalName:p.name,failureReason:e.error||e.message||"上传失败"}))},I.value=!0)}catch(a){console.error("❌ 上传异常:",a),c.value={templateName:n.value.name,templateCode:n.value.templateCode,totalFiles:r.value.length,successCount:0,failureCount:r.value.length,uploadedFiles:[],failedFiles:r.value.map(e=>({originalName:e.name,failureReason:a.message||"网络错误，请重试"}))},I.value=!0}finally{w.value=!1}}},ae=()=>{n.value={name:"",templateCode:"",description:"",bucketName:"resume-templates",categoryId:null,categoryIds:[],industry:"",style:"",colorScheme:"",isPremium:0,price:null,sortOrder:0,tags:"",features:"",configData:""},r.value=[],u.value={},f.value="",g.value="",y.value="",B.value&&(B.value.value=""),$()},z=()=>{I.value=!1,c.value=null},be=()=>{var e;z(),((e=c.value)==null?void 0:e.successCount)>0&&console.log(`✅ 模板 "${c.value.templateName}" 上传成功！`)},ke=()=>{z(),ae()};return(a,e)=>{var p,d,k,P;return t(),l("div",Ne,[e[45]||(e[45]=te('<div class="page-header" data-v-4048cff5><div class="breadcrumb" data-v-4048cff5><span class="breadcrumb-item" data-v-4048cff5>管理后台</span><span class="breadcrumb-separator" data-v-4048cff5>/</span><span class="breadcrumb-item" data-v-4048cff5>模板管理</span><span class="breadcrumb-separator" data-v-4048cff5>/</span><span class="breadcrumb-item active" data-v-4048cff5>模板上传</span></div><h1 class="page-title" data-v-4048cff5>简历模板上传</h1><p class="page-description" data-v-4048cff5>支持批量上传Vue组件、CSS样式、预览图等文件，自动创建模板记录</p></div>',1)),s("div",je,[s("div",xe,[e[23]||(e[23]=s("h2",{class:"section-title"},"基本信息",-1)),s("form",{onSubmit:J(se,["prevent"]),class:"upload-form"},[s("div",Te,[s("div",Ve,[e[9]||(e[9]=s("label",{class:"form-label required"},"模板名称",-1)),s("div",De,[_(s("input",{"onUpdate:modelValue":e[0]||(e[0]=o=>n.value.name=o),type:"text",class:h(["form-input",{error:u.value.name}]),placeholder:"请输入模板名称，如：商务简约模板",onBlur:Z,onInput:ue},null,34),[[F,n.value.name]]),s("div",Be,[g.value==="checking"?(t(),l("span",Me,"检查中...")):g.value==="available"?(t(),l("span",Oe,"✓ 可用")):g.value==="unavailable"?(t(),l("span",Ue,"✗ 已存在")):v("",!0)])]),u.value.name?(t(),l("span",ze,i(u.value.name),1)):v("",!0)]),s("div",Pe,[e[10]||(e[10]=s("label",{class:"form-label required"},"模板代码",-1)),_(s("input",{"onUpdate:modelValue":e[1]||(e[1]=o=>n.value.templateCode=o),type:"text",class:h(["form-input",{error:u.value.templateCode}]),placeholder:"请输入模板代码，如：business-simple",onBlur:Q,onInput:re},null,34),[[F,n.value.templateCode]]),u.value.templateCode?(t(),l("span",Je,i(u.value.templateCode),1)):v("",!0),f.value==="checking"?(t(),l("span",Re,"正在检查可用性...")):v("",!0),f.value==="available"?(t(),l("span",$e,"✓ 代码可用")):v("",!0),f.value==="unavailable"?(t(),l("span",Le,"✗ 代码已存在")):v("",!0)])]),s("div",We,[s("div",Ae,[e[14]||(e[14]=s("label",{class:"form-label"},"模板分类",-1)),s("div",{class:"category-header"},[e[11]||(e[11]=s("span",null,"请选择适用的分类（可多选）",-1)),s("button",{type:"button",onClick:ie,class:"refresh-categories-btn",title:"刷新分类列表"}," 🔄 刷新分类 ")]),s("div",Ee,[M.value.length===0?(t(),l("div",Ge,e[12]||(e[12]=[s("span",null,"暂无分类数据，请先在分类管理中创建分类",-1)]))):v("",!0),(t(!0),l(x,null,T(M.value,o=>(t(),l("div",{key:o.type,class:"category-group"},[s("h4",qe,i(o.name),1),s("div",He,[(t(!0),l(x,null,T(o.options,C=>(t(),l("label",{key:C.id,class:h(["category-option",{selected:n.value.categoryIds.includes(C.id)}])},[_(s("input",{type:"checkbox",value:C.id,"onUpdate:modelValue":e[2]||(e[2]=le=>n.value.categoryIds=le),class:"category-checkbox"},null,8,Ke),[[Se,n.value.categoryIds]]),s("span",Ze,i(C.name),1)],2))),128))])]))),128))]),s("div",Qe,[e[13]||(e[13]=s("span",{class:"selected-label"},"已选择分类：",-1)),H.value.length===0?(t(),l("span",Xe,"未选择")):(t(),l("span",Ye,i(H.value.join(", ")),1))])])]),s("div",es,[s("div",ss,[e[16]||(e[16]=s("label",{class:"form-label"},"模板类型",-1)),_(s("select",{"onUpdate:modelValue":e[3]||(e[3]=o=>n.value.isPremium=o),class:"form-select"},e[15]||(e[15]=[s("option",{value:0},"免费模板",-1),s("option",{value:1},"付费模板",-1)]),512),[[we,n.value.isPremium]])])]),n.value.isPremium===1?(t(),l("div",as,[s("div",ls,[e[17]||(e[17]=s("label",{class:"form-label required"},"模板价格",-1)),_(s("input",{"onUpdate:modelValue":e[4]||(e[4]=o=>n.value.price=o),type:"number",step:"0.01",min:"0",class:h(["form-input",{error:u.value.price}]),placeholder:"请输入价格（元）"},null,2),[[F,n.value.price]]),u.value.price?(t(),l("span",ts,i(u.value.price),1)):v("",!0)]),s("div",os,[e[18]||(e[18]=s("label",{class:"form-label"},"排序权重",-1)),_(s("input",{"onUpdate:modelValue":e[5]||(e[5]=o=>n.value.sortOrder=o),type:"number",min:"0",class:"form-input",placeholder:"数值越大排序越靠前"},null,512),[[F,n.value.sortOrder]])])])):v("",!0),s("div",ns,[s("div",is,[e[21]||(e[21]=s("label",{class:"form-label"},"模板预览图",-1)),s("div",us,[s("div",{class:h(["upload-area",{"has-image":N.value}])},[N.value?(t(),l("div",rs,[s("img",{src:N.value,alt:"预览图",class:"preview-image"},null,8,cs),s("div",{class:"image-actions"},[s("button",{type:"button",onClick:ee,class:"btn-change"},"更换图片"),s("button",{type:"button",onClick:_e,class:"btn-remove"},"删除图片")])])):(t(),l("div",{key:0,class:"upload-placeholder",onClick:ee},e[19]||(e[19]=[s("div",{class:"upload-icon"},"🖼️",-1),s("p",null,"点击上传预览图",-1),s("p",{class:"upload-tips"},"支持 JPG、PNG 格式，建议尺寸 400x600px",-1)])))],2),s("input",{ref_key:"previewFileInput",ref:A,type:"file",accept:"image/jpeg,image/png,image/jpg",onChange:ye,style:{display:"none"}},null,544),e[20]||(e[20]=s("div",{class:"upload-info"},[s("span",{class:"info-text"},"预览图将显示在模板列表中，帮助用户快速了解模板样式")],-1))])])]),s("div",ds,[e[22]||(e[22]=s("label",{class:"form-label"},"模板描述",-1)),_(s("textarea",{"onUpdate:modelValue":e[6]||(e[6]=o=>n.value.description=o),class:"form-textarea",rows:"3",placeholder:"请描述模板的特点和适用场景..."},null,512),[[F,n.value.description]])])],32)]),s("div",vs,[e[28]||(e[28]=s("h2",{class:"section-title"},"存储配置",-1)),s("div",ps,[s("div",ms,[e[24]||(e[24]=s("label",{class:"form-label"},"MinIO桶名称",-1)),_(s("input",{"onUpdate:modelValue":e[7]||(e[7]=o=>n.value.bucketName=o),type:"text",class:"form-input",placeholder:"默认：resume-templates",onBlur:$},null,544),[[F,n.value.bucketName]]),y.value==="checking"?(t(),l("span",fs,"正在检查桶状态...")):v("",!0),y.value==="available"?(t(),l("span",gs,"✓ 桶可用")):v("",!0),y.value==="unavailable"?(t(),l("span",hs,"✗ 桶不可用")):v("",!0)]),s("div",ys,[e[27]||(e[27]=s("label",{class:"form-label"},"存储说明",-1)),s("div",_s,[s("p",null,[e[25]||(e[25]=oe("文件将存储在：")),s("code",null,i(n.value.bucketName)+"/"+i(n.value.templateCode||"{模板代码}")+"/",1)]),e[26]||(e[26]=s("p",{class:"info-text"},"系统会自动根据模板代码创建存储目录",-1))])])])]),s("div",bs,[e[30]||(e[30]=s("h2",{class:"section-title"},"文件上传",-1)),s("div",{class:h(["upload-area",{"drag-over":D.value,"has-files":r.value.length>0}]),onDrop:ve,onDragover:pe,onDragleave:me,onClick:ce},[s("input",{ref_key:"fileInput",ref:B,type:"file",multiple:"",accept:".vue,.js,.ts,.html,.css,.json,.txt,.jpg,.jpeg,.png,.gif,.svg",style:{display:"none"},onChange:de},null,544),r.value.length===0?(t(),l("div",ks,e[29]||(e[29]=[s("div",{class:"upload-icon"},"📁",-1),s("p",{class:"upload-text"},"点击选择文件或拖拽文件到此区域",-1),s("p",{class:"upload-hint"},"支持 .vue .js .html .css .json .png .jpg 等格式",-1)]))):(t(),l("div",Cs,[s("div",ws,[s("span",null,"已选择 "+i(r.value.length)+" 个文件",1),s("button",{type:"button",onClick:J(ge,["stop"]),class:"clear-btn"},"清空")]),s("div",Ss,[(t(!0),l(x,null,T(r.value,(o,C)=>(t(),l("div",{key:C,class:h(["file-item",{invalid:!L(o),"vue-file":b(o)==="vue","json-file":b(o)==="json","important-file":Y(o)}])},[s("div",Fs,[s("div",Is,[s("span",Ns,i(o.name),1),s("span",{class:h(["file-type-badge",b(o)])},i(he(o)),3)]),s("div",js,[s("span",xs,i(W(o.size)),1),L(o)?Y(o)?(t(),l("span",Vs,"核心文件")):(t(),l("span",Ds,"支持")):(t(),l("span",Ts,"不支持"))])]),s("button",{type:"button",onClick:J(le=>fe(C),["stop"]),class:"remove-btn"}," × ",8,Bs)],2))),128))]),s("div",Ms,[s("span",null,"总大小: "+i(W(ne.value)),1),s("span",null,"有效文件: "+i(E.value),1),s("span",Os,"Vue文件: "+i(R.value),1),s("span",Us,"JSON文件: "+i(G.value),1)])]))],34),e[31]||(e[31]=te('<div class="file-types-info" data-v-4048cff5><h4 data-v-4048cff5>支持的文件类型：</h4><div class="file-types-list" data-v-4048cff5><span class="file-type-tag priority" data-v-4048cff5>Vue组件 (.vue)</span><span class="file-type-tag priority" data-v-4048cff5>JSON内容 (.json)</span><span class="file-type-tag" data-v-4048cff5>CSS样式 (.css)</span><span class="file-type-tag" data-v-4048cff5>JavaScript (.js)</span><span class="file-type-tag" data-v-4048cff5>TypeScript (.ts)</span><span class="file-type-tag" data-v-4048cff5>图片 (.jpg .png .gif .svg)</span><span class="file-type-tag" data-v-4048cff5>其他 (.html .txt)</span></div><div class="file-types-note" data-v-4048cff5><div class="note-section" data-v-4048cff5><strong data-v-4048cff5>📋 新架构说明：</strong><ul data-v-4048cff5><li data-v-4048cff5><strong data-v-4048cff5>Vue文件</strong>：模板的UI组件，必须包含（存储在MinIO）</li><li data-v-4048cff5><strong data-v-4048cff5>JSON文件</strong>：默认简历内容，推荐包含（存储在数据库）</li><li data-v-4048cff5><strong data-v-4048cff5>其他文件</strong>：图片、样式等辅助文件（存储在MinIO）</li></ul></div><div class="note-section" data-v-4048cff5><strong data-v-4048cff5>✨ 优势：</strong><span data-v-4048cff5>内容与样式分离，支持内容复用，便于维护管理</span></div></div></div>',1))]),r.value.length>0?(t(),l("div",zs,[R.value===0?(t(),l("div",Ps,e[32]||(e[32]=[s("span",{class:"warning-icon"},"⚠️",-1),s("span",null,"请至少上传一个Vue文件作为模板组件",-1)]))):G.value===0?(t(),l("div",Js,e[33]||(e[33]=[s("span",{class:"info-icon"},"💡",-1),s("span",null,"建议上传JSON文件作为默认内容，如果没有将使用系统推荐内容",-1)]))):(t(),l("div",Rs,e[34]||(e[34]=[s("span",{class:"success-icon"},"✅",-1),s("span",null,"文件配置完整，包含Vue组件和JSON内容",-1)])))])):v("",!0),s("div",$s,[s("button",{type:"button",onClick:se,class:h(["submit-btn",{loading:w.value}]),disabled:w.value||!q.value},[w.value?(t(),l("span",Ws,"⏳")):v("",!0),oe(" "+i(w.value?"上传中...":"开始上传"),1)],10,Ls),s("button",{type:"button",onClick:ae,class:"reset-btn"}," 重置表单 ")])]),I.value?(t(),l("div",{key:0,class:"result-modal",onClick:z},[s("div",{class:"result-content",onClick:e[8]||(e[8]=J(()=>{},["stop"]))},[s("div",{class:"result-header"},[e[35]||(e[35]=s("h3",null,"上传结果",-1)),s("button",{onClick:z,class:"close-btn"},"×")]),s("div",As,[c.value?(t(),l("div",Es,[s("div",Gs,[e[36]||(e[36]=s("span",{class:"label"},"模板名称:",-1)),s("span",qs,i(c.value.templateName),1)]),s("div",Hs,[e[37]||(e[37]=s("span",{class:"label"},"模板代码:",-1)),s("span",Ks,i(c.value.templateCode),1)]),s("div",Zs,[e[38]||(e[38]=s("span",{class:"label"},"总文件数:",-1)),s("span",Qs,i(c.value.totalFiles),1)]),s("div",Xs,[e[39]||(e[39]=s("span",{class:"label"},"成功上传:",-1)),s("span",Ys,i(c.value.successCount),1)]),s("div",ea,[e[40]||(e[40]=s("span",{class:"label"},"上传失败:",-1)),s("span",sa,i(c.value.failureCount),1)]),c.value.contentId?(t(),l("div",aa,[e[41]||(e[41]=s("span",{class:"label"},"内容ID:",-1)),s("span",la,i(c.value.contentId),1)])):v("",!0),c.value.mainTemplateFileId?(t(),l("div",ta,[e[42]||(e[42]=s("span",{class:"label"},"主模板文件ID:",-1)),s("span",oa,i(c.value.mainTemplateFileId),1)])):v("",!0)])):v("",!0),(d=(p=c.value)==null?void 0:p.uploadedFiles)!=null&&d.length?(t(),l("div",na,[e[43]||(e[43]=s("h4",null,"成功上传的文件:",-1)),s("div",ia,[(t(!0),l(x,null,T(c.value.uploadedFiles,o=>(t(),l("div",{key:o.fileId,class:"file-result success"},[s("span",ua,i(o.originalName),1),s("span",ra,i(W(o.fileSize)),1)]))),128))])])):v("",!0),(P=(k=c.value)==null?void 0:k.failedFiles)!=null&&P.length?(t(),l("div",ca,[e[44]||(e[44]=s("h4",null,"上传失败的文件:",-1)),s("div",da,[(t(!0),l(x,null,T(c.value.failedFiles,o=>(t(),l("div",{key:o.originalName,class:"file-result error"},[s("span",va,i(o.originalName),1),s("span",pa,i(o.failureReason),1)]))),128))])])):v("",!0)]),s("div",{class:"result-actions"},[s("button",{onClick:be,class:"btn-primary"}," 确定 "),s("button",{onClick:ke,class:"btn-secondary"}," 清空并继续上传 ")])])])):v("",!0)])}}},_a=Ie(ma,[["__scopeId","data-v-4048cff5"]]);export{_a as default};
