<template>
  <div class="bg-white py-12">
    <div class="container-width section-padding">
      <!-- 标题和控制按钮 -->
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-2xl font-bold text-secondary-900">
          <NuxtLink 
            :to="titleLink"
            class="hover:text-primary-600 transition-colors duration-200 cursor-pointer"
          >
            {{ title }}
          </NuxtLink>
        </h2>
        <div class="flex items-center space-x-3">
          <button
            @click="slidePrev"
            :disabled="isAtStart"
            class="p-2 rounded-full border border-secondary-200 hover:border-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            :class="isAtStart ? 'text-secondary-400' : 'text-secondary-600 hover:text-primary-600'"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <button
            @click="slideNext"
            :disabled="isAtEnd"
            class="p-2 rounded-full border border-secondary-200 hover:border-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            :class="isAtEnd ? 'text-secondary-400' : 'text-secondary-600 hover:text-primary-600'"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 轮播容器 -->
      <div class="relative overflow-hidden">
        <div 
          ref="carouselRef"
          class="flex transition-transform duration-500 ease-out"
          :style="{ transform: `translateX(-${currentSlide * slideWidth}%)` }"
        >
          <div
            v-for="(template, index) in templates"
            :key="template.id"
            class="flex-shrink-0"
            :style="{ width: `${slideWidth}%` }"
          >
            <div class="px-2">
              <!-- 模板卡片 -->
              <div 
                class="group cursor-pointer bg-white rounded-xl shadow-card border border-secondary-100 overflow-hidden transition-all duration-300 hover:shadow-soft hover:-translate-y-1"
                @click="handleTemplateClick(template)"
              >
                <!-- 模板预览图 -->
                <div class="relative aspect-[3/4] bg-gradient-to-br from-secondary-50 to-secondary-100">
                  <img
                    v-if="template.thumbnail"
                    :src="template.thumbnail"
                    :alt="template.name"
                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    loading="lazy"
                  />
                  <!-- 默认预览内容 -->
                  <div v-else class="p-4 h-full flex flex-col justify-center items-center text-center">
                    <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-3">
                      <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                    </div>
                    <span class="text-xs text-secondary-600">{{ template.name }}</span>
                  </div>

                  <!-- 使用人数标签 -->
                  <div class="absolute bottom-3 left-3">
                    <span class="inline-flex items-center px-2 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-danger-600">
                      {{ template.usageCount || 0 }}人使用
                    </span>
                  </div>

                  <!-- 悬浮操作按钮 -->
                  <div class="absolute inset-0 bg-primary-600/0 group-hover:bg-primary-600/90 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <div class="flex space-x-2">
                      <button 
                        @click.stop="handleUseTemplate(template)"
                        class="px-3 py-1.5 bg-white text-primary-600 rounded-lg hover:bg-secondary-50 transition-colors duration-200 text-sm font-medium"
                      >
                        使用模板
                      </button>
                      <button 
                        @click.stop="handlePreview(template)"
                        class="px-3 py-1.5 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors duration-200 text-sm font-medium"
                      >
                        预览
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 模板信息 -->
                <div class="p-4">
                  <h3 class="font-semibold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200 line-clamp-1">
                    {{ template.name }}
                  </h3>
                  <div class="flex items-center justify-between text-sm">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                      {{ template.category || '经典' }}
                    </span>
                    <div class="flex items-center text-secondary-500">
                      <svg class="w-3 h-3 mr-1 text-warning-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                      {{ template.rating || 4.8 }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 指示器 -->
      <div v-if="showIndicators && totalSlides > 1" class="flex justify-center space-x-2 mt-6">
        <button
          v-for="(slide, index) in totalSlides"
          :key="index"
          @click="goToSlide(index)"
          class="w-2 h-2 rounded-full transition-all duration-200"
          :class="currentSlide === index ? 'bg-primary-600' : 'bg-secondary-300 hover:bg-secondary-400'"
        ></button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  templates: {
    type: Array,
    required: true,
    default: () => []
  },
  titleLink: {
    type: String,
    default: '/templates'
  },
  slidesToShow: {
    type: Number,
    default: 6
  },
  slidesToScroll: {
    type: Number,
    default: 1
  },
  showIndicators: {
    type: Boolean,
    default: false
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  autoplayInterval: {
    type: Number,
    default: 5000
  }
})

// Emits
const emit = defineEmits(['template-click', 'use-template', 'preview'])

// 响应式数据
const currentSlide = ref(0)
const carouselRef = ref(null)
const autoplayTimer = ref(null)

// 计算属性
const slideWidth = computed(() => {
  return 100 / props.slidesToShow
})

const totalSlides = computed(() => {
  return Math.max(0, Math.ceil((props.templates.length - props.slidesToShow) / props.slidesToScroll) + 1)
})

const isAtStart = computed(() => {
  return currentSlide.value === 0
})

const isAtEnd = computed(() => {
  return currentSlide.value >= totalSlides.value - 1
})

// 方法
const slideNext = () => {
  if (!isAtEnd.value) {
    currentSlide.value += 1
  }
}

const slidePrev = () => {
  if (!isAtStart.value) {
    currentSlide.value -= 1
  }
}

const goToSlide = (index) => {
  currentSlide.value = Math.max(0, Math.min(index, totalSlides.value - 1))
}

const handleTemplateClick = (template) => {
  emit('template-click', template)
}

const handleUseTemplate = (template) => {
  emit('use-template', template)
}

const handlePreview = (template) => {
  emit('preview', template)
}

// 自动播放
const startAutoplay = () => {
  if (props.autoplay && totalSlides.value > 1) {
    autoplayTimer.value = setInterval(() => {
      if (isAtEnd.value) {
        currentSlide.value = 0
      } else {
        slideNext()
      }
    }, props.autoplayInterval)
  }
}

const stopAutoplay = () => {
  if (autoplayTimer.value) {
    clearInterval(autoplayTimer.value)
    autoplayTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  startAutoplay()
})

onUnmounted(() => {
  stopAutoplay()
})
</script> 