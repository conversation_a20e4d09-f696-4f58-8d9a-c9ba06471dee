<template>
  <div class="resume-preview" ref="previewRef">
    <div class="preview-container">
      <!-- 缩放包装器 -->
      <div class="scale-wrapper">
        <!-- 自然流动的简历容器 -->
        <div 
          class="natural-flow-container" 
          ref="resumeRef" 
          :style="{ 
            minHeight: containerMinHeight + 'px',
            transform: `scale(${scale})`,
            transformOrigin: 'center top'
          }"
        >
        <!-- A4边界标识 -->
        <div class="a4-boundary-label">A4 纸张区域 (210mm × 297mm)</div>
        <!-- 分页边界指示器 -->
        <div class="page-break-indicators">
          <div 
            v-for="(indicator, index) in pageBreakIndicators"
            :key="`break-${index}`"
            class="page-break-indicator"
            :style="{ top: indicator.top + 'px' }"
          >
            <div class="break-line">
              <!-- 只在非最后一页显示左侧提示文字 -->
              <div v-if="!indicator.isLastPage" class="break-text-left">页面分隔处，避免在此编辑内容</div>
              <!-- 右侧始终显示页码 -->
              <div class="break-text-right">第 {{ indicator.pageNumber }} 页</div>
            </div>
          </div>
        </div>
        
        <!-- 连续的简历内容 -->
        <div class="continuous-resume-content" :style="{ ...pageStyles, minHeight: containerMinHeight + 'px' }">
          <!-- 简历模板组件，包含所有内容 -->
          <ResumeTemplate
            :resume-data="currentResumeData"
            :text-style="compatibleTextStyle"
            :is-draggable="true"
            :visible-modules="visibleModuleIds"
            :page-index="0"
            :is-pagination-mode="false"
            :is-natural-flow="true"
            @basic-info-update="handleBasicInfoUpdate"
            @module-move-up="handleModuleMoveUp"
            @module-move-down="handleModuleMoveDown"
            @module-delete="handleModuleDelete"
            @skill-update="handleSkillUpdate"
            @experience-update="handleExperienceUpdate"
            @education-update="handleEducationUpdate"
            @project-update="handleProjectUpdate"
            @content-height-change="handleContentHeightChange"
          />
        </div>
        

        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * 简历预览组件
 * @description 实时预览简历效果，支持拖拽排序和模块管理，支持自动分页
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'
import ResumeTemplate from './ResumeTemplate.vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 简历数据
   */
  resumeData: {
    type: Object,
    default: () => ({})
  },

  /**
   * 样式设置
   */
  settings: {
    type: Object,
    default: () => ({})
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['module-order-change', 'module-delete', 'template-defaults-loaded'])

// ================================
// 响应式数据
// ================================
const editorStore = useEditorStore()
const previewRef = ref(null)
const resumeRef = ref(null)
const contentHeight = ref(0)
const pageBreakIndicators = ref([])

// 缩放相关
const scale = ref(1)
const containerWidth = ref(0)
const A4_WIDTH_MM = 210 // A4纸张宽度（毫米）
const A4_WIDTH_PX = 794 // A4纸张宽度（像素，210mm = 794px at 96 DPI）

/**
 * 默认样式设置 - 与标准CSS变量规范保持一致
 */
const defaultStyles = {
  fontFamily: 'system-ui',
  fontSize: '14px',
  textColor: '#333333',
  margins: {
    top: 60,
    bottom: 60,
    side: 60
  },
  spacings: {
    module: 24,
    item: 16,
    line: 1.6,
    modulePadding: 16,
    itemPadding: 12,
    paragraph: 12,
    columnGap: 40
  }
}

// ================================
// 分页相关数据
// ================================
const A4_HEIGHT = 1122 // A4纸张高度（px）
const PAGE_PADDING = 60 // 页面内边距
const CONTENT_HEIGHT = A4_HEIGHT - PAGE_PADDING * 2 // 可用内容高度

// ================================
// 计算属性
// ================================

/**
 * 简历样式
 */
const resumeStyles = computed(() => ({
  ...defaultStyles,
  ...props.settings
}))

/**
 * 页面样式
 */
const pageStyles = computed(() => {
  // 添加调试信息
  console.log('🐛 pageStyles 调试 - 模块间距:', {
    userModuleSpacing: resumeStyles.value.spacings?.module,
    finalModuleSpacing: resumeStyles.value.spacings?.module || 8,
    resumeStylesValue: resumeStyles.value,
    spacings: resumeStyles.value.spacings
  })
  
  return {
    fontFamily: resumeStyles.value.fontFamily,
    fontSize: resumeStyles.value.fontSize,
    lineHeight: resumeStyles.value.spacings?.line || 1.6,
    color: resumeStyles.value.textColor,
    // 使用新的标准CSS变量名
    '--resume-margin-top': `${resumeStyles.value.margins?.top || 20}px`,
    '--resume-margin-bottom': `${resumeStyles.value.margins?.bottom || 20}px`,
    '--resume-margin-left': `${resumeStyles.value.margins?.side || 60}px`,
    '--resume-margin-right': `${resumeStyles.value.margins?.side || 60}px`,
    '--resume-module-spacing': `${resumeStyles.value.spacings?.module || 24}px`,
  '--resume-module-padding': `${resumeStyles.value.spacings?.modulePadding || 16}px`,
  '--resume-item-spacing': `${resumeStyles.value.spacings?.item || 16}px`,
  '--resume-item-padding': `${resumeStyles.value.spacings?.itemPadding || 12}px`,
  '--resume-line-height': resumeStyles.value.spacings?.line || 1.6,
  '--resume-paragraph-spacing': `${resumeStyles.value.spacings?.paragraph || 12}px`,
  '--resume-column-gap': `${resumeStyles.value.spacings?.columnGap || 40}px`,
  // 兼容旧变量名，保证向后兼容
  '--margin-top': `${resumeStyles.value.margins?.top || 20}px`,
  '--margin-side': `${resumeStyles.value.margins?.side || 60}px`,
  '--module-spacing': `${resumeStyles.value.spacings?.module || 24}px`,
  '--item-spacing': `${resumeStyles.value.spacings?.item || 16}px`,
  '--line-height': resumeStyles.value.spacings?.line || 1.6,
  }
})

/**
 * 兼容的文本样式（用于ResumeTemplate）
 */
const compatibleTextStyle = computed(() => ({
  fontSize: resumeStyles.value.fontSize || '14px',
  lineHeight: resumeStyles.value.spacings?.line || 1.6,
  textColor: resumeStyles.value.textColor || '#333333',
  primaryColor: '#3b82f6', // 保持默认主色
  secondaryColor: '#6366f1', // 保持默认次色
  fontFamily: resumeStyles.value.fontFamily || 'system-ui',
  // 映射边距和间距到CSS变量
  margins: resumeStyles.value.margins,
  spacings: resumeStyles.value.spacings
}))

/**
 * 当前简历数据
 */
const currentResumeData = computed(() => {
  // 确保正确访问响应式数据
  const finalData = props.resumeData || {}
  console.log('✅ ResumePreview 使用数据:', finalData)
  console.log('🔍 ResumePreview - modules:', Object.keys(finalData.modules || {}))
  
  return finalData
})

/**
 * 可见模块列表
 */
const visibleModules = computed(() => {
  const resumeModules = currentResumeData.value?.modules || {}
  
  console.log('🔍 ResumePreview - 计算可见模块')
  console.log('📊 简历数据模块:', Object.keys(resumeModules))
  console.log('🏪 editorStore:', editorStore)
  
  // 如果没有简历数据，返回空数组
  if (!resumeModules || Object.keys(resumeModules).length === 0) {
    console.log('⚠️ 没有简历数据')
    return []
  }
  
  // 确保 editorStore 存在
  if (!editorStore) {
    console.warn('⚠️ editorStore 未定义')
    return []
  }
  
  // 访问响应式数据以确保计算属性能够响应变化
  const storeEnabledModules = editorStore.enabledModules
  const storeModuleConfigs = editorStore.moduleConfigs
  const storeCustomModules = editorStore.customModules
  
  console.log('🔍 响应式数据检查:', {
    enabledModules: storeEnabledModules,
    moduleConfigs: storeModuleConfigs,
    customModules: storeCustomModules
  })
  
  // 尝试多种方式获取启用的模块
  let enabledModules = []
  
  try {
    // 首先尝试使用 editorStore.enabledModules
    if (storeEnabledModules && Array.isArray(storeEnabledModules) && storeEnabledModules.length > 0) {
      enabledModules = storeEnabledModules
      console.log('✅ 使用 editorStore.enabledModules:', enabledModules.map(m => `${m.id}(${m.order})`))
    } else {
      // 后备方案1：尝试直接从 moduleConfigs 获取
      const moduleConfigs = storeModuleConfigs?.value || storeModuleConfigs || []
      const customModules = storeCustomModules?.value || storeCustomModules || []
      
      if (Array.isArray(moduleConfigs) && moduleConfigs.length > 0) {
        const allModules = [...moduleConfigs, ...customModules]
        enabledModules = allModules
          .filter(module => module.enabled)
          .sort((a, b) => (a.order || 0) - (b.order || 0))
        
        console.log('🔄 使用 moduleConfigs 后备方案:', enabledModules.map(m => `${m.id}(${m.order})`))
      } else {
        // 后备方案2：基于有数据的模块来显示
        console.log('🔄 使用数据驱动的最终后备方案')
        const moduleIds = Object.keys(resumeModules)
        
        // 定义基本的模块顺序
        const defaultOrder = {
          'basic': 1,
          'education': 2,
          'workExperience': 3,
          'projectExperience': 4,
          'skills': 5,
          'certificate': 6,
          'selfEvaluation': 7,
          'internship': 8,
          'awards': 9,
          'hobbies': 10
        }
        
        enabledModules = moduleIds.map(id => ({
          id,
          name: id,
          enabled: true,
          order: defaultOrder[id] || 999
        })).sort((a, b) => a.order - b.order)
        
        console.log('🔄 基于数据的模块:', enabledModules.map(m => `${m.id}(${m.order})`))
      }
    }
  } catch (error) {
    console.error('❌ 获取启用模块时出错:', error)
    return []
  }
  
  // 只显示既有数据又被启用的模块
  const visibleModuleList = enabledModules
    .map(config => {
      const moduleData = resumeModules[config.id]
      if (moduleData !== undefined) {
        return {
          id: config.id,
          data: moduleData,
          enabled: true,
          order: config.order || 0,
          name: config.name
        }
      }
      return null
    })
    .filter(module => module !== null)
  
  console.log('✅ 最终可见模块:', visibleModuleList.map(m => `${m.name}(${m.id})`))
  
  return visibleModuleList
})

/**
 * 可见模块ID列表
 */
const visibleModuleIds = computed(() => {
  return visibleModules.value.map(module => module.id)
})

/**
 * 计算总页数
 */
const totalPages = computed(() => {
  if (contentHeight.value === 0) return 1
  return Math.ceil(contentHeight.value / A4_HEIGHT)
})

/**
 * 计算容器最小高度（确保显示完整页面）
 */
const containerMinHeight = computed(() => {
  const pages = totalPages.value
  const minHeight = pages * A4_HEIGHT
  console.log(`📏 计算容器最小高度: ${pages}页 × ${A4_HEIGHT}px = ${minHeight}px`)
  return minHeight
})

// ================================
// 缩放相关计算属性
// ================================

/**
 * 计算缩放比例
 */
const calculateScale = () => {
  if (!previewRef.value) return 1
  
  // 获取预览容器的实际宽度
  const containerRect = previewRef.value.getBoundingClientRect()
  const availableWidth = containerRect.width - 24 // 减去左右padding (12px * 2)
  
  // 确保有足够的宽度进行计算
  if (availableWidth <= 0) return 0.3
  
  // 计算缩放比例，确保A4纸张能完全显示在容器中
  // 预留一些空间确保缩放后的内容不会贴边
  const safeWidth = availableWidth - 20 // 预留10px左右边距
  const calculatedScale = safeWidth / A4_WIDTH_PX
  
  // 限制缩放范围：最小25%，最大120%
  const finalScale = Math.max(0.25, Math.min(calculatedScale, 1.2))
  
  console.log(`🔍 容器宽度: ${containerRect.width}px, 可用宽度: ${availableWidth}px, 安全宽度: ${safeWidth}px, A4宽度: ${A4_WIDTH_PX}px, 计算缩放: ${calculatedScale.toFixed(2)}, 最终缩放: ${finalScale.toFixed(2)}`)
  
  return finalScale
}

// ================================
// 模板默认值读取
// ================================

/**
 * 从模板CSS中读取默认样式变量
 */
const getTemplateDefaults = () => {
  const templateElement = document.querySelector('.resume-template')
  if (!templateElement) return {}
  
  const computedStyles = getComputedStyle(templateElement)
  
  return {
    moduleSpacing: parseInt(computedStyles.getPropertyValue('--template-default-module-spacing')) || 24,
    modulePadding: parseInt(computedStyles.getPropertyValue('--template-default-module-padding')) || 16,
    itemSpacing: parseInt(computedStyles.getPropertyValue('--template-default-item-spacing')) || 16,
    itemPadding: parseInt(computedStyles.getPropertyValue('--template-default-item-padding')) || 12,
    marginTop: parseInt(computedStyles.getPropertyValue('--template-default-margin-top')) || 60,
    marginBottom: parseInt(computedStyles.getPropertyValue('--template-default-margin-bottom')) || 60,
    marginSide: parseInt(computedStyles.getPropertyValue('--template-default-margin-side')) || 60,
    lineHeight: parseFloat(computedStyles.getPropertyValue('--template-default-line-height')) || 1.6,
    paragraphSpacing: parseInt(computedStyles.getPropertyValue('--template-default-paragraph-spacing')) || 12
  }
}

// ================================
// 生命周期钩子
// ================================

let resizeObserver = null

onMounted(() => {
  nextTick(() => {
    console.log('🚀 初始化预览缩放功能')
    
    // 初始化缩放
    updateScale()
    
    // 读取模板默认值并发送给父组件
    const templateDefaults = getTemplateDefaults()
    console.log('📋 读取到的模板默认值:', templateDefaults)
    emit('template-defaults-loaded', templateDefaults)
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)
    
    // 使用ResizeObserver监听容器大小变化
    if (previewRef.value && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver((entries) => {
        console.log('📏 容器大小变化检测到:', entries[0]?.contentRect)
        updateScale()
      })
      resizeObserver.observe(previewRef.value)
      console.log('👀 已启动容器大小监听')
    } else {
      console.warn('⚠️ ResizeObserver 不可用，使用定时器作为备选方案')
      // 如果ResizeObserver不可用，使用定时器作为备选方案
      const timer = setInterval(() => {
        updateScale()
      }, 100)
      
      onUnmounted(() => {
        clearInterval(timer)
      })
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (resizeObserver) {
    resizeObserver.disconnect()
    console.log('🛑 已停止容器大小监听')
  }
})

// ================================
// 缩放相关方法
// ================================

/**
 * 更新缩放比例
 */
const updateScale = () => {
  if (!previewRef.value) {
    console.log('⚠️ 预览容器引用不存在')
    return
  }
  
  nextTick(() => {
    const newScale = calculateScale()
    const oldScale = scale.value
    scale.value = newScale
    console.log(`🔍 预览缩放比例更新: ${Math.round(oldScale * 100)}% → ${Math.round(newScale * 100)}%`)
  })
}

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  updateScale()
}

// ================================
// 自然流动分页方法
// ================================

/**
 * 处理内容高度变化
 */
const handleContentHeightChange = (height) => {
  console.log('📏 内容高度变化:', height)
  contentHeight.value = height
  updatePageBreakIndicators()
}

/**
 * 更新分页边界指示器
 */
const updatePageBreakIndicators = () => {
  const indicators = []
  const totalPagesCount = totalPages.value
  
  console.log('📄 计算分页指示器，总页数:', totalPagesCount)
  
  // 页面之间的分割线（显示提示文字和页码）
  for (let i = 1; i < totalPagesCount; i++) {
    const breakPosition = i * A4_HEIGHT - PAGE_PADDING
    indicators.push({
      pageNumber: i, // 显示分割线上面那一页的页码
      top: breakPosition,
      isVisible: true,
      isLastPage: false
    })
    
    console.log(`📍 第${i}页底部分割线位置:`, breakPosition, `显示页码: 第${i}页`)
  }
  
  // 最后一页底部的分割线（只显示页码，不显示提示文字）
  if (totalPagesCount > 0) {
    const lastPageBottomPosition = totalPagesCount * A4_HEIGHT - PAGE_PADDING - 30 // 距离底部30px
    indicators.push({
      pageNumber: totalPagesCount, // 显示最后一页的页码
      top: lastPageBottomPosition,
      isVisible: true,
      isLastPage: true
    })
    
    console.log(`📍 最后一页（第${totalPagesCount}页）底部分割线位置:`, lastPageBottomPosition)
  }
  
  pageBreakIndicators.value = indicators
}

// 旧的复杂分页逻辑已移除，改为自然流动方式

// 旧的分页检测和重分配逻辑已移除

// ================================
// 生命周期和监听
// ================================

// 监听数据变化，更新分页指示器
watch(() => currentResumeData.value, () => {
  console.log('📊 简历数据变化，准备更新分页指示器')
  // 等待DOM更新后重新计算分页
  nextTick(() => {
    setTimeout(() => {
      if (resumeRef.value) {
        const contentEl = resumeRef.value.querySelector('.continuous-resume-content')
        if (contentEl) {
          const height = contentEl.scrollHeight
          handleContentHeightChange(height)
        }
      }
    }, 100)
  })
}, { 
  deep: true, 
  immediate: true 
})

onMounted(() => {
  console.log('🚀 初始化自然流动分页系统...')
})

// ================================
// 方法定义（为后续模板组件预留）
// ================================

/**
 * 判断模块是否可见
 * @param {string} moduleId - 模块ID
 * @returns {boolean} 是否可见
 */
const isModuleVisible = (moduleId) => {
  return visibleModules.value.some(module => module.id === moduleId)
}

/**
 * 获取模块数据
 * @param {string} moduleId - 模块ID
 * @returns {Object} 模块数据
 */
const getModuleData = (moduleId) => {
  const module = visibleModules.value.find(m => m.id === moduleId)
  return module?.data || editorStore.getModuleData(moduleId) || {}
}

/**
 * 移动模块
 * @param {string} moduleId - 模块ID
 * @param {number} direction - 移动方向 (-1: 上移, 1: 下移)
 */
const moveModule = (moduleId, direction) => {
  console.log(`移动模块 ${moduleId}, 方向: ${direction}`)
  emit('module-order-change', { moduleId, direction })
}

/**
 * 删除模块
 * @param {string} moduleId - 模块ID
 */
const deleteModule = (moduleId) => {
  if (confirm(`确定要删除 ${moduleId} 模块吗？`)) {
    emit('module-delete', moduleId)
  }
}

// ================================
// 事件处理方法（与ResumeTemplate同步）
// ================================

/**
 * 处理基本信息更新
 * @param {Object} basicInfo - 新的基本信息
 */
const handleBasicInfoUpdate = (basicInfo) => {
  editorStore.updateModuleData('basic_info', basicInfo)
}

/**
 * 处理模块上移
 * @param {string} moduleType - 模块类型
 */
const handleModuleMoveUp = (moduleType) => {
  editorStore.moveModule(moduleType, -1)
  emit('module-order-change', { moduleType, direction: -1 })
}

/**
 * 处理模块下移
 * @param {string} moduleType - 模块类型
 */
const handleModuleMoveDown = (moduleType) => {
  editorStore.moveModule(moduleType, 1)
  emit('module-order-change', { moduleType, direction: 1 })
}

/**
 * 处理模块删除
 * @param {string} moduleType - 模块类型
 */
const handleModuleDelete = (moduleType) => {
  editorStore.deleteModule(moduleType)
  emit('module-delete', moduleType)
}

/**
 * 处理技能更新
 * @param {Array} skills - 新的技能列表
 */
const handleSkillUpdate = (skills) => {
  console.log('🔄 处理技能更新:', skills)
  // 确保传递正确的数据格式：{ skills: [...] }
  const skillsData = { skills: skills }
  editorStore.updateModuleData('skills', skillsData)
}

/**
 * 处理工作经历更新
 * @param {Array} experiences - 新的工作经历列表
 */
const handleExperienceUpdate = (experiences) => {
  editorStore.updateModuleData('work', experiences)
}

/**
 * 处理教育经历更新
 * @param {Array} educations - 新的教育经历列表
 */
const handleEducationUpdate = (educations) => {
  editorStore.updateModuleData('education', { education: educations })
}

/**
 * 处理项目经历更新
 * @param {Array} projects - 新的项目经历列表
 */
const handleProjectUpdate = (projects) => {
  editorStore.updateModuleData('project', { projects: projects })
}

/**
 * 处理内容溢出
 */
const handleContentOverflow = (overflowData) => {
  console.log('内容溢出:', overflowData)
  // 这里可以实现更复杂的分页逻辑
}
</script>

<style scoped>
.resume-preview {
  @apply h-full w-full;
}

.preview-container {
  @apply h-full overflow-auto;
  background-color: #f9fafb; /* 很浅的灰色背景，突显A4纸张 */
  padding: 12px;
  
  /* 确保缩放后内容始终居中且可见 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  
  /* 确保有足够的空间容纳缩放后的内容 */
  min-width: 0;
  box-sizing: border-box;
}

/* 自然流动容器 */
.natural-flow-container {
  @apply relative;
  width: 794px; /* 固定A4宽度（210mm = 794px at 96 DPI） */
  /* min-height 由JavaScript动态计算 */
  text-align: left; /* 恢复内容的左对齐 */
  
  /* 外层边界提示 - 只保留蓝色虚线边框 */
  border: 1px dashed rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  
  /* 缩放相关样式 - 确保始终居中且不超出边界 */
  transition: transform 0.2s ease-out;
  flex-shrink: 0; /* 防止容器被压缩 */
  
  /* 确保缩放原点为中心，避免偏移 */
  transform-box: border-box;
}

/* 缩放包装器 */
.scale-wrapper {
  @apply w-full flex justify-center;
  /* 确保缩放后的内容始终居中 */
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  min-height: 100%;
  overflow: visible;
}

/* A4边界标识 */
.a4-boundary-label {
  @apply absolute z-30 text-xs px-2 rounded-b;
  background-color: #d1d5db; /* 和分割线一样的灰色 */
  color: #6b7280; /* 深灰色文字 */
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
  height: 15px; /* 缩小四分之一，从20px -> 15px */
  line-height: 15px; /* 调整行高保持文字居中 */
}

/* 连续的简历内容 */
.continuous-resume-content {
  @apply relative z-10 bg-white;
  
  /* A4纸张标准尺寸 */
  width: 794px; /* 210mm = 794px at 96 DPI */
  /* min-height 由JavaScript动态计算，确保显示完整页面 */
  padding: 0; /* 移除硬编码padding，由简历模板的CSS变量控制边距 */
  box-sizing: border-box;
  
  /* 增强的A4纸张边界效果 - 只保留阴影，去掉灰色边框 */
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  
  /* 打印样式 */
  @media print {
    @apply shadow-none;
    border: none;
    margin: 0;
    max-width: none;
    width: 210mm;
    padding: 40px;
  }
}



/* 分页边界指示器 */
.page-break-indicators {
  @apply absolute inset-0 z-20 pointer-events-none;
}

.page-break-indicator {
  @apply absolute w-full pointer-events-none; /* 改为pointer-events-none，不阻挡交互 */
  left: 0;
  right: 0;
  transition: all 0.2s ease;
}

.break-line {
  @apply w-full relative flex items-center justify-between;
  height: 15px; /* 减少四分之一高度：20px -> 15px */
  background-color: #d1d5db; /* 纯浅灰色，不用渐变 */
  transition: all 0.2s ease;
}

.break-text-left {
  @apply absolute left-4 font-medium text-gray-600;
  font-size: 10px; /* 更小的字体 */
  white-space: nowrap;
}

.break-text-right {
  @apply absolute right-4 font-medium text-gray-600;
  font-size: 10px; /* 更小的字体 */
  white-space: nowrap;
}



/* 内容的分页效果（仅用于预览，实际分页由后端处理） */
.continuous-resume-content :deep(.resume-template) {
  /* 允许内容自然流动，不强制分页 */
  height: auto;
  min-height: auto;
}

/* 在指定位置添加视觉分页提示 */
.page-break-helper {
  @apply relative;
}

.page-break-helper::after {
  @apply absolute inset-x-0 h-px bg-red-400 opacity-30;
  content: '';
  top: calc(297mm - 60px); /* A4高度减去内边距 */
}

/* 打印时的真实分页 */
@media print {
  .page-break-indicators {
    display: none !important;
  }
  
  .continuous-resume-content {
    /* 让内容自然流动到多页 */
    page-break-inside: auto;
  }
  
  /* 每297mm高度强制分页 */
  .continuous-resume-content :deep(*) {
    page-break-inside: avoid;
  }
}
</style> 