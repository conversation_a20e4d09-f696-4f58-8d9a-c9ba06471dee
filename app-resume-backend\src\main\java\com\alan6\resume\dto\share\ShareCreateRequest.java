package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 创建分享请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareCreateRequest", description = "创建分享请求")
public class ShareCreateRequest {

    /**
     * 分享类型（必填，1:公开，2:密码保护，3:仅链接访问，4:指定用户）
     */
    @Schema(description = "分享类型（1:公开，2:密码保护，3:仅链接访问，4:指定用户）", example = "1", required = true)
    @NotNull(message = "分享类型不能为空")
    @Min(value = 1, message = "分享类型必须在1-4之间")
    @Max(value = 4, message = "分享类型必须在1-4之间")
    private Byte shareType;

    /**
     * 过期时间（可选，格式：yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "过期时间", example = "2024-12-31 23:59:59")
    private LocalDateTime expireTime;

    /**
     * 访问密码（shareType为2或4时必填）
     */
    @Schema(description = "访问密码（shareType为2或4时必填）", example = "123456")
    private String password;

    /**
     * 是否允许下载（可选，默认true）
     */
    @Schema(description = "是否允许下载", example = "true")
    private Boolean allowDownload = true;

    /**
     * 访问次数限制（可选，-1表示无限制）
     */
    @Schema(description = "访问次数限制（-1表示无限制）", example = "100")
    private Integer viewLimit;

    /**
     * 分享范围（可选，1:公开，2:仅链接，3:指定域名，默认1）
     */
    @Schema(description = "分享范围（1:公开，2:仅链接，3:指定域名）", example = "1")
    @Min(value = 1, message = "分享范围必须在1-3之间")
    @Max(value = 3, message = "分享范围必须在1-3之间")
    private Byte shareScope = 1;

    /**
     * 允许访问的域名白名单（shareScope为3时必填）
     */
    @Schema(description = "允许访问的域名白名单", example = "[\"example.com\", \"*.company.com\"]")
    private List<String> allowedDomains;

    /**
     * 分享描述（可选）
     */
    @Schema(description = "分享描述", example = "我的简历分享")
    private String description;
} 