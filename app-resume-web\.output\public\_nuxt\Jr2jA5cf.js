import{_ as p}from"./DlAUqK2U.js";import{r as a,c as u,a as e,F as y,g as m,t as c,o as r,s as _,d as g}from"./CURHyiUL.js";const C={class:"test-page"},f={class:"test-section"},x={class:"test-section"},h={class:"field-options"},F=["onClick"],O={class:"option-icon"},B={class:"test-section"},N={__name:"click-test",setup($){const n=a(0),o=a([]),d=[{key:"wechat",label:"微信号"},{key:"website",label:"个人网站"},{key:"github",label:"GitHub"},{key:"custom",label:"自定义"}],i=t=>{n.value++,console.log("按钮点击:",t,"点击次数:",n.value)},k=t=>{console.log("选项按钮点击:",t),t==="custom"?console.log("自定义按钮点击"):v(t)},v=t=>{const s=o.value.indexOf(t);s>-1?(o.value.splice(s,1),console.log("移除字段:",t)):(o.value.push(t),console.log("添加字段:",t))},b=t=>t==="custom"?"+":o.value.includes(t)?"✓":"+";return(t,s)=>(r(),u("div",C,[s[5]||(s[5]=e("h1",null,"按钮点击测试",-1)),e("div",f,[s[2]||(s[2]=e("h2",null,"基本按钮测试",-1)),e("button",{onClick:s[0]||(s[0]=l=>i("basic"))},"基本按钮"),e("button",{onClick:s[1]||(s[1]=l=>i("styled")),class:"styled-btn"},"样式按钮")]),e("div",x,[s[3]||(s[3]=e("h2",null,"选项按钮测试",-1)),e("div",h,[(r(),u(y,null,m(d,l=>e("button",{key:l.key,type:"button",class:_(["option-btn",{active:o.value.includes(l.key)&&l.key!=="custom","custom-btn":l.key==="custom"}]),onClick:w=>k(l.key)},[e("span",O,c(b(l.key)),1),g(" "+c(l.label),1)],10,F)),64))])]),e("div",B,[s[4]||(s[4]=e("h2",null,"调试信息",-1)),e("pre",null,c(JSON.stringify({activeFields:o.value,clickCount:n.value},null,2)),1)])]))}},T=p(N,[["__scopeId","data-v-f1d914fe"]]);export{T as default};
