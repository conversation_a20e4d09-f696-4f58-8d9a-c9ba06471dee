/**
 * HTML转换服务
 * 提供HTML模板转Vue组件的完整功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */

import { ref, reactive, computed } from 'vue'
import { useApiConfig } from '~/composables/shared/useApiConfig'
import { useMessage } from '~/utils/message'

export const useHtmlConverterService = () => {
  const { getApiUrl } = useApiConfig()
  const message = useMessage()
  
  // 获取认证Token
  const getAuthToken = () => {
    if (process.client) {
      return localStorage.getItem('admin_token') || localStorage.getItem('token')
    }
    return null
  }
  
  // 创建认证请求头
  const getAuthHeaders = () => {
    const token = getAuthToken()
    return token ? { 'Authorization': `Bearer ${token}` } : {}
  }
  
  // 响应式状态
  const state = reactive({
    // 上传状态
    uploading: false,
    uploadProgress: 0,
    uploadedFiles: [],
    
    // 验证状态
    validating: false,
    validationResults: [],
    
    // 转换状态
    converting: false,
    conversionResults: null,
    
    // 任务信息
    taskId: null,
    taskDirectory: null,
    
    // 统计信息
    statistics: {
      totalFiles: 0,
      validFiles: 0,
      invalidFiles: 0,
      totalErrors: 0,
      totalWarnings: 0,
      validationRate: 0
    }
  })
  
  // 计算属性
  const hasUploadedFiles = computed(() => state.uploadedFiles.length > 0)
  const hasValidationResults = computed(() => state.validationResults.length > 0)
  const hasConversionResults = computed(() => state.conversionResults !== null)
  const isProcessing = computed(() => state.uploading || state.validating || state.converting)
  
  /**
   * 上传HTML文件
   * 
   * @param {FileList} files - 文件列表
   * @returns {Promise<Object>} 上传结果
   */
  const uploadHtmlFiles = async (files) => {
    console.log('开始上传HTML文件，数量:', files.length)
    
    try {
      state.uploading = true
      state.uploadProgress = 0
      
      // 创建FormData
      const formData = new FormData()
      Array.from(files).forEach(file => {
        formData.append('files', file)
      })
      
      // 发送上传请求
      const response = await $fetch('/admin/html-converter/upload', {
        method: 'POST',
        body: formData,
        headers: getAuthHeaders(),
        onUploadProgress: (progress) => {
          state.uploadProgress = Math.round((progress.loaded / progress.total) * 100)
        }
      })
      
      if (response.code === 200) {
        // 更新状态
        state.taskId = response.data.taskId
        state.uploadedFiles = response.data.uploadedFiles
        state.validationResults = response.data.validationResults
        state.statistics = response.data.statistics
        
        message.success(`成功上传 ${state.uploadedFiles.length} 个文件`)
        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '上传失败')
      }
      
    } catch (error) {
      console.error('上传HTML文件失败:', error)
      message.error('上传失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    } finally {
      state.uploading = false
      state.uploadProgress = 0
    }
  }
  
  /**
   * 验证HTML文件
   * 
   * @param {string} taskId - 任务ID
   * @param {string[]} fileNames - 文件名列表
   * @returns {Promise<Object>} 验证结果
   */
  const validateHtmlFiles = async (taskId, fileNames) => {
    console.log('开始验证HTML文件:', fileNames)
    
    try {
      state.validating = true
      
      const response = await $fetch('/admin/html-converter/validate', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: {
          taskId,
          fileNames
        }
      })
      
      if (response.code === 200) {
        state.validationResults = response.data
        
        // 更新统计信息
        updateValidationStatistics(response.data)
        
        const validCount = response.data.filter(r => r.valid).length
        const invalidCount = response.data.length - validCount
        
        if (invalidCount > 0) {
          message.warning(`验证完成：${validCount} 个通过，${invalidCount} 个失败`)
        } else {
          message.success(`验证完成：全部 ${validCount} 个文件通过`)
        }
        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '验证失败')
      }
      
    } catch (error) {
      console.error('验证HTML文件失败:', error)
      message.error('验证失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    } finally {
      state.validating = false
    }
  }
  
  /**
   * 转换HTML文件为Vue组件
   * 
   * @param {Object} request - 转换请求
   * @returns {Promise<Object>} 转换结果
   */
  const convertHtmlFiles = async (request) => {
    console.log('开始转换HTML文件:', request)
    
    try {
      state.converting = true
      
      const response = await $fetch('/admin/html-converter/convert', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: request
      })
      
      if (response.code === 200) {
        state.conversionResults = response.data
        
        const stats = response.data.statistics
        if (stats.failedCount > 0) {
          message.warning(`转换完成：${stats.successCount} 个成功，${stats.failedCount} 个失败`)
        } else {
          message.success(`转换完成：全部 ${stats.successCount} 个文件成功`)
        }
        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '转换失败')
      }
      
    } catch (error) {
      console.error('转换HTML文件失败:', error)
      message.error('转换失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    } finally {
      state.converting = false
    }
  }
  
  /**
   * 单个文件转换
   * 
   * @param {string} taskId - 任务ID
   * @param {string} fileName - 文件名
   * @returns {Promise<Object>} 转换结果
   */
  const convertSingleFile = async (taskId, fileName) => {
    console.log('开始转换单个文件:', fileName)
    
    try {
      state.converting = true
      
      const response = await $fetch('/admin/html-converter/convert/single', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: {
          taskId,
          fileName
        }
      })
      
      if (response.code === 200) {
        state.conversionResults = response.data
        message.success(`文件 ${fileName} 转换成功`)
        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '转换失败')
      }
      
    } catch (error) {
      console.error('转换单个文件失败:', error)
      message.error('转换失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    } finally {
      state.converting = false
    }
  }
  
  /**
   * 批量文件转换
   * 
   * @param {string} taskId - 任务ID
   * @param {string[]} fileNames - 文件名列表
   * @returns {Promise<Object>} 转换结果
   */
  const convertBatchFiles = async (taskId, fileNames) => {
    console.log('开始批量转换文件:', fileNames)
    
    try {
      state.converting = true
      
      const response = await $fetch('/admin/html-converter/convert/batch', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: {
          taskId,
          fileNames
        }
      })
      
      if (response.code === 200) {
        state.conversionResults = response.data
        
        const stats = response.data.statistics
        message.success(`批量转换完成：${stats.successCount} 个成功，${stats.failedCount} 个失败`)
        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '转换失败')
      }
      
    } catch (error) {
      console.error('批量转换文件失败:', error)
      message.error('转换失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    } finally {
      state.converting = false
    }
  }
  
  /**
   * 获取转换结果
   * 
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} 转换结果信息
   */
  const getConversionResult = async (taskId) => {
    console.log('获取转换结果:', taskId)
    
    try {
      const response = await $fetch(`/admin/html-converter/result/${taskId}`, {
        method: 'GET',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        state.taskDirectory = response.data.taskDirectory
        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '获取结果失败')
      }
      
    } catch (error) {
      console.error('获取转换结果失败:', error)
      message.error('获取结果失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 获取支持的模块ID列表
   * 
   * @returns {Promise<Object>} 模块ID列表
   */
  const getSupportedModules = async () => {
    try {
      const response = await $fetch('/admin/html-converter/modules', {
        method: 'GET',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '获取失败')
      }
      
    } catch (error) {
      console.error('获取支持的模块ID失败:', error)
      
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 获取支持的字段名称列表
   * 
   * @returns {Promise<Object>} 字段名称列表
   */
  const getSupportedFields = async () => {
    try {
      const response = await $fetch('/admin/html-converter/fields', {
        method: 'GET',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '获取失败')
      }
      
    } catch (error) {
      console.error('获取支持的字段名称失败:', error)
      
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 生成验证报告
   * 
   * @param {string} taskId - 任务ID
   * @param {string[]} fileNames - 文件名列表
   * @returns {Promise<Object>} 验证报告
   */
  const generateValidationReport = async (taskId, fileNames) => {
    console.log('生成验证报告:', fileNames)
    
    try {
      const response = await $fetch('/admin/html-converter/report', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: {
          taskId,
          fileNames
        }
      })
      
      if (response.code === 200) {
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '生成报告失败')
      }
      
    } catch (error) {
      console.error('生成验证报告失败:', error)
      message.error('生成报告失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 下载转换结果
   * 
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} 下载结果
   */
  const downloadResults = async (taskId) => {
    console.log('开始下载转换结果:', taskId)
    
    try {
      // 先测试接口是否可访问
      console.log('1. 测试接口连通性...')
      const testResponse = await $fetch(`/admin/html-converter/test-download/${taskId}`, {
        method: 'GET',
        headers: getAuthHeaders()
      })
      
      console.log('测试接口响应:', testResponse)
      
      if (testResponse.code !== 200) {
        throw new Error('测试接口调用失败')
      }
      
      console.log('2. 开始下载文件...')
      
      // 调用真正的下载接口
      const response = await fetch(`/admin/html-converter/result/${taskId}/download`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      })
      
      console.log('下载响应状态:', response.status, response.statusText)
      console.log('响应头:', Object.fromEntries(response.headers.entries()))
      
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`)
      }
      
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let fileName = `conversion_results_${taskId}.zip`
      if (contentDisposition) {
        console.log('Content-Disposition:', contentDisposition)
        const matches = contentDisposition.match(/filename="([^"]+)"/)
        if (matches && matches[1]) {
          fileName = matches[1]
        }
      }
      
      console.log('3. 处理文件下载, 文件名:', fileName)
      
      // 创建Blob并下载
      const blob = await response.blob()
      console.log('Blob大小:', blob.size, 'bytes')
      
      if (blob.size === 0) {
        throw new Error('下载的文件为空')
      }
      
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      a.style.display = 'none'
      
      // 添加到DOM并触发下载
      document.body.appendChild(a)
      console.log('4. 触发下载...')
      a.click()
      
      // 延迟清理，确保下载开始
      setTimeout(() => {
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
        console.log('5. 清理完成')
      }, 100)
      
      // 备用方案：如果自动下载失败，提供手动下载链接
      setTimeout(() => {
        // 检查是否真的开始了下载
        console.log('6. 检查下载状态...')
        message.info('如果没有自动下载，请检查浏览器的下载设置或弹窗拦截')
      }, 2000)
      
      message.success(`转换结果下载完成: ${fileName}`)
      
      return {
        success: true,
        fileName,
        fileSize: blob.size
      }
      
    } catch (error) {
      console.error('下载转换结果失败:', error)
      message.error('下载失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 清理任务
   * 
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} 清理结果
   */
  const cleanupTask = async (taskId) => {
    console.log('清理任务:', taskId)
    
    try {
      const response = await $fetch(`/admin/html-converter/task/${taskId}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        message.success('任务清理成功')
        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '清理失败')
      }
      
    } catch (error) {
      console.error('清理任务失败:', error)
      message.error('清理失败: ' + error.message)
      
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 重置状态
   */
  const resetState = () => {
    state.uploading = false
    state.uploadProgress = 0
    state.uploadedFiles = []
    state.validating = false
    state.validationResults = []
    state.converting = false
    state.conversionResults = null
    state.taskId = null
    state.taskDirectory = null
    state.statistics = {
      totalFiles: 0,
      validFiles: 0,
      invalidFiles: 0,
      totalErrors: 0,
      totalWarnings: 0,
      validationRate: 0
    }
  }
  
  /**
   * 更新验证统计信息
   * 
   * @param {Array} validationResults - 验证结果列表
   */
  const updateValidationStatistics = (validationResults) => {
    const totalFiles = validationResults.length
    const validFiles = validationResults.filter(r => r.valid).length
    const invalidFiles = totalFiles - validFiles
    const totalErrors = validationResults.reduce((sum, r) => sum + (r.errorCount || 0), 0)
    const totalWarnings = validationResults.reduce((sum, r) => sum + (r.warningCount || 0), 0)
    const validationRate = totalFiles > 0 ? (validFiles / totalFiles) * 100 : 0
    
    state.statistics = {
      totalFiles,
      validFiles,
      invalidFiles,
      totalErrors,
      totalWarnings,
      validationRate: Math.round(validationRate * 100) / 100
    }
  }
  
  /**
   * 创建转换请求对象
   * 
   * @param {string} type - 转换类型 ('single' | 'batch')
   * @param {string[]} fileNames - 文件名列表
   * @param {Object} options - 转换选项
   * @param {string} taskId - 任务ID
   * @returns {Object} 转换请求对象
   */
  const createConversionRequest = (type, fileNames, options = {}, taskId = null) => {
    return {
      taskId: taskId || state.taskId,
      conversionType: type,
      htmlFiles: fileNames,
      options: {
        validateHtml: options.validateHtml !== false,
        generateFullComponent: options.generateFullComponent !== false,
        preserveComments: options.preserveComments || false,
        formatOutput: options.formatOutput !== false,
        generateTypes: options.generateTypes || false,
        targetVueVersion: options.targetVueVersion || 'vue3',
        cssProcessing: options.cssProcessing || 'scoped',
        strictMode: options.strictMode || false,
        ...options
      }
    }
  }
  
  /**
   * 验证文件类型
   * 
   * @param {FileList} files - 文件列表
   * @returns {Object} 验证结果
   */
  const validateFileTypes = (files) => {
    const validFiles = []
    const invalidFiles = []
    
    Array.from(files).forEach(file => {
      if (file.name.toLowerCase().endsWith('.html')) {
        validFiles.push(file)
      } else {
        invalidFiles.push(file)
      }
    })
    
    return {
      valid: invalidFiles.length === 0,
      validFiles,
      invalidFiles,
      message: invalidFiles.length > 0 
        ? `发现 ${invalidFiles.length} 个非HTML文件，将被忽略`
        : '所有文件都是有效的HTML文件'
    }
  }
  
  return {
    // 状态
    state: readonly(state),
    
    // 计算属性
    hasUploadedFiles,
    hasValidationResults,
    hasConversionResults,
    isProcessing,
    
    // 方法
    uploadHtmlFiles,
    validateHtmlFiles,
    convertHtmlFiles,
    convertSingleFile,
    convertBatchFiles,
    getConversionResult,
    getSupportedModules,
    getSupportedFields,
    generateValidationReport,
    downloadResults,
    cleanupTask,
    resetState,
    createConversionRequest,
    validateFileTypes
  }
} 