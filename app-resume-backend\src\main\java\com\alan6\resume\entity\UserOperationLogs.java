package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("user_operation_logs")
@Schema(name = "UserOperationLogs对象", description = "用户操作日志表")
public class UserOperationLogs implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "日志ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "操作类型")
    private String operationType;

    @Schema(description = "操作模块")
    private String operationModule;

    @Schema(description = "目标对象类型")
    private String targetType;

    @Schema(description = "目标对象ID")
    private Long targetId;

    @Schema(description = "操作描述")
    private String operationDesc;

    @Schema(description = "操作相关数据")
    private String operationData;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "用户代理")
    private String userAgent;

    @Schema(description = "操作平台")
    private String platform;

    @Schema(description = "是否删除")
    private Byte isDeleted;

    @Schema(description = "操作时间")
    private LocalDateTime createTime;
}
