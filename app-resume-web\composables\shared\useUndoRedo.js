/**
 * 撤销重做功能
 * @description 管理编辑器的撤销重做操作历史
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed } from 'vue'

/**
 * 撤销重做管理Hook
 * @param {Ref} targetData - 目标数据的ref
 * @param {number} maxHistory - 最大历史记录数量
 * @returns {Object} 撤销重做方法
 */
export const useUndoRedo = (targetData, maxHistory = 50) => {
  // ================================
  // 响应式数据
  // ================================
  
  // 历史记录栈
  const history = ref([])
  
  // 当前历史位置
  const currentIndex = ref(-1)
  
  // ================================
  // 计算属性
  // ================================
  
  /**
   * 是否可以撤销
   */
  const canUndo = computed(() => {
    return currentIndex.value > 0
  })
  
  /**
   * 是否可以重做
   */
  const canRedo = computed(() => {
    return currentIndex.value < history.value.length - 1
  })
  
  // ================================
  // 核心方法
  // ================================
  
  /**
   * 保存当前状态到历史记录
   * @param {Object} state - 要保存的状态，如果不传则使用当前targetData
   */
  const saveState = (state = null) => {
    try {
      const stateToSave = state || deepClone(targetData.value)
      
      // 如果当前不在历史记录的末尾，删除后面的记录
      if (currentIndex.value < history.value.length - 1) {
        history.value = history.value.slice(0, currentIndex.value + 1)
      }
      
      // 添加新状态
      history.value.push(stateToSave)
      currentIndex.value = history.value.length - 1
      
      // 限制历史记录数量
      if (history.value.length > maxHistory) {
        history.value.shift()
        currentIndex.value = Math.max(0, currentIndex.value - 1)
      }
      
      console.log(`状态已保存，当前历史位置: ${currentIndex.value}`)
    } catch (error) {
      console.error('保存状态失败:', error)
    }
  }
  
  /**
   * 撤销操作
   * @returns {Object|null} 撤销后的状态
   */
  const undo = () => {
    if (!canUndo.value) {
      console.warn('无法撤销：已到达历史记录开始')
      return null
    }
    
    try {
      currentIndex.value -= 1
      const previousState = deepClone(history.value[currentIndex.value])
      
      console.log(`撤销操作，回到历史位置: ${currentIndex.value}`)
      return previousState
    } catch (error) {
      console.error('撤销操作失败:', error)
      return null
    }
  }
  
  /**
   * 重做操作
   * @returns {Object|null} 重做后的状态
   */
  const redo = () => {
    if (!canRedo.value) {
      console.warn('无法重做：已到达历史记录末尾')
      return null
    }
    
    try {
      currentIndex.value += 1
      const nextState = deepClone(history.value[currentIndex.value])
      
      console.log(`重做操作，前进到历史位置: ${currentIndex.value}`)
      return nextState
    } catch (error) {
      console.error('重做操作失败:', error)
      return null
    }
  }
  
  /**
   * 重置历史记录
   * @param {Object} initialState - 初始状态
   */
  const reset = (initialState = null) => {
    try {
      const stateToReset = initialState || deepClone(targetData.value)
      
      history.value = [stateToReset]
      currentIndex.value = 0
      
      console.log('历史记录已重置')
    } catch (error) {
      console.error('重置历史记录失败:', error)
    }
  }
  
  /**
   * 清空历史记录
   */
  const clear = () => {
    history.value = []
    currentIndex.value = -1
    console.log('历史记录已清空')
  }
  
  /**
   * 获取历史记录统计信息
   * @returns {Object} 统计信息
   */
  const getStats = () => {
    return {
      totalHistory: history.value.length,
      currentPosition: currentIndex.value + 1,
      canUndo: canUndo.value,
      canRedo: canRedo.value,
      undoCount: currentIndex.value,
      redoCount: history.value.length - 1 - currentIndex.value
    }
  }
  
  /**
   * 跳转到指定历史位置
   * @param {number} index - 目标位置索引
   * @returns {Object|null} 目标状态
   */
  const jumpTo = (index) => {
    if (index < 0 || index >= history.value.length) {
      console.warn(`无效的历史位置: ${index}`)
      return null
    }
    
    try {
      currentIndex.value = index
      const targetState = deepClone(history.value[index])
      
      console.log(`跳转到历史位置: ${index}`)
      return targetState
    } catch (error) {
      console.error('跳转历史位置失败:', error)
      return null
    }
  }
  
  // ================================
  // 自动保存功能
  // ================================
  
  /**
   * 创建自动保存器
   * @param {number} interval - 保存间隔（毫秒）
   * @returns {Object} 自动保存控制器
   */
  const createAutoSaver = (interval = 30000) => {
    let autoSaveTimer = null
    let lastSavedState = null
    
    const start = () => {
      if (autoSaveTimer) {
        stop()
      }
      
      autoSaveTimer = setInterval(() => {
        const currentState = JSON.stringify(targetData.value)
        if (lastSavedState !== currentState) {
          saveState()
          lastSavedState = currentState
          console.log('自动保存状态')
        }
      }, interval)
      
      console.log(`自动保存已启动，间隔: ${interval}ms`)
    }
    
    const stop = () => {
      if (autoSaveTimer) {
        clearInterval(autoSaveTimer)
        autoSaveTimer = null
        console.log('自动保存已停止')
      }
    }
    
    return { start, stop }
  }
  
  // ================================
  // 返回方法和状态
  // ================================
  return {
    // 状态
    history: readonly(history),
    currentIndex: readonly(currentIndex),
    canUndo,
    canRedo,
    
    // 核心方法
    saveState,
    undo,
    redo,
    reset,
    clear,
    
    // 工具方法
    getStats,
    jumpTo,
    createAutoSaver
  }
}

// ================================
// 工具函数
// ================================

/**
 * 深度克隆对象
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 只读包装器
 * @param {Ref} ref - 响应式引用
 * @returns {ComputedRef} 只读的计算属性
 */
function readonly(ref) {
  return computed(() => ref.value)
} 