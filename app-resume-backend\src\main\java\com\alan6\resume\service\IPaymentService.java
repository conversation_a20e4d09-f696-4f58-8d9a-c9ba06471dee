package com.alan6.resume.service;

import com.alan6.resume.dto.payment.*;

/**
 * 支付服务接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface IPaymentService {

    /**
     * 获取支付方式列表
     * 
     * @return 支付方式列表
     */
    PaymentMethodResponse getPaymentMethods();

    /**
     * 创建支付订单
     * 
     * @param request 创建支付订单请求
     * @return 支付订单信息
     */
    PaymentCreateResponse createPayment(PaymentCreateRequest request);

    /**
     * 查询支付状态
     * 
     * @param orderNo 订单号
     * @return 支付状态信息
     */
    PaymentStatusResponse getPaymentStatus(String orderNo);

    /**
     * 处理微信支付回调
     * 
     * @param callbackData 回调数据
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param serialNo 证书序列号
     * @return 处理结果
     */
    Boolean handleWechatCallback(String callbackData, String signature, String timestamp, String nonce, String serialNo);

    /**
     * 处理支付宝支付回调
     * 
     * @param callbackParams 回调参数
     * @return 处理结果
     */
    Boolean handleAlipayCallback(java.util.Map<String, String> callbackParams);

    /**
     * 申请退款
     * 
     * @param orderNo 订单号
     * @param request 退款请求
     * @return 退款响应
     */
    RefundResponse applyRefund(String orderNo, RefundRequest request);

    /**
     * 查询退款状态
     * 
     * @param refundId 退款ID
     * @return 退款状态信息
     */
    RefundStatusResponse getRefundStatus(String refundId);

    /**
     * 验证支付签名
     * 
     * @param paymentMethod 支付方式
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 原始数据
     * @param serialNo 证书序列号（微信支付需要）
     * @return 验证结果
     */
    SignatureVerifyResponse verifySignature(String paymentMethod, String signature, String timestamp, 
                                          String nonce, String body, String serialNo);
} 