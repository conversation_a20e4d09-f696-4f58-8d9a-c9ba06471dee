/**
 * 简历动画组合式函数
 * @description 处理各种动画效果，提升用户体验
 */

import { nextTick } from 'vue'

export function useResumeAnimations() {
  /**
   * 添加淡入动画
   * @param {HTMLElement} element - 目标元素
   * @param {number} delay - 延迟时间（毫秒）
   */
  const addFadeInAnimation = (element, delay = 0) => {
    if (!element) return
    
    element.style.opacity = '0'
    element.style.transform = 'translateY(20px)'
    element.style.transition = 'all 0.6s ease-out'
    
    setTimeout(() => {
      element.style.opacity = '1'
      element.style.transform = 'translateY(0)'
    }, delay)
  }
  
  /**
   * 添加缩放动画
   * @param {HTMLElement} element - 目标元素
   * @param {number} scale - 缩放比例
   * @param {number} duration - 动画时长（毫秒）
   */
  const addScaleAnimation = (element, scale = 1.05, duration = 200) => {
    if (!element) return
    
    const originalTransform = element.style.transform
    element.style.transition = `transform ${duration}ms ease-out`
    element.style.transform = `${originalTransform} scale(${scale})`
    
    setTimeout(() => {
      element.style.transform = originalTransform
    }, duration)
  }
  
  /**
   * 添加震动动画
   * @param {HTMLElement} element - 目标元素
   */
  const addShakeAnimation = (element) => {
    if (!element) return
    
    element.classList.add('animate-shake')
    setTimeout(() => {
      element.classList.remove('animate-shake')
    }, 500)
  }
  
  /**
   * 添加滑入动画
   * @param {HTMLElement} element - 目标元素
   * @param {string} direction - 滑入方向 ('left', 'right', 'up', 'down')
   */
  const addSlideInAnimation = (element, direction = 'up') => {
    if (!element) return
    
    const transformMap = {
      left: 'translateX(-100%)',
      right: 'translateX(100%)',
      up: 'translateY(-100%)',
      down: 'translateY(100%)'
    }
    
    element.style.transform = transformMap[direction] || transformMap.up
    element.style.transition = 'transform 0.4s ease-out'
    
    nextTick(() => {
      element.style.transform = 'translate(0, 0)'
    })
  }
  
  /**
   * 高亮动画
   * @param {HTMLElement} element - 目标元素
   * @param {string} color - 高亮颜色
   */
  const addHighlightAnimation = (element, color = '#3b82f6') => {
    if (!element) return
    
    const originalBg = element.style.backgroundColor
    element.style.transition = 'background-color 0.3s ease-out'
    element.style.backgroundColor = `${color}20` // 20% 透明度
    
    setTimeout(() => {
      element.style.backgroundColor = originalBg
    }, 300)
  }
  
  /**
   * 删除动画
   * @param {HTMLElement} element - 目标元素
   * @param {Function} callback - 动画完成回调
   */
  const addDeleteAnimation = (element, callback) => {
    if (!element) return
    
    element.style.transition = 'all 0.3s ease-out'
    element.style.opacity = '0'
    element.style.transform = 'scale(0.8) translateX(-20px)'
    element.style.height = '0'
    element.style.marginTop = '0'
    element.style.marginBottom = '0'
    element.style.paddingTop = '0'
    element.style.paddingBottom = '0'
    
    setTimeout(() => {
      if (callback) callback()
    }, 300)
  }
  
  /**
   * 拖拽反馈动画
   * @param {HTMLElement} element - 目标元素
   * @param {boolean} isDragging - 是否正在拖拽
   */
  const addDragFeedback = (element, isDragging) => {
    if (!element) return
    
    if (isDragging) {
      element.style.transform = 'rotate(2deg) scale(1.02)'
      element.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)'
      element.style.zIndex = '1000'
    } else {
      element.style.transform = ''
      element.style.boxShadow = ''
      element.style.zIndex = ''
    }
  }
  
  return {
    addFadeInAnimation,
    addScaleAnimation,
    addShakeAnimation,
    addSlideInAnimation,
    addHighlightAnimation,
    addDeleteAnimation,
    addDragFeedback
  }
} 