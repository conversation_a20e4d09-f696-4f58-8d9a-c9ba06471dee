<template>
  <Teleport to="body">
    <Transition name="modal-fade">
      <div 
        v-if="isVisible" 
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
        @click="closeModal"
      >
        <Transition name="modal-scale">
          <div 
            v-if="isVisible"
            class="relative w-full max-w-4xl mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden"
            @click.stop
          >
            <!-- 关闭按钮 -->
            <button
              @click="closeModal"
              class="absolute top-4 right-4 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-white/80 hover:bg-white transition-all duration-200 text-secondary-500 hover:text-secondary-700"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>

            <div class="flex min-h-[500px] md:min-h-[600px]">
              <!-- 左侧品牌区域 - 仅在大屏幕显示 -->
              <div class="hidden md:flex md:w-2/5 bg-gradient-to-br from-primary-500 via-primary-600 to-orange-600 relative overflow-hidden">
                <!-- 装饰性几何图形 -->
                <div class="absolute inset-0 opacity-10">
                  <div class="absolute top-10 left-10 w-32 h-32 border-2 border-white rounded-full"></div>
                  <div class="absolute top-32 right-16 w-20 h-20 bg-white/20 rounded-lg rotate-45"></div>
                  <div class="absolute bottom-20 left-20 w-16 h-16 bg-white/15 rounded-full"></div>
                  <div class="absolute bottom-32 right-32 w-24 h-24 border border-white rounded-lg rotate-12"></div>
                </div>

                <!-- 主要内容 -->
                <div class="relative z-10 flex flex-col justify-center p-12 text-white">
                  <!-- Logo和品牌名 -->
                  <div class="mb-8">
                    <div class="flex items-center mb-4">
                      <!-- 火花Logo -->
                      <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <svg class="w-7 h-7 text-white" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.19 0 2.34-.21 3.41-.6.3-.11.49-.4.49-.72 0-.43-.35-.78-.78-.78-.17 0-.33.06-.46.14-.82.29-1.69.44-2.58.44-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6c0 .89-.15 1.76-.44 2.58-.08.13-.14.29-.14.46 0 .43.35.78.78.78.32 0 .61-.19.72-.49.39-1.07.6-2.22.6-3.41C22 6.48 17.52 2 12 2z"/>
                          <path d="M12 8l-2 6h4l-2-6z"/>
                        </svg>
                      </div>
                      <div>
                        <h1 class="text-2xl font-bold">火花简历</h1>
                        <p class="text-white/80 text-sm">点燃职场新可能</p>
                      </div>
                    </div>
                    <p class="text-lg text-white/90 mb-8">专业简历制作平台，助您脱颖而出</p>
                  </div>

                  <!-- 产品特色 -->
                  <div class="space-y-4">
                    <div class="flex items-center">
                      <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
                      <span class="text-white/90">1000+ 专业简历模板</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
                      <span class="text-white/90">AI 智能内容优化</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
                      <span class="text-white/90">一键导出多种格式</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
                      <span class="text-white/90">安全云端同步</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧登录区域 -->
              <div class="w-full md:w-3/5 flex flex-col">
                <!-- 头部切换按钮 -->
                <div class="flex justify-end items-center px-12 py-8 pb-2 pt-12">
                                      <button
                      v-if="currentPage === 'login'"
                      @click="toggleMode"
                      class="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 flex items-center space-x-1"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    <span>{{ currentMode === 'wechat' ? '密码登录' : '微信登录' }}</span>
                  </button>
                                      <button
                      v-else
                      @click="switchToLogin"
                      class="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 flex items-center space-x-1"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>返回登录</span>
                  </button>
                </div>

                <!-- 动态内容区域 -->
                <div class="flex-1 px-12 py-4 mt-5">
                  <Transition name="slide-fade" mode="out-in">
                    <!-- 登录页面 -->
                    <div v-if="currentPage === 'login'" key="login">
                      <Transition name="slide-fade" mode="out-in">
                        <!-- 微信登录 -->
                        <div v-if="currentMode === 'wechat'" key="wechat" class="space-y-6">
                          <div class="text-center">
                            <!-- 微信扫一扫标题 -->
                            <h3 class="font-semibold text-lg text-secondary-900 mb-6">微信扫一扫</h3>
                            
                            <!-- 二维码区域 -->
                            <div class="relative w-48 h-48 mx-auto mb-6 bg-white border-2 border-secondary-200 rounded-2xl flex items-center justify-center">
                              <div v-if="!qrCodeLoaded" class="text-center">
                                <div class="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                                <p class="text-secondary-600 text-sm">正在生成二维码...</p>
                              </div>
                              
                              <!-- 模拟二维码 -->
                              <div v-else class="w-40 h-40 bg-black rounded-lg relative overflow-hidden">
                                <div class="absolute inset-2 bg-white rounded">
                                  <div class="w-full h-full relative">
                                    <!-- 二维码图案 -->
                                    <div class="absolute top-1 left-1 w-6 h-6 border-2 border-black"></div>
                                    <div class="absolute top-1 right-1 w-6 h-6 border-2 border-black"></div>
                                    <div class="absolute bottom-1 left-1 w-6 h-6 border-2 border-black"></div>
                                    
                                    <!-- 中间logo -->
                                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-primary-600 rounded flex items-center justify-center">
                                      <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.146 4.203 2.943 5.558-.687 1.717-1.562 3.525-1.562 3.525s3.528-.612 5.125-1.36c.687.145 1.395.217 2.185.217 4.8 0 8.691-3.287 8.691-7.34 0-4.052-3.891-7.341-8.691-7.341z"/>
                                      </svg>
                                    </div>
                                    
                                    <!-- 随机点阵 -->
                                    <div class="absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px p-8">
                                      <div v-for="i in 64" :key="i" :class="Math.random() > 0.5 ? 'bg-black' : 'bg-white'" class="w-full h-full"></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                              <!-- 刷新按钮 -->
                              <button 
                                @click="refreshQRCode"
                                class="absolute top-2 right-2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-secondary-50 transition-colors duration-200"
                              >
                                <svg class="w-4 h-4 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                              </button>
                            </div>
                            
                            <div class="space-y-3">
                              <p class="text-sm text-secondary-600">微信扫码，关注公众号授权登录</p>
                            </div>
                          </div>
                        </div>

                        <!-- 账号登录 -->
                        <div v-else key="account" class="pt-3">
                          <!-- 使用新的密码登录表单组件 -->
                          <PasswordLoginForm
                            @login-success="handlePasswordLoginSuccess"
                            @login-error="handlePasswordLoginError"
                            @forgot-password="handleForgotPassword"
                          />
                          
                          <!-- 服务条款 -->
                          <div class="flex items-start space-x-3 mt-6">
                            <input type="checkbox" class="w-4 h-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500 mt-0.5" checked required>
                            <label class="text-sm text-secondary-600">
                              我已阅读并同意
                              <button type="button" class="text-primary-600 hover:text-primary-700 transition-colors duration-200">用户协议</button>
                              和
                              <button type="button" class="text-primary-600 hover:text-primary-700 transition-colors duration-200">隐私政策</button>
                            </label>
                          </div>

                          <!-- 注册链接 -->
                          <div class="mt-6 text-center">
                            <p class="text-sm text-secondary-600">
                              还没有账户？
                              <button @click="switchToRegister" class="font-medium text-primary-600 hover:underline">
                                立即注册
                              </button>
                            </p>
                          </div>
                        </div>
                      </Transition>
                    </div>

                    <!-- 注册页面 -->
                    <div v-else key="register" class="space-y-6">
                      
                      <form @submit.prevent="handleRegister">
                        <div>
                          <label class="block text-sm font-medium text-secondary-700 mb-2">手机号</label>
                          <input 
                            v-model="registerForm.phone"
                            type="tel" 
                            :class="[
                              'w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200',
                              formErrors.registerPhone ? 'border-red-300 bg-red-50' : 'border-secondary-300'
                            ]"
                            placeholder="请输入手机号"
                            @blur="validateRegisterPhone"
                            @input="clearRegisterPhoneError"
                            required
                          >
                          <div class="mt-1 text-sm text-red-600 h-5">
                            {{ formErrors.registerPhone }}
                          </div>
                        </div>

                        <div>
                          <label class="block text-sm font-medium text-secondary-700 mb-2">验证码</label>
                          <div class="flex space-x-3">
                            <input 
                              v-model="registerForm.verifyCode"
                              type="text" 
                              :class="[
                                'flex-1 px-4 py-3 border rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200',
                                formErrors.registerVerifyCode ? 'border-red-300 bg-red-50' : 'border-secondary-300'
                              ]"
                              placeholder="请输入验证码"
                              @blur="validateRegisterVerifyCode"
                              @input="clearRegisterVerifyCodeError"
                              required
                            >
                            <button
                              type="button"
                              @click="sendVerifyCode"
                              :disabled="verifyCodeDisabled || authService.loading.value"
                              class="px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 bg-primary-100 text-primary-700 hover:bg-primary-200 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                            >
                              {{ verifyCodeText }}
                            </button>
                          </div>
                          <div class="mt-1 text-sm text-red-600 h-5">
                            {{ formErrors.registerVerifyCode }}
                          </div>
                        </div>
                        
                        <div>
                          <label class="block text-sm font-medium text-secondary-700 mb-2">密码</label>
                          <div class="relative">
                            <input 
                              v-model="registerForm.password"
                              :type="showPassword ? 'text' : 'password'"
                              :class="[
                                'w-full px-4 py-3 pr-12 border rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200',
                                formErrors.registerPassword ? 'border-red-300 bg-red-50' : 'border-secondary-300'
                              ]"
                              placeholder="请输入密码"
                              @blur="validateRegisterPassword"
                              @input="clearRegisterPasswordError"
                              required
                            >
                            <button 
                              type="button"
                              @click="showPassword = !showPassword"
                              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
                            >
                              <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                              </svg>
                              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 711.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                              </svg>
                            </button>
                          </div>
                          <div class="mt-1 text-sm text-red-600 h-5">
                            {{ formErrors.registerPassword }}
                          </div>
                        </div>


                        
                        <!-- 服务条款 -->
                        <div class="flex items-start space-x-3">
                          <input type="checkbox" class="w-4 h-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500 mt-0.5" checked required>
                          <label class="text-sm text-secondary-600">
                            我已阅读并同意
                            <button type="button" class="text-primary-600 hover:text-primary-700 transition-colors duration-200">用户协议</button>
                            和
                            <button type="button" class="text-primary-600 hover:text-primary-700 transition-colors duration-200">隐私政策</button>
                          </label>
                        </div>
                        
                        <button 
                          type="submit"
                                                        :disabled="authService.loading.value"
                          :class="[
                            'w-full btn-primary mt-9',
                            authService.loading.value ? 'opacity-75 cursor-not-allowed' : ''
                          ]"
                        >
                          <span v-if="authService.loading.value" class="flex items-center justify-center">
                            <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            注册中...
                          </span>
                          <span v-else>立即注册</span>
                        </button>

                        <!-- 登录链接 -->
                        <div class="mt-6 text-center">
                          <p class="text-sm text-secondary-600">
                            已有账户？
                            <button @click="switchToLogin" class="font-medium text-primary-600 hover:underline">
                              立即登录
                            </button>
                          </p>
                        </div>
                      </form>
                    </div>
                  </Transition>
                </div>

                <!-- 底部切换按钮 - 仅在登录页面显示 -->
                <div v-if="currentPage === 'login'" class="px-12 py-8 pt-0">
                  <div class="text-center">
                    <button
                      @click="toggleMode"
                      class="text-secondary-600 hover:text-primary-600 transition-colors duration-200 flex items-center justify-center space-x-2 mx-auto"
                    >
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path v-if="currentMode === 'wechat'" d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.146 4.203 2.943 5.558-.687 1.717-1.562 3.525-1.562 3.525s3.528-.612 5.125-1.36c.687.145 1.395.217 2.185.217 4.8 0 8.691-3.287 8.691-7.34 0-4.052-3.891-7.341-8.691-7.341z"/>
                        <path v-else fill="none" stroke="currentColor" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                      </svg>
                      <span>{{ currentMode === 'wechat' ? '使用密码登录' : '使用微信登录' }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useAuthService } from '~/composables/auth/useAuthService'
import { $message } from '~/composables/shared/useGlobalMessage'
import PasswordLoginForm from '~/components/auth/PasswordLoginForm.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'login-success', 'switch-to-register'])

// 使用认证服务
const authService = useAuthService()

// 响应式状态
const isVisible = ref(props.modelValue)
const currentMode = ref('account') // 默认账号登录，因为后端主要支持手机号登录
const currentPage = ref('login') // 'login' 或 'register'
const qrCodeLoaded = ref(false)
const showPassword = ref(false)

// 登录表单数据 - 保留用于其他登录方式（如微信登录）
const loginForm = ref({
  phone: '',
  password: ''
})

// 注册表单数据
const registerForm = ref({
  phone: '',
  password: '',
  verifyCode: ''
})

// 验证码相关
const verifyCodeText = ref('获取验证码')
const verifyCodeDisabled = ref(false)
const verifyCodeCountdown = ref(0)

// 表单错误状态
const formErrors = ref({
  loginPhone: '',
  loginPassword: '',
  registerPhone: '',
  registerVerifyCode: '',
  registerPassword: ''
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  isVisible.value = newVal
  if (newVal) {
    // 弹窗打开时重置状态
    currentPage.value = 'login'
    currentMode.value = 'account' // 默认账号登录
    resetForm()
  }
})

// 监听 isVisible 变化
watch(isVisible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 手机号校验
const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 清除错误信息 - 保留用于其他功能
const clearLoginPhoneError = () => {
  formErrors.value.loginPhone = ''
}

const clearLoginPasswordError = () => {
  formErrors.value.loginPassword = ''
}

const clearRegisterPhoneError = () => {
  formErrors.value.registerPhone = ''
}

const clearRegisterVerifyCodeError = () => {
  formErrors.value.registerVerifyCode = ''
}

const clearRegisterPasswordError = () => {
  formErrors.value.registerPassword = ''
}

// 验证方法 - 保留用于其他功能
const validateLoginPhone = () => {
  if (!loginForm.value.phone) {
    formErrors.value.loginPhone = '请输入手机号'
    return false
  }
  if (!validatePhone(loginForm.value.phone)) {
    formErrors.value.loginPhone = '请输入正确的手机号'
    return false
  }
  formErrors.value.loginPhone = ''
  return true
}

const validateLoginPassword = () => {
  if (!loginForm.value.password) {
    formErrors.value.loginPassword = '请输入密码'
    return false
  }
  if (loginForm.value.password.length < 6) {
    formErrors.value.loginPassword = '密码长度不能少于6位'
    return false
  }
  formErrors.value.loginPassword = ''
  return true
}

const validateRegisterPhone = () => {
  if (!registerForm.value.phone) {
    formErrors.value.registerPhone = '请输入手机号'
    return false
  }
  if (!validatePhone(registerForm.value.phone)) {
    formErrors.value.registerPhone = '请输入正确的手机号'
    return false
  }
  formErrors.value.registerPhone = ''
  return true
}

const validateRegisterVerifyCode = () => {
  if (!registerForm.value.verifyCode) {
    formErrors.value.registerVerifyCode = '请输入验证码'
    return false
  }
  if (!/^\d{6}$/.test(registerForm.value.verifyCode)) {
    formErrors.value.registerVerifyCode = '请输入6位数字验证码'
    return false
  }
  formErrors.value.registerVerifyCode = ''
  return true
}

const validateRegisterPassword = () => {
  if (!registerForm.value.password) {
    formErrors.value.registerPassword = '请输入密码'
    return false
  }
  if (registerForm.value.password.length < 6) {
    formErrors.value.registerPassword = '密码长度不能少于6位'
    return false
  }
  formErrors.value.registerPassword = ''
  return true
}

// 重置表单
const resetForm = () => {
  loginForm.value = {
    phone: '',
    password: ''
  }
  registerForm.value = {
    phone: '',
    password: '',
    verifyCode: ''
  }
  // 清除所有错误状态
  formErrors.value = {
    loginPhone: '',
    loginPassword: '',
    registerPhone: '',
    registerVerifyCode: '',
    registerPassword: ''
  }
  clearVerifyCodeTimer()
}

// 关闭弹窗
const closeModal = () => {
  isVisible.value = false
  resetForm()
}

// 切换登录方式
const toggleMode = () => {
  currentMode.value = currentMode.value === 'wechat' ? 'account' : 'wechat'
  if (currentMode.value === 'wechat' && !qrCodeLoaded.value) {
    loadQRCode()
  }
}

// 切换到注册页面
const switchToRegister = () => {
  currentPage.value = 'register'
  currentMode.value = 'account'
}

// 切换到登录页面
const switchToLogin = () => {
  currentPage.value = 'login'
}

// 加载二维码
const loadQRCode = () => {
  qrCodeLoaded.value = false
  setTimeout(() => {
    qrCodeLoaded.value = true
  }, 1500)
}

// 刷新二维码
const refreshQRCode = () => {
  loadQRCode()
}

// 处理密码登录成功
const handlePasswordLoginSuccess = (loginData) => {
  try {
    // 不在这里显示消息，因为 PasswordLoginForm 组件已经显示了
    emit('login-success', loginData)
    closeModal()
  } catch (error) {
    console.error('处理登录成功回调时发生错误:', error)
  }
}

// 处理密码登录错误
const handlePasswordLoginError = (error) => {
  try {
    console.error('密码登录失败:', error)
    // 不在这里显示消息，因为 PasswordLoginForm 组件已经显示了
  } catch (err) {
    console.error('处理登录错误回调时发生错误:', err)
  }
}

// 处理忘记密码
const handleForgotPassword = () => {
  try {
    // TODO: 实现忘记密码功能
    $message.info('忘记密码功能即将上线')
    console.log('用户点击了忘记密码')
  } catch (error) {
    console.error('处理忘记密码时发生错误:', error)
  }
}

// 发送验证码
const sendVerifyCode = async () => {
  // 验证注册页面的手机号
  if (currentPage.value === 'register') {
    if (!validateRegisterPhone()) {
      return
    }
  }
  
  const phone = currentPage.value === 'register' ? registerForm.value.phone : loginForm.value.phone
  
  try {
    const result = await authService.sendSmsCode(phone)
    
    if (result.success) {
      $message.success('验证码发送成功')
      startVerifyCodeCountdown()
    } else {
      $message.error(result.error || '验证码发送失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    $message.error('验证码发送失败，请重试')
  }
}

// 开始验证码倒计时
const startVerifyCodeCountdown = () => {
  verifyCodeDisabled.value = true
  verifyCodeCountdown.value = 60
  
  const timer = setInterval(() => {
    verifyCodeCountdown.value--
    verifyCodeText.value = `${verifyCodeCountdown.value}秒后重发`
    
    if (verifyCodeCountdown.value <= 0) {
      clearInterval(timer)
      verifyCodeDisabled.value = false
      verifyCodeText.value = '获取验证码'
    }
  }, 1000)
}

// 清除验证码定时器
const clearVerifyCodeTimer = () => {
  verifyCodeDisabled.value = false
  verifyCodeText.value = '获取验证码'
  verifyCodeCountdown.value = 0
}

// 处理注册
const handleRegister = async () => {
  // 验证表单
  const isPhoneValid = validateRegisterPhone()
  const isVerifyCodeValid = validateRegisterVerifyCode()
  const isPasswordValid = validateRegisterPassword()
  
  if (!isPhoneValid || !isVerifyCodeValid || !isPasswordValid) {
    return
  }
  
  try {
    const result = await authService.register({
      phone: registerForm.value.phone,
      password: registerForm.value.password,
      verifyCode: registerForm.value.verifyCode
    })
    
    if (result.success) {
      $message.success('注册成功')
      emit('login-success', result.data)
      closeModal()
    } else {
      $message.error(result.error || '注册失败')
    }
  } catch (error) {
    console.error('注册失败:', error)
    $message.error('注册失败，请重试')
  }
}

// 初始化
onMounted(() => {
  if (isVisible.value && currentMode.value === 'wechat') {
    loadQRCode()
  }
})
</script>

<style scoped>
/* 弹窗动画 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-scale-enter-active,
.modal-scale-leave-active {
  transition: all 0.3s ease;
}

.modal-scale-enter-from,
.modal-scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 内容切换动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.4s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 页面切换动画 */
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.5s ease;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style> 