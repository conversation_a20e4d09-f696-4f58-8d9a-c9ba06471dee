import { c as defineEvent<PERSON>and<PERSON>, g as getRouter<PERSON>arams, e as setHeader } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'vue';
import 'consola';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const ____params_ = defineEventHandler(async (event) => {
  const params = getRouterParams(event);
  const pathSegments = params.params;
  let width = 120;
  let height = 80;
  if (pathSegments && pathSegments.length >= 2) {
    width = parseInt(pathSegments[0]) || 120;
    height = parseInt(pathSegments[1]) || 80;
  }
  width = Math.max(50, Math.min(800, width));
  height = Math.max(50, Math.min(600, height));
  const svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="#f3f4f6"/>
    <text x="50%" y="50%" text-anchor="middle" dy="0.3em" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af">
      ${width} \xD7 ${height}
    </text>
  </svg>`;
  setHeader(event, "Content-Type", "image/svg+xml");
  setHeader(event, "Cache-Control", "public, max-age=31536000");
  return svg;
});

export { ____params_ as default };
//# sourceMappingURL=_...params_.mjs.map
