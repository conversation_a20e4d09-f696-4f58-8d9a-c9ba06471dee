package com.alan6.resume.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统维护请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "MaintenanceRequest", description = "系统维护请求")
public class MaintenanceRequest {

    /**
     * 是否启用维护模式
     */
    @Schema(description = "是否启用维护模式", example = "true")
    private Boolean enabled;

    /**
     * 维护提示消息
     */
    @Schema(description = "维护提示消息", example = "系统维护中，预计30分钟后恢复")
    private String message;

    /**
     * 允许访问的IP列表
     */
    @Schema(description = "允许访问的IP列表", example = "[\"*************\", \"*************\"]")
    private List<String> allowedIps;

    /**
     * 维护开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "维护开始时间", example = "2024-12-22 18:00:00")
    private LocalDateTime startTime;

    /**
     * 维护结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "维护结束时间", example = "2024-12-22 18:30:00")
    private LocalDateTime endTime;
} 