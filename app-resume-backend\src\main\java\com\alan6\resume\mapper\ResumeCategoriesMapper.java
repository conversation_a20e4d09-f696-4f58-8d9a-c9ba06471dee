package com.alan6.resume.mapper;

import com.alan6.resume.entity.ResumeCategories;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 简历分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface ResumeCategoriesMapper extends BaseMapper<ResumeCategories> {

    /**
     * 根据分类ID获取关联的模板数量
     */
    Integer getTemplateCountByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 获取分类树（包含模板数量）
     */
    List<ResumeCategories> getCategoryTreeWithCount();
} 