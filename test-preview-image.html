<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预览图功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-item {
            margin-bottom: 15px;
        }
        .preview-image {
            max-width: 200px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .error-example {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success-example {
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>预览图功能测试页面</h1>
    
    <div class="test-section">
        <h2>1. 正常预览图测试</h2>
        <div class="test-item">
            <h3>通过API代理访问 (推荐方式)</h3>
            <p>URL: <code>/api/admin/template/preview-image/business-simple</code></p>
            <img src="/api/admin/template/preview-image/business-simple" 
                 alt="商务简约模板预览" 
                 class="preview-image"
                 onload="console.log('图片加载成功')"
                 onerror="handleImageError(this)">
        </div>
        
        <div class="test-item">
            <h3>直接MinIO访问 (备用方式)</h3>
            <p>URL: <code>/api/minio/resume-templates/business-simple/preview.jpg</code></p>
            <img src="/api/minio/resume-templates/business-simple/preview.jpg" 
                 alt="商务简约模板预览" 
                 class="preview-image"
                 onload="console.log('MinIO直接访问成功')"
                 onerror="handleImageError(this)">
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. 错误处理测试</h2>
        <div class="test-item">
            <h3>不存在的模板</h3>
            <p>URL: <code>/api/admin/template/preview-image/non-existent-template</code></p>
            <img src="/api/admin/template/preview-image/non-existent-template" 
                 alt="不存在的模板" 
                 class="preview-image"
                 onload="console.log('意外的成功')"
                 onerror="handleImageError(this)">
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试结果</h2>
        <div id="test-results">
            <p>加载中...</p>
        </div>
    </div>

    <script>
        let successCount = 0;
        let errorCount = 0;
        const results = [];

        function handleImageError(img) {
            errorCount++;
            const testName = img.alt;
            const errorMsg = `❌ ${testName} - 图片加载失败`;
            console.log(errorMsg);
            results.push(errorMsg);
            
            // 显示占位符
            img.style.display = 'none';
            const placeholder = document.createElement('div');
            placeholder.style.cssText = 'width: 200px; height: 150px; background: #f8f9fa; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; border-radius: 4px; color: #666;';
            placeholder.textContent = '预览图加载失败';
            img.parentNode.insertBefore(placeholder, img);
            
            updateResults();
        }

        // 监听所有图片加载成功事件
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.addEventListener('load', function() {
                    successCount++;
                    const testName = this.alt;
                    const successMsg = `✅ ${testName} - 图片加载成功`;
                    console.log(successMsg);
                    results.push(successMsg);
                    updateResults();
                });
            });

            // 延迟更新结果，等待所有图片加载完成
            setTimeout(updateResults, 3000);
        });

        function updateResults() {
            const resultsDiv = document.getElementById('test-results');
            let html = `
                <div class="success-example">
                    <strong>成功加载: ${successCount} 个图片</strong>
                </div>
                <div class="error-example">
                    <strong>加载失败: ${errorCount} 个图片</strong>
                </div>
                <h4>详细结果:</h4>
                <ul>
            `;
            
            results.forEach(result => {
                html += `<li>${result}</li>`;
            });
            
            html += '</ul>';

            if (successCount > 0) {
                html += `
                    <div class="success-example">
                        <strong>✅ 前端预览图功能正常工作</strong><br>
                        - 图片加载机制正常<br>
                        - 错误处理机制正常<br>
                        - API调用路径正确
                    </div>
                `;
            }

            if (errorCount === document.querySelectorAll('img').length) {
                html += `
                    <div class="error-example">
                        <strong>⚠️ 所有图片加载失败</strong><br>
                        可能的原因:<br>
                        - 后端API尚未实现 <code>/api/admin/template/preview-image/{templateCode}</code><br>
                        - MinIO服务未启动或配置错误<br>
                        - 数据库中没有预览图数据
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        }

        // 测试上传功能
        function testUploadFunction() {
            console.log('测试预览图上传功能...');
            
            // 创建一个测试文件
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 600;
            const ctx = canvas.getContext('2d');
            
            // 绘制一个简单的预览图
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 400, 600);
            ctx.fillStyle = '#333';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('测试预览图', 200, 300);
            
            canvas.toBlob(function(blob) {
                const file = new File([blob], 'test-preview.png', { type: 'image/png' });
                console.log('创建测试预览图文件:', file);
                
                const formData = new FormData();
                formData.append('previewImage', file);
                formData.append('savePreviewImageUrl', 'true');
                
                console.log('预览图上传数据准备完成，等待后端API实现');
            });
        }

        // 页面加载完成后运行测试
        setTimeout(testUploadFunction, 1000);
    </script>
</body>
</html>
