import{_ as Y}from"./BXnw39BI.js";import{r as F,l as J,i as K,c as r,a as s,q as p,b as i,d as u,h as n,F as S,g as M,m as U,s as k,G as Q,t as a,w as X,p as f,I,y as V,H as D,f as Z,$ as ss,o as d,z as ts}from"./CURHyiUL.js";import{u as es,a as ls}from"./DF3nxAfK.js";import{_ as os}from"./DlAUqK2U.js";const is={class:"template-converter"},ns={class:"page-header"},as={class:"header-content"},ds={class:"page-title"},rs={class:"header-actions"},cs=["disabled"],us={class:"converter-steps"},vs={class:"step-indicator"},ps={class:"step-number"},ms={key:1,class:"step-num"},bs={class:"step-content"},gs={class:"step-title"},_s={class:"step-desc"},fs={class:"converter-content"},ks={key:0,class:"step-panel upload-panel"},ys={class:"upload-area"},Cs={class:"dropzone-content"},hs=["disabled"],ws={key:0,class:"upload-progress"},Fs={class:"progress-bar"},Ss={class:"progress-text"},Ms={class:"specification-info"},Vs={class:"info-header"},xs={class:"info-title"},Rs={class:"info-content"},Hs={class:"info-grid"},Ts={class:"info-item"},Ls={class:"info-item"},Us={class:"info-item"},Is={class:"info-item"},Ds={class:"info-footer"},$s={class:"info-note"},Es={key:1,class:"step-panel validation-panel"},Ns={class:"validation-stats"},Os={class:"stats-grid"},Ps={class:"stat-item"},zs={class:"stat-number"},Bs={class:"stat-item success"},qs={class:"stat-number"},js={class:"stat-item error"},Gs={class:"stat-number"},As={class:"stat-item warning"},Ws={class:"stat-number"},Ys={class:"validation-results"},Js={class:"validation-header"},Ks={class:"file-info"},Qs={class:"file-name"},Xs={class:"validation-status"},Zs={key:0,class:"validation-details"},st={key:0,class:"error-list"},tt={class:"detail-title error"},et={class:"error-message"},lt={class:"error-suggestion"},ot={key:1,class:"warning-list"},it={class:"detail-title warning"},nt={class:"warning-message"},at={class:"warning-suggestion"},dt={class:"validation-actions"},rt=["disabled"],ct=["disabled"],ut=["disabled"],vt={key:2,class:"step-panel conversion-panel"},pt={class:"conversion-options"},mt={class:"option-group"},bt={class:"radio-group"},gt={class:"radio-item"},_t=["disabled"],ft={class:"radio-item"},kt={class:"option-group"},yt={class:"checkbox-group"},Ct={class:"checkbox-item"},ht={class:"checkbox-item"},wt={class:"checkbox-item"},Ft={class:"checkbox-item"},St={class:"option-group"},Mt={class:"advanced-options"},Vt={class:"option-row"},xt={class:"option-row"},Rt={class:"option-row"},Ht={class:"toggle-switch"},Tt={class:"conversion-actions"},Lt=["disabled"],Ut={key:3,class:"step-panel results-panel"},It={key:0,class:"conversion-stats"},Dt={class:"stats-grid"},$t={class:"stat-item"},Et={class:"stat-number"},Nt={class:"stat-item success"},Ot={class:"stat-number"},Pt={class:"stat-item error"},zt={class:"stat-number"},Bt={class:"stat-item"},qt={class:"stat-number"},jt={key:1,class:"conversion-results"},Gt={class:"result-header"},At={class:"file-info"},Wt={class:"file-names"},Yt={class:"source-name"},Jt={class:"target-name"},Kt={class:"result-status"},Qt={key:0,class:"result-details"},Xt={class:"detail-grid"},Zt={class:"detail-item"},se={class:"detail-value"},te={class:"detail-item"},ee={class:"detail-value"},le={class:"detail-item"},oe={class:"detail-value"},ie={class:"detail-item"},ne={class:"detail-value"},ae={key:1,class:"result-error"},de={class:"error-message"},re={class:"results-actions"},ce=["disabled"],ue=["disabled"],ve={class:"modal-header"},pe={__name:"converter",setup(me){const _=es(),m=ls(),{state:l,hasUploadedFiles:$,hasValidationResults:E,isProcessing:N}=_,b=F(0),h=F(!1),x=F(!1),R=F(null),y=F("batch"),v=J({validateHtml:!0,generateFullComponent:!0,preserveComments:!1,formatOutput:!0,generateTypes:!1,targetVueVersion:"vue3",cssProcessing:"scoped",strictMode:!1}),O=[{key:"upload",title:"上传文件",description:"选择HTML模板文件"},{key:"validate",title:"验证规范",description:"检查文件规范性"},{key:"configure",title:"转换设置",description:"配置转换选项"},{key:"results",title:"查看结果",description:"获取转换结果"}],P=c=>{c.preventDefault(),h.value=!1;const t=c.dataTransfer.files;t.length>0&&T(t)},z=c=>{const t=c.target.files;t.length>0&&T(t)},T=async c=>{console.log("处理文件上传:",c.length);const t=_.validateFileTypes(c);if(t.invalidFiles.length>0&&m.warning(t.message),t.validFiles.length===0){m.error("没有有效的HTML文件");return}(await _.uploadHtmlFiles(t.validFiles)).success&&(b.value=1)},B=async()=>{if(!l.sessionId||!l.uploadedFiles.length){m.error("没有文件需要验证");return}(await _.validateHtmlFiles(l.sessionId,l.uploadedFiles)).success&&m.success("重新验证完成")},q=async()=>{if(!l.sessionId||!l.uploadedFiles.length){m.error("没有验证结果可生成报告");return}const c=await _.generateValidationReport(l.sessionId,l.uploadedFiles);if(c.success){const t=new Blob([c.data],{type:"text/markdown"}),o=URL.createObjectURL(t),C=document.createElement("a");C.href=o,C.download=`validation-report-${l.sessionId}.md`,C.click(),URL.revokeObjectURL(o),m.success("验证报告已生成")}},j=()=>{if(l.statistics.validFiles===0){m.error("没有通过验证的文件");return}b.value=2},G=async()=>{if(!l.taskId||!l.uploadedFiles.length){m.error("没有文件需要转换");return}const c=l.uploadedFiles.filter((C,e)=>{const g=l.validationResults[e];return g&&g.valid});if(c.length===0){m.error("没有通过验证的文件可以转换");return}const t=_.createConversionRequest(y.value,c,v);(await _.convertHtmlFiles(t)).success&&(b.value=3)},A=async()=>{if(!l.taskId){m.error("没有转换结果可下载");return}await _.downloadResults(l.taskId)},W=async()=>{if(!l.taskId){m.error("没有转换结果可查看");return}const c=await _.getConversionResult(l.taskId);c.success&&(console.log("转换结果详情:",c.data),m.info("请查看控制台获取详细信息"))},L=()=>{_.resetState(),b.value=0,y.value="batch",R.value&&(R.value.value=""),m.info("转换器已重置")};return K(()=>{console.log("模板转换器页面已加载")}),(c,t)=>{const o=ss("Icon"),C=Y;return d(),r("div",is,[s("div",ns,[s("div",as,[s("h1",ds,[i(o,{name:"document",class:"title-icon"}),t[18]||(t[18]=u(" 模板转换器 "))]),t[19]||(t[19]=s("p",{class:"page-description"}," 将HTML简历模板转换为Vue组件，支持单个和批量转换 ",-1))]),s("div",rs,[n($)?(d(),r("button",{key:0,onClick:L,class:"btn btn-outline",disabled:n(N)},[i(o,{name:"undo"}),t[20]||(t[20]=u(" 重新开始 "))],8,cs)):p("",!0),n(l).sessionId?(d(),r("button",{key:1,onClick:t[0]||(t[0]=e=>x.value=!0),class:"btn btn-outline"},[i(o,{name:"info"}),t[21]||(t[21]=u(" 使用帮助 "))])):p("",!0)])]),s("div",us,[s("div",vs,[(d(),r(S,null,M(O,(e,g)=>s("div",{key:e.key,class:k(["step-item",{active:b.value===g,completed:b.value>g,disabled:b.value<g}])},[s("div",ps,[b.value>g?(d(),ts(o,{key:0,name:"check",class:"step-icon completed"})):(d(),r("span",ms,a(g+1),1))]),s("div",bs,[s("h3",gs,a(e.title),1),s("p",_s,a(e.description),1)])],2)),64))])]),s("div",fs,[b.value===0?(d(),r("div",ks,[t[32]||(t[32]=s("div",{class:"panel-header"},[s("h2",{class:"panel-title"},"上传HTML文件"),s("p",{class:"panel-subtitle"},"选择需要转换的HTML简历模板文件")],-1)),s("div",ys,[s("div",{class:k(["dropzone",{dragover:h.value,uploading:n(l).uploading}]),onDrop:P,onDragover:t[2]||(t[2]=U(e=>h.value=!0,["prevent"])),onDragleave:t[3]||(t[3]=e=>h.value=!1),onDragend:t[4]||(t[4]=e=>h.value=!1)},[s("div",Cs,[i(o,{name:"plus",class:"upload-icon"}),t[23]||(t[23]=s("h3",{class:"upload-title"},"拖拽文件到此处",-1)),t[24]||(t[24]=s("p",{class:"upload-subtitle"},"或点击选择文件",-1)),s("input",{ref_key:"fileInput",ref:R,type:"file",multiple:"",accept:".html",onChange:z,class:"file-input"},null,544),s("button",{onClick:t[1]||(t[1]=e=>c.$refs.fileInput.click()),class:"btn btn-primary upload-btn",disabled:n(l).uploading},[i(o,{name:"plus"}),t[22]||(t[22]=u(" 选择HTML文件 "))],8,hs)])],34),n(l).uploading?(d(),r("div",ws,[s("div",Fs,[s("div",{class:"progress-fill",style:Q({width:n(l).uploadProgress+"%"})},null,4)]),s("p",Ss," 上传中... "+a(n(l).uploadProgress)+"% ",1)])):p("",!0)]),s("div",Ms,[s("div",Vs,[s("h3",xs,[i(o,{name:"info"}),t[25]||(t[25]=u(" HTML规范要求 "))]),i(C,{to:"/admin/template/html-specification",class:"btn btn-outline btn-sm"},{default:X(()=>[i(o,{name:"document"}),t[26]||(t[26]=u(" 查看详细规范 "))]),_:1,__:[26]})]),s("div",Rs,[s("div",Hs,[s("div",Ts,[i(o,{name:"check-circle",class:"info-icon success"}),t[27]||(t[27]=s("div",null,[s("h4",null,"必需元素"),s("p",null,"包含DOCTYPE、html、head、body标签")],-1))]),s("div",Ls,[i(o,{name:"check-circle",class:"info-icon success"}),t[28]||(t[28]=s("div",null,[s("h4",null,"模块标识"),s("p",null,"使用data-module属性标识简历模块")],-1))]),s("div",Us,[i(o,{name:"check-circle",class:"info-icon success"}),t[29]||(t[29]=s("div",null,[s("h4",null,"字段绑定"),s("p",null,"使用data-field属性绑定数据字段")],-1))]),s("div",Is,[i(o,{name:"check-circle",class:"info-icon success"}),t[30]||(t[30]=s("div",null,[s("h4",null,"样式要求"),s("p",null,"包含CSS样式，使用相对单位")],-1))])]),s("div",Ds,[s("p",$s,[i(o,{name:"info",class:"note-icon"}),t[31]||(t[31]=u(" 以上只是基本要求概览，完整的开发规范请点击上方按钮查看详细文档，包含所有字段定义、示例代码和验证清单。 "))])])])])])):p("",!0),b.value===1?(d(),r("div",Es,[t[40]||(t[40]=s("div",{class:"panel-header"},[s("h2",{class:"panel-title"},"验证结果"),s("p",{class:"panel-subtitle"},"检查HTML文件是否符合转换规范")],-1)),s("div",Ns,[s("div",Os,[s("div",Ps,[s("div",zs,a(n(l).statistics.totalFiles),1),t[33]||(t[33]=s("div",{class:"stat-label"},"总文件数",-1))]),s("div",Bs,[s("div",qs,a(n(l).statistics.validFiles),1),t[34]||(t[34]=s("div",{class:"stat-label"},"验证通过",-1))]),s("div",js,[s("div",Gs,a(n(l).statistics.invalidFiles),1),t[35]||(t[35]=s("div",{class:"stat-label"},"验证失败",-1))]),s("div",As,[s("div",Ws,a(n(l).statistics.totalWarnings),1),t[36]||(t[36]=s("div",{class:"stat-label"},"警告数量",-1))])])]),s("div",Ys,[(d(!0),r(S,null,M(n(l).validationResults,(e,g)=>(d(),r("div",{key:g,class:k(["validation-item",{valid:e.valid,invalid:!e.valid}])},[s("div",Js,[s("div",Ks,[i(o,{name:e.valid?"check-circle":"exclamation-triangle",class:k(e.valid?"success":"error")},null,8,["name","class"]),s("span",Qs,a(n(l).uploadedFiles[g]),1)]),s("div",Xs,[s("span",{class:k(["status-badge",e.valid?"success":"error"])},a(e.valid?"通过":"失败"),3)])]),!e.valid||e.warningCount>0?(d(),r("div",Zs,[e.errorCount>0?(d(),r("div",st,[s("h4",tt,[i(o,{name:"exclamation-triangle"}),u(" 错误信息 ("+a(e.errorCount)+") ",1)]),(d(!0),r(S,null,M(e.errors,(w,H)=>(d(),r("div",{key:H,class:"error-item"},[s("div",et,a(w.message),1),s("div",lt,[i(o,{name:"info"}),u(" "+a(w.suggestion),1)])]))),128))])):p("",!0),e.warningCount>0?(d(),r("div",ot,[s("h4",it,[i(o,{name:"warning"}),u(" 警告信息 ("+a(e.warningCount)+") ",1)]),(d(!0),r(S,null,M(e.warnings,(w,H)=>(d(),r("div",{key:H,class:"warning-item"},[s("div",nt,a(w.message),1),s("div",at,[i(o,{name:"info"}),u(" "+a(w.suggestion),1)])]))),128))])):p("",!0)])):p("",!0)],2))),128))]),s("div",dt,[s("button",{onClick:B,class:"btn btn-outline",disabled:n(l).validating},[i(o,{name:"undo"}),t[37]||(t[37]=u(" 重新验证 "))],8,rt),s("button",{onClick:q,class:"btn btn-outline",disabled:!n(E)},[i(o,{name:"document"}),t[38]||(t[38]=u(" 生成报告 "))],8,ct),s("button",{onClick:j,class:"btn btn-primary",disabled:n(l).statistics.validFiles===0},[i(o,{name:"arrow-down"}),t[39]||(t[39]=u(" 开始转换 "))],8,ut)])])):p("",!0),b.value===2?(d(),r("div",vt,[t[63]||(t[63]=s("div",{class:"panel-header"},[s("h2",{class:"panel-title"},"转换设置"),s("p",{class:"panel-subtitle"},"配置转换选项和参数")],-1)),s("div",pt,[s("div",mt,[t[45]||(t[45]=s("h3",{class:"option-title"},"转换类型",-1)),s("div",bt,[s("label",gt,[f(s("input",{"onUpdate:modelValue":t[5]||(t[5]=e=>y.value=e),type:"radio",value:"single",disabled:n(l).statistics.validFiles<=1},null,8,_t),[[I,y.value]]),t[41]||(t[41]=s("span",{class:"radio-label"},"单个转换",-1)),t[42]||(t[42]=s("span",{class:"radio-desc"},"逐个转换文件，便于调试",-1))]),s("label",ft,[f(s("input",{"onUpdate:modelValue":t[6]||(t[6]=e=>y.value=e),type:"radio",value:"batch"},null,512),[[I,y.value]]),t[43]||(t[43]=s("span",{class:"radio-label"},"批量转换",-1)),t[44]||(t[44]=s("span",{class:"radio-desc"},"一次性转换所有文件",-1))])])]),s("div",kt,[t[54]||(t[54]=s("h3",{class:"option-title"},"转换选项",-1)),s("div",yt,[s("label",Ct,[f(s("input",{"onUpdate:modelValue":t[7]||(t[7]=e=>v.validateHtml=e),type:"checkbox"},null,512),[[V,v.validateHtml]]),t[46]||(t[46]=s("span",{class:"checkbox-label"},"HTML验证",-1)),t[47]||(t[47]=s("span",{class:"checkbox-desc"},"转换前验证HTML规范",-1))]),s("label",ht,[f(s("input",{"onUpdate:modelValue":t[8]||(t[8]=e=>v.generateFullComponent=e),type:"checkbox"},null,512),[[V,v.generateFullComponent]]),t[48]||(t[48]=s("span",{class:"checkbox-label"},"完整组件",-1)),t[49]||(t[49]=s("span",{class:"checkbox-desc"},"生成完整的Vue组件结构",-1))]),s("label",wt,[f(s("input",{"onUpdate:modelValue":t[9]||(t[9]=e=>v.formatOutput=e),type:"checkbox"},null,512),[[V,v.formatOutput]]),t[50]||(t[50]=s("span",{class:"checkbox-label"},"格式化输出",-1)),t[51]||(t[51]=s("span",{class:"checkbox-desc"},"格式化生成的代码",-1))]),s("label",Ft,[f(s("input",{"onUpdate:modelValue":t[10]||(t[10]=e=>v.preserveComments=e),type:"checkbox"},null,512),[[V,v.preserveComments]]),t[52]||(t[52]=s("span",{class:"checkbox-label"},"保留注释",-1)),t[53]||(t[53]=s("span",{class:"checkbox-desc"},"保留原HTML中的注释",-1))])])]),s("div",St,[t[61]||(t[61]=s("h3",{class:"option-title"},"高级选项",-1)),s("div",Mt,[s("div",Vt,[t[56]||(t[56]=s("label",{class:"option-label"},"Vue版本",-1)),f(s("select",{"onUpdate:modelValue":t[11]||(t[11]=e=>v.targetVueVersion=e),class:"option-select"},t[55]||(t[55]=[s("option",{value:"vue3"},"Vue 3",-1),s("option",{value:"vue2"},"Vue 2",-1)]),512),[[D,v.targetVueVersion]])]),s("div",xt,[t[58]||(t[58]=s("label",{class:"option-label"},"CSS处理",-1)),f(s("select",{"onUpdate:modelValue":t[12]||(t[12]=e=>v.cssProcessing=e),class:"option-select"},t[57]||(t[57]=[s("option",{value:"scoped"},"Scoped",-1),s("option",{value:"module"},"Module",-1),s("option",{value:"global"},"Global",-1)]),512),[[D,v.cssProcessing]])]),s("div",Rt,[t[60]||(t[60]=s("label",{class:"option-label"},"严格模式",-1)),s("label",Ht,[f(s("input",{"onUpdate:modelValue":t[13]||(t[13]=e=>v.strictMode=e),type:"checkbox",class:"toggle-input"},null,512),[[V,v.strictMode]]),t[59]||(t[59]=s("span",{class:"toggle-slider"},null,-1))])])])])]),s("div",Tt,[s("button",{onClick:t[14]||(t[14]=e=>b.value=1),class:"btn btn-outline"},[i(o,{name:"arrow-up"}),t[62]||(t[62]=u(" 返回验证 "))]),s("button",{onClick:G,class:"btn btn-primary",disabled:n(l).converting},[i(o,{name:"check"}),u(" "+a(n(l).converting?"转换中...":"开始转换"),1)],8,Lt)])])):p("",!0),b.value===3?(d(),r("div",Ut,[t[75]||(t[75]=s("div",{class:"panel-header"},[s("h2",{class:"panel-title"},"转换结果"),s("p",{class:"panel-subtitle"},"查看转换结果和生成的文件")],-1)),n(l).conversionResults?(d(),r("div",It,[s("div",Dt,[s("div",$t,[s("div",Et,a(n(l).conversionResults.statistics.totalFiles),1),t[64]||(t[64]=s("div",{class:"stat-label"},"总文件数",-1))]),s("div",Nt,[s("div",Ot,a(n(l).conversionResults.statistics.successCount),1),t[65]||(t[65]=s("div",{class:"stat-label"},"转换成功",-1))]),s("div",Pt,[s("div",zt,a(n(l).conversionResults.statistics.failedCount),1),t[66]||(t[66]=s("div",{class:"stat-label"},"转换失败",-1))]),s("div",Bt,[s("div",qt,a(Math.round(n(l).conversionResults.statistics.successRate))+"%",1),t[67]||(t[67]=s("div",{class:"stat-label"},"成功率",-1))])])])):p("",!0),n(l).conversionResults?(d(),r("div",jt,[(d(!0),r(S,null,M(n(l).conversionResults.results,(e,g)=>(d(),r("div",{key:g,class:k(["result-item",{success:e.status==="SUCCESS",failed:e.status==="FAILED"}])},[s("div",Gt,[s("div",At,[i(o,{name:e.status==="SUCCESS"?"check-circle":"exclamation-triangle",class:k(e.status==="SUCCESS"?"success":"error")},null,8,["name","class"]),s("div",Wt,[s("span",Yt,a(e.sourceFileName),1),i(o,{name:"arrow-down",class:"arrow-icon"}),s("span",Jt,a(e.targetFileName),1)])]),s("div",Kt,[s("span",{class:k(["status-badge",e.status==="SUCCESS"?"success":"error"])},a(e.status==="SUCCESS"?"成功":"失败"),3)])]),e.details?(d(),r("div",Qt,[s("div",Xt,[s("div",Zt,[t[68]||(t[68]=s("span",{class:"detail-label"},"模块数量",-1)),s("span",se,a(e.details.moduleCount),1)]),s("div",te,[t[69]||(t[69]=s("span",{class:"detail-label"},"字段数量",-1)),s("span",ee,a(e.details.fieldCount),1)]),s("div",le,[t[70]||(t[70]=s("span",{class:"detail-label"},"代码行数",-1)),s("span",oe,a(e.details.vueCodeLines),1)]),s("div",ie,[t[71]||(t[71]=s("span",{class:"detail-label"},"处理时间",-1)),s("span",ne,a(e.processingTime)+"ms",1)])])])):p("",!0),e.error?(d(),r("div",ae,[i(o,{name:"exclamation-triangle",class:"error-icon"}),s("span",de,a(e.error),1)])):p("",!0)],2))),128))])):p("",!0),s("div",re,[s("button",{onClick:A,class:"btn btn-primary",disabled:!n(l).conversionResults||n(l).conversionResults.statistics.successCount===0},[i(o,{name:"download"}),t[72]||(t[72]=u(" 下载结果 "))],8,ce),s("button",{onClick:W,class:"btn btn-outline",disabled:!n(l).conversionResults},[i(o,{name:"eye"}),t[73]||(t[73]=u(" 查看详情 "))],8,ue),s("button",{onClick:L,class:"btn btn-outline"},[i(o,{name:"undo"}),t[74]||(t[74]=u(" 重新转换 "))])])])):p("",!0)]),x.value?(d(),r("div",{key:0,class:"modal-overlay",onClick:t[17]||(t[17]=e=>x.value=!1)},[s("div",{class:"modal-content",onClick:t[16]||(t[16]=U(()=>{},["stop"]))},[s("div",ve,[t[76]||(t[76]=s("h3",{class:"modal-title"},"使用帮助",-1)),s("button",{onClick:t[15]||(t[15]=e=>x.value=!1),class:"modal-close"},[i(o,{name:"close"})])]),t[77]||(t[77]=Z('<div class="modal-body" data-v-1541b00c><div class="help-content" data-v-1541b00c><h4 data-v-1541b00c>HTML规范要求</h4><ul data-v-1541b00c><li data-v-1541b00c>文件必须包含完整的HTML文档结构</li><li data-v-1541b00c>使用data-module属性标识简历模块</li><li data-v-1541b00c>使用data-field属性绑定数据字段</li><li data-v-1541b00c>CSS样式建议使用相对单位</li></ul><h4 data-v-1541b00c>转换流程</h4><ol data-v-1541b00c><li data-v-1541b00c>上传HTML文件</li><li data-v-1541b00c>验证文件规范性</li><li data-v-1541b00c>配置转换选项</li><li data-v-1541b00c>执行转换并查看结果</li></ol><h4 data-v-1541b00c>常见问题</h4><ul data-v-1541b00c><li data-v-1541b00c>确保HTML文件编码为UTF-8</li><li data-v-1541b00c>检查data属性是否正确设置</li><li data-v-1541b00c>验证CSS选择器是否冲突</li></ul></div></div>',1))])])):p("",!0)])}}},ke=os(pe,[["__scopeId","data-v-1541b00c"]]);export{ke as default};
