<template>
  <div class="template-categories-page">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
      <h1 class="page-title">分类管理</h1>
      <p class="page-description">管理简历模板分类</p>
        </div>
        <div class="header-right">
          <button @click="initCategories" class="btn-init" v-if="categoryTreeData.length === 0">
            🔧 初始化分类数据
          </button>
          <button @click="handleAdd" class="btn-primary">
            ➕ 新增分类
          </button>
        </div>
      </div>
    </div>
    
    <div class="page-content">
      <!-- 分类树展示 -->
      <div class="content-card">
        <div class="card-header">
          <span>分类树</span>
          <button @click="refreshData" class="refresh-btn" title="刷新">
            🔄
          </button>
        </div>
        
        <div class="tree-container">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner">⏳</div>
            <span>加载中...</span>
          </div>
          
          <div v-else-if="categoryTreeData.length === 0" class="empty-state">
        <div class="empty-icon">📁</div>
            <h3>暂无分类数据</h3>
            <p>点击上方"新增分类"按钮创建第一个分类</p>
          </div>
          
          <div v-else class="tree-list">
            <div v-for="parentCategory in categoryTreeData" :key="parentCategory.id" class="tree-parent">
              <div class="tree-node parent-node">
                <div class="node-content">
                  <button 
                    @click="toggleExpanded(parentCategory.id)" 
                    class="expand-btn"
                    :class="{ 'expanded': isExpanded(parentCategory.id) }"
                  >
                    {{ isExpanded(parentCategory.id) ? '▼' : '▶' }}
                  </button>
                  <span class="node-icon">📂</span>
                  <span class="node-name">{{ parentCategory.name }}</span>
                  <span class="node-code">[{{ parentCategory.code }}]</span>
                  <span class="node-type">{{ getCategoryTypeLabel(parentCategory.categoryType) }}</span>
                  <span class="node-count">({{ parentCategory.templateCount || 0 }}个模板)</span>
                </div>
                <div class="node-actions">
                  <button 
                    @click="handleAddChild(parentCategory)" 
                    class="action-btn add-btn"
                    title="添加子分类"
                  >
                    ➕ 添加子分类
                  </button>
                  <button 
                    @click="handleEdit(parentCategory)" 
                    class="action-btn edit-btn"
                    title="编辑"
                  >
                    ✏️ 编辑
                  </button>
                  <button 
                    @click="handleDelete(parentCategory)" 
                    class="action-btn delete-btn"
                    title="删除"
                  >
                    🗑️ 删除
                  </button>
                </div>
              </div>
              
              <!-- 子分类 -->
              <div v-if="parentCategory.children && parentCategory.children.length > 0 && isExpanded(parentCategory.id)" class="tree-children">
                <div v-for="childCategory in parentCategory.children" :key="childCategory.id" class="tree-node child-node">
                  <div class="node-content">
                    <span class="child-connector">└─</span>
                    <span class="node-icon">📄</span>
                    <span class="node-name">{{ childCategory.name }}</span>
                    <span class="node-code">[{{ childCategory.code }}]</span>
                    <span class="node-count">({{ childCategory.templateCount || 0 }}个模板)</span>
                  </div>
                  <div class="node-actions">
                    <button 
                      @click="handleEdit(childCategory)" 
                      class="action-btn edit-btn"
                      title="编辑"
                    >
                      ✏️ 编辑
                    </button>
                    <button 
                      @click="handleDelete(childCategory)" 
                      class="action-btn delete-btn"
                      title="删除"
                    >
                      🗑️ 删除
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑分类对话框 -->
    <div v-if="dialogVisible" class="modal-overlay" @click="closeDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ dialogTitle }}</h3>
          <button @click="closeDialog" class="close-btn">✕</button>
        </div>
        
        <form @submit.prevent="handleSave" class="modal-form">
          <div class="form-group">
            <label class="form-label required">分类名称</label>
            <input 
              v-model="formData.name" 
              type="text" 
              class="form-input"
              placeholder="请输入分类名称"
              :class="{ 'error': formErrors.name }"
              required
            />
            <span v-if="formErrors.name" class="error-message">{{ formErrors.name }}</span>
          </div>
          
          <div class="form-group">
            <label class="form-label required">分类代码</label>
            <input 
              v-model="formData.code" 
              type="text" 
              class="form-input"
              placeholder="请输入分类代码（英文字母、数字、连字符）"
              :class="{ 'error': formErrors.code }"
              pattern="[a-zA-Z0-9_-]+"
              required
            />
            <span v-if="formErrors.code" class="error-message">{{ formErrors.code }}</span>
          </div>
          
          <div class="form-group">
            <label class="form-label">分类描述</label>
            <textarea 
              v-model="formData.description" 
              class="form-textarea"
              rows="3"
              placeholder="请输入分类描述"
            ></textarea>
          </div>
          
          <!-- 父级分类选择 - 仅编辑时显示 -->
          <div v-if="isEdit && formData.parentId !== 0" class="form-group">
            <label class="form-label required">父级分类</label>
            <select v-model="formData.parentId" class="form-select" required>
              <option value="0">顶级分类</option>
              <option
                v-for="category in parentCategories"
                :key="category.id"
                :value="category.id"
              >
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <!-- 分类类型 - 移除此选项，新增分类默认为父级分类 -->
          <!-- 新增父级分类时不显示分类类型选择，直接设置为默认值 -->
          
          <!-- 添加子分类时显示父分类信息 -->
          <div v-if="addChildMode && currentParentCategory" class="form-group">
            <label class="form-label">父级分类</label>
            <div class="parent-info">
              <span class="parent-name">{{ currentParentCategory.name }}</span>
              <span class="parent-type">[{{ getCategoryTypeLabel(currentParentCategory.categoryType) }}]</span>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">排序权重</label>
            <input 
              v-model.number="formData.sortOrder" 
              type="number" 
              class="form-input"
              min="0" 
              max="9999"
              placeholder="数值越小越靠前"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">状态</label>
            <div class="radio-group">
              <label class="radio-option">
                <input type="radio" :value="1" v-model="formData.status" />
                <span>启用</span>
              </label>
              <label class="radio-option">
                <input type="radio" :value="0" v-model="formData.status" />
                <span>禁用</span>
              </label>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeDialog" class="btn-cancel">取消</button>
            <button type="submit" class="btn-primary" :disabled="saving">
              {{ saving ? '保存中...' : (isEdit ? '更新' : '创建') }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除对话框 -->
    <div v-if="deleteDialogVisible" class="modal-overlay" @click="cancelDelete">
      <div class="modal-content small" @click.stop>
        <div class="modal-header">
          <h3>删除确认</h3>
          <button @click="cancelDelete" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <p>确定要删除分类 "{{ deleteTarget?.name }}" 吗？</p>
          <p class="warning-text">此操作不可恢复，请谨慎操作。</p>
        </div>
        <div class="form-actions">
          <button @click="cancelDelete" class="btn-cancel">取消</button>
          <button @click="confirmDelete" class="btn-danger" :disabled="deleting">
            {{ deleting ? '删除中...' : '确定删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message.show" class="message-toast" :class="message.type">
      <span>{{ message.text }}</span>
      <button @click="hideMessage" class="message-close">✕</button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'

definePageMeta({
  title: '分类管理 - 管理后台',
  layout: 'admin'
})

// 响应式数据
const loading = ref(false)
const categoryTreeData = ref([])
const dialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const saving = ref(false)
const deleting = ref(false)
const isEdit = ref(false)
const parentCategories = ref([])
const deleteTarget = ref(null)
const expandedCategories = ref(new Set()) // 展开的分类ID
const addChildMode = ref(false) // 是否是添加子分类模式
const currentParentCategory = ref(null) // 当前要添加子分类的父分类

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  code: '',
  description: '',
  parentId: 0,
  categoryType: '',
  sortOrder: 0,
  status: 1
})

// 表单错误
const formErrors = ref({})

// 消息提示
const message = ref({
  show: false,
  type: 'success', // success, error, warning
  text: ''
})

// 计算属性
const dialogTitle = computed(() => {
  if (isEdit.value) {
    return '编辑分类'
  }
  if (addChildMode.value && currentParentCategory.value) {
    return `为"${currentParentCategory.value.name}"添加子分类`
  }
  return '新增父级分类'
})

// 获取分类类型标签
const getCategoryTypeLabel = (type) => {
  const typeMap = {
    style: '设计风格',
    industry: '适用行业',
    major: '高校专业'
  }
  return typeMap[type] || type
}

// 切换分类展开/折叠状态
const toggleExpanded = (categoryId) => {
  if (expandedCategories.value.has(categoryId)) {
    expandedCategories.value.delete(categoryId)
  } else {
    expandedCategories.value.add(categoryId)
  }
}

// 检查分类是否展开
const isExpanded = (categoryId) => {
  return expandedCategories.value.has(categoryId)
}

// 显示消息
const showMessage = (text, type = 'success') => {
  message.value = { show: true, text, type }
  setTimeout(() => {
    hideMessage()
  }, 3000)
}

// 隐藏消息
const hideMessage = () => {
  message.value.show = false
}

// 获取分类树数据
const fetchCategoryTree = async () => {
  try {
    loading.value = true
    const token = localStorage.getItem('admin_token')
    if (!token) {
      throw new Error('请先登录')
    }

    const response = await $fetch('/api/admin/template/category/tree', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.code === 200) {
      categoryTreeData.value = response.data || []
      
      // 提取父级分类（用于下拉选择）
      parentCategories.value = response.data?.filter(item => item.parentId === 0) || []
      
      // 默认展开第一个父级分类
      if (categoryTreeData.value.length > 0) {
        expandedCategories.value.clear()
        expandedCategories.value.add(categoryTreeData.value[0].id)
      }
    } else {
      throw new Error(response.msg || '获取分类数据失败')
    }
  } catch (error) {
    console.error('获取分类数据失败:', error)
    showMessage(error.message || '获取分类数据失败', 'error')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchCategoryTree()
}

// 验证表单
const validateForm = () => {
  formErrors.value = {}
  
  if (!formData.name.trim()) {
    formErrors.value.name = '分类名称不能为空'
  } else if (formData.name.length > 50) {
    formErrors.value.name = '分类名称长度不能超过50个字符'
  }
  
  if (!formData.code.trim()) {
    formErrors.value.code = '分类代码不能为空'
  } else if (!/^[a-zA-Z0-9_-]+$/.test(formData.code)) {
    formErrors.value.code = '分类代码只能包含字母、数字、下划线和连字符'
  } else if (formData.code.length > 50) {
    formErrors.value.code = '分类代码长度不能超过50个字符'
  }
  
  return Object.keys(formErrors.value).length === 0
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    description: '',
    parentId: 0,
    categoryType: '',
    sortOrder: 0,
    status: 1
  })
  formErrors.value = {}
  addChildMode.value = false
  currentParentCategory.value = null
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
  resetForm()
}

// 新增父级分类
const handleAdd = () => {
  resetForm()
  isEdit.value = false
  addChildMode.value = false
  formData.parentId = 0
  formData.categoryType = 'general' // 默认设置为通用类型
  dialogVisible.value = true
}

// 新增子分类
const handleAddChild = (parentCategory) => {
  resetForm()
  addChildMode.value = true
  currentParentCategory.value = parentCategory
  formData.parentId = parentCategory.id
  formData.categoryType = parentCategory.categoryType
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (category) => {
  resetForm()
  Object.assign(formData, {
    id: category.id,
    name: category.name,
    code: category.code,
    description: category.description || '',
    parentId: category.parentId,
    categoryType: category.categoryType,
    sortOrder: category.sortOrder || 0,
    status: category.status
  })
  isEdit.value = true
  addChildMode.value = false
  dialogVisible.value = true
}

// 删除分类
const handleDelete = (category) => {
  deleteTarget.value = category
  deleteDialogVisible.value = true
}

// 取消删除
const cancelDelete = () => {
  deleteDialogVisible.value = false
  deleteTarget.value = null
}

// 确认删除
const confirmDelete = async () => {
  if (!deleteTarget.value) return
  
  try {
    deleting.value = true
    const token = localStorage.getItem('admin_token')
    const response = await $fetch(`/api/admin/template/category/${deleteTarget.value.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.code === 200) {
      showMessage('删除成功', 'success')
      await fetchCategoryTree()
      deleteDialogVisible.value = false
      deleteTarget.value = null
    } else {
      throw new Error(response.msg || '删除失败')
    }
  } catch (error) {
    console.error('删除分类失败:', error)
    showMessage(error.message || '删除失败', 'error')
  } finally {
    deleting.value = false
  }
}

// 初始化分类数据
const initCategories = async () => {
  try {
    saving.value = true
    const token = localStorage.getItem('admin_token')
    
    const response = await $fetch('/api/admin/template/category/init', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.code === 200) {
      showMessage('分类数据初始化成功', 'success')
      await fetchCategoryTree()
    } else {
      throw new Error(response.msg || '初始化失败')
    }
  } catch (error) {
    console.error('初始化分类数据失败:', error)
    showMessage(error.message || '初始化失败', 'error')
  } finally {
    saving.value = false
  }
}

// 保存分类
const handleSave = async () => {
  if (!validateForm()) return
  
  try {
    saving.value = true
    const token = localStorage.getItem('admin_token')
    
    const response = await $fetch('/api/admin/template/category', {
      method: isEdit.value ? 'PUT' : 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: formData
    })

    if (response.code === 200) {
      showMessage(isEdit.value ? '更新成功' : '创建成功', 'success')
      dialogVisible.value = false
      await fetchCategoryTree()
      resetForm()
    } else {
      throw new Error(response.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存分类失败:', error)
    showMessage(error.message || '保存失败', 'error')
  } finally {
    saving.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCategoryTree()
})
</script>

<style scoped>
/* 页面布局 */
.template-categories-page {
  /* 移除padding，使用布局提供的padding */
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  margin-left: 24px;
}

.btn-primary {
  display: inline-block;
  padding: 12px 24px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s;
  text-decoration: none;
}

.btn-primary:hover {
  background: #66b1ff;
}

.btn-primary:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.btn-init {
  display: inline-block;
  padding: 12px 24px;
  background: #67c23a;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s;
  text-decoration: none;
  margin-right: 12px;
}

.btn-init:hover {
  background: #85ce61;
}

/* 内容卡片 */
.page-content {
  background: white;
  border-radius: 8px;
}

.content-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.card-header span {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.refresh-btn {
  padding: 8px;
  background: none;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background: #f5f7fa;
  border-color: #409eff;
}

/* 树形展示 */
.tree-container {
  padding: 24px;
  min-height: 300px;
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.loading-spinner {
  font-size: 24px;
  margin-bottom: 12px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}

.tree-parent {
  margin-bottom: 16px;
}

.tree-parent:last-child {
  margin-bottom: 0;
}

/* 展开折叠按钮 */
.expand-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;
  padding: 2px 4px;
  border-radius: 2px;
  transition: all 0.2s;
  min-width: 20px;
  text-align: center;
}

.expand-btn:hover {
  background: #f0f0f0;
  color: #409eff;
}

.expand-btn.expanded {
  color: #409eff;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.2s;
}

.tree-node:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
}

.parent-node {
  background: #fafbfc;
  border-color: #d3d4d6;
  font-weight: 500;
}

.child-node {
  margin-left: 24px;
  margin-top: 8px;
  background: white;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.child-connector {
  color: #c0c4cc;
  font-family: monospace;
  margin-right: 4px;
}

.node-icon {
  font-size: 16px;
}

.node-name {
  font-size: 14px;
  color: #303133;
}

.node-code {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.node-type {
  font-size: 12px;
  color: #409eff;
  background: #ecf5ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.node-count {
  font-size: 12px;
  color: #67c23a;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.action-btn {
  padding: 4px 8px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.add-btn {
  background: #e7f6ff;
  color: #409eff;
}

.add-btn:hover {
  background: #409eff;
  color: white;
}

.edit-btn {
  background: #fdf6ec;
  color: #e6a23c;
}

.edit-btn:hover {
  background: #e6a23c;
  color: white;
}

.delete-btn {
  background: #fef0f0;
  color: #f56c6c;
}

.delete-btn:hover {
  background: #f56c6c;
  color: white;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.small {
  max-width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #909399;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f5f7fa;
  color: #303133;
}

.modal-body {
  padding: 24px;
}

.warning-text {
  color: #f56c6c;
  font-size: 14px;
  margin-top: 8px;
}

/* 表单样式 */
.modal-form {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.form-label.required::after {
  content: ' *';
  color: #f56c6c;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #409eff;
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: #f56c6c;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.radio-group {
  display: flex;
  gap: 16px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.radio-option input[type="radio"] {
  width: auto;
  margin: 0;
}

.error-message {
  display: block;
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.btn-cancel {
  padding: 10px 20px;
  background: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-cancel:hover {
  background: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.btn-danger {
  padding: 10px 20px;
  background: #f56c6c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.btn-danger:hover {
  background: #f78989;
}

.btn-danger:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

/* 消息提示 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 2000;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: slideIn 0.3s ease;
}

.message-toast.success {
  background: #67c23a;
}

.message-toast.error {
  background: #f56c6c;
}

.message-toast.warning {
  background: #e6a23c;
}

.message-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 父分类信息显示 */
.parent-info {
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.parent-name {
  font-weight: 500;
  color: #303133;
}

.parent-type {
  color: #909399;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-right {
    margin-left: 0;
    align-self: flex-start;
  }
  
  .tree-node {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .node-actions {
    opacity: 1;
    align-self: flex-end;
  }
  
  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
}
</style> 