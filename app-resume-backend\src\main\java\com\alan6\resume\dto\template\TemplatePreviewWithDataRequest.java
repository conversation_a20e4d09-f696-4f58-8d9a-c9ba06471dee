package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 模板预览（带数据）请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板预览（带数据）请求DTO")
public class TemplatePreviewWithDataRequest {

    /**
     * 简历数据（必填，用于填充模板）
     */
    @NotNull(message = "简历数据不能为空")
    @Schema(description = "简历数据", example = "{\n" +
            "  \"basicInfo\": {\n" +
            "    \"realName\": \"张三\",\n" +
            "    \"phone\": \"13800138000\",\n" +
            "    \"email\": \"<EMAIL>\",\n" +
            "    \"jobIntention\": \"前端开发工程师\"\n" +
            "  },\n" +
            "  \"workExperiences\": [\n" +
            "    {\n" +
            "      \"companyName\": \"XX科技有限公司\",\n" +
            "      \"position\": \"前端开发工程师\",\n" +
            "      \"startDate\": \"2022-01-01\",\n" +
            "      \"endDate\": \"2024-12-01\",\n" +
            "      \"description\": \"负责前端开发工作...\"\n" +
            "    }\n" +
            "  ]\n" +
            "}")
    private Map<String, Object> resumeData;

    /**
     * 预览格式（可选，image:图片预览, html:HTML预览，默认: image）
     */
    @Pattern(regexp = "^(image|html)$", message = "预览格式只能是image或html")
    @Schema(description = "预览格式", example = "image", allowableValues = {"image", "html"})
    private String format = "image";

    /**
     * 图片质量（可选，当format=image时有效，low/medium/high，默认: high）
     */
    @Pattern(regexp = "^(low|medium|high)$", message = "图片质量只能是low、medium或high")
    @Schema(description = "图片质量", example = "high", allowableValues = {"low", "medium", "high"})
    private String quality = "high";

    /**
     * 预览宽度（可选，像素值，默认: 1200）
     */
    @Min(value = 200, message = "预览宽度最小值为200")
    @Max(value = 2000, message = "预览宽度最大值为2000")
    @Schema(description = "预览宽度", example = "1200")
    private Integer width = 1200;

    /**
     * 预览高度（可选，像素值，0表示自动计算）
     */
    @Min(value = 0, message = "预览高度不能为负数")
    @Max(value = 3000, message = "预览高度最大值为3000")
    @Schema(description = "预览高度", example = "0")
    private Integer height = 0;

    /**
     * 缩放比例（可选，0.5-2.0，默认: 1.0）
     */
    @DecimalMin(value = "0.5", message = "缩放比例最小值为0.5")
    @DecimalMax(value = "2.0", message = "缩放比例最大值为2.0")
    @Schema(description = "缩放比例", example = "1.0")
    private Double scale = 1.0;

    /**
     * 是否显示水印（可选，默认: false）
     */
    @Schema(description = "是否显示水印", example = "false")
    private Boolean showWatermark = false;
} 