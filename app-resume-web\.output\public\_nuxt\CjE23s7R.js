import{_ as ae}from"./BXnw39BI.js";import{k as g,l as B,r as E,e as ne,i as ie,c as i,a as o,f as le,m as de,p as V,q as k,v as A,s as y,t as b,x as F,d as _,y as ce,b as ue,w as me,o as l,n as ge}from"./CURHyiUL.js";import{A as U,H as j,u as fe}from"./CKDEb6Y1.js";import{$ as L}from"./D1FrdRFX.js";import{s as z}from"./x_rD_Ya3.js";import{_ as ve}from"./DlAUqK2U.js";const I=E(!1),d=B({loading:!1,countdown:0,canSend:!0,error:""});class x{static validatePhone(e){return e?e.length!==11?{isValid:!1,message:"手机号长度必须为11位"}:/^1[3-9]\d{9}$/.test(e)?{isValid:!0,message:""}:{isValid:!1,message:"请输入正确的手机号格式"}:{isValid:!1,message:"请输入手机号"}}static validatePassword(e){if(!e)return{isValid:!1,message:"请输入密码"};if(e.length<6)return{isValid:!1,message:"密码长度不能少于6位"};if(e.length>20)return{isValid:!1,message:"密码长度不能超过20位"};const a=/[a-zA-Z]/.test(e),t=/\d/.test(e);return!a||!t?{isValid:!1,message:"密码必须包含字母和数字"}:{isValid:!0,message:""}}static validateConfirmPassword(e,a){return a?e!==a?{isValid:!1,message:"两次输入的密码不一致"}:{isValid:!0,message:""}:{isValid:!1,message:"请确认密码"}}static validateSmsCode(e){return e?e.length!==6?{isValid:!1,message:"验证码长度为6位"}:/^\d{6}$/.test(e)?{isValid:!0,message:""}:{isValid:!1,message:"验证码只能包含数字"}:{isValid:!1,message:"请输入验证码"}}static validateAgreement(e){return e?{isValid:!0,message:""}:{isValid:!1,message:"请先阅读并同意用户协议和隐私政策"}}static validateRegisterForm(e){const a={};let t=!0;const n=this.validatePhone(e.phone);n.isValid||(a.phone=n.message,t=!1);const m=this.validatePassword(e.password);m.isValid||(a.password=m.message,t=!1);const v=this.validateConfirmPassword(e.password,e.confirmPassword);v.isValid||(a.confirmPassword=v.message,t=!1);const f=this.validateSmsCode(e.verificationCode);f.isValid||(a.verificationCode=f.message,t=!1);const c=this.validateAgreement(e.agreeTerms);return c.isValid||(a.agreeTerms=c.message,t=!1),{isValid:t,errors:a}}}class pe{static buildRegisterRequest(e){return{registerType:1,phone:e.phone.trim(),password:e.password,verificationCode:e.verificationCode.trim(),agreeTerms:e.agreeTerms,platform:"web"}}}class P{static async sendSmsCode(e){try{const a=x.validatePhone(e);if(!a.isValid)throw new Error(a.message);d.loading=!0,d.error="";const t=`${U.ENDPOINTS.AUTH.SEND_CODE}?phone=${encodeURIComponent(e.trim())}&platform=web`;return await j.post(t),this.startCountdown(),{success:!0,message:"验证码发送成功，请注意查收"}}catch(a){const t=a.message||"发送验证码失败，请重试";return d.error=t,{success:!1,error:t}}finally{d.loading=!1}}static startCountdown(){d.countdown=60,d.canSend=!1;const e=z(()=>{d.countdown--,d.countdown<=0&&(clearInterval(e),d.canSend=!0,d.countdown=0)},1e3)}static async register(e){try{I.value=!0;const a=x.validateRegisterForm(e);if(!a.isValid){const m=Object.values(a.errors)[0];throw new Error(m)}const t=pe.buildRegisterRequest(e),n=await j.post(U.ENDPOINTS.AUTH.REGISTER,t);return n.accessToken&&await this.handleRegistrationSuccess(n),{success:!0,data:n,message:"注册成功"}}catch(a){return{success:!1,error:a.message||"注册失败，请重试"}}finally{I.value=!1}}static async handleRegistrationSuccess(e){{console.log("🎯 开始处理注册成功逻辑:",e);const a=e.userInfo?{id:e.userInfo.id,userId:e.userInfo.id,username:e.userInfo.username,nickname:e.userInfo.nickname,phone:e.userInfo.phone,email:e.userInfo.email,avatar:e.userInfo.avatar,gender:e.userInfo.gender,status:e.userInfo.status,isPhoneVerified:e.userInfo.isPhoneVerified,preferredLanguage:e.userInfo.preferredLanguage,registerPlatform:e.userInfo.registerPlatform,lastLoginTime:e.userInfo.registerTime,membershipInfo:{isMember:e.userInfo.membershipLevel>0,memberLevel:e.userInfo.membershipLevel,expireTime:e.userInfo.membershipExpireTime,remainDays:e.userInfo.membershipExpireTime?Math.max(0,Math.floor((new Date(e.userInfo.membershipExpireTime)-new Date)/(1e3*60*60*24))):0},permissions:[],isNewUser:!0}:null;e.accessToken&&(localStorage.setItem("auth_token",e.accessToken),console.log("✅ Token已存储")),e.refreshToken&&(localStorage.setItem("refresh_token",e.refreshToken),console.log("✅ RefreshToken已存储")),a&&(localStorage.setItem("user_info",JSON.stringify(a)),console.log("✅ 用户信息已存储:",a)),console.log("✅ 注册成功数据处理完成，返回给调用方处理状态更新"),localStorage.setItem("register_response",JSON.stringify(e))}}static clearError(){d.error=""}static clearSuccess(){}static resetState(){I.value=!1,d.loading=!1,d.error=""}}const he=()=>{const C=g(()=>I.value),e=g(()=>d.loading),a=g(()=>d.canSend),t=g(()=>d.countdown),n=async u=>await P.register(u),m=async u=>await P.sendSmsCode(u),v=(u,w,M={})=>{switch(u){case"phone":return x.validatePhone(w);case"password":return x.validatePassword(w);case"confirmPassword":return x.validateConfirmPassword(M.password,w);case"verificationCode":return x.validateSmsCode(w);case"agreeTerms":return x.validateAgreement(w);default:return{isValid:!0,message:""}}},f=u=>x.validateRegisterForm(u),c=()=>{P.clearError()},S=()=>{P.clearSuccess()},T=()=>{P.resetState()};return{loading:we(I),smsCodeState:d,isLoading:C,smsLoading:e,canSendSms:a,smsCountdown:t,register:n,sendSmsCode:m,validateField:v,validateForm:f,clearError:c,clearSuccess:S,resetState:T}};function we(C){return g(()=>C)}const be={class:"pt-16 min-h-screen bg-gradient-to-br from-primary-50 to-orange-50 flex items-center justify-center"},xe={class:"max-w-md w-full space-y-8 p-8"},Ce={class:"form-group"},ye=["disabled"],ke={key:0,class:"mt-1 text-sm text-red-600"},_e={class:"form-group"},Se={class:"relative"},Te=["type","disabled"],Ve=["disabled"],Pe={key:0,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ie={key:1,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Me={key:0,class:"mt-1 text-sm text-red-600"},Re={class:"form-group"},Ee={class:"relative"},$e=["type","disabled"],Le=["disabled"],Be={key:0,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ne={key:1,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ae={key:0,class:"mt-1 text-sm text-red-600"},Fe={class:"form-group"},Ue={class:"flex space-x-3"},je=["disabled"],ze=["disabled"],Oe={key:0,class:"flex items-center"},qe={key:1},He={key:2},Je={class:"flex items-center"},De={key:3},Ge={key:0,class:"mt-1 text-sm text-red-600"},Ze={key:1,class:"mt-1 text-sm text-red-600"},Ke={class:"form-group"},Qe={class:"flex items-start space-x-3 cursor-pointer"},We=["disabled"],Xe={key:0,class:"mt-1 text-sm text-red-600"},Ye=["disabled"],es={key:0,class:"flex items-center justify-center"},ss={key:1},ts={class:"text-center"},os={class:"text-secondary-600"},rs={__name:"register",setup(C){ne({title:"注册 - 火花简历",description:"注册火花简历账户，开始制作专业简历，享受专业的简历制作服务"});const e=he(),a=fe(),t=B({phone:"",password:"",confirmPassword:"",verificationCode:"",agreeTerms:!1}),n=B({phone:"",password:"",confirmPassword:"",verificationCode:"",agreeTerms:""}),m=E(!1),v=E(!1),f=E(!1),c=g(()=>e.isLoading.value),S=g(()=>e.smsLoading.value),T=g(()=>e.canSendSms.value),u=g(()=>e.smsCountdown.value),w=g(()=>e.smsCodeState||{error:""}),M=g(()=>t.phone.trim()!==""&&t.password.trim()!==""&&t.confirmPassword.trim()!==""&&t.verificationCode.trim()!==""&&t.agreeTerms&&!c.value),N=()=>{const r=e.validateField("phone",t.phone);return n.phone=r.message,r.isValid},O=()=>{const r=e.validateField("password",t.password);return n.password=r.message,r.isValid},q=()=>{const r=e.validateField("confirmPassword",t.confirmPassword,t);return n.confirmPassword=r.message,r.isValid},H=()=>{const r=e.validateField("verificationCode",t.verificationCode);return n.verificationCode=r.message,r.isValid},J=()=>{const r=e.validateField("agreeTerms",t.agreeTerms);return n.agreeTerms=r.message,r.isValid},D=()=>{n.phone=""},G=()=>{n.password=""},Z=()=>{n.confirmPassword=""},K=()=>{n.verificationCode=""},Q=()=>{m.value=!m.value},W=()=>{v.value=!v.value},R=r=>{const s="w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 disabled:bg-secondary-50 disabled:cursor-not-allowed";return n[r]?`${s} border-red-300 focus:ring-red-500`:`${s} border-secondary-300`},X=()=>{const r="w-4 h-4 text-orange-600 border-secondary-300 rounded focus:ring-orange-500 disabled:cursor-not-allowed";return n.agreeTerms?`${r} border-red-300`:r},Y=()=>{const r="w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";return M.value?`${r} bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500`:`${r} bg-secondary-300 text-secondary-500 cursor-not-allowed`},ee=()=>{const r="px-4 py-3 text-sm font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 whitespace-nowrap";return f.value&&u.value>0?`${r} bg-green-600 text-white border-green-600 focus:ring-green-500 cursor-default`:!T.value||!t.phone||S.value?`${r} bg-secondary-300 text-secondary-500 cursor-not-allowed focus:ring-secondary-500`:`${r} bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500`},se=async()=>{if(!N())return;console.log("🚀 开始发送验证码...");const r=await e.sendSmsCode(t.phone);if(console.log("📱 发送验证码结果:",r),r.success){f.value=!0,console.log("✅ 验证码发送成功，按钮状态已更新为绿色");const s=z(()=>{u.value<=0&&(f.value=!1,clearInterval(s),console.log("⏰ 倒计时结束，按钮状态重置"))},1e3)}else console.log("❌ 发送失败:",r.error)},te=async()=>{console.log("🚀 开始注册...");const r=await e.register(t);if(console.log("📝 注册结果:",r),r.success){console.log("✅ 注册成功，开始处理自动登录");const s=r.data;console.log("📋 注册响应数据:",s),s.accessToken?(console.log("🔑 Token已由registerService存储，现在重新初始化认证状态..."),console.log("🔑 accessToken长度:",s.accessToken?s.accessToken.length:0),setTimeout(async()=>{const p=localStorage.getItem("auth_token");console.log("🔍 验证存储的Token:",p?`${p.substring(0,20)}...`:"null"),console.log("🔍 存储Token长度:",p?p.length:0),p&&(console.log("🔄 重新初始化认证状态"),await a.initAuth(),console.log("✅ 认证状态初始化完成"))},100),L.success("注册成功！欢迎加入火花简历"),setTimeout(()=>{console.log("🔄 跳转到首页"),ge("/",{replace:!0})},1500)):(console.error("❌ 注册响应缺少accessToken:",s),L.error("注册成功但登录状态更新失败，请手动登录"))}else console.log("❌ 注册失败:",r.error),L.error(r.error||"注册失败，请重试")},oe=()=>{console.log("显示用户协议")},re=()=>{console.log("显示隐私政策")};return ie(()=>{e.resetState()}),(r,s)=>{var $;const p=ae;return l(),i("div",be,[o("div",xe,[s[20]||(s[20]=le('<div class="text-center" data-v-9610368b><div class="w-16 h-16 bg-gradient-to-br from-primary-600 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4" data-v-9610368b><svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor" data-v-9610368b><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.19 0 2.34-.21 3.41-.6.3-.11.49-.4.49-.72 0-.43-.35-.78-.78-.78-.17 0-.33.06-.46.14-.82.29-1.69.44-2.58.44-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6c0 .89-.15 1.76-.44 2.58-.08.13-.14.29-.14.46 0 .43.35.78.78.78.32 0 .61-.19.72-.49.39-1.07.6-2.22.6-3.41C22 6.48 17.52 2 12 2z" data-v-9610368b></path><path d="M12 8l-2 6h4l-2-6z" data-v-9610368b></path></svg></div><h2 class="text-3xl font-display font-bold text-secondary-900" data-v-9610368b>创建账户</h2><p class="mt-2 text-secondary-600" data-v-9610368b>加入火花简历，开始制作专业简历</p></div>',1)),o("form",{onSubmit:de(te,["prevent"]),class:"space-y-6"},[o("div",Ce,[s[5]||(s[5]=o("label",{class:"block text-sm font-medium text-secondary-700 mb-2"},"手机号",-1)),V(o("input",{"onUpdate:modelValue":s[0]||(s[0]=h=>t.phone=h),type:"tel",class:y(R("phone")),placeholder:"请输入手机号",onBlur:N,onInput:D,disabled:c.value,maxlength:"11",autocomplete:"tel"},null,42,ye),[[A,t.phone]]),n.phone?(l(),i("div",ke,b(n.phone),1)):k("",!0)]),o("div",_e,[s[8]||(s[8]=o("label",{class:"block text-sm font-medium text-secondary-700 mb-2"},"密码",-1)),o("div",Se,[V(o("input",{"onUpdate:modelValue":s[1]||(s[1]=h=>t.password=h),type:m.value?"text":"password",class:y(R("password")),placeholder:"请设置密码（6-20位，包含字母和数字）",onBlur:O,onInput:G,disabled:c.value,maxlength:"20",autocomplete:"new-password"},null,42,Te),[[F,t.password]]),o("button",{type:"button",onClick:Q,class:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200",disabled:c.value},[m.value?(l(),i("svg",Pe,s[6]||(s[6]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.5 8.5M14.12 14.12L15.5 15.5"},null,-1)]))):(l(),i("svg",Ie,s[7]||(s[7]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))],8,Ve)]),n.password?(l(),i("div",Me,b(n.password),1)):k("",!0)]),o("div",Re,[s[11]||(s[11]=o("label",{class:"block text-sm font-medium text-secondary-700 mb-2"},"确认密码",-1)),o("div",Ee,[V(o("input",{"onUpdate:modelValue":s[2]||(s[2]=h=>t.confirmPassword=h),type:v.value?"text":"password",class:y(R("confirmPassword")),placeholder:"请再次输入密码",onBlur:q,onInput:Z,disabled:c.value,maxlength:"20",autocomplete:"new-password"},null,42,$e),[[F,t.confirmPassword]]),o("button",{type:"button",onClick:W,class:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200",disabled:c.value},[v.value?(l(),i("svg",Be,s[9]||(s[9]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.5 8.5M14.12 14.12L15.5 15.5"},null,-1)]))):(l(),i("svg",Ne,s[10]||(s[10]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))],8,Le)]),n.confirmPassword?(l(),i("div",Ae,b(n.confirmPassword),1)):k("",!0)]),o("div",Fe,[s[14]||(s[14]=o("label",{class:"block text-sm font-medium text-secondary-700 mb-2"},"短信验证码",-1)),o("div",Ue,[V(o("input",{"onUpdate:modelValue":s[3]||(s[3]=h=>t.verificationCode=h),type:"text",class:y([R("verificationCode"),"flex-1"]),placeholder:"请输入6位验证码",onBlur:H,onInput:K,disabled:c.value,maxlength:"6",autocomplete:"one-time-code"},null,42,je),[[A,t.verificationCode]]),o("button",{type:"button",onClick:se,disabled:!T.value||!t.phone||S.value,class:y(ee())},[S.value?(l(),i("span",Oe,s[12]||(s[12]=[o("div",{class:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"},null,-1),_(" 发送中 ")]))):!T.value&&u.value>0&&!f.value?(l(),i("span",qe,b(u.value)+"s后重发 ",1)):f.value&&u.value>0?(l(),i("span",He,[o("div",Je,[s[13]||(s[13]=o("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[o("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)),_(" 发送成功("+b(u.value)+"s) ",1)])])):(l(),i("span",De," 发送验证码 "))],10,ze)]),n.verificationCode?(l(),i("div",Ge,b(n.verificationCode),1)):k("",!0),($=w.value)!=null&&$.error?(l(),i("div",Ze,b(w.value.error),1)):k("",!0)]),o("div",Ke,[o("label",Qe,[V(o("input",{"onUpdate:modelValue":s[4]||(s[4]=h=>t.agreeTerms=h),type:"checkbox",class:y([X(),"mt-0.5"]),onChange:J,disabled:c.value},null,42,We),[[ce,t.agreeTerms]]),o("span",{class:"text-sm text-secondary-600 leading-relaxed"},[s[15]||(s[15]=_(" 我已阅读并同意 ")),o("button",{type:"button",onClick:oe,class:"text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200"}," 《用户协议》 "),s[16]||(s[16]=_(" 和 ")),o("button",{type:"button",onClick:re,class:"text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200"}," 《隐私政策》 ")])]),n.agreeTerms?(l(),i("div",Xe,b(n.agreeTerms),1)):k("",!0)]),o("button",{type:"submit",disabled:!M.value,class:y(Y())},[c.value?(l(),i("div",es,s[17]||(s[17]=[o("div",{class:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"},null,-1),o("span",null,"注册中...",-1)]))):(l(),i("span",ss,"注册账户"))],10,Ye)],32),o("div",ts,[o("p",os,[s[19]||(s[19]=_(" 已有账户？ ")),ue(p,{to:"/login",class:"text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200"},{default:me(()=>s[18]||(s[18]=[_(" 立即登录 ")])),_:1,__:[18]})])])])])}}},us=ve(rs,[["__scopeId","data-v-9610368b"]]);export{us as default};
