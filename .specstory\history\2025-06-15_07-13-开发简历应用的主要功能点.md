<!-- Generated by SpecStory -->

# 开发简历应用的主要功能点 (2025-06-15 15:13:37)

_**User**_

我打算开发一个基于模板创建简历的应用，包括web端和小程序，app。你帮我列出主要的功能点，我看看

---

_**Assistant**_



---

我来帮你梳理一个基于模板创建简历的应用的主要功能点。这类应用通常需要涵盖以下几个核心模块：

## 🔐 用户管理模块
- **用户注册/登录**（手机号、邮箱、微信授权等）
- **用户信息管理**（头像、昵称、联系方式等）
- **会员体系**（免费用户、VIP用户权限区分）
- **账号同步**（多端数据同步）

## 📋 模板管理模块
- **模板分类展示**（按行业、风格、颜色分类）
- **模板预览**（支持实时预览效果）
- **模板收藏**（用户可收藏喜欢的模板）
- **模板推荐**（根据用户偏好推荐）
- **自定义模板**（高级用户可自定义布局和样式）

## ✏️ 简历编辑模块
- **基本信息编辑**（姓名、联系方式、照片等）
- **教育经历管理**（学校、专业、时间、成绩等）
- **工作经历管理**（公司、职位、时间、工作内容等）
- **技能标签管理**（专业技能、熟练程度等）
- **项目经验管理**（项目名称、描述、技术栈等）
- **获奖荣誉管理**
- **自我评价编辑**
- **实时预览**（编辑时实时查看效果）
- **多套简历管理**（针对不同岗位创建不同版本）

## 📤 导出分享模块
- **多格式导出**（PDF、Word、图片等）
- **在线分享**（生成分享链接）
- **二维码分享**（小程序特别适用）
- **邮件发送**（直接通过邮件发送简历）
- **打印优化**（确保打印效果良好）

## 💾 数据管理模块
- **云端存储**（简历数据云端保存）
- **数据备份**（防止数据丢失）
- **历史版本**（保存编辑历史，支持回退）
- **数据导入**（从其他平台导入简历数据）

## 🎨 个性化定制
- **颜色主题调整**
- **字体样式选择**
- **布局微调**
- **模块显示隐藏**
- **照片裁剪美化**

## 📊 数据分析模块
- **简历浏览统计**（查看简历被浏览次数）
- **下载统计**
- **分享数据分析**
- **求职进度跟踪**

## 🔧 辅助功能
- **简历检查**（语法检查、格式规范提醒）
- **关键词优化建议**
- **行业模板推荐**
- **面试技巧提示**
- **求职状态管理**

## 💰 商业化功能
- **付费模板**（高级模板需付费使用）
- **会员服务**（无限制导出、高级功能等）
- **简历优化服务**（人工优化建议）
- **职业咨询服务**

## 📱 平台特色功能
**Web端特色：**
- 大屏编辑体验
- 键盘快捷键支持
- 批量操作功能

**小程序特色：**
- 微信直接分享
- 轻量快速创建
- 扫码查看简历

**App特色：**
- 离线编辑功能
- 推送通知
- 拍照识别文字

这些功能点可以根据你的目标用户群体和资源情况进行优先级排序，建议先实现核心的简历编辑和导出功能，再逐步完善其他增值功能。你觉得哪些功能是你最想优先实现的？

---

_**User**_

针对你刚才列出的功能点，基于mysql8编写数据库表，实体表要有逻辑删除，所有的表主键采用bigint，方便支持雪花算法。表名多个英文名的用下划线_隔开。每个字段都加上中文注释

---

_**Assistant**_



---

我来为你设计基于简历应用功能的MySQL 8数据库表结构：

```sql
-- ================================
-- 用户管理相关表
-- ================================

-- 用户表
CREATE TABLE `users` (
  `id` BIGINT NOT NULL COMMENT '用户ID（主键）',
  `username` VARCHAR(50) DEFAULT NULL COMMENT '用户名',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `password` VARCHAR(255) DEFAULT NULL COMMENT '密码（加密）',
  `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `nickname` VARCHAR(100) DEFAULT NULL COMMENT '昵称',
  `gender` TINYINT DEFAULT 0 COMMENT '性别（0:未知,1:男,2:女）',
  `birthday` DATE DEFAULT NULL COMMENT '生日',
  `register_type` TINYINT DEFAULT 1 COMMENT '注册类型（1:邮箱,2:手机,3:微信,4:QQ）',
  `openid_wechat` VARCHAR(100) DEFAULT NULL COMMENT '微信OpenID',
  `openid_qq` VARCHAR(100) DEFAULT NULL COMMENT 'QQ OpenID',
  `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
  `status` TINYINT DEFAULT 1 COMMENT '账号状态（0:禁用,1:正常）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_openid_wechat` (`openid_wechat`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户会员表
CREATE TABLE `user_memberships` (
  `id` BIGINT NOT NULL COMMENT '会员记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `membership_type` TINYINT NOT NULL COMMENT '会员类型（1:月会员,2:季会员,3:年会员,4:永久会员）',
  `start_time` DATETIME NOT NULL COMMENT '会员开始时间',
  `end_time` DATETIME NOT NULL COMMENT '会员结束时间',
  `order_no` VARCHAR(64) DEFAULT NULL COMMENT '订单号',
  `payment_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '支付金额',
  `payment_method` TINYINT DEFAULT NULL COMMENT '支付方式（1:微信,2:支付宝,3:其他）',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:失效,1:生效）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员表';

-- ================================
-- 简历模板相关表
-- ================================

-- 简历模板表
CREATE TABLE `resume_templates` (
  `id` BIGINT NOT NULL COMMENT '模板ID',
  `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `description` TEXT DEFAULT NULL COMMENT '模板描述',
  `preview_image_url` VARCHAR(500) DEFAULT NULL COMMENT '预览图URL',
  `template_data` JSON DEFAULT NULL COMMENT '模板结构数据（JSON格式）',
  `category_id` BIGINT DEFAULT NULL COMMENT '分类ID',
  `industry` VARCHAR(50) DEFAULT NULL COMMENT '适用行业',
  `style` VARCHAR(50) DEFAULT NULL COMMENT '模板风格',
  `color_scheme` VARCHAR(50) DEFAULT NULL COMMENT '配色方案',
  `is_premium` TINYINT DEFAULT 0 COMMENT '是否付费模板（0:免费,1:付费）',
  `price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '模板价格',
  `use_count` INT DEFAULT 0 COMMENT '使用次数',
  `like_count` INT DEFAULT 0 COMMENT '点赞数',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:下架,1:上架）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_is_premium` (`is_premium`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历模板表';

-- 模板分类表
CREATE TABLE `template_categories` (
  `id` BIGINT NOT NULL COMMENT '分类ID',
  `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '分类描述',
  `icon_url` VARCHAR(500) DEFAULT NULL COMMENT '分类图标URL',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父分类ID（0为顶级分类）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板分类表';

-- ================================
-- 简历相关表
-- ================================

-- 简历主表
CREATE TABLE `resumes` (
  `id` BIGINT NOT NULL COMMENT '简历ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `template_id` BIGINT DEFAULT NULL COMMENT '使用的模板ID',
  `name` VARCHAR(100) NOT NULL COMMENT '简历名称',
  `title` VARCHAR(100) DEFAULT NULL COMMENT '求职标题',
  `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱地址',
  `address` VARCHAR(200) DEFAULT NULL COMMENT '居住地址',
  `self_evaluation` TEXT DEFAULT NULL COMMENT '自我评价',
  `job_intention` VARCHAR(200) DEFAULT NULL COMMENT '求职意向',
  `expected_salary` VARCHAR(50) DEFAULT NULL COMMENT '期望薪资',
  `work_years` TINYINT DEFAULT NULL COMMENT '工作年限',
  `custom_styles` JSON DEFAULT NULL COMMENT '自定义样式配置',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公开（0:私有,1:公开）',
  `view_count` INT DEFAULT 0 COMMENT '浏览次数',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `share_count` INT DEFAULT 0 COMMENT '分享次数',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:草稿,1:完成）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历主表';

-- 教育经历表
CREATE TABLE `resume_educations` (
  `id` BIGINT NOT NULL COMMENT '教育经历ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `school_name` VARCHAR(100) NOT NULL COMMENT '学校名称',
  `major` VARCHAR(100) DEFAULT NULL COMMENT '专业',
  `degree` VARCHAR(50) DEFAULT NULL COMMENT '学历',
  `start_date` DATE DEFAULT NULL COMMENT '开始时间',
  `end_date` DATE DEFAULT NULL COMMENT '结束时间',
  `is_current` TINYINT DEFAULT 0 COMMENT '是否在读（0:否,1:是）',
  `gpa` VARCHAR(20) DEFAULT NULL COMMENT 'GPA',
  `description` TEXT DEFAULT NULL COMMENT '描述',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='教育经历表';

-- 工作经历表
CREATE TABLE `resume_work_experiences` (
  `id` BIGINT NOT NULL COMMENT '工作经历ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `company_name` VARCHAR(100) NOT NULL COMMENT '公司名称',
  `position` VARCHAR(100) NOT NULL COMMENT '职位',
  `department` VARCHAR(100) DEFAULT NULL COMMENT '部门',
  `start_date` DATE DEFAULT NULL COMMENT '开始时间',
  `end_date` DATE DEFAULT NULL COMMENT '结束时间',  
  `is_current` TINYINT DEFAULT 0 COMMENT '是否在职（0:否,1:是）',
  `job_description` TEXT DEFAULT NULL COMMENT '工作描述',
  `achievements` TEXT DEFAULT NULL COMMENT '主要成就',
  `salary_range` VARCHAR(50) DEFAULT NULL COMMENT '薪资范围',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作经历表';

-- 项目经验表
CREATE TABLE `resume_projects` (
  `id` BIGINT NOT NULL COMMENT '项目经验ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `project_name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `project_role` VARCHAR(50) DEFAULT NULL COMMENT '项目角色',
  `start_date` DATE DEFAULT NULL COMMENT '开始时间',
  `end_date` DATE DEFAULT NULL COMMENT '结束时间',
  `project_description` TEXT DEFAULT NULL COMMENT '项目描述',
  `responsibilities` TEXT DEFAULT NULL COMMENT '主要职责',
  `technologies` TEXT DEFAULT NULL COMMENT '技术栈',
  `project_url` VARCHAR(500) DEFAULT NULL COMMENT '项目链接',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_

---

_**User**_

暂停一下，小程序端有微信、抖音、百度等等

---

_**Assistant**_



---

好的，我理解了！小程序确实有多个平台（微信、抖音、百度、支付宝等），我需要重新设计用户登录相关的表结构来支持多平台。让我调整一下数据库设计：

```sql
-- ================================
-- 用户管理相关表（调整版）
-- ================================

-- 用户表
CREATE TABLE `users` (
  `id` BIGINT NOT NULL COMMENT '用户ID（主键）',
  `username` VARCHAR(50) DEFAULT NULL COMMENT '用户名',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `password` VARCHAR(255) DEFAULT NULL COMMENT '密码（加密）',
  `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `nickname` VARCHAR(100) DEFAULT NULL COMMENT '昵称',
  `gender` TINYINT DEFAULT 0 COMMENT '性别（0:未知,1:男,2:女）',
  `birthday` DATE DEFAULT NULL COMMENT '生日',
  `register_type` TINYINT DEFAULT 1 COMMENT '注册类型（1:邮箱,2:手机,3:第三方授权）',
  `register_platform` VARCHAR(20) DEFAULT NULL COMMENT '注册平台（web,wechat_mp,douyin_mp,baidu_mp,alipay_mp,app_ios,app_android）',
  `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_platform` VARCHAR(20) DEFAULT NULL COMMENT '最后登录平台',
  `status` TINYINT DEFAULT 1 COMMENT '账号状态（0:禁用,1:正常）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_register_platform` (`register_platform`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户第三方授权表
CREATE TABLE `user_third_party_auths` (
  `id` BIGINT NOT NULL COMMENT '授权记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `platform` VARCHAR(20) NOT NULL COMMENT '平台类型（wechat_mp:微信小程序,douyin_mp:抖音小程序,baidu_mp:百度小程序,alipay_mp:支付宝小程序,wechat_app:微信APP,qq:QQ等）',
  `openid` VARCHAR(100) NOT NULL COMMENT '平台OpenID',
  `unionid` VARCHAR(100) DEFAULT NULL COMMENT '平台UnionID（如果有）',
  `platform_nickname` VARCHAR(100) DEFAULT NULL COMMENT '平台昵称',
  `platform_avatar` VARCHAR(500) DEFAULT NULL COMMENT '平台头像',
  `platform_user_info` JSON DEFAULT NULL COMMENT '平台用户信息（JSON格式）',
  `access_token` VARCHAR(500) DEFAULT NULL COMMENT '访问令牌',
  `refresh_token` VARCHAR(500) DEFAULT NULL COMMENT '刷新令牌',
  `expires_in` INT DEFAULT NULL COMMENT 'token过期时间（秒）',
  `scope` VARCHAR(200) DEFAULT NULL COMMENT '授权范围',
  `bind_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_auth_time` DATETIME DEFAULT NULL COMMENT '最后授权时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:解绑,1:已绑定）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_openid` (`platform`, `openid`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_unionid` (`unionid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户第三方授权表';

-- 平台配置表
CREATE TABLE `platform_configs` (
  `id` BIGINT NOT NULL COMMENT '配置ID',
  `platform` VARCHAR(20) NOT NULL COMMENT '平台类型',
  `platform_name` VARCHAR(50) NOT NULL COMMENT '平台名称',
  `app_id` VARCHAR(100) NOT NULL COMMENT '应用ID',
  `app_secret` VARCHAR(200) NOT NULL COMMENT '应用密钥',
  `api_domain` VARCHAR(200) DEFAULT NULL COMMENT 'API域名',
  `auth_url` VARCHAR(500) DEFAULT NULL COMMENT '授权URL',
  `token_url` VARCHAR(500) DEFAULT NULL COMMENT '获取token的URL',
  `user_info_url` VARCHAR(500) DEFAULT NULL COMMENT '获取用户信息URL',
  `config_data` JSON DEFAULT NULL COMMENT '其他配置数据',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform` (`platform`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台配置表';
```

这样调整后的好处：

1. **支持多平台小程序**：微信、抖音、百度、支付宝等小程序都可以支持
2. **灵活的第三方授权管理**：通过独立的授权表管理各平台的登录信息
3. **平台配置集中管理**：可以动态配置各平台的API参数
4. **用户行为追踪**：可以知道用户从哪个平台注册和最后从哪个平台登录
5. **扩展性强**：未来新增平台只需要添加配置即可

你觉得这样的调整如何？我继续完成其他表的设计吗？

---

_**User**_

继续

---

_**Assistant**_



---

好的，我继续完成剩余的数据库表设计：

```sql
-- ================================
-- 简历相关表（续）
-- ================================

-- 技能标签表
CREATE TABLE `resume_skills` (
  `id` BIGINT NOT NULL COMMENT '技能ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `skill_name` VARCHAR(100) NOT NULL COMMENT '技能名称',
  `skill_level` TINYINT DEFAULT 1 COMMENT '技能水平（1:入门,2:熟练,3:精通,4:专家）',
  `skill_category` VARCHAR(50) DEFAULT NULL COMMENT '技能分类（编程语言、框架、工具等）',
  `years_of_experience` TINYINT DEFAULT NULL COMMENT '使用年限',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_skill_category` (`skill_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历技能表';

-- 获奖荣誉表
CREATE TABLE `resume_awards` (
  `id` BIGINT NOT NULL COMMENT '奖项ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `award_name` VARCHAR(100) NOT NULL COMMENT '奖项名称',
  `award_level` VARCHAR(50) DEFAULT NULL COMMENT '奖项级别（国家级、省级、市级等）',
  `issuing_organization` VARCHAR(100) DEFAULT NULL COMMENT '颁发机构',
  `award_date` DATE DEFAULT NULL COMMENT '获奖时间',
  `description` TEXT DEFAULT NULL COMMENT '获奖描述',
  `certificate_url` VARCHAR(500) DEFAULT NULL COMMENT '证书图片URL',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历获奖荣誉表';

-- 证书资质表
CREATE TABLE `resume_certificates` (
  `id` BIGINT NOT NULL COMMENT '证书ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `certificate_name` VARCHAR(100) NOT NULL COMMENT '证书名称',
  `issuing_organization` VARCHAR(100) DEFAULT NULL COMMENT '颁发机构',
  `certificate_number` VARCHAR(100) DEFAULT NULL COMMENT '证书编号',
  `issue_date` DATE DEFAULT NULL COMMENT '颁发时间',
  `expiry_date` DATE DEFAULT NULL COMMENT '到期时间',
  `is_permanent` TINYINT DEFAULT 0 COMMENT '是否永久有效（0:否,1:是）',
  `certificate_url` VARCHAR(500) DEFAULT NULL COMMENT '证书图片URL',
  `description` TEXT DEFAULT NULL COMMENT '证书描述',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历证书资质表';

-- 语言能力表
CREATE TABLE `resume_languages` (
  `id` BIGINT NOT NULL COMMENT '语言能力ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `language_name` VARCHAR(50) NOT NULL COMMENT '语言名称',
  `proficiency_level` TINYINT DEFAULT 1 COMMENT '熟练程度（1:入门,2:初级,3:中级,4:高级,5:母语）',
  `certificate_type` VARCHAR(50) DEFAULT NULL COMMENT '证书类型（如：CET-4、CET-6、TOEFL、IELTS等）',
  `certificate_score` VARCHAR(20) DEFAULT NULL COMMENT '证书成绩',
  `certificate_date` DATE DEFAULT NULL COMMENT '证书获得时间',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历语言能力表';

-- ================================
-- 用户行为相关表
-- ================================

-- 用户收藏表
CREATE TABLE `user_favorites` (
  `id` BIGINT NOT NULL COMMENT '收藏ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `target_type` TINYINT NOT NULL COMMENT '收藏类型（1:模板,2:简历）',
  `target_id` BIGINT NOT NULL COMMENT '目标ID（模板ID或简历ID）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`),
  KEY `idx_target` (`target_type`, `target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';

-- 分享记录表
CREATE TABLE `share_records` (
  `id` BIGINT NOT NULL COMMENT '分享记录ID',
  `user_id` BIGINT NOT NULL COMMENT '分享用户ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `share_type` TINYINT NOT NULL COMMENT '分享类型（1:链接分享,2:二维码分享,3:邮件分享）',
  `share_platform` VARCHAR(20) DEFAULT NULL COMMENT '分享平台（wechat,qq,weibo,email等）',
  `share_code` VARCHAR(64) NOT NULL COMMENT '分享码（用于生成分享链接）',
  `share_url` VARCHAR(500) DEFAULT NULL COMMENT '分享链接',
  `view_count` INT DEFAULT 0 COMMENT '查看次数',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `expire_time` DATETIME DEFAULT NULL COMMENT '过期时间',
  `is_active` TINYINT DEFAULT 1 COMMENT '是否有效（0:失效,1:有效）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_code` (`share_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享记录表';

-- 简历导出记录表
CREATE TABLE `resume_export_records` (
  `id` BIGINT NOT NULL COMMENT '导出记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `export_format` VARCHAR(20) NOT NULL COMMENT '导出格式（pdf,word,image,html）',
  `export_quality` VARCHAR(20) DEFAULT 'standard' COMMENT '导出质量（standard,high,print）',
  `file_size` BIGINT DEFAULT NULL COMMENT '文件大小（字节）',
  `file_url` VARCHAR(500) DEFAULT NULL COMMENT '文件URL',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `export_platform` VARCHAR(20) DEFAULT NULL COMMENT '导出平台',
  `export_ip` VARCHAR(50) DEFAULT NULL COMMENT '导出IP',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历导出记录表';

-- ================================
-- 数据统计分析表
-- ================================

-- 用户行为日志表
CREATE TABLE `user_behavior_logs` (
  `id` BIGINT NOT NULL COMMENT '日志ID',
  `user_id` BIGINT DEFAULT NULL COMMENT '用户ID（可为空，支持匿名用户）',
  `session_id` VARCHAR(64) DEFAULT NULL COMMENT '会话ID',
  `action_type` VARCHAR(50) NOT NULL COMMENT '行为类型（login,logout,view_template,edit_resume,export_resume等）',
  `target_type` VARCHAR(20) DEFAULT NULL COMMENT '目标类型（template,resume,user等）',
  `target_id` BIGINT DEFAULT NULL COMMENT '目标ID',
  `platform` VARCHAR(20) DEFAULT NULL COMMENT '操作平台',
  `device_info` JSON DEFAULT NULL COMMENT '设备信息',
  `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
  `referer` VARCHAR(500) DEFAULT NULL COMMENT '来源页面',
  `extra_data` JSON DEFAULT NULL COMMENT '额外数据',
  `duration` INT DEFAULT NULL COMMENT '操作耗时（毫秒）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_platform` (`platform`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为日志表';

-- 简历访问统计表
CREATE TABLE `resume_visit_stats` (
  `id` BIGINT NOT NULL COMMENT '统计ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `visitor_id` BIGINT DEFAULT NULL COMMENT '访问者ID（可为空）',
  `visitor_ip` VARCHAR(50) DEFAULT NULL COMMENT '访问者IP',
  `visit_source` VARCHAR(50) DEFAULT NULL COMMENT '访问来源（direct,share,search等）',
  `visit_platform` VARCHAR(20) DEFAULT NULL COMMENT '访问平台',
  `visit_duration` INT DEFAULT NULL COMMENT '访问时长（秒）',
  `page_views` INT DEFAULT 1 COMMENT '页面浏览数',
  `is_downloaded` TINYINT DEFAULT 0 COMMENT '是否下载（0:否,1:是）',
  `device_info` JSON DEFAULT NULL COMMENT '设备信息',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历访问统计表';

-- ================================
-- 支付订单相关表
-- ================================

-- 订单表
CREATE TABLE `orders` (
  `id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `order_type` TINYINT NOT NULL COMMENT '订单类型（1:会员充值,2:模板购买,3:增值服务）',
  `product_type` VARCHAR(50) DEFAULT NULL COMMENT '产品类型',
  `product_id` BIGINT DEFAULT NULL COMMENT '产品ID',
  `product_name` VARCHAR(100) DEFAULT NULL COMMENT '产品名称',
  `original_amount` DECIMAL(10,2) NOT NULL COMMENT '原价',
  `discount_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
  `final_amount` DECIMAL(10,2) NOT NULL COMMENT '实付金额',
  `currency` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
  `payment_method` TINYINT DEFAULT NULL COMMENT '支付方式（1:微信,2:支付宝,3:Apple Pay等）',
  `payment_platform` VARCHAR(20) DEFAULT NULL COMMENT '支付平台',
  `trade_no` VARCHAR(100) DEFAULT NULL COMMENT '第三方交易号',
  `order_status` TINYINT DEFAULT 1 COMMENT '订单状态（1:待支付,2:已支付,3:已完成,4:已取消,5:已退款）',
  `pay_time` DATETIME DEFAULT NULL COMMENT '支付时间',
  `complete_time` DATETIME DEFAULT NULL COMMENT '完成时间',
  `cancel_time` DATETIME DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` VARCHAR(200) DEFAULT NULL COMMENT '取消原因',
  `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 退款记录表
CREATE TABLE `refund_records` (
  `id` BIGINT NOT NULL COMMENT '退款记录ID',
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `refund_no` VARCHAR(64) NOT NULL COMMENT '退款单号',
  `refund_amount` DECIMAL(10,2) NOT NULL COMMENT '退款金额',
  `refund_reason` VARCHAR(200) DEFAULT NULL COMMENT '退款原因',
  `refund_type` TINYINT DEFAULT 1 COMMENT '退款类型（1:用户申请,2:系统自动,3:客服处理）',
  `refund_status` TINYINT DEFAULT 1 COMMENT '退款状态（1:申请中,2:处理中,3:已退款,4:已拒绝）',
  `third_party_refund_no` VARCHAR(100) DEFAULT NULL COMMENT '第三方退款单号',
  `process_time` DATETIME DEFAULT NULL COMMENT '处理时间',
  `complete_time` DATETIME DEFAULT NULL COMMENT '完成时间',
  `processor_id` BIGINT DEFAULT NULL COMMENT '处理人ID',
  `process_remark` VARCHAR(500) DEFAULT NULL COMMENT '处理备注',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_refund_status` (`refund_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- ================================
-- 系统配置相关表
-- ================================

-- 系统配置表
CREATE TABLE `system_configs` (
  `id` BIGINT NOT NULL COMMENT '配置ID',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT DEFAULT NULL COMMENT '配置值',
  `config_type` VARCHAR(20) DEFAULT 'string' COMMENT '配置类型（string,number,boolean,json）',
  `config_group` VARCHAR(50) DEFAULT NULL COMMENT '配置分组',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '配置描述',
  `is_encrypted` TINYINT DEFAULT 0 COMMENT '是否加密（0:否,1:是）',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公開（0:否,1:是）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 文件上传记录表
CREATE TABLE `file_upload_records` (
  `id` BIGINT NOT NULL COMMENT '文件ID',
  `user_id` BIGINT DEFAULT NULL COMMENT '上传用户ID',
  `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
  `original_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
  `file_type` VARCHAR(100) NOT NULL COMMENT '文件类型（MIME类型）',
  `file_extension` VARCHAR(20) NOT NULL COMMENT '文件扩展名',
  `upload_platform` VARCHAR(20) DEFAULT NULL COMMENT '上传平台',
  `storage_type` VARCHAR(20) DEFAULT 'local' COMMENT '存储类型（local,oss,cos,qiniu等）',
  `use_count` INT DEFAULT 0 COMMENT '使用次数',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公开（0:私有,1:公开）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传记录表';
```

至此，整个简历应用的数据库表结构设计完成！这套表结构涵盖了：

**核心功能模块：**
- 用户管理（多平台支持）
- 简历管理（完整的简历信息）
- 模板管理
- 用户行为追踪
- 支付订单管理
- 数据统计分析
- 系统配置管理

**设计特点：**
- 所有主键使用BIGINT，支持雪花算法
- 所有实体表都有逻辑删除字段
- 表名使用下划线分隔
- 每个字段都有详细的中文注释
- 支持多平台小程序（微信、抖音、百度、支付宝等）
- 预留了JSON字段用于扩展
- 考虑了数据统计和分析需求

你还需要调整哪些表结构，或者有其他特殊需求吗？

---

_**User**_

1、没有会员体系呢；
2、简历包含个人基本信息、学校学历、职业技能、工作经历、项目经历、研究经历、获奖荣誉、兴趣爱好、自我评价、自荐信等模块，模块可以排序，可以选择是否显示。

---

_**Assistant**_



---

你说得对！我来补充和调整这两个重要部分：

## 1. 会员体系相关表

```sql
-- ================================
-- 会员体系完善版
-- ================================

-- 会员套餐表
CREATE TABLE `membership_packages` (
  `id` BIGINT NOT NULL COMMENT '套餐ID',
  `package_name` VARCHAR(100) NOT NULL COMMENT '套餐名称',
  `package_code` VARCHAR(50) NOT NULL COMMENT '套餐代码',
  `description` TEXT DEFAULT NULL COMMENT '套餐描述',
  `duration_type` TINYINT NOT NULL COMMENT '时长类型（1:天,2:月,3:季,4:年,5:永久）',
  `duration_value` INT NOT NULL COMMENT '时长数值',
  `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
  `current_price` DECIMAL(10,2) NOT NULL COMMENT '现价',
  `features` JSON DEFAULT NULL COMMENT '套餐功能特权（JSON格式）',
  `max_resume_count` INT DEFAULT -1 COMMENT '最大简历数量（-1表示无限制）',
  `max_template_count` INT DEFAULT -1 COMMENT '可用模板数量（-1表示无限制）',
  `max_export_count` INT DEFAULT -1 COMMENT '每月导出次数（-1表示无限制）',
  `support_formats` VARCHAR(200) DEFAULT NULL COMMENT '支持导出格式（逗号分隔）',
  `is_recommended` TINYINT DEFAULT 0 COMMENT '是否推荐（0:否,1:是）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:下架,1:上架）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_package_code` (`package_code`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员套餐表';

-- 用户会员表（调整版）
CREATE TABLE `user_memberships` (
  `id` BIGINT NOT NULL COMMENT '会员记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `order_id` BIGINT DEFAULT NULL COMMENT '订单ID',
  `start_time` DATETIME NOT NULL COMMENT '会员开始时间',
  `end_time` DATETIME NOT NULL COMMENT '会员结束时间',
  `is_auto_renew` TINYINT DEFAULT 0 COMMENT '是否自动续费（0:否,1:是）',
  `used_resume_count` INT DEFAULT 0 COMMENT '已使用简历数量',
  `used_export_count` INT DEFAULT 0 COMMENT '本月已导出次数',
  `last_export_reset_time` DATETIME DEFAULT NULL COMMENT '导出次数最后重置时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:失效,1:生效,2:即将到期）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员表';

-- 会员权益使用记录表
CREATE TABLE `membership_usage_logs` (
  `id` BIGINT NOT NULL COMMENT '使用记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `membership_id` BIGINT NOT NULL COMMENT '会员记录ID',
  `usage_type` VARCHAR(50) NOT NULL COMMENT '使用类型（create_resume,export_pdf,use_premium_template等）',
  `resource_id` BIGINT DEFAULT NULL COMMENT '资源ID（简历ID、模板ID等）',
  `usage_count` INT DEFAULT 1 COMMENT '使用数量',
  `usage_date` DATE NOT NULL COMMENT '使用日期',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_membership_id` (`membership_id`),
  KEY `idx_usage_date` (`usage_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员权益使用记录表';
```

## 2. 简历模块化管理

```sql
-- ================================
-- 简历模块化管理
-- ================================

-- 简历模块配置表
CREATE TABLE `resume_module_configs` (
  `id` BIGINT NOT NULL COMMENT '模块配置ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `module_type` VARCHAR(50) NOT NULL COMMENT '模块类型（basic_info,education,skills,work_experience,project_experience,research_experience,awards,hobbies,self_evaluation,cover_letter）',
  `module_name` VARCHAR(100) NOT NULL COMMENT '模块名称',
  `is_visible` TINYINT DEFAULT 1 COMMENT '是否显示（0:隐藏,1:显示）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `custom_title` VARCHAR(100) DEFAULT NULL COMMENT '自定义标题',
  `module_settings` JSON DEFAULT NULL COMMENT '模块设置（JSON格式，如样式配置等）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_module` (`resume_id`, `module_type`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历模块配置表';

-- 简历主表（调整版，移除部分字段到模块配置）
CREATE TABLE `resumes` (
  `id` BIGINT NOT NULL COMMENT '简历ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `template_id` BIGINT DEFAULT NULL COMMENT '使用的模板ID',
  `name` VARCHAR(100) NOT NULL COMMENT '简历名称',
  `custom_styles` JSON DEFAULT NULL COMMENT '自定义样式配置',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公开（0:私有,1:公开）',
  `view_count` INT DEFAULT 0 COMMENT '浏览次数',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `share_count` INT DEFAULT 0 COMMENT '分享次数',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:草稿,1:完成）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历主表';

-- 简历基本信息表
CREATE TABLE `resume_basic_info` (
  `id` BIGINT NOT NULL COMMENT '基本信息ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
  `english_name` VARCHAR(100) DEFAULT NULL COMMENT '英文名',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱地址',
  `wechat` VARCHAR(50) DEFAULT NULL COMMENT '微信号',
  `address` VARCHAR(200) DEFAULT NULL COMMENT '居住地址',
  `current_city` VARCHAR(50) DEFAULT NULL COMMENT '现居城市',
  `hometown` VARCHAR(50) DEFAULT NULL COMMENT '籍贯',
  `birthday` DATE DEFAULT NULL COMMENT '出生日期',
  `gender` TINYINT DEFAULT 0 COMMENT '性别（0:未知,1:男,2:女）',
  `marital_status` TINYINT DEFAULT 0 COMMENT '婚姻状况（0:未知,1:未婚,2:已婚）',
  `job_intention` VARCHAR(200) DEFAULT NULL COMMENT '求职意向',
  `expected_salary` VARCHAR(50) DEFAULT NULL COMMENT '期望薪资',
  `expected_city` VARCHAR(100) DEFAULT NULL COMMENT '期望工作城市',
  `work_years` TINYINT DEFAULT NULL COMMENT '工作年限',
  `personal_website` VARCHAR(200) DEFAULT NULL COMMENT '个人网站',
  `github_url` VARCHAR(200) DEFAULT NULL COMMENT 'GitHub地址',
  `linkedin_url` VARCHAR(200) DEFAULT NULL COMMENT 'LinkedIn地址',
  `portfolio_url` VARCHAR(200) DEFAULT NULL COMMENT '作品集地址',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历基本信息表';

-- 研究经历表（新增）
CREATE TABLE `resume_research_experiences` (
  `id` BIGINT NOT NULL COMMENT '研究经历ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `research_title` VARCHAR(200) NOT NULL COMMENT '研究课题',
  `research_type` VARCHAR(50) DEFAULT NULL COMMENT '研究类型（学术研究、产业研究、毕业论文等）',
  `institution` VARCHAR(100) DEFAULT NULL COMMENT '研究机构',
  `supervisor` VARCHAR(50) DEFAULT NULL COMMENT '指导老师/导师',
  `start_date` DATE DEFAULT NULL COMMENT '开始时间',
  `end_date` DATE DEFAULT NULL COMMENT '结束时间',
  `is_current` TINYINT DEFAULT 0 COMMENT '是否进行中（0:否,1:是）',
  `research_field` VARCHAR(100) DEFAULT NULL COMMENT '研究领域',
  `keywords` VARCHAR(200) DEFAULT NULL COMMENT '关键词（逗号分隔）',
  `description` TEXT DEFAULT NULL COMMENT '研究描述',
  `achievements` TEXT DEFAULT NULL COMMENT '研究成果',
  `publications` TEXT DEFAULT NULL COMMENT '发表论文',
  `research_url` VARCHAR(500) DEFAULT NULL COMMENT '研究链接',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历研究经历表';

-- 兴趣爱好表（新增）
CREATE TABLE `resume_hobbies` (
  `id` BIGINT NOT NULL COMMENT '兴趣爱好ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `hobby_name` VARCHAR(100) NOT NULL COMMENT '兴趣爱好名称',
  `hobby_category` VARCHAR(50) DEFAULT NULL COMMENT '兴趣分类（运动、音乐、阅读、旅行等）',
  `proficiency_level` TINYINT DEFAULT 1 COMMENT '熟练程度（1:入门,2:业余,3:熟练,4:专业）',
  `years_of_experience` TINYINT DEFAULT NULL COMMENT '从事年限',
  `description` TEXT DEFAULT NULL COMMENT '详细描述',
  `achievements` TEXT DEFAULT NULL COMMENT '相关成就',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_hobby_category` (`hobby_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历兴趣爱好表';

-- 自我评价表
CREATE TABLE `resume_self_evaluations` (
  `id` BIGINT NOT NULL COMMENT '自我评价ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `content` TEXT NOT NULL COMMENT '自我评价内容',
  `word_count` INT DEFAULT 0 COMMENT '字数统计',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历自我评价表';

-- 自荐信表（新增）
CREATE TABLE `resume_cover_letters` (
  `id` BIGINT NOT NULL COMMENT '自荐信ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `title` VARCHAR(100) DEFAULT NULL COMMENT '自荐信标题',
  `content` TEXT NOT NULL COMMENT '自荐信内容',
  `target_company` VARCHAR(100) DEFAULT NULL COMMENT '目标公司',
  `target_position` VARCHAR(100) DEFAULT NULL COMMENT '目标职位',
  `word_count` INT DEFAULT 0 COMMENT '字数统计',
  `template_type` VARCHAR(50) DEFAULT 'default' COMMENT '模板类型',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历自荐信表';

-- 简历模块默认配置表
CREATE TABLE `default_module_configs` (
  `id` BIGINT NOT NULL COMMENT '默认配置ID',
  `module_type` VARCHAR(50) NOT NULL COMMENT '模块类型',
  `module_name` VARCHAR(100) NOT NULL COMMENT '模块名称',
  `default_visible` TINYINT DEFAULT 1 COMMENT '默认是否显示',
  `default_sort_order` INT DEFAULT 0 COMMENT '默认排序权重',
  `is_required` TINYINT DEFAULT 0 COMMENT '是否必须（0:否,1:是）',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '模块描述',
  `icon` VARCHAR(100) DEFAULT NULL COMMENT '模块图标',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_module_type` (`module_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历模块默认配置表';
```

## 3. 初始化默认模块配置数据

```sql
-- 插入默认模块配置
INSERT INTO `default_module_configs` (`id`, `module_type`, `module_name`, `default_visible`, `default_sort_order`, `is_required`, `description`) VALUES
(1, 'basic_info', '个人基本信息', 1, 1, 1, '包含姓名、联系方式、头像等基础信息'),
(2, 'education', '教育经历', 1, 2, 0, '学校、专业、学历等教育背景'),
(3, 'skills', '职业技能', 1, 3, 0, '专业技能、熟练程度等'),
(4, 'work_experience', '工作经历', 1, 4, 0, '工作经验、职位、公司等'),
(5, 'project_experience', '项目经历', 1, 5, 0, '参与的项目经验'),
(6, 'research_experience', '研究经历', 0, 6, 0, '学术研究、论文发表等'),
(7, 'awards', '获奖荣誉', 0, 7, 0, '获得的奖项和荣誉'),
(8, 'certificates', '证书资质', 0, 8, 0, '专业证书、资格认证等'),
(9, 'languages', '语言能力', 0, 9, 0, '掌握的语言及熟练程度'),
(10, 'hobbies', '兴趣爱好', 0, 10, 0, '个人兴趣爱好'),
(11, 'self_evaluation', '自我评价', 1, 11, 0, '个人能力和特点的自我描述'),
(12, 'cover_letter', '自荐信', 0, 12, 0, '求职自荐信');
```

这样调整后的设计特点：

**会员体系完善：**
- 灵活的套餐配置
- 详细的权益管理
- 使用记录追踪

**简历模块化管理：**
- 支持模块显示/隐藏
- 支持自定义排序
- 支持自定义模块标题
- 新增研究经历、兴趣爱好、自荐信等模块
- 通过配置表统一管理所有模块

这样设计更符合你的需求吗？

---

_**User**_

所有表汇总一下，我再看看

---

_**Assistant**_



---

好的，我来汇总一下所有的数据库表设计：

```sql
-- ================================
-- 简历应用数据库表设计汇总
-- ================================

-- ================================
-- 用户管理相关表
-- ================================

-- 用户表
CREATE TABLE `users` (
  `id` BIGINT NOT NULL COMMENT '用户ID（主键）',
  `username` VARCHAR(50) DEFAULT NULL COMMENT '用户名',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `password` VARCHAR(255) DEFAULT NULL COMMENT '密码（加密）',
  `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `nickname` VARCHAR(100) DEFAULT NULL COMMENT '昵称',
  `gender` TINYINT DEFAULT 0 COMMENT '性别（0:未知,1:男,2:女）',
  `birthday` DATE DEFAULT NULL COMMENT '生日',
  `register_type` TINYINT DEFAULT 1 COMMENT '注册类型（1:邮箱,2:手机,3:第三方授权）',
  `register_platform` VARCHAR(20) DEFAULT NULL COMMENT '注册平台（web,wechat_mp,douyin_mp,baidu_mp,alipay_mp,app_ios,app_android）',
  `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_platform` VARCHAR(20) DEFAULT NULL COMMENT '最后登录平台',
  `status` TINYINT DEFAULT 1 COMMENT '账号状态（0:禁用,1:正常）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_register_platform` (`register_platform`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户第三方授权表
CREATE TABLE `user_third_party_auths` (
  `id` BIGINT NOT NULL COMMENT '授权记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `platform` VARCHAR(20) NOT NULL COMMENT '平台类型（wechat_mp:微信小程序,douyin_mp:抖音小程序,baidu_mp:百度小程序,alipay_mp:支付宝小程序,wechat_app:微信APP,qq:QQ等）',
  `openid` VARCHAR(100) NOT NULL COMMENT '平台OpenID',
  `unionid` VARCHAR(100) DEFAULT NULL COMMENT '平台UnionID（如果有）',
  `platform_nickname` VARCHAR(100) DEFAULT NULL COMMENT '平台昵称',
  `platform_avatar` VARCHAR(500) DEFAULT NULL COMMENT '平台头像',
  `platform_user_info` JSON DEFAULT NULL COMMENT '平台用户信息（JSON格式）',
  `access_token` VARCHAR(500) DEFAULT NULL COMMENT '访问令牌',
  `refresh_token` VARCHAR(500) DEFAULT NULL COMMENT '刷新令牌',
  `expires_in` INT DEFAULT NULL COMMENT 'token过期时间（秒）',
  `scope` VARCHAR(200) DEFAULT NULL COMMENT '授权范围',
  `bind_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_auth_time` DATETIME DEFAULT NULL COMMENT '最后授权时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:解绑,1:已绑定）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_openid` (`platform`, `openid`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_unionid` (`unionid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户第三方授权表';

-- 平台配置表
CREATE TABLE `platform_configs` (
  `id` BIGINT NOT NULL COMMENT '配置ID',
  `platform` VARCHAR(20) NOT NULL COMMENT '平台类型',
  `platform_name` VARCHAR(50) NOT NULL COMMENT '平台名称',
  `app_id` VARCHAR(100) NOT NULL COMMENT '应用ID',
  `app_secret` VARCHAR(200) NOT NULL COMMENT '应用密钥',
  `api_domain` VARCHAR(200) DEFAULT NULL COMMENT 'API域名',
  `auth_url` VARCHAR(500) DEFAULT NULL COMMENT '授权URL',
  `token_url` VARCHAR(500) DEFAULT NULL COMMENT '获取token的URL',
  `user_info_url` VARCHAR(500) DEFAULT NULL COMMENT '获取用户信息URL',
  `config_data` JSON DEFAULT NULL COMMENT '其他配置数据',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform` (`platform`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台配置表';

-- ================================
-- 会员体系相关表
-- ================================

-- 会员套餐表
CREATE TABLE `membership_packages` (
  `id` BIGINT NOT NULL COMMENT '套餐ID',
  `package_name` VARCHAR(100) NOT NULL COMMENT '套餐名称',
  `package_code` VARCHAR(50) NOT NULL COMMENT '套餐代码',
  `description` TEXT DEFAULT NULL COMMENT '套餐描述',
  `duration_type` TINYINT NOT NULL COMMENT '时长类型（1:天,2:月,3:季,4:年,5:永久）',
  `duration_value` INT NOT NULL COMMENT '时长数值',
  `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
  `current_price` DECIMAL(10,2) NOT NULL COMMENT '现价',
  `features` JSON DEFAULT NULL COMMENT '套餐功能特权（JSON格式）',
  `max_resume_count` INT DEFAULT -1 COMMENT '最大简历数量（-1表示无限制）',
  `max_template_count` INT DEFAULT -1 COMMENT '可用模板数量（-1表示无限制）',
  `max_export_count` INT DEFAULT -1 COMMENT '每月导出次数（-1表示无限制）',
  `support_formats` VARCHAR(200) DEFAULT NULL COMMENT '支持导出格式（逗号分隔）',
  `is_recommended` TINYINT DEFAULT 0 COMMENT '是否推荐（0:否,1:是）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:下架,1:上架）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_package_code` (`package_code`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员套餐表';

-- 用户会员表
CREATE TABLE `user_memberships` (
  `id` BIGINT NOT NULL COMMENT '会员记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `order_id` BIGINT DEFAULT NULL COMMENT '订单ID',
  `start_time` DATETIME NOT NULL COMMENT '会员开始时间',
  `end_time` DATETIME NOT NULL COMMENT '会员结束时间',
  `is_auto_renew` TINYINT DEFAULT 0 COMMENT '是否自动续费（0:否,1:是）',
  `used_resume_count` INT DEFAULT 0 COMMENT '已使用简历数量',
  `used_export_count` INT DEFAULT 0 COMMENT '本月已导出次数',
  `last_export_reset_time` DATETIME DEFAULT NULL COMMENT '导出次数最后重置时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:失效,1:生效,2:即将到期）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员表';

-- 会员权益使用记录表
CREATE TABLE `membership_usage_logs` (
  `id` BIGINT NOT NULL COMMENT '使用记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `membership_id` BIGINT NOT NULL COMMENT '会员记录ID',
  `usage_type` VARCHAR(50) NOT NULL COMMENT '使用类型（create_resume,export_pdf,use_premium_template等）',
  `resource_id` BIGINT DEFAULT NULL COMMENT '资源ID（简历ID、模板ID等）',
  `usage_count` INT DEFAULT 1 COMMENT '使用数量',
  `usage_date` DATE NOT NULL COMMENT '使用日期',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_membership_id` (`membership_id`),
  KEY `idx_usage_date` (`usage_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员权益使用记录表';

-- ================================
-- 简历模板相关表
-- ================================

-- 模板分类表
CREATE TABLE `template_categories` (
  `id` BIGINT NOT NULL COMMENT '分类ID',
  `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '分类描述',
  `icon_url` VARCHAR(500) DEFAULT NULL COMMENT '分类图标URL',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父分类ID（0为顶级分类）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板分类表';

-- 简历模板表
CREATE TABLE `resume_templates` (
  `id` BIGINT NOT NULL COMMENT '模板ID',
  `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `description` TEXT DEFAULT NULL COMMENT '模板描述',
  `preview_image_url` VARCHAR(500) DEFAULT NULL COMMENT '预览图URL',
  `template_data` JSON DEFAULT NULL COMMENT '模板结构数据（JSON格式）',
  `category_id` BIGINT DEFAULT NULL COMMENT '分类ID',
  `industry` VARCHAR(50) DEFAULT NULL COMMENT '适用行业',
  `style` VARCHAR(50) DEFAULT NULL COMMENT '模板风格',
  `color_scheme` VARCHAR(50) DEFAULT NULL COMMENT '配色方案',
  `is_premium` TINYINT DEFAULT 0 COMMENT '是否付费模板（0:免费,1:付费）',
  `price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '模板价格',
  `use_count` INT DEFAULT 0 COMMENT '使用次数',
  `like_count` INT DEFAULT 0 COMMENT '点赞数',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:下架,1:上架）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_is_premium` (`is_premium`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历模板表';

-- ================================
-- 简历相关表
-- ================================

-- 简历主表
CREATE TABLE `resumes` (
  `id` BIGINT NOT NULL COMMENT '简历ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `template_id` BIGINT DEFAULT NULL COMMENT '使用的模板ID',
  `name` VARCHAR(100) NOT NULL COMMENT '简历名称',
  `custom_styles` JSON DEFAULT NULL COMMENT '自定义样式配置',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公开（0:私有,1:公开）',
  `view_count` INT DEFAULT 0 COMMENT '浏览次数',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `share_count` INT DEFAULT 0 COMMENT '分享次数',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:草稿,1:完成）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历主表';

-- 简历模块默认配置表
CREATE TABLE `default_module_configs` (
  `id` BIGINT NOT NULL COMMENT '默认配置ID',
  `module_type` VARCHAR(50) NOT NULL COMMENT '模块类型',
  `module_name` VARCHAR(100) NOT NULL COMMENT '模块名称',
  `default_visible` TINYINT DEFAULT 1 COMMENT '默认是否显示',
  `default_sort_order` INT DEFAULT 0 COMMENT '默认排序权重',
  `is_required` TINYINT DEFAULT 0 COMMENT '是否必须（0:否,1:是）',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '模块描述',
  `icon` VARCHAR(100) DEFAULT NULL COMMENT '模块图标',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_module_type` (`module_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历模块默认配置表';

-- 简历模块配置表
CREATE TABLE `resume_module_configs` (
  `id` BIGINT NOT NULL COMMENT '模块配置ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `module_type` VARCHAR(50) NOT NULL COMMENT '模块类型（basic_info,education,skills,work_experience,project_experience,research_experience,awards,hobbies,self_evaluation,cover_letter）',
  `module_name` VARCHAR(100) NOT NULL COMMENT '模块名称',
  `is_visible` TINYINT DEFAULT 1 COMMENT '是否显示（0:隐藏,1:显示）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `custom_title` VARCHAR(100) DEFAULT NULL COMMENT '自定义标题',
  `module_settings` JSON DEFAULT NULL COMMENT '模块设置（JSON格式，如样式配置等）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_module` (`resume_id`, `module_type`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历模块配置表';

-- 简历基本信息表
CREATE TABLE `resume_basic_info` (
  `id` BIGINT NOT NULL COMMENT '基本信息ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
  `english_name` VARCHAR(100) DEFAULT NULL COMMENT '英文名',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱地址',
  `wechat` VARCHAR(50) DEFAULT NULL COMMENT '微信号',
  `address` VARCHAR(200) DEFAULT NULL COMMENT '居住地址',
  `current_city` VARCHAR(50) DEFAULT NULL COMMENT '现居城市',
  `hometown` VARCHAR(50) DEFAULT NULL COMMENT '籍贯',
  `birthday` DATE DEFAULT NULL COMMENT '出生日期',
  `gender` TINYINT DEFAULT 0 COMMENT '性别（0:未知,1:男,2:女）',
  `marital_status` TINYINT DEFAULT 0 COMMENT '婚姻状况（0:未知,1:未婚,2:已婚）',
  `job_intention` VARCHAR(200) DEFAULT NULL COMMENT '求职意向',
  `expected_salary` VARCHAR(50) DEFAULT NULL COMMENT '期望薪资',
  `expected_city` VARCHAR(100) DEFAULT NULL COMMENT '期望工作城市',
  `work_years` TINYINT DEFAULT NULL COMMENT '工作年限',
  `personal_website` VARCHAR(200) DEFAULT NULL COMMENT '个人网站',
  `github_url` VARCHAR(200) DEFAULT NULL COMMENT 'GitHub地址',
  `linkedin_url` VARCHAR(200) DEFAULT NULL COMMENT 'LinkedIn地址',
  `portfolio_url` VARCHAR(200) DEFAULT NULL COMMENT '作品集地址',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历基本信息表';

-- 教育经历表
CREATE TABLE `resume_educations` (
  `id` BIGINT NOT NULL COMMENT '教育经历ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `school_name` VARCHAR(100) NOT NULL COMMENT '学校名称',
  `major` VARCHAR(100) DEFAULT NULL COMMENT '专业',
  `degree` VARCHAR(50) DEFAULT NULL COMMENT '学历',
  `start_date` DATE DEFAULT NULL COMMENT '开始时间',
  `end_date` DATE DEFAULT NULL COMMENT '结束时间',
  `is_current` TINYINT DEFAULT 0 COMMENT '是否在读（0:否,1:是）',
  `gpa` VARCHAR(20) DEFAULT NULL COMMENT 'GPA',
  `description` TEXT DEFAULT NULL COMMENT '描述',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='教育经历表';

-- 技能标签表
CREATE TABLE `resume_skills` (
  `id` BIGINT NOT NULL COMMENT '技能ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `skill_name` VARCHAR(100) NOT NULL COMMENT '技能名称',
  `skill_level` TINYINT DEFAULT 1 COMMENT '技能水平（1:入门,2:熟练,3:精通,4:专家）',
  `skill_category` VARCHAR(50) DEFAULT NULL COMMENT '技能分类（编程语言、框架、工具等）',
  `years_of_experience` TINYINT DEFAULT NULL COMMENT '使用年限',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_skill_category` (`skill_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历技能表';

-- 工作经历表
CREATE TABLE `resume_work_experiences` (
  `id` BIGINT NOT NULL COMMENT '工作经历ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `company_name` VARCHAR(100) NOT NULL COMMENT '公司名称',
  `position` VARCHAR(100) NOT NULL COMMENT '职位',
  `department` VARCHAR(100) DEFAULT NULL COMMENT '部门',
  `start_date` DATE DEFAULT NULL COMMENT '开始时间',
  `end_date` DATE DEFAULT NULL COMMENT '结束时间',  
  `is_current` TINYINT DEFAULT 0 COMMENT '是否在职（0:否,1:是）',
  `job_description` TEXT DEFAULT NULL COMMENT '工作描述',
  `achievements` TEXT DEFAULT NULL COMMENT '主要成就',
  `salary_range` VARCHAR(50) DEFAULT NULL COMMENT '薪资范围',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作经历表';

-- 项目经验表
CREATE TABLE `resume_projects` (
  `id` BIGINT NOT NULL COMMENT '项目经验ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `project_name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `project_role` VARCHAR(50) DEFAULT NULL COMMENT '项目角色',
  `start_date` DATE DEFAULT NULL COMMENT '开始时间',
  `end_date` DATE DEFAULT NULL COMMENT '结束时间',
  `project_description` TEXT DEFAULT NULL COMMENT '项目描述',
  `responsibilities` TEXT DEFAULT NULL COMMENT '主要职责',
  `technologies` TEXT DEFAULT NULL COMMENT '技术栈',
  `project_url` VARCHAR(500) DEFAULT NULL COMMENT '项目链接',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目经验表';

-- 研究经历表
CREATE TABLE `resume_research_experiences` (
  `id` BIGINT NOT NULL COMMENT '研究经历ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `research_title` VARCHAR(200) NOT NULL COMMENT '研究课题',
  `research_type` VARCHAR(50) DEFAULT NULL COMMENT '研究类型（学术研究、产业研究、毕业论文等）',
  `institution` VARCHAR(100) DEFAULT NULL COMMENT '研究机构',
  `supervisor` VARCHAR(50) DEFAULT NULL COMMENT '指导老师/导师',
  `start_date` DATE DEFAULT NULL COMMENT '开始时间',
  `end_date` DATE DEFAULT NULL COMMENT '结束时间',
  `is_current` TINYINT DEFAULT 0 COMMENT '是否进行中（0:否,1:是）',
  `research_field` VARCHAR(100) DEFAULT NULL COMMENT '研究领域',
  `keywords` VARCHAR(200) DEFAULT NULL COMMENT '关键词（逗号分隔）',
  `description` TEXT DEFAULT NULL COMMENT '研究描述',
  `achievements` TEXT DEFAULT NULL COMMENT '研究成果',
  `publications` TEXT DEFAULT NULL COMMENT '发表论文',
  `research_url` VARCHAR(500) DEFAULT NULL COMMENT '研究链接',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历研究经历表';

-- 获奖荣誉表
CREATE TABLE `resume_awards` (
  `id` BIGINT NOT NULL COMMENT '奖项ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `award_name` VARCHAR(100) NOT NULL COMMENT '奖项名称',
  `award_level` VARCHAR(50) DEFAULT NULL COMMENT '奖项级别（国家级、省级、市级等）',
  `issuing_organization` VARCHAR(100) DEFAULT NULL COMMENT '颁发机构',
  `award_date` DATE DEFAULT NULL COMMENT '获奖时间',
  `description` TEXT DEFAULT NULL COMMENT '获奖描述',
  `certificate_url` VARCHAR(500) DEFAULT NULL COMMENT '证书图片URL',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历获奖荣誉表';

-- 证书资质表
CREATE TABLE `resume_certificates` (
  `id` BIGINT NOT NULL COMMENT '证书ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `certificate_name` VARCHAR(100) NOT NULL COMMENT '证书名称',
  `issuing_organization` VARCHAR(100) DEFAULT NULL COMMENT '颁发机构',
  `certificate_number` VARCHAR(100) DEFAULT NULL COMMENT '证书编号',
  `issue_date` DATE DEFAULT NULL COMMENT '颁发时间',
  `expiry_date` DATE DEFAULT NULL COMMENT '到期时间',
  `is_permanent` TINYINT DEFAULT 0 COMMENT '是否永久有效（0:否,1:是）',
  `certificate_url` VARCHAR(500) DEFAULT NULL COMMENT '证书图片URL',
  `description` TEXT DEFAULT NULL COMMENT '证书描述',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历证书资质表';

-- 语言能力表
CREATE TABLE `resume_languages` (
  `id` BIGINT NOT NULL COMMENT '语言能力ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `language_name` VARCHAR(50) NOT NULL COMMENT '语言名称',
  `proficiency_level` TINYINT DEFAULT 1 COMMENT '熟练程度（1:入门,2:初级,3:中级,4:高级,5:母语）',
  `certificate_type` VARCHAR(50) DEFAULT NULL COMMENT '证书类型（如：CET-4、CET-6、TOEFL、IELTS等）',
  `certificate_score` VARCHAR(20) DEFAULT NULL COMMENT '证书成绩',
  `certificate_date` DATE DEFAULT NULL COMMENT '证书获得时间',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历语言能力表';

-- 兴趣爱好表
CREATE TABLE `resume_hobbies` (
  `id` BIGINT NOT NULL COMMENT '兴趣爱好ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `hobby_name` VARCHAR(100) NOT NULL COMMENT '兴趣爱好名称',
  `hobby_category` VARCHAR(50) DEFAULT NULL COMMENT '兴趣分类（运动、音乐、阅读、旅行等）',
  `proficiency_level` TINYINT DEFAULT 1 COMMENT '熟练程度（1:入门,2:业余,3:熟练,4:专业）',
  `years_of_experience` TINYINT DEFAULT NULL COMMENT '从事年限',
  `description` TEXT DEFAULT NULL COMMENT '详细描述',
  `achievements` TEXT DEFAULT NULL COMMENT '相关成就',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_hobby_category` (`hobby_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历兴趣爱好表';

-- 自我评价表
CREATE TABLE `resume_self_evaluations` (
  `id` BIGINT NOT NULL COMMENT '自我评价ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `content` TEXT NOT NULL COMMENT '自我评价内容',
  `word_count` INT DEFAULT 0 COMMENT '字数统计',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历自我评价表';

-- 自荐信表
CREATE TABLE `resume_cover_letters` (
  `id` BIGINT NOT NULL COMMENT '自荐信ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `title` VARCHAR(100) DEFAULT NULL COMMENT '自荐信标题',
  `content` TEXT NOT NULL COMMENT '自荐信内容',
  `target_company` VARCHAR(100) DEFAULT NULL COMMENT '目标公司',
  `target_position` VARCHAR(100) DEFAULT NULL COMMENT '目标职位',
  `word_count` INT DEFAULT 0 COMMENT '字数统计',
  `template_type` VARCHAR(50) DEFAULT 'default' COMMENT '模板类型',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历自荐信表';

-- ================================
-- 用户行为相关表
-- ================================

-- 用户收藏表
CREATE TABLE `user_favorites` (
  `id` BIGINT NOT NULL COMMENT '收藏ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `target_type` TINYINT NOT NULL COMMENT '收藏类型（1:模板,2:简历）',
  `target_id` BIGINT NOT NULL COMMENT '目标ID（模板ID或简历ID）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`),
  KEY `idx_target` (`target_type`, `target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';

-- 分享记录表
CREATE TABLE `share_records` (
  `id` BIGINT NOT NULL COMMENT '分享记录ID',
  `user_id` BIGINT NOT NULL COMMENT '分享用户ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `share_type` TINYINT NOT NULL COMMENT '分享类型（1:链接分享,2:二维码分享,3:邮件分享）',
  `share_platform` VARCHAR(20) DEFAULT NULL COMMENT '分享平台（wechat,qq,weibo,email等）',
  `share_code` VARCHAR(64) NOT NULL COMMENT '分享码（用于生成分享链接）',
  `share_url` VARCHAR(500) DEFAULT NULL COMMENT '分享链接',
  `view_count` INT DEFAULT 0 COMMENT '查看次数',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `expire_time` DATETIME DEFAULT NULL COMMENT '过期时间',
  `is_active` TINYINT DEFAULT 1 COMMENT '是否有效（0:失效,1:有效）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_code` (`share_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享记录表';

-- 简历导出记录表
CREATE TABLE `resume_export_records` (
  `id` BIGINT NOT NULL COMMENT '导出记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `export_format` VARCHAR(20) NOT NULL COMMENT '导出格式（pdf,word,image,html）',
  `export_quality` VARCHAR(20) DEFAULT 'standard' COMMENT '导出质量（standard,high,print）',
  `file_size` BIGINT DEFAULT NULL COMMENT '文件大小（字节）',
  `file_url` VARCHAR(500) DEFAULT NULL COMMENT '文件URL',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `export_platform` VARCHAR(20) DEFAULT NULL COMMENT '导出平台',
  `export_ip` VARCHAR(50) DEFAULT NULL COMMENT '导出IP',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历导出记录表';

-- ================================
-- 数据统计分析表
-- ================================

-- 用户行为日志表
CREATE TABLE `user_behavior_logs` (
  `id` BIGINT NOT NULL COMMENT '日志ID',
  `user_id` BIGINT DEFAULT NULL COMMENT '用户ID（可为空，支持匿名用户）',
  `session_id` VARCHAR(64) DEFAULT NULL COMMENT '会话ID',
  `action_type` VARCHAR(50) NOT NULL COMMENT '行为类型（login,logout,view_template,edit_resume,export_resume等）',
  `target_type` VARCHAR(20) DEFAULT NULL COMMENT '目标类型（template,resume,user等）',
  `target_id` BIGINT DEFAULT NULL COMMENT '目标ID',
  `platform` VARCHAR(20) DEFAULT NULL COMMENT '操作平台',
  `device_info` JSON DEFAULT NULL COMMENT '设备信息',
  `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
  `referer` VARCHAR(500) DEFAULT NULL COMMENT '来源页面',
  `extra_data` JSON DEFAULT NULL COMMENT '额外数据',
  `duration` INT DEFAULT NULL COMMENT '操作耗时（毫秒）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_platform` (`platform`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为日志表';

-- 简历访问统计

---

_**User**_

你怎么停下了？

---

_**Assistant**_



---

抱歉，回复被截断了！我继续完成剩余的表结构：

```sql
-- 简历访问统计表
CREATE TABLE `resume_visit_stats` (
  `id` BIGINT NOT NULL COMMENT '统计ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `visitor_id` BIGINT DEFAULT NULL COMMENT '访问者ID（可为空）',
  `visitor_ip` VARCHAR(50) DEFAULT NULL COMMENT '访问者IP',
  `visit_source` VARCHAR(50) DEFAULT NULL COMMENT '访问来源（direct,share,search等）',
  `visit_platform` VARCHAR(20) DEFAULT NULL COMMENT '访问平台',
  `visit_duration` INT DEFAULT NULL COMMENT '访问时长（秒）',
  `page_views` INT DEFAULT 1 COMMENT '页面浏览数',
  `is_downloaded` TINYINT DEFAULT 0 COMMENT '是否下载（0:否,1:是）',
  `device_info` JSON DEFAULT NULL COMMENT '设备信息',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历访问统计表';

-- ================================
-- 支付订单相关表
-- ================================

-- 订单表
CREATE TABLE `orders` (
  `id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `order_type` TINYINT NOT NULL COMMENT '订单类型（1:会员充值,2:模板购买,3:增值服务）',
  `product_type` VARCHAR(50) DEFAULT NULL COMMENT '产品类型',
  `product_id` BIGINT DEFAULT NULL COMMENT '产品ID',
  `product_name` VARCHAR(100) DEFAULT NULL COMMENT '产品名称',
  `original_amount` DECIMAL(10,2) NOT NULL COMMENT '原价',
  `discount_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
  `final_amount` DECIMAL(10,2) NOT NULL COMMENT '实付金额',
  `currency` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
  `payment_method` TINYINT DEFAULT NULL COMMENT '支付方式（1:微信,2:支付宝,3:Apple Pay等）',
  `payment_platform` VARCHAR(20) DEFAULT NULL COMMENT '支付平台',
  `trade_no` VARCHAR(100) DEFAULT NULL COMMENT '第三方交易号',
  `order_status` TINYINT DEFAULT 1 COMMENT '订单状态（1:待支付,2:已支付,3:已完成,4:已取消,5:已退款）',
  `pay_time` DATETIME DEFAULT NULL COMMENT '支付时间',
  `complete_time` DATETIME DEFAULT NULL COMMENT '完成时间',
  `cancel_time` DATETIME DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` VARCHAR(200) DEFAULT NULL COMMENT '取消原因',
  `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 退款记录表
CREATE TABLE `refund_records` (
  `id` BIGINT NOT NULL COMMENT '退款记录ID',
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `refund_no` VARCHAR(64) NOT NULL COMMENT '退款单号',
  `refund_amount` DECIMAL(10,2) NOT NULL COMMENT '退款金额',
  `refund_reason` VARCHAR(200) DEFAULT NULL COMMENT '退款原因',
  `refund_type` TINYINT DEFAULT 1 COMMENT '退款类型（1:用户申请,2:系统自动,3:客服处理）',
  `refund_status` TINYINT DEFAULT 1 COMMENT '退款状态（1:申请中,2:处理中,3:已退款,4:已拒绝）',
  `third_party_refund_no` VARCHAR(100) DEFAULT NULL COMMENT '第三方退款单号',
  `process_time` DATETIME DEFAULT NULL COMMENT '处理时间',
  `complete_time` DATETIME DEFAULT NULL COMMENT '完成时间',
  `processor_id` BIGINT DEFAULT NULL COMMENT '处理人ID',
  `process_remark` VARCHAR(500) DEFAULT NULL COMMENT '处理备注',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_refund_status` (`refund_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- ================================
-- 系统配置相关表
-- ================================

-- 系统配置表
CREATE TABLE `system_configs` (
  `id` BIGINT NOT NULL COMMENT '配置ID',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT DEFAULT NULL COMMENT '配置值',
  `config_type` VARCHAR(20) DEFAULT 'string' COMMENT '配置类型（string,number,boolean,json）',
  `config_group` VARCHAR(50) DEFAULT NULL COMMENT '配置分组',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '配置描述',
  `is_encrypted` TINYINT DEFAULT 0 COMMENT '是否加密（0:否,1:是）',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公開（0:否,1:是）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 文件上传记录表
CREATE TABLE `file_upload_records` (
  `id` BIGINT NOT NULL COMMENT '文件ID',
  `user_id` BIGINT DEFAULT NULL COMMENT '上传用户ID',
  `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
  `original_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
  `file_type` VARCHAR(100) NOT NULL COMMENT '文件类型（MIME类型）',
  `file_extension` VARCHAR(20) NOT NULL COMMENT '文件扩展名',
  `upload_platform` VARCHAR(20) DEFAULT NULL COMMENT '上传平台',
  `storage_type` VARCHAR(20) DEFAULT 'local' COMMENT '存储类型（local,oss,cos,qiniu等）',
  `use_count` INT DEFAULT 0 COMMENT '使用次数',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公开（0:私有,1:公开）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传记录表';

-- ================================
-- 初始化数据
-- ================================

-- 插入默认模块配置
INSERT INTO `default_module_configs` (`id`, `module_type`, `module_name`, `default_visible`, `default_sort_order`, `is_required`, `description`) VALUES
(1, 'basic_info', '个人基本信息', 1, 1, 1, '包含姓名、联系方式、头像等基础信息'),
(2, 'education', '教育经历', 1, 2, 0, '学校、专业、学历等教育背景'),
(3, 'skills', '职业技能', 1, 3, 0, '专业技能、熟练程度等'),
(4, 'work_experience', '工作经历', 1, 4, 0, '工作经验、职位、公司等'),
(5, 'project_experience', '项目经历', 1, 5, 0, '参与的项目经验'),
(6, 'research_experience', '研究经历', 0, 6, 0, '学术研究、论文发表等'),
(7, 'awards', '获奖荣誉', 0, 7, 0, '获得的奖项和荣誉'),
(8, 'certificates', '证书资质', 0, 8, 0, '专业证书、资格认证等'),
(9, 'languages', '语言能力', 0, 9, 0, '掌握的语言及熟练程度'),
(10, 'hobbies', '兴趣爱好', 0, 10, 0, '个人兴趣爱好'),
(11, 'self_evaluation', '自我评价', 1, 11, 0, '个人能力和特点的自我描述'),
(12, 'cover_letter', '自荐信', 0, 12, 0, '求职自荐信');
```

## 数据库表汇总（共28张表）

### 📊 表分类统计：
- **用户管理相关**：3张表（users, user_third_party_auths, platform_configs）
- **会员体系相关**：3张表（membership_packages, user_memberships, membership_usage_logs）
- **简历模板相关**：2张表（template_categories, resume_templates）
- **简历数据相关**：13张表（resumes, default_module_configs, resume_module_configs, resume_basic_info, resume_educations, resume_skills, resume_work_experiences, resume_projects, resume_research_experiences, resume_awards, resume_certificates, resume_languages, resume_hobbies, resume_self_evaluations, resume_cover_letters）
- **用户行为相关**：3张表（user_favorites, share_records, resume_export_records）
- **数据统计相关**：2张表（user_behavior_logs, resume_visit_stats）
- **支付订单相关**：2张表（orders, refund_records）
- **系统配置相关**：2张表（system_configs, file_upload_records）

### 🔑 设计特点：
- ✅ 所有主键使用BIGINT支持雪花算法
- ✅ 所有实体表都有逻辑删除字段
- ✅ 表名使用下划线分隔
- ✅ 每个字段都有详细中文注释
- ✅ 支持多平台小程序登录
- ✅ 完整的会员体系管理
- ✅ 灵活的简历模块化配置
- ✅ 完善的数据统计和分析功能

这套数据库设计能够全面支持你的简历应用的各项功能需求！

---

_**User**_

不再有用户注册，用户登录自动注册，web端和APP的用户登录包括手机号，微信登录。其他小程序的登录使用各个平台的登录，这个表设计是否满足？

---

_**Assistant**_



---

根据你提到的登录需求，让我分析一下当前的表设计是否满足要求：

## 📋 需求分析

**登录方式：**
- Web端/APP：手机号登录、微信登录
- 小程序：各平台原生登录（微信、抖音、百度、支付宝等）
- 用户登录时自动注册

## ✅ 当前设计的支持情况

**当前表设计基本满足需求，具体分析：**

### 1. 手机号登录 ✅
- `users`表的`phone`字段支持手机号登录
- 可以通过手机号+验证码的方式实现登录自动注册

### 2. 微信登录 ✅  
- `user_third_party_auths`表支持存储微信登录信息
- `platform`字段可以区分`wechat_app`（微信APP登录）

### 3. 各平台小程序登录 ✅
- `user_third_party_auths`表的`platform`字段支持：
  - `wechat_mp`（微信小程序）
  - `douyin_mp`（抖音小程序）  
  - `baidu_mp`（百度小程序）
  - `alipay_mp`（支付宝小程序）
- `platform_configs`表可以配置各平台的AppID、AppSecret等

### 4. 登录自动注册 ✅
- `register_type`和`register_platform`字段可以记录注册方式
- 支持在登录流程中自动创建用户记录

## 🔧 建议的小幅调整

不过，基于你的新需求，我建议做一些小调整来优化表结构：

```sql
-- 调整用户表，移除不必要的字段
ALTER TABLE `users` 
MODIFY COLUMN `username` VARCHAR(50) DEFAULT NULL COMMENT '用户名（系统生成或用户设置）',
MODIFY COLUMN `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱（可选绑定）',
MODIFY COLUMN `password` VARCHAR(255) DEFAULT NULL COMMENT '密码（手机号登录时可选）';

-- 为用户表添加一些自动注册相关的字段
ALTER TABLE `users`
ADD COLUMN `auto_username` VARCHAR(50) DEFAULT NULL COMMENT '自动生成的用户名' AFTER `username`,
ADD COLUMN `is_phone_verified` TINYINT DEFAULT 0 COMMENT '手机号是否已验证（0:否,1:是）' AFTER `phone`,
ADD COLUMN `first_login_time` DATETIME DEFAULT NULL COMMENT '首次登录时间' AFTER `last_login_platform`;
```

## 📱 登录流程设计建议

**手机号登录流程：**
1. 用户输入手机号 → 发送验证码
2. 验证码验证通过 → 查询`users`表是否存在该手机号
3. 不存在 → 自动创建用户记录（`register_type=2`, `register_platform='web'`或`'app'`）
4. 存在 → 直接登录，更新`last_login_time`等字段

**第三方登录流程：**
1. 获取第三方平台授权 → 获取openid/unionid
2. 查询`user_third_party_auths`表是否存在该平台+openid的记录
3. 不存在 → 自动创建用户记录和授权记录
4. 存在 → 直接登录，更新相关时间字段

## 💡 实际登录场景示例

**场景1：微信小程序首次登录**
```sql
-- 1. 创建用户记录
INSERT INTO users (id, nickname, avatar_url, register_type, register_platform, first_login_time, last_login_time) 
VALUES (雪花ID, '微信用户xxx', '头像URL', 3, 'wechat_mp', NOW(), NOW());

-- 2. 创建第三方授权记录  
INSERT INTO user_third_party_auths (id, user_id, platform, openid, platform_nickname, bind_time)
VALUES (雪花ID, 用户ID, 'wechat_mp', 'openid', '微信昵称', NOW());
```

**场景2：手机号登录**
```sql
-- 验证码验证通过后，如果用户不存在则创建
INSERT INTO users (id, phone, register_type, register_platform, is_phone_verified, first_login_time, last_login_time)
VALUES (雪花ID, '***********', 2, 'web', 1, NOW(), NOW());
```

## ✅ 结论

**当前的表设计完全满足你的需求！** 主要优势：

1. **灵活支持多种登录方式** - 手机号、各平台第三方登录
2. **自动注册机制** - 通过`register_type`和`register_platform`字段记录
3. **多平台兼容** - 统一的第三方授权表支持所有平台
4. **扩展性强** - 新增平台只需要在`platform_configs`表添加配置

你觉得这个设计方案如何？是否还有其他细节需要调整？

---

_**User**_

如果用户既有微信登录，又有手机号登录，后面通过微信绑定手机号进行账号合并呢

---

_**Assistant**_



---

这是一个非常重要的场景！账号合并确实需要特殊的设计考虑。让我分析一下当前表设计并提出改进方案：

## 🔄 账号合并场景分析

**典型场景：**
1. 用户先通过微信登录 → 创建账号A（只有微信信息）
2. 用户后来通过手机号登录 → 创建账号B（只有手机号）
3. 用户在微信登录状态下绑定手机号 → 发现手机号已被其他账号使用
4. 系统提示账号合并 → 用户确认合并

## 📊 当前设计的不足

当前的表设计在账号合并方面有一些不足：
- 没有账号合并的记录机制
- 缺少合并前的数据备份
- 没有考虑合并时的数据优先级规则

## 🔧 改进方案

让我设计一个支持账号合并的完整方案：

---

```diff
- 
+ -- ================================
+ -- 账号合并支持的数据库表设计改进
+ -- ================================
+ 
+ -- 1. 用户表添加合并相关字段
+ ALTER TABLE `users`
+ ADD COLUMN `merged_from_user_id` BIGINT DEFAULT NULL COMMENT '合并来源用户ID（如果是合并后的账号）' AFTER `last_login_platform`,
+ ADD COLUMN `is_merged` TINYINT DEFAULT 0 COMMENT '是否已被合并（0:否,1:是，已合并到其他账号）' AFTER `merged_from_user_id`,
+ ADD COLUMN `merge_time` DATETIME DEFAULT NULL COMMENT '账号合并时间' AFTER `is_merged`,
+ ADD INDEX `idx_merged_from_user_id` (`merged_from_user_id`),
+ ADD INDEX `idx_is_merged` (`is_merged`);
+ 
+ -- 2. 账号合并记录表（新增）
+ CREATE TABLE `user_merge_records` (
+   `id` BIGINT NOT NULL COMMENT '合并记录ID',
+   `master_user_id` BIGINT NOT NULL COMMENT '主账号ID（保留的账号）',
+   `slave_user_id` BIGINT NOT NULL COMMENT '从账号ID（被合并的账号）',
+   `merge_type` TINYINT NOT NULL COMMENT '合并类型（1:微信绑定手机号,2:手机号绑定微信,3:管理员操作）',
+   `merge_trigger` VARCHAR(50) DEFAULT NULL COMMENT '合并触发场景（bind_phone,bind_wechat,admin_merge）',
+   `master_data_backup` JSON DEFAULT NULL COMMENT '主账号合并前数据备份',
+   `slave_data_backup` JSON DEFAULT NULL COMMENT '从账号合并前数据备份',
+   `data_merge_rules` JSON DEFAULT NULL COMMENT '数据合并规则记录',
+   `merge_ip` VARCHAR(50) DEFAULT NULL COMMENT '操作IP',
+   `merge_platform` VARCHAR(20) DEFAULT NULL COMMENT '操作平台',
+   `operator_id` BIGINT DEFAULT NULL COMMENT '操作员ID（如果是管理员操作）',
+   `is_reversible` TINYINT DEFAULT 1 COMMENT '是否可逆（0:不可逆,1:可逆）',
+   `reversed_time` DATETIME DEFAULT NULL COMMENT '撤销时间',
+   `reverse_reason` VARCHAR(200) DEFAULT NULL COMMENT '撤销原因',
+   `status` TINYINT DEFAULT 1 COMMENT '状态（1:合并成功,2:合并失败,3:已撤销）',
+   `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
+   `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
+   `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
+   PRIMARY KEY (`id`),
+   KEY `idx_master_user_id` (`master_user_id`),
+   KEY `idx_slave_user_id` (`slave_user_id`),
+   KEY `idx_merge_type` (`merge_type`),
+   KEY `idx_create_time` (`create_time`)
+ ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账号合并记录表';
+ 
+ -- 3. 账号绑定请求表（新增）
+ CREATE TABLE `account_bind_requests` (
+   `id` BIGINT NOT NULL COMMENT '绑定请求ID',
+   `user_id` BIGINT NOT NULL COMMENT '发起绑定的用户ID',
+   `bind_type` TINYINT NOT NULL COMMENT '绑定类型（1:绑定手机号,2:绑定微信,3:绑定其他平台）',
+   `bind_target` VARCHAR(100) NOT NULL COMMENT '绑定目标（手机号或平台标识）',
+   `conflict_user_id` BIGINT DEFAULT NULL COMMENT '冲突用户ID（如果绑定目标已被其他用户使用）',
+   `verification_code` VARCHAR(20) DEFAULT NULL COMMENT '验证码',
+   `code_expire_time` DATETIME DEFAULT NULL COMMENT '验证码过期时间',
+   `request_status` TINYINT DEFAULT 1 COMMENT '请求状态（1:待验证,2:待确认合并,3:已完成,4:已拒绝,5:已过期）',
+   `merge_confirmed` TINYINT DEFAULT 0 COMMENT '是否确认合并（0:未确认,1:已确认）',
+   `confirm_time` DATETIME DEFAULT NULL COMMENT '确认时间',
+   `request_ip` VARCHAR(50) DEFAULT NULL COMMENT '请求IP',
+   `request_platform` VARCHAR(20) DEFAULT NULL COMMENT '请求平台',
+   `expire_time` DATETIME DEFAULT NULL COMMENT '请求过期时间',
+   `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
+   `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
+   `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
+   PRIMARY KEY (`id`),
+   KEY `idx_user_id` (`user_id`),
+   KEY `idx_conflict_user_id` (`conflict_user_id`),
+   KEY `idx_bind_target` (`bind_target`),
+   KEY `idx_request_status` (`request_status`),
+   KEY `idx_expire_time` (`expire_time`)
+ ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账号绑定请求表';
+ 
+ -- 4. 用户第三方授权表添加合并相关字段
+ ALTER TABLE `user_third_party_auths`
+ ADD COLUMN `original_user_id` BIGINT DEFAULT NULL COMMENT '原始用户ID（合并前的用户ID）' AFTER `user_id`,
+ ADD COLUMN `merge_time` DATETIME DEFAULT NULL COMMENT '账号合并时间' AFTER `original_user_id`,
+ ADD INDEX `idx_original_user_id` (`original_user_id`);
+ 
+ -- 5. 为其他业务表添加合并处理（示例：简历表）
+ ALTER TABLE `resumes`
+ ADD COLUMN `original_user_id` BIGINT DEFAULT NULL COMMENT '原始用户ID（合并前的用户ID）' AFTER `user_id`,
+ ADD COLUMN `merge_time` DATETIME DEFAULT NULL COMMENT '账号合并时间' AFTER `original_user_id`,
+ ADD INDEX `idx_original_user_id` (`original_user_id`);
+ 
+ -- 6. 为其他业务表添加合并处理（示例：用户会员表）
+ ALTER TABLE `user_memberships`  
+ ADD COLUMN `original_user_id` BIGINT DEFAULT NULL COMMENT '原始用户ID（合并前的用户ID）' AFTER `user_id`,
+ ADD COLUMN `merge_time` DATETIME DEFAULT NULL COMMENT '账号合并时间' AFTER `original_user_id`,
+ ADD INDEX `idx_original_user_id` (`original_user_id`);
+ 
+ -- 7. 账号合并配置表（新增）
+ CREATE TABLE `merge_config_rules` (
+   `id` BIGINT NOT NULL COMMENT '规则ID',
+   `rule_name` VARCHAR(100) NOT NULL COMMENT '规则名称',
+   `rule_type` VARCHAR(50) NOT NULL COMMENT '规则类型（user_info,resume_data,membership,orders等）',
+   `merge_strategy` VARCHAR(50) NOT NULL COMMENT '合并策略（keep_master,keep_slave,merge_both,keep_latest,keep_richer）',
+   `priority` INT DEFAULT 0 COMMENT '优先级',
+   `is_active` TINYINT DEFAULT 1 COMMENT '是否启用（0:禁用,1:启用）',
+   `description` TEXT DEFAULT NULL COMMENT '规则描述',
+   `config_data` JSON DEFAULT NULL COMMENT '规则配置数据',
+   `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
+   `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
+   `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
+   PRIMARY KEY (`id`),
+   KEY `idx_rule_type` (`rule_type`),
+   KEY `idx_priority` (`priority`)
+ ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账号合并配置规则表';
+ 
+ -- ================================
+ -- 初始化合并规则配置
+ -- ================================
+ 
+ INSERT INTO `merge_config_rules` (`id`, `rule_name`, `rule_type`, `merge_strategy`, `priority`, `description`) VALUES
+ (1, '用户基本信息合并', 'user_info', 'keep_richer', 100, '保留信息更完整的账号的基本信息，空字段用另一账号补充'),
+ (2, '简历数据合并', 'resume_data', 'merge_both', 90, '合并两个账号的所有简历，重名简历自动重命名'),
+ (3, '会员权益合并', 'membership', 'keep_latest', 80, '保留有效期更长的会员权益'),
+ (4, '订单记录合并', 'orders', 'merge_both', 70, '合并所有订单记录到主账号'),
+ (5, '收藏记录合并', 'favorites', 'merge_both', 60, '合并收藏记录，去重'),
+ (6, '第三方授权合并', 'third_party_auth', 'merge_both', 50, '合并所有第三方授权到主账号'); 
```

---

现在让我详细解释这个账号合并的解决方案：

## 🔄 账号合并完整流程

### 1. **绑定冲突检测阶段**
```sql
-- 用户尝试绑定手机号时检测冲突
SELECT u.id, u.phone, u.nickname 
FROM users u 
WHERE u.phone = '***********' AND u.is_deleted = 0;
```

### 2. **创建绑定请求**
```sql
-- 如果检测到冲突，创建绑定请求记录
INSERT INTO account_bind_requests (id, user_id, bind_type, bind_target, conflict_user_id, request_status)
VALUES (雪花ID, 当前用户ID, 1, '***********', 冲突用户ID, 2);
```

### 3. **用户确认合并**
当用户确认要合并账号时，系统执行合并流程。

### 4. **数据合并策略**

**不同合并策略的说明：**
- `keep_master`：保留主账号数据，忽略从账号
- `keep_slave`：保留从账号数据，忽略主账号  
- `merge_both`：合并两个账号的数据
- `keep_latest`：保留时间更新的数据
- `keep_richer`：保留信息更完整的数据

## 📱 实际应用场景示例

### 场景1：微信用户绑定手机号
```javascript
// 前端流程
1. 用户在微信登录状态下点击"绑定手机号"
2. 输入手机号 → 发送验证码
3. 验证码验证通过 → 后端检测到手机号已被其他账号使用
4. 系统提示："该手机号已被其他账号使用，是否合并账号？"
5. 用户确认 → 执行账号合并
```

### 场景2：手机号用户绑定微信
```javascript
// 前端流程  
1. 用户在手机号登录状态下点击"绑定微信"
2. 微信授权登录 → 后端检测到微信已被其他账号使用
3. 系统提示账号合并确认
4. 用户确认 → 执行账号合并
```

## 🛠 核心功能特点

### ✅ **完整的合并记录**
- 记录合并前后的完整数据
- 支持合并撤销（可配置）
- 详细的操作日志

### ✅ **灵活的合并策略**  
- 可配置的数据合并规则
- 不同业务数据的不同处理策略
- 支持自定义合并逻辑

### ✅ **安全可靠**
- 数据备份机制
- 事务性操作保证
- 详细的审计日志

### ✅ **用户体验友好**
- 清晰的合并提示
- 可选的合并确认流程  
- 合并结果展示

## 💡 推荐的主账号选择策略

```sql
-- 推荐的主账号选择逻辑（按优先级）
1. 有付费会员的账号优先作为主账号
2. 简历数量多的账号优先
3. 注册时间早的账号优先  
4. 用户活跃度高的账号优先
```

这个设计方案完全解决了你提到的账号合并问题，既保证了数据完整性，又提供了良好的用户体验。你觉得这个方案如何？还有什么需要调整的地方吗？

---

_**User**_

  `master_data_backup` JSON DEFAULT NULL COMMENT '主账号合并前数据备份',
  `slave_data_backup` JSON DEFAULT NULL COMMENT '从账号合并前数据备份',
  `data_merge_rules` JSON DEFAULT NULL COMMENT '数据合并规则记录',

以上字段有什么用？会不会占用太多空间？

---

_**Assistant**_



---

你提出了一个非常好的问题！这三个JSON字段确实存在空间占用和实用性的问题。让我分析一下：

## 🔍 字段用途分析

### 原本设计意图：
- **`master_data_backup`**: 主账号合并前的完整数据备份，用于合并失败时恢复
- **`slave_data_backup`**: 从账号合并前的完整数据备份，用于合并撤销
- **`data_merge_rules`**: 记录本次合并使用的具体规则，用于审计和问题排查

### 实际问题：
1. **空间占用巨大** - 用户可能有大量简历、项目经历等数据
2. **使用频率极低** - 合并撤销的场景很少
3. **数据冗余** - 原表数据通过逻辑删除已经保留

## 💡 优化方案

让我提供一个更合理的设计：

```sql
-- 优化后的账号合并记录表
ALTER TABLE `user_merge_records`
DROP COLUMN `master_data_backup`,
DROP COLUMN `slave_data_backup`,
MODIFY COLUMN `data_merge_rules` VARCHAR(500) DEFAULT NULL COMMENT '数据合并规则摘要（简化版）';

-- 新增字段记录关键信息
ALTER TABLE `user_merge_records`
ADD COLUMN `merged_data_summary` JSON DEFAULT NULL COMMENT '合并数据摘要（仅关键统计信息）' AFTER `data_merge_rules`,
ADD COLUMN `backup_strategy` TINYINT DEFAULT 1 COMMENT '备份策略（1:逻辑删除保留,2:完全删除,3:归档存储）' AFTER `merged_data_summary`;
```

## 📊 更好的数据保护策略

### 1. **依赖逻辑删除机制**
```sql
-- 被合并的账号数据通过逻辑删除保留，不需要JSON备份
UPDATE users SET is_deleted = 1, is_merged = 1, merge_time = NOW() 
WHERE id = 从账号ID;

-- 关联数据也通过逻辑删除保留
UPDATE resumes SET is_deleted = 1, original_user_id = user_id, user_id = 主账号ID, merge_time = NOW()
WHERE user_id = 从账号ID;
```

### 2. **简化的合并摘要**
```json
{
  "merged_tables": ["users", "resumes", "user_memberships"],
  "data_counts": {
    "resumes": 3,
    "work_experiences": 5,
    "projects": 2
  },
  "merge_rules_applied": ["user_info:keep_richer", "resume_data:merge_both"],
  "conflicts_resolved": 1
}
```

### 3. **关键信息记录**
```sql
-- 精简版的合并记录
CREATE TABLE `user_merge_records_optimized` (
  `id` BIGINT NOT NULL COMMENT '合并记录ID',
  `master_user_id` BIGINT NOT NULL COMMENT '主账号ID',
  `slave_user_id` BIGINT NOT NULL COMMENT '从账号ID（逻辑删除，可恢复）',
  `merge_type` TINYINT NOT NULL COMMENT '合并类型（1:微信绑定手机号,2:手机号绑定微信）',
  `merge_trigger` VARCHAR(50) DEFAULT NULL COMMENT '合并触发场景',
  `merged_data_summary` JSON DEFAULT NULL COMMENT '合并数据摘要（轻量级）',
  `applied_rules` VARCHAR(500) DEFAULT NULL COMMENT '应用的合并规则列表',
  `merge_ip` VARCHAR(50) DEFAULT NULL COMMENT '操作IP',
  `merge_platform` VARCHAR(20) DEFAULT NULL COMMENT '操作平台',
  `is_reversible` TINYINT DEFAULT 1 COMMENT '是否可逆',
  `status` TINYINT DEFAULT 1 COMMENT '状态（1:成功,2:失败,3:已撤销）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_master_user_id` (`master_user_id`),
  KEY `idx_slave_user_id` (`slave_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账号合并记录表（优化版）';
```

## 🔄 合并撤销机制

如果真的需要撤销合并，可以通过以下方式：

```sql
-- 撤销合并操作
-- 1. 恢复被合并的用户账号
UPDATE users SET is_deleted = 0, is_merged = 0, merge_time = NULL 
WHERE id = 从账号ID AND is_merged = 1;

-- 2. 恢复相关业务数据
UPDATE resumes SET is_deleted = 0, user_id = original_user_id, merge_time = NULL
WHERE original_user_id = 从账号ID AND merge_time IS NOT NULL;

-- 3. 更新合并记录状态
UPDATE user_merge_records SET status = 3, reversed_time = NOW()
WHERE slave_user_id = 从账号ID;
```

## 📈 空间占用对比

**原方案（每条记录）：**
- 完整数据备份：可能10KB-100KB+
- 总存储：非常大

**优化方案（每条记录）：**
- 合并摘要：通常100-500字节
- 总存储：减少95%+

## ✅ 推荐的最终方案

1. **移除重量级JSON备份字段**
2. **依赖逻辑删除机制保护数据**
3. **记录轻量级的合并摘要**
4. **保留关键的审计信息**

这样既保证了数据安全，又大大减少了存储空间占用。你觉得这个优化方案如何？

---

_**User**_

重新给出完整的所有的表，我再看看，不要设计得太复杂

---

_**Assistant**_



---



---



---



---

