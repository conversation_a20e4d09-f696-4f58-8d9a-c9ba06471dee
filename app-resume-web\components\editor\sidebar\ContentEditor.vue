<template>
  <div class="content-editor">
    <!-- 编辑区头部 -->
    <div class="editor-header">
      <div class="module-info">
        <Icon :name="currentModule?.icon || 'edit'" size="sm" class="module-icon" />
        <h3 class="module-title">{{ currentModule?.name || '请选择模块' }}</h3>
      </div>
      

    </div>

    <!-- 编辑区内容 - 左右分栏布局 -->
    <div class="editor-content">
      <!-- 左侧：编写建议 -->
      <div v-if="showHelpTips && currentModule" :class="['help-panel', { 'help-panel-collapsed': helpPanelCollapsed }]">
        <!-- 展开状态 -->
        <div v-if="!helpPanelCollapsed" class="help-tips">
          <div class="tips-header">
            <span class="tips-title">编写建议</span>
            <button class="collapse-btn" @click="toggleHelpPanel" title="折叠编写建议">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="15,18 9,12 15,6"></polyline>
              </svg>
            </button>
          </div>
          <div class="tips-content">
            <p class="tips-description">{{ currentModule.tips || '暂无编写建议' }}</p>
            <ul v-if="currentModule.examples && currentModule.examples.length" class="tips-examples">
              <li v-for="(example, index) in currentModule.examples" :key="index">
                {{ example }}
              </li>
            </ul>
          </div>
        </div>
        
        <!-- 折叠状态 -->
        <div v-else class="help-panel-toggle">
          <button class="expand-btn" @click="toggleHelpPanel" title="展开编写建议">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
            <span class="expand-text">编写建议</span>
          </button>
        </div>
      </div>

      <!-- 右侧：表单内容区域 -->
      <div :class="['form-container', { 'form-container-full': !showHelpTips || !currentModule || helpPanelCollapsed }]">
        <!-- 基本信息模块 -->
        <BasicInfoForm 
          v-if="currentModuleId === 'basic_info'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 教育经历模块 -->
        <EducationForm 
          v-else-if="currentModuleId === 'education'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 工作经历模块 -->
        <WorkExperienceForm 
          v-else-if="currentModuleId === 'work_experience'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 项目经验模块 -->
        <ProjectForm 
          v-else-if="currentModuleId === 'project'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 技能模块 -->
        <SkillsForm 
          v-else-if="currentModuleId === 'skills'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 语言能力模块 -->
        <LanguageForm 
          v-else-if="currentModuleId === 'language'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 获奖荣誉模块 -->
        <AwardForm 
          v-else-if="currentModuleId === 'award'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 兴趣爱好模块 -->
        <HobbiesForm 
          v-else-if="currentModuleId === 'hobbies'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 自我评价模块 -->
        <SelfEvaluationForm 
          v-else-if="currentModuleId === 'self_evaluation'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 实习经历模块 -->
        <InternshipForm 
          v-else-if="currentModuleId === 'internship'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 培训经历模块 -->
        <TrainingForm 
          v-else-if="currentModuleId === 'training'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 研究经历模块 -->
        <ResearchExperienceForm 
          v-else-if="currentModuleId === 'research_experience'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 论文专利模块 -->
        <PublicationForm 
          v-else-if="currentModuleId === 'publication'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 证书资质模块 -->
        <CertificateForm 
          v-else-if="currentModuleId === 'certificate'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 志愿服务模块 -->
        <VolunteerExperienceForm 
          v-else-if="currentModuleId === 'volunteer_experience'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 作品集模块 -->
        <PortfolioForm 
          v-else-if="currentModuleId === 'portfolio'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 自荐信模块 -->
        <CoverLetterForm 
          v-else-if="currentModuleId === 'cover_letter'"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 自定义模块 -->
        <CustomForm 
          v-else-if="currentModuleId.startsWith('custom_')"
          :data="moduleData"
          @update="handleDataUpdate"
        />
        
        <!-- 调试信息 -->
        <div v-else-if="currentModuleId && currentModuleId.includes('custom')" class="debug-info">
          <h4>调试信息</h4>
          <p>当前模块ID: {{ currentModuleId }}</p>
          <p>是否以custom_开头: {{ currentModuleId.startsWith('custom_') }}</p>
          <p>当前模块配置: {{ JSON.stringify(currentModule, null, 2) }}</p>
          <p>模块数据: {{ JSON.stringify(moduleData, null, 2) }}</p>
          <p>所有自定义模块: {{ JSON.stringify(editorStore.customModules?.value || [], null, 2) }}</p>
        </div>
        
        <!-- 其他文本类模块 -->
        <TextForm 
          v-else-if="currentModule && currentModule.type === 'text'"
          :data="moduleData"
          :config="currentModule"
          @update="handleDataUpdate"
        />
        
        <!-- 未选择模块状态 -->
        <div v-else class="empty-state">
          <Icon name="edit" size="xl" class="empty-icon" />
          <h4 class="empty-title">选择一个模块开始编辑</h4>
          <p class="empty-desc">在左侧选择要编辑的简历模块，在这里填写详细信息</p>
        </div>
      </div>
    </div>
    
    <!-- 模块导航按钮 - 固定在底部右侧 -->
    <div v-if="currentModule" class="module-navigation-footer">
      <div class="nav-spacer"></div>
      <div class="nav-buttons-group">
        <button 
          v-if="!isFirstModule"
          class="nav-btn nav-btn-prev"
          @click="goToPreviousModule"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="15,18 9,12 15,6"></polyline>
          </svg>
          <span>上一步</span>
        </button>
        
        <button 
          v-if="!isLastModule"
          class="nav-btn nav-btn-next"
          @click="goToNextModule"
        >
          <span>下一步</span>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * 内容编辑器组件
 * @description 根据当前选中的模块显示相应的编辑表单
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, watch, nextTick } from 'vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'
import Icon from '~/components/common/Icon.vue'

// 表单组件
import BasicInfoForm from './forms/BasicInfoForm.vue'
import EducationForm from './forms/EducationForm.vue'
import WorkExperienceForm from './forms/WorkExperienceForm.vue'
import ProjectForm from './forms/ProjectForm.vue'
import SkillsForm from './forms/SkillsForm.vue'
import LanguageForm from './forms/LanguageForm.vue'
import AwardForm from './forms/AwardForm.vue'
import HobbiesForm from './forms/HobbiesForm.vue'
import SelfEvaluationForm from './forms/SelfEvaluationForm.vue'
import InternshipForm from './forms/InternshipForm.vue'
import TrainingForm from './forms/TrainingForm.vue'
import ResearchExperienceForm from './forms/ResearchExperienceForm.vue'
import PublicationForm from './forms/PublicationForm.vue'
import CertificateForm from './forms/CertificateForm.vue'
import VolunteerExperienceForm from './forms/VolunteerExperienceForm.vue'
import PortfolioForm from './forms/PortfolioForm.vue'
import CoverLetterForm from './forms/CoverLetterForm.vue'
import CustomForm from './forms/CustomForm.vue'
import TextForm from './forms/TextForm.vue'

// ================================
// 响应式数据
// ================================
const showHelpTips = ref(true)
const helpPanelCollapsed = ref(false) // 编写建议面板折叠状态，默认展开

// ================================
// 状态管理
// ================================
const editorStore = useEditorStore()

// ================================
// 计算属性
// ================================

/**
 * 当前选中的模块ID
 */
const currentModuleId = computed(() => {
  const moduleId = editorStore.currentModuleId.value
  console.log('ContentEditor - 当前模块ID:', moduleId)
  return moduleId
})

/**
 * 当前模块配置
 */
const currentModule = computed(() => {
  const configs = editorStore.moduleConfigs.value
  if (!Array.isArray(configs)) {
    console.warn('ContentEditor - 模块配置不是数组:', configs)
    return null
  }
  
  // 先在普通模块中查找
  let module = configs.find(module => module.id === currentModuleId.value)
  
  // 如果没找到，在自定义模块中查找
  if (!module) {
    const customModules = editorStore.customModules?.value || []
    module = customModules.find(module => module.id === currentModuleId.value)
  }
  
  console.log('ContentEditor - 找到模块配置:', module?.name || '未找到')
  return module
})

/**
 * 当前模块数据
 */
const moduleData = computed(() => {
  const rawData = editorStore.getModuleData(currentModuleId.value)
  const defaultData = getDefaultModuleData()
  
  // 如果没有原始数据，使用默认数据
  if (!rawData || (typeof rawData === 'object' && Object.keys(rawData).length === 0)) {
    console.log('ContentEditor - 使用默认数据:', currentModuleId.value, defaultData)
    return defaultData
  }
  
  // 如果有原始数据，合并默认数据（确保字段完整）
  const mergedData = typeof defaultData === 'object' && defaultData !== null 
    ? { ...defaultData, ...rawData }
    : rawData
  
  console.log('ContentEditor - 合并后数据:', currentModuleId.value, mergedData)
  return mergedData
})

/**
 * 启用的模块列表（用于导航）
 */
const enabledModules = computed(() => {
  const configs = editorStore.moduleConfigs.value
  if (!Array.isArray(configs)) return []
  
  // 获取已启用的普通模块
  const enabledNormalModules = configs.filter(module => module && module.enabled)
  
  // 获取已启用的自定义模块
  const customModules = editorStore.customModules?.value || []
  const enabledCustomModules = customModules.filter(module => module && module.enabled)
  
  // 合并所有已启用的模块并按order排序
  return [...enabledNormalModules, ...enabledCustomModules]
    .sort((a, b) => (a.order || 0) - (b.order || 0))
})

/**
 * 当前模块在启用模块列表中的索引
 */
const currentModuleIndex = computed(() => {
  const modules = enabledModules.value
  return modules.findIndex(module => module.id === currentModuleId.value)
})

/**
 * 是否为第一个模块
 */
const isFirstModule = computed(() => {
  return currentModuleIndex.value === 0
})

/**
 * 是否为最后一个模块
 */
const isLastModule = computed(() => {
  const modules = enabledModules.value
  return currentModuleIndex.value === modules.length - 1
})

// ================================
// 监听器
// ================================

/**
 * 监听模块切换，重置帮助提示显示状态
 */
watch(currentModuleId, () => {
  // 切换模块时默认显示提示，但保持编写建议的折叠状态
  showHelpTips.value = true
  // helpPanelCollapsed.value 保持当前状态，不重置
})

// ================================
// 方法定义
// ================================

/**
 * 获取默认模块数据
 * @returns {Object} 默认数据结构
 */
const getDefaultModuleData = () => {
  if (!currentModule.value) return null
  
  switch (currentModule.value.type) {
    case 'basic_info':
      return {
        // 基本信息
        photo: '',
        name: '张三',
        phone: '13800138000',
        email: '<EMAIL>',
        age: '25',
        currentCity: '北京',
        address: '北京市朝阳区***路***号**小区',
        currentStatus: '已离职-随时到岗',
        intendedCity: '北京',
        expectedPosition: '前端开发工程师',
        expectedSalary: '面议',
        
        // 社交信息
        wechat: '',
        website: '',
        github: '',
        
        // 其他信息
        gender: '',
        height: '',
        weight: '',
        politicalStatus: '',
        maritalStatus: '',
        hometown: '',
        ethnicity: '',
        
        // 自定义字段数组
        customSocials: [],
        customInfos: []
      }
    
    case 'education':
    case 'work_experience':
    case 'project':
    case 'internship':
    case 'training':
    case 'research_experience':
    case 'volunteer_experience':
      return []
    
    case 'skills':
      return {
        skills: []
      }
    
    case 'language':
      return {
        languages: []
      }
    
    case 'award':
      return {
        awards: []
      }
    
    case 'hobbies':
      return {
        hobbies: []
      }
    
    case 'publication':
      return {
        publications: []
      }
    
    case 'certificate':
      return {
        certificates: []
      }
    
    case 'portfolio':
      return {
        items: []
      }
    
    case 'text':
    case 'self_evaluation':
    case 'cover_letter':
      return {
        content: ''
      }
    
    case 'custom':
      // 如果是动态创建的自定义模块（custom_xxx），使用items数组
      if (currentModule.value && currentModule.value.id.startsWith('custom_')) {
        return {
          items: []
        }
      }
      // 原来的固定custom模块，使用content
      return {
        content: ''
      }
    
    default:
      // 对于动态创建的自定义模块，也使用items数组
      if (currentModule.value && currentModule.value.id.startsWith('custom_')) {
        return {
          items: []
        }
      }
      return {}
  }
}

/**
 * 处理数据更新
 * @param {Object} updatedData - 更新的数据
 */
const handleDataUpdate = (updatedData) => {
  console.log('📝 ContentEditor - handleDataUpdate 被调用:', {
    moduleId: currentModuleId.value,
    updatedData,
    timestamp: new Date().toISOString()
  })
  
  // 强制触发响应式更新
  const moduleId = currentModuleId.value
  editorStore.updateModuleData(moduleId, updatedData)
  
  // 手动触发重新渲染
  nextTick(() => {
    console.log('✅ 数据更新后的store状态:', editorStore.currentResumeData)
  })
}

/**
 * 切换提示显示状态
 */
const toggleTips = () => {
  showHelpTips.value = !showHelpTips.value
}

/**
 * 切换编写建议面板折叠状态
 */
const toggleHelpPanel = () => {
  helpPanelCollapsed.value = !helpPanelCollapsed.value
}

/**
 * 跳转到上一个模块
 */
const goToPreviousModule = () => {
  const modules = enabledModules.value
  const currentIndex = currentModuleIndex.value
  
  if (currentIndex > 0) {
    const previousModule = modules[currentIndex - 1]
    editorStore.setCurrentModule(previousModule.id)
  }
}

/**
 * 跳转到下一个模块
 */
const goToNextModule = () => {
  const modules = enabledModules.value
  const currentIndex = currentModuleIndex.value
  
  if (currentIndex < modules.length - 1) {
    const nextModule = modules[currentIndex + 1]
    editorStore.setCurrentModule(nextModule.id)
  }
}
</script>

<style scoped>
.content-editor {
  @apply flex flex-col flex-1 bg-white;
  min-height: 0; /* 允许flex子项收缩 */
}

/* 编辑区头部 */
.editor-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.module-info {
  @apply flex items-center space-x-3;
}

.module-icon {
  @apply text-primary-600;
}

.module-title {
  @apply text-lg font-medium text-gray-900;
}

.module-actions {
  @apply flex items-center space-x-2;
}

.action-btn {
  @apply p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200;
}

/* 编辑区内容 - 左右分栏 */
.editor-content {
  @apply flex-1 flex overflow-hidden;
}

/* 左侧帮助面板 */
.help-panel {
  @apply flex-shrink-0 border-r border-gray-200 overflow-hidden transition-all duration-300 ease-in-out;
  width: 30%; /* 展开时占30%宽度 */
}

.help-panel-collapsed {
  width: 48px; /* 折叠时的宽度，只显示按钮 */
}

/* 帮助提示 */
.help-tips {
  @apply bg-blue-50 h-full overflow-y-auto;
  padding: 16px 16px 80px 16px; /* 顶部16px，左右16px，底部80px，为底部导航留出空间 */
}

.tips-header {
  @apply flex items-center justify-between mb-3;
}

.collapse-btn {
  @apply p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-100 rounded transition-all duration-200;
}

.tips-title {
  @apply text-sm font-medium text-blue-900;
}

.tips-content {
  @apply space-y-3;
}

/* 折叠状态下的展开按钮 */
.help-panel-toggle {
  @apply h-full flex items-start justify-center pt-4;
  width: 48px;
}

.expand-btn {
  @apply flex flex-col items-center space-y-2 p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200;
}

.expand-text {
  @apply text-xs font-medium;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.tips-description {
  @apply text-sm text-blue-800 leading-relaxed;
}

.tips-examples {
  @apply list-disc list-inside space-y-1 text-sm text-blue-700;
}

.tips-examples li {
  @apply leading-relaxed;
}

/* 表单容器 */
.form-container {
  @apply overflow-y-auto transition-all duration-300 ease-in-out;
  width: 70%; /* 默认占70%宽度 */
  padding: 24px 24px 80px 24px; /* 顶部24px，左右24px，底部80px */
}

.form-container-full {
  width: 100%; /* 当编写建议隐藏或折叠时占满宽度 */
}

/* 空状态 */
.empty-state {
  @apply text-center py-16;
}

.empty-icon {
  @apply text-gray-300 mx-auto mb-4;
}

.empty-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.empty-desc {
  @apply text-sm text-gray-600 max-w-md mx-auto leading-relaxed;
}

/* 模块底部导航
 * ================================ */
.module-navigation-footer {
  @apply flex items-center px-6 py-3 border-t border-gray-200 bg-gray-50;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.nav-buttons-group {
  @apply flex items-center gap-4;
}

.nav-btn {
  @apply flex items-center space-x-2 px-4 py-1.5 text-sm font-medium rounded-lg transition-all duration-200;
}

.nav-btn-prev {
  @apply border border-gray-300 bg-white hover:bg-gray-50 hover:border-gray-400;
  @apply text-gray-700 hover:text-gray-900;
}

.nav-btn-next {
  @apply border border-blue-300 hover:border-blue-400 bg-blue-50 hover:bg-blue-100;
  @apply text-blue-600 hover:text-blue-700;
}

.nav-spacer {
  @apply flex-1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .help-panel {
    width: 35%; /* 中等屏幕时占35%宽度 */
  }
  
  .form-container {
    width: 65%; /* 对应调整表单容器宽度 */
  }
}

@media (max-width: 768px) {
  .editor-content {
    @apply flex-col; /* 移动端改为上下布局 */
  }
  
  .help-panel {
    @apply w-full border-r-0 border-b border-gray-200; /* 移动端改为顶部 */
    height: auto;
    max-height: 200px;
  }
  
  .help-panel-collapsed {
    width: 100%;
    height: 48px;
    max-height: 48px;
  }
  
  .help-tips {
    width: 100%;
    padding: 12px 12px 60px 12px; /* 移动端减少底部间距 */
  }
  
  .help-panel-toggle {
    @apply flex-row justify-center items-center pt-0;
    width: 100%;
    height: 48px;
  }
  
  .expand-btn {
    @apply flex-row space-y-0 space-x-2;
  }
  
  .expand-text {
    writing-mode: initial;
    text-orientation: initial;
  }
  
  .editor-header {
    @apply px-4 py-3;
  }
  
  .module-title {
    @apply text-base;
  }
  
  .form-container {
    width: 100%; /* 移动端表单容器占满宽度 */
    padding: 16px 16px 60px 16px; /* 移动端也增加底部间距 */
  }
  
  .empty-state {
    @apply py-12;
  }
  
  .module-navigation-footer {
    @apply px-4 py-3;
  }
  
  .nav-btn {
    @apply px-3 py-1.5 text-xs;
  }
}

/* 滚动条样式 */
.help-panel::-webkit-scrollbar,
.form-container::-webkit-scrollbar {
  @apply w-2;
}

.help-panel::-webkit-scrollbar-track,
.form-container::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

.help-panel::-webkit-scrollbar-thumb,
.form-container::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.help-panel::-webkit-scrollbar-thumb:hover,
.form-container::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
</style> 