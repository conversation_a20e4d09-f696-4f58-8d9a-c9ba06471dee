/**
 * 用户注册服务
 * @description 处理用户注册相关的所有业务逻辑
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, computed, reactive } from 'vue'
import HttpClient, { API_CONFIG } from '~/utils/api-config'
import { $message } from '../shared/useGlobalMessage'

// ================================
// 响应式状态管理
// ================================
const loading = ref(false)

// 验证码发送相关状态
const smsCodeState = reactive({
  loading: false,
  countdown: 0,
  canSend: true,
  error: ''
})

// ================================
// 表单验证工具类
// ================================
class RegisterFormValidator {
  /**
   * 验证手机号格式
   * @param {string} phone - 手机号
   * @returns {Object} 验证结果
   */
  static validatePhone(phone) {
    if (!phone) {
      return { isValid: false, message: '请输入手机号' }
    }
    
    if (phone.length !== 11) {
      return { isValid: false, message: '手机号长度必须为11位' }
    }
    
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return { isValid: false, message: '请输入正确的手机号格式' }
    }
    
    return { isValid: true, message: '' }
  }

  /**
   * 验证密码格式
   * @param {string} password - 密码
   * @returns {Object} 验证结果
   */
  static validatePassword(password) {
    if (!password) {
      return { isValid: false, message: '请输入密码' }
    }
    
    if (password.length < 6) {
      return { isValid: false, message: '密码长度不能少于6位' }
    }
    
    if (password.length > 20) {
      return { isValid: false, message: '密码长度不能超过20位' }
    }
    
    // 检查密码强度（至少包含字母和数字）
    const hasLetter = /[a-zA-Z]/.test(password)
    const hasNumber = /\d/.test(password)
    
    if (!hasLetter || !hasNumber) {
      return { isValid: false, message: '密码必须包含字母和数字' }
    }
    
    return { isValid: true, message: '' }
  }

  /**
   * 验证确认密码
   * @param {string} password - 原密码
   * @param {string} confirmPassword - 确认密码
   * @returns {Object} 验证结果
   */
  static validateConfirmPassword(password, confirmPassword) {
    if (!confirmPassword) {
      return { isValid: false, message: '请确认密码' }
    }
    
    if (password !== confirmPassword) {
      return { isValid: false, message: '两次输入的密码不一致' }
    }
    
    return { isValid: true, message: '' }
  }

  /**
   * 验证短信验证码
   * @param {string} code - 验证码
   * @returns {Object} 验证结果
   */
  static validateSmsCode(code) {
    if (!code) {
      return { isValid: false, message: '请输入验证码' }
    }
    
    if (code.length !== 6) {
      return { isValid: false, message: '验证码长度为6位' }
    }
    
    if (!/^\d{6}$/.test(code)) {
      return { isValid: false, message: '验证码只能包含数字' }
    }
    
    return { isValid: true, message: '' }
  }

  /**
   * 验证用户协议同意状态
   * @param {boolean} agreeTerms - 是否同意协议
   * @returns {Object} 验证结果
   */
  static validateAgreement(agreeTerms) {
    if (!agreeTerms) {
      return { isValid: false, message: '请先阅读并同意用户协议和隐私政策' }
    }
    
    return { isValid: true, message: '' }
  }

  /**
   * 验证整个注册表单
   * @param {Object} formData - 表单数据
   * @returns {Object} 验证结果
   */
  static validateRegisterForm(formData) {
    const errors = {}
    let isValid = true

    // 验证手机号
    const phoneResult = this.validatePhone(formData.phone)
    if (!phoneResult.isValid) {
      errors.phone = phoneResult.message
      isValid = false
    }

    // 验证密码
    const passwordResult = this.validatePassword(formData.password)
    if (!passwordResult.isValid) {
      errors.password = passwordResult.message
      isValid = false
    }

    // 验证确认密码
    const confirmPasswordResult = this.validateConfirmPassword(
      formData.password, 
      formData.confirmPassword
    )
    if (!confirmPasswordResult.isValid) {
      errors.confirmPassword = confirmPasswordResult.message
      isValid = false
    }

    // 验证验证码
    const smsCodeResult = this.validateSmsCode(formData.verificationCode)
    if (!smsCodeResult.isValid) {
      errors.verificationCode = smsCodeResult.message
      isValid = false
    }

    // 验证协议同意
    const agreementResult = this.validateAgreement(formData.agreeTerms)
    if (!agreementResult.isValid) {
      errors.agreeTerms = agreementResult.message
      isValid = false
    }

    return { isValid, errors }
  }
}

// ================================
// 注册请求构建器
// ================================
class RegisterRequestBuilder {
  /**
   * 构建注册请求参数
   * @param {Object} formData - 表单数据
   * @returns {Object} 请求参数
   */
  static buildRegisterRequest(formData) {
    return {
      registerType: 1,                        // 手机号+密码注册
      phone: formData.phone.trim(),
      password: formData.password,
      verificationCode: formData.verificationCode.trim(),
      agreeTerms: formData.agreeTerms,
      platform: 'web'                        // 注册平台
    }
  }
}

// ================================
// 注册业务逻辑类
// ================================
class RegisterService {
  /**
   * 发送短信验证码
   * @param {string} phone - 手机号
   * @returns {Promise<Object>} 发送结果
   */
  static async sendSmsCode(phone) {
    try {
      // 验证手机号
      const phoneValidation = RegisterFormValidator.validatePhone(phone)
      if (!phoneValidation.isValid) {
        throw new Error(phoneValidation.message)
      }

      smsCodeState.loading = true
      smsCodeState.error = ''

      // 发送验证码请求
      const sendCodeUrl = `${API_CONFIG.ENDPOINTS.AUTH.SEND_CODE}?phone=${encodeURIComponent(phone.trim())}&platform=web`
      await HttpClient.post(sendCodeUrl)

      // 开始倒计时
      this.startCountdown()

      return {
        success: true,
        message: '验证码发送成功，请注意查收'
      }

    } catch (err) {
      const errorMessage = err.message || '发送验证码失败，请重试'
      smsCodeState.error = errorMessage

      return {
        success: false,
        error: errorMessage
      }
    } finally {
      smsCodeState.loading = false
    }
  }

  /**
   * 开始验证码倒计时
   */
  static startCountdown() {
    smsCodeState.countdown = 60
    smsCodeState.canSend = false

    const timer = setInterval(() => {
      smsCodeState.countdown--
      
      if (smsCodeState.countdown <= 0) {
        clearInterval(timer)
        smsCodeState.canSend = true
        smsCodeState.countdown = 0
      }
    }, 1000)
  }

  /**
   * 用户注册
   * @param {Object} formData - 注册表单数据
   * @returns {Promise<Object>} 注册结果
   */
  static async register(formData) {
    try {
      loading.value = true

      // 表单验证
      const validation = RegisterFormValidator.validateRegisterForm(formData)
      if (!validation.isValid) {
        const firstError = Object.values(validation.errors)[0]
        throw new Error(firstError)
      }

      // 构建注册请求
      const registerRequest = RegisterRequestBuilder.buildRegisterRequest(formData)

      // 发送注册请求
      const response = await HttpClient.post(
        API_CONFIG.ENDPOINTS.AUTH.REGISTER,
        registerRequest
      )

      // 注册成功后自动登录 - 处理数据格式和状态同步
      if (response.accessToken) {
        await this.handleRegistrationSuccess(response)
      }

      return {
        success: true,
        data: response,
        message: '注册成功'
      }

    } catch (err) {
      const errorMessage = err.message || '注册失败，请重试'
      
      return {
        success: false,
        error: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理注册成功后的逻辑
   * @param {Object} response - 注册响应数据
   */
  static async handleRegistrationSuccess(response) {
    if (process.client) {
      console.log('🎯 开始处理注册成功逻辑:', response)
      
      // 1. 转换数据格式以兼容登录逻辑
      const userInfo = response.userInfo ? {
        id: response.userInfo.id,
        userId: response.userInfo.id,
        username: response.userInfo.username,
        nickname: response.userInfo.nickname,
        phone: response.userInfo.phone,
        email: response.userInfo.email,
        avatar: response.userInfo.avatar,
        gender: response.userInfo.gender,
        status: response.userInfo.status,
        isPhoneVerified: response.userInfo.isPhoneVerified,
        preferredLanguage: response.userInfo.preferredLanguage,
        registerPlatform: response.userInfo.registerPlatform,
        lastLoginTime: response.userInfo.registerTime, // 注册时间作为首次登录时间
        membershipInfo: {
          isMember: response.userInfo.membershipLevel > 0,
          memberLevel: response.userInfo.membershipLevel,
          expireTime: response.userInfo.membershipExpireTime,
          remainDays: response.userInfo.membershipExpireTime ? 
            Math.max(0, Math.floor((new Date(response.userInfo.membershipExpireTime) - new Date()) / (1000 * 60 * 60 * 24))) : 0
        },
        permissions: [],
        isNewUser: true
      } : null
      
      // 2. 存储Token和用户信息（使用与登录相同的key）
      if (response.accessToken) {
        localStorage.setItem('auth_token', response.accessToken)
        console.log('✅ Token已存储')
      }
      
      if (response.refreshToken) {
        localStorage.setItem('refresh_token', response.refreshToken)
        console.log('✅ RefreshToken已存储')
      }
      
      if (userInfo) {
        localStorage.setItem('user_info', JSON.stringify(userInfo))
        console.log('✅ 用户信息已存储:', userInfo)
      }
      
      // 3. 返回处理后的用户信息，让调用方处理状态更新
      console.log('✅ 注册成功数据处理完成，返回给调用方处理状态更新')
      
      // 4. 存储完整的注册响应（用于调试）
      localStorage.setItem('register_response', JSON.stringify(response))
    }
  }

  /**
   * 清除错误状态
   */
  static clearError() {
    smsCodeState.error = ''
  }

  /**
   * 清除成功状态
   */
  static clearSuccess() {
    // 不再需要清除本地成功状态，因为使用全局消息
  }

  /**
   * 重置所有状态
   */
  static resetState() {
    loading.value = false
    smsCodeState.loading = false
    smsCodeState.error = ''
  }
}

// ================================
// Composable导出
// ================================
export const useRegisterService = () => {
  // ================================
  // 计算属性
  // ================================
  const isLoading = computed(() => loading.value)
  const smsLoading = computed(() => smsCodeState.loading)
  const canSendSms = computed(() => smsCodeState.canSend)
  const smsCountdown = computed(() => smsCodeState.countdown)

  // ================================
  // 方法
  // ================================
  
  /**
   * 用户注册
   * @param {Object} formData - 表单数据
   * @returns {Promise<Object>} 注册结果
   */
  const register = async (formData) => {
    return await RegisterService.register(formData)
  }

  /**
   * 发送短信验证码
   * @param {string} phone - 手机号
   * @returns {Promise<Object>} 发送结果
   */
  const sendSmsCode = async (phone) => {
    return await RegisterService.sendSmsCode(phone)
  }

  /**
   * 验证单个字段
   * @param {string} field - 字段名
   * @param {*} value - 字段值
   * @param {Object} formData - 完整表单数据（用于确认密码验证）
   * @returns {Object} 验证结果
   */
  const validateField = (field, value, formData = {}) => {
    switch (field) {
      case 'phone':
        return RegisterFormValidator.validatePhone(value)
      case 'password':
        return RegisterFormValidator.validatePassword(value)
      case 'confirmPassword':
        return RegisterFormValidator.validateConfirmPassword(formData.password, value)
      case 'verificationCode':
        return RegisterFormValidator.validateSmsCode(value)
      case 'agreeTerms':
        return RegisterFormValidator.validateAgreement(value)
      default:
        return { isValid: true, message: '' }
    }
  }

  /**
   * 验证整个表单
   * @param {Object} formData - 表单数据
   * @returns {Object} 验证结果
   */
  const validateForm = (formData) => {
    return RegisterFormValidator.validateRegisterForm(formData)
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    RegisterService.clearError()
  }

  /**
   * 清除成功状态
   */
  const clearSuccess = () => {
    RegisterService.clearSuccess()
  }

  /**
   * 重置所有状态
   */
  const resetState = () => {
    RegisterService.resetState()
  }

  // ================================
  // 返回对象
  // ================================
  return {
    // 状态
    loading: readonly(loading),
    smsCodeState,
    
    // 计算属性
    isLoading,
    smsLoading,
    canSendSms,
    smsCountdown,
    
    // 方法
    register,
    sendSmsCode,
    validateField,
    validateForm,
    clearError,
    clearSuccess,
    resetState
  }
}

/**
 * 只读响应式引用工具函数
 * @param {Ref} ref - 响应式引用
 * @returns {Readonly<Ref>} 只读响应式引用
 */
function readonly(refValue) {
  return computed(() => refValue)
} 