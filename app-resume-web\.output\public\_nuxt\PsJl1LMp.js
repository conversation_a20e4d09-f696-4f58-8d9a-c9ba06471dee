import{r as a,k as p,P as t}from"./CURHyiUL.js";const A=()=>{const s=a(null),l=a(!1),o=a(""),r=e=>{s.value=e},u=()=>{s.value=null},i=()=>{l.value=!l.value},c=e=>{l.value=e},d=e=>{o.value=e},m=p(()=>{var e,n;return((n=(e=s.value)==null?void 0:e.roles)==null?void 0:n.includes("SUPER_ADMIN"))||!1}),v=e=>{var n;return(n=s.value)!=null&&n.permissions?s.value.permissions.includes(e):!1},f=e=>{var n;return(n=s.value)!=null&&n.roles?s.value.roles.includes(e):!1};return{adminInfo:t(s),sidebarCollapsed:l,activeMenuItem:t(o),isSuperAdmin:m,setAdminInfo:r,clearAdminInfo:u,toggleSidebar:i,setSidebarCollapsed:c,setActiveMenuItem:d,hasPermission:v,hasRole:f}};export{A as u};
