package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 模板搜索请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板搜索请求DTO")
public class TemplateSearchRequest {

    /**
     * 搜索关键词（必填）
     */
    @Schema(description = "搜索关键词", required = true)
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;

    /**
     * 当前页码（默认: 1）
     */
    @Schema(description = "当前页码", example = "1")
    private Integer current = 1;

    /**
     * 每页大小（默认: 12）
     */
    @Schema(description = "每页大小", example = "12")
    private Integer size = 12;

    /**
     * 分类ID（可选）
     */
    @Schema(description = "分类ID")
    private Long categoryId;

    /**
     * 适用行业（可选）
     */
    @Schema(description = "适用行业")
    private String industry;

    /**
     * 模板风格（可选）
     */
    @Schema(description = "模板风格")
    private String style;

    /**
     * 是否付费（可选，0:免费, 1:付费）
     */
    @Schema(description = "是否付费", example = "0")
    private Integer isPremium;

    /**
     * 排序方式（可选，default:默认, popular:热门, latest:最新, rating:评分）
     */
    @Schema(description = "排序方式", example = "default")
    private String sortBy = "default";
} 