package com.alan6.resume.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录类型枚举类
 * 
 * 主要功能：
 * 1. 定义不同的登录方式
 * 2. 提供登录类型的中文描述
 * 3. 提供根据code查找枚举的工具方法
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Getter
@AllArgsConstructor
public enum LoginTypeEnum {

    /**
     * 手机号登录
     * 
     * 适用场景：
     * - Web端手机号+验证码登录
     * - App端手机号+验证码登录
     */
    PHONE(1, "手机号登录"),

    /**
     * 微信授权登录
     * 
     * 适用场景：
     * - Web端微信扫码登录
     * - App端微信授权登录
     */
    WECHAT(2, "微信授权登录"),

    /**
     * 小程序登录
     * 
     * 适用场景：
     * - 微信小程序登录
     * - 抖音小程序登录
     * - 百度小程序登录
     * - 支付宝小程序登录
     */
    MINIPROGRAM(3, "小程序登录");

    /**
     * 登录类型编码
     */
    private final int code;

    /**
     * 登录类型描述
     */
    private final String description;

    /**
     * 根据登录类型编码获取对应的枚举实例
     * 
     * 使用场景：
     * - 前端传入登录类型code，后端转换为枚举进行处理
     * - 参数校验时判断登录类型是否有效
     * 
     * @param code 登录类型编码
     * @return 对应的登录类型枚举，如果未找到则返回null
     */
    public static LoginTypeEnum getByCode(int code) {
        // 遍历所有枚举值，查找匹配的code
        for (LoginTypeEnum loginType : LoginTypeEnum.values()) {
            if (loginType.getCode() == code) {
                return loginType;
            }
        }
        // 未找到匹配的登录类型
        return null;
    }

    /**
     * 判断指定的登录类型编码是否有效
     * 
     * 使用场景：
     * - 接口参数校验
     * - 业务逻辑判断
     * 
     * @param code 登录类型编码
     * @return true-有效，false-无效
     */
    public static boolean isValidCode(int code) {
        return getByCode(code) != null;
    }

    /**
     * 获取所有支持的登录类型编码数组
     * 
     * 使用场景：
     * - 参数校验注解中定义允许的取值范围
     * - 文档生成时显示所有可选值
     * 
     * @return 所有登录类型编码的数组
     */
    public static int[] getAllCodes() {
        LoginTypeEnum[] values = LoginTypeEnum.values();
        int[] codes = new int[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 重写toString方法，返回更友好的字符串表示
     * 
     * @return 格式化的字符串，包含编码和描述
     */
    @Override
    public String toString() {
        return String.format("LoginTypeEnum{code=%d, description='%s'}", code, description);
    }
} 