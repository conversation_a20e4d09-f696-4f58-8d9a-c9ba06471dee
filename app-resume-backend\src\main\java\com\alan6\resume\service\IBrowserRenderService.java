package com.alan6.resume.service;

/**
 * 浏览器渲染服务接口
 * 
 * @description 使用浏览器引擎渲染HTML页面并生成文件
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IBrowserRenderService {

    /**
     * 渲染页面
     * 
     * @param url 页面URL
     * @param format 输出格式（pdf/word/image）
     * @return 渲染后的文件字节数组
     */
    byte[] render(String url, String format);

    /**
     * 渲染页面为PDF
     * 
     * @param url 页面URL
     * @return PDF文件字节数组
     */
    byte[] renderToPdf(String url);

    /**
     * 渲染页面为图片
     * 
     * @param url 页面URL
     * @return 图片文件字节数组
     */
    byte[] renderToImage(String url);

    /**
     * 渲染页面为Word文档
     * 
     * @param url 页面URL
     * @return Word文档字节数组
     */
    byte[] renderToWord(String url);

    /**
     * 测试浏览器服务是否可用
     * 
     * @return 是否可用
     */
    boolean isAvailable();
} 