package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 使用模板请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "使用模板请求DTO")
public class TemplateUseRequest {

    /**
     * 简历名称
     */
    @Schema(description = "简历名称", required = true)
    @NotBlank(message = "简历名称不能为空")
    private String resumeName;

    /**
     * 语言
     */
    @Schema(description = "语言", example = "zh-CN")
    private String language = "zh-CN";
} 