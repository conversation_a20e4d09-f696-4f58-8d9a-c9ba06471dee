<template>
  <div>
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-secondary-900">我的订单</h2>
      <p class="mt-1 text-sm text-secondary-600">查看您的购买记录</p>
    </div>
    
    <div class="text-center py-12">
      <div class="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-medium text-secondary-900 mb-2">暂无订单</h3>
      <p class="text-secondary-600">您还没有任何购买记录</p>
    </div>
  </div>
</template>

<script setup>
// TODO: 实现订单列表功能
</script> 