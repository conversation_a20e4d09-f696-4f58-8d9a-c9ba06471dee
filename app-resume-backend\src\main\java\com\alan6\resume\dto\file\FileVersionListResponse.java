package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件版本列表响应DTO
 * 
 * 主要功能：
 * 1. 返回文件的版本历史列表
 * 2. 支持分页查询
 * 3. 提供版本比较和管理功能
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "文件版本列表响应", description = "文件版本历史列表")
public class FileVersionListResponse {

    /**
     * 总记录数
     */
    @Schema(description = "总记录数", example = "5")
    private Long total;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码", example = "1")
    private Integer page;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer size;

    /**
     * 总页数
     */
    @Schema(description = "总页数", example = "1")
    private Integer pages;

    /**
     * 文件ID
     */
    @Schema(description = "文件ID", example = "F20241222123456789")
    private String fileId;

    /**
     * 版本列表
     */
    @Schema(description = "版本列表")
    private List<FileVersion> versions;

    /**
     * 文件版本内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "文件版本", description = "文件版本信息")
    public static class FileVersion {

        /**
         * 版本ID
         */
        @Schema(description = "版本ID", example = "v1.0.4")
        private String versionId;

        /**
         * ETag
         */
        @Schema(description = "ETag", example = "d41d8cd98f00b204e9800998ecf8427e")
        private String etag;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小", example = "102400")
        private Long fileSize;

        /**
         * 是否为最新版本
         */
        @Schema(description = "是否为最新版本", example = "true")
        private Boolean isLatest;

        /**
         * 存储类别
         */
        @Schema(description = "存储类别", example = "STANDARD")
        private String storageClass;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间", example = "2024-12-22 15:30:00")
        private LocalDateTime createTime;

        /**
         * 最后修改时间
         */
        @Schema(description = "最后修改时间", example = "2024-12-22 15:30:00")
        private LocalDateTime lastModified;

        /**
         * 版本标签
         */
        @Schema(description = "版本标签", example = "stable")
        private String versionTag;

        /**
         * 版本描述
         */
        @Schema(description = "版本描述", example = "修复了图片压缩问题")
        private String description;

        /**
         * 格式化文件大小为可读格式
         * 
         * @return 格式化后的文件大小字符串
         */
        public String getFormattedFileSize() {
            if (fileSize == null) {
                return "未知";
            }
            
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else if (fileSize < 1024 * 1024 * 1024) {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            } else {
                return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
            }
        }

        /**
         * 检查是否为最新版本
         * 
         * @return true-最新版本，false-历史版本
         */
        public boolean isCurrentVersion() {
            return isLatest != null && isLatest;
        }

        /**
         * 获取版本年龄（创建至今的天数）
         * 
         * @return 版本年龄天数
         */
        public long getVersionAge() {
            if (createTime == null) {
                return 0;
            }
            
            return java.time.Duration.between(createTime, LocalDateTime.now()).toDays();
        }

        /**
         * 获取版本状态描述
         * 
         * @return 版本状态描述
         */
        public String getVersionStatus() {
            if (isCurrentVersion()) {
                return "当前版本";
            } else {
                long age = getVersionAge();
                if (age == 0) {
                    return "今天创建";
                } else if (age == 1) {
                    return "昨天创建";
                } else if (age <= 7) {
                    return age + "天前创建";
                } else if (age <= 30) {
                    return (age / 7) + "周前创建";
                } else {
                    return (age / 30) + "个月前创建";
                }
            }
        }
    }

    /**
     * 检查是否有版本数据
     * 
     * @return true-有数据，false-无数据
     */
    public boolean hasVersions() {
        return versions != null && !versions.isEmpty();
    }

    /**
     * 获取最新版本
     * 
     * @return 最新版本，如果不存在返回null
     */
    public FileVersion getLatestVersion() {
        if (versions == null) {
            return null;
        }
        
        return versions.stream()
                .filter(FileVersion::isCurrentVersion)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取历史版本列表
     * 
     * @return 历史版本列表
     */
    public List<FileVersion> getHistoryVersions() {
        if (versions == null) {
            return List.of();
        }
        
        return versions.stream()
                .filter(version -> !version.isCurrentVersion())
                .toList();
    }

    /**
     * 根据版本ID查找版本
     * 
     * @param versionId 版本ID
     * @return 版本信息，如果不存在返回null
     */
    public FileVersion findVersionById(String versionId) {
        if (versions == null || versionId == null) {
            return null;
        }
        
        return versions.stream()
                .filter(version -> versionId.equals(version.getVersionId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查是否有下一页
     * 
     * @return true-有下一页，false-无下一页
     */
    public boolean hasNext() {
        return page != null && pages != null && page < pages;
    }

    /**
     * 检查是否有上一页
     * 
     * @return true-有上一页，false-无上一页
     */
    public boolean hasPrevious() {
        return page != null && page > 1;
    }

    /**
     * 创建空的版本列表响应
     * 
     * @param fileId 文件ID
     * @param page 页码
     * @param size 每页大小
     * @return 空的版本列表响应
     */
    public static FileVersionListResponse empty(String fileId, Integer page, Integer size) {
        return FileVersionListResponse.builder()
                .total(0L)
                .page(page)
                .size(size)
                .pages(0)
                .fileId(fileId)
                .versions(List.of())
                .build();
    }
} 