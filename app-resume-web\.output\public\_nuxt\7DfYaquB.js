import{P as v}from"./DdM5dVZ_.js";import{l as d,A as m,u as g,c as r,a as s,b as h,t as c,F as y,g as f,D as k,o as l,q as b}from"./CURHyiUL.js";import{_ as D}from"./DlAUqK2U.js";import"./D7AOVZt6.js";import"./Ciwj-CUv.js";const w={class:"test-container"},P={class:"test-layout"},N={class:"editor-side"},S={class:"form-container"},T={class:"debug-info"},x={class:"preview-side"},C={class:"preview-container"},O={class:"project-preview"},B={key:0,class:"project-list"},F={class:"project-header"},H={class:"project-role"},L={class:"project-details"},V={class:"project-company"},J={class:"project-period"},M=["innerHTML"],U={key:1,class:"empty-state"},q={class:"debug-info"},A={__name:"project-data-sync-test",setup(E){const o=d({projects:[]}),e=d({projects:[]}),a=i=>{console.log("🔄 项目数据更新:",i),e.projects=i.projects||[],console.log("📊 更新后的预览数据:",e.projects)};m(()=>o.projects,i=>{console.log("👀 监听到项目数据变化:",i)},{deep:!0});const p=()=>{const i={name:`测试项目${o.projects.length+1}`,role:"项目经理",company:"测试公司",startDate:"2023-01",endDate:"2023-12",isOngoing:!1,description:"<p>这是一个<strong>测试项目</strong>的描述</p>"};o.projects.push(i),k(()=>{a({projects:[...o.projects]})})},_=()=>{o.projects=[],e.projects=[]},u=()=>{console.log("📝 编辑器数据:",o),console.log("👁️ 预览数据:",e)};return g({title:"项目经验数据同步测试"}),(i,t)=>(l(),r("div",w,[t[5]||(t[5]=s("h1",null,"项目经验数据同步测试",-1)),s("div",P,[s("div",N,[t[1]||(t[1]=s("h2",null,"编辑器侧（左侧）",-1)),s("div",S,[h(v,{data:o,onUpdate:a},null,8,["data"])]),s("div",T,[t[0]||(t[0]=s("h3",null,"编辑器数据状态：",-1)),s("pre",null,c(JSON.stringify(o,null,2)),1)])]),s("div",x,[t[4]||(t[4]=s("h2",null,"预览侧（右侧）",-1)),s("div",C,[s("div",O,[t[2]||(t[2]=s("h3",null,"项目经历",-1)),e.projects&&e.projects.length>0?(l(),r("div",B,[(l(!0),r(y,null,f(e.projects,(n,j)=>(l(),r("div",{key:j,class:"project-item"},[s("div",F,[s("h4",null,c(n.name||"未命名项目"),1),s("span",H,c(n.role||"未设置角色"),1)]),s("div",L,[s("div",V,c(n.company||"未设置公司"),1),s("div",J,c(n.startDate||"未设置")+" - "+c(n.endDate||n.isOngoing?"至今":"未设置"),1),n.description?(l(),r("div",{key:0,class:"project-description",innerHTML:n.description},null,8,M)):b("",!0)])]))),128))])):(l(),r("div",U," 暂无项目经历 "))])]),s("div",q,[t[3]||(t[3]=s("h3",null,"预览数据状态：",-1)),s("pre",null,c(JSON.stringify(e,null,2)),1)])])]),s("div",{class:"test-actions"},[s("button",{onClick:p},"添加测试项目"),s("button",{onClick:_},"清空项目"),s("button",{onClick:u},"打印状态")])]))}},Q=D(A,[["__scopeId","data-v-b1680b21"]]);export{Q as default};
