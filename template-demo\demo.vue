<template>
  <div class="resume-container">
    <!-- 头部信息 -->
    <header class="header">
      <div class="avatar">
        <img v-if="resumeData.basicInfo.avatar" :src="resumeData.basicInfo.avatar" alt="头像" />
        <span v-else>👤</span>
      </div>
      <h1 class="name">{{ resumeData.basicInfo.name || '张三2' }}</h1>
      <div class="title">{{ resumeData.basicInfo.jobTitle || '高级前端开发工程师' }}</div>
      <div class="contact-info">
        <div v-if="resumeData.basicInfo.phone" class="contact-item">
          <span>📱</span>
          <span>{{ resumeData.basicInfo.phone }}</span>
        </div>
        <div v-if="resumeData.basicInfo.email" class="contact-item">
          <span>📧</span>
          <span>{{ resumeData.basicInfo.email }}</span>
        </div>
        <div v-if="resumeData.basicInfo.address" class="contact-item">
          <span>📍</span>
          <span>{{ resumeData.basicInfo.address }}</span>
        </div>
      </div>
    </header>

    <div class="main-content">
      <!-- 左侧栏 -->
      <aside class="left-column">
        <!-- 专业技能 -->
        <section v-if="resumeData.skills && resumeData.skills.length > 0" class="skills-section">
          <h2 class="section-title">专业技能</h2>
          <div 
            v-for="(skill, index) in resumeData.skills" 
            :key="skill.id || index"
            class="skill-item"
            :class="{ 'draggable': isDraggable }"
            @mouseenter="showMoveButtons = skill.id"
            @mouseleave="showMoveButtons = null"
          >
            <!-- 拖拽操作按钮 -->
            <div v-if="isDraggable && showMoveButtons === skill.id" class="move-buttons">
              <button 
                v-if="index > 0" 
                @click="moveSkillUp(index)"
                class="move-btn move-up"
                title="上移"
              >
                ↑
              </button>
              <button 
                v-if="index < resumeData.skills.length - 1" 
                @click="moveSkillDown(index)"
                class="move-btn move-down"
                title="下移"
              >
                ↓
              </button>
              <button 
                @click="deleteSkill(index)"
                class="move-btn move-delete"
                title="删除"
              >
                ×
              </button>
            </div>
            
            <div class="skill-name">{{ skill.name }}</div>
            <div class="skill-level">
              <div 
                class="skill-progress" 
                :style="{ 
                  width: skill.level + '%',
                  background: textStyle.primaryColor ? `linear-gradient(90deg, ${textStyle.primaryColor}, ${textStyle.secondaryColor || '#764ba2'})` : undefined 
                }"
              ></div>
            </div>
          </div>
        </section>
      </aside>

      <!-- 右侧主要内容 -->
      <main class="right-column">
        <!-- 工作经历 -->
        <section v-if="resumeData.workExperiences && resumeData.workExperiences.length > 0" class="experience-section">
          <h2 class="section-title" :style="{ color: textStyle.primaryColor }">工作经历</h2>
          
          <div 
            v-for="(experience, index) in resumeData.workExperiences" 
            :key="experience.id || index"
            class="experience-item"
            :class="{ 'draggable': isDraggable }"
            @mouseenter="showMoveButtons = experience.id"
            @mouseleave="showMoveButtons = null"
          >
            <!-- 拖拽操作按钮 -->
            <div v-if="isDraggable && showMoveButtons === experience.id" class="move-buttons">
              <button 
                v-if="index > 0" 
                @click="moveExperienceUp(index)"
                class="move-btn move-up"
                title="上移"
              >
                ↑
              </button>
              <button 
                v-if="index < resumeData.workExperiences.length - 1" 
                @click="moveExperienceDown(index)"
                class="move-btn move-down"
                title="下移"
              >
                ↓
              </button>
              <button 
                @click="deleteExperience(index)"
                class="move-btn move-delete"
                title="删除"
              >
                ×
              </button>
            </div>

            <div class="experience-header">
              <div>
                <div class="job-title" :style="{ fontSize: textStyle.fontSize }">
                  {{ experience.jobTitle }}
                </div>
                <div class="company-name" :style="{ color: textStyle.primaryColor }">
                  {{ experience.companyName }}
                </div>
              </div>
              <div class="date-range">
                {{ experience.startDate }} 至 {{ experience.endDate || '现在' }}
              </div>
            </div>
            <div 
              class="job-description" 
              :style="{ 
                fontSize: textStyle.fontSize, 
                lineHeight: textStyle.lineHeight,
                color: textStyle.textColor 
              }"
            >
              {{ experience.description }}
            </div>
          </div>
        </section>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  resumeData: {
    type: Object,
    default: () => ({
      basicInfo: {
        name: '张三2',
        jobTitle: '高级前端开发工程师',
        phone: '138-0013-8001',
        email: '<EMAIL>',
        address: '北京市朝阳区',
        avatar: ''
      },
      skills: [
        { id: 1, name: 'JavaScript', level: 90 },
        { id: 2, name: 'Vue.js', level: 85 },
        { id: 3, name: 'React', level: 80 }
      ],
      workExperiences: [
        {
          id: 1,
          jobTitle: '高级前端开发工程师',
          companyName: 'ABC科技有限公司',
          startDate: '2022-03',
          endDate: '',
          description: '负责公司核心产品的前端架构设计和开发，带领团队完成多个重要项目的技术攻关。'
        },
        {
          id: 2,
          jobTitle: '前端开发工程师',
          companyName: 'XYZ互联网公司',
          startDate: '2020-06',
          endDate: '2022-02',
          description: '负责电商平台前端功能开发和维护，优化页面性能，提升用户体验。'
        }
      ]
    })
  },
  textStyle: {
    type: Object,
    default: () => ({
      fontSize: '14px',
      lineHeight: '1.6',
      textColor: '#333',
      primaryColor: '#667eea',
      secondaryColor: '#764ba2'
    })
  },
  isDraggable: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'update:resumeData',
  'move-skill-up',
  'move-skill-down', 
  'delete-skill',
  'move-experience-up',
  'move-experience-down',
  'delete-experience'
])

// 响应式数据
const showMoveButtons = ref(null)

// 技能操作方法
const moveSkillUp = (index) => {
  emit('move-skill-up', index)
}

const moveSkillDown = (index) => {
  emit('move-skill-down', index)
}

const deleteSkill = (index) => {
  if (confirm('确定要删除这个技能吗？')) {
    emit('delete-skill', index)
  }
}

// 工作经历操作方法
const moveExperienceUp = (index) => {
  emit('move-experience-up', index)
}

const moveExperienceDown = (index) => {
  emit('move-experience-down', index)
}

const deleteExperience = (index) => {
  if (confirm('确定要删除这段工作经历吗？')) {
    emit('delete-experience', index)
  }
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.resume-container {
  max-width: 210mm; /* A4纸宽度 */
  margin: 20px auto;
  background: white;
  box-shadow: 0 0 20px rgba(0,0,0,0.1);
  min-height: 297mm; /* A4纸高度 */
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 头部信息区域 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  text-align: center;
  position: relative;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid white;
  margin: 0 auto 20px;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: #666;
  overflow: hidden;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.name {
  font-size: 2.5em;
  font-weight: bold;
  margin-bottom: 10px;
}

.title {
  font-size: 1.2em;
  opacity: 0.9;
  margin-bottom: 20px;
}

.contact-info {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  font-size: 0.9em;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  min-height: calc(297mm - 200px);
}

.left-column {
  width: 35%;
  background: #f8f9fa;
  padding: 30px;
}

.right-column {
  width: 65%;
  padding: 30px;
}

/* 模块标题 */
.section-title {
  font-size: 1.3em;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #667eea;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 30px;
  height: 2px;
  background: #764ba2;
}

/* 技能模块 */
.skill-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  margin-bottom: 15px;
  position: relative;
}

.skill-item.draggable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transition: all 0.2s ease;
}

.skill-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.skill-level {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 工作经历 */
.experience-item {
  margin-bottom: 30px;
  padding-bottom: 25px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.experience-item:last-child {
  border-bottom: none;
}

.experience-item::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 5px;
  width: 10px;
  height: 10px;
  background: #667eea;
  border-radius: 50%;
}

.experience-item.draggable:hover {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 15px;
  margin: 0 -15px 15px -15px;
  transition: all 0.2s ease;
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.job-title {
  font-size: 1.1em;
  font-weight: bold;
  color: #333;
}

.company-name {
  color: #667eea;
  font-weight: 500;
}

.date-range {
  color: #666;
  font-size: 0.9em;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 拖拽操作按钮 */
.move-buttons {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  z-index: 10;
}

.move-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.move-up, .move-down {
  background: #4f46e5;
  color: white;
}

.move-up:hover, .move-down:hover {
  background: #3730a3;
  transform: scale(1.1);
}

.move-delete {
  background: #ef4444;
  color: white;
}

.move-delete:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 响应式设计 */
@media print {
  .resume-container {
    box-shadow: none;
    margin: 0;
    max-width: none;
  }
  
  .move-buttons {
    display: none;
  }
}

@media (max-width: 768px) {
  .resume-container {
    margin: 10px;
    max-width: none;
  }
  
  .main-content {
    flex-direction: column;
  }
  
  .left-column, .right-column {
    width: 100%;
  }
  
  .contact-info {
    flex-direction: column;
    gap: 10px;
  }
  
  .experience-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style> 