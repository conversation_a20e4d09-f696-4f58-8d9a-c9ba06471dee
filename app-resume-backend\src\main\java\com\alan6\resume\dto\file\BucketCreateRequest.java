package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 存储桶创建请求DTO
 * 
 * 主要功能：
 * 1. 接收创建存储桶的请求参数
 * 2. 支持存储桶配置设置
 * 3. 提供参数验证功能
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "存储桶创建请求", description = "创建存储桶的请求参数")
public class BucketCreateRequest {

    /**
     * 存储桶名称
     * 必填参数，3-63字符，只能包含小写字母、数字、连字符
     */
    @Schema(description = "存储桶名称", example = "custom-bucket", required = true)
    private String name;

    /**
     * 存储桶描述
     * 可选参数，用于说明存储桶用途
     */
    @Schema(description = "存储桶描述", example = "自定义存储桶")
    private String description;

    /**
     * 区域
     * 可选参数，默认为us-east-1
     */
    @Schema(description = "区域", example = "us-east-1")
    private String region;

    /**
     * 是否启用版本控制
     * 可选参数，默认为true
     */
    @Schema(description = "是否启用版本控制", example = "true")
    private Boolean versioning;

    /**
     * 加密类型
     * 可选参数，SSE-S3/SSE-KMS，默认为SSE-S3
     */
    @Schema(description = "加密类型", example = "SSE-S3", allowableValues = {"SSE-S3", "SSE-KMS", "SSE-C"})
    private String encryption;

    /**
     * 存储类别
     * 可选参数，默认为STANDARD
     */
    @Schema(description = "存储类别", example = "STANDARD", allowableValues = {"STANDARD", "REDUCED_REDUNDANCY", "COLD"})
    private String storageClass;

    /**
     * 访问策略
     * 可选参数，默认为private
     */
    @Schema(description = "访问策略", example = "private", allowableValues = {"private", "public-read", "public-read-write"})
    private String accessPolicy;

    /**
     * 是否启用对象锁定
     * 可选参数，默认为false
     */
    @Schema(description = "是否启用对象锁定", example = "false")
    private Boolean objectLock;

    /**
     * 生命周期规则
     * 可选参数，用于设置对象自动删除规则
     */
    @Schema(description = "生命周期规则（天数）", example = "365")
    private Integer lifecycleDays;

    /**
     * 是否启用事件通知
     * 可选参数，默认为false
     */
    @Schema(description = "是否启用事件通知", example = "false")
    private Boolean eventNotification;

    /**
     * 标签
     * 可选参数，用于存储桶分类和管理
     */
    @Schema(description = "标签", example = "project:resume,env:prod")
    private String tags;

    /**
     * 验证请求参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return name != null && !name.trim().isEmpty() && isValidBucketName(name);
    }

    /**
     * 验证存储桶名称是否符合规范
     * 
     * @param bucketName 存储桶名称
     * @return true-符合规范，false-不符合规范
     */
    private boolean isValidBucketName(String bucketName) {
        if (bucketName == null || bucketName.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = bucketName.trim().toLowerCase();
        
        // 长度检查
        if (trimmed.length() < 3 || trimmed.length() > 63) {
            return false;
        }
        
        // 格式检查：只能包含小写字母、数字、连字符
        if (!trimmed.matches("^[a-z0-9][a-z0-9-]*[a-z0-9]$")) {
            return false;
        }
        
        // 不能包含连续的连字符
        if (trimmed.contains("--")) {
            return false;
        }
        
        return true;
    }

    /**
     * 验证加密类型是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValidEncryption() {
        return encryption == null || 
               encryption.equals("SSE-S3") || 
               encryption.equals("SSE-KMS") || 
               encryption.equals("SSE-C");
    }

    /**
     * 验证存储类别是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValidStorageClass() {
        return storageClass == null || 
               storageClass.equals("STANDARD") || 
               storageClass.equals("REDUCED_REDUNDANCY") || 
               storageClass.equals("COLD");
    }

    /**
     * 验证访问策略是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValidAccessPolicy() {
        return accessPolicy == null || 
               accessPolicy.equals("private") || 
               accessPolicy.equals("public-read") || 
               accessPolicy.equals("public-read-write");
    }

    /**
     * 获取默认区域
     * 
     * @return 默认区域
     */
    public String getDefaultRegion() {
        return region != null ? region : "us-east-1";
    }

    /**
     * 获取默认版本控制设置
     * 
     * @return 默认版本控制设置
     */
    public Boolean getDefaultVersioning() {
        return versioning != null ? versioning : true;
    }

    /**
     * 获取默认加密类型
     * 
     * @return 默认加密类型
     */
    public String getDefaultEncryption() {
        return encryption != null ? encryption : "SSE-S3";
    }

    /**
     * 获取默认存储类别
     * 
     * @return 默认存储类别
     */
    public String getDefaultStorageClass() {
        return storageClass != null ? storageClass : "STANDARD";
    }

    /**
     * 获取默认访问策略
     * 
     * @return 默认访问策略
     */
    public String getDefaultAccessPolicy() {
        return accessPolicy != null ? accessPolicy : "private";
    }

    /**
     * 获取默认对象锁定设置
     * 
     * @return 默认对象锁定设置
     */
    public Boolean getDefaultObjectLock() {
        return objectLock != null ? objectLock : false;
    }

    /**
     * 获取默认事件通知设置
     * 
     * @return 默认事件通知设置
     */
    public Boolean getDefaultEventNotification() {
        return eventNotification != null ? eventNotification : false;
    }

    /**
     * 标准化存储桶名称
     * 将输入的名称转换为符合规范的存储桶名称
     * 
     * @return 标准化后的存储桶名称
     */
    public String getNormalizedName() {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        
        // 转换为小写并移除非法字符
        String normalized = name.trim()
                .toLowerCase()
                .replaceAll("[^a-z0-9-]", "-")
                .replaceAll("-+", "-")
                .replaceAll("^-+|-+$", "");
        
        // 确保长度在合理范围内
        if (normalized.length() < 3) {
            return null;
        } else if (normalized.length() > 63) {
            normalized = normalized.substring(0, 63);
        }
        
        // 确保以字母或数字开头和结尾
        if (!normalized.matches("^[a-z0-9].*[a-z0-9]$")) {
            return null;
        }
        
        return normalized;
    }

    /**
     * 检查是否有自定义配置
     * 
     * @return true-有自定义配置，false-使用默认配置
     */
    public boolean hasCustomConfiguration() {
        return region != null || 
               versioning != null || 
               encryption != null || 
               storageClass != null || 
               accessPolicy != null || 
               objectLock != null || 
               lifecycleDays != null || 
               eventNotification != null || 
               tags != null;
    }

    /**
     * 获取配置摘要
     * 
     * @return 配置摘要字符串
     */
    public String getConfigurationSummary() {
        return String.format("存储桶: %s, 区域: %s, 版本控制: %s, 加密: %s, 存储类别: %s, 访问策略: %s", 
                name, 
                getDefaultRegion(), 
                getDefaultVersioning(), 
                getDefaultEncryption(), 
                getDefaultStorageClass(), 
                getDefaultAccessPolicy());
    }
} 