import{_ as y}from"./BXnw39BI.js";import{e as _,r as g,K as e,k as w,A as b,i as M,L as C,c as i,a as o,F as B,g as j,z as d,M as p,N as z,o as a,b as L,w as V,s as c,t as $}from"./CURHyiUL.js";import m from"./BIEVL60m.js";import H from"./CwhbaRSW.js";import N from"./oq5eyTLE.js";import R from"./txG9Uhjy.js";import S from"./OzmMLQKy.js";import{_ as q}from"./DlAUqK2U.js";import"./D1FrdRFX.js";const A={class:"min-h-screen bg-gray-50 pt-16"},D={class:"max-w-screen-2xl mx-auto px-6 lg:px-8 py-8"},F={class:"flex gap-10"},I={class:"w-72 flex-shrink-0"},E={class:"space-y-2"},K={class:"flex-1 text-base"},G={class:"flex-1 bg-white rounded-lg shadow-sm p-8"},J={__name:"index",setup(O){const u=C(),k=z();_({title:"个人中心 - 火花简历",description:"管理您的简历、订单、收藏和账户设置"});const n=g("resumes"),l=[{key:"resumes",label:"我的简历",icon:e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])},{key:"orders",label:"我的订单",icon:e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"})])},{key:"favorites",label:"我的收藏",icon:e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])},{key:"membership",label:"会员购买",icon:e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])},{key:"settings",label:"账户设置",icon:e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])}],h={resumes:m,orders:H,favorites:N,membership:R,settings:S},v=w(()=>h[n.value]||m),x=()=>{k.push("/templates")};return b(()=>u.query.tab,t=>{t&&l.some(s=>s.key===t)&&(n.value=t)},{immediate:!0}),M(()=>{const t=u.query.tab;t&&l.some(s=>s.key===t)&&(n.value=t)}),(t,s)=>{const f=y;return a(),i("div",A,[o("div",D,[o("div",F,[o("aside",I,[o("button",{onClick:x,class:"w-full mb-8 px-6 py-4 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center space-x-3 text-base"},s[0]||(s[0]=[o("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),o("span",null,"新建简历",-1)])),o("nav",E,[(a(),i(B,null,j(l,r=>L(f,{key:r.key,to:`/profile?tab=${r.key}`,onClick:P=>n.value=r.key,class:c(["flex items-center space-x-4 px-6 py-4 text-base font-medium transition-all duration-150 rounded-lg relative group",n.value===r.key?"bg-blue-50 text-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"])},{default:V(()=>[(a(),d(p(r.icon),{class:c(["w-6 h-6 flex-shrink-0",n.value===r.key?"text-blue-600":"text-gray-400 group-hover:text-gray-600"])},null,8,["class"])),o("span",K,$(r.label),1),(a(),i("svg",{class:c(["w-5 h-5 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity",n.value===r.key&&"opacity-100"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},s[1]||(s[1]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))]),_:2},1032,["to","onClick","class"])),64))])]),o("main",G,[(a(),d(p(v.value)))])])])])}}},te=q(J,[["__scopeId","data-v-ac857043"]]);export{te as default};
