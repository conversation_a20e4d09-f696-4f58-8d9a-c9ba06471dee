/**
 * 模板列表服务
 * 提供模板列表的获取、搜索、筛选、分页等功能
 */

import { ref, reactive, computed } from 'vue'

export const useTemplateListService = () => {
  // 响应式数据
  const templates = ref([])
  const categories = ref([])
  const loading = ref(false)
  const error = ref(null)
  
  // 分页信息
  const pagination = reactive({
    current: 1,
    size: 10,
    total: 0,
    pages: 0
  })
  
  // 筛选条件
  const filters = reactive({
    keyword: '',
    categoryId: '',
    isPremium: '', // '', 0, 1, 2
    isDeleted: '', // '', 0, 1
    sortBy: 'default' // default, latest, popular, price_asc, price_desc
  })
  
  // 计算属性
  const hasMore = computed(() => {
    return pagination.current < pagination.pages
  })
  
  const isEmpty = computed(() => {
    return templates.value.length === 0 && !loading.value
  })
  
  /**
   * 获取模板列表
   */
  const fetchTemplates = async (params = {}) => {
    loading.value = true
    error.value = null
    
    try {

      
      // 构建请求参数，过滤空值
      const requestParams = {
        current: pagination.current,
        size: pagination.size,
        ...params
      }
      
      // 只添加非空的筛选参数
      if (filters.keyword && filters.keyword.trim()) {
        requestParams.keyword = filters.keyword.trim()
      }
      if (filters.categoryId) {
        requestParams.categoryId = filters.categoryId
      }
      if (filters.isPremium !== '' && filters.isPremium !== null && filters.isPremium !== undefined) {
        requestParams.isPremium = filters.isPremium
      }
      if (filters.isDeleted !== '' && filters.isDeleted !== null && filters.isDeleted !== undefined) {
        requestParams.isDeleted = filters.isDeleted
      }
      if (filters.sortBy && filters.sortBy !== 'default') {
        requestParams.sortBy = filters.sortBy
      }
      

      
      // 使用管理员专用的模板列表接口
      const response = await $fetch('/api/admin/template/list', {
        method: 'GET',
        params: requestParams,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      })
      
      if (response.code === 200) {
        
        // 更新数据
        let templatesList = response.data.records || []
        
        // 处理预览图URL，确保能正确访问MinIO中的图片
        templatesList = templatesList.map(template => {
          if (template.previewImageUrl) {
            // 如果预览图URL不是完整路径，通过API代理访问
            if (!template.previewImageUrl.startsWith('http') && !template.previewImageUrl.startsWith('/api/')) {
              template.previewImageUrl = `/api/admin/template/preview-image/${template.templateCode}`
            }
          }
          return template
        })
        
        templates.value = templatesList
        
        // 修复：确保total是数字类型
        pagination.total = parseInt(response.data.total) || 0
        pagination.current = response.data.current || 1
        pagination.size = response.data.size || 10
        // 修复：如果pages为0但有数据，重新计算pages
        pagination.pages = response.data.pages || (pagination.total > 0 ? Math.ceil(pagination.total / pagination.size) : 0)
        

        
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '获取模板列表失败')
      }
      
    } catch (err) {
      console.error('❌ 获取模板列表失败:', err)
      error.value = err.message || '获取模板列表失败'
      
      return {
        success: false,
        error: error.value
      }
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 搜索模板
   */
  const searchTemplates = async (keyword) => {
    filters.keyword = keyword
    pagination.current = 1 // 重置到第一页
    return await fetchTemplates()
  }
  
  /**
   * 筛选模板
   */
  const filterTemplates = async (filterParams) => {
    Object.assign(filters, filterParams)
    pagination.current = 1 // 重置到第一页
    return await fetchTemplates()
  }
  
  /**
   * 分页加载
   */
  const loadPage = async (page) => {
    pagination.current = page
    return await fetchTemplates()
  }
  
  /**
   * 加载更多
   */
  const loadMore = async () => {
    if (hasMore.value && !loading.value) {
      pagination.current += 1
      const result = await fetchTemplates()
      
      if (result.success && result.data.records) {
        // 追加到现有列表
        templates.value.push(...result.data.records)
      }
      
      return result
    }
    return { success: false, error: '没有更多数据' }
  }
  
  /**
   * 刷新列表
   */
  const refreshTemplates = async () => {
    pagination.current = 1
    return await fetchTemplates()
  }
  
  /**
   * 重置筛选条件
   */
  const resetFilters = () => {
    filters.keyword = ''
    filters.categoryId = ''
    filters.isPremium = ''
    filters.isDeleted = ''
    filters.sortBy = 'default'
    pagination.current = 1
  }
  
  /**
   * 获取模板分类列表
   */
  const fetchCategories = async () => {
    try {
      // 使用管理员专用的分类接口
      const response = await $fetch('/api/admin/template/categories', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      })
      
      if (response.code === 200) {
        categories.value = response.data || []
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '获取模板分类失败')
      }
      
    } catch (err) {
      console.error('❌ 获取模板分类失败:', err)
      error.value = err.message || '获取模板分类失败'
      
      return {
        success: false,
        error: error.value
      }
    }
  }
  
  /**
   * 删除模板
   */
  const deleteTemplate = async (templateId) => {
    try {
      
              const response = await $fetch(`/api/admin/template/${templateId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      })
      
      if (response.code === 200) {
        
        // 从本地列表中移除
        templates.value = templates.value.filter(t => t.id !== templateId)
        
        return {
          success: true,
          message: '模板删除成功'
        }
      } else {
        throw new Error(response.msg || '删除模板失败')
      }
      
    } catch (err) {
      console.error('❌ 删除模板失败:', err)
      
      return {
        success: false,
        error: err.message || '删除模板失败'
      }
    }
  }
  
  /**
   * 更新模板状态
   */
  const updateTemplateStatus = async (templateId, status) => {
    try {
      
      const response = await $fetch(`/api/admin/template/${templateId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json'
        },
        body: {
          status: status
        }
      })
      
      if (response.code === 200) {
        
        // 更新本地状态
        const template = templates.value.find(t => t.id === templateId)
        if (template) {
          template.status = status
        }
        
        return {
          success: true,
          message: '模板状态更新成功'
        }
      } else {
        throw new Error(response.msg || '更新模板状态失败')
      }
      
    } catch (err) {
      console.error('❌ 更新模板状态失败:', err)
      
      return {
        success: false,
        error: err.message || '更新模板状态失败'
      }
    }
  }
  
  /**
   * 批量删除模板
   */
  const batchDeleteTemplates = async (templateIds) => {
    try {
      
              const response = await $fetch('/api/admin/template/batch-delete', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json'
        },
        body: {
          templateIds: templateIds
        }
      })
      
      if (response.code === 200) {
        
        // 从本地列表中移除
        templates.value = templates.value.filter(t => !templateIds.includes(t.id))
        
        return {
          success: true,
          message: '模板批量删除成功'
        }
      } else {
        throw new Error(response.msg || '批量删除模板失败')
      }
      
    } catch (err) {
      console.error('❌ 批量删除模板失败:', err)
      
      return {
        success: false,
        error: err.message || '批量删除模板失败'
      }
    }
  }
  
  return {
    // 数据
    templates,
    categories,
    loading,
    error,
    pagination,
    filters,
    
    // 计算属性
    hasMore,
    isEmpty,
    
    // 方法
    fetchTemplates,
    searchTemplates,
    filterTemplates,
    loadPage,
    loadMore,
    refreshTemplates,
    resetFilters,
    fetchCategories,
    deleteTemplate,
    updateTemplateStatus,
    batchDeleteTemplates
  }
} 