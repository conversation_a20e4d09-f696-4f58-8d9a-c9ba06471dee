package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 简历模板内容实体类
 * 
 * @description 存储简历模板的默认内容数据，支持按行业、职位等维度分类
 *              实现模板样式与内容数据的分离，提高内容复用性
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "resume_template_content", autoResultMap = true)
public class ResumeTemplateContent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 内容ID - 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 内容名称
     * 
     * @description 便于管理和识别的内容名称
     * @example "前端工程师标准内容"、"产品经理通用内容"
     */
    @TableField("name")
    private String name;

    /**
     * 内容代码
     * 
     * @description 唯一标识内容的代码，用于程序化调用
     * @example "frontend-standard"、"product-manager-common"
     */
    @TableField("code")
    private String code;

    /**
     * 内容描述
     * 
     * @description 详细描述内容的适用场景和特点
     */
    @TableField("description")
    private String description;

    /**
     * 简历内容数据
     * 
     * @description JSON格式存储的完整简历内容数据，包含：
     *              - 基本信息模板
     *              - 工作经历示例
     *              - 项目经验模板
     *              - 教育背景示例
     *              - 技能清单模板
     *              - 其他模块的默认内容
     */
    @TableField(value = "content_data", typeHandler = JacksonTypeHandler.class)
    private Object contentData;

    /**
     * 适用行业
     * 
     * @description 该内容适用的行业分类
     * @example "IT互联网"、"金融"、"教育"、"制造业"
     */
    @TableField("industry")
    private String industry;

    /**
     * 适用职位
     * 
     * @description 该内容适用的职位类型
     * @example "前端工程师"、"产品经理"、"数据分析师"
     */
    @TableField("position")
    private String position;

    /**
     * 语言
     * 
     * @description 内容使用的语言
     * @example "zh-CN"、"en-US"、"ja-JP"
     */
    @TableField("language")
    private String language;

    /**
     * 版本号
     * 
     * @description 内容的版本信息，用于版本管理和升级
     * @example "1.0.0"、"2.1.3"
     */
    @TableField("version")
    private String version;

    /**
     * 状态
     * 
     * @description 内容的启用状态
     * @value 0 禁用 - 不可使用
     * @value 1 启用 - 可正常使用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     * 
     * @description 内容记录的创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     * 
     * @description 内容记录的最后更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    // ================================
    // 业务常量定义
    // ================================

    /**
     * 状态常量 - 禁用
     */
    public static final Integer STATUS_DISABLED = 0;

    /**
     * 状态常量 - 启用
     */
    public static final Integer STATUS_ENABLED = 1;

    /**
     * 默认语言
     */
    public static final String DEFAULT_LANGUAGE = "zh-CN";

    /**
     * 默认版本
     */
    public static final String DEFAULT_VERSION = "1.0.0";

    // ================================
    // 业务方法
    // ================================

    /**
     * 判断内容是否启用
     * 
     * @return true-启用，false-禁用
     */
    public boolean isEnabled() {
        return STATUS_ENABLED.equals(this.status);
    }

    /**
     * 判断内容是否禁用
     * 
     * @return true-禁用，false-启用
     */
    public boolean isDisabled() {
        return STATUS_DISABLED.equals(this.status);
    }

    /**
     * 判断是否为中文内容
     * 
     * @return true-中文，false-其他语言
     */
    public boolean isChineseContent() {
        return DEFAULT_LANGUAGE.equals(this.language);
    }

    /**
     * 获取内容数据的JSON字符串
     * 
     * @return JSON字符串，如果为空则返回null
     */
    public String getContentDataAsString() {
        if (this.contentData == null) {
            return null;
        }
        // 这里可以根据实际需要进行JSON序列化
        return this.contentData.toString();
    }
} 