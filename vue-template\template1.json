{"templateCategory": "modern", "templateName": "现代简洁简历模板", "description": "适用于互联网、科技行业的现代化简历模板", "version": "1.0.0", "defaultContent": {"basic_info": {"name": "李明浩", "gender": "男", "age": "28", "currentCity": "上海", "intendedCity": "上海", "phone": "138-1234-5678", "email": "limin<PERSON>@example.com", "address": "上海市浦东新区张江高科技园区", "currentStatus": "在职-考虑机会", "expectedPosition": "高级产品经理", "expectedSalary": "25-35K", "photo": "", "wechat": "liminhao2025", "website": "https://liminhao.portfolio.com", "github": "https://github.com/liminhao", "height": "175cm", "weight": "68kg", "politicalStatus": "群众", "maritalStatus": "未婚", "hometown": "江苏南京", "ethnicity": "汉族"}, "education": [{"id": "edu_1", "school": "复旦大学", "major": "计算机科学与技术", "degree": "本科", "start_date": "2018-09", "end_date": "2022-06", "description": "GPA: 3.7/4.0，主修课程：数据结构、算法设计、软件工程、人机交互设计。获得国家励志奖学金，担任班级学习委员。"}, {"id": "edu_2", "school": "上海交通大学", "major": "工商管理", "degree": "硕士", "start_date": "2022-09", "end_date": "2024-06", "description": "专业方向：数字化转型与产品管理。参与多个企业实训项目，深入了解产品全生命周期管理。"}], "work_experience": [{"id": "work_1", "company": "字节跳动", "position": "产品经理", "start_date": "2024-07", "end_date": "至今", "description": "负责抖音电商产品功能规划与迭代，主导用户增长策略制定。与技术、设计、运营团队紧密协作，推动产品持续优化。\n\n主要成就：\n• 主导推出智能推荐功能，用户停留时长提升35%\n• 优化购买流程，转化率提升28%\n• 建立数据驱动的产品决策体系，A/B测试覆盖率达90%"}, {"id": "work_2", "company": "美团", "position": "产品助理", "start_date": "2023-03", "end_date": "2024-06", "description": "参与美团外卖商家端产品设计，负责需求调研、原型设计、用户测试等工作。深度参与产品从0到1的完整开发流程。\n\n主要贡献：\n• 完成50+商家深度访谈，输出用户画像和需求分析报告\n• 设计并上线智能排班功能，帮助商家节省30%人力成本\n• 协助制定产品路线图，功能上线准确率达95%"}], "project": [{"id": "project_1", "name": "AI智能客服产品", "role": "产品负责人", "technologies": "NLP、机器学习、React、Node.js", "description": "设计并推动实现基于AI的智能客服系统，支持多轮对话、情感分析、智能路由等功能。\n\n项目成果：\n• 客服效率提升60%，用户满意度提升25%\n• 支持10种语言，日处理咨询量50万+\n• 获得公司年度最佳产品创新奖"}, {"id": "project_2", "name": "企业级SaaS数据分析平台", "role": "核心产品经理", "technologies": "大数据、可视化、微服务架构", "description": "负责企业级数据分析平台的产品规划，为中小企业提供一站式数据洞察解决方案。\n\n关键指标：\n• 服务企业客户200+，月活跃用户10万+\n• 平台稳定性99.9%，数据准确率99.5%\n• 获得客户NPS评分85+"}], "skills": [{"id": "skill_1", "name": "产品规划", "level": 90}, {"id": "skill_2", "name": "用户研究", "level": 85}, {"id": "skill_3", "name": "数据分析", "level": 88}, {"id": "skill_4", "name": "原型设计", "level": 82}, {"id": "skill_5", "name": "项目管理", "level": 86}, {"id": "skill_6", "name": "SQL", "level": 75}, {"id": "skill_7", "name": "Python", "level": 70}, {"id": "skill_8", "name": "Figma", "level": 80}], "award": [{"id": "award_1", "name": "年度最佳产品创新奖", "date": "2024-12", "organization": "字节跳动", "level": "公司级", "description": "凭借AI智能客服产品的突出表现，获得公司年度最佳产品创新奖，该产品为公司节省成本超过500万元。"}, {"id": "award_2", "name": "优秀毕业生", "date": "2024-06", "organization": "上海交通大学", "level": "校级", "description": "学业成绩优异，积极参与学术研究和社会实践，获得上海交通大学优秀毕业生荣誉称号。"}, {"id": "award_3", "name": "全国大学生创新创业大赛银奖", "date": "2021-11", "organization": "教育部", "level": "国家级", "description": "带领团队开发智能学习辅助系统，在全国大学生创新创业大赛中获得银奖，项目获得投资意向50万元。"}], "self_evaluation": {"content": "我是一名充满激情的产品经理，拥有3年互联网产品经验，专注于用户体验和商业价值的平衡。具备敏锐的市场洞察力和强大的逻辑思维能力，善于将复杂的业务需求转化为简洁易用的产品方案。\n\n在工作中，我坚持数据驱动的产品决策方式，通过深入的用户研究和细致的数据分析，确保每个产品决策都有充分的依据。同时，我具有良好的沟通协调能力，能够有效推动跨部门合作，确保产品按时高质量交付。\n\n我热爱学习新技术和新方法，紧跟行业发展趋势，持续提升自己的专业能力。未来希望在更大的平台上发挥自己的专业特长，为用户创造更大价值。"}}, "metadata": {"createdAt": "2025-01-16T10:30:00Z", "updatedAt": "2025-01-16T10:30:00Z", "author": "火花简历团队", "tags": ["现代", "商务", "产品", "互联网"], "targetIndustries": ["互联网", "科技", "电商", "金融科技"], "targetPositions": ["产品经理", "产品总监", "项目管理", "运营经理"], "language": "zh-CN", "contentLevel": "senior"}}