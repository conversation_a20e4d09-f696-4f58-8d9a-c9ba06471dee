package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.user.*;
import com.alan6.resume.entity.Users;
import com.alan6.resume.entity.UserFavorites;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.mapper.UsersMapper;
import com.alan6.resume.service.IUserFavoritesService;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.IResumeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 用户服务实现类单元测试
 * 
 * 测试范围：
 * 1. 用户信息管理功能
 * 2. 用户设置管理功能
 * 3. 异常情况处理
 * 4. 边界条件验证
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户服务实现类测试")
class UsersServiceImplTest {

    /**
     * 被测试的服务实现类 - 使用Spy以便部分Mock
     */
    @Spy
    @InjectMocks
    private UsersServiceImpl usersService;

    /**
     * Mock的Mapper依赖
     */
    @Mock
    private UsersMapper usersMapper;

    /**
     * Mock的收藏服务依赖
     */
    @Mock
    private IUserFavoritesService userFavoritesService;

    /**
     * Mock的模板服务依赖
     */
    @Mock
    private IResumeTemplatesService resumeTemplatesService;

    /**
     * Mock的简历服务依赖
     */
    @Mock
    private IResumeService resumesService;

    /**
     * 测试用户数据
     */
    private Users testUser;

    /**
     * 测试用户ID
     */
    private final Long TEST_USER_ID = 1001L;

    /**
     * 每个测试方法执行前的初始化
     */
    @BeforeEach
    void setUp() {
        // 初始化测试用户数据
        testUser = new Users();
        testUser.setId(TEST_USER_ID);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("***********");
        testUser.setNickname("测试用户");
        testUser.setAvatarUrl("https://example.com/avatar.jpg");
        testUser.setGender((byte) 1);
        testUser.setBirthday(LocalDate.of(1990, 1, 1));
        testUser.setRegisterType((byte) 2);
        testUser.setRegisterPlatform("web");
        testUser.setPreferredLanguage("zh-CN");
        testUser.setStatus((byte) 1);
        testUser.setIsPhoneVerified((byte) 1);
        testUser.setCreateTime(LocalDateTime.now().minusDays(30));
        testUser.setLastLoginTime(LocalDateTime.now().minusHours(1));
        testUser.setLastLoginIp("*************");
        testUser.setLastLoginPlatform("web");
    }

    /**
     * 测试获取用户信息 - 成功场景
     */
    @Test
    @DisplayName("获取用户信息 - 成功")
    void testGetUserProfile_Success() {
        // Given: 模拟用户存在 - 使用doReturn方式Mock Spy对象的方法
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // When: 调用获取用户信息方法
        UserProfileResponse response = usersService.getUserProfile(TEST_USER_ID);

        // Then: 验证结果
        assertNotNull(response, "响应不应为空");
        assertEquals(TEST_USER_ID, response.getId(), "用户ID应该匹配");
        assertEquals("testuser", response.getUsername(), "用户名应该匹配");
        assertEquals("t***<EMAIL>", response.getEmail(), "邮箱应该脱敏");
        assertEquals("138****8000", response.getPhone(), "手机号应该脱敏");
        assertEquals("测试用户", response.getNickname(), "昵称应该匹配");
        assertEquals((byte) 1, response.getGender(), "性别应该匹配");
        assertEquals("zh-CN", response.getPreferredLanguage(), "语言偏好应该匹配");
        
        // 验证会员信息
        assertNotNull(response.getMembership(), "会员信息不应为空");
        assertEquals(0, response.getMembership().getLevel(), "默认会员等级应该为0");

        // 验证方法调用
        verify(usersService, times(1)).getById(TEST_USER_ID);
    }

    /**
     * 测试获取用户信息 - 用户不存在
     */
    @Test
    @DisplayName("获取用户信息 - 用户不存在")
    void testGetUserProfile_UserNotFound() {
        // Given: 模拟用户不存在
        doReturn(null).when(usersService).getById(TEST_USER_ID);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> usersService.getUserProfile(TEST_USER_ID),
                "应该抛出用户不存在异常");
        
        assertEquals("用户不存在", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
    }

    /**
     * 测试更新用户信息 - 成功场景
     */
    @Test
    @DisplayName("更新用户信息 - 成功")
    void testUpdateUserProfile_Success() {
        // Given: 准备更新请求和模拟数据
        UserProfileRequest request = new UserProfileRequest();
        request.setNickname("新昵称");
        request.setGender((byte) 2);
        request.setBirthday(LocalDate.of(1995, 6, 15));
        request.setPreferredLanguage("en");

        doReturn(testUser).when(usersService).getById(TEST_USER_ID);
        doReturn(true).when(usersService).updateById(any(Users.class));

        // When: 调用更新方法
        boolean result = usersService.updateUserProfile(TEST_USER_ID, request);

        // Then: 验证结果
        assertTrue(result, "更新应该成功");
        
        // 验证方法调用
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(usersService, times(1)).updateById(any(Users.class));
    }

    /**
     * 测试更新用户信息 - 用户不存在
     */
    @Test
    @DisplayName("更新用户信息 - 用户不存在")
    void testUpdateUserProfile_UserNotFound() {
        // Given: 准备更新请求，模拟用户不存在
        UserProfileRequest request = new UserProfileRequest();
        request.setNickname("新昵称");

        doReturn(null).when(usersService).getById(TEST_USER_ID);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.updateUserProfile(TEST_USER_ID, request),
                "应该抛出用户不存在异常");

        assertEquals("用户不存在", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(usersService, never()).updateById(any(Users.class));
    }

    /**
     * 测试获取用户设置 - 成功场景
     */
    @Test
    @DisplayName("获取用户设置 - 成功")
    void testGetUserSettings_Success() {
        // Given: 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // When: 调用获取用户设置方法
        UserSettingsResponse response = usersService.getUserSettings(TEST_USER_ID);

        // Then: 验证结果
        assertNotNull(response, "响应不应为空");
        assertEquals("zh-CN", response.getPreferredLanguage(), "语言偏好应该匹配");

        verify(usersService, times(1)).getById(TEST_USER_ID);
    }

    /**
     * 测试获取用户设置 - 用户不存在
     */
    @Test
    @DisplayName("获取用户设置 - 用户不存在")
    void testGetUserSettings_UserNotFound() {
        // Given: 模拟用户不存在
        doReturn(null).when(usersService).getById(TEST_USER_ID);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.getUserSettings(TEST_USER_ID),
                "应该抛出用户不存在异常");

        assertEquals("用户不存在", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
    }

    /**
     * 测试更新用户设置 - 成功场景
     */
    @Test
    @DisplayName("更新用户设置 - 成功")
    void testUpdateUserSettings_Success() {
        // Given: 准备更新请求和模拟数据
        UserSettingsRequest request = new UserSettingsRequest();
        request.setPreferredLanguage("en");

        doReturn(testUser).when(usersService).getById(TEST_USER_ID);
        doReturn(true).when(usersService).updateById(any(Users.class));

        // When: 调用更新方法
        boolean result = usersService.updateUserSettings(TEST_USER_ID, request);

        // Then: 验证结果
        assertTrue(result, "更新应该成功");

        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(usersService, times(1)).updateById(any(Users.class));
    }

    /**
     * 测试头像上传 - 功能未实现
     */
    @Test
    @DisplayName("头像上传 - 功能未实现")
    void testUploadAvatar_NotImplemented() {
        // Given: 准备文件对象
        MultipartFile mockFile = mock(MultipartFile.class);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.uploadAvatar(TEST_USER_ID, mockFile),
                "应该抛出功能未实现异常");

        assertEquals("头像上传功能暂未实现", exception.getMessage(), "异常消息应该匹配");
    }

    /**
     * 测试密码修改 - 功能未实现
     */
    @Test
    @DisplayName("密码修改 - 功能未实现")
    void testChangePassword_NotImplemented() {
        // Given: 准备密码修改请求
        PasswordChangeRequest request = new PasswordChangeRequest();
        request.setVerificationType(1);
        request.setOldPassword("oldpassword");
        request.setNewPassword("newpassword123");
        request.setConfirmPassword("newpassword123");

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.changePassword(TEST_USER_ID, request),
                "应该抛出功能未实现异常");

        assertEquals("密码修改功能暂未实现", exception.getMessage(), "异常消息应该匹配");
    }

    /**
     * 测试手机号绑定 - 功能未实现
     */
    @Test
    @DisplayName("手机号绑定 - 功能未实现")
    void testBindPhone_NotImplemented() {
        // Given: 准备手机号绑定请求
        PhoneBindRequest request = new PhoneBindRequest();
        request.setPhone("***********");
        request.setSmsCode("123456");

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.bindPhone(TEST_USER_ID, request),
                "应该抛出功能未实现异常");

        assertEquals("手机号绑定功能暂未实现", exception.getMessage(), "异常消息应该匹配");
    }

    /**
     * 测试验证方法 - 返回默认值
     */
    @Test
    @DisplayName("验证方法 - 返回默认值")
    void testVerifyMethods_DefaultValues() {
        // When & Then: 测试各种验证方法的默认返回值
        assertFalse(usersService.verifyPassword(TEST_USER_ID, "password"), 
                "密码验证应该返回false");
        
        assertFalse(usersService.verifySmsCode("***********", "123456"), 
                "短信验证码验证应该返回false");
        
        assertFalse(usersService.verifyEmailCode("<EMAIL>", "123456"), 
                "邮箱验证码验证应该返回false");
    }

    /**
     * 测试邮箱脱敏功能
     */
    @Test
    @DisplayName("测试邮箱脱敏功能")
    void testEmailMasking() {
        // 测试正常邮箱
        assertEquals("t***<EMAIL>", 
                UserProfileResponse.maskEmail("<EMAIL>"), 
                "邮箱脱敏应该正确");

        // 测试短邮箱
        assertEquals("<EMAIL>", 
                UserProfileResponse.maskEmail("<EMAIL>"), 
                "短邮箱不应该脱敏");

        // 测试null值
        assertNull(UserProfileResponse.maskEmail(null), "null值应该返回null");

        // 测试无效邮箱
        assertEquals("invalid-email", 
                UserProfileResponse.maskEmail("invalid-email"), 
                "无效邮箱应该原样返回");
    }

    /**
     * 测试手机号脱敏功能
     */
    @Test
    @DisplayName("测试手机号脱敏功能")
    void testPhoneMasking() {
        // 测试正常手机号
        assertEquals("138****8000", 
                UserProfileResponse.maskPhone("***********"), 
                "手机号脱敏应该正确");

        // 测试非11位手机号
        assertEquals("12345", 
                UserProfileResponse.maskPhone("12345"), 
                "非11位手机号应该原样返回");

        // 测试null值
        assertNull(UserProfileResponse.maskPhone(null), "null值应该返回null");
    }

    // ======================== 收藏管理相关测试 ========================

    /**
     * 测试获取用户收藏列表 - 成功场景
     */
    @Test
    @DisplayName("获取用户收藏列表 - 成功")
    void testGetUserFavorites_Success() {
        // Given: 准备测试数据
        Long current = 1L;
        Long size = 10L;
        Byte targetType = 1; // 模板收藏

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // 准备收藏数据
        UserFavorites favorite1 = new UserFavorites();
        favorite1.setId(1L);
        favorite1.setUserId(TEST_USER_ID);
        favorite1.setTargetType((byte) 1);
        favorite1.setTargetId(3001L);
        favorite1.setCreateTime(LocalDateTime.now());

        UserFavorites favorite2 = new UserFavorites();
        favorite2.setId(2L);
        favorite2.setUserId(TEST_USER_ID);
        favorite2.setTargetType((byte) 1);
        favorite2.setTargetId(3002L);
        favorite2.setCreateTime(LocalDateTime.now().minusHours(1));

        List<UserFavorites> favoriteList = Arrays.asList(favorite1, favorite2);

        // 模拟分页查询结果
        Page<UserFavorites> favoritePage = new Page<>();
        favoritePage.setRecords(favoriteList);
        favoritePage.setTotal(2L);
        favoritePage.setCurrent(current);
        favoritePage.setSize(size);
        favoritePage.setPages(1L);

        when(userFavoritesService.page(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(favoritePage);

        // 模拟模板数据
        ResumeTemplates template1 = new ResumeTemplates();
        template1.setId(3001L);
        template1.setName("简约商务模板");
        template1.setDescription("适合商务人士");
        template1.setPreviewImageUrl("https://example.com/preview1.jpg");
        template1.setIsPremium((byte) 0);
        template1.setIsDeleted((byte) 0);

        ResumeTemplates template2 = new ResumeTemplates();
        template2.setId(3002L);
        template2.setName("创意设计模板");
        template2.setDescription("适合设计师");
        template2.setPreviewImageUrl("https://example.com/preview2.jpg");
        template2.setIsPremium((byte) 1);
        template2.setIsDeleted((byte) 0);

        when(resumeTemplatesService.getById(3001L)).thenReturn(template1);
        when(resumeTemplatesService.getById(3002L)).thenReturn(template2);

        // When: 调用获取收藏列表方法
        Page<UserFavoriteResponse> result = usersService.getUserFavorites(TEST_USER_ID, targetType, current, size);

        // Then: 验证结果
        assertNotNull(result, "结果不应为空");
        assertEquals(2L, result.getTotal(), "总数应该为2");
        assertEquals(2, result.getRecords().size(), "记录数应该为2");

        // 验证第一个收藏
        UserFavoriteResponse response1 = result.getRecords().get(0);
        assertEquals(1L, response1.getId(), "收藏ID应该匹配");
        assertEquals((byte) 1, response1.getTargetType(), "收藏类型应该为模板");
        assertEquals(3001L, response1.getTargetId(), "目标ID应该匹配");
        assertEquals("简约商务模板", response1.getTargetName(), "模板名称应该匹配");
        assertFalse(response1.getIsPremium(), "应该不是付费模板");

        // 验证第二个收藏
        UserFavoriteResponse response2 = result.getRecords().get(1);
        assertEquals(2L, response2.getId(), "收藏ID应该匹配");
        assertEquals("创意设计模板", response2.getTargetName(), "模板名称应该匹配");
        assertTrue(response2.getIsPremium(), "应该是付费模板");

        // 验证方法调用
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(userFavoritesService, times(1)).page(any(Page.class), any(LambdaQueryWrapper.class));
        verify(resumeTemplatesService, times(1)).getById(3001L);
        verify(resumeTemplatesService, times(1)).getById(3002L);
    }

    /**
     * 测试获取用户收藏列表 - 用户不存在
     */
    @Test
    @DisplayName("获取用户收藏列表 - 用户不存在")
    void testGetUserFavorites_UserNotFound() {
        // Given: 模拟用户不存在
        doReturn(null).when(usersService).getById(TEST_USER_ID);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.getUserFavorites(TEST_USER_ID, null, 1L, 10L),
                "应该抛出用户不存在异常");

        assertEquals("用户不存在", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(userFavoritesService, never()).page(any(Page.class), any(LambdaQueryWrapper.class));
    }

    /**
     * 测试添加收藏 - 成功场景（模板收藏）
     */
    @Test
    @DisplayName("添加收藏 - 成功（模板收藏）")
    void testAddFavorite_Template_Success() {
        // Given: 准备测试数据
        UserFavoriteRequest request = new UserFavoriteRequest();
        request.setTargetType((byte) 1); // 模板收藏
        request.setTargetId(3001L);

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // 模拟模板存在
        ResumeTemplates template = new ResumeTemplates();
        template.setId(3001L);
        template.setName("简约商务模板");
        template.setIsDeleted((byte) 0);
        when(resumeTemplatesService.getById(3001L)).thenReturn(template);

        // 模拟没有重复收藏
        when(userFavoritesService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 模拟保存成功
        when(userFavoritesService.save(any(UserFavorites.class))).thenReturn(true);

        // When: 调用添加收藏方法
        boolean result = usersService.addFavorite(TEST_USER_ID, request);

        // Then: 验证结果
        assertTrue(result, "添加收藏应该成功");

        // 验证方法调用
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(resumeTemplatesService, times(1)).getById(3001L);
        verify(userFavoritesService, times(1)).getOne(any(LambdaQueryWrapper.class));
        verify(userFavoritesService, times(1)).save(any(UserFavorites.class));
    }

    /**
     * 测试添加收藏 - 用户不存在
     */
    @Test
    @DisplayName("添加收藏 - 用户不存在")
    void testAddFavorite_UserNotFound() {
        // Given: 准备测试数据
        UserFavoriteRequest request = new UserFavoriteRequest();
        request.setTargetType((byte) 1);
        request.setTargetId(3001L);

        // 模拟用户不存在
        doReturn(null).when(usersService).getById(TEST_USER_ID);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.addFavorite(TEST_USER_ID, request),
                "应该抛出用户不存在异常");

        assertEquals("用户不存在", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(resumeTemplatesService, never()).getById(any());
        verify(userFavoritesService, never()).save(any(UserFavorites.class));
    }

    /**
     * 测试添加收藏 - 收藏类型无效
     */
    @Test
    @DisplayName("添加收藏 - 收藏类型无效")
    void testAddFavorite_InvalidTargetType() {
        // Given: 准备测试数据
        UserFavoriteRequest request = new UserFavoriteRequest();
        request.setTargetType((byte) 3); // 无效类型
        request.setTargetId(3001L);

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.addFavorite(TEST_USER_ID, request),
                "应该抛出收藏类型无效异常");

        assertEquals("收藏类型无效", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(userFavoritesService, never()).save(any(UserFavorites.class));
    }

    /**
     * 测试添加收藏 - 模板不存在
     */
    @Test
    @DisplayName("添加收藏 - 模板不存在")
    void testAddFavorite_TemplateNotFound() {
        // Given: 准备测试数据
        UserFavoriteRequest request = new UserFavoriteRequest();
        request.setTargetType((byte) 1);
        request.setTargetId(3001L);

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // 模拟模板不存在
        when(resumeTemplatesService.getById(3001L)).thenReturn(null);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.addFavorite(TEST_USER_ID, request),
                "应该抛出模板不存在异常");

        assertEquals("模板不存在或已删除", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(resumeTemplatesService, times(1)).getById(3001L);
        verify(userFavoritesService, never()).save(any(UserFavorites.class));
    }

    /**
     * 测试添加收藏 - 已经收藏过
     */
    @Test
    @DisplayName("添加收藏 - 已经收藏过")
    void testAddFavorite_AlreadyFavorited() {
        // Given: 准备测试数据
        UserFavoriteRequest request = new UserFavoriteRequest();
        request.setTargetType((byte) 1);
        request.setTargetId(3001L);

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // 模拟模板存在
        ResumeTemplates template = new ResumeTemplates();
        template.setId(3001L);
        template.setIsDeleted((byte) 0);
        when(resumeTemplatesService.getById(3001L)).thenReturn(template);

        // 模拟已经收藏过
        UserFavorites existFavorite = new UserFavorites();
        existFavorite.setId(1L);
        existFavorite.setUserId(TEST_USER_ID);
        existFavorite.setTargetType((byte) 1);
        existFavorite.setTargetId(3001L);
        when(userFavoritesService.getOne(any(LambdaQueryWrapper.class))).thenReturn(existFavorite);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.addFavorite(TEST_USER_ID, request),
                "应该抛出已收藏异常");

        assertEquals("已经收藏过该内容", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(resumeTemplatesService, times(1)).getById(3001L);
        verify(userFavoritesService, times(1)).getOne(any(LambdaQueryWrapper.class));
        verify(userFavoritesService, never()).save(any(UserFavorites.class));
    }

    /**
     * 测试取消收藏 - 成功场景
     */
    @Test
    @DisplayName("取消收藏 - 成功")
    void testRemoveFavorite_Success() {
        // Given: 准备测试数据
        Long favoriteId = 1L;

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // 模拟收藏记录存在
        UserFavorites favorite = new UserFavorites();
        favorite.setId(favoriteId);
        favorite.setUserId(TEST_USER_ID);
        favorite.setTargetType((byte) 1);
        favorite.setTargetId(3001L);
        favorite.setIsDeleted((byte) 0);
        when(userFavoritesService.getById(favoriteId)).thenReturn(favorite);

        // 模拟更新成功
        when(userFavoritesService.updateById(any(UserFavorites.class))).thenReturn(true);

        // When: 调用取消收藏方法
        boolean result = usersService.removeFavorite(TEST_USER_ID, favoriteId);

        // Then: 验证结果
        assertTrue(result, "取消收藏应该成功");

        // 验证方法调用
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(userFavoritesService, times(1)).getById(favoriteId);
        verify(userFavoritesService, times(1)).updateById(any(UserFavorites.class));
    }

    /**
     * 测试取消收藏 - 用户不存在
     */
    @Test
    @DisplayName("取消收藏 - 用户不存在")
    void testRemoveFavorite_UserNotFound() {
        // Given: 准备测试数据
        Long favoriteId = 1L;

        // 模拟用户不存在
        doReturn(null).when(usersService).getById(TEST_USER_ID);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.removeFavorite(TEST_USER_ID, favoriteId),
                "应该抛出用户不存在异常");

        assertEquals("用户不存在", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(userFavoritesService, never()).getById(any());
        verify(userFavoritesService, never()).updateById(any(UserFavorites.class));
    }

    /**
     * 测试取消收藏 - 收藏记录不存在
     */
    @Test
    @DisplayName("取消收藏 - 收藏记录不存在")
    void testRemoveFavorite_FavoriteNotFound() {
        // Given: 准备测试数据
        Long favoriteId = 1L;

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // 模拟收藏记录不存在
        when(userFavoritesService.getById(favoriteId)).thenReturn(null);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.removeFavorite(TEST_USER_ID, favoriteId),
                "应该抛出收藏记录不存在异常");

        assertEquals("收藏记录不存在", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(userFavoritesService, times(1)).getById(favoriteId);
        verify(userFavoritesService, never()).updateById(any(UserFavorites.class));
    }

    /**
     * 测试取消收藏 - 无权限删除（不是自己的收藏）
     */
    @Test
    @DisplayName("取消收藏 - 无权限删除")
    void testRemoveFavorite_NoPermission() {
        // Given: 准备测试数据
        Long favoriteId = 1L;
        Long otherUserId = 2002L; // 其他用户ID

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // 模拟收藏记录存在但属于其他用户
        UserFavorites favorite = new UserFavorites();
        favorite.setId(favoriteId);
        favorite.setUserId(otherUserId); // 属于其他用户
        favorite.setTargetType((byte) 1);
        favorite.setTargetId(3001L);
        favorite.setIsDeleted((byte) 0);
        when(userFavoritesService.getById(favoriteId)).thenReturn(favorite);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.removeFavorite(TEST_USER_ID, favoriteId),
                "应该抛出无权限异常");

        assertEquals("无权限删除该收藏", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(userFavoritesService, times(1)).getById(favoriteId);
        verify(userFavoritesService, never()).updateById(any(UserFavorites.class));
    }

    /**
     * 测试取消收藏 - 收藏记录已删除
     */
    @Test
    @DisplayName("取消收藏 - 收藏记录已删除")
    void testRemoveFavorite_AlreadyDeleted() {
        // Given: 准备测试数据
        Long favoriteId = 1L;

        // 模拟用户存在
        doReturn(testUser).when(usersService).getById(TEST_USER_ID);

        // 模拟收藏记录已删除
        UserFavorites favorite = new UserFavorites();
        favorite.setId(favoriteId);
        favorite.setUserId(TEST_USER_ID);
        favorite.setTargetType((byte) 1);
        favorite.setTargetId(3001L);
        favorite.setIsDeleted((byte) 1); // 已删除
        when(userFavoritesService.getById(favoriteId)).thenReturn(favorite);

        // When & Then: 调用方法应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> usersService.removeFavorite(TEST_USER_ID, favoriteId),
                "应该抛出收藏记录不存在异常");

        assertEquals("收藏记录不存在", exception.getMessage(), "异常消息应该匹配");
        verify(usersService, times(1)).getById(TEST_USER_ID);
        verify(userFavoritesService, times(1)).getById(favoriteId);
        verify(userFavoritesService, never()).updateById(any(UserFavorites.class));
    }
} 