package com.alan6.resume.service.impl;

import com.alan6.resume.entity.Permissions;
import com.alan6.resume.mapper.PermissionsMapper;
import com.alan6.resume.service.IPermissionsService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 权限服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class PermissionsServiceImpl extends ServiceImpl<PermissionsMapper, Permissions> implements IPermissionsService {

    @Autowired
    private PermissionsMapper permissionsMapper;

    @Override
    public List<Permissions> getPermissionsByUserId(Long userId) {
        return permissionsMapper.selectPermissionsByUserId(userId);
    }

    @Override
    public List<Permissions> getPermissionsByRoleId(Long roleId) {
        return permissionsMapper.selectPermissionsByRoleId(roleId);
    }

    @Override
    public Permissions getPermissionByCode(String permissionCode) {
        QueryWrapper<Permissions> wrapper = new QueryWrapper<>();
        wrapper.eq("permission_code", permissionCode);
        return this.getOne(wrapper);
    }

    @Override
    public List<Permissions> getMenuPermissions() {
        QueryWrapper<Permissions> wrapper = new QueryWrapper<>();
        wrapper.eq("type", "menu")
                .orderByAsc("sort_order");
        return this.list(wrapper);
    }

    @Override
    public List<Permissions> getMenuPermissionsByUserId(Long userId) {
        return permissionsMapper.selectMenuPermissionsByUserId(userId);
    }

    @Override
    public boolean createPermission(Permissions permission) {
        return this.save(permission);
    }

    @Override
    public boolean updatePermission(Permissions permission) {
        return this.updateById(permission);
    }

    @Override
    public boolean deletePermission(Long permissionId) {
        return this.removeById(permissionId);
    }

    @Override
    public boolean existsPermissionCode(String permissionCode, Long excludeId) {
        QueryWrapper<Permissions> wrapper = new QueryWrapper<>();
        wrapper.eq("permission_code", permissionCode);
        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }
        return this.count(wrapper) > 0;
    }
} 