package com.alan6.resume.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统配置响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "SystemConfigResponse", description = "系统配置响应")
public class SystemConfigResponse {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long id;

    /**
     * 配置键
     */
    @Schema(description = "配置键", example = "system.file.max_size")
    private String configKey;

    /**
     * 配置值
     */
    @Schema(description = "配置值", example = "52428800")
    private String configValue;

    /**
     * 配置类型
     */
    @Schema(description = "配置类型", example = "number")
    private String configType;

    /**
     * 配置分组
     */
    @Schema(description = "配置分组", example = "file")
    private String configGroup;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "文件上传最大大小限制（字节）")
    private String description;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2024-01-01 00:00:00")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2024-01-01 00:00:00")
    private LocalDateTime updateTime;
} 