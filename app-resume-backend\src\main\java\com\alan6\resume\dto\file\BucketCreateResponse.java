package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 存储桶创建响应DTO
 * 
 * 主要功能：
 * 1. 返回存储桶创建操作的结果
 * 2. 包含创建的存储桶详细信息
 * 3. 提供存储桶配置确认信息
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "存储桶创建响应", description = "存储桶创建操作结果")
public class BucketCreateResponse {

    /**
     * 存储桶名称
     */
    @Schema(description = "存储桶名称", example = "custom-bucket")
    private String name;

    /**
     * 存储桶描述
     */
    @Schema(description = "存储桶描述", example = "自定义存储桶")
    private String description;

    /**
     * 区域
     */
    @Schema(description = "区域", example = "us-east-1")
    private String region;

    /**
     * 是否启用版本控制
     */
    @Schema(description = "是否启用版本控制", example = "true")
    private Boolean versioning;

    /**
     * 加密类型
     */
    @Schema(description = "加密类型", example = "SSE-S3")
    private String encryption;

    /**
     * 存储类别
     */
    @Schema(description = "存储类别", example = "STANDARD")
    private String storageClass;

    /**
     * 访问策略
     */
    @Schema(description = "访问策略", example = "private")
    private String accessPolicy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-22 17:00:00")
    private LocalDateTime createTime;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID", example = "123")
    private Long creatorId;

    /**
     * 存储桶URL
     */
    @Schema(description = "存储桶URL", example = "https://minio.example.com/custom-bucket")
    private String bucketUrl;

    /**
     * 创建状态
     */
    @Schema(description = "创建状态", example = "SUCCESS")
    private String createStatus;

    /**
     * 创建消息
     */
    @Schema(description = "创建消息", example = "存储桶创建成功")
    private String createMessage;

    /**
     * 检查创建操作是否成功
     * 
     * @return true-成功，false-失败
     */
    public boolean isCreateSuccessful() {
        return "SUCCESS".equalsIgnoreCase(createStatus);
    }

    /**
     * 检查是否启用版本控制
     * 
     * @return true-启用，false-未启用
     */
    public boolean isVersioningEnabled() {
        return versioning != null && versioning;
    }

    /**
     * 检查是否为公开访问
     * 
     * @return true-公开访问，false-私有访问
     */
    public boolean isPublicAccess() {
        return accessPolicy != null && 
               (accessPolicy.equals("public-read") || accessPolicy.equals("public-read-write"));
    }

    /**
     * 创建成功的存储桶响应
     * 
     * @param request 创建请求
     * @param bucketUrl 存储桶URL
     * @param creatorId 创建者ID
     * @return 成功的存储桶响应
     */
    public static BucketCreateResponse success(BucketCreateRequest request, String bucketUrl, Long creatorId) {
        return BucketCreateResponse.builder()
                .name(request.getName())
                .description(request.getDescription())
                .region(request.getDefaultRegion())
                .versioning(request.getDefaultVersioning())
                .encryption(request.getDefaultEncryption())
                .storageClass(request.getDefaultStorageClass())
                .accessPolicy(request.getDefaultAccessPolicy())
                .createTime(LocalDateTime.now())
                .creatorId(creatorId)
                .bucketUrl(bucketUrl)
                .createStatus("SUCCESS")
                .createMessage("存储桶创建成功")
                .build();
    }

    /**
     * 创建失败的存储桶响应
     * 
     * @param request 创建请求
     * @param errorMessage 错误消息
     * @param creatorId 创建者ID
     * @return 失败的存储桶响应
     */
    public static BucketCreateResponse failure(BucketCreateRequest request, String errorMessage, Long creatorId) {
        return BucketCreateResponse.builder()
                .name(request.getName())
                .description(request.getDescription())
                .region(request.getDefaultRegion())
                .createTime(LocalDateTime.now())
                .creatorId(creatorId)
                .createStatus("FAILED")
                .createMessage(errorMessage)
                .build();
    }
} 