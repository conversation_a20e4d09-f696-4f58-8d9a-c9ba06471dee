<template>
  <div class="basic-info-form">
    <div class="form-header">
      <h4 class="form-title">基本信息</h4>
      <p class="form-desc">完善您的个人基本信息</p>
    </div>
    
    <div class="form-content">
      <!-- 照片上传 -->
      <div class="form-group photo-group">
        <label class="form-label">照片</label>
        <div class="photo-upload">
          <div class="photo-preview" @click="handlePhotoClick">
            <img v-if="formData.photo" :src="formData.photo" alt="照片" class="photo-img" />
            <div v-else class="photo-placeholder">
              <Icon name="image" size="lg" />
              <span>点击上传照片</span>
            </div>
          </div>
          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handlePhotoUpload"
          />
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">姓名</label>
          <input
            v-model="formData.name"
            type="text"
            class="form-input"
            placeholder="请输入您的姓名"
            @input="handleInputChange"
          />
        </div>
        <div class="form-group">
          <label class="form-label">年龄</label>
          <input
            v-model="formData.age"
            type="number"
            class="form-input"
            placeholder="请输入年龄"
            @input="handleInputChange"
          />
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">手机号</label>
        <input
          v-model="formData.phone"
          type="tel"
          class="form-input"
          placeholder="请输入手机号"
          @input="handleInputChange"
        />
      </div>

      <div class="form-group">
        <label class="form-label">邮箱</label>
        <input
          v-model="formData.email"
          type="email"
          class="form-input"
          placeholder="请输入邮箱地址"
          @input="handleInputChange"
        />
      </div>

      <div class="form-row">
        <div class="form-group">
          <label class="form-label">所在城市</label>
          <input
            v-model="formData.currentCity"
            type="text"
            class="form-input"
            placeholder="如：北京"
            @input="handleInputChange"
          />
        </div>
        <div class="form-group">
          <label class="form-label">意向城市</label>
          <input
            v-model="formData.intendedCity"
            type="text"
            class="form-input"
            placeholder="如：北京"
            @input="handleInputChange"
          />
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">居住地址</label>
        <input
          v-model="formData.address"
          type="text"
          class="form-input"
          placeholder="如：北京市朝阳区***路***号**小区"
          @input="handleInputChange"
        />
      </div>

      <div class="form-group">
        <label class="form-label">当前状态</label>
        <input
          v-model="formData.currentStatus"
          type="text"
          class="form-input"
          placeholder="如：已离职-随时到岗"
          @input="handleInputChange"
        />
      </div>

      <div class="form-group">
        <label class="form-label">期望岗位</label>
        <input
          v-model="formData.expectedPosition"
          type="text"
          class="form-input"
          placeholder="如：销售总监"
          @input="handleInputChange"
        />
      </div>

      <div class="form-group">
        <label class="form-label">期望薪资</label>
        <input
          v-model="formData.expectedSalary"
          type="text"
          class="form-input"
          placeholder="如：面议"
          @input="handleInputChange"
        />
      </div>

      <!-- 社交信息 -->
      <div class="section-divider">
        <h5 class="section-title">社交信息</h5>
        <div class="section-actions">
          <button type="button" class="collapse-btn" @click="showSocialOptions = !showSocialOptions">
            {{ showSocialOptions ? '收起' : '展开' }}
          </button>
        </div>
      </div>

      <!-- 社交信息选择器 -->
      <div v-if="showSocialOptions" class="field-selector">
        <div class="field-options">
          <button
            v-for="option in socialOptions"
            :key="option.key"
            type="button"
            class="option-btn"
            :class="{ 
              active: activeSocialFields.includes(option.key) && option.key !== 'custom',
              'custom-btn': option.key === 'custom'
            }"
            @click.stop="handleSocialFieldClick(option.key)"
          >
            <span class="option-icon">{{ getOptionIcon(option.key, 'social') }}</span>
            {{ option.label }}
          </button>
        </div>
      </div>

      <!-- 已添加的社交信息字段 -->
      <div v-if="activeSocialFields.length > 0 || customSocialFields.length > 0" class="optional-section">
        <!-- 标准字段 -->
        <div v-if="getActiveSocialStandardFields().length > 0">
          <div
            v-for="field in getActiveSocialStandardFields()"
            :key="field"
            class="form-row"
          >
            <div class="form-group-horizontal">
              <label class="form-label-horizontal">{{ getSocialFieldLabel(field) }}</label>
              <input
                v-model="formData[field]"
                :type="getSocialFieldType(field)"
                class="form-input"
                :placeholder="getSocialFieldPlaceholder(field)"
                @input="handleInputChange"
              />
            </div>
            <button
              type="button"
              class="delete-field-btn"
              @click="removeSocialField(field)"
              title="删除此字段"
            >
              ×
            </button>
          </div>
        </div>

        <!-- 自定义字段 -->
        <div v-if="customSocialFields.length > 0" class="custom-fields-section">
          <div class="form-row" v-for="(customField, index) in customSocialFields" :key="customField.id">
            <div class="form-group">
              <label class="form-label">标签名称</label>
              <input
                v-model="customField.label"
                type="text"
                class="form-input"
                placeholder="如：微博"
                @input="updateCustomSocialFields"
              />
            </div>
            <div class="form-group">
              <label class="form-label">内容</label>
              <input
                v-model="customField.value"
                type="text"
                class="form-input"
                placeholder="请输入内容"
                @input="updateCustomSocialFields"
              />
            </div>
            <button
              type="button"
              class="delete-field-btn"
              @click="removeCustomSocialField(index)"
              title="删除此字段"
            >
              ×
            </button>
          </div>
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="section-divider">
        <h5 class="section-title">其他信息</h5>
        <div class="section-actions">
          <button type="button" class="collapse-btn" @click="showOtherOptions = !showOtherOptions">
            {{ showOtherOptions ? '收起' : '展开' }}
          </button>
        </div>
      </div>

      <!-- 其他信息选择器 -->
      <div v-if="showOtherOptions" class="field-selector">
        <div class="field-options">
          <button
            v-for="option in otherOptions"
            :key="option.key"
            type="button"
            class="option-btn"
            :class="{ 
              active: activeOtherFields.includes(option.key) && option.key !== 'custom',
              'custom-btn': option.key === 'custom'
            }"
            @click.stop="handleOtherFieldClick(option.key)"
          >
            <span class="option-icon">{{ getOptionIcon(option.key, 'other') }}</span>
            {{ option.label }}
          </button>
        </div>
      </div>

      <!-- 已添加的其他信息字段 -->
      <div v-if="activeOtherFields.length > 0 || customOtherFields.length > 0" class="optional-section">
        <!-- 标准字段 -->
        <div v-if="getActiveOtherStandardFields().length > 0">
          <div
            v-for="field in getActiveOtherStandardFields()"
            :key="field"
            class="form-row"
          >
            <div class="form-group-horizontal">
              <label class="form-label-horizontal">{{ getOtherFieldLabel(field) }}</label>
              <select
                v-if="getOtherFieldType(field) === 'select'"
                v-model="formData[field]"
                class="form-select"
                @change="handleInputChange"
              >
                <option value="">请选择</option>
                <option
                  v-for="option in getOtherFieldOptions(field)"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
              <input
                v-else
                v-model="formData[field]"
                :type="getOtherFieldType(field)"
                class="form-input"
                :placeholder="getOtherFieldPlaceholder(field)"
                @input="handleInputChange"
              />
            </div>
            <button
              type="button"
              class="delete-field-btn"
              @click="removeOtherField(field)"
              title="删除此字段"
            >
              ×
            </button>
          </div>
        </div>

        <!-- 自定义字段 -->
        <div v-if="customOtherFields.length > 0" class="custom-fields-section">
          <div class="form-row" v-for="(customField, index) in customOtherFields" :key="customField.id">
            <div class="form-group">
              <label class="form-label">标签名称</label>
              <input
                v-model="customField.label"
                type="text"
                class="form-input"
                placeholder="如：特长"
                @input="updateCustomOtherFields"
              />
            </div>
            <div class="form-group">
              <label class="form-label">内容</label>
              <input
                v-model="customField.value"
                type="text"
                class="form-input"
                placeholder="请输入内容"
                @input="updateCustomOtherFields"
              />
            </div>
            <button
              type="button"
              class="delete-field-btn"
              @click="removeCustomOtherField(index)"
              title="删除此字段"
            >
              ×
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import Icon from '~/components/common/Icon.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update'])

const fileInput = ref(null)
const showSocialOptions = ref(true) // 默认展开
const showOtherOptions = ref(true) // 默认展开
const activeSocialFields = ref([])
const activeOtherFields = ref([])
const customSocialFields = ref([])
const customOtherFields = ref([])

// 社交信息选项
const socialOptions = [
  { key: 'wechat', label: '微信号' },
  { key: 'website', label: '个人网站' },
  { key: 'github', label: 'GitHub' },
  { key: 'custom', label: '自定义' }
]

// 其他信息选项
const otherOptions = [
  { key: 'gender', label: '性别' },
  { key: 'height', label: '身高' },
  { key: 'weight', label: '体重' },
  { key: 'politicalStatus', label: '政治面貌' },
  { key: 'maritalStatus', label: '婚姻状况' },
  { key: 'hometown', label: '籍贯' },
  { key: 'ethnicity', label: '民族' },
  { key: 'custom', label: '自定义' }
]

// 表单数据
const formData = reactive({
  // 基本信息
  photo: '',
  name: '',
  phone: '',
  email: '',
  age: '',
  currentCity: '',
  address: '',
  currentStatus: '',
  intendedCity: '',
  expectedPosition: '',
  expectedSalary: '',
  
  // 社交信息
  wechat: '',
  website: '',
  github: '',
  
  // 其他信息
  gender: '',
  height: '',
  weight: '',
  politicalStatus: '',
  maritalStatus: '',
  hometown: '',
  ethnicity: '',
  
  // 自定义字段数组
  customSocials: [],
  customInfos: []
})

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData, {
      photo: '',
      name: '',
      phone: '',
      email: '',
      age: '',
      currentCity: '',
      address: '',
      currentStatus: '',
      intendedCity: '',
      expectedPosition: '',
      expectedSalary: '',
      wechat: '',
      website: '',
      github: '',
      gender: '',
      height: '',
      weight: '',
      politicalStatus: '',
      maritalStatus: '',
      hometown: '',
      ethnicity: '',
      customSocials: [],
      customInfos: [],
      ...newData
    })
    
    // 同步自定义字段
    if (newData.customSocials) {
      customSocialFields.value = [...newData.customSocials]
    }
    if (newData.customInfos) {
      customOtherFields.value = [...newData.customInfos]
    }
    
    // 同步已选择的标准字段 - 检查字段是否有值或者在activeSocialFields中
    activeSocialFields.value = socialOptions
      .filter(option => {
        if (option.key === 'custom') return false
        // 如果字段有值，或者当前已经在activeSocialFields中，则保留
        return (newData[option.key] && newData[option.key].trim() !== '') || 
               activeSocialFields.value.includes(option.key)
      })
      .map(option => option.key)
    
    activeOtherFields.value = otherOptions
      .filter(option => {
        if (option.key === 'custom') return false
        // 如果字段有值，或者当前已经在activeOtherFields中，则保留
        return (newData[option.key] && newData[option.key].trim() !== '') || 
               activeOtherFields.value.includes(option.key)
      })
      .map(option => option.key)
  }
}, { immediate: true, deep: true })

// 处理输入变化
const handleInputChange = () => {
  console.log('📝 BasicInfoForm - 发射更新事件:', { ...formData })
  emit('update', { ...formData })
}

// 处理照片点击
const handlePhotoClick = () => {
  fileInput.value?.click()
}

// 处理照片上传
const handlePhotoUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.photo = e.target.result
      handleInputChange()
    }
    reader.readAsDataURL(file)
  }
}

// 获取图标
const getOptionIcon = (field, type) => {
  if (field === 'custom') {
    return '+'
  }
  
  if (type === 'social') {
    return activeSocialFields.value.includes(field) ? '✓' : '+'
  } else {
    return activeOtherFields.value.includes(field) ? '✓' : '+'
  }
}

// 社交信息字段管理
const handleSocialFieldClick = (field) => {
  if (field === 'custom') {
    addCustomSocialField()
  } else {
    toggleSocialField(field)
  }
}

const toggleSocialField = (field) => {
  const index = activeSocialFields.value.indexOf(field)
  if (index > -1) {
    activeSocialFields.value.splice(index, 1)
    formData[field] = ''
  } else {
    activeSocialFields.value.push(field)
    // 给字段设置一个默认值，这样watch函数就不会重置activeSocialFields
    formData[field] = formData[field] || ''
  }
  handleInputChange()
}

const removeSocialField = (field) => {
  const index = activeSocialFields.value.indexOf(field)
  if (index > -1) {
    activeSocialFields.value.splice(index, 1)
    formData[field] = ''
    handleInputChange()
  }
}

const getActiveSocialStandardFields = () => {
  return activeSocialFields.value.filter(field => field !== 'custom')
}

const getSocialFieldLabel = (field) => {
  return socialOptions.find(opt => opt.key === field)?.label || field
}

const getSocialFieldType = (field) => {
  return ['website', 'github'].includes(field) ? 'url' : 'text'
}

const getSocialFieldPlaceholder = (field) => {
  const placeholders = {
    wechat: '请输入微信号',
    website: '请输入个人网站链接',
    github: '请输入GitHub链接'
  }
  return placeholders[field] || ''
}

// 自定义社交字段管理
const addCustomSocialField = () => {
  if (customSocialFields.value.length >= 3) {
    alert('最多只能自定义3个社交信息')
    return
  }
  
  customSocialFields.value.push({
    id: Date.now(),
    label: '',
    value: ''
  })
  updateCustomSocialFields()
}

const removeCustomSocialField = (index) => {
  customSocialFields.value.splice(index, 1)
  updateCustomSocialFields()
}

const updateCustomSocialFields = () => {
  formData.customSocials = [...customSocialFields.value]
  handleInputChange()
}

// 其他信息字段管理
const handleOtherFieldClick = (field) => {
  if (field === 'custom') {
    addCustomOtherField()
  } else {
    toggleOtherField(field)
  }
}

const toggleOtherField = (field) => {
  const index = activeOtherFields.value.indexOf(field)
  if (index > -1) {
    activeOtherFields.value.splice(index, 1)
    formData[field] = ''
  } else {
    activeOtherFields.value.push(field)
    // 给字段设置一个默认值，这样watch函数就不会重置activeOtherFields
    formData[field] = formData[field] || ''
  }
  handleInputChange()
}

const removeOtherField = (field) => {
  const index = activeOtherFields.value.indexOf(field)
  if (index > -1) {
    activeOtherFields.value.splice(index, 1)
    formData[field] = ''
    handleInputChange()
  }
}

const getActiveOtherStandardFields = () => {
  return activeOtherFields.value.filter(field => field !== 'custom')
}

const getOtherFieldLabel = (field) => {
  return otherOptions.find(opt => opt.key === field)?.label || field
}

const getOtherFieldType = (field) => {
  return ['gender', 'politicalStatus', 'maritalStatus'].includes(field) ? 'select' : 'text'
}

const getOtherFieldOptions = (field) => {
  const options = {
    gender: ['男', '女'],
    politicalStatus: ['中共党员', '中共预备党员', '共青团员', '民主党派', '群众'],
    maritalStatus: ['未婚', '已婚', '离异', '丧偶']
  }
  return options[field] || []
}

const getOtherFieldPlaceholder = (field) => {
  const placeholders = {
    height: '如：175cm',
    weight: '如：65kg',
    hometown: '如：山东省济南市',
    ethnicity: '如：汉族'
  }
  return placeholders[field] || ''
}

// 自定义其他字段管理
const addCustomOtherField = () => {
  if (customOtherFields.value.length >= 3) {
    alert('最多只能自定义3个其他信息')
    return
  }
  
  customOtherFields.value.push({
    id: Date.now(),
    label: '',
    value: ''
  })
  updateCustomOtherFields()
}

const removeCustomOtherField = (index) => {
  customOtherFields.value.splice(index, 1)
  updateCustomOtherFields()
}

const updateCustomOtherFields = () => {
  formData.customInfos = [...customOtherFields.value]
  handleInputChange()
}
</script>

<style scoped>
.basic-info-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.form-content {
  @apply space-y-4;
}

.form-row {
  @apply flex gap-4;
}

.form-group {
  @apply flex flex-col space-y-2 flex-1;
}

.form-group-horizontal {
  @apply flex items-center gap-3 flex-1 py-2;
}

.form-group-with-delete {
  @apply flex items-end gap-2;
}

.form-label {
  @apply text-sm font-medium text-gray-700;
}

.form-label-horizontal {
  @apply text-sm font-medium text-gray-700 min-w-20 flex-shrink-0 text-right;
}

.form-input {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.form-select {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
}

.photo-group {
  @apply items-start;
}

.photo-upload {
  @apply flex items-start;
}

.photo-preview {
  @apply w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 transition-colors duration-200 flex items-center justify-center overflow-hidden;
}

.photo-img {
  @apply w-full h-full object-cover;
}

.photo-placeholder {
  @apply flex flex-col items-center justify-center text-gray-500 text-xs;
}

.section-divider {
  @apply flex items-center justify-between pt-4 mt-6 border-t border-gray-200;
}

.section-title {
  @apply text-base font-medium text-gray-900;
}

.section-actions {
  @apply flex gap-2;
}

.collapse-btn {
  @apply px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors duration-200;
}

.field-selector {
  @apply bg-gray-50 p-3 rounded-md mt-2;
}

.field-options {
  @apply flex flex-wrap gap-2;
}

.option-btn {
  @apply px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 transition-colors duration-200 flex items-center gap-1;
  cursor: pointer;
  pointer-events: auto;
}

.option-btn.active {
  @apply bg-blue-100 border-blue-300 text-blue-700;
}

.option-btn.custom-btn {
  @apply bg-gray-50 border-gray-300 text-gray-700;
}

.option-btn.custom-btn:hover {
  @apply bg-gray-100;
}

.option-icon {
  @apply text-xs font-bold;
}

.delete-field-btn {
  @apply w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 ml-2;
}

.optional-section {
  @apply space-y-3 pt-4;
}

.optional-section > div {
  @apply space-y-3;
}

.custom-fields-section {
  @apply space-y-2;
}

.form-row .form-group-with-delete {
  @apply flex-1;
}
</style> 