package com.alan6.resume.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alan6.resume.common.constants.AuthConstants;
import com.alan6.resume.common.enums.LoginTypeEnum;
import com.alan6.resume.common.utils.RedisUtils;
import com.alan6.resume.common.utils.TokenUtils;
import com.alan6.resume.dto.auth.AdminLoginRequest;
import com.alan6.resume.dto.auth.AdminLoginResponse;
import com.alan6.resume.dto.auth.LoginRequest;
import com.alan6.resume.dto.auth.LoginResponse;
import com.alan6.resume.dto.auth.PasswordLoginRequest;
import com.alan6.resume.dto.auth.RegisterRequest;
import com.alan6.resume.dto.auth.RegisterResponse;
import com.alan6.resume.entity.Users;
import com.alan6.resume.entity.Roles;
import com.alan6.resume.entity.Permissions;
import com.alan6.resume.security.UserPrincipal;
import com.alan6.resume.service.IAuthService;
import com.alan6.resume.service.IUsersService;
import com.alan6.resume.service.IRolesService;
import com.alan6.resume.service.IPermissionsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 认证服务实现类
 * 
 * 主要功能：
 * 1. 实现用户登录的核心逻辑
 * 2. 处理多种登录方式的验证
 * 3. 管理用户Token的生成和存储
 * 4. 实现自动注册新用户功能
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Service
public class AuthServiceImpl implements IAuthService {

    /**
     * 用户服务
     */
    @Autowired
    private IUsersService usersService;

    /**
     * Token工具类
     */
    @Autowired
    private TokenUtils tokenUtils;

    /**
     * Redis工具类
     */
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 角色服务
     */
    @Autowired
    private IRolesService rolesService;

    /**
     * 权限服务
     */
    @Autowired
    private IPermissionsService permissionsService;

    /**
     * 是否允许多设备同时登录
     */
    @Value("${app.auth.allow-multi-device:true}")
    private boolean allowMultiDevice;

    /**
     * 单用户最大Token数量
     */
    @Value("${app.auth.max-tokens-per-user:5}")
    private int maxTokensPerUser;

    /**
     * 用户注册
     * 
     * 处理用户注册的完整流程：
     * 1. 验证注册参数的有效性
     * 2. 检查手机号是否已被注册
     * 3. 验证短信验证码的正确性
     * 4. 创建新用户账户
     * 5. 生成访问Token和刷新Token
     * 6. 返回注册成功响应
     * 
     * @param registerRequest 用户注册请求参数
     * @return 注册响应数据，包含Token和用户信息
     */
    @Override
    public RegisterResponse register(RegisterRequest registerRequest) {
        try {
            log.info("用户注册开始，平台: {}, 手机号: {}", 
                    registerRequest.getPlatform(), registerRequest.getPhone());
            
            // 1. 验证注册参数
            if (!validateRegisterRequest(registerRequest)) {
                throw new RuntimeException("注册参数不完整或格式不正确");
            }
            
            // 2. 检查手机号是否已被注册
            if (isPhoneAlreadyRegistered(registerRequest.getPhone())) {
                throw new RuntimeException("该手机号已被注册，请直接登录或使用其他手机号");
            }
            
            // 3. 验证短信验证码
            if (!verifySmsCode(registerRequest.getPhone(), registerRequest.getVerificationCode())) {
                throw new RuntimeException("验证码错误或已过期，请重新获取");
            }
            
            // 4. 创建新用户
            Users newUser = createNewUser(registerRequest);
            if (newUser == null || newUser.getId() == null) {
                throw new RuntimeException("用户创建失败，请重试");
            }
            
            // 5. 生成访问Token和刷新Token
            String accessToken = tokenUtils.generateToken();
            String refreshToken = tokenUtils.generateToken(); // 使用相同的方法生成刷新Token
            
            // 6. 存储Token到Redis
            String userId = String.valueOf(newUser.getId());
            UserPrincipal userPrincipal = createUserPrincipalWithRoles(newUser);
            boolean storeSuccess = redisUtils.storeUserToken(accessToken, userPrincipal);
            if (!storeSuccess) {
                log.error("注册时Token存储失败，用户ID: {}", newUser.getId());
                throw new RuntimeException("注册失败，请重试");
            }
            
            // 将Token添加到用户Token集合
            redisUtils.addUserToken(userId, accessToken);
            
            // 8. 构建注册响应
            RegisterResponse response = buildRegisterResponse(accessToken, refreshToken, newUser);
            
            log.info("用户注册成功，用户ID: {}, 平台: {}", newUser.getId(), registerRequest.getPlatform());
            
            return response;
            
        } catch (Exception e) {
            log.error("用户注册失败，平台: {}, 手机号: {}, 错误: {}", 
                     registerRequest.getPlatform(), registerRequest.getPhone(), e.getMessage(), e);
            throw new RuntimeException("注册失败：" + e.getMessage());
        }
    }

    /**
     * 用户密码登录
     * 
     * 专门用于已注册用户的密码登录：
     * 1. 验证手机号和密码格式
     * 2. 检查用户是否存在
     * 3. 验证密码正确性
     * 4. 生成Token并返回用户信息
     * 
     * 注意：此方法不会创建新用户，仅用于已注册用户登录
     * 
     * @param passwordLoginRequest 密码登录请求参数
     * @return 登录响应数据
     */
    @Override
    public LoginResponse passwordLogin(PasswordLoginRequest passwordLoginRequest) {
        try {
            log.info("用户密码登录开始，平台: {}, 手机号: {}", 
                    passwordLoginRequest.getPlatform(), passwordLoginRequest.getPhone());
            
            // 参数基础验证
            if (!validatePasswordLoginRequest(passwordLoginRequest)) {
                throw new RuntimeException("登录参数不完整");
            }
            
            // 处理密码登录逻辑
            Users user = processPasswordLogin(passwordLoginRequest);
            
            // 生成Token
            String token = tokenUtils.generateToken();
            
            // 根据配置执行Token管理策略
            String userId = String.valueOf(user.getId());
            
            if (allowMultiDevice) {
                // 多设备模式：检查Token数量限制
                long currentTokenCount = redisUtils.getUserTokenCount(userId);
                if (currentTokenCount >= maxTokensPerUser) {
                    // 删除最旧的Token
                    redisUtils.removeOldestUserToken(userId);
                    log.info("用户Token数量超限，删除最旧Token，用户ID: {}, 当前数量: {}", userId, currentTokenCount);
                }
                
                // 存储新Token
                UserPrincipal userPrincipal = createUserPrincipalWithRoles(user);
                boolean storeSuccess = redisUtils.storeUserToken(token, userPrincipal);
                if (!storeSuccess) {
                    log.error("Token存储失败，用户ID: {}", user.getId());
                    throw new RuntimeException("登录失败，请重试");
                }
                
                // 将Token添加到用户Token集合
                redisUtils.addUserToken(userId, token);
                
                log.info("多设备密码登录成功，用户ID: {}, Token数量: {}", userId, currentTokenCount + 1);
                
            } else {
                // 单设备模式：清理所有旧Token
                redisUtils.cleanAllUserTokens(userId);
                
                // 存储新Token
                UserPrincipal userPrincipal = createUserPrincipalWithRoles(user);
                boolean storeSuccess = redisUtils.storeUserToken(token, userPrincipal);
                if (!storeSuccess) {
                    log.error("Token存储失败，用户ID: {}", user.getId());
                    throw new RuntimeException("登录失败，请重试");
                }
                
                // 将Token添加到用户Token集合
                redisUtils.addUserToken(userId, token);
                
                log.info("单设备密码登录成功，用户ID: {}", userId);
            }
            
            // 更新用户最后登录信息
            updateUserPasswordLoginInfo(user, passwordLoginRequest);
            
            // 构建登录响应
            LoginResponse response = buildLoginResponse(token, user);
            
            log.info("用户密码登录成功，用户ID: {}, Token: {}", user.getId(), token);
            
            return response;
            
        } catch (Exception e) {
            log.error("用户密码登录失败，平台: {}, 手机号: {}, 错误: {}", 
                     passwordLoginRequest.getPlatform(), passwordLoginRequest.getPhone(), e.getMessage(), e);
            throw new RuntimeException("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户登录（短信验证码）
     * 
     * 统一登录入口，支持多种登录方式：
     * 1. 手机号+验证码登录
     * 2. 微信授权登录
     * 3. 小程序登录
     * 
     * @param loginRequest 登录请求参数
     * @return 登录响应数据
     */
    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        try {
            log.info("用户登录开始，登录类型: {}, 平台: {}", loginRequest.getLoginType(), loginRequest.getPlatform());
            
            // 参数基础验证
            if (!validateLoginRequest(loginRequest)) {
                throw new RuntimeException("登录参数不完整");
            }
            
            // 根据登录类型处理不同的登录逻辑
            Users user = processLoginByType(loginRequest);
            
            // 生成Token
            String token = tokenUtils.generateToken();
            
            // 根据配置执行Token管理策略
            String userId = String.valueOf(user.getId());
            
            if (allowMultiDevice) {
                // 多设备模式：检查Token数量限制
                long currentTokenCount = redisUtils.getUserTokenCount(userId);
                if (currentTokenCount >= maxTokensPerUser) {
                    // 删除最旧的Token
                    redisUtils.removeOldestUserToken(userId);
                    log.info("用户Token数量超限，删除最旧Token，用户ID: {}, 当前数量: {}", userId, currentTokenCount);
                }
                
                // 存储新Token
                UserPrincipal userPrincipal = createUserPrincipalWithRoles(user);
                boolean storeSuccess = redisUtils.storeUserToken(token, userPrincipal);
                if (!storeSuccess) {
                    log.error("Token存储失败，用户ID: {}", user.getId());
                    throw new RuntimeException("登录失败，请重试");
                }
                
                // 将Token添加到用户Token集合
                redisUtils.addUserToken(userId, token);
                
                log.info("多设备登录成功，用户ID: {}, Token数量: {}", userId, currentTokenCount + 1);
                
            } else {
                // 单设备模式：清理所有旧Token
                redisUtils.cleanAllUserTokens(userId);
                
                // 存储新Token
                UserPrincipal userPrincipal = createUserPrincipalWithRoles(user);
                boolean storeSuccess = redisUtils.storeUserToken(token, userPrincipal);
                if (!storeSuccess) {
                    log.error("Token存储失败，用户ID: {}", user.getId());
                    throw new RuntimeException("登录失败，请重试");
                }
                
                // 将Token添加到用户Token集合
                redisUtils.addUserToken(userId, token);
                
                log.info("单设备登录成功，用户ID: {}", userId);
            }
            
            // 更新用户最后登录信息
            updateUserLoginInfo(user, loginRequest);
            
            // 构建登录响应
            LoginResponse response = buildLoginResponse(token, user);
            
            log.info("用户登录成功，用户ID: {}, Token: {}", user.getId(), token);
            
            return response;
            
        } catch (Exception e) {
            log.error("用户登录失败，登录类型: {}, 平台: {}, 错误: {}", 
                     loginRequest.getLoginType(), loginRequest.getPlatform(), e.getMessage(), e);
            throw new RuntimeException("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     * 
     * @param token 用户Token
     * @return true-登出成功，false-登出失败
     */
    @Override
    public boolean logout(String token) {
        try {
            log.info("用户登出，Token: {}", token);
            
            // 从SecurityContext获取当前认证的用户ID（避免重复Redis调用）
            String userId = getCurrentUserId();
            
            // 如果无法从SecurityContext获取，则尝试从Redis获取
            if (userId == null) {
                Object userInfo = redisUtils.getUserByToken(token);
                if (userInfo != null) {
                    userId = extractUserIdFromUserInfo(userInfo);
                }
            }
            
            // 从用户Token集合中移除Token
            if (userId != null) {
                redisUtils.removeUserTokenFromSet(userId, token);
            }
            
            // 从Redis删除Token
            boolean success = redisUtils.removeUserToken(token);
            
            if (success) {
                log.info("用户登出成功，Token: {}", token);
            } else {
                log.warn("用户登出失败，Token可能已失效: {}", token);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("用户登出异常，Token: {}", token, e);
            return false;
        }
    }

    /**
     * 刷新Token
     * 
     * @param token 当前Token
     * @return true-刷新成功，false-刷新失败
     */
    @Override
    public boolean refreshToken(String token) {
        try {
            log.debug("刷新Token，Token: {}", token);
            
            // 刷新Token过期时间
            boolean success = redisUtils.refreshTokenExpire(token);
            
            if (success) {
                log.info("Token刷新成功，Token: {}", token);
            } else {
                log.warn("Token刷新失败，Token: {}", token);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("Token刷新异常，Token: {}", token, e);
            return false;
        }
    }

    /**
     * 验证Token有效性
     * 
     * @param token Token字符串
     * @return true-有效，false-无效
     */
    @Override
    public boolean validateToken(String token) {
        try {
            // 检查Token格式
            if (!tokenUtils.isValidTokenFormat(token)) {
                return false;
            }
            
            // 检查Token是否在Redis中存在
            return redisUtils.isTokenValid(token);
            
        } catch (Exception e) {
            log.error("Token验证异常，Token: {}", token, e);
            return false;
        }
    }

    /**
     * 发送短信验证码
     * 
     * @param phone 手机号
     * @param platform 平台标识
     * @return true-发送成功，false-发送失败
     */
    @Override
    public boolean sendSmsCode(String phone, String platform) {
        try {
            log.info("发送短信验证码，手机号: {}, 平台: {}", phone, platform);
            
            // 检查发送频率限制
            if (!checkSmsCodeSendLimit(phone)) {
                throw new RuntimeException("验证码发送过于频繁，请稍后再试");
            }
            
            // 生成验证码
            String code = RandomUtil.randomNumbers(AuthConstants.SmsCode.CODE_LENGTH);
            
            // 存储验证码到Redis
            String smsCodeKey = AuthConstants.RedisKey.SMS_CODE_KEY + phone;
            boolean storeSuccess = redisUtils.set(smsCodeKey, code, AuthConstants.SmsCode.CODE_EXPIRE_TIME);
            
            if (!storeSuccess) {
                log.error("验证码存储失败，手机号: {}", phone);
                return false;
            }
            
            // TODO: 调用短信服务发送验证码
            // 这里暂时只是模拟发送，实际需要接入短信服务商
            log.info("短信验证码发送成功，手机号: {}, 验证码: {}", phone, code);
            
            return true;
            
        } catch (Exception e) {
            log.error("发送短信验证码失败，手机号: {}, 平台: {}", phone, platform, e);
            return false;
        }
    }

    /**
     * 验证登录请求参数
     * 
     * @param loginRequest 登录请求
     * @return true-验证通过，false-验证失败
     */
    private boolean validateLoginRequest(LoginRequest loginRequest) {
        // 检查登录类型是否有效
        if (!LoginTypeEnum.isValidCode(loginRequest.getLoginType())) {
            log.warn("无效的登录类型: {}", loginRequest.getLoginType());
            return false;
        }
        
        // 根据登录类型验证相应参数
        LoginTypeEnum loginType = LoginTypeEnum.getByCode(loginRequest.getLoginType());
        switch (loginType) {
            case PHONE:
                return loginRequest.isPhoneLoginValid();
            case WECHAT:
                return loginRequest.isWechatLoginValid();
            case MINIPROGRAM:
                return loginRequest.isMiniProgramLoginValid();
            default:
                return false;
        }
    }

    /**
     * 根据登录类型处理登录逻辑
     * 
     * @param loginRequest 登录请求
     * @return 用户对象
     */
    private Users processLoginByType(LoginRequest loginRequest) {
        LoginTypeEnum loginType = LoginTypeEnum.getByCode(loginRequest.getLoginType());
        
        switch (loginType) {
            case PHONE:
                return processPhoneLogin(loginRequest);
            case WECHAT:
                return processWechatLogin(loginRequest);
            case MINIPROGRAM:
                return processMiniProgramLogin(loginRequest);
            default:
                throw new RuntimeException("不支持的登录类型");
        }
    }

    /**
     * 处理手机号登录
     * 
     * @param loginRequest 登录请求
     * @return 用户对象
     */
    private Users processPhoneLogin(LoginRequest loginRequest) {
        // 验证短信验证码
        if (!verifySmsCode(loginRequest.getPhone(), loginRequest.getSmsCode())) {
            throw new RuntimeException("验证码错误或已过期");
        }
        
        // 查找用户
        Users user = findUserByPhone(loginRequest.getPhone());
        
        // 如果用户不存在，自动注册
        if (user == null) {
            user = createUserByPhone(loginRequest);
            log.info("手机号登录自动注册用户，手机号: {}, 用户ID: {}", loginRequest.getPhone(), user.getId());
        }
        
        return user;
    }

    /**
     * 处理微信授权登录（暂时简化实现）
     * 
     * @param loginRequest 登录请求
     * @return 用户对象
     */
    private Users processWechatLogin(LoginRequest loginRequest) {
        // TODO: 实现微信授权登录逻辑
        // 1. 通过微信授权码获取用户信息
        // 2. 查找或创建用户
        throw new RuntimeException("微信登录功能暂未开放");
    }

    /**
     * 处理小程序登录（暂时简化实现）
     * 
     * @param loginRequest 登录请求
     * @return 用户对象
     */
    private Users processMiniProgramLogin(LoginRequest loginRequest) {
        // TODO: 实现小程序登录逻辑
        // 1. 通过jsCode获取openId
        // 2. 查找或创建用户
        throw new RuntimeException("小程序登录功能暂未开放");
    }

    /**
     * 验证短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return true-验证通过，false-验证失败
     */
    private boolean verifySmsCode(String phone, String code) {
        try {
            String smsCodeKey = AuthConstants.RedisKey.SMS_CODE_KEY + phone;
            Object storedCode = redisUtils.get(smsCodeKey);
            
            if (storedCode == null) {
                log.warn("验证码不存在或已过期，手机号: {}", phone);
                return false;
            }
            
            boolean isValid = storedCode.toString().equals(code);
            
            if (isValid) {
                // 验证成功后删除验证码
                redisUtils.delete(smsCodeKey);
                log.info("短信验证码验证成功，手机号: {}", phone);
            } else {
                log.warn("短信验证码错误，手机号: {}, 输入: {}, 正确: {}", phone, code, storedCode);
            }
            
            return isValid;
            
        } catch (Exception e) {
            log.error("验证短信验证码异常，手机号: {}", phone, e);
            return false;
        }
    }

    /**
     * 根据手机号查找用户
     * 
     * @param phone 手机号
     * @return 用户对象，如果不存在则返回null
     */
    private Users findUserByPhone(String phone) {
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getPhone, phone);
        queryWrapper.eq(Users::getIsDeleted, AuthConstants.UserStatus.NOT_DELETED);
        
        return usersService.getOne(queryWrapper);
    }

    /**
     * 根据手机号创建新用户
     * 
     * @param loginRequest 登录请求
     * @return 新创建的用户对象
     */
    private Users createUserByPhone(LoginRequest loginRequest) {
        Users user = new Users();
        
        // 设置基本信息
        user.setPhone(loginRequest.getPhone());
        user.setUsername(generateUsername(loginRequest.getPhone()));
        user.setNickname("用户" + loginRequest.getPhone().substring(7)); // 使用手机号后4位
        user.setRegisterType((byte) 2); // 手机号注册
        user.setRegisterPlatform(loginRequest.getPlatform());
        user.setStatus(AuthConstants.UserStatus.NORMAL);
        user.setIsPhoneVerified((byte) 1); // 已验证
        user.setPreferredLanguage("zh-CN");
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        // 保存用户
        boolean saveSuccess = usersService.save(user);
        if (!saveSuccess) {
            throw new RuntimeException("用户注册失败");
        }
        
        return user;
    }

    /**
     * 生成用户名
     * 
     * @param phone 手机号
     * @return 生成的用户名
     */
    private String generateUsername(String phone) {
        return "user_" + phone;
    }

    /**
     * 更新用户最后登录信息
     * 
     * @param user 用户对象
     * @param loginRequest 登录请求
     */
    private void updateUserLoginInfo(Users user, LoginRequest loginRequest) {
        try {
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(loginRequest.getClientIp());
            user.setLastLoginPlatform(loginRequest.getPlatform());
            user.setUpdateTime(LocalDateTime.now());
            
            usersService.updateById(user);
            
        } catch (Exception e) {
            log.error("更新用户登录信息失败，用户ID: {}", user.getId(), e);
            // 这里不抛出异常，避免影响登录流程
        }
    }

    /**
     * 构建登录响应对象
     * 
     * @param token Token
     * @param user 用户对象
     * @return 登录响应对象
     */
    private LoginResponse buildLoginResponse(String token, Users user) {
        return LoginResponse.builder()
                .token(token)
                .tokenExpireTime(LocalDateTime.now().plusSeconds(AuthConstants.Token.TOKEN_EXPIRE_TIME))
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatarUrl(user.getAvatarUrl())
                .phone(LoginResponse.maskPhone(user.getPhone()))
                .email(LoginResponse.maskEmail(user.getEmail()))
                .gender(user.getGender())
                .registerPlatform(user.getRegisterPlatform())
                .status(user.getStatus())
                .isPhoneVerified(user.getIsPhoneVerified())
                .preferredLanguage(user.getPreferredLanguage())
                .isNewUser(user.getCreateTime().isAfter(LocalDateTime.now().minusMinutes(1))) // 1分钟内注册的认为是新用户
                .lastLoginTime(user.getLastLoginTime())
                .build();
    }

    /**
     * 检查短信验证码发送频率限制
     * 
     * @param phone 手机号
     * @return true-可以发送，false-发送过于频繁
     */
    private boolean checkSmsCodeSendLimit(String phone) {
        // TODO: 实现发送频率检查逻辑
        // 这里暂时简化实现，实际需要记录发送次数和时间间隔
        return true;
    }

    /**
     * 从SecurityContext获取当前用户ID
     * 
     * @return 用户ID字符串，如果获取失败返回null
     */
    private String getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                Long userId = userPrincipal.getUserId();
                return userId != null ? String.valueOf(userId) : null;
            }
            return null;
        } catch (Exception e) {
            log.error("从SecurityContext获取用户ID失败", e);
            return null;
        }
    }

    /**
     * 验证密码登录请求参数
     * 
     * @param passwordLoginRequest 密码登录请求
     * @return true-参数有效，false-参数无效
     */
    private boolean validatePasswordLoginRequest(PasswordLoginRequest passwordLoginRequest) {
        if (passwordLoginRequest == null) {
            log.warn("密码登录请求为空");
            return false;
        }
        
        if (StrUtil.isBlank(passwordLoginRequest.getPhone())) {
            log.warn("手机号为空");
            return false;
        }
        
        if (StrUtil.isBlank(passwordLoginRequest.getPassword())) {
            log.warn("密码为空");
            return false;
        }
        
        if (StrUtil.isBlank(passwordLoginRequest.getPlatform())) {
            log.warn("登录平台为空");
            return false;
        }
        
        return passwordLoginRequest.isPasswordLoginValid();
    }

    /**
     * 处理密码登录逻辑
     * 
     * @param passwordLoginRequest 密码登录请求
     * @return 用户对象
     */
    private Users processPasswordLogin(PasswordLoginRequest passwordLoginRequest) {
        String phone = passwordLoginRequest.getPhone();
        String password = passwordLoginRequest.getPassword();
        
        // 查找用户
        Users user = findUserByPhone(phone);
        if (user == null) {
            log.warn("用户不存在，手机号: {}", phone);
            throw new RuntimeException("用户不存在，请先注册");
        }
        
        // 检查用户状态
        if (user.getStatus() == null || !user.getStatus().equals(AuthConstants.UserStatus.NORMAL)) {
            log.warn("用户状态异常，手机号: {}, 状态: {}", phone, user.getStatus());
            throw new RuntimeException("账号状态异常，请联系客服");
        }
        
        // 验证密码
        if (!verifyPassword(password, user.getPassword())) {
            log.warn("密码错误，手机号: {}", phone);
            throw new RuntimeException("手机号或密码错误");
        }
        
        log.info("密码验证成功，用户ID: {}", user.getId());
        return user;
    }

    /**
     * 验证密码
     * 
     * @param inputPassword 输入的密码
     * @param storedPassword 存储的密码（加密后）
     * @return true-密码正确，false-密码错误
     */
    private boolean verifyPassword(String inputPassword, String storedPassword) {
        try {
            // 如果存储的密码为空，说明用户还没设置密码
            if (StrUtil.isBlank(storedPassword)) {
                log.warn("用户未设置密码");
                throw new RuntimeException("账号未设置密码，请使用短信验证码登录");
            }
            
            // TODO: 这里应该使用BCrypt或其他加密算法进行密码验证
            // 暂时使用简单的字符串比较，实际项目中需要替换为安全的密码验证
            // 建议使用：BCryptPasswordEncoder.matches(inputPassword, storedPassword)
            return inputPassword.equals(storedPassword);
            
        } catch (Exception e) {
            log.error("密码验证异常", e);
            return false;
        }
    }

    /**
     * 更新用户密码登录信息
     * 
     * @param user 用户对象
     * @param passwordLoginRequest 密码登录请求
     */
    private void updateUserPasswordLoginInfo(Users user, PasswordLoginRequest passwordLoginRequest) {
        try {
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(passwordLoginRequest.getClientIp());
            user.setLastLoginPlatform(passwordLoginRequest.getPlatform());
            user.setUpdateTime(LocalDateTime.now());
            
            usersService.updateById(user);
            
        } catch (Exception e) {
            log.error("更新用户密码登录信息失败，用户ID: {}", user.getId(), e);
            // 这里不抛出异常，避免影响登录流程
        }
    }

    /**
     * 从用户信息对象中提取用户ID
     * 
     * @param userInfo 用户信息对象
     * @return 用户ID字符串
     */
    private String extractUserIdFromUserInfo(Object userInfo) {
        try {
            if (userInfo instanceof Users) {
                return String.valueOf(((Users) userInfo).getId());
            }
            
            // 处理LinkedHashMap类型（Redis反序列化后的数据）
            if (userInfo instanceof Map) {
                Map<?, ?> userMap = (Map<?, ?>) userInfo;
                Object idObj = userMap.get("id");
                if (idObj != null) {
                    return String.valueOf(idObj);
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("提取用户ID失败", e);
            return null;
        }
    }

    /**
     * 验证注册请求参数
     * 
     * @param registerRequest 注册请求参数
     * @return true-参数有效，false-参数无效
     */
    private boolean validateRegisterRequest(RegisterRequest registerRequest) {
        if (registerRequest == null) {
            log.warn("注册请求为空");
            return false;
        }
        
        if (StrUtil.isBlank(registerRequest.getPhone())) {
            log.warn("注册手机号为空");
            return false;
        }
        
        if (StrUtil.isBlank(registerRequest.getPassword())) {
            log.warn("注册密码为空");
            return false;
        }
        
        if (StrUtil.isBlank(registerRequest.getVerificationCode())) {
            log.warn("注册验证码为空");
            return false;
        }
        
        if (StrUtil.isBlank(registerRequest.getPlatform())) {
            log.warn("注册平台为空");
            return false;
        }
        
        if (registerRequest.getAgreeTerms() == null || !registerRequest.getAgreeTerms()) {
            log.warn("用户未同意用户协议");
            return false;
        }
        
        // 验证手机号格式
        if (!registerRequest.getPhone().matches("^1[3-9]\\d{9}$")) {
            log.warn("手机号格式不正确: {}", registerRequest.getPhone());
            return false;
        }
        
        // 验证密码长度
        if (registerRequest.getPassword().length() < 6 || registerRequest.getPassword().length() > 20) {
            log.warn("密码长度不符合要求");
            return false;
        }
        
        // 验证验证码格式
        if (!registerRequest.getVerificationCode().matches("\\d{6}")) {
            log.warn("验证码格式不正确");
            return false;
        }
        
        return true;
    }

    /**
     * 检查手机号是否已被注册
     * 
     * @param phone 手机号
     * @return true-已注册，false-未注册
     */
    private boolean isPhoneAlreadyRegistered(String phone) {
        try {
            Users existingUser = findUserByPhone(phone);
            boolean isRegistered = existingUser != null;
            
            if (isRegistered) {
                log.info("手机号已被注册: {}", phone);
            } else {
                log.info("手机号未被注册，可以使用: {}", phone);
            }
            
            return isRegistered;
            
        } catch (Exception e) {
            log.error("检查手机号注册状态失败: {}", phone, e);
            // 出现异常时，为了安全起见，认为手机号已被注册
            return true;
        }
    }

    /**
     * 创建新用户
     * 
     * @param registerRequest 注册请求参数
     * @return 新创建的用户对象
     */
    private Users createNewUser(RegisterRequest registerRequest) {
        try {
            Users newUser = new Users();
            
            // 设置基本信息
            newUser.setPhone(registerRequest.getPhone());
            newUser.setPassword(registerRequest.getPassword()); // TODO: 在实际项目中需要加密存储
            newUser.setUsername(generateUsername(registerRequest.getPhone()));
            newUser.setNickname(RegisterResponse.maskPhone(registerRequest.getPhone())); // 使用脱敏手机号作为初始昵称
            
            // 设置注册相关信息
            newUser.setRegisterType(registerRequest.getRegisterType().byteValue());
            newUser.setRegisterPlatform(registerRequest.getPlatform());
            
            // 设置状态信息
            newUser.setStatus(AuthConstants.UserStatus.NORMAL);
            newUser.setIsPhoneVerified((byte) 1); // 通过短信验证码注册，默认手机号已验证
            
            // 设置默认值
            newUser.setGender((byte) 0); // 未知
            newUser.setPreferredLanguage("zh-CN"); // 默认中文
            
            // 设置时间戳
            LocalDateTime now = LocalDateTime.now();
            newUser.setCreateTime(now);
            newUser.setUpdateTime(now);
            newUser.setLastLoginTime(now);
            
            // 保存用户到数据库
            boolean saveSuccess = usersService.save(newUser);
            if (!saveSuccess) {
                log.error("保存新用户到数据库失败");
                throw new RuntimeException("用户创建失败");
            }
            
            log.info("新用户创建成功，用户ID: {}, 手机号: {}", newUser.getId(), registerRequest.getPhone());
            
            return newUser;
            
        } catch (Exception e) {
            log.error("创建新用户失败，手机号: {}", registerRequest.getPhone(), e);
            throw new RuntimeException("用户创建失败：" + e.getMessage());
        }
    }

    /**
     * 创建包含角色权限的UserPrincipal
     * 
     * @param user 用户对象
     * @return UserPrincipal对象
     */
    private UserPrincipal createUserPrincipalWithRoles(Users user) {
        try {
            // 获取用户角色
            List<Roles> roles = rolesService.getRolesByUserId(user.getId());
            
            // 获取用户权限
            List<Permissions> permissions = permissionsService.getPermissionsByUserId(user.getId());
            
            // 创建UserPrincipal
            UserPrincipal userPrincipal = UserPrincipal.create(user);
            
            // 转换角色列表为字符串列表
            if (roles != null && !roles.isEmpty()) {
                List<String> roleNames = roles.stream()
                    .map(Roles::getRoleCode)
                    .collect(java.util.stream.Collectors.toList());
                userPrincipal.setRoles(roleNames);
            }
            
            // 转换权限列表为字符串列表
            if (permissions != null && !permissions.isEmpty()) {
                List<String> permissionNames = permissions.stream()
                    .map(Permissions::getPermissionCode)
                    .collect(java.util.stream.Collectors.toList());
                userPrincipal.setPermissions(permissionNames);
            }
            
            return userPrincipal;
        } catch (Exception e) {
            log.error("创建UserPrincipal失败，用户ID: {}", user.getId(), e);
            // 出错时返回不包含角色权限的UserPrincipal
            return UserPrincipal.create(user);
        }
    }

    /**
     * 构建注册响应对象
     * 
     * @param accessToken 访问Token
     * @param refreshToken 刷新Token
     * @param user 用户对象
     * @return 注册响应对象
     */
    private RegisterResponse buildRegisterResponse(String accessToken, String refreshToken, Users user) {
        try {
            // Token过期时间（秒）
            Long expiresIn = AuthConstants.Token.TOKEN_EXPIRE_TIME;
            
            // 构建用户信息
            RegisterResponse.UserInfo userInfo = RegisterResponse.UserInfo.builder()
                    .id(user.getId())
                    .phone(RegisterResponse.maskPhone(user.getPhone()))
                    .username(user.getUsername())
                    .nickname(user.getNickname())
                    .avatar(user.getAvatarUrl())
                    .email(user.getEmail())
                    .membershipLevel(0) // 新用户默认为普通用户
                    .membershipExpireTime(null) // 新用户暂无会员到期时间
                    .registerTime(user.getCreateTime()) // 使用创建时间作为注册时间
                    .registerPlatform(user.getRegisterPlatform())
                    .build();
            
            // 构建注册响应
            return RegisterResponse.builder()
                    .userId(user.getId())
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .expiresIn(expiresIn)
                    .tokenExpireTime(LocalDateTime.now().plusSeconds(expiresIn))
                    .userInfo(userInfo)
                    .build();
                    
        } catch (Exception e) {
            log.error("构建注册响应失败，用户ID: {}", user.getId(), e);
            throw new RuntimeException("构建注册响应失败");
        }
    }

    /**
     * 管理员登录
     * 
     * 专门用于后台管理系统的登录接口
     * 只有具有管理员角色的用户才能登录成功
     * 
     * @param adminLoginRequest 管理员登录请求参数
     * @param clientIp 客户端IP地址
     * @return 管理员登录响应数据，包含权限和菜单信息
     */
    @Override
    public AdminLoginResponse adminLogin(AdminLoginRequest adminLoginRequest, String clientIp) {
        try {
            log.info("管理员登录开始，平台: {}, 手机号: {}, IP: {}", 
                    adminLoginRequest.getPlatform(), adminLoginRequest.getPhone(), clientIp);
            
            // 1. 验证登录参数
            if (!validateAdminLoginRequest(adminLoginRequest)) {
                throw new RuntimeException("登录参数不完整或格式不正确");
            }
            
            // 2. 验证用户存在且密码正确
            Users user = processAdminLogin(adminLoginRequest);
            
            // 3. 验证用户是否具有管理员权限
            if (!isUserAdmin(user)) {
                throw new RuntimeException("权限不足，您不是管理员用户");
            }
            
            // 4. 生成Token
            String token = tokenUtils.generateToken();
            
            // 5. 存储Token到Redis
            UserPrincipal userPrincipal = createUserPrincipalWithRoles(user);
            boolean storeSuccess = redisUtils.storeUserToken(token, userPrincipal);
            if (!storeSuccess) {
                log.error("管理员登录Token存储失败，用户ID: {}", user.getId());
                throw new RuntimeException("登录失败，请重试");
            }
            
            // 6. 更新用户登录信息
            updateUserAdminLoginInfo(user, adminLoginRequest, clientIp);
            
            // 7. 构建管理员登录响应
            AdminLoginResponse response = buildAdminLoginResponse(token, user, userPrincipal);
            
            log.info("管理员登录成功，用户ID: {}, 用户名: {}, 角色: {}", 
                    user.getId(), user.getUsername(), userPrincipal.getRoles());
            
            return response;
            
        } catch (Exception e) {
            log.error("管理员登录失败，平台: {}, 手机号: {}, IP: {}, 错误: {}", 
                     adminLoginRequest.getPlatform(), adminLoginRequest.getPhone(), clientIp, e.getMessage(), e);
            throw new RuntimeException("管理员登录失败：" + e.getMessage());
        }
    }

    /**
     * 验证管理员登录请求参数
     * 
     * @param adminLoginRequest 管理员登录请求参数
     * @return true-验证通过，false-验证失败
     */
    private boolean validateAdminLoginRequest(AdminLoginRequest adminLoginRequest) {
        if (adminLoginRequest == null) {
            log.warn("管理员登录请求为空");
            return false;
        }
        
        if (StrUtil.isBlank(adminLoginRequest.getPhone())) {
            log.warn("管理员登录手机号为空");
            return false;
        }
        
        if (StrUtil.isBlank(adminLoginRequest.getPassword())) {
            log.warn("管理员登录密码为空");
            return false;
        }
        
        if (StrUtil.isBlank(adminLoginRequest.getPlatform())) {
            log.warn("管理员登录平台为空");
            return false;
        }
        
        // 验证手机号格式
        if (!adminLoginRequest.getPhone().matches("^1[3-9]\\d{9}$")) {
            log.warn("管理员登录手机号格式不正确: {}", adminLoginRequest.getPhone());
            return false;
        }
        
        return true;
    }

    /**
     * 处理管理员登录逻辑
     * 
     * @param adminLoginRequest 管理员登录请求参数
     * @return 用户对象
     */
    private Users processAdminLogin(AdminLoginRequest adminLoginRequest) {
        // 根据手机号查找用户
        Users user = findUserByPhone(adminLoginRequest.getPhone());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 检查用户状态
        if (user.getStatus() != AuthConstants.UserStatus.NORMAL) {
            throw new RuntimeException("账户已被禁用，请联系管理员");
        }
        
        // 验证密码
        if (!verifyPassword(adminLoginRequest.getPassword(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }
        
        return user;
    }

    /**
     * 检查用户是否具有管理员权限
     * 
     * @param user 用户对象
     * @return true-是管理员，false-不是管理员
     */
    private boolean isUserAdmin(Users user) {
        try {
            // 获取用户角色
            List<Roles> roles = rolesService.getRolesByUserId(user.getId());
            
            if (roles == null || roles.isEmpty()) {
                log.warn("用户没有任何角色，用户ID: {}", user.getId());
                return false;
            }
            
            // 检查是否包含管理员角色
            boolean hasAdminRole = roles.stream()
                    .anyMatch(role -> "ADMIN".equals(role.getRoleCode()) || "SUPER_ADMIN".equals(role.getRoleCode()));
            
            if (!hasAdminRole) {
                log.warn("用户不具有管理员权限，用户ID: {}, 角色: {}", 
                        user.getId(), roles.stream().map(Roles::getRoleCode).collect(java.util.stream.Collectors.toList()));
            }
            
            return hasAdminRole;
            
        } catch (Exception e) {
            log.error("检查用户管理员权限失败，用户ID: {}", user.getId(), e);
            return false;
        }
    }

    /**
     * 更新用户管理员登录信息
     * 
     * @param user 用户对象
     * @param adminLoginRequest 管理员登录请求参数
     * @param clientIp 客户端IP地址
     */
    private void updateUserAdminLoginInfo(Users user, AdminLoginRequest adminLoginRequest, String clientIp) {
        try {
            // 更新最后登录时间
            user.setLastLoginTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            // 保存用户信息
            usersService.updateById(user);
            
            log.info("管理员登录信息更新成功，用户ID: {}", user.getId());
            
        } catch (Exception e) {
            log.error("更新管理员登录信息失败，用户ID: {}", user.getId(), e);
            // 不抛出异常，不影响登录流程
        }
    }

    /**
     * 构建管理员登录响应
     * 
     * @param token 访问Token
     * @param user 用户对象
     * @param userPrincipal 用户主体对象
     * @return 管理员登录响应
     */
    private AdminLoginResponse buildAdminLoginResponse(String token, Users user, UserPrincipal userPrincipal) {
        try {
            AdminLoginResponse response = new AdminLoginResponse();
            
            // 设置Token信息
            response.setToken(token);
            response.setTokenExpireTime(LocalDateTime.now().plusSeconds(AuthConstants.Token.TOKEN_EXPIRE_TIME));
            
            // 设置用户基本信息
            response.setUserId(user.getId());
            response.setUsername(user.getUsername());
            response.setNickname(user.getNickname());
            response.setPhone(user.getPhone());
            response.setEmail(user.getEmail());
            response.setAvatarUrl(user.getAvatarUrl());
            response.setGender(user.getGender() != null ? user.getGender().intValue() : null);
            response.setStatus(user.getStatus() != null ? user.getStatus().intValue() : null);
            response.setLastLoginTime(user.getLastLoginTime());
            
            // 设置角色和权限
            response.setRoles(userPrincipal.getRoles());
            response.setPermissions(userPrincipal.getPermissions());
            
            // 设置管理员菜单（简化版本，实际项目中应该从数据库获取）
            response.setMenus(buildAdminMenus(userPrincipal.getRoles()));
            
            return response;
            
        } catch (Exception e) {
            log.error("构建管理员登录响应失败，用户ID: {}", user.getId(), e);
            throw new RuntimeException("构建登录响应失败");
        }
    }

    /**
     * 构建管理员菜单
     * 
     * @param roles 用户角色列表
     * @return 管理员菜单列表
     */
    private List<AdminLoginResponse.AdminMenuDTO> buildAdminMenus(List<String> roles) {
        List<AdminLoginResponse.AdminMenuDTO> menus = new java.util.ArrayList<>();
        
        try {
            // 基础菜单（所有管理员都有）
            AdminLoginResponse.AdminMenuDTO dashboard = new AdminLoginResponse.AdminMenuDTO();
            dashboard.setId(1L);
            dashboard.setName("仪表盘");
            dashboard.setPath("/admin/dashboard");
            dashboard.setIcon("dashboard");
            dashboard.setSort(1);
            dashboard.setParentId(0L);
            menus.add(dashboard);
            
            AdminLoginResponse.AdminMenuDTO users = new AdminLoginResponse.AdminMenuDTO();
            users.setId(2L);
            users.setName("用户管理");
            users.setPath("/admin/users");
            users.setIcon("users");
            users.setSort(2);
            users.setParentId(0L);
            menus.add(users);
            
            AdminLoginResponse.AdminMenuDTO templates = new AdminLoginResponse.AdminMenuDTO();
            templates.setId(3L);
            templates.setName("模板管理");
            templates.setPath("/admin/templates");
            templates.setIcon("templates");
            templates.setSort(3);
            templates.setParentId(0L);
            menus.add(templates);
            
            // 超级管理员专属菜单
            if (roles != null && roles.contains("SUPER_ADMIN")) {
                AdminLoginResponse.AdminMenuDTO system = new AdminLoginResponse.AdminMenuDTO();
                system.setId(4L);
                system.setName("系统管理");
                system.setPath("/admin/system");
                system.setIcon("system");
                system.setSort(4);
                system.setParentId(0L);
                menus.add(system);
            }
            
            return menus;
            
        } catch (Exception e) {
            log.error("构建管理员菜单失败", e);
            return new java.util.ArrayList<>();
        }
    }
} 