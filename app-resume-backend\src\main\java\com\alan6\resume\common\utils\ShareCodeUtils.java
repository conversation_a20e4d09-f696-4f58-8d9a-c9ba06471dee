package com.alan6.resume.common.utils;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 分享码生成工具类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public class ShareCodeUtils {

    private static final String CHARACTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final int DEFAULT_LENGTH = 12;

    /**
     * 生成简历分享码
     *
     * @param resumeId 简历ID
     * @return 分享码
     */
    public static String generateResumeShareCode(Long resumeId) {
        return "r_" + generateRandomCode(DEFAULT_LENGTH);
    }

    /**
     * 生成模板分享码
     *
     * @param templateId 模板ID
     * @return 分享码
     */
    public static String generateTemplateShareCode(Long templateId) {
        return "tpl_" + generateRandomCode(DEFAULT_LENGTH);
    }

    /**
     * 生成随机码
     *
     * @param length 长度
     * @return 随机码
     */
    private static String generateRandomCode(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }

    /**
     * 生成分享链接
     *
     * @param baseUrl   基础URL
     * @param shareCode 分享码
     * @return 分享链接
     */
    public static String generateShareUrl(String baseUrl, String shareCode) {
        if (baseUrl.endsWith("/")) {
            return baseUrl + "share/" + shareCode;
        }
        return baseUrl + "/share/" + shareCode;
    }

    /**
     * 生成二维码URL
     *
     * @param cdnUrl    CDN基础URL
     * @param shareCode 分享码
     * @return 二维码URL
     */
    public static String generateQrCodeUrl(String cdnUrl, String shareCode) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        if (cdnUrl.endsWith("/")) {
            return cdnUrl + "qr/" + shareCode + "_" + timestamp + ".png";
        }
        return cdnUrl + "/qr/" + shareCode + "_" + timestamp + ".png";
    }
} 