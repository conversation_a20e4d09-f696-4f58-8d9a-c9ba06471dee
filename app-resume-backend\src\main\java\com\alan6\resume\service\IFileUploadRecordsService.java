package com.alan6.resume.service;

import com.alan6.resume.dto.file.*;
import com.alan6.resume.entity.FileUploadRecords;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件上传记录表服务类
 * 
 * 主要功能：
 * 1. 提供文件上传和下载服务
 * 2. 实现MinIO对象存储集成
 * 3. 管理文件版本控制和预签名URL
 * 4. 处理存储桶管理功能
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IFileUploadRecordsService extends IService<FileUploadRecords> {

    /**
     * 上传文件到MinIO
     * 
     * @param file 上传的文件
     * @param request 上传请求参数
     * @param userId 用户ID
     * @return 文件上传响应
     */
    FileUploadResponse uploadFile(MultipartFile file, FileUploadRequest request, Long userId);

    /**
     * 获取文件信息
     * 
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 文件信息响应
     */
    FileInfoResponse getFileInfo(String fileId, Long userId);

    /**
     * 获取文件列表
     * 
     * @param type 文件类型筛选
     * @param bucketName 存储桶筛选
     * @param fileType MIME类型筛选
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param keyword 关键词搜索
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @return 文件列表响应
     */
    FileListResponse getFileList(String type, String bucketName, String fileType, 
                                LocalDateTime startTime, LocalDateTime endTime, String keyword,
                                Integer page, Integer size, Long userId);

    /**
     * 删除文件
     * 
     * @param fileId 文件ID
     * @param permanent 是否永久删除
     * @param userId 用户ID
     * @return 删除结果
     */
    boolean deleteFile(String fileId, Boolean permanent, Long userId);

    /**
     * 生成预签名URL
     * 
     * @param request 预签名URL请求
     * @param userId 用户ID
     * @return 预签名URL响应
     */
    PresignedUrlResponse generatePresignedUrl(PresignedUrlRequest request, Long userId);

    /**
     * 获取文件版本列表
     * 
     * @param fileId 文件ID
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @return 文件版本列表
     */
    FileVersionListResponse getFileVersions(String fileId, Integer page, Integer size, Long userId);

    /**
     * 恢复文件版本
     * 
     * @param fileId 文件ID
     * @param versionId 版本ID
     * @param userId 用户ID
     * @return 恢复结果
     */
    FileVersionRestoreResponse restoreFileVersion(String fileId, String versionId, Long userId);

    /**
     * 获取存储桶列表
     * 
     * @param userId 用户ID
     * @return 存储桶列表响应
     */
    BucketListResponse getBucketList(Long userId);

    /**
     * 创建存储桶
     * 
     * @param request 创建存储桶请求
     * @param userId 用户ID
     * @return 创建结果
     */
    BucketCreateResponse createBucket(BucketCreateRequest request, Long userId);

    /**
     * 下载文件
     * 
     * @param fileId 文件ID
     * @param download 是否强制下载
     * @param filename 自定义文件名
     * @param userId 用户ID
     * @return 文件字节数组
     */
    byte[] downloadFile(String fileId, Boolean download, String filename, Long userId);

    /**
     * 检查文件访问权限
     * 
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return true-有权限，false-无权限
     */
    boolean checkFileAccess(String fileId, Long userId);

    /**
     * 生成唯一文件名
     * 
     * @param originalFileName 原始文件名
     * @param fileType 文件类型
     * @return 唯一文件名
     */
    String generateUniqueFileName(String originalFileName, String fileType);

    /**
     * 验证文件类型和大小
     * 
     * @param file 文件
     * @param fileType 文件类型
     * @return 验证结果
     */
    boolean validateFile(MultipartFile file, String fileType);

    /**
     * 获取文件类型对应的存储桶名称
     * 
     * @param fileType 文件类型
     * @return 存储桶名称
     */
    String getBucketNameByFileType(String fileType);

    /**
     * 构建文件路径
     * 
     * @param fileType 文件类型
     * @param fileName 文件名
     * @return 文件路径
     */
    String buildFilePath(String fileType, String fileName);
}
