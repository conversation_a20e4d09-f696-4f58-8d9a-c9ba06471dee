import{u as de}from"./CEpU8TOc.js";import{R as pe}from"./JeH0SXlh.js";import{s as me}from"./x_rD_Ya3.js";import{_ as ge}from"./DlAUqK2U.js";import{r as g,k as c,i as N,D as I,B as G,A as ve,c as S,o as P,a as d,G as R,F as fe,g as he,q as be,t as ye,b as xe}from"./CURHyiUL.js";const Me={class:"preview-container"},$e={class:"scale-wrapper"},Se={class:"page-break-indicators"},Pe={class:"break-line"},we={key:0,class:"break-text-left"},ke={class:"break-text-right"},T=794,b=1122,q=60,_e={__name:"ResumePreview",props:{resumeData:{type:Object,default:()=>({})},settings:{type:Object,default:()=>({})}},emits:["module-order-change","module-delete","template-defaults-loaded"],setup(W,{emit:X}){const C=W,y=X,n=de(),m=g(null),w=g(null),k=g(0),E=g([]),_=g(1);g(0);const J={fontFamily:"system-ui",fontSize:"14px",textColor:"#333333",margins:{top:60,bottom:60,side:60},spacings:{module:24,item:16,line:1.6,modulePadding:16,itemPadding:12,paragraph:12,columnGap:40}},o=c(()=>({...J,...C.settings})),K=c(()=>{var e,t,a,l,r,v,f,s,p,h,u,i,$,O,j,A,B,F,L;return console.log("🐛 pageStyles 调试 - 模块间距:",{userModuleSpacing:(e=o.value.spacings)==null?void 0:e.module,finalModuleSpacing:((t=o.value.spacings)==null?void 0:t.module)||8,resumeStylesValue:o.value,spacings:o.value.spacings}),{fontFamily:o.value.fontFamily,fontSize:o.value.fontSize,lineHeight:((a=o.value.spacings)==null?void 0:a.line)||1.6,color:o.value.textColor,"--resume-margin-top":`${((l=o.value.margins)==null?void 0:l.top)||20}px`,"--resume-margin-bottom":`${((r=o.value.margins)==null?void 0:r.bottom)||20}px`,"--resume-margin-left":`${((v=o.value.margins)==null?void 0:v.side)||60}px`,"--resume-margin-right":`${((f=o.value.margins)==null?void 0:f.side)||60}px`,"--resume-module-spacing":`${((s=o.value.spacings)==null?void 0:s.module)||24}px`,"--resume-module-padding":`${((p=o.value.spacings)==null?void 0:p.modulePadding)||16}px`,"--resume-item-spacing":`${((h=o.value.spacings)==null?void 0:h.item)||16}px`,"--resume-item-padding":`${((u=o.value.spacings)==null?void 0:u.itemPadding)||12}px`,"--resume-line-height":((i=o.value.spacings)==null?void 0:i.line)||1.6,"--resume-paragraph-spacing":`${(($=o.value.spacings)==null?void 0:$.paragraph)||12}px`,"--resume-column-gap":`${((O=o.value.spacings)==null?void 0:O.columnGap)||40}px`,"--margin-top":`${((j=o.value.margins)==null?void 0:j.top)||20}px`,"--margin-side":`${((A=o.value.margins)==null?void 0:A.side)||60}px`,"--module-spacing":`${((B=o.value.spacings)==null?void 0:B.module)||24}px`,"--item-spacing":`${((F=o.value.spacings)==null?void 0:F.item)||16}px`,"--line-height":((L=o.value.spacings)==null?void 0:L.line)||1.6}}),Q=c(()=>{var e;return{fontSize:o.value.fontSize||"14px",lineHeight:((e=o.value.spacings)==null?void 0:e.line)||1.6,textColor:o.value.textColor||"#333333",primaryColor:"#3b82f6",secondaryColor:"#6366f1",fontFamily:o.value.fontFamily||"system-ui",margins:o.value.margins,spacings:o.value.spacings}}),D=c(()=>{const e=C.resumeData||{};return console.log("✅ ResumePreview 使用数据:",e),console.log("🔍 ResumePreview - modules:",Object.keys(e.modules||{})),e}),Y=c(()=>{var f;const e=((f=D.value)==null?void 0:f.modules)||{};if(console.log("🔍 ResumePreview - 计算可见模块"),console.log("📊 简历数据模块:",Object.keys(e)),console.log("🏪 editorStore:",n),!e||Object.keys(e).length===0)return console.log("⚠️ 没有简历数据"),[];if(!n)return console.warn("⚠️ editorStore 未定义"),[];const t=n.enabledModules,a=n.moduleConfigs,l=n.customModules;console.log("🔍 响应式数据检查:",{enabledModules:t,moduleConfigs:a,customModules:l});let r=[];try{if(t&&Array.isArray(t)&&t.length>0)r=t,console.log("✅ 使用 editorStore.enabledModules:",r.map(s=>`${s.id}(${s.order})`));else{const s=(a==null?void 0:a.value)||a||[],p=(l==null?void 0:l.value)||l||[];if(Array.isArray(s)&&s.length>0)r=[...s,...p].filter(u=>u.enabled).sort((u,i)=>(u.order||0)-(i.order||0)),console.log("🔄 使用 moduleConfigs 后备方案:",r.map(u=>`${u.id}(${u.order})`));else{console.log("🔄 使用数据驱动的最终后备方案");const h=Object.keys(e),u={basic:1,education:2,workExperience:3,projectExperience:4,skills:5,certificate:6,selfEvaluation:7,internship:8,awards:9,hobbies:10};r=h.map(i=>({id:i,name:i,enabled:!0,order:u[i]||999})).sort((i,$)=>i.order-$.order),console.log("🔄 基于数据的模块:",r.map(i=>`${i.id}(${i.order})`))}}}catch(s){return console.error("❌ 获取启用模块时出错:",s),[]}const v=r.map(s=>{const p=e[s.id];return p!==void 0?{id:s.id,data:p,enabled:!0,order:s.order||0,name:s.name}:null}).filter(s=>s!==null);return console.log("✅ 最终可见模块:",v.map(s=>`${s.name}(${s.id})`)),v}),Z=c(()=>Y.value.map(e=>e.id)),V=c(()=>k.value===0?1:Math.ceil(k.value/b)),H=c(()=>{const e=V.value,t=e*b;return console.log(`📏 计算容器最小高度: ${e}页 × ${b}px = ${t}px`),t}),ee=()=>{if(!m.value)return 1;const e=m.value.getBoundingClientRect(),t=e.width-24;if(t<=0)return .3;const a=t-20,l=a/T,r=Math.max(.25,Math.min(l,1.2));return console.log(`🔍 容器宽度: ${e.width}px, 可用宽度: ${t}px, 安全宽度: ${a}px, A4宽度: ${T}px, 计算缩放: ${l.toFixed(2)}, 最终缩放: ${r.toFixed(2)}`),r},te=()=>{const e=document.querySelector(".resume-template");if(!e)return{};const t=getComputedStyle(e);return{moduleSpacing:parseInt(t.getPropertyValue("--template-default-module-spacing"))||24,modulePadding:parseInt(t.getPropertyValue("--template-default-module-padding"))||16,itemSpacing:parseInt(t.getPropertyValue("--template-default-item-spacing"))||16,itemPadding:parseInt(t.getPropertyValue("--template-default-item-padding"))||12,marginTop:parseInt(t.getPropertyValue("--template-default-margin-top"))||60,marginBottom:parseInt(t.getPropertyValue("--template-default-margin-bottom"))||60,marginSide:parseInt(t.getPropertyValue("--template-default-margin-side"))||60,lineHeight:parseFloat(t.getPropertyValue("--template-default-line-height"))||1.6,paragraphSpacing:parseInt(t.getPropertyValue("--template-default-paragraph-spacing"))||12}};let x=null;N(()=>{I(()=>{console.log("🚀 初始化预览缩放功能"),M();const e=te();if(console.log("📋 读取到的模板默认值:",e),y("template-defaults-loaded",e),window.addEventListener("resize",z),m.value&&"ResizeObserver"in window)x=new ResizeObserver(t=>{var a;console.log("📏 容器大小变化检测到:",(a=t[0])==null?void 0:a.contentRect),M()}),x.observe(m.value),console.log("👀 已启动容器大小监听");else{console.warn("⚠️ ResizeObserver 不可用，使用定时器作为备选方案");const t=me(()=>{M()},100);G(()=>{clearInterval(t)})}})}),G(()=>{window.removeEventListener("resize",z),x&&(x.disconnect(),console.log("🛑 已停止容器大小监听"))});const M=()=>{if(!m.value){console.log("⚠️ 预览容器引用不存在");return}I(()=>{const e=ee(),t=_.value;_.value=e,console.log(`🔍 预览缩放比例更新: ${Math.round(t*100)}% → ${Math.round(e*100)}%`)})},z=()=>{M()},U=e=>{console.log("📏 内容高度变化:",e),k.value=e,oe()},oe=()=>{const e=[],t=V.value;console.log("📄 计算分页指示器，总页数:",t);for(let a=1;a<t;a++){const l=a*b-q;e.push({pageNumber:a,top:l,isVisible:!0,isLastPage:!1}),console.log(`📍 第${a}页底部分割线位置:`,l,`显示页码: 第${a}页`)}if(t>0){const a=t*b-q-30;e.push({pageNumber:t,top:a,isVisible:!0,isLastPage:!0}),console.log(`📍 最后一页（第${t}页）底部分割线位置:`,a)}E.value=e};ve(()=>D.value,()=>{console.log("📊 简历数据变化，准备更新分页指示器"),I(()=>{setTimeout(()=>{if(w.value){const e=w.value.querySelector(".continuous-resume-content");if(e){const t=e.scrollHeight;U(t)}}},100)})},{deep:!0,immediate:!0}),N(()=>{console.log("🚀 初始化自然流动分页系统...")});const ae=e=>{n.updateModuleData("basic_info",e)},se=e=>{n.moveModule(e,-1),y("module-order-change",{moduleType:e,direction:-1})},le=e=>{n.moveModule(e,1),y("module-order-change",{moduleType:e,direction:1})},ne=e=>{n.deleteModule(e),y("module-delete",e)},re=e=>{console.log("🔄 处理技能更新:",e);const t={skills:e};n.updateModuleData("skills",t)},ie=e=>{n.updateModuleData("work",e)},ue=e=>{n.updateModuleData("education",{education:e})},ce=e=>{n.updateModuleData("project",{projects:e})};return(e,t)=>(P(),S("div",{class:"resume-preview",ref_key:"previewRef",ref:m},[d("div",Me,[d("div",$e,[d("div",{class:"natural-flow-container",ref_key:"resumeRef",ref:w,style:R({minHeight:H.value+"px",transform:`scale(${_.value})`,transformOrigin:"center top"})},[t[0]||(t[0]=d("div",{class:"a4-boundary-label"},"A4 纸张区域 (210mm × 297mm)",-1)),d("div",Se,[(P(!0),S(fe,null,he(E.value,(a,l)=>(P(),S("div",{key:`break-${l}`,class:"page-break-indicator",style:R({top:a.top+"px"})},[d("div",Pe,[a.isLastPage?be("",!0):(P(),S("div",we,"页面分隔处，避免在此编辑内容")),d("div",ke,"第 "+ye(a.pageNumber)+" 页",1)])],4))),128))]),d("div",{class:"continuous-resume-content",style:R({...K.value,minHeight:H.value+"px"})},[xe(pe,{"resume-data":D.value,"text-style":Q.value,"is-draggable":!0,"visible-modules":Z.value,"page-index":0,"is-pagination-mode":!1,"is-natural-flow":!0,onBasicInfoUpdate:ae,onModuleMoveUp:se,onModuleMoveDown:le,onModuleDelete:ne,onSkillUpdate:re,onExperienceUpdate:ie,onEducationUpdate:ue,onProjectUpdate:ce,onContentHeightChange:U},null,8,["resume-data","text-style","visible-modules"])],4)],4)])])],512))}},Ve=ge(_e,[["__scopeId","data-v-7f54bcd5"]]);export{Ve as R};
