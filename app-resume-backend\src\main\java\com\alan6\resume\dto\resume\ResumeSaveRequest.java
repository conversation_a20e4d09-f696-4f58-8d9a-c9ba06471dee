package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 简历保存请求DTO
 * 
 * @description 用于创建或更新简历时的请求参数
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "简历保存请求参数")
public class ResumeSaveRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", required = true)
    private Long userId;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    private Long templateId;

    /**
     * 简历名称
     */
    @NotBlank(message = "简历名称不能为空")
    @Schema(description = "简历名称", required = true)
    private String name;

    /**
     * 简历语言
     */
    @Schema(description = "简历语言", defaultValue = "zh-CN")
    private String language = "zh-CN";

    /**
     * 简历模块数据
     * 包含所有模块的内容和配置
     */
    @Schema(description = "简历模块数据")
    private List<ModuleData> modules;

    /**
     * 全局设置
     * 包含主题、布局、排版等设置
     */
    @Schema(description = "全局设置")
    private GlobalSettings globalSettings;

    /**
     * 简历状态
     * 0-草稿，1-完成
     */
    @Schema(description = "简历状态（0-草稿，1-完成）", defaultValue = "0")
    private Integer status = 0;

    /**
     * 模块数据定义
     */
    @Data
    @Schema(description = "模块数据")
    public static class ModuleData {
        /**
         * 模块ID
         */
        @Schema(description = "模块ID")
        private String id;

        /**
         * 模块类型
         */
        @Schema(description = "模块类型")
        private String type;

        /**
         * 模块名称
         */
        @Schema(description = "模块名称")
        private String name;

        /**
         * 是否启用
         */
        @Schema(description = "是否启用", defaultValue = "true")
        private Boolean enabled = true;

        /**
         * 排序顺序
         */
        @Schema(description = "排序顺序")
        private Integer order;

        /**
         * 自定义标题
         */
        @Schema(description = "自定义标题")
        private String customTitle;

        /**
         * 模块样式
         */
        @Schema(description = "模块样式")
        private Map<String, Object> styles;

        /**
         * 模块数据
         */
        @Schema(description = "模块数据")
        private Map<String, Object> data;
    }

    /**
     * 全局设置定义
     */
    @Data
    @Schema(description = "全局设置")
    public static class GlobalSettings {
        /**
         * 主题设置
         */
        @Schema(description = "主题设置")
        private ThemeSettings theme;

        /**
         * 布局设置
         */
        @Schema(description = "布局设置")
        private LayoutSettings layout;

        /**
         * 排版设置
         */
        @Schema(description = "排版设置")
        private TypographySettings typography;

        /**
         * 页面设置
         */
        @Schema(description = "页面设置")
        private PageSettings page;
    }

    /**
     * 主题设置
     */
    @Data
    @Schema(description = "主题设置")
    public static class ThemeSettings {
        @Schema(description = "主色调")
        private String primaryColor;
        
        @Schema(description = "次要色调")
        private String secondaryColor;
        
        @Schema(description = "背景色")
        private String backgroundColor;
        
        @Schema(description = "文本颜色")
        private String textColor;
        
        @Schema(description = "字体族")
        private String fontFamily;
        
        @Schema(description = "基础字体大小")
        private String fontSize;
        
        @Schema(description = "行高")
        private String lineHeight;
    }

    /**
     * 布局设置
     */
    @Data
    @Schema(description = "布局设置")
    public static class LayoutSettings {
        @Schema(description = "上边距")
        private String marginTop;
        
        @Schema(description = "下边距")
        private String marginBottom;
        
        @Schema(description = "左边距")
        private String marginLeft;
        
        @Schema(description = "右边距")
        private String marginRight;
        
        @Schema(description = "模块间距")
        private String moduleSpacing;
    }

    /**
     * 排版设置
     */
    @Data
    @Schema(description = "排版设置")
    public static class TypographySettings {
        @Schema(description = "一级标题大小")
        private String h1Size;
        
        @Schema(description = "一级标题字重")
        private String h1Weight;
        
        @Schema(description = "二级标题大小")
        private String h2Size;
        
        @Schema(description = "二级标题字重")
        private String h2Weight;
        
        @Schema(description = "正文字体大小")
        private String bodySize;
        
        @Schema(description = "正文字重")
        private String bodyWeight;
    }

    /**
     * 页面设置
     */
    @Data
    @Schema(description = "页面设置")
    public static class PageSettings {
        @Schema(description = "纸张大小（A4, Letter等）")
        private String size;
        
        @Schema(description = "页面方向（portrait, landscape）")
        private String orientation;
        
        @Schema(description = "缩放比例")
        private Double scale;
    }
} 