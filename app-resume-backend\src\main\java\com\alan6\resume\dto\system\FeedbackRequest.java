package com.alan6.resume.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Email;
import java.util.List;

/**
 * 意见反馈请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "FeedbackRequest", description = "意见反馈请求")
public class FeedbackRequest {

    /**
     * 反馈类型
     */
    @NotBlank(message = "反馈类型不能为空")
    @Schema(description = "反馈类型（bug, feature, complaint, suggestion）", example = "bug")
    private String type;

    /**
     * 反馈标题
     */
    @NotBlank(message = "反馈标题不能为空")
    @Size(max = 100, message = "反馈标题长度不能超过100字符")
    @Schema(description = "反馈标题", example = "文件上传失败")
    private String title;

    /**
     * 反馈内容
     */
    @NotBlank(message = "反馈内容不能为空")
    @Size(max = 2000, message = "反馈内容长度不能超过2000字符")
    @Schema(description = "反馈内容", example = "在上传简历照片时，系统提示文件格式不支持，但我上传的是JPG格式")
    private String content;

    /**
     * 联系邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<FeedbackAttachment> attachments;

    /**
     * 客户端信息
     */
    @Schema(description = "客户端信息（浏览器、设备等）")
    private java.util.Map<String, Object> clientInfo;

    /**
     * 附件内部类
     */
    @Data
    @Schema(name = "FeedbackAttachment", description = "反馈附件")
    public static class FeedbackAttachment {
        
        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "screenshot.png")
        private String fileName;
        
        /**
         * 文件URL
         */
        @Schema(description = "文件URL", example = "https://cdn.example.com/feedback/screenshot.png")
        private String fileUrl;
    }
} 