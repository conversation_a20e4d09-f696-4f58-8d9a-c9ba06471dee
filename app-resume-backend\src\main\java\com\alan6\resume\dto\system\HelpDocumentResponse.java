package com.alan6.resume.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 帮助文档响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(description = "帮助文档响应DTO")
public class HelpDocumentResponse {

    /**
     * 分类列表
     */
    @Schema(description = "分类列表")
    private List<CategoryInfo> categories;

    /**
     * 分类信息
     */
    @Data
    @Schema(description = "分类信息")
    public static class CategoryInfo {
        
        /**
         * 分类ID
         */
        @Schema(description = "分类ID")
        private Long id;

        /**
         * 分类名称
         */
        @Schema(description = "分类名称", example = "getting-started")
        private String name;

        /**
         * 分类标题
         */
        @Schema(description = "分类标题", example = "快速开始")
        private String title;

        /**
         * 分类描述
         */
        @Schema(description = "分类描述", example = "新手入门指南")
        private String description;

        /**
         * 分类图标
         */
        @Schema(description = "分类图标")
        private String icon;

        /**
         * 文章列表
         */
        @Schema(description = "文章列表")
        private List<ArticleInfo> articles;
    }

    /**
     * 文章信息
     */
    @Data
    @Schema(description = "文章信息")
    public static class ArticleInfo {
        
        /**
         * 文章ID
         */
        @Schema(description = "文章ID")
        private Long id;

        /**
         * 文章标题
         */
        @Schema(description = "文章标题", example = "如何注册账号")
        private String title;

        /**
         * 文章摘要
         */
        @Schema(description = "文章摘要", example = "详细介绍账号注册流程")
        private String summary;

        /**
         * 访问URL
         */
        @Schema(description = "访问URL", example = "/help/getting-started/register")
        private String url;

        /**
         * 浏览次数
         */
        @Schema(description = "浏览次数", example = "1250")
        private Integer viewCount;

        /**
         * 有用评价次数
         */
        @Schema(description = "有用评价次数", example = "89")
        private Integer helpfulCount;

        /**
         * 更新时间
         */
        @Schema(description = "更新时间", example = "2024-12-01 10:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updateTime;
    }
} 