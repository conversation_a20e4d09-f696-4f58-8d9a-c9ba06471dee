import{_ as W}from"./BXnw39BI.js";import{r as p,k as $,P as U,c as m,o as d,a as e,q as z,t as f,F as A,g as V,s as N,f as Z,d as D,i as H,B as R,b as L,w as P,G as q,m as S,z as G,T as K,Q,A as J,L as ee,N as te,e as se,h as M}from"./CURHyiUL.js";import{u as oe}from"./DCz3uDMC.js";import{s as ae}from"./x_rD_Ya3.js";import{_ as le}from"./DlAUqK2U.js";const re=()=>{const o=p(null),g=p([]),n=p([]),r=p(!1),a=p(null),h=oe(),y=async i=>{r.value=!0,a.value=null;try{const t=await h.fetchTemplateById(i);if(t.success)return o.value=t.data,t.data;throw new Error(t.error||"获取模板详情失败")}catch(t){throw console.error("获取模板详情失败:",t),a.value=t.message||"获取模板详情失败",t}finally{r.value=!1}},k=async(i,t=12)=>{try{const s=o.value;if(s){const l={current:1,size:t,categoryId:s.categoryId,industry:s.industry,style:s.style,sortBy:"popular"},c=await h.fetchTemplates(l);if(c.success){const v=c.data.filter(I=>I.id!==parseInt(i));return g.value=v.slice(0,t),g.value}}return g.value=[],[]}catch(s){return console.error("获取相似模板失败:",s),g.value=[],[]}},x=async(i=12)=>{try{const t={current:1,size:i,sortBy:"popular"},s=await h.fetchTemplates(t);return s.success?(n.value=s.data,n.value):(n.value=[],[])}catch(t){return console.error("获取热门模板失败:",t),n.value=[],[]}},_=async i=>{try{await h.recordTemplateUsage(i),o.value&&o.value.id==i&&(o.value.useCount=(o.value.useCount||0)+1)}catch(t){console.error("记录模板使用失败:",t)}},b=async(i,t)=>{try{const s=await h.useTemplate(i,t);return s.success&&o.value&&o.value.id==i&&(o.value.useCount=(o.value.useCount||0)+1),s}catch(s){throw console.error("使用模板失败:",s),s}},u=async i=>{try{await y(i),await Promise.all([k(i),x()])}catch(t){throw console.error("初始化页面数据失败:",t),t}},T=()=>{o.value=null,g.value=[],n.value=[],a.value=null},w=i=>{if(!i)return"";const{name:t,industry:s,style:l}=i;return t||`${s||"通用"}-${l||"经典"}`},B=$(()=>w(o.value)),j=$(()=>r.value),C=$(()=>!!a.value);return{currentTemplate:U(o),similarTemplates:U(g),hotTemplates:U(n),loading:U(r),error:U(a),templateDisplayName:B,isLoading:j,hasError:C,fetchTemplateDetail:y,fetchSimilarTemplates:k,fetchHotTemplates:x,recordTemplateUsage:_,useTemplate:b,initializePageData:u,clearData:T,formatTemplateName:w}},ne={class:"bg-white rounded-2xl shadow-card overflow-hidden"},ie={class:"flex gap-8 p-6 lg:p-8"},ce={class:"w-[60%] space-y-4"},de=["src","alt"],ue={key:1,class:"p-8 h-full flex flex-col justify-center items-center text-center"},me={class:"text-lg font-semibold text-secondary-900 mb-2"},ve={key:0,class:"flex space-x-2 overflow-x-auto"},pe=["onClick"],he=["src","alt"],ye={class:"w-[40%] space-y-6"},fe={class:"text-2xl lg:text-3xl font-bold text-secondary-900 mb-3"},xe={class:"flex flex-wrap items-center gap-3 mb-4"},ge={class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"},be={class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary-100 text-secondary-700"},we={class:"flex items-center text-sm text-secondary-600"},ke={class:"ml-2 text-danger-600"},_e={class:"text-secondary-700 leading-relaxed mb-6"},Te={class:"space-y-3 mb-6"},$e={class:"grid grid-cols-4 gap-3"},Ce={class:"text-xs text-secondary-700"},Me={__name:"TemplateDetail",props:{template:{type:Object,required:!0,validator:o=>o&&typeof o=="object"&&o.id}},emits:["image-click","use-template"],setup(o,{emit:g}){const n=o,r=g,a=p(0),h=$(()=>[{id:1,name:"产品设计"},{id:2,name:"产品"},{id:3,name:"产品策划"},{id:4,name:"经典"},{id:5,name:"左右结构"},{id:6,name:"自定义数据"},{id:7,name:"自定义颜色"},{id:8,name:"技能条"},{id:9,name:"荣誉栏"},{id:10,name:"一键模板"}]),y=$(()=>n.template.images&&n.template.images.length>0?n.template.images[a.value]:n.template.thumbnail||""),k=()=>{r("image-click",{imageUrl:y.value,imageAlt:n.template.name})},x=b=>{a.value=b},_=()=>{r("use-template",n.template)};return(b,u)=>(d(),m("div",ne,[e("div",ie,[e("div",ce,[e("div",{class:"relative group cursor-pointer aspect-[3/4] bg-gradient-to-br from-secondary-50 to-secondary-100 rounded-2xl overflow-hidden border border-secondary-100 transition-all duration-300 hover:shadow-soft hover:-translate-y-1",onClick:k},[o.template.thumbnail?(d(),m("img",{key:0,src:o.template.thumbnail,alt:o.template.name,class:"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105",loading:"lazy"},null,8,de)):(d(),m("div",ue,[u[0]||(u[0]=e("div",{class:"w-20 h-20 bg-primary-100 rounded-2xl flex items-center justify-center mb-4"},[e("svg",{class:"w-10 h-10 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("h3",me,f(o.template.name),1),u[1]||(u[1]=e("p",{class:"text-sm text-secondary-600"},"点击查看大图",-1))])),u[2]||(u[2]=e("div",{class:"absolute inset-0 bg-primary-600/0 group-hover:bg-primary-600/10 transition-all duration-300 flex items-center justify-center"},[e("div",{class:"opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0"},[e("div",{class:"px-4 py-2 bg-white/90 backdrop-blur-sm rounded-lg text-primary-600 font-medium text-sm"}," 点击查看大图 ")])],-1))]),o.template.images&&o.template.images.length>1?(d(),m("div",ve,[(d(!0),m(A,null,V(o.template.images,(T,w)=>(d(),m("div",{key:w,class:N(["flex-shrink-0 w-16 h-20 bg-secondary-100 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200",a.value===w?"border-primary-500":"border-transparent hover:border-secondary-300"]),onClick:B=>x(w)},[e("img",{src:T,alt:`${o.template.name} 预览图 ${w+1}`,class:"w-full h-full object-cover"},null,8,he)],10,pe))),128))])):z("",!0)]),e("div",ye,[e("div",null,[e("h1",fe,f(o.template.name),1),e("div",xe,[e("span",ge,f(o.template.category||"经典模板"),1),e("span",be,f(o.template.industry||"通用行业"),1),e("div",we,[u[3]||(u[3]=e("svg",{class:"w-4 h-4 mr-1 text-warning-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1)),D(" "+f(o.template.rating||4.8)+" ",1),e("span",ke,f(o.template.usageCount||4732)+"人使用",1)])]),e("p",_e,f(o.template.description||"这是一款专业的简历模板，设计简洁大方，适合各行各业的求职者使用。模板结构清晰，内容布局合理，能够有效突出个人优势和专业技能。"),1),e("div",Te,[u[5]||(u[5]=e("h3",{class:"text-lg font-semibold text-secondary-900"},"模板特色",-1)),e("div",$e,[(d(!0),m(A,null,V(h.value.slice(0,8),T=>(d(),m("div",{key:T.id,class:"flex items-center space-x-2 p-2 bg-secondary-50 rounded-lg"},[u[4]||(u[4]=e("div",{class:"w-6 h-6 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0"},[e("svg",{class:"w-3 h-3 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)),e("span",Ce,f(T.name),1)]))),128))])]),e("div",{class:"flex justify-center"},[e("button",{onClick:_,class:"inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95"}," 立即使用模板 ")])]),u[6]||(u[6]=Z('<div class="mt-8"><div class="relative bg-gradient-to-br from-orange-400 to-red-500 rounded-2xl overflow-hidden"><div class="absolute inset-0 bg-black/20"></div><div class="relative p-6 text-white text-center"><h3 class="text-xl font-bold mb-2">2025大学生秋招信息共享群联</h3><p class="text-sm opacity-90 mb-4">2025企业最新秋招信息更新共享</p><div class="flex items-center justify-between"><div class="text-left"><p class="text-xs opacity-75">免费领取闪光简历会员 立省49.9</p><p class="text-sm font-medium">大学生求职攻略一本通</p><p class="text-xs opacity-75">免费简历诊断、求职咨询</p></div><div class="w-20 h-20 bg-white rounded-lg flex items-center justify-center"><div class="w-16 h-16 bg-black rounded-lg flex items-center justify-center"><svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg></div></div></div><p class="text-xs opacity-75 mt-2">添加好友后发送&quot;进群&quot;</p></div></div></div>',1))])])]))}},je={class:"bg-white py-12"},Se={class:"container-width section-padding"},Be={class:"flex items-center justify-between mb-8"},Le={class:"text-2xl font-bold text-secondary-900"},Ie={class:"flex items-center space-x-3"},ze=["disabled"],Ue=["disabled"],De={class:"relative overflow-hidden"},Ee={class:"px-2"},Ne=["onClick"],Ae={class:"relative aspect-[3/4] bg-gradient-to-br from-secondary-50 to-secondary-100"},Ve=["src","alt"],Pe={key:1,class:"p-4 h-full flex flex-col justify-center items-center text-center"},Xe={class:"text-xs text-secondary-600"},qe={class:"absolute bottom-3 left-3"},He={class:"inline-flex items-center px-2 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-danger-600"},Re={class:"absolute inset-0 bg-primary-600/0 group-hover:bg-primary-600/90 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center"},Ye={class:"flex space-x-2"},Oe=["onClick"],Fe=["onClick"],We={class:"p-4"},Ze={class:"font-semibold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200 line-clamp-1"},Ge={class:"flex items-center justify-between text-sm"},Ke={class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"},Qe={class:"flex items-center text-secondary-500"},Je={key:0,class:"flex justify-center space-x-2 mt-6"},et=["onClick"],Y={__name:"TemplateCarousel",props:{title:{type:String,required:!0},templates:{type:Array,required:!0,default:()=>[]},titleLink:{type:String,default:"/templates"},slidesToShow:{type:Number,default:6},slidesToScroll:{type:Number,default:1},showIndicators:{type:Boolean,default:!1},autoplay:{type:Boolean,default:!1},autoplayInterval:{type:Number,default:5e3}},emits:["template-click","use-template","preview"],setup(o,{emit:g}){const n=o,r=g,a=p(0),h=p(null),y=p(null),k=$(()=>100/n.slidesToShow),x=$(()=>Math.max(0,Math.ceil((n.templates.length-n.slidesToShow)/n.slidesToScroll)+1)),_=$(()=>a.value===0),b=$(()=>a.value>=x.value-1),u=()=>{b.value||(a.value+=1)},T=()=>{_.value||(a.value-=1)},w=s=>{a.value=Math.max(0,Math.min(s,x.value-1))},B=s=>{r("template-click",s)},j=s=>{r("use-template",s)},C=s=>{r("preview",s)},i=()=>{n.autoplay&&x.value>1&&(y.value=ae(()=>{b.value?a.value=0:u()},n.autoplayInterval))},t=()=>{y.value&&(clearInterval(y.value),y.value=null)};return H(()=>{i()}),R(()=>{t()}),(s,l)=>{const c=W;return d(),m("div",je,[e("div",Se,[e("div",Be,[e("h2",Le,[L(c,{to:o.titleLink,class:"hover:text-primary-600 transition-colors duration-200 cursor-pointer"},{default:P(()=>[D(f(o.title),1)]),_:1},8,["to"])]),e("div",Ie,[e("button",{onClick:T,disabled:_.value,class:N(["p-2 rounded-full border border-secondary-200 hover:border-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",_.value?"text-secondary-400":"text-secondary-600 hover:text-primary-600"])},l[0]||(l[0]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)]),10,ze),e("button",{onClick:u,disabled:b.value,class:N(["p-2 rounded-full border border-secondary-200 hover:border-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",b.value?"text-secondary-400":"text-secondary-600 hover:text-primary-600"])},l[1]||(l[1]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),10,Ue)])]),e("div",De,[e("div",{ref_key:"carouselRef",ref:h,class:"flex transition-transform duration-500 ease-out",style:q({transform:`translateX(-${a.value*k.value}%)`})},[(d(!0),m(A,null,V(o.templates,(v,I)=>(d(),m("div",{key:v.id,class:"flex-shrink-0",style:q({width:`${k.value}%`})},[e("div",Ee,[e("div",{class:"group cursor-pointer bg-white rounded-xl shadow-card border border-secondary-100 overflow-hidden transition-all duration-300 hover:shadow-soft hover:-translate-y-1",onClick:X=>B(v)},[e("div",Ae,[v.thumbnail?(d(),m("img",{key:0,src:v.thumbnail,alt:v.name,class:"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105",loading:"lazy"},null,8,Ve)):(d(),m("div",Pe,[l[2]||(l[2]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-3"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("span",Xe,f(v.name),1)])),e("div",qe,[e("span",He,f(v.usageCount||0)+"人使用 ",1)]),e("div",Re,[e("div",Ye,[e("button",{onClick:S(X=>j(v),["stop"]),class:"px-3 py-1.5 bg-white text-primary-600 rounded-lg hover:bg-secondary-50 transition-colors duration-200 text-sm font-medium"}," 使用模板 ",8,Oe),e("button",{onClick:S(X=>C(v),["stop"]),class:"px-3 py-1.5 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors duration-200 text-sm font-medium"}," 预览 ",8,Fe)])])]),e("div",We,[e("h3",Ze,f(v.name),1),e("div",Ge,[e("span",Ke,f(v.category||"经典"),1),e("div",Qe,[l[3]||(l[3]=e("svg",{class:"w-3 h-3 mr-1 text-warning-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1)),D(" "+f(v.rating||4.8),1)])])])],8,Ne)])],4))),128))],4)]),o.showIndicators&&x.value>1?(d(),m("div",Je,[(d(!0),m(A,null,V(x.value,(v,I)=>(d(),m("button",{key:I,onClick:X=>w(I),class:N(["w-2 h-2 rounded-full transition-all duration-200",a.value===I?"bg-primary-600":"bg-secondary-300 hover:bg-secondary-400"])},null,10,et))),128))])):z("",!0)])])}}},tt={class:"relative w-full h-full flex items-center justify-center p-4"},st=["src","alt"],ot={class:"absolute top-4 right-4 flex items-center space-x-2"},at={class:"absolute bottom-4 left-4 px-3 py-1 bg-white/10 backdrop-blur-sm rounded-lg text-white text-sm"},O=.1,F=5,E=.2,lt={__name:"ImageViewer",props:{visible:{type:Boolean,default:!1},imageUrl:{type:String,required:!0},imageAlt:{type:String,default:"图片预览"}},emits:["close"],setup(o,{emit:g}){const n=o,r=g,a=p(1),h=p(0),y=p(0),k=p(!1),x=p(0),_=p(0),b=p(null),u=()=>{a.value=Math.min(a.value+E,F)},T=()=>{a.value=Math.max(a.value-E,O)},w=()=>{a.value=1,h.value=0,y.value=0},B=l=>{const c=l.deltaY>0?-E:E;a.value=Math.max(O,Math.min(F,a.value+c))},j=l=>{a.value<=1||(k.value=!0,x.value=l.clientX,_.value=l.clientY,document.addEventListener("mousemove",C),document.addEventListener("mouseup",i))},C=l=>{if(!k.value)return;const c=l.clientX-x.value,v=l.clientY-_.value;h.value+=c,y.value+=v,x.value=l.clientX,_.value=l.clientY},i=()=>{k.value=!1,document.removeEventListener("mousemove",C),document.removeEventListener("mouseup",i)},t=()=>{w(),r("close")},s=l=>{l.key==="Escape"&&n.visible&&t()};return H(()=>{document.addEventListener("keydown",s)}),R(()=>{document.removeEventListener("keydown",s),document.removeEventListener("mousemove",C),document.removeEventListener("mouseup",i)}),(l,c)=>(d(),G(Q,{to:"body"},[L(K,{"enter-active-class":"transition-all duration-300 ease-out","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"transition-all duration-200 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:P(()=>[o.visible?(d(),m("div",{key:0,class:"fixed inset-0 z-50 bg-black/80 backdrop-blur-sm",onClick:t},[e("div",tt,[e("img",{ref_key:"imageRef",ref:b,src:o.imageUrl,alt:o.imageAlt,class:"max-w-full max-h-full object-contain transition-transform duration-300 ease-out select-none",style:q({transform:`scale(${a.value}) translate(${h.value}px, ${y.value}px)`}),onClick:c[0]||(c[0]=S(()=>{},["stop"])),onWheel:S(B,["prevent"]),onMousedown:j,onDragstart:c[1]||(c[1]=S(()=>{},["prevent"]))},null,44,st),e("div",ot,[e("button",{onClick:S(u,["stop"]),class:"p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors duration-200",title:"放大"},c[2]||(c[2]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1)])),e("button",{onClick:S(T,["stop"]),class:"p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors duration-200",title:"缩小"},c[3]||(c[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)])),e("button",{onClick:S(w,["stop"]),class:"p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors duration-200",title:"重置"},c[4]||(c[4]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)])),e("button",{onClick:S(t,["stop"]),class:"p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors duration-200",title:"关闭"},c[5]||(c[5]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",at,f(Math.round(a.value*100))+"% ",1)])])):z("",!0)]),_:1})]))}},rt={class:"min-h-screen bg-gray-50 pt-20"},nt={class:"bg-white border-b border-gray-200 shadow-sm min-h-[60px] flex items-center"},it={class:"container-width px-4 py-3 w-full"},ct={class:"flex items-center space-x-2 text-sm","aria-label":"面包屑导航"},dt={class:"flex items-center space-x-2 text-gray-600"},ut={class:"text-gray-900 font-medium"},mt={class:"container-width section-padding py-8"},vt={key:0,class:"flex justify-center items-center py-20"},pt={key:1,class:"flex justify-center items-center py-20"},ht={class:"text-center"},yt={class:"text-secondary-600 mb-4"},ft={key:2},xt={class:"mb-12"},gt={key:0,class:"mb-12"},bt={key:1},wt={__name:"[id]",setup(o){const g=ee(),n=te(),r=re(),a=p(!1),h=p(""),y=p(""),k=$(()=>{const t=r.currentTemplate.value;if(!t)return"/templates";const s=new URLSearchParams;return t.industry&&s.set("industry",t.industry),t.style&&s.set("style",t.style),t.category&&s.set("category",t.category),`/templates?${s.toString()}`}),x=$(()=>"/templates?category=hot"),_=$(()=>{const t=r.currentTemplate.value;if(!t)return"模板详情";const{name:s,industry:l,style:c}=t;return s||`${l||"通用"}-${c||"经典"}`});H(async()=>{await b()}),R(()=>{r.clearData()}),J(()=>g.params.id,async t=>{t&&await b()});const b=async()=>{const t=g.params.id;if(!t){n.push("/templates");return}try{await r.initializePageData(t),u()}catch(s){console.error("初始化页面失败:",s)}},u=()=>{const t=r.currentTemplate.value;if(!t)return;const s=`${r.templateDisplayName.value} - 简历模板详情 - 火花简历`,l=t.description||`专业的${t.industry||"通用"}简历模板，${t.style||"经典"}风格设计，已有${t.usageCount||0}人使用。`;se({title:s,description:l,keywords:`简历模板,${t.industry},${t.style},${t.category},简历设计,求职简历,火花简历`,ogTitle:s,ogDescription:l,ogImage:t.thumbnail,twitterCard:"summary_large_image"})},T=async()=>{await b()},w=t=>{h.value=t.imageUrl,y.value=t.imageAlt,a.value=!0},B=()=>{a.value=!1,h.value="",y.value=""},j=async t=>{try{await r.recordTemplateUsage(t.id),n.push(`/resume/edit?templateId=${t.id}`)}catch(s){console.error("使用模板失败:",s)}},C=t=>{n.push(`/templates/${t.id}`)},i=t=>{console.log("预览模板:",t)};return(t,s)=>{const l=W;return d(),m("div",rt,[e("div",nt,[e("div",it,[e("nav",ct,[e("div",dt,[L(l,{to:"/",class:"hover:text-blue-600 transition-colors duration-200 flex items-center text-blue-500"},{default:P(()=>s[0]||(s[0]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})],-1),D(" 首页 ")])),_:1,__:[0]}),s[2]||(s[2]=e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)),L(l,{to:"/templates",class:"hover:text-blue-600 transition-colors duration-200 text-blue-500"},{default:P(()=>s[1]||(s[1]=[D(" 简历模板库 ")])),_:1,__:[1]}),s[3]||(s[3]=e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)),e("span",ut,f(M(r).currentTemplate.value?_.value:"模板详情"),1)])])])]),e("div",mt,[M(r).isLoading.value?(d(),m("div",vt,s[4]||(s[4]=[e("div",{class:"flex flex-col items-center"},[e("div",{class:"w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mb-4"}),e("p",{class:"text-secondary-600"},"正在加载模板详情...")],-1)]))):M(r).hasError.value?(d(),m("div",pt,[e("div",ht,[s[5]||(s[5]=e("div",{class:"w-16 h-16 bg-danger-100 rounded-full flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-danger-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),s[6]||(s[6]=e("h3",{class:"text-lg font-semibold text-secondary-900 mb-2"},"加载失败",-1)),e("p",yt,f(M(r).error.value),1),e("button",{onClick:T,class:"btn-primary"}," 重试 ")])])):M(r).currentTemplate.value?(d(),m("div",ft,[e("div",xt,[L(Me,{template:M(r).currentTemplate.value,onImageClick:w,onUseTemplate:j},null,8,["template"])]),M(r).similarTemplates.value.length>0?(d(),m("div",gt,[L(Y,{title:"更多相似模板",templates:M(r).similarTemplates.value,"title-link":k.value,"slides-to-show":6,onTemplateClick:C,onUseTemplate:j,onPreview:i},null,8,["templates","title-link"])])):z("",!0),M(r).hotTemplates.value.length>0?(d(),m("div",bt,[L(Y,{title:"更多热门模板",templates:M(r).hotTemplates.value,"title-link":x.value,"slides-to-show":6,onTemplateClick:C,onUseTemplate:j,onPreview:i},null,8,["templates","title-link"])])):z("",!0)])):z("",!0)]),L(lt,{visible:a.value,"image-url":h.value,"image-alt":y.value,onClose:B},null,8,["visible","image-url","image-alt"])])}}},Mt=le(wt,[["__scopeId","data-v-c84955dd"]]);export{Mt as default};
