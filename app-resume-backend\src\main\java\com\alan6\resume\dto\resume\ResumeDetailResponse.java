package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 简历详情响应DTO
 * 
 * @description 返回简历的完整数据，包括所有模块内容和样式设置
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "简历详情响应")
public class ResumeDetailResponse {

    /**
     * 简历ID
     */
    @Schema(description = "简历ID")
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    private Long templateId;

    /**
     * 简历名称
     */
    @Schema(description = "简历名称")
    private String name;

    /**
     * 简历语言
     */
    @Schema(description = "简历语言")
    private String language;

    /**
     * 简历模块数据
     */
    @Schema(description = "简历模块数据")
    private List<ResumeSaveRequest.ModuleData> modules;

    /**
     * 全局设置
     */
    @Schema(description = "全局设置")
    private ResumeSaveRequest.GlobalSettings globalSettings;

    /**
     * 简历状态
     */
    @Schema(description = "简历状态（0-草稿，1-完成）")
    private Integer status;

    /**
     * 浏览次数
     */
    @Schema(description = "浏览次数")
    private Integer viewCount;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数")
    private Integer downloadCount;

    /**
     * 分享次数
     */
    @Schema(description = "分享次数")
    private Integer shareCount;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开（0-私有，1-公开）")
    private Integer isPublic;

    /**
     * 最后保存时间
     */
    @Schema(description = "最后保存时间")
    private LocalDateTime lastSaved;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 模板信息
     */
    @Schema(description = "模板信息")
    private TemplateInfo templateInfo;

    /**
     * 数据版本号
     */
    @Schema(description = "数据版本号")
    private Long version;

    /**
     * 模板信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Schema(description = "模板信息")
    public static class TemplateInfo {
        /**
         * 模板ID
         */
        @Schema(description = "模板ID")
        private Long id;

        /**
         * 模板名称
         */
        @Schema(description = "模板名称")
        private String name;

        /**
         * 模板缩略图
         */
        @Schema(description = "模板缩略图")
        private String thumbnail;

        /**
         * 模板类型
         */
        @Schema(description = "模板类型")
        private String type;

        /**
         * 模板风格
         */
        @Schema(description = "模板风格")
        private String style;
    }
} 