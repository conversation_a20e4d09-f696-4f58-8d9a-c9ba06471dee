<template>
  <div class="save-status">
    <!-- 保存状态指示器 -->
    <div class="status-indicator">
      <!-- 正在保存 -->
      <div v-if="isSaving" class="status-item saving">
        <div class="loading-spinner"></div>
        <span class="status-text">正在保存...</span>
      </div>
      
      <!-- 有未保存更改 -->
      <div v-else-if="isDirty" class="status-item dirty">
        <div class="status-icon unsaved"></div>
        <span class="status-text">有未保存的更改</span>
      </div>
      
      <!-- 已保存 -->
      <div v-else class="status-item saved">
        <div class="status-icon saved-icon"></div>
        <span class="status-text">已保存</span>
      </div>
    </div>
    
    <!-- 最后保存时间 -->
    <div v-if="lastSaved && !isSaving" class="last-saved">
      <span class="time-text">{{ formatLastSaved }}</span>
    </div>
  </div>
</template>

<script setup>
/**
 * 保存状态指示器组件
 * @description 显示简历的保存状态和最后保存时间
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { computed } from 'vue'
import { useTimeFormat } from '~/composables/shared/useTimeFormat'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 是否有未保存的更改
   */
  isDirty: {
    type: Boolean,
    default: false
  },
  
  /**
   * 是否正在保存
   */
  isSaving: {
    type: Boolean,
    default: false
  },
  
  /**
   * 最后保存时间
   */
  lastSaved: {
    type: [String, Date],
    default: null
  }
})

// ================================
// 时间格式化
// ================================
const timeFormat = useTimeFormat()

/**
 * 格式化最后保存时间
 */
const formatLastSaved = computed(() => {
  if (!props.lastSaved) return ''
  
  try {
    const date = new Date(props.lastSaved)
    const now = new Date()
    const diffMs = now - date
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    
    if (diffMinutes < 1) {
      return '刚刚保存'
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前保存`
    } else if (diffMinutes < 24 * 60) {
      const hours = Math.floor(diffMinutes / 60)
      return `${hours}小时前保存`
    } else {
      return timeFormat.formatDate(date, 'MM-DD HH:mm')
    }
  } catch (error) {
    console.error('格式化保存时间失败:', error)
    return '时间格式错误'
  }
})
</script>

<style scoped>
.save-status {
  @apply flex items-center space-x-3 text-sm;
}

.status-indicator {
  @apply flex items-center;
}

.status-item {
  @apply flex items-center space-x-2;
}

/* 保存状态样式 */
.status-item.saving {
  @apply text-blue-600;
}

.status-item.dirty {
  @apply text-orange-600;
}

.status-item.saved {
  @apply text-green-600;
}

.status-text {
  @apply font-medium;
}

/* 状态图标 */
.status-icon {
  @apply w-2 h-2 rounded-full;
}

.status-icon.unsaved {
  @apply bg-orange-400;
  animation: pulse 2s infinite;
}

.status-icon.saved-icon {
  @apply bg-green-400;
}

/* 加载动画 */
.loading-spinner {
  @apply w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin;
}

/* 最后保存时间 */
.last-saved {
  @apply text-gray-500;
}

.time-text {
  @apply text-xs;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .save-status {
    @apply space-x-2;
  }
  
  .status-text {
    @apply hidden;
  }
  
  .time-text {
    @apply hidden;
  }
}
</style> 