<template>
  <div class="admin-sidebar" :class="{ 'collapsed': sidebarCollapsed }">
    <!-- Logo区域 -->
    <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-icon">
          <Icon name="star" class="w-8 h-8 text-blue-500" />
        </div>
        <transition name="fade">
          <span v-show="!sidebarCollapsed" class="logo-text">火花简历</span>
        </transition>
      </div>
    </div>

    <!-- 菜单区域 -->
    <div class="sidebar-menu">
      <nav class="menu-nav">
        <ul class="menu-list">
          <!-- 仪表盘 -->
          <li class="menu-item">
            <NuxtLink 
              to="/admin" 
              class="menu-link"
              :class="{ 'active': isActiveRoute('/admin') }"
              @click="setActiveMenuItem('dashboard')"
            >
              <Icon name="chart-bar" class="menu-icon" />
              <transition name="fade">
                <span v-show="!sidebarCollapsed" class="menu-text">仪表盘</span>
              </transition>
            </NuxtLink>
          </li>

          <!-- 用户管理 -->
          <li class="menu-item">
            <NuxtLink 
              to="/admin/users" 
              class="menu-link"
              :class="{ 'active': isActiveRoute('/admin/users') }"
              @click="setActiveMenuItem('users')"
            >
              <Icon name="users" class="menu-icon" />
              <transition name="fade">
                <span v-show="!sidebarCollapsed" class="menu-text">用户管理</span>
              </transition>
            </NuxtLink>
          </li>

          <!-- 简历管理 -->
          <li class="menu-item">
            <NuxtLink 
              to="/admin/resumes" 
              class="menu-link"
              :class="{ 'active': isActiveRoute('/admin/resumes') }"
              @click="setActiveMenuItem('resumes')"
            >
              <Icon name="document" class="menu-icon" />
              <transition name="fade">
                <span v-show="!sidebarCollapsed" class="menu-text">简历管理</span>
              </transition>
            </NuxtLink>
          </li>

          <!-- 模板管理 -->
          <li class="menu-item has-submenu">
            <div 
              class="menu-link submenu-trigger"
              :class="{ 'active': isTemplateActive }"
              @click="toggleTemplateSubmenu"
            >
              <Icon name="folder" class="menu-icon" />
              <transition name="fade">
                <span v-show="!sidebarCollapsed" class="menu-text">模板管理</span>
              </transition>
              <transition name="fade">
                <Icon 
                  v-show="!sidebarCollapsed"
                  name="arrow-down"
                  class="submenu-arrow" 
                  :class="{ 'rotate-180': !templateSubmenuOpen }"
                />
              </transition>
            </div>

            <!-- 子菜单 -->
            <transition name="submenu">
              <ul v-show="templateSubmenuOpen && !sidebarCollapsed" class="submenu">
                <li class="submenu-item">
                  <NuxtLink 
                    to="/admin/template/list" 
                    class="submenu-link"
                    :class="{ 'active': isActiveRoute('/admin/template/list') }"
                    @click="setActiveMenuItem('template-list')"
                  >
                    <Icon name="document" class="submenu-icon" />
                    <span class="submenu-text">模板列表</span>
                  </NuxtLink>
                </li>
                <li class="submenu-item">
                  <NuxtLink 
                    to="/admin/template/upload" 
                    class="submenu-link"
                    :class="{ 'active': isActiveRoute('/admin/template/upload') }"
                    @click="setActiveMenuItem('template-upload')"
                  >
                    <Icon name="plus" class="submenu-icon" />
                    <span class="submenu-text">模板上传</span>
                  </NuxtLink>
                </li>
                <li class="submenu-item">
                  <NuxtLink 
                    to="/admin/template/categories" 
                    class="submenu-link"
                    :class="{ 'active': isActiveRoute('/admin/template/categories') }"
                    @click="setActiveMenuItem('template-categories')"
                  >
                    <Icon name="folder" class="submenu-icon" />
                    <span class="submenu-text">分类管理</span>
                  </NuxtLink>
                </li>
                <li class="submenu-item">
                  <NuxtLink 
                    to="/admin/template/converter" 
                    class="submenu-link"
                    :class="{ 'active': isActiveRoute('/admin/template/converter') }"
                    @click="setActiveMenuItem('template-converter')"
                  >
                    <Icon name="settings" class="submenu-icon" />
                    <span class="submenu-text">模板转换</span>
                  </NuxtLink>
                </li>
              </ul>
            </transition>
          </li>

          <!-- 订单管理 -->
          <li class="menu-item">
            <NuxtLink 
              to="/admin/orders" 
              class="menu-link"
              :class="{ 'active': isActiveRoute('/admin/orders') }"
              @click="setActiveMenuItem('orders')"
            >
              <Icon name="shopping-cart" class="menu-icon" />
              <transition name="fade">
                <span v-show="!sidebarCollapsed" class="menu-text">订单管理</span>
              </transition>
            </NuxtLink>
          </li>

          <!-- 会员管理 -->
          <li class="menu-item">
            <NuxtLink 
              to="/admin/memberships" 
              class="menu-link"
              :class="{ 'active': isActiveRoute('/admin/memberships') }"
              @click="setActiveMenuItem('memberships')"
            >
              <Icon name="star" class="menu-icon" />
              <transition name="fade">
                <span v-show="!sidebarCollapsed" class="menu-text">会员管理</span>
              </transition>
            </NuxtLink>
          </li>

          <!-- 系统设置 -->
          <li class="menu-item" v-if="isSuperAdmin">
            <NuxtLink 
              to="/admin/settings" 
              class="menu-link"
              :class="{ 'active': isActiveRoute('/admin/settings') }"
              @click="setActiveMenuItem('settings')"
            >
              <Icon name="settings" class="menu-icon" />
              <transition name="fade">
                <span v-show="!sidebarCollapsed" class="menu-text">系统设置</span>
              </transition>
            </NuxtLink>
          </li>
        </ul>
      </nav>
    </div>

    <!-- 折叠按钮 -->
    <div class="sidebar-footer">
      <button 
        @click="toggleSidebar" 
        class="collapse-btn"
        :title="sidebarCollapsed ? '展开侧边栏' : '折叠侧边栏'"
      >
        <Icon 
          v-if="!sidebarCollapsed"
          name="arrow-up"
          class="w-5 h-5" 
        />
        <Icon 
          v-else
          name="arrow-down"
          class="w-5 h-5" 
        />
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useAdminStore } from '~/composables/admin/useAdminStore'
import Icon from '~/components/common/Icon.vue'

const route = useRoute()
const { sidebarCollapsed, isSuperAdmin, toggleSidebar, setActiveMenuItem } = useAdminStore()

// 子菜单展开状态
const templateSubmenuOpen = ref(true) // 默认展开模板子菜单

/**
 * 检查当前路由是否激活
 */
const isActiveRoute = (path) => {
  if (path === '/admin') {
    return route.path === '/admin'
  }
  return route.path.startsWith(path)
}

/**
 * 检查模板管理菜单是否激活
 */
const isTemplateActive = computed(() => {
  return route.path.startsWith('/admin/template')
})

/**
 * 切换模板子菜单
 */
const toggleTemplateSubmenu = () => {
  if (!sidebarCollapsed.value) {
    templateSubmenuOpen.value = !templateSubmenuOpen.value
  }
}

/**
 * 监听路由变化，自动展开相关子菜单
 */
watch(route, (newRoute) => {
  if (newRoute.path.startsWith('/admin/template')) {
    templateSubmenuOpen.value = true
  }
}, { immediate: true })

/**
 * 监听侧边栏折叠状态，折叠时关闭所有子菜单
 */
watch(sidebarCollapsed, (collapsed) => {
  if (collapsed) {
    templateSubmenuOpen.value = false
  }
})


</script>

<style scoped>
.admin-sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.admin-sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  flex-shrink: 0;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  white-space: nowrap;
}

.sidebar-menu {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  margin-bottom: 4px;
}

.menu-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #cbd5e1;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0;
  position: relative;
}

.menu-link:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.menu-link.active {
  background-color: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
  border-right: 3px solid #3b82f6;
}

.menu-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.menu-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

/* 子菜单样式 */
.has-submenu .submenu-trigger {
  cursor: pointer;
  user-select: none;
}

.submenu-arrow {
  width: 16px;
  height: 16px;
  margin-left: auto;
  transition: transform 0.2s ease;
}

.submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.submenu-item {
  margin: 0;
}

.submenu-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px 10px 52px;
  color: #94a3b8;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 13px;
  border-radius: 0;
}

.submenu-link:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.submenu-link.active {
  background-color: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
  border-right: 3px solid #3b82f6;
}

.submenu-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.submenu-text {
  white-space: nowrap;
}

/* 子菜单过渡动画 */
.submenu-enter-active,
.submenu-leave-active {
  transition: all 0.3s ease;
  max-height: 200px;
}

.submenu-enter-from,
.submenu-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.collapse-btn {
  width: 100%;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  color: #cbd5e1;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }
  
  .admin-sidebar.mobile-open {
    transform: translateX(0);
  }
}
</style> 