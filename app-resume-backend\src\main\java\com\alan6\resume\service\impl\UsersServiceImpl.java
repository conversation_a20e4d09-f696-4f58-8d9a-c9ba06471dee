package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.user.*;
import com.alan6.resume.entity.Users;
import com.alan6.resume.entity.UserFavorites;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.entity.Resumes;
import com.alan6.resume.mapper.UsersMapper;
import com.alan6.resume.service.IUsersService;
import com.alan6.resume.service.IUserFavoritesService;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.IResumeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户表服务实现类
 * 
 * 主要功能：
 * 1. 实现用户基础CRUD操作
 * 2. 用户信息管理和头像上传
 * 3. 密码修改和验证功能
 * 4. 用户设置管理
 * 5. 手机号和邮箱绑定管理
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
public class UsersServiceImpl extends ServiceImpl<UsersMapper, Users> implements IUsersService {

    /**
     * 用户收藏服务
     */
    @Autowired
    private IUserFavoritesService userFavoritesService;

    /**
     * 简历模板服务
     */
    @Autowired
    private IResumeTemplatesService resumeTemplatesService;

    /**
     * 简历服务
     */
    @Autowired
    private IResumeService resumeService;

    @Override
    public UserProfileResponse getUserProfile(Long userId) {
        // 查询用户信息
        Users user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 构建响应对象
        return UserProfileResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(UserProfileResponse.maskEmail(user.getEmail()))
                .phone(UserProfileResponse.maskPhone(user.getPhone()))
                .nickname(user.getNickname())
                .avatarUrl(user.getAvatarUrl())
                .gender(user.getGender())
                .birthday(user.getBirthday())
                .registerType(user.getRegisterType())
                .registerPlatform(user.getRegisterPlatform())
                .preferredLanguage(user.getPreferredLanguage())
                .status(user.getStatus())
                .isPhoneVerified(user.getIsPhoneVerified())
                .createTime(user.getCreateTime())
                .lastLoginTime(user.getLastLoginTime())
                .lastLoginIp(user.getLastLoginIp())
                .lastLoginPlatform(user.getLastLoginPlatform())
                .membership(UserProfileResponse.MembershipInfo.builder()
                        .level(0) // 暂时返回普通用户
                        .expireTime(null)
                        .usedResumeCount(0)
                        .usedExportCount(0)
                        .build())
                .build();
    }

    @Override
    public boolean updateUserProfile(Long userId, UserProfileRequest request) {
        // 查询用户是否存在
        Users user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 构建更新对象
        Users updateUser = new Users();
        updateUser.setId(userId);
        updateUser.setUpdateTime(LocalDateTime.now());

        // 只更新非空字段
        if (request.getNickname() != null) {
            updateUser.setNickname(request.getNickname());
        }
        if (request.getGender() != null) {
            updateUser.setGender(request.getGender());
        }
        if (request.getBirthday() != null) {
            updateUser.setBirthday(request.getBirthday());
        }
        if (request.getPreferredLanguage() != null) {
            updateUser.setPreferredLanguage(request.getPreferredLanguage());
        }

        return updateById(updateUser);
    }

    @Override
    public AvatarUploadResponse uploadAvatar(Long userId, MultipartFile file) {
        // TODO: 实现文件上传逻辑
        // 这里暂时抛出异常，实际需要实现文件存储逻辑
        throw new BusinessException("头像上传功能暂未实现");
    }

    @Override
    public boolean changePassword(Long userId, PasswordChangeRequest request) {
        // TODO: 实现密码修改逻辑
        // 需要加密处理和验证逻辑
        throw new BusinessException("密码修改功能暂未实现");
    }

    @Override
    public UserSettingsResponse getUserSettings(Long userId) {
        Users user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        return UserSettingsResponse.create(user.getPreferredLanguage());
    }

    @Override
    public boolean updateUserSettings(Long userId, UserSettingsRequest request) {
        // 查询用户是否存在
        Users user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 构建更新对象
        Users updateUser = new Users();
        updateUser.setId(userId);
        updateUser.setPreferredLanguage(request.getPreferredLanguage());
        updateUser.setUpdateTime(LocalDateTime.now());

        return updateById(updateUser);
    }

    @Override
    public boolean bindPhone(Long userId, PhoneBindRequest request) {
        // TODO: 实现手机号绑定逻辑
        throw new BusinessException("手机号绑定功能暂未实现");
    }

    @Override
    public boolean unbindPhone(Long userId, PhoneBindRequest request) {
        // TODO: 实现手机号解绑逻辑
        throw new BusinessException("手机号解绑功能暂未实现");
    }

    @Override
    public boolean bindEmail(Long userId, EmailBindRequest request) {
        // TODO: 实现邮箱绑定逻辑
        throw new BusinessException("邮箱绑定功能暂未实现");
    }

    @Override
    public boolean unbindEmail(Long userId, EmailBindRequest request) {
        // TODO: 实现邮箱解绑逻辑
        throw new BusinessException("邮箱解绑功能暂未实现");
    }

    @Override
    public boolean deactivateAccount(Long userId, AccountDeactivateRequest request) {
        // TODO: 实现账号注销逻辑
        throw new BusinessException("账号注销功能暂未实现");
    }

    @Override
    public boolean verifyPassword(Long userId, String password) {
        // TODO: 实现密码验证逻辑
        return false;
    }

    @Override
    public boolean verifySmsCode(String phone, String smsCode) {
        // TODO: 实现短信验证码验证逻辑
        return false;
    }

    @Override
    public boolean verifyEmailCode(String email, String emailCode) {
        // TODO: 实现邮箱验证码验证逻辑
        return false;
    }

    // ======================== 收藏管理相关方法实现 ========================

    @Override
    public Page<UserFavoriteResponse> getUserFavorites(Long userId, Byte targetType, Long current, Long size) {
        // 检查用户是否存在
        Users user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 构建查询条件
        LambdaQueryWrapper<UserFavorites> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFavorites::getUserId, userId)
                .eq(UserFavorites::getIsDeleted, 0);
        
        // 如果指定了收藏类型，添加类型筛选
        if (targetType != null) {
            queryWrapper.eq(UserFavorites::getTargetType, targetType);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(UserFavorites::getCreateTime);

        // 分页查询收藏记录
        Page<UserFavorites> favoritePage = new Page<>(current, size);
        Page<UserFavorites> favoriteResult = userFavoritesService.page(favoritePage, queryWrapper);

        // 转换为响应对象
        List<UserFavoriteResponse> responseList = favoriteResult.getRecords().stream()
                .map(this::convertToFavoriteResponse)
                .collect(Collectors.toList());

        // 构建分页响应
        Page<UserFavoriteResponse> responsePage = new Page<>();
        responsePage.setRecords(responseList);
        responsePage.setTotal(favoriteResult.getTotal());
        responsePage.setCurrent(favoriteResult.getCurrent());
        responsePage.setSize(favoriteResult.getSize());
        responsePage.setPages(favoriteResult.getPages());

        return responsePage;
    }

    @Override
    public boolean addFavorite(Long userId, UserFavoriteRequest request) {
        // 检查用户是否存在
        Users user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 校验收藏类型
        if (!request.isTargetTypeValid()) {
            throw new BusinessException("收藏类型无效");
        }

        // 检查目标对象是否存在
        if (request.isTemplateTarget()) {
            // 检查模板是否存在
            ResumeTemplates template = resumeTemplatesService.getById(request.getTargetId());
            if (template == null || template.getIsDeleted() == 1) {
                throw new BusinessException("模板不存在或已删除");
            }
        } else if (request.isResumeTarget()) {
            // 检查简历是否存在
            Resumes resume = resumeService.getById(request.getTargetId());
            if (resume == null || resume.getIsDeleted() == 1) {
                throw new BusinessException("简历不存在或已删除");
            }
        }

        // 检查是否已经收藏
        LambdaQueryWrapper<UserFavorites> existQuery = new LambdaQueryWrapper<>();
        existQuery.eq(UserFavorites::getUserId, userId)
                .eq(UserFavorites::getTargetType, request.getTargetType())
                .eq(UserFavorites::getTargetId, request.getTargetId())
                .eq(UserFavorites::getIsDeleted, 0);
        
        UserFavorites existFavorite = userFavoritesService.getOne(existQuery);
        if (existFavorite != null) {
            throw new BusinessException("已经收藏过该内容");
        }

        // 创建收藏记录
        UserFavorites favorite = new UserFavorites();
        favorite.setUserId(userId);
        favorite.setTargetType(request.getTargetType());
        favorite.setTargetId(request.getTargetId());
        favorite.setIsDeleted((byte) 0);
        favorite.setCreateTime(LocalDateTime.now());
        favorite.setUpdateTime(LocalDateTime.now());

        return userFavoritesService.save(favorite);
    }

    @Override
    public boolean removeFavorite(Long userId, Long favoriteId) {
        // 检查用户是否存在
        Users user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 查询收藏记录
        UserFavorites favorite = userFavoritesService.getById(favoriteId);
        if (favorite == null || favorite.getIsDeleted() == 1) {
            throw new BusinessException("收藏记录不存在");
        }

        // 检查权限：只能删除自己的收藏
        if (!favorite.getUserId().equals(userId)) {
            throw new BusinessException("无权限删除该收藏");
        }

        // 软删除收藏记录
        favorite.setIsDeleted((byte) 1);
        favorite.setUpdateTime(LocalDateTime.now());

        return userFavoritesService.updateById(favorite);
    }

    /**
     * 将收藏实体转换为响应DTO
     * 
     * 根据收藏类型查询对应的目标对象信息，构建完整的响应数据
     * 
     * @param favorite 收藏实体
     * @return 收藏响应DTO
     */
    private UserFavoriteResponse convertToFavoriteResponse(UserFavorites favorite) {
        if (favorite.getTargetType() == 1) {
            // 模板收藏
            ResumeTemplates template = resumeTemplatesService.getById(favorite.getTargetId());
            if (template != null && template.getIsDeleted() == 0) {
                return UserFavoriteResponse.createTemplateResponse(
                        favorite.getId(),
                        template.getId(),
                        template.getName(),
                        template.getPreviewImageUrl(),
                        template.getDescription(),
                        template.getIsPremium() == 1,
                        favorite.getCreateTime()
                );
            } else {
                // 模板已删除，返回基础信息
                return UserFavoriteResponse.createTemplateResponse(
                        favorite.getId(),
                        favorite.getTargetId(),
                        "模板已删除",
                        null,
                        "该模板已被删除",
                        false,
                        favorite.getCreateTime()
                );
            }
        } else if (favorite.getTargetType() == 2) {
            // 简历收藏
            Resumes resume = resumeService.getById(favorite.getTargetId());
            if (resume != null && resume.getIsDeleted() == 0) {
                return UserFavoriteResponse.createResumeResponse(
                        favorite.getId(),
                        resume.getId(),
                        resume.getName(),
                        null, // 简历暂时没有预览图
                        "简历：" + resume.getName(),
                        favorite.getCreateTime()
                );
            } else {
                // 简历已删除，返回基础信息
                return UserFavoriteResponse.createResumeResponse(
                        favorite.getId(),
                        favorite.getTargetId(),
                        "简历已删除",
                        null,
                        "该简历已被删除",
                        favorite.getCreateTime()
                );
            }
        } else {
            // 未知类型，返回基础信息
            UserFavoriteResponse response = new UserFavoriteResponse();
            response.setId(favorite.getId());
            response.setTargetType(favorite.getTargetType());
            response.setTargetId(favorite.getTargetId());
            response.setTargetName("未知类型");
            response.setDescription("未知的收藏类型");
            response.setCreateTime(favorite.getCreateTime());
            return response;
        }
    }
}
