package com.alan6.resume.service.impl;

import com.alan6.resume.entity.ResumeTemplateContent;
import com.alan6.resume.mapper.ResumeTemplateContentMapper;
import com.alan6.resume.service.IResumeTemplateContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 简历模板内容服务实现类
 * 
 * @description 实现简历模板内容的业务逻辑处理
 *              提供内容的增删改查、智能推荐等功能
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeTemplateContentServiceImpl extends ServiceImpl<ResumeTemplateContentMapper, ResumeTemplateContent> 
        implements IResumeTemplateContentService {

    private final ResumeTemplateContentMapper contentMapper;

    /**
     * 根据内容代码获取内容
     * 
     * @param code 内容代码
     * @return 匹配的模板内容
     */
    @Override
    public ResumeTemplateContent getByCode(String code) {
        if (!StringUtils.hasText(code)) {
            log.warn("内容代码不能为空");
            return null;
        }

        try {
            ResumeTemplateContent content = contentMapper.selectByCode(code);
            if (content == null) {
                log.info("未找到代码为 {} 的模板内容", code);
            }
            return content;
        } catch (Exception e) {
            log.error("根据代码获取模板内容失败，代码: {}, 错误: {}", code, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据行业获取推荐内容列表
     * 
     * @param industry 行业名称
     * @return 推荐的内容列表
     */
    @Override
    public List<ResumeTemplateContent> getRecommendedByIndustry(String industry) {
        if (!StringUtils.hasText(industry)) {
            log.warn("行业名称不能为空");
            return new ArrayList<>();
        }

        try {
            List<ResumeTemplateContent> contentList = contentMapper.selectByIndustry(industry);
            log.info("根据行业 {} 获取到 {} 个推荐内容", industry, contentList.size());
            return contentList;
        } catch (Exception e) {
            log.error("根据行业获取推荐内容失败，行业: {}, 错误: {}", industry, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据职位获取推荐内容列表
     * 
     * @param position 职位名称
     * @return 推荐的内容列表
     */
    @Override
    public List<ResumeTemplateContent> getRecommendedByPosition(String position) {
        if (!StringUtils.hasText(position)) {
            log.warn("职位名称不能为空");
            return new ArrayList<>();
        }

        try {
            List<ResumeTemplateContent> contentList = contentMapper.selectByPosition(position);
            log.info("根据职位 {} 获取到 {} 个推荐内容", position, contentList.size());
            return contentList;
        } catch (Exception e) {
            log.error("根据职位获取推荐内容失败，职位: {}, 错误: {}", position, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据行业和职位获取最佳匹配内容
     * 
     * @param industry 行业名称
     * @param position 职位名称
     * @return 最佳匹配的内容列表
     */
    @Override
    public List<ResumeTemplateContent> getBestMatchContent(String industry, String position) {
        if (!StringUtils.hasText(industry) && !StringUtils.hasText(position)) {
            log.warn("行业和职位不能同时为空");
            return getAllAvailable();
        }

        try {
            List<ResumeTemplateContent> contentList = contentMapper.selectByIndustryAndPosition(industry, position);
            log.info("根据行业 {} 和职位 {} 获取到 {} 个最佳匹配内容", industry, position, contentList.size());
            return contentList;
        } catch (Exception e) {
            log.error("根据行业和职位获取最佳匹配内容失败，行业: {}, 职位: {}, 错误: {}", 
                    industry, position, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据语言获取内容列表
     * 
     * @param language 语言代码
     * @return 匹配的内容列表
     */
    @Override
    public List<ResumeTemplateContent> getByLanguage(String language) {
        if (!StringUtils.hasText(language)) {
            // 如果语言为空，默认返回中文内容
            language = ResumeTemplateContent.DEFAULT_LANGUAGE;
        }

        try {
            List<ResumeTemplateContent> contentList = contentMapper.selectByLanguage(language);
            log.info("根据语言 {} 获取到 {} 个内容", language, contentList.size());
            return contentList;
        } catch (Exception e) {
            log.error("根据语言获取内容失败，语言: {}, 错误: {}", language, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有可用内容
     * 
     * @return 所有可用的内容列表
     */
    @Override
    public List<ResumeTemplateContent> getAllAvailable() {
        try {
            List<ResumeTemplateContent> contentList = contentMapper.selectAllEnabled();
            log.info("获取到 {} 个可用内容", contentList.size());
            return contentList;
        } catch (Exception e) {
            log.error("获取所有可用内容失败，错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建新的模板内容
     * 
     * @param content 要创建的内容对象
     * @return 创建成功返回true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createContent(ResumeTemplateContent content) {
        if (content == null) {
            log.warn("创建内容对象不能为空");
            return false;
        }

        // 业务验证
        if (!validateContentForCreate(content)) {
            return false;
        }

        try {
            // 设置默认值
            setDefaultValues(content);
            
            // 保存内容
            boolean result = this.save(content);
            if (result) {
                log.info("创建模板内容成功，ID: {}, 代码: {}", content.getId(), content.getCode());
            } else {
                log.warn("创建模板内容失败，代码: {}", content.getCode());
            }
            return result;
        } catch (Exception e) {
            log.error("创建模板内容异常，代码: {}, 错误: {}", content.getCode(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新模板内容
     * 
     * @param content 要更新的内容对象
     * @return 更新成功返回true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContent(ResumeTemplateContent content) {
        if (content == null || content.getId() == null) {
            log.warn("更新内容对象或ID不能为空");
            return false;
        }

        // 业务验证
        if (!validateContentForUpdate(content)) {
            return false;
        }

        try {
            // 设置更新时间
            content.setUpdateTime(LocalDateTime.now());
            
            // 更新内容
            boolean result = this.updateById(content);
            if (result) {
                log.info("更新模板内容成功，ID: {}, 代码: {}", content.getId(), content.getCode());
            } else {
                log.warn("更新模板内容失败，ID: {}", content.getId());
            }
            return result;
        } catch (Exception e) {
            log.error("更新模板内容异常，ID: {}, 错误: {}", content.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除模板内容
     * 
     * @param contentId 内容ID
     * @return 删除成功返回true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContent(Long contentId) {
        if (contentId == null) {
            log.warn("删除内容ID不能为空");
            return false;
        }

        try {
            // 检查内容是否存在
            ResumeTemplateContent content = this.getById(contentId);
            if (content == null) {
                log.warn("要删除的内容不存在，ID: {}", contentId);
                return false;
            }

            // 执行删除
            boolean result = this.removeById(contentId);
            if (result) {
                log.info("删除模板内容成功，ID: {}, 代码: {}", contentId, content.getCode());
            } else {
                log.warn("删除模板内容失败，ID: {}", contentId);
            }
            return result;
        } catch (Exception e) {
            log.error("删除模板内容异常，ID: {}, 错误: {}", contentId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 启用模板内容
     * 
     * @param contentId 内容ID
     * @return 操作成功返回true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableContent(Long contentId) {
        return updateContentStatus(contentId, ResumeTemplateContent.STATUS_ENABLED);
    }

    /**
     * 禁用模板内容
     * 
     * @param contentId 内容ID
     * @return 操作成功返回true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableContent(Long contentId) {
        return updateContentStatus(contentId, ResumeTemplateContent.STATUS_DISABLED);
    }

    /**
     * 验证内容代码是否可用
     * 
     * @param code 内容代码
     * @return 如果可用返回true
     */
    @Override
    public boolean isCodeAvailable(String code) {
        if (!StringUtils.hasText(code)) {
            return false;
        }

        try {
            boolean exists = contentMapper.existsByCode(code);
            return !exists;
        } catch (Exception e) {
            log.error("验证内容代码可用性失败，代码: {}, 错误: {}", code, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取所有行业列表
     * 
     * @return 行业列表
     */
    @Override
    public List<String> getAllIndustries() {
        try {
            List<String> industries = contentMapper.selectDistinctIndustries();
            log.info("获取到 {} 个行业类型", industries.size());
            return industries;
        } catch (Exception e) {
            log.error("获取行业列表失败，错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有职位列表
     * 
     * @return 职位列表
     */
    @Override
    public List<String> getAllPositions() {
        try {
            List<String> positions = contentMapper.selectDistinctPositions();
            log.info("获取到 {} 个职位类型", positions.size());
            return positions;
        } catch (Exception e) {
            log.error("获取职位列表失败，错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 统计行业内容数量
     * 
     * @param industry 行业名称
     * @return 内容数量
     */
    @Override
    public int countByIndustry(String industry) {
        if (!StringUtils.hasText(industry)) {
            return 0;
        }

        try {
            return contentMapper.countByIndustry(industry);
        } catch (Exception e) {
            log.error("统计行业内容数量失败，行业: {}, 错误: {}", industry, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 统计职位内容数量
     * 
     * @param position 职位名称
     * @return 内容数量
     */
    @Override
    public int countByPosition(String position) {
        if (!StringUtils.hasText(position)) {
            return 0;
        }

        try {
            return contentMapper.countByPosition(position);
        } catch (Exception e) {
            log.error("统计职位内容数量失败，职位: {}, 错误: {}", position, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 复制内容
     * 
     * @param sourceId 源内容ID
     * @param newCode 新内容代码
     * @param newName 新内容名称
     * @return 复制成功返回新内容对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResumeTemplateContent copyContent(Long sourceId, String newCode, String newName) {
        if (sourceId == null || !StringUtils.hasText(newCode) || !StringUtils.hasText(newName)) {
            log.warn("复制内容参数不完整");
            return null;
        }

        try {
            // 获取源内容
            ResumeTemplateContent sourceContent = this.getById(sourceId);
            if (sourceContent == null) {
                log.warn("源内容不存在，ID: {}", sourceId);
                return null;
            }

            // 检查新代码是否可用
            if (!isCodeAvailable(newCode)) {
                log.warn("新内容代码已存在，代码: {}", newCode);
                return null;
            }

            // 创建新内容
            ResumeTemplateContent newContent = new ResumeTemplateContent();
            newContent.setName(newName);
            newContent.setCode(newCode);
            newContent.setDescription(sourceContent.getDescription());
            newContent.setContentData(sourceContent.getContentData());
            newContent.setIndustry(sourceContent.getIndustry());
            newContent.setPosition(sourceContent.getPosition());
            newContent.setLanguage(sourceContent.getLanguage());
            newContent.setVersion(ResumeTemplateContent.DEFAULT_VERSION);
            newContent.setStatus(ResumeTemplateContent.STATUS_ENABLED);
            newContent.setCreateTime(LocalDateTime.now());
            newContent.setUpdateTime(LocalDateTime.now());

            // 保存新内容
            boolean result = this.save(newContent);
            if (result) {
                log.info("复制内容成功，源ID: {}, 新ID: {}, 新代码: {}", sourceId, newContent.getId(), newCode);
                return newContent;
            } else {
                log.warn("复制内容失败，源ID: {}, 新代码: {}", sourceId, newCode);
                return null;
            }
        } catch (Exception e) {
            log.error("复制内容异常，源ID: {}, 新代码: {}, 错误: {}", sourceId, newCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 批量导入内容
     * 
     * @param contentList 要导入的内容列表
     * @return 导入成功的数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchImportContent(List<ResumeTemplateContent> contentList) {
        if (CollectionUtils.isEmpty(contentList)) {
            log.warn("导入内容列表为空");
            return 0;
        }

        int successCount = 0;
        for (ResumeTemplateContent content : contentList) {
            try {
                // 验证内容
                if (!validateContentForCreate(content)) {
                    log.warn("内容验证失败，跳过导入，代码: {}", content.getCode());
                    continue;
                }

                // 设置默认值
                setDefaultValues(content);

                // 保存内容
                boolean result = this.save(content);
                if (result) {
                    successCount++;
                    log.info("导入内容成功，代码: {}", content.getCode());
                } else {
                    log.warn("导入内容失败，代码: {}", content.getCode());
                }
            } catch (Exception e) {
                log.error("导入内容异常，代码: {}, 错误: {}", content.getCode(), e.getMessage(), e);
            }
        }

        log.info("批量导入内容完成，总数: {}, 成功: {}", contentList.size(), successCount);
        return successCount;
    }

    // ================================
    // 私有辅助方法
    // ================================

    /**
     * 验证内容对象用于创建
     * 
     * @param content 内容对象
     * @return 验证通过返回true
     */
    private boolean validateContentForCreate(ResumeTemplateContent content) {
        // 验证必填字段
        if (!StringUtils.hasText(content.getName())) {
            log.warn("内容名称不能为空");
            return false;
        }

        if (!StringUtils.hasText(content.getCode())) {
            log.warn("内容代码不能为空");
            return false;
        }

        if (content.getContentData() == null) {
            log.warn("内容数据不能为空");
            return false;
        }

        // 验证代码唯一性
        if (!isCodeAvailable(content.getCode())) {
            log.warn("内容代码已存在，代码: {}", content.getCode());
            return false;
        }

        return true;
    }

    /**
     * 验证内容对象用于更新
     * 
     * @param content 内容对象
     * @return 验证通过返回true
     */
    private boolean validateContentForUpdate(ResumeTemplateContent content) {
        // 验证必填字段
        if (!StringUtils.hasText(content.getName())) {
            log.warn("内容名称不能为空");
            return false;
        }

        if (content.getContentData() == null) {
            log.warn("内容数据不能为空");
            return false;
        }

        // 验证内容是否存在
        ResumeTemplateContent existingContent = this.getById(content.getId());
        if (existingContent == null) {
            log.warn("要更新的内容不存在，ID: {}", content.getId());
            return false;
        }

        // 如果代码有变化，验证新代码的唯一性
        if (StringUtils.hasText(content.getCode()) && 
            !content.getCode().equals(existingContent.getCode())) {
            if (!isCodeAvailable(content.getCode())) {
                log.warn("新内容代码已存在，代码: {}", content.getCode());
                return false;
            }
        }

        return true;
    }

    /**
     * 设置默认值
     * 
     * @param content 内容对象
     */
    private void setDefaultValues(ResumeTemplateContent content) {
        if (!StringUtils.hasText(content.getLanguage())) {
            content.setLanguage(ResumeTemplateContent.DEFAULT_LANGUAGE);
        }

        if (!StringUtils.hasText(content.getVersion())) {
            content.setVersion(ResumeTemplateContent.DEFAULT_VERSION);
        }

        if (content.getStatus() == null) {
            content.setStatus(ResumeTemplateContent.STATUS_ENABLED);
        }

        LocalDateTime now = LocalDateTime.now();
        if (content.getCreateTime() == null) {
            content.setCreateTime(now);
        }
        content.setUpdateTime(now);
    }

    /**
     * 更新内容状态
     * 
     * @param contentId 内容ID
     * @param status 新状态
     * @return 更新成功返回true
     */
    private boolean updateContentStatus(Long contentId, Integer status) {
        if (contentId == null) {
            log.warn("内容ID不能为空");
            return false;
        }

        try {
            ResumeTemplateContent content = this.getById(contentId);
            if (content == null) {
                log.warn("内容不存在，ID: {}", contentId);
                return false;
            }

            content.setStatus(status);
            content.setUpdateTime(LocalDateTime.now());

            boolean result = this.updateById(content);
            if (result) {
                String statusText = ResumeTemplateContent.STATUS_ENABLED.equals(status) ? "启用" : "禁用";
                log.info("{}内容成功，ID: {}, 代码: {}", statusText, contentId, content.getCode());
            } else {
                log.warn("更新内容状态失败，ID: {}", contentId);
            }
            return result;
        } catch (Exception e) {
            log.error("更新内容状态异常，ID: {}, 状态: {}, 错误: {}", contentId, status, e.getMessage(), e);
            return false;
        }
    }
} 