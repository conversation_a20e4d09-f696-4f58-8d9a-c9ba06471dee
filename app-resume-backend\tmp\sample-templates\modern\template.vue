<template>
  <div class="resume-template modern-template">
    <!-- 简历头部 -->
    <div class="resume-header">
      <div class="personal-info">
        <h1 class="name">{{ resumeData.basicInfo?.name || '姓名' }}</h1>
        <h2 class="title">{{ resumeData.basicInfo?.title || '职位标题' }}</h2>
        <div class="contact-info">
          <div v-if="resumeData.basicInfo?.email" class="contact-item">
            <i class="icon-email"></i>
            <span>{{ resumeData.basicInfo.email }}</span>
          </div>
          <div v-if="resumeData.basicInfo?.phone" class="contact-item">
            <i class="icon-phone"></i>
            <span>{{ resumeData.basicInfo.phone }}</span>
          </div>
          <div v-if="resumeData.basicInfo?.address" class="contact-item">
            <i class="icon-location"></i>
            <span>{{ resumeData.basicInfo.address }}</span>
          </div>
          <div v-if="resumeData.basicInfo?.website" class="contact-item">
            <i class="icon-website"></i>
            <a :href="resumeData.basicInfo.website" target="_blank">{{ resumeData.basicInfo.website }}</a>
          </div>
        </div>
      </div>
      <div v-if="resumeData.basicInfo?.photo" class="photo-container">
        <img :src="resumeData.basicInfo.photo" :alt="resumeData.basicInfo.name" class="profile-photo" />
      </div>
    </div>

    <!-- 个人简介 -->
    <div v-if="resumeData.selfEvaluation?.content" class="resume-section">
      <h3 class="section-title">个人简介</h3>
      <div class="section-content">
        <p class="summary">{{ resumeData.selfEvaluation.content }}</p>
      </div>
    </div>

    <!-- 工作经历 -->
    <div v-if="resumeData.workExperiences && resumeData.workExperiences.length > 0" class="resume-section">
      <h3 class="section-title">工作经历</h3>
      <div class="section-content">
        <div v-for="exp in resumeData.workExperiences" :key="exp.id" class="experience-item">
          <div class="item-header">
            <h4 class="company-position">{{ exp.company }} - {{ exp.position }}</h4>
            <span class="duration">{{ exp.startDate }} - {{ exp.endDate }}</span>
          </div>
          <div v-if="exp.location" class="location">{{ exp.location }}</div>
          <div v-if="exp.description" class="description">{{ exp.description }}</div>
          <div v-if="exp.achievements && exp.achievements.length > 0" class="achievements">
            <ul>
              <li v-for="achievement in exp.achievements" :key="achievement">{{ achievement }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目经历 -->
    <div v-if="resumeData.projects && resumeData.projects.length > 0" class="resume-section">
      <h3 class="section-title">项目经历</h3>
      <div class="section-content">
        <div v-for="project in resumeData.projects" :key="project.id" class="project-item">
          <div class="item-header">
            <h4 class="project-name">{{ project.name }}</h4>
            <span class="duration">{{ project.startDate }} - {{ project.endDate }}</span>
          </div>
          <div v-if="project.role" class="role">角色：{{ project.role }}</div>
          <div v-if="project.technology" class="technology">技术栈：{{ project.technology }}</div>
          <div v-if="project.description" class="description">{{ project.description }}</div>
          <div v-if="project.url" class="project-url">
            <a :href="project.url" target="_blank">项目链接</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 教育背景 -->
    <div v-if="resumeData.educations && resumeData.educations.length > 0" class="resume-section">
      <h3 class="section-title">教育背景</h3>
      <div class="section-content">
        <div v-for="edu in resumeData.educations" :key="edu.id" class="education-item">
          <div class="item-header">
            <h4 class="school-major">{{ edu.school }} - {{ edu.major }}</h4>
            <span class="duration">{{ edu.startDate }} - {{ edu.endDate }}</span>
          </div>
          <div class="degree">{{ edu.degree }}</div>
          <div v-if="edu.gpa" class="gpa">GPA: {{ edu.gpa }}</div>
          <div v-if="edu.description" class="description">{{ edu.description }}</div>
        </div>
      </div>
    </div>

    <!-- 技能特长 -->
    <div v-if="resumeData.skills && resumeData.skills.length > 0" class="resume-section">
      <h3 class="section-title">技能特长</h3>
      <div class="section-content">
        <div class="skills-grid">
          <div v-for="skill in resumeData.skills" :key="skill.name" class="skill-item">
            <div class="skill-header">
              <span class="skill-name">{{ skill.name }}</span>
              <span class="skill-level-text">{{ getSkillLevelText(skill.level) }}</span>
            </div>
            <div class="skill-bar">
              <div class="skill-progress" :style="{ width: skill.level + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 获奖证书 -->
    <div v-if="resumeData.awards && resumeData.awards.length > 0" class="resume-section">
      <h3 class="section-title">获奖证书</h3>
      <div class="section-content">
        <div v-for="award in resumeData.awards" :key="award.id" class="award-item">
          <div class="item-header">
            <h4 class="award-name">{{ award.name }}</h4>
            <span class="award-date">{{ award.date }}</span>
          </div>
          <div v-if="award.issuer" class="issuer">颁发机构：{{ award.issuer }}</div>
          <div v-if="award.description" class="description">{{ award.description }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModernTemplate',
  props: {
    resumeData: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    /**
     * 获取技能等级文本
     * @param {number} level 技能等级（0-100）
     * @returns {string} 等级文本
     */
    getSkillLevelText(level) {
      if (level >= 90) return '精通'
      if (level >= 80) return '熟练'
      if (level >= 70) return '良好'
      if (level >= 60) return '一般'
      return '入门'
    }
  }
}
</script>

<style scoped>
/* 现代简约模板样式 */
.resume-template {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #fff;
}

/* 简历头部 */
.resume-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 3px solid #3B82F6;
}

.personal-info {
  flex: 1;
}

.name {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1F2937;
  letter-spacing: -0.025em;
}

.title {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 20px 0;
  color: #3B82F6;
}

.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: #6B7280;
}

.contact-item a {
  color: #3B82F6;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.photo-container {
  margin-left: 30px;
}

.profile-photo {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  object-fit: cover;
  border: 3px solid #E5E7EB;
}

/* 图标样式 */
.icon-email::before { content: "✉"; }
.icon-phone::before { content: "📞"; }
.icon-location::before { content: "📍"; }
.icon-website::before { content: "🌐"; }

/* 简历章节 */
.resume-section {
  margin-bottom: 35px;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #1F2937;
  position: relative;
  padding-left: 20px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: #3B82F6;
  border-radius: 2px;
}

.section-content {
  margin-left: 20px;
}

/* 个人简介 */
.summary {
  font-size: 1rem;
  line-height: 1.7;
  color: #4B5563;
  margin: 0;
}

/* 工作经历 */
.experience-item,
.project-item,
.education-item,
.award-item {
  margin-bottom: 25px;
  padding: 20px;
  background: #F9FAFB;
  border-radius: 8px;
  border-left: 4px solid #3B82F6;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.company-position,
.project-name,
.school-major,
.award-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: #1F2937;
  flex: 1;
}

.duration,
.award-date {
  font-size: 0.9rem;
  color: #6B7280;
  font-weight: 500;
  white-space: nowrap;
  margin-left: 15px;
}

.location,
.role,
.technology,
.degree,
.gpa,
.issuer {
  font-size: 0.9rem;
  color: #6B7280;
  margin-bottom: 8px;
}

.description {
  color: #4B5563;
  line-height: 1.6;
  margin-bottom: 10px;
}

.achievements ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.achievements li {
  color: #4B5563;
  margin-bottom: 4px;
}

.project-url a {
  color: #3B82F6;
  text-decoration: none;
  font-weight: 500;
}

.project-url a:hover {
  text-decoration: underline;
}

/* 技能特长 */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.skill-item {
  background: #F9FAFB;
  padding: 15px;
  border-radius: 8px;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.skill-name {
  font-weight: 500;
  color: #1F2937;
}

.skill-level-text {
  font-size: 0.8rem;
  color: #6B7280;
}

.skill-bar {
  height: 6px;
  background: #E5E7EB;
  border-radius: 3px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, #3B82F6, #1D4ED8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resume-template {
    padding: 20px;
  }
  
  .resume-header {
    flex-direction: column;
    text-align: center;
  }
  
  .photo-container {
    margin: 20px 0 0 0;
  }
  
  .contact-info {
    justify-content: center;
  }
  
  .item-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .duration,
  .award-date {
    margin-left: 0;
    margin-top: 5px;
  }
  
  .skills-grid {
    grid-template-columns: 1fr;
  }
}

/* 打印样式 */
@media print {
  .resume-template {
    padding: 20px;
    font-size: 12px;
  }
  
  .name {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.2rem;
  }
  
  .experience-item,
  .project-item,
  .education-item,
  .award-item {
    break-inside: avoid;
    margin-bottom: 15px;
    padding: 15px;
  }
  
  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style> 