package com.alan6.resume.service;

import com.alan6.resume.entity.Roles;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
public interface IRolesService extends IService<Roles> {

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Roles> getRolesByUserId(Long userId);

    /**
     * 根据角色代码获取角色
     *
     * @param roleCode 角色代码
     * @return 角色信息
     */
    Roles getRoleByCode(String roleCode);

    /**
     * 获取所有启用的角色
     *
     * @return 角色列表
     */
    List<Roles> getEnabledRoles();

    /**
     * 创建角色
     *
     * @param role 角色信息
     * @return 是否成功
     */
    boolean createRole(Roles role);

    /**
     * 更新角色
     *
     * @param role 角色信息
     * @return 是否成功
     */
    boolean updateRole(Roles role);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean deleteRole(Long roleId);

    /**
     * 检查角色代码是否存在
     *
     * @param roleCode 角色代码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsRoleCode(String roleCode, Long excludeId);
} 