import{r as n,k as S}from"./CURHyiUL.js";const b=()=>{const i=n([]),c=n(!1),s=n(null),r=n({current:1,size:20,total:0,pages:0}),t=n({keyword:"",categoryId:null,industry:"",style:"",colorScheme:"",isPremium:null,sortBy:"default"}),d=S(()=>i.value),l=async(e={})=>{try{c.value=!0,s.value=null;const a={current:r.value.current,size:r.value.size,sortBy:t.value.sortBy,...e};t.value.keyword&&t.value.keyword.trim()&&(a.keyword=t.value.keyword.trim()),t.value.categoryId&&(a.categoryId=t.value.categoryId),t.value.industry&&t.value.industry.trim()&&(a.industry=t.value.industry.trim()),t.value.style&&t.value.style.trim()&&(a.style=t.value.style.trim()),t.value.colorScheme&&t.value.colorScheme.trim()&&(a.colorScheme=t.value.colorScheme.trim()),t.value.isPremium!==null&&t.value.isPremium!==""&&(a.isPremium=t.value.isPremium);const u=await $fetch("/api/template/list",{method:"GET",params:a});if(u.code===200){const o=u.data;return i.value=o.records||[],r.value.total=parseInt(o.total)||0,r.value.current=o.current||1,r.value.size=o.size||20,r.value.pages=o.pages||Math.ceil(r.value.total/r.value.size),{success:!0,data:i.value,total:r.value.total}}else throw new Error(u.msg||"获取模板列表失败")}catch(a){return console.error("获取模板列表失败:",a),s.value=a.message||"获取模板列表失败",{success:!1,error:s.value}}finally{c.value=!1}},g=async e=>{try{c.value=!0,s.value=null;const a=await $fetch(`/api/template/detail/${e}`,{method:"GET"});if(a.code===200)return{success:!0,data:a.data};throw new Error(a.msg||"获取模板详情失败")}catch(a){return console.error("获取模板详情失败:",a),s.value=a.message||"获取模板详情失败",{success:!1,error:s.value}}finally{c.value=!1}},f=async(e,a)=>{try{c.value=!0,s.value=null;const u=await $fetch(`/api/template/use/${e}`,{method:"POST",body:a,headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(u.code===200)return{success:!0,data:u.data};throw new Error(u.msg||"使用模板失败")}catch(u){return console.error("使用模板失败:",u),s.value=u.message||"使用模板失败",{success:!1,error:s.value}}finally{c.value=!1}},w=async e=>({success:!0}),h=async()=>{try{const e=await $fetch("/api/template/categories",{method:"GET"});if(e.code===200)return{success:!0,data:e.data||[]};throw new Error(e.msg||"获取模板分类失败")}catch(e){return console.error("获取模板分类失败:",e),{success:!1,error:e.message||"获取模板分类失败"}}},p=async e=>{t.value.keyword=e,r.value.current=1,await l()},y=async e=>{t.value.categoryId=e,r.value.current=1,await l()},v=async e=>{t.value.industry=e,r.value.current=1,await l()},m=async e=>{t.value.style=e,r.value.current=1,await l()};return{templates:d,loading:c,error:s,pagination:r,currentFilters:t,fetchTemplates:l,fetchTemplateById:g,useTemplate:f,recordTemplateUsage:w,fetchCategories:h,setKeywordFilter:p,setCategoryFilter:y,setIndustryFilter:v,setStyleFilter:m,setPremiumFilter:async e=>{t.value.isPremium=e,r.value.current=1,await l()},setSortBy:async e=>{t.value.sortBy=e,r.value.current=1,await l()},changePage:async e=>{e>=1&&e<=r.value.pages&&(r.value.current=e,await l())},setPageSize:async e=>{r.value.size=e,r.value.current=1,await l()},clearFilters:async()=>{t.value={keyword:"",categoryId:null,industry:"",style:"",colorScheme:"",isPremium:null,sortBy:"default"},r.value.current=1,await l()},setMainCategoryFilter:async e=>{e&&e.id&&await y(e.id)},setSubCategoryFilter:async e=>{e&&e.subCategory&&(e.subCategory.industry?await v(e.subCategory.industry):e.subCategory.style&&await m(e.subCategory.style))}}};export{b as u};
