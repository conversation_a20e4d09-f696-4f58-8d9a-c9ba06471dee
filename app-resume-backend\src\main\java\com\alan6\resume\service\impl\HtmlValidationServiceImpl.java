package com.alan6.resume.service.impl;

import com.alan6.resume.dto.template.HtmlValidationResult;
import com.alan6.resume.service.IHtmlValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * HTML模板验证服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class HtmlValidationServiceImpl implements IHtmlValidationService {
    
    /**
     * 支持的模块ID列表
     */
    private static final List<String> SUPPORTED_MODULE_IDS = Arrays.asList(
        // 匹配前端编辑器实际使用的模块ID（单数形式）
        "basic_info", "education", "work_experience", "project", "skills",
        "language", "award", "hobbies", "self_evaluation",
        "internship", "training", "research_experience", "publication", 
        "certificate", "volunteer_experience", "portfolio", "cover_letter", "custom"
    );
    
    /**
     * 支持的字段名称列表
     */
    private static final List<String> SUPPORTED_FIELD_NAMES = Arrays.asList(
        // 基本信息字段
        "name", "phone", "email", "age", "currentCity", "intendedCity", "address", 
        "currentStatus", "expectedPosition", "expectedSalary", "photo",
        "gender", "height", "weight", "politicalStatus", "maritalStatus", 
        "hometown", "ethnicity", "wechat", "website", "github",
        "customSocials", "customInfos", "label", "value",
        
        // 工作经历字段
        "company", "position", "department", "startDate", "endDate", "description",
        
        // 教育经历字段
        "school", "major", "degree", "customDegree", "gpa",
        
        // 项目经历字段
        "role",
        
        // 技能字段
        "level", "proficiency", "displayMode",
        
        // 语言能力字段
        "certificate",
        
        // 获奖情况字段
        "organization", "date",
        
        // 证书资质字段
        "number",
        
        // 实习经历字段
        
        // 培训经历字段
        "course",
        
        // 志愿服务字段
        "location",
        
        // 研究经历字段
        "topic",
        
        // 论文发表字段
        "title", "type", "journal",
        
        // 作品集字段
        "url",
        
        // 自我评价字段
        "content",
        
        // 自荐信字段
        
        // 兴趣爱好字段
        
        // 自定义模块字段
        
        // 通用时间字段
        "start_date", "end_date", // 支持下划线格式
        
        // 其他通用字段
        "avatar", "job_title", "summary", "achievements", "technologies", 
        "project_name", "skill_name", "skill_level", "award_name", "award_date", 
        "certificate_name", "language_name", "hobby_name", "portfolio_name", 
        "portfolio_url"
    );
    
    /**
     * HTML文档结构验证正则表达式
     */
    private static final Pattern DOCTYPE_PATTERN = Pattern.compile("<!DOCTYPE\\s+html>", Pattern.CASE_INSENSITIVE);
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<html[^>]*>", Pattern.CASE_INSENSITIVE);
    private static final Pattern HEAD_TAG_PATTERN = Pattern.compile("<head[^>]*>", Pattern.CASE_INSENSITIVE);
    private static final Pattern BODY_TAG_PATTERN = Pattern.compile("<body[^>]*>", Pattern.CASE_INSENSITIVE);
    private static final Pattern CHARSET_PATTERN = Pattern.compile("<meta[^>]*charset=[\"']?UTF-8[\"']?[^>]*>", Pattern.CASE_INSENSITIVE);
    private static final Pattern TITLE_TAG_PATTERN = Pattern.compile("<title[^>]*>.*?</title>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    
    /**
     * 模块和字段验证正则表达式
     */
    private static final Pattern MODULE_ROOT_PATTERN = Pattern.compile("data-module-root=[\"']true[\"']", Pattern.CASE_INSENSITIVE);
    private static final Pattern MODULE_PATTERN = Pattern.compile("data-module=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
    private static final Pattern FIELD_PATTERN = Pattern.compile("data-field=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
    private static final Pattern VUE_TEXT_PATTERN = Pattern.compile("data-vue-text=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
    private static final Pattern VUE_FOR_PATTERN = Pattern.compile("data-vue-for=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
    private static final Pattern VUE_IF_PATTERN = Pattern.compile("data-vue-if=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
    
    /**
     * CSS样式验证正则表达式
     */
    private static final Pattern STYLE_TAG_PATTERN = Pattern.compile("<style[^>]*>.*?</style>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern CSS_LINK_PATTERN = Pattern.compile("<link[^>]*rel=[\"']stylesheet[\"'][^>]*>", Pattern.CASE_INSENSITIVE);
    
    @Override
    public HtmlValidationResult validateHtmlFile(File htmlFile) {
        log.info("开始验证HTML文件: {}", htmlFile.getName());
        
        try {
            // 读取文件内容
            String htmlContent = Files.readString(htmlFile.toPath());
            return validateHtmlContent(htmlContent, htmlFile.getName());
        } catch (IOException e) {
            log.error("读取HTML文件失败: {}", htmlFile.getName(), e);
            HtmlValidationResult errorResult = HtmlValidationResult.builder()
                    .valid(false)
                    .errors(new ArrayList<>())
                    .warnings(new ArrayList<>())
                    .build();
            errorResult.addError(HtmlValidationResult.createError(
                    "FILE_READ_ERROR", "FILE_001", 
                    "无法读取文件: " + e.getMessage(),
                    null, htmlFile.getName(), "检查文件是否存在且有读取权限"
            ));
            return errorResult;
        }
    }
    
    @Override
    public HtmlValidationResult validateHtmlContent(String htmlContent, String fileName) {
        log.info("开始验证HTML内容: {}", fileName);
        
        if (!StringUtils.hasText(htmlContent)) {
            HtmlValidationResult errorResult = HtmlValidationResult.builder()
                    .valid(false)
                    .errors(new ArrayList<>())
                    .warnings(new ArrayList<>())
                    .build();
            errorResult.addError(HtmlValidationResult.createError(
                    "CONTENT_EMPTY", "CONTENT_001",
                    "HTML内容为空", 1, fileName, "请提供有效的HTML内容"
            ));
            return errorResult;
        }
        
        // 综合验证结果
        HtmlValidationResult result = HtmlValidationResult.builder()
                .valid(true)
                .errors(new ArrayList<>())
                .warnings(new ArrayList<>())
                .build();
        
        // 1. 验证文档结构
        HtmlValidationResult docResult = validateDocumentStructure(htmlContent, fileName);
        mergeValidationResults(result, docResult);
        
        // 2. 验证模块标识
        HtmlValidationResult moduleResult = validateModuleIdentifiers(htmlContent, fileName);
        mergeValidationResults(result, moduleResult);
        
        // 3. 验证字段绑定
        HtmlValidationResult fieldResult = validateFieldBindings(htmlContent, fileName);
        mergeValidationResults(result, fieldResult);
        
        // 4. 验证CSS样式
        HtmlValidationResult cssResult = validateCssStyles(htmlContent, fileName);
        mergeValidationResults(result, cssResult);
        
        // 5. 生成统计信息
        result.setStatistics(generateStatistics(htmlContent, result));
        
        // 6. 设置最终验证状态
        result.setValid(!result.hasErrors());
        
        log.info("HTML验证完成: {}, 错误数: {}, 警告数: {}", 
                fileName, result.getErrorCount(), result.getWarningCount());
        
        return result;
    }
    
    @Override
    public List<HtmlValidationResult> validateHtmlFiles(List<File> htmlFiles) {
        log.info("开始批量验证HTML文件，数量: {}", htmlFiles.size());
        
        List<HtmlValidationResult> results = new ArrayList<>();
        for (File htmlFile : htmlFiles) {
            results.add(validateHtmlFile(htmlFile));
        }
        
        log.info("批量验证完成，总数: {}", results.size());
        return results;
    }
    
    @Override
    public HtmlValidationResult validateDocumentStructure(String htmlContent, String fileName) {
        log.debug("验证HTML文档结构: {}", fileName);
        
        HtmlValidationResult result = HtmlValidationResult.builder()
                .valid(true)
                .errors(new ArrayList<>())
                .warnings(new ArrayList<>())
                .build();
        
        // 检查DOCTYPE声明
        if (!DOCTYPE_PATTERN.matcher(htmlContent).find()) {
            result.addError(HtmlValidationResult.createError(
                    "DOCUMENT_STRUCTURE", "DOC_001",
                    "缺少DOCTYPE声明", 1, fileName,
                    "在文档开头添加: <!DOCTYPE html>"
            ));
        }
        
        // 检查HTML标签
        if (!HTML_TAG_PATTERN.matcher(htmlContent).find()) {
            result.addError(HtmlValidationResult.createError(
                    "DOCUMENT_STRUCTURE", "DOC_002",
                    "缺少<html>标签", null, fileName,
                    "添加<html>标签包裹整个文档"
            ));
        }
        
        // 检查HEAD标签
        if (!HEAD_TAG_PATTERN.matcher(htmlContent).find()) {
            result.addError(HtmlValidationResult.createError(
                    "DOCUMENT_STRUCTURE", "DOC_003",
                    "缺少<head>标签", null, fileName,
                    "添加<head>标签包含文档元信息"
            ));
        }
        
        // 检查BODY标签
        if (!BODY_TAG_PATTERN.matcher(htmlContent).find()) {
            result.addError(HtmlValidationResult.createError(
                    "DOCUMENT_STRUCTURE", "DOC_004",
                    "缺少<body>标签", null, fileName,
                    "添加<body>标签包含文档内容"
            ));
        }
        
        // 检查字符编码
        if (!CHARSET_PATTERN.matcher(htmlContent).find()) {
            result.addError(HtmlValidationResult.createError(
                    "DOCUMENT_STRUCTURE", "DOC_005",
                    "缺少UTF-8字符编码声明", null, fileName,
                    "在<head>中添加: <meta charset=\"UTF-8\">"
            ));
        }
        
        // 检查标题
        if (!TITLE_TAG_PATTERN.matcher(htmlContent).find()) {
            result.addWarning(HtmlValidationResult.createWarning(
                    "DOCUMENT_STRUCTURE", "DOC_006",
                    "缺少<title>标签", null, fileName,
                    "建议添加<title>标签描述文档标题"
            ));
        }
        
        // 检查根容器
        if (!MODULE_ROOT_PATTERN.matcher(htmlContent).find()) {
            result.addError(HtmlValidationResult.createError(
                    "DOCUMENT_STRUCTURE", "DOC_007",
                    "缺少根容器标识", null, fileName,
                    "在根容器元素上添加: data-module-root=\"true\""
            ));
        }
        
        result.setValid(!result.hasErrors());
        return result;
    }
    
    @Override
    public HtmlValidationResult validateModuleIdentifiers(String htmlContent, String fileName) {
        log.debug("验证模块标识: {}", fileName);
        
        HtmlValidationResult result = HtmlValidationResult.builder()
                .valid(true)
                .errors(new ArrayList<>())
                .warnings(new ArrayList<>())
                .build();
        
        // 查找所有模块标识
        Matcher moduleMatcher = MODULE_PATTERN.matcher(htmlContent);
        Set<String> foundModules = new HashSet<>();
        int moduleCount = 0;
        
        while (moduleMatcher.find()) {
            String moduleId = moduleMatcher.group(1);
            moduleCount++;
            foundModules.add(moduleId);
            
            // 检查模块ID是否支持
            if (!isSupportedModuleId(moduleId)) {
                result.addError(HtmlValidationResult.createError(
                        "MODULE_IDENTIFIER", "MOD_001",
                        "不支持的模块ID: " + moduleId,
                        getLineNumber(htmlContent, moduleMatcher.start()),
                        fileName,
                        "使用支持的模块ID: " + String.join(", ", SUPPORTED_MODULE_IDS)
                ));
            }
        }
        
        // 检查是否至少有一个模块
        if (moduleCount == 0) {
            result.addError(HtmlValidationResult.createError(
                    "MODULE_IDENTIFIER", "MOD_002",
                    "未找到任何模块标识", null, fileName,
                    "至少添加一个data-module属性"
            ));
        }
        
        // 检查重复模块
        if (foundModules.size() < moduleCount) {
            result.addWarning(HtmlValidationResult.createWarning(
                    "MODULE_IDENTIFIER", "MOD_003",
                    "存在重复的模块标识", null, fileName,
                    "建议每个模块ID只使用一次"
            ));
        }
        
        result.setValid(!result.hasErrors());
        return result;
    }
    
    @Override
    public HtmlValidationResult validateFieldBindings(String htmlContent, String fileName) {
        log.debug("验证字段绑定: {}", fileName);
        
        HtmlValidationResult result = HtmlValidationResult.builder()
                .valid(true)
                .errors(new ArrayList<>())
                .warnings(new ArrayList<>())
                .build();
        
        // 查找所有字段绑定
        Matcher fieldMatcher = FIELD_PATTERN.matcher(htmlContent);
        int fieldCount = 0;
        
        while (fieldMatcher.find()) {
            String fieldName = fieldMatcher.group(1);
            fieldCount++;
            
            // 解析字段名称（处理数组格式）
            String baseFieldName = parseBaseFieldName(fieldName);
            
            // 检查字段名称是否支持
            if (!isSupportedFieldName(baseFieldName)) {
                result.addError(HtmlValidationResult.createError(
                        "FIELD_BINDING", "FIELD_001",
                        "不支持的字段名称: " + fieldName,
                        getLineNumber(htmlContent, fieldMatcher.start()),
                        fileName,
                        "使用支持的字段名称或检查拼写"
                ));
            }
            
            // 检查数组字段格式
            if (fieldName.contains("[") && !isValidArrayFieldFormat(fieldName)) {
                result.addError(HtmlValidationResult.createError(
                        "FIELD_BINDING", "FIELD_002",
                        "数组字段格式错误: " + fieldName,
                        getLineNumber(htmlContent, fieldMatcher.start()),
                        fileName,
                        "使用正确格式: fieldName[index].subField"
                ));
            }
        }
        
        // 检查是否至少有一个字段
        if (fieldCount == 0) {
            result.addWarning(HtmlValidationResult.createWarning(
                    "FIELD_BINDING", "FIELD_003",
                    "未找到任何字段绑定", null, fileName,
                    "建议添加data-field属性进行数据绑定"
            ));
        }
        
        result.setValid(!result.hasErrors());
        return result;
    }
    
    @Override
    public HtmlValidationResult validateCssStyles(String htmlContent, String fileName) {
        log.debug("验证CSS样式: {}", fileName);
        
        HtmlValidationResult result = HtmlValidationResult.builder()
                .valid(true)
                .errors(new ArrayList<>())
                .warnings(new ArrayList<>())
                .build();
        
        // 检查是否包含CSS样式
        boolean hasInlineStyles = STYLE_TAG_PATTERN.matcher(htmlContent).find();
        boolean hasExternalStyles = CSS_LINK_PATTERN.matcher(htmlContent).find();
        
        if (!hasInlineStyles && !hasExternalStyles) {
            result.addWarning(HtmlValidationResult.createWarning(
                    "CSS_STYLES", "CSS_001",
                    "未找到CSS样式", null, fileName,
                    "建议添加<style>标签或外部CSS文件"
            ));
        }
        
        // 检查内联样式
        if (hasInlineStyles) {
            Matcher styleMatcher = STYLE_TAG_PATTERN.matcher(htmlContent);
            while (styleMatcher.find()) {
                String styleContent = styleMatcher.group();
                validateCssContent(styleContent, fileName, result, 
                        getLineNumber(htmlContent, styleMatcher.start()));
            }
        }
        
        result.setValid(!result.hasErrors());
        return result;
    }
    
    @Override
    public String generateValidationReport(List<HtmlValidationResult> validationResults) {
        log.info("生成验证报告，文件数量: {}", validationResults.size());
        
        StringBuilder report = new StringBuilder();
        report.append("# HTML模板验证报告\n\n");
        report.append("生成时间: ").append(new Date()).append("\n\n");
        
        // 统计信息
        int totalFiles = validationResults.size();
        int validFiles = (int) validationResults.stream().filter(HtmlValidationResult::getValid).count();
        int totalErrors = validationResults.stream().mapToInt(HtmlValidationResult::getErrorCount).sum();
        int totalWarnings = validationResults.stream().mapToInt(HtmlValidationResult::getWarningCount).sum();
        
        report.append("## 总体统计\n");
        report.append("- 总文件数: ").append(totalFiles).append("\n");
        report.append("- 验证通过: ").append(validFiles).append("\n");
        report.append("- 验证失败: ").append(totalFiles - validFiles).append("\n");
        report.append("- 总错误数: ").append(totalErrors).append("\n");
        report.append("- 总警告数: ").append(totalWarnings).append("\n\n");
        
        // 详细结果
        report.append("## 详细结果\n\n");
        for (int i = 0; i < validationResults.size(); i++) {
            HtmlValidationResult result = validationResults.get(i);
            report.append("### 文件 ").append(i + 1).append("\n");
            report.append("- 验证状态: ").append(result.getValid() ? "通过" : "失败").append("\n");
            report.append("- 错误数: ").append(result.getErrorCount()).append("\n");
            report.append("- 警告数: ").append(result.getWarningCount()).append("\n");
            
            if (result.hasErrors()) {
                report.append("#### 错误信息\n");
                for (HtmlValidationResult.ValidationError error : result.getErrors()) {
                    report.append("- ").append(error.getMessage()).append("\n");
                    report.append("  - 建议: ").append(error.getSuggestion()).append("\n");
                }
            }
            
            if (result.hasWarnings()) {
                report.append("#### 警告信息\n");
                for (HtmlValidationResult.ValidationWarning warning : result.getWarnings()) {
                    report.append("- ").append(warning.getMessage()).append("\n");
                    report.append("  - 建议: ").append(warning.getSuggestion()).append("\n");
                }
            }
            
            report.append("\n");
        }
        
        return report.toString();
    }
    
    @Override
    public List<String> getSupportedModuleIds() {
        List<String> supportedIds = new ArrayList<>(SUPPORTED_MODULE_IDS);
        // 添加自定义模块格式说明
        supportedIds.add("custom_[timestamp] (自定义模块格式)");
        return supportedIds;
    }
    
    @Override
    public List<String> getSupportedFieldNames() {
        return new ArrayList<>(SUPPORTED_FIELD_NAMES);
    }
    
    @Override
    public boolean isSupportedModuleId(String moduleId) {
        // 支持标准模块ID
        if (SUPPORTED_MODULE_IDS.contains(moduleId)) {
            return true;
        }
        
        // 支持自定义模块ID格式：custom_[时间戳]
        if (moduleId != null && moduleId.startsWith("custom_")) {
            // 验证时间戳部分是否为数字
            String timestampPart = moduleId.substring(7); // "custom_".length() = 7
            return timestampPart.matches("\\d+");
        }
        
        return false;
    }
    
    @Override
    public boolean isSupportedFieldName(String fieldName) {
        return SUPPORTED_FIELD_NAMES.contains(fieldName);
    }
    
    /**
     * 合并验证结果
     * 
     * @param target 目标结果
     * @param source 源结果
     */
    private void mergeValidationResults(HtmlValidationResult target, HtmlValidationResult source) {
        if (source.getErrors() != null) {
            target.getErrors().addAll(source.getErrors());
        }
        if (source.getWarnings() != null) {
            target.getWarnings().addAll(source.getWarnings());
        }
    }
    
    /**
     * 生成统计信息
     * 
     * @param htmlContent HTML内容
     * @param result 验证结果
     * @return 统计信息
     */
    private HtmlValidationResult.ValidationStatistics generateStatistics(String htmlContent, HtmlValidationResult result) {
        // 统计模块数量
        Matcher moduleMatcher = MODULE_PATTERN.matcher(htmlContent);
        Set<String> modules = new HashSet<>();
        while (moduleMatcher.find()) {
            modules.add(moduleMatcher.group(1));
        }
        
        // 统计字段数量
        Matcher fieldMatcher = FIELD_PATTERN.matcher(htmlContent);
        Set<String> fields = new HashSet<>();
        while (fieldMatcher.find()) {
            fields.add(fieldMatcher.group(1));
        }
        
        return HtmlValidationResult.ValidationStatistics.builder()
                .totalModules(modules.size())
                .validModules((int) modules.stream().filter(this::isSupportedModuleId).count())
                .totalFields(fields.size())
                .validFields((int) fields.stream().filter(f -> isSupportedFieldName(parseBaseFieldName(f))).count())
                .errorCount(result.getErrorCount())
                .warningCount(result.getWarningCount())
                .detectedModules(new ArrayList<>(modules))
                .detectedFields(new ArrayList<>(fields))
                .build();
    }
    
    /**
     * 获取行号
     * 
     * @param content 内容
     * @param position 位置
     * @return 行号
     */
    private Integer getLineNumber(String content, int position) {
        if (position < 0 || position >= content.length()) {
            return null;
        }
        
        int line = 1;
        for (int i = 0; i < position; i++) {
            if (content.charAt(i) == '\n') {
                line++;
            }
        }
        return line;
    }
    
    /**
     * 解析基础字段名称
     * 
     * @param fieldName 字段名称
     * @return 基础字段名称
     */
    private String parseBaseFieldName(String fieldName) {
        if (fieldName.contains("[")) {
            // 处理数组格式: educations[0].school -> school
            int dotIndex = fieldName.indexOf('.');
            if (dotIndex > 0) {
                return fieldName.substring(dotIndex + 1);
            }
            // 处理数组格式: educations[0] -> educations
            int bracketIndex = fieldName.indexOf('[');
            if (bracketIndex > 0) {
                return fieldName.substring(0, bracketIndex);
            }
        }
        return fieldName;
    }
    
    /**
     * 验证数组字段格式
     * 
     * @param fieldName 字段名称
     * @return 是否有效
     */
    private boolean isValidArrayFieldFormat(String fieldName) {
        // 正确格式: fieldName[index].subField 或 fieldName[index]
        Pattern arrayPattern = Pattern.compile("^[a-zA-Z_][a-zA-Z0-9_]*\\[\\d+\\](?:\\.[a-zA-Z_][a-zA-Z0-9_]*)?$");
        return arrayPattern.matcher(fieldName).matches();
    }
    
    /**
     * 验证CSS内容
     * 
     * @param styleContent CSS内容
     * @param fileName 文件名
     * @param result 验证结果
     * @param lineNumber 行号
     */
    private void validateCssContent(String styleContent, String fileName, HtmlValidationResult result, Integer lineNumber) {
        // 检查是否使用相对单位
        if (styleContent.contains("px") && !styleContent.contains("rem") && !styleContent.contains("em")) {
            result.addWarning(HtmlValidationResult.createWarning(
                    "CSS_STYLES", "CSS_002",
                    "建议使用相对单位(rem, em)而非像素单位", lineNumber, fileName,
                    "使用rem或em单位提高响应式兼容性"
            ));
        }
        
        // 检查是否包含媒体查询
        if (!styleContent.contains("@media")) {
            result.addWarning(HtmlValidationResult.createWarning(
                    "CSS_STYLES", "CSS_003",
                    "建议添加媒体查询支持响应式设计", lineNumber, fileName,
                    "添加@media查询适配不同屏幕尺寸"
            ));
        }
    }
} 