package com.alan6.resume.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 管理员登录请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@Schema(description = "管理员登录请求参数")
public class AdminLoginRequest {
    
    @Schema(description = "手机号", required = true, example = "13800138000")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @Schema(description = "密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @Schema(description = "平台标识", required = true, example = "web")
    @NotBlank(message = "平台标识不能为空")
    @Pattern(regexp = "^(web|wechat_mp|douyin_mp|baidu_mp|alipay_mp|app_ios|app_android)$", 
             message = "平台标识格式不正确")
    private String platform;
} 