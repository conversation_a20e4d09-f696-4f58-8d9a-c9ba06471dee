<template>
  <div class="module-manager-overlay" @click="handleOverlayClick">
    <div class="module-manager-dialog" @click.stop>
      <!-- 弹窗头部 -->
      <div class="dialog-header">
        <h3 class="dialog-title">管理简历模块</h3>
        <button class="close-btn" @click="$emit('close')">
          <Icon name="close" size="sm" />
        </button>
      </div>

      <!-- 弹窗内容 -->
      <div class="dialog-content">
        <div class="section">
          <h4 class="section-title">已启用模块</h4>
          <p class="section-desc">拖拽调整模块顺序，点击切换启用状态</p>
          
          <div 
            v-if="enabledModules.length === 0"
            class="empty-state"
          >
            <Icon name="info" size="lg" class="empty-icon" />
            <p class="empty-text">暂无已启用的模块</p>
          </div>
          
          <div v-else class="module-list">
            <div
              v-for="(module, index) in enabledModules"
              :key="module.id"
              :class="[
                'module-item',
                { 'required': module.required },
                { 'no-drag': module.id === 'basic_info' },
                { 'recently-moved': recentlyMovedModuleId === module.id }
              ]"
              :draggable="module.id !== 'basic_info'"
              @dragstart="handleDragStart(index)"
              @dragover="handleDragOver"
              @drop="handleDrop(index)"
            >
              <div class="module-drag-handle">
                <Icon name="drag" size="sm" />
              </div>
              
              <div class="module-content">
                <div class="module-info">
                  <Icon :name="module.icon" size="sm" class="module-icon" />
                  <span class="module-name">{{ module.name }}</span>
                  <span v-if="module.required" class="required-badge">必填</span>
                </div>
                <p class="module-desc">{{ module.description }}</p>
              </div>
              
              <div class="module-actions">
                <div v-if="!module.required" class="action-buttons">
                  <!-- 上移按钮 -->
                  <button 
                    v-if="module.id !== 'basic_info' && index > 0"
                    class="action-btn sort-btn"
                    @click="moveModuleUp(index)"
                    title="上移"
                  >
                    <Icon name="arrow-up" size="sm" />
                  </button>
                  
                  <!-- 下移按钮 -->
                  <button 
                    v-if="module.id !== 'basic_info' && index < enabledModules.length - 1"
                    class="action-btn sort-btn"
                    @click="moveModuleDown(index)"
                    title="下移"
                  >
                    <Icon name="arrow-down" size="sm" />
                  </button>
                  
                  <!-- 删除按钮 -->
                  <button 
                    v-if="!module.id.startsWith('custom_')"
                    class="action-btn delete-btn"
                    @click="toggleModule(module)"
                    title="删除模块"
                  >
                    <Icon name="delete" size="sm" />
                  </button>
                  
                  <!-- 自定义模块删除按钮 -->
                  <button 
                    v-if="module.id.startsWith('custom_')"
                    class="action-btn delete-btn"
                    @click="handleDeleteCustomModule(module.id)"
                    title="删除自定义模块"
                  >
                    <Icon name="delete" size="sm" />
                  </button>
                </div>
                <span v-else class="required-text">必需</span>
              </div>
            </div>
          </div>
        </div>

        <div class="section">
          <h4 class="section-title">可用模块</h4>
          <p class="section-desc">点击启用更多模块丰富你的简历</p>
          
          <!-- 空状态提示 -->
          <div 
            v-if="disabledModules.length === 0 && disabledCustomModules.length === 0"
            class="empty-state"
          >
            <Icon name="check-circle" size="lg" class="empty-icon text-green-500" />
            <p class="empty-text">所有模块均已启用</p>
          </div>
          
          <!-- 模块网格 -->
          <div class="module-grid">
            <!-- 普通可用模块 -->
            <div
              v-for="module in disabledModules"
              :key="module.id"
              class="module-card"
            >
              <div class="card-content">
                <Icon :name="module.icon" size="md" class="card-icon" />
                <h5 class="card-title">{{ module.name }}</h5>
                <p class="card-desc">{{ module.description }}</p>
              </div>
              
              <div class="card-action">
                <button 
                  class="enable-btn"
                  @click="toggleModule(module)"
                >
                  <Icon name="plus" size="sm" />
                  <span>启用</span>
                </button>
              </div>
            </div>
            
            <!-- 已创建的自定义模块 -->
            <div
              v-for="module in disabledCustomModules"
              :key="module.id"
              class="module-card custom-module"
            >
              <div class="card-content">
                <Icon :name="module.icon" size="md" class="card-icon" />
                <h5 class="card-title">{{ module.name }}</h5>
                <p class="card-desc">{{ module.description }}</p>
              </div>
              
              <div class="card-action">
                <div class="custom-module-actions">
                  <button 
                    class="enable-btn"
                    @click="toggleModule(module)"
                  >
                    <Icon name="plus" size="sm" />
                    <span>启用</span>
                  </button>
                  <button 
                    class="delete-btn"
                    @click="handleDeleteCustomModule(module.id)"
                    title="删除自定义模块"
                  >
                    <Icon name="delete" size="sm" />
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 自定义模块创建器 - 始终显示 -->
            <div
              :class="[
                'module-card custom-creator',
                { 'disabled': customModules.length >= 3 }
              ]"
              @click="handleCreateClick"
            >
              <div class="card-content">
                <Icon name="plus" size="md" class="card-icon creator-icon" />
                <h5 class="card-title">创建自定义模块</h5>
                <p class="card-desc">
                  {{ customModules.length >= 3 ? '已达到创建上限，请删除后再创建' : '创建个性化的简历模块' }}
                </p>
              </div>
              
              <div class="card-action">
                <span class="creator-count">{{ customModules.length }}/3</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗底部 -->
      <div class="dialog-footer">
        <button class="cancel-btn" @click="$emit('close')">
          取消
        </button>
        <button class="confirm-btn" @click="handleConfirm">
          确定
        </button>
      </div>
    </div>
    
    <!-- 创建自定义模块对话框 -->
    <CreateCustomModuleDialog 
      v-if="showCreateDialog"
      @confirm="handleCreateCustomModule"
      @cancel="showCreateDialog = false"
    />
  </div>
</template>

<script setup>
/**
 * 模块管理组件
 * @description 提供简历模块的启用、禁用、排序等管理功能
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, onMounted, nextTick } from 'vue'
import Icon from '~/components/common/Icon.vue'
import CreateCustomModuleDialog from './CreateCustomModuleDialog.vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 模块配置列表
   */
  modules: {
    type: Array,
    required: true
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['close', 'update'])

// ================================
// 响应式数据
// ================================
const localModules = ref([])
const dragStartIndex = ref(-1)
const showCreateDialog = ref(false)
const recentlyMovedModuleId = ref(null)
let moveHighlightTimeout = null

// ================================
// 状态管理
// ================================
const editorStore = useEditorStore()

// ================================
// 计算属性
// ================================

/**
 * 已启用的模块
 */
const enabledModules = computed(() => {
  // 获取已启用的普通模块
  const enabledLocalModules = localModules.value
    .filter(module => module.enabled)
    .sort((a, b) => a.order - b.order)
  
  // 获取已启用的自定义模块
  const enabledCustomModules = customModules.value
    .filter(module => module.enabled)
    .sort((a, b) => a.order - b.order)
  
  // 合并并按order排序
  return [...enabledLocalModules, ...enabledCustomModules]
    .sort((a, b) => a.order - b.order)
})

/**
 * 未启用的模块（排除自定义模块）
 */
const disabledModules = computed(() => {
  return localModules.value
    .filter(module => !module.enabled && !module.id.startsWith('custom_'))
    .sort((a, b) => a.name.localeCompare(b.name))
})

/**
 * 未启用的自定义模块
 */
const disabledCustomModules = computed(() => {
  return customModules.value.filter(module => !module.enabled)
})

/**
 * 自定义模块列表
 */
const customModules = computed(() => {
  return editorStore.customModules?.value || []
})

// ================================
// 生命周期
// ================================
onMounted(() => {
  // 深拷贝模块配置，避免直接修改原数据，并排除自定义模块
  localModules.value = JSON.parse(JSON.stringify(props.modules))
    .filter(module => !module.id.startsWith('custom_'))
})

// ================================
// 方法定义
// ================================

/**
 * 处理遮罩层点击
 */
const handleOverlayClick = () => {
  emit('close')
}

/**
 * 切换模块启用状态
 * @param {Object} module - 模块对象
 */
const toggleModule = (module) => {
  if (module.required && module.enabled) {
    // 必填模块不能禁用
    return
  }
  
  // 保存当前滚动位置和容器引用
  const dialogContent = document.querySelector('.dialog-content')
  const scrollTop = dialogContent ? dialogContent.scrollTop : 0
  
  // 临时禁用滚动行为，防止DOM变化导致的自动滚动
  const originalOverflow = dialogContent ? dialogContent.style.overflow : ''
  const originalScrollBehavior = dialogContent ? dialogContent.style.scrollBehavior : ''
  
  if (dialogContent) {
    dialogContent.style.overflow = 'hidden'
    dialogContent.style.scrollBehavior = 'auto'
  }
  
  // 执行模块切换操作
  if (module.id.startsWith('custom_')) {
    editorStore.toggleCustomModule(module.id)
  } else {
    // 普通模块的处理逻辑
    const moduleIndex = localModules.value.findIndex(m => m.id === module.id)
    if (moduleIndex !== -1) {
      localModules.value[moduleIndex].enabled = !localModules.value[moduleIndex].enabled
      
      // 如果是启用模块，设置到最后位置
      if (localModules.value[moduleIndex].enabled) {
        const maxOrder = Math.max(...localModules.value
          .filter(m => m.enabled)
          .map(m => m.order)
        )
        localModules.value[moduleIndex].order = maxOrder + 1
      }
    }
  }
  
  // 等待DOM更新完成后恢复滚动
  nextTick(() => {
    // 使用 setTimeout 确保在Vue完全渲染后执行
    setTimeout(() => {
      if (dialogContent) {
        // 恢复滚动位置
        dialogContent.scrollTop = scrollTop
        // 恢复原始样式
        dialogContent.style.overflow = originalOverflow
        dialogContent.style.scrollBehavior = originalScrollBehavior
      }
    }, 0)
  })
}

/**
 * 处理拖拽开始
 * @param {Number} index - 拖拽项索引
 */
const handleDragStart = (index) => {
  const module = enabledModules.value[index]
  // 基本信息模块不能拖拽
  if (module.id === 'basic_info') {
    return
  }
  dragStartIndex.value = index
}

/**
 * 处理拖拽悬停
 * @param {Event} event - 拖拽事件
 */
const handleDragOver = (event) => {
  event.preventDefault()
}

/**
 * 处理拖拽放置
 * @param {Number} index - 放置位置索引
 */
const handleDrop = (index) => {
  event.preventDefault()
  
  if (dragStartIndex.value === -1 || dragStartIndex.value === index) {
    return
  }
  
  const modules = [...enabledModules.value]
  const draggedModule = modules[dragStartIndex.value]
  const targetModule = modules[index]
  
  // 不能拖拽到基本信息的位置，也不能拖拽基本信息
  if (draggedModule.id === 'basic_info' || targetModule.id === 'basic_info') {
    dragStartIndex.value = -1
    return
  }
  
  // 重新排序已启用模块（排除基本信息）
  const nonBasicModules = modules.filter(m => m.id !== 'basic_info')
  const draggedIndex = nonBasicModules.findIndex(m => m.id === draggedModule.id)
  const targetIndex = nonBasicModules.findIndex(m => m.id === targetModule.id)
  
  if (draggedIndex !== -1 && targetIndex !== -1) {
    // 移除拖拽的模块
    nonBasicModules.splice(draggedIndex, 1)
    
    // 在新位置插入
    nonBasicModules.splice(targetIndex, 0, draggedModule)
    
    // 更新所有非基本信息模块的顺序（从2开始，因为基本信息固定为1）
    nonBasicModules.forEach((module, idx) => {
      const originalIndex = localModules.value.findIndex(m => m.id === module.id)
      if (originalIndex !== -1) {
        localModules.value[originalIndex].order = idx + 2 // 基本信息是1，所以从2开始
      }
    })
  }
  
  dragStartIndex.value = -1
}

/**
 * 确认保存
 */
const handleConfirm = () => {
  // 更新普通模块状态到 editorStore
  localModules.value.forEach(localModule => {
    const storeModule = editorStore.moduleConfigs.value.find(m => m.id === localModule.id)
    if (storeModule) {
      storeModule.enabled = localModule.enabled
      storeModule.order = localModule.order
    }
  })
  
  // 更新自定义模块状态
  localModules.value.forEach(module => {
    if (module.type === 'custom') {
      const customModule = editorStore.customModules?.value?.find(m => m.id === module.id)
      if (customModule) {
        customModule.enabled = module.enabled
        customModule.order = module.order
      }
    }
  })
  
  // 强制触发响应式更新
  editorStore.moduleConfigs.value = [...editorStore.moduleConfigs.value]
  
  emit('update', localModules.value)
}

/**
 * 上移模块
 * @param {Number} index - 当前模块的索引
 */
const moveModuleUp = (index) => {
  if (index > 0) {
    const modules = [...enabledModules.value]
    const currentModule = modules[index]
    const previousModule = modules[index - 1]
    
    // 如果上一个模块是基本信息，则不能上移
    if (previousModule.id === 'basic_info') {
      return
    }
    
    // 交换order值
    const tempOrder = currentModule.order
    
    // 根据模块类型在不同的数组中查找和更新
    const updateModuleOrder = (module, newOrder) => {
      if (module.id.startsWith('custom_')) {
        // 自定义模块
        const customModule = customModules.value.find(m => m.id === module.id)
        if (customModule) {
          customModule.order = newOrder
        }
      } else {
        // 普通模块
        const moduleIndex = localModules.value.findIndex(m => m.id === module.id)
        if (moduleIndex !== -1) {
          localModules.value[moduleIndex].order = newOrder
        }
      }
    }
    
    updateModuleOrder(currentModule, previousModule.order)
    updateModuleOrder(previousModule, tempOrder)
    
    // 高亮显示被移动的模块
    highlightMovedModule(currentModule.id)
    
    // 滚动到移动后的位置，提供更好的用户体验
    scrollToModule(currentModule.id, index - 1)
  }
}

/**
 * 下移模块
 * @param {Number} index - 当前模块的索引
 */
const moveModuleDown = (index) => {
  if (index < enabledModules.value.length - 1) {
    const modules = [...enabledModules.value]
    const currentModule = modules[index]
    const nextModule = modules[index + 1]
    
    // 如果当前模块是基本信息，则不能下移
    if (currentModule.id === 'basic_info') {
      return
    }
    
    // 交换order值
    const tempOrder = currentModule.order
    
    // 根据模块类型在不同的数组中查找和更新
    const updateModuleOrder = (module, newOrder) => {
      if (module.id.startsWith('custom_')) {
        // 自定义模块
        const customModule = customModules.value.find(m => m.id === module.id)
        if (customModule) {
          customModule.order = newOrder
        }
      } else {
        // 普通模块
        const moduleIndex = localModules.value.findIndex(m => m.id === module.id)
        if (moduleIndex !== -1) {
          localModules.value[moduleIndex].order = newOrder
        }
      }
    }
    
    updateModuleOrder(currentModule, nextModule.order)
    updateModuleOrder(nextModule, tempOrder)
    
    // 高亮显示被移动的模块
    highlightMovedModule(currentModule.id)
    
    // 滚动到移动后的位置，提供更好的用户体验
    scrollToModule(currentModule.id, index + 1)
  }
}

/**
 * 滚动到指定模块位置
 * @param {string} moduleId - 模块ID
 * @param {number} targetIndex - 目标索引
 */
const scrollToModule = (moduleId, targetIndex) => {
  // 使用 nextTick 确保DOM已更新
  nextTick(() => {
    setTimeout(() => {
      const dialogContent = document.querySelector('.dialog-content')
      if (!dialogContent) return
      
      // 查找对应的模块元素
      const moduleItems = dialogContent.querySelectorAll('.module-item')
      if (!moduleItems || moduleItems.length === 0) return
      
      const targetModule = moduleItems[targetIndex]
      if (!targetModule) return
      
      // 获取当前滚动位置和容器信息
      const scrollTop = dialogContent.scrollTop
      const containerHeight = dialogContent.clientHeight
      const moduleRect = targetModule.getBoundingClientRect()
      const containerRect = dialogContent.getBoundingClientRect()
      
      // 计算模块相对于容器的位置
      const moduleTop = targetModule.offsetTop
      const moduleHeight = targetModule.offsetHeight
      
      // 设置滚动的边界阈值
      const scrollThreshold = 60 // 距离边界多少像素时触发滚动
      const scrollOffset = 80 // 滚动时的额外偏移，提供更好的视觉体验
      
      let targetScrollTop = scrollTop
      
      // 检查模块是否在可视区域的上方
      if (moduleTop < scrollTop + scrollThreshold) {
        targetScrollTop = Math.max(0, moduleTop - scrollOffset)
      }
      // 检查模块是否在可视区域的下方
      else if (moduleTop + moduleHeight > scrollTop + containerHeight - scrollThreshold) {
        targetScrollTop = moduleTop + moduleHeight - containerHeight + scrollOffset
      }
      
      // 只有当需要滚动时才执行
      if (Math.abs(targetScrollTop - scrollTop) > 5) {
        dialogContent.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        })
      }
    }, 100) // 给DOM更新一些时间
  })
}

/**
 * 高亮显示被移动的模块
 * @param {string} moduleId - 模块ID
 */
const highlightMovedModule = (moduleId) => {
  // 清除之前的高亮定时器
  if (moveHighlightTimeout) {
    clearTimeout(moveHighlightTimeout)
  }
  
  // 设置当前高亮的模块
  recentlyMovedModuleId.value = moduleId
  
  // 2秒后清除高亮
  moveHighlightTimeout = setTimeout(() => {
    recentlyMovedModuleId.value = null
  }, 2000)
}

/**
 * 处理创建自定义模块
 * @param {string} name - 模块名称
 */
const handleCreateCustomModule = (name) => {
  try {
    editorStore.createCustomModule(name)
    showCreateDialog.value = false
  } catch (error) {
    alert(error.message)
  }
}

/**
 * 处理创建自定义模块点击
 */
const handleCreateClick = () => {
  if (customModules.value.length >= 3) {
    alert('已达到创建上限，请删除后再创建')
    return
  }
  showCreateDialog.value = true
}

/**
 * 处理删除自定义模块
 * @param {string} moduleId - 模块ID
 */
const handleDeleteCustomModule = (moduleId) => {
  if (confirm('确定要删除此自定义模块吗？')) {
    // 保存当前滚动位置和容器引用
    const dialogContent = document.querySelector('.dialog-content')
    const scrollTop = dialogContent ? dialogContent.scrollTop : 0
    
    // 临时禁用滚动行为，防止DOM变化导致的自动滚动
    const originalOverflow = dialogContent ? dialogContent.style.overflow : ''
    const originalScrollBehavior = dialogContent ? dialogContent.style.scrollBehavior : ''
    
    if (dialogContent) {
      dialogContent.style.overflow = 'hidden'
      dialogContent.style.scrollBehavior = 'auto'
    }
    
    editorStore.deleteCustomModule(moduleId)
    
    // 等待DOM更新完成后恢复滚动
    nextTick(() => {
      // 使用 setTimeout 确保在Vue完全渲染后执行
      setTimeout(() => {
        if (dialogContent) {
          // 恢复滚动位置
          dialogContent.scrollTop = scrollTop
          // 恢复原始样式
          dialogContent.style.overflow = originalOverflow
          dialogContent.style.scrollBehavior = originalScrollBehavior
        }
      }, 0)
    })
  }
}
</script>

<style scoped>
.module-manager-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.module-manager-dialog {
  @apply bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col;
}

/* 弹窗头部 */
.dialog-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.dialog-title {
  @apply text-lg font-semibold text-gray-900;
}

.close-btn {
  @apply p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200;
}

/* 弹窗内容 */
.dialog-content {
  @apply flex-1 overflow-y-auto p-6 space-y-8;
}

.section {
  @apply space-y-4;
}

.section-title {
  @apply text-base font-medium text-gray-900;
}

.section-desc {
  @apply text-sm text-gray-600;
}

/* 空状态 */
.empty-state {
  @apply text-center py-8;
}

.empty-icon {
  @apply text-gray-400 mx-auto mb-3;
}

.empty-text {
  @apply text-sm text-gray-500;
}

/* 模块列表 */
.module-list {
  @apply space-y-3;
}

.module-item {
  @apply flex items-center bg-white border border-gray-200 rounded-lg p-4 transition-all duration-200;
}

.module-item:hover {
  @apply border-gray-300 shadow-sm;
}

.module-item.required {
  @apply border-orange-200 bg-orange-50;
}

.module-item.no-drag {
  @apply cursor-default;
}

.module-item.no-drag .module-drag-handle {
  @apply text-gray-300 cursor-not-allowed;
}

.module-item.recently-moved {
  @apply border-green-400 bg-green-50 shadow-md;
  animation: highlight-pulse 0.5s ease-in-out;
}

.module-item.recently-moved .module-name {
  @apply text-green-800 font-semibold;
}

.module-item.recently-moved .module-icon {
  @apply text-green-600;
}

@keyframes highlight-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

.module-drag-handle {
  @apply text-gray-400 cursor-move mr-3;
}

.module-content {
  @apply flex-1;
}

.module-info {
  @apply flex items-center space-x-3 mb-2;
}

.module-icon {
  @apply text-gray-500;
}

.module-name {
  @apply text-sm font-medium text-gray-900;
}

.required-badge {
  @apply px-2 py-0.5 text-xs font-medium bg-orange-100 text-orange-700 rounded;
}

.module-desc {
  @apply text-xs text-gray-600;
}

.module-actions {
  @apply ml-4;
}

.action-buttons {
  @apply flex items-center space-x-2;
}

.action-btn {
  @apply p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200;
}

.action-btn.sort-btn {
  @apply text-gray-400 hover:text-primary-600 hover:bg-primary-50;
}

.action-btn.delete-btn {
  @apply text-gray-400 hover:text-red-600 hover:bg-red-50;
}

.required-text {
  @apply text-xs text-orange-600 font-medium;
}

/* 模块网格 */
.module-grid {
  @apply grid grid-cols-4 gap-4;
}

.module-card {
  @apply bg-white border border-gray-200 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-primary-300 hover:shadow-sm;
}

.card-content {
  @apply text-center mb-4;
}

.card-icon {
  @apply text-gray-500 mx-auto mb-3;
}

.card-title {
  @apply text-sm font-medium text-gray-900 mb-2;
}

.card-desc {
  @apply text-xs text-gray-600;
}

.card-action {
  @apply text-center;
}

.enable-btn {
  @apply inline-flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-primary-600 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors duration-200;
}

/* 弹窗底部 */
.dialog-footer {
  @apply flex items-center justify-end space-x-3 px-6 py-4 border-t border-gray-200;
}

.cancel-btn {
  @apply px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200;
}

.confirm-btn {
  @apply px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors duration-200;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .module-manager-dialog {
    @apply mx-2 max-h-[95vh];
  }
  
  .dialog-content {
    @apply p-4 space-y-6;
  }
  
  .module-grid {
    @apply grid-cols-1;
  }
  
  .module-item {
    @apply p-3;
  }
}

/* 滚动条样式 */
.dialog-content::-webkit-scrollbar {
  @apply w-2;
}

.dialog-content::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

.dialog-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* 自定义模块创建器样式 */
.module-card.custom-creator {
  @apply border-dashed border-blue-300 bg-blue-50 hover:bg-blue-100 hover:border-blue-400;
}

.module-card.custom-creator.disabled {
  @apply border-gray-300 bg-gray-50 cursor-not-allowed;
}

.module-card.custom-creator.disabled:hover {
  @apply bg-gray-50 border-gray-300;
}

.module-card.custom-creator .creator-icon {
  @apply text-blue-500;
}

.module-card.custom-creator.disabled .creator-icon {
  @apply text-gray-400;
}

.module-card.custom-creator .card-title {
  @apply text-blue-700;
}

.module-card.custom-creator.disabled .card-title {
  @apply text-gray-500;
}

.module-card.custom-creator .card-desc {
  @apply text-blue-600;
}

.module-card.custom-creator.disabled .card-desc {
  @apply text-gray-500;
}

.creator-count {
  @apply text-sm font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full;
}

.module-card.custom-creator.disabled .creator-count {
  @apply text-gray-500 bg-gray-200;
}

/* 自定义模块样式 */
.module-card.custom-module {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-4;
}

.module-card.custom-module .card-icon {
  @apply text-gray-500;
}

.module-card.custom-module .card-title {
  @apply text-sm font-medium text-gray-900 mb-2;
}

.module-card.custom-module .card-desc {
  @apply text-xs text-gray-600;
}

.custom-module-actions {
  @apply flex items-center justify-center space-x-2;
}

.custom-module-actions .enable-btn {
  @apply inline-flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-primary-600 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors duration-200;
}

.custom-module-actions .delete-btn {
  @apply p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200;
}
</style> 