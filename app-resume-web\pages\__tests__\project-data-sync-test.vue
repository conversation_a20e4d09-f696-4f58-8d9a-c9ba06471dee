<template>
  <div class="test-container">
    <h1>项目经验数据同步测试</h1>
    
    <div class="test-layout">
      <!-- 左侧：模拟编辑器 -->
      <div class="editor-side">
        <h2>编辑器侧（左侧）</h2>
        <div class="form-container">
          <ProjectForm 
            :data="projectData"
            @update="handleProjectUpdate"
          />
        </div>
        
        <div class="debug-info">
          <h3>编辑器数据状态：</h3>
          <pre>{{ JSON.stringify(projectData, null, 2) }}</pre>
        </div>
      </div>
      
      <!-- 右侧：模拟预览 -->
      <div class="preview-side">
        <h2>预览侧（右侧）</h2>
        <div class="preview-container">
          <div class="project-preview">
            <h3>项目经历</h3>
            <div v-if="previewData.projects && previewData.projects.length > 0" class="project-list">
              <div v-for="(project, index) in previewData.projects" :key="index" class="project-item">
                <div class="project-header">
                  <h4>{{ project.name || '未命名项目' }}</h4>
                  <span class="project-role">{{ project.role || '未设置角色' }}</span>
                </div>
                <div class="project-details">
                  <div class="project-company">{{ project.company || '未设置公司' }}</div>
                  <div class="project-period">
                    {{ project.startDate || '未设置' }} - {{ project.endDate || project.isOngoing ? '至今' : '未设置' }}
                  </div>
                  <div v-if="project.description" class="project-description" v-html="project.description"></div>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              暂无项目经历
            </div>
          </div>
        </div>
        
        <div class="debug-info">
          <h3>预览数据状态：</h3>
          <pre>{{ JSON.stringify(previewData, null, 2) }}</pre>
        </div>
      </div>
    </div>
    
    <div class="test-actions">
      <button @click="addTestProject">添加测试项目</button>
      <button @click="clearProjects">清空项目</button>
      <button @click="logStates">打印状态</button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import ProjectForm from '~/components/editor/sidebar/forms/ProjectForm.vue'

// 测试数据
const projectData = reactive({
  projects: []
})

const previewData = reactive({
  projects: []
})

// 处理项目数据更新
const handleProjectUpdate = (updatedData) => {
  console.log('🔄 项目数据更新:', updatedData)
  
  // 更新预览数据
  previewData.projects = updatedData.projects || []
  
  console.log('📊 更新后的预览数据:', previewData.projects)
}

// 监听项目数据变化
watch(() => projectData.projects, (newProjects) => {
  console.log('👀 监听到项目数据变化:', newProjects)
}, { deep: true })

// 测试操作
const addTestProject = () => {
  const newProject = {
    name: `测试项目${projectData.projects.length + 1}`,
    role: '项目经理',
    company: '测试公司',
    startDate: '2023-01',
    endDate: '2023-12',
    isOngoing: false,
    description: '<p>这是一个<strong>测试项目</strong>的描述</p>'
  }
  
  projectData.projects.push(newProject)
  
  // 手动触发更新
  nextTick(() => {
    handleProjectUpdate({ projects: [...projectData.projects] })
  })
}

const clearProjects = () => {
  projectData.projects = []
  previewData.projects = []
}

const logStates = () => {
  console.log('📝 编辑器数据:', projectData)
  console.log('👁️ 预览数据:', previewData)
}

// 页面标题
useHead({
  title: '项目经验数据同步测试'
})
</script>

<style scoped>
.test-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.test-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 20px 0;
}

.editor-side,
.preview-side {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background: #f9f9f9;
}

.editor-side h2,
.preview-side h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.form-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.preview-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.project-preview h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.project-list {
  space-y: 16px;
}

.project-item {
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background: #fafafa;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.project-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.project-role {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.project-details {
  space-y: 8px;
}

.project-company,
.project-period {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.project-description {
  margin-top: 8px;
  line-height: 1.6;
  color: #555;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 40px;
  font-style: italic;
}

.debug-info {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  margin-top: 20px;
}

.debug-info h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.debug-info pre {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.test-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ddd;
}

.test-actions button {
  padding: 8px 16px;
  border: 1px solid #007bff;
  background: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.test-actions button:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.test-actions button:nth-child(2) {
  background: #dc3545;
  border-color: #dc3545;
}

.test-actions button:nth-child(2):hover {
  background: #c82333;
  border-color: #c82333;
}

.test-actions button:nth-child(3) {
  background: #28a745;
  border-color: #28a745;
}

.test-actions button:nth-child(3):hover {
  background: #218838;
  border-color: #218838;
}
</style> 