package com.alan6.resume.service;

import com.alan6.resume.dto.resume.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.alan6.resume.entity.Resumes;

import java.util.List;

/**
 * 简历服务接口
 * 
 * @description 定义简历管理的核心业务方法
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IResumeService extends IService<Resumes> {

    /**
     * 创建新简历
     * 
     * @param request 创建请求参数
     * @return 简历ID
     */
    Long createResume(ResumeSaveRequest request);

    /**
     * 更新简历
     * 
     * @param id 简历ID
     * @param request 更新请求参数
     */
    void updateResume(Long id, ResumeSaveRequest request);

    /**
     * 获取简历详情
     * 
     * @param id 简历ID
     * @return 简历详情
     */
    ResumeDetailResponse getResumeDetail(Long id);

    /**
     * 获取用户简历列表
     * 
     * @param userId 用户ID
     * @param status 简历状态（可选）
     * @return 简历列表
     */
    List<ResumeListResponse> getResumeList(Long userId, Integer status);

    /**
     * 删除简历（软删除）
     * 
     * @param id 简历ID
     */
    void deleteResume(Long id);

    /**
     * 复制简历
     * 
     * @param sourceId 源简历ID
     * @param newName 新简历名称
     * @return 新简历ID
     */
    Long copyResume(Long sourceId, String newName);

    /**
     * 导出简历
     * 
     * @param request 导出请求参数
     * @return 导出响应
     */
    ResumeExportResponse exportResume(ResumeExportRequest request);

    /**
     * 获取导出状态
     * 
     * @param jobId 任务ID
     * @return 导出状态
     */
    ResumeExportResponse getExportStatus(String jobId);

    /**
     * 保存导出状态
     * 
     * @param request 预览请求参数
     * @return 状态ID
     */
    String saveExportState(ResumePreviewRequest request);

    /**
     * 获取导出数据
     * 
     * @param jobId 任务ID
     * @return 导出数据
     */
    ResumePreviewResponse getExportData(String jobId);

    /**
     * 自动保存简历
     * 
     * @param id 简历ID
     * @param request 保存请求
     */
    void autoSaveResume(Long id, ResumeSaveRequest request);

    /**
     * 增加浏览次数
     * 
     * @param id 简历ID
     */
    void incrementViewCount(Long id);

    /**
     * 增加下载次数
     * 
     * @param id 简历ID
     */
    void incrementDownloadCount(Long id);

    /**
     * 增加分享次数
     * 
     * @param id 简历ID
     */
    void incrementShareCount(Long id);

    /**
     * 计算简历完成度
     * 
     * @param id 简历ID
     * @return 完成度百分比
     */
    Integer calculateCompleteness(Long id);

    /**
     * 更新简历状态
     * 
     * @param id 简历ID
     * @param status 新状态
     */
    void updateResumeStatus(Long id, Integer status);

    /**
     * 更新简历公开状态
     * 
     * @param id 简历ID
     * @param isPublic 是否公开
     */
    void updatePublicStatus(Long id, Integer isPublic);

    /**
     * 获取用户简历数
     *
     * @param userId 用户ID
     * @return 简历数量
     */
    Integer getUserResumeCount(Long userId);
} 