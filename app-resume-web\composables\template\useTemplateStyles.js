/**
 * 模板样式计算组合式函数
 * @description 处理简历模板的样式计算、主题色彩、字体设置等
 * <AUTHOR>
 * @since 1.0.0
 */

import { computed } from 'vue'

/**
 * 模板样式计算组合式函数
 * @param {Object} props - 组件属性
 * @returns {Object} 样式计算相关的方法和计算属性
 */
export function useTemplateStyles(props) {
  
  // ================================
  // 默认样式配置
  // ================================
  
  /**
   * 默认文本样式
   * @description 当用户未设置样式时使用的默认值
   */
  const defaultTextStyle = {
    fontSize: '14px',
    lineHeight: '1.6',
    textColor: '#1f2937',
    primaryColor: '#3b82f6',
    secondaryColor: '#6366f1',
    fontFamily: 'system-ui, -apple-system, sans-serif',
    sectionTitleFontSize: '1.3em',
    itemTitleFontSize: '1.1em',
    bodyFontSize: '1em'
  }
  
  /**
   * 默认间距配置
   * @description 模块、项目、行间距等配置
   */
  const defaultSpacing = {
    moduleSpacing: '24px',
    itemSpacing: '16px',
    lineSpacing: '1.6',
    paragraphSpacing: '12px',
    sectionPadding: '20px',
    itemPadding: '12px'
  }
  
  /**
   * 默认边距配置
   * @description 页面边距配置
   */
  const defaultMargins = {
    top: '60px',
    bottom: '60px',
    left: '60px',
    right: '60px'
  }
  
  // ================================
  // 样式计算方法
  // ================================
  
  /**
   * 合并用户样式与默认样式
   * @param {Object} userStyle - 用户设置的样式
   * @param {Object} defaultStyle - 默认样式
   * @returns {Object} 合并后的样式
   */
  const mergeStyles = (userStyle, defaultStyle) => {
    return { ...defaultStyle, ...userStyle }
  }
  
  /**
   * 解析颜色值
   * @param {string} color - 颜色值
   * @returns {string} 标准化的颜色值
   */
  const parseColor = (color) => {
    if (!color) return '#000000'
    
    // 如果是十六进制颜色，直接返回
    if (color.startsWith('#')) {
      return color
    }
    
    // 如果是RGB/RGBA，直接返回
    if (color.startsWith('rgb')) {
      return color
    }
    
    // 如果是颜色名称，返回对应的十六进制值
    const colorMap = {
      'red': '#ef4444',
      'blue': '#3b82f6',
      'green': '#10b981',
      'yellow': '#f59e0b',
      'purple': '#8b5cf6',
      'pink': '#ec4899',
      'indigo': '#6366f1',
      'gray': '#6b7280',
      'black': '#000000',
      'white': '#ffffff'
    }
    
    return colorMap[color.toLowerCase()] || color
  }
  
  /**
   * 计算对比色
   * @param {string} backgroundColor - 背景色
   * @returns {string} 对比色（黑色或白色）
   */
  const getContrastColor = (backgroundColor) => {
    const color = parseColor(backgroundColor)
    
    // 去除#号
    const hex = color.replace('#', '')
    
    // 计算亮度
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    
    // 使用相对亮度公式
    const brightness = (r * 299 + g * 587 + b * 114) / 1000
    
    // 返回对比色
    return brightness > 128 ? '#000000' : '#ffffff'
  }
  
  /**
   * 生成渐变色
   * @param {string} startColor - 起始颜色
   * @param {string} endColor - 结束颜色
   * @param {string} direction - 渐变方向
   * @returns {string} CSS渐变值
   */
  const generateGradient = (startColor, endColor, direction = '90deg') => {
    const start = parseColor(startColor)
    const end = parseColor(endColor)
    return `linear-gradient(${direction}, ${start}, ${end})`
  }
  
  // ================================
  // 计算属性
  // ================================
  
  /**
   * 合并后的文本样式
   * @description 用户设置与默认样式的合并结果
   */
  const mergedTextStyle = computed(() => {
    const userStyle = props.textStyle || {}
    return mergeStyles(userStyle, defaultTextStyle)
  })
  
  /**
   * 标题样式
   * @description 模块标题的样式配置
   */
  const titleStyle = computed(() => {
    const style = mergedTextStyle.value
    return {
      color: parseColor(style.primaryColor),
      fontSize: style.sectionTitleFontSize,
      fontFamily: style.fontFamily,
      fontWeight: '600',
      lineHeight: '1.4',
      marginBottom: '16px'
    }
  })
  
  /**
   * 子标题样式
   * @description 项目标题的样式配置
   */
  const subtitleStyle = computed(() => {
    const style = mergedTextStyle.value
    return {
      color: parseColor(style.textColor),
      fontSize: style.itemTitleFontSize,
      fontFamily: style.fontFamily,
      fontWeight: '500',
      lineHeight: '1.4',
      marginBottom: '8px'
    }
  })
  
  /**
   * 正文样式
   * @description 正文内容的样式配置
   */
  const bodyStyle = computed(() => {
    const style = mergedTextStyle.value
    return {
      color: parseColor(style.textColor),
      fontSize: style.bodyFontSize,
      fontFamily: style.fontFamily,
      fontWeight: '400',
      lineHeight: style.lineHeight,
      marginBottom: defaultSpacing.paragraphSpacing
    }
  })
  
  /**
   * 技能进度条样式
   * @description 技能等级进度条的样式配置
   */
  const skillProgressStyle = computed(() => {
    const style = mergedTextStyle.value
    const primaryColor = parseColor(style.primaryColor)
    const secondaryColor = parseColor(style.secondaryColor)
    
    return {
      background: generateGradient(primaryColor, secondaryColor, '90deg'),
      borderRadius: '4px',
      height: '8px',
      transition: 'width 0.3s ease-out'
    }
  })
  
  /**
   * 获取技能进度条样式
   * @param {number} level - 技能等级
   * @returns {Object} 进度条样式对象
   */
  const getSkillProgressStyle = (level) => {
    const baseStyle = skillProgressStyle.value
    return {
      ...baseStyle,
      width: `${level}%`
    }
  }
  
  /**
   * 模块容器样式
   * @description 模块容器的样式配置
   */
  const moduleContainerStyle = computed(() => {
    return {
      marginBottom: defaultSpacing.moduleSpacing,
      padding: defaultSpacing.sectionPadding,
      borderRadius: '8px',
      backgroundColor: '#ffffff',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.2s ease'
    }
  })
  
  /**
   * 项目容器样式
   * @description 项目项的样式配置
   */
  const itemContainerStyle = computed(() => {
    return {
      marginBottom: defaultSpacing.itemSpacing,
      padding: defaultSpacing.itemPadding,
      borderRadius: '6px',
      backgroundColor: '#f9fafb',
      border: '1px solid #e5e7eb',
      transition: 'all 0.2s ease'
    }
  })
  
  /**
   * 悬停样式
   * @description 鼠标悬停时的样式变化
   */
  const hoverStyle = computed(() => {
    const style = mergedTextStyle.value
    return {
      backgroundColor: '#f3f4f6',
      borderColor: parseColor(style.primaryColor),
      boxShadow: `0 4px 12px rgba(0, 0, 0, 0.1)`,
      transform: 'translateY(-2px)'
    }
  })
  
  /**
   * 操作按钮样式
   * @description 模块操作按钮的样式配置
   */
  const operationButtonStyle = computed(() => {
    const style = mergedTextStyle.value
    return {
      backgroundColor: parseColor(style.primaryColor),
      color: getContrastColor(style.primaryColor),
      border: 'none',
      borderRadius: '4px',
      padding: '4px 8px',
      fontSize: '12px',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      opacity: 0.8
    }
  })
  
  /**
   * 页面样式
   * @description 整个页面的样式配置
   */
  const pageStyle = computed(() => {
    const style = mergedTextStyle.value
    return {
      fontFamily: style.fontFamily,
      fontSize: style.fontSize,
      lineHeight: style.lineHeight,
      color: parseColor(style.textColor),
      backgroundColor: '#ffffff',
      padding: `${defaultMargins.top} ${defaultMargins.right} ${defaultMargins.bottom} ${defaultMargins.left}`,
      minHeight: '100vh',
      // CSS变量，供子组件使用
      '--primary-color': parseColor(style.primaryColor),
      '--secondary-color': parseColor(style.secondaryColor),
      '--text-color': parseColor(style.textColor),
      '--font-family': style.fontFamily,
      '--font-size': style.fontSize,
      '--line-height': style.lineHeight,
      '--module-spacing': defaultSpacing.moduleSpacing,
      '--item-spacing': defaultSpacing.itemSpacing,
      '--section-padding': defaultSpacing.sectionPadding,
      '--item-padding': defaultSpacing.itemPadding
    }
  })
  
  // ================================
  // 响应式样式方法
  // ================================
  
  /**
   * 获取响应式字体大小
   * @param {string} baseSize - 基础字体大小
   * @param {string} breakpoint - 断点 ('sm' | 'md' | 'lg')
   * @returns {string} 响应式字体大小
   */
  const getResponsiveFontSize = (baseSize, breakpoint = 'md') => {
    const sizeMap = {
      sm: '0.875rem',
      md: '1rem',
      lg: '1.125rem'
    }
    
    return sizeMap[breakpoint] || baseSize
  }
  
  /**
   * 获取响应式间距
   * @param {string} baseSpacing - 基础间距
   * @param {string} breakpoint - 断点
   * @returns {string} 响应式间距
   */
  const getResponsiveSpacing = (baseSpacing, breakpoint = 'md') => {
    const spacingMap = {
      sm: '12px',
      md: '16px',
      lg: '24px'
    }
    
    return spacingMap[breakpoint] || baseSpacing
  }
  
  // ================================
  // 主题切换方法
  // ================================
  
  /**
   * 应用主题
   * @param {Object} theme - 主题配置
   */
  const applyTheme = (theme) => {
    const root = document.documentElement
    
    // 应用CSS变量
    Object.entries(theme).forEach(([key, value]) => {
      root.style.setProperty(`--${key}`, value)
    })
  }
  
  /**
   * 预设主题
   */
  const presetThemes = {
    default: {
      'primary-color': '#3b82f6',
      'secondary-color': '#6366f1',
      'text-color': '#1f2937',
      'background-color': '#ffffff'
    },
    dark: {
      'primary-color': '#60a5fa',
      'secondary-color': '#a78bfa',
      'text-color': '#f9fafb',
      'background-color': '#111827'
    },
    warm: {
      'primary-color': '#f59e0b',
      'secondary-color': '#f97316',
      'text-color': '#1f2937',
      'background-color': '#fffbeb'
    },
    cool: {
      'primary-color': '#06b6d4',
      'secondary-color': '#0891b2',
      'text-color': '#1f2937',
      'background-color': '#f0f9ff'
    }
  }
  
  // ================================
  // 返回对象
  // ================================
  
  return {
    // 计算属性
    mergedTextStyle,
    titleStyle,
    subtitleStyle,
    bodyStyle,
    skillProgressStyle,
    moduleContainerStyle,
    itemContainerStyle,
    hoverStyle,
    operationButtonStyle,
    pageStyle,
    
    // 方法
    mergeStyles,
    parseColor,
    getContrastColor,
    generateGradient,
    getSkillProgressStyle,
    getResponsiveFontSize,
    getResponsiveSpacing,
    applyTheme,
    
    // 常量
    defaultTextStyle,
    defaultSpacing,
    defaultMargins,
    presetThemes
  }
} 