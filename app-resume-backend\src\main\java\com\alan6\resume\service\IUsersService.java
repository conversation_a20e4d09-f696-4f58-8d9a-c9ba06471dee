package com.alan6.resume.service;

import com.alan6.resume.dto.user.*;
import com.alan6.resume.entity.Users;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户表服务类
 * 
 * 主要功能：
 * 1. 提供用户基础CRUD操作
 * 2. 实现用户信息管理功能
 * 3. 处理用户头像上传
 * 4. 管理用户密码修改
 * 5. 处理手机号和邮箱绑定
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IUsersService extends IService<Users> {

    /**
     * 获取用户详细信息
     * 
     * 包含用户基本信息、会员信息等完整数据
     * 对敏感信息进行脱敏处理
     * 
     * @param userId 用户ID
     * @return 用户详细信息响应对象
     * @throws RuntimeException 当用户不存在时抛出异常
     */
    UserProfileResponse getUserProfile(Long userId);

    /**
     * 更新用户基本信息
     * 
     * 支持更新昵称、性别、生日、语言偏好等信息
     * 采用增量更新方式，只更新非空字段
     * 
     * @param userId 用户ID
     * @param request 用户信息更新请求
     * @return true-更新成功，false-更新失败
     * @throws RuntimeException 当用户不存在或更新失败时抛出异常
     */
    boolean updateUserProfile(Long userId, UserProfileRequest request);

    /**
     * 上传用户头像
     * 
     * 支持jpg、png、gif格式，文件大小不超过5MB
     * 自动生成缩略图和压缩处理
     * 
     * @param userId 用户ID
     * @param file 头像文件
     * @return 头像上传响应对象，包含新头像URL和文件信息
     * @throws RuntimeException 当文件格式不支持或上传失败时抛出异常
     */
    AvatarUploadResponse uploadAvatar(Long userId, MultipartFile file);

    /**
     * 修改用户密码
     * 
     * 支持两种验证方式：
     * 1. 原密码验证
     * 2. 短信验证码验证
     * 
     * @param userId 用户ID
     * @param request 密码修改请求
     * @return true-修改成功，false-修改失败
     * @throws RuntimeException 当验证失败或密码不符合要求时抛出异常
     */
    boolean changePassword(Long userId, PasswordChangeRequest request);

    /**
     * 获取用户设置信息
     * 
     * 目前主要包含语言偏好设置
     * 
     * @param userId 用户ID
     * @return 用户设置信息
     * @throws RuntimeException 当用户不存在时抛出异常
     */
    UserSettingsResponse getUserSettings(Long userId);

    /**
     * 更新用户设置信息
     * 
     * 目前主要支持语言偏好设置
     * 
     * @param userId 用户ID
     * @param request 用户设置更新请求
     * @return true-更新成功，false-更新失败
     * @throws RuntimeException 当用户不存在或更新失败时抛出异常
     */
    boolean updateUserSettings(Long userId, UserSettingsRequest request);

    /**
     * 绑定手机号
     * 
     * 验证短信验证码后绑定新手机号
     * 检查手机号是否已被其他用户使用
     * 
     * @param userId 用户ID
     * @param request 手机号绑定请求
     * @return true-绑定成功，false-绑定失败
     * @throws RuntimeException 当验证码错误或手机号已被使用时抛出异常
     */
    boolean bindPhone(Long userId, PhoneBindRequest request);

    /**
     * 解绑手机号
     * 
     * 通过短信验证码验证后解绑当前手机号
     * 
     * @param userId 用户ID
     * @param request 手机号解绑请求
     * @return true-解绑成功，false-解绑失败
     * @throws RuntimeException 当验证码错误时抛出异常
     */
    boolean unbindPhone(Long userId, PhoneBindRequest request);

    /**
     * 绑定邮箱
     * 
     * 验证邮箱验证码后绑定新邮箱
     * 检查邮箱是否已被其他用户使用
     * 
     * @param userId 用户ID
     * @param request 邮箱绑定请求
     * @return true-绑定成功，false-绑定失败
     * @throws RuntimeException 当验证码错误或邮箱已被使用时抛出异常
     */
    boolean bindEmail(Long userId, EmailBindRequest request);

    /**
     * 解绑邮箱
     * 
     * 通过邮箱验证码验证后解绑当前邮箱
     * 
     * @param userId 用户ID
     * @param request 邮箱解绑请求
     * @return true-解绑成功，false-解绑失败
     * @throws RuntimeException 当验证码错误时抛出异常
     */
    boolean unbindEmail(Long userId, EmailBindRequest request);

    /**
     * 注销用户账号
     * 
     * 支持密码验证或短信验证码验证
     * 注销后用户数据标记为删除状态
     * 
     * @param userId 用户ID
     * @param request 账号注销请求
     * @return true-注销成功，false-注销失败
     * @throws RuntimeException 当验证失败时抛出异常
     */
    boolean deactivateAccount(Long userId, AccountDeactivateRequest request);

    /**
     * 验证用户密码
     * 
     * 用于密码修改和账号注销时的身份验证
     * 
     * @param userId 用户ID
     * @param password 待验证的密码
     * @return true-密码正确，false-密码错误
     */
    boolean verifyPassword(Long userId, String password);

    /**
     * 验证短信验证码
     * 
     * 用于手机相关操作的验证
     * 
     * @param phone 手机号
     * @param smsCode 短信验证码
     * @return true-验证通过，false-验证失败
     */
    boolean verifySmsCode(String phone, String smsCode);

    /**
     * 验证邮箱验证码
     * 
     * 用于邮箱相关操作的验证
     * 
     * @param email 邮箱地址
     * @param emailCode 邮箱验证码
     * @return true-验证通过，false-验证失败
     */
    boolean verifyEmailCode(String email, String emailCode);

    // ======================== 收藏管理相关方法 ========================

    /**
     * 获取用户收藏列表
     * 
     * 支持按收藏类型筛选，分页查询
     * 返回收藏的详细信息，包括目标对象的基本信息
     * 
     * @param userId 用户ID
     * @param targetType 收藏类型（1:模板, 2:简历, null:全部）
     * @param current 当前页码
     * @param size 每页大小
     * @return 收藏列表分页响应
     * @throws RuntimeException 当用户不存在时抛出异常
     */
    com.baomidou.mybatisplus.extension.plugins.pagination.Page<UserFavoriteResponse> getUserFavorites(
            Long userId, Byte targetType, Long current, Long size);

    /**
     * 添加收藏
     * 
     * 检查收藏类型和目标ID的有效性
     * 防止重复收藏同一个对象
     * 
     * @param userId 用户ID
     * @param request 收藏请求
     * @return true-添加成功，false-添加失败
     * @throws RuntimeException 当目标不存在或已收藏时抛出异常
     */
    boolean addFavorite(Long userId, UserFavoriteRequest request);

    /**
     * 取消收藏
     * 
     * 根据收藏ID删除收藏记录
     * 只能删除当前用户的收藏
     * 
     * @param userId 用户ID
     * @param favoriteId 收藏ID
     * @return true-取消成功，false-取消失败
     * @throws RuntimeException 当收藏不存在或无权限时抛出异常
     */
    boolean removeFavorite(Long userId, Long favoriteId);
}
