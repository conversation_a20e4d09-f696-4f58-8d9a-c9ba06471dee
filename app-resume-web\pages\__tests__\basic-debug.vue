<template>
  <div class="basic-debug">
    <h1>基础调试页面</h1>
    
    <div class="section">
      <h2>1. Store 状态检查</h2>
      <div class="status-grid">
        <div class="status-item">
          <span>EditorStore: </span>
          <span :class="editorStore ? 'success' : 'error'">
            {{ editorStore ? '✅ 正常' : '❌ 异常' }}
          </span>
        </div>
        <div class="status-item">
          <span>currentResumeData: </span>
          <span :class="editorStore?.currentResumeData ? 'success' : 'error'">
            {{ editorStore?.currentResumeData ? '✅ 正常' : '❌ 异常' }}
          </span>
        </div>
        <div class="status-item">
          <span>modules: </span>
          <span :class="editorStore?.currentResumeData?.modules ? 'success' : 'error'">
            {{ editorStore?.currentResumeData?.modules ? '✅ 正常' : '❌ 异常' }}
          </span>
        </div>
        <div class="status-item">
          <span>modules 类型: </span>
          <span>{{ typeof editorStore?.currentResumeData?.modules }}</span>
        </div>
        <div class="status-item">
          <span>modules 内容: </span>
          <span>{{ JSON.stringify(editorStore?.currentResumeData?.modules) }}</span>
        </div>
      </div>
    </div>
    
    <div class="section">
      <h2>2. 数据更新测试</h2>
      <div class="test-area">
        <input v-model="testName" placeholder="输入姓名" />
        <button @click="updateData">更新数据</button>
        <button @click="logData">打印数据</button>
      </div>
      <div class="result-area">
        <p>输入值: {{ testName }}</p>
        <p>Store中的姓名: {{ storeName }}</p>
        <p>更新次数: {{ updateCount }}</p>
      </div>
    </div>
    
    <div class="section">
      <h2>3. 完整数据结构</h2>
      <pre class="data-dump">{{ JSON.stringify(editorStore?.currentResumeData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'

const editorStore = useEditorStore()
const testName = ref('')
const updateCount = ref(0)

const storeName = computed(() => {
  return editorStore?.currentResumeData?.modules?.basic_info?.name || '未设置'
})

const updateData = () => {
  console.log('更新数据:', testName.value)
  console.log('更新前的状态:', {
    currentResumeData: editorStore?.currentResumeData,
    modules: editorStore?.currentResumeData?.modules,
    modulesType: typeof editorStore?.currentResumeData?.modules
  })
  
  const basicInfo = {
    name: testName.value,
    title: '测试职位',
    phone: '13800138000',
    email: '<EMAIL>'
  }
  
  try {
    // 确保 modules 对象存在
    if (!editorStore?.currentResumeData?.modules) {
      console.log('modules 不存在，手动创建')
      if (editorStore?.currentResumeData) {
        editorStore.currentResumeData.modules = {}
      }
    }
    
    editorStore.updateModuleData('basic_info', basicInfo)
    updateCount.value++
    console.log('更新成功')
    console.log('更新后的状态:', {
      currentResumeData: editorStore?.currentResumeData,
      modules: editorStore?.currentResumeData?.modules,
      basicInfo: editorStore?.currentResumeData?.modules?.basic_info
    })
  } catch (error) {
    console.error('更新失败:', error)
  }
}

const logData = () => {
  console.log('当前数据:', {
    testName: testName.value,
    storeName: storeName.value,
    fullData: editorStore?.currentResumeData
  })
}

onMounted(async () => {
  console.log('初始化...')
  try {
    await editorStore.createNewResume()
    testName.value = '张三'
    console.log('初始化完成')
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.basic-debug {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.section h2 {
  margin-bottom: 15px;
  color: #333;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.success {
  color: #28a745;
  font-weight: bold;
}

.error {
  color: #dc3545;
  font-weight: bold;
}

.test-area {
  margin-bottom: 15px;
}

.test-area input {
  padding: 8px;
  margin-right: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.test-area button {
  padding: 8px 16px;
  margin-right: 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-area button:hover {
  background: #0056b3;
}

.result-area {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.result-area p {
  margin: 5px 0;
}

.data-dump {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style> 