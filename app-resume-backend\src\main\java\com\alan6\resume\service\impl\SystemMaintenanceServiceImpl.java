package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.common.utils.RedisUtils;
import com.alan6.resume.dto.system.CacheClearRequest;
import com.alan6.resume.dto.system.CacheClearResponse;
import com.alan6.resume.dto.system.MaintenanceRequest;
import com.alan6.resume.dto.system.MaintenanceResponse;
import com.alan6.resume.service.ISystemMaintenanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 系统维护服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemMaintenanceServiceImpl implements ISystemMaintenanceService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisUtils redisUtils;

    /**
     * 维护模式配置在Redis中的键
     */
    private static final String MAINTENANCE_MODE_KEY = "system:maintenance:config";
    private static final String CACHE_STATS_KEY = "system:cache:stats";

    /**
     * 清理缓存
     *
     * @param request 缓存清理请求
     * @return 清理结果
     */
    @Override
    public CacheClearResponse clearCache(CacheClearRequest request) {
        log.info("开始清理缓存, request: {}", request);
        
        long startTime = System.currentTimeMillis();
        int clearedKeys = 0;
        long freedMemory = 0;
        
        try {
            if (request == null || "all".equals(request.getCacheType())) {
                // 清理所有缓存
                clearedKeys = clearAllCache();
            } else if (request.getKeys() != null && !request.getKeys().isEmpty()) {
                // 按指定的键清理缓存
                for (String key : request.getKeys()) {
                    clearedKeys += clearCacheByPattern(key);
                }
            } else {
                // 按类型清理缓存
                clearedKeys = clearCacheByType(request.getCacheType());
            }
            
            // 计算释放的内存（估算）
            freedMemory = clearedKeys * 1024; // 假设每个key平均1KB
            
            long operationTime = System.currentTimeMillis() - startTime;
            
            // 构建响应
            CacheClearResponse response = new CacheClearResponse();
            response.setClearedKeys(clearedKeys);
            response.setFreedMemory(formatMemorySize(freedMemory));
            response.setOperationTime(operationTime + "ms");
            
            log.info("缓存清理完成, 清理键数: {}, 释放内存: {}, 耗时: {}ms", 
                    clearedKeys, response.getFreedMemory(), operationTime);
            
            return response;
            
        } catch (Exception e) {
            log.error("清理缓存失败: {}", e.getMessage(), e);
            throw new BusinessException("清理缓存失败: " + e.getMessage());
        }
    }

    /**
     * 设置维护模式
     *
     * @param request 维护模式请求
     * @return 设置结果
     */
    @Override
    public MaintenanceResponse setMaintenanceMode(MaintenanceRequest request) {
        log.info("设置维护模式, request: {}", request);
        
        try {
            // 构建维护配置
            MaintenanceResponse config = new MaintenanceResponse();
            config.setEnabled(request.getEnabled());
            config.setMessage(request.getMessage());
            config.setEstimatedEndTime(request.getEndTime());
            
            // 处理允许访问的IP列表
            if (request.getAllowedIps() != null && !request.getAllowedIps().isEmpty()) {
                config.setAllowedIps(String.join(",", request.getAllowedIps()));
            }
            
            config.setOperationTime(LocalDateTime.now());
            config.setOperator("ADMIN"); // TODO: 从安全上下文获取操作人
            
            if (request.getEnabled()) {
                config.setStartTime(request.getStartTime() != null ? request.getStartTime() : LocalDateTime.now());
                config.setEndTime(request.getEndTime());
            } else {
                config.setEndTime(LocalDateTime.now());
            }
            
            // 保存到Redis
            if (request.getEndTime() != null) {
                // 设置过期时间
                long ttl = java.time.Duration.between(LocalDateTime.now(), request.getEndTime()).getSeconds();
                if (ttl > 0) {
                    redisUtils.set(MAINTENANCE_MODE_KEY, config, ttl);
                } else {
                    redisUtils.set(MAINTENANCE_MODE_KEY, config);
                }
            } else {
                redisUtils.set(MAINTENANCE_MODE_KEY, config);
            }
            
            log.info("维护模式设置成功, enabled: {}, message: {}", 
                    request.getEnabled(), request.getMessage());
            
            return config;
            
        } catch (Exception e) {
            log.error("设置维护模式失败: {}", e.getMessage(), e);
            throw new BusinessException("设置维护模式失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前维护状态
     *
     * @return 维护状态
     */
    @Override
    public MaintenanceResponse getMaintenanceStatus() {
        try {
            MaintenanceResponse config = (MaintenanceResponse) redisUtils.get(MAINTENANCE_MODE_KEY);
            if (config == null) {
                // 默认非维护状态
                config = new MaintenanceResponse();
                config.setEnabled(false);
                config.setMessage("系统正常运行中");
            }
            return config;
        } catch (Exception e) {
            log.warn("获取维护状态失败: {}", e.getMessage());
            // 返回默认状态
            MaintenanceResponse config = new MaintenanceResponse();
            config.setEnabled(false);
            config.setMessage("系统正常运行中");
            return config;
        }
    }

    /**
     * 检查系统是否处于维护模式
     *
     * @return 是否维护模式
     */
    @Override
    public boolean isMaintenanceMode() {
        try {
            MaintenanceResponse config = getMaintenanceStatus();
            return config.getEnabled() != null && config.getEnabled();
        } catch (Exception e) {
            log.warn("检查维护模式状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 清理所有缓存
     *
     * @return 清理的键数量
     */
    private int clearAllCache() {
        try {
            Set<String> keys = redisTemplate.keys("*");
            if (keys != null && !keys.isEmpty()) {
                // 排除系统关键配置
                keys.removeIf(key -> key.startsWith("system:") || key.startsWith("security:"));
                if (!keys.isEmpty()) {
                    redisTemplate.delete(keys);
                    return keys.size();
                }
            }
            return 0;
        } catch (Exception e) {
            log.error("清理所有缓存失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 按类型清理缓存
     *
     * @param cacheType 缓存类型
     * @return 清理的键数量
     */
    private int clearCacheByType(String cacheType) {
        try {
            String pattern = getCachePattern(cacheType);
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                return keys.size();
            }
            return 0;
        } catch (Exception e) {
            log.error("清理缓存类型 {} 失败: {}", cacheType, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 根据缓存类型获取匹配模式
     *
     * @param cacheType 缓存类型
     * @return 匹配模式
     */
    private String getCachePattern(String cacheType) {
        switch (cacheType.toLowerCase()) {
            case "user":
                return "user:*";
            case "resume":
                return "resume:*";
            case "template":
                return "template:*";
            case "config":
                return "config:*";
            case "session":
                return "session:*";
            case "token":
                return "token:*";
            default:
                return cacheType + ":*";
        }
    }

    /**
     * 格式化内存大小
     *
     * @param bytes 字节数
     * @return 格式化后的大小
     */
    private String formatMemorySize(long bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2fKB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2fMB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.2fGB", bytes / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 按模式清理缓存
     *
     * @param pattern 匹配模式
     * @return 清理的键数量
     */
    private int clearCacheByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                // 排除系统关键配置
                keys.removeIf(key -> key.startsWith("system:") || key.startsWith("security:"));
                if (!keys.isEmpty()) {
                    redisTemplate.delete(keys);
                    return keys.size();
                }
            }
            return 0;
        } catch (Exception e) {
            log.error("清理缓存模式 {} 失败: {}", pattern, e.getMessage(), e);
            return 0;
        }
    }
} 