package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 热门模板请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "热门模板请求DTO")
public class TemplateHotRequest {

    /**
     * 返回数量（默认: 10，最大: 50）
     */
    @Schema(description = "返回数量", example = "10")
    private Integer limit = 10;

    /**
     * 分类ID（可选，指定分类的热门模板）
     */
    @Schema(description = "分类ID")
    private Long categoryId;

    /**
     * 时间范围（可选，week:本周, month:本月, all:全部，默认: month）
     */
    @Schema(description = "时间范围", example = "month")
    private String timeRange = "month";
} 