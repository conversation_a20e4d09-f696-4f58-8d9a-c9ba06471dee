package com.alan6.resume.service;

import com.alan6.resume.dto.system.HelpArticleResponse;
import com.alan6.resume.dto.system.HelpDocumentResponse;

/**
 * 帮助文档服务接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface IHelpDocumentService {

    /**
     * 获取帮助文档列表
     *
     * @param category 分类代码（可选）
     * @param keyword  搜索关键词（可选）
     * @return 帮助文档响应
     */
    HelpDocumentResponse getHelpDocuments(String category, String keyword);

    /**
     * 获取帮助文章详情
     *
     * @param articleId 文章ID
     * @return 文章详情
     */
    HelpArticleResponse getHelpArticle(Long articleId);

    /**
     * 增加文章浏览次数
     *
     * @param articleId 文章ID
     */
    void incrementViewCount(Long articleId);

    /**
     * 评价文章是否有用
     *
     * @param articleId 文章ID
     * @param helpful   是否有用（true:有用, false:无用）
     */
    void rateArticle(Long articleId, boolean helpful);

    /**
     * 搜索帮助文章
     *
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    HelpDocumentResponse searchArticles(String keyword);
} 