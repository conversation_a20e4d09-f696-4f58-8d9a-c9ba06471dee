/**
 * 管理员登录服务
 * @description 专门处理管理员后台登录功能
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, computed } from 'vue'

// ================================
// 响应式状态管理
// ================================
const loading = ref(false)
const error = ref(null)

// ================================
// 管理员登录服务类
// ================================
class AdminLoginService {
  /**
   * 执行管理员登录
   * @param {Object} loginData - 登录数据 {phone, password, platform}
   * @returns {Promise<Object>} 登录结果
   */
  static async login(loginData) {
    try {
      loading.value = true
      error.value = null

      console.log('🔒 开始管理员登录...')

      // 构建登录请求参数
      const loginRequest = {
        phone: loginData.phone,
        password: loginData.password,
        platform: loginData.platform || 'web'
      }

      console.log('🔒 管理员登录请求参数:', loginRequest)

      // 发送管理员登录请求
      const response = await $fetch('/api/auth/login-admin', {
        method: 'POST',
        body: loginRequest,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      })

      console.log('🔒 管理员登录响应:', response)

      if (response && response.code === 200 && response.data && response.data.token) {
        // 存储管理员Token（使用不同的key）
        if (process.client) {
          localStorage.setItem('admin_token', response.data.token)
          localStorage.setItem('admin_info', JSON.stringify(response.data))
          console.log('💾 管理员信息已存储到localStorage')
        }
        
        // 同时存储到cookie，支持服务端渲染
        const adminTokenCookie = useCookie('admin_token', {
          default: () => '',
          maxAge: 60 * 60 * 24 * 7, // 7天
          httpOnly: false,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax'
        })
        adminTokenCookie.value = response.data.token

        // 返回成功结果
        return {
          success: true,
          data: response.data,
          message: response.msg || '管理员登录成功'
        }
      } else {
        throw new Error(response?.msg || response?.message || '管理员登录失败')
      }

    } catch (err) {
      const errorMessage = this.handleLoginError(err)
      error.value = errorMessage
      
      console.error('❌ 管理员登录失败:', errorMessage)
      
      return {
        success: false,
        error: errorMessage,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理登录错误
   * @param {Error} error - 错误对象
   * @returns {string} 用户友好的错误消息
   */
  static handleLoginError(error) {
    const errorMessage = error.message || '管理员登录失败'
    
    // 根据错误类型返回不同的提示
    if (errorMessage.includes('用户不存在')) {
      return '账号不存在，请检查手机号'
    } else if (errorMessage.includes('密码错误')) {
      return '密码错误，请重新输入'
    } else if (errorMessage.includes('权限不足')) {
      return '权限不足，您不是管理员用户'
    } else if (errorMessage.includes('账户已被禁用')) {
      return '账户已被禁用，请联系系统管理员'
    } else if (errorMessage.includes('登录失败次数过多')) {
      return '登录失败次数过多，请稍后再试'
    } else if (errorMessage.includes('参数验证失败')) {
      return '输入信息有误，请检查后重试'
    } else {
      return errorMessage
    }
  }

  /**
   * 清除管理员登录状态
   */
  static clearAdminAuth() {
    if (process.client) {
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_info')
      console.log('🗑️ 管理员认证信息已清除')
    }
    
    // 清除cookie
    const adminTokenCookie = useCookie('admin_token')
    adminTokenCookie.value = null
  }

  /**
   * 检查是否已登录管理员
   * @returns {boolean} 是否已登录
   */
  static isAdminLoggedIn() {
    if (process.client) {
      const adminToken = localStorage.getItem('admin_token')
      return !!adminToken
    }
    return false
  }

  /**
   * 获取管理员信息
   * @returns {Object|null} 管理员信息
   */
  static getAdminInfo() {
    if (process.client) {
      const adminInfoStr = localStorage.getItem('admin_info')
      if (adminInfoStr) {
        try {
          return JSON.parse(adminInfoStr)
        } catch (e) {
          console.error('解析管理员信息失败:', e)
          return null
        }
      }
    }
    return null
  }
}

// ================================
// 组合式函数导出
// ================================
export const useAdminLoginService = () => {
  // 计算属性
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)

  /**
   * 执行管理员登录
   * @param {Object} loginData - 登录数据
   * @returns {Promise<Object>} 登录结果
   */
  const adminLogin = async (loginData) => {
    return await AdminLoginService.login(loginData)
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 清除管理员认证状态
   */
  const clearAdminAuth = () => {
    AdminLoginService.clearAdminAuth()
  }

  /**
   * 检查是否已登录管理员
   */
  const isAdminLoggedIn = () => {
    return AdminLoginService.isAdminLoggedIn()
  }

  /**
   * 获取管理员信息
   */
  const getAdminInfo = () => {
    return AdminLoginService.getAdminInfo()
  }

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    isLoading,
    hasError,
    
    // 方法
    adminLogin,
    clearError,
    clearAdminAuth,
    isAdminLoggedIn,
    getAdminInfo
  }
}

// 为了兼容性，添加readonly函数
function readonly(ref) {
  return computed(() => ref.value)
} 