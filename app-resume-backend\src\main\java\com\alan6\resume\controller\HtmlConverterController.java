package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.dto.template.HtmlConversionRequest;
import com.alan6.resume.dto.template.HtmlConversionResponse;
import com.alan6.resume.dto.template.HtmlValidationResult;
import com.alan6.resume.service.IHtmlToVueConverterService;
import com.alan6.resume.service.IHtmlValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * HTML转换控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/admin/html-converter")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
public class HtmlConverterController {
    
    private final IHtmlToVueConverterService converterService;
    private final IHtmlValidationService validationService;
    
    /**
     * 上传HTML文件
     * 
     * @param files HTML文件列表
     * @return 上传结果
     */
    @PostMapping("/upload")
    public R<Map<String, Object>> uploadHtmlFiles(@RequestParam("files") MultipartFile[] files) {
        log.info("上传HTML文件，数量: {}", files.length);
        
        try {
            // 生成任务ID
            String taskId = converterService.generateTaskId();
            
            // 创建任务目录
            String taskDir = converterService.createTaskDirectory(taskId);
            
            List<String> uploadedFiles = new ArrayList<>();
            List<HtmlValidationResult> validationResults = new ArrayList<>();
            
            // 保存上传的文件
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    continue;
                }
                
                String fileName = file.getOriginalFilename();
                if (fileName == null || !fileName.toLowerCase().endsWith(".html")) {
                    log.warn("跳过非HTML文件: {}", fileName);
                    continue;
                }
                
                try {
                    // 保存文件
                    Path filePath = Paths.get(taskDir, fileName);
                    Files.write(filePath, file.getBytes());
                    uploadedFiles.add(fileName);
                    
                    // 验证文件
                    String htmlContent = new String(file.getBytes());
                    HtmlValidationResult validation = validationService.validateHtmlContent(htmlContent, fileName);
                    validationResults.add(validation);
                    
                    log.debug("保存HTML文件: {}", fileName);
                    
                } catch (IOException e) {
                    log.error("保存文件失败: {}", fileName, e);
                }
            }
            
            // 生成验证统计
            Map<String, Object> statistics = generateUploadStatistics(validationResults);
            
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("uploadedFiles", uploadedFiles);
            result.put("validationResults", validationResults);
            result.put("statistics", statistics);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("上传HTML文件失败", e);
            return R.fail("上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证HTML文件
     * 
     * @param taskId 任务ID
     * @param fileNames 文件名列表
     * @return 验证结果
     */
    @PostMapping("/validate")
    public R<List<HtmlValidationResult>> validateHtmlFiles(
            @RequestParam("taskId") String taskId,
            @RequestParam("fileNames") List<String> fileNames) {
        
        log.info("验证HTML文件，任务ID: {}, 文件数量: {}", taskId, fileNames.size());
        
        try {
            String taskDir = converterService.getTaskDirectory(taskId);
            List<File> htmlFiles = new ArrayList<>();
            
            // 获取文件列表
            for (String fileName : fileNames) {
                File htmlFile = new File(taskDir, fileName);
                if (htmlFile.exists()) {
                    htmlFiles.add(htmlFile);
                } else {
                    log.warn("HTML文件不存在: {}", fileName);
                }
            }
            
            if (htmlFiles.isEmpty()) {
                return R.fail("未找到有效的HTML文件");
            }
            
            // 验证文件
            List<HtmlValidationResult> results = validationService.validateHtmlFiles(htmlFiles);
            
            return R.ok(results);
            
        } catch (Exception e) {
            log.error("验证HTML文件失败", e);
            return R.fail("验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换HTML文件为Vue组件
     * 
     * @param request 转换请求
     * @return 转换结果
     */
    @PostMapping("/convert")
    public R<HtmlConversionResponse> convertHtmlFiles(@Valid @RequestBody HtmlConversionRequest request) {
        log.info("转换HTML文件，类型: {}, 文件数量: {}", request.getConversionType(), request.getFileCount());
        
        try {
            // 使用请求中的任务ID
            String taskId = request.getTaskId();
            if (taskId == null || taskId.trim().isEmpty()) {
                return R.fail("任务ID不能为空");
            }
            
            // 获取任务目录
            String taskDir = converterService.getTaskDirectory(taskId);
            List<File> htmlFiles = new ArrayList<>();
            
            // 获取文件列表
            for (String fileName : request.getHtmlFiles()) {
                File htmlFile = new File(taskDir, fileName);
                if (htmlFile.exists()) {
                    htmlFiles.add(htmlFile);
                } else {
                    log.warn("HTML文件不存在: {}", fileName);
                }
            }
            
            if (htmlFiles.isEmpty()) {
                return R.fail("未找到有效的HTML文件");
            }
            
            // 根据转换类型执行转换
            HtmlConversionResponse response;
            if (request.isSingleConversion()) {
                response = converterService.convertSingleFile(htmlFiles.get(0), taskId);
            } else {
                response = converterService.convertBatchFiles(htmlFiles, taskId);
            }
            
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("转换HTML文件失败", e);
            return R.fail("转换失败: " + e.getMessage());
        }
    }
    
    /**
     * 单个文件转换
     * 
     * @param taskId 任务ID
     * @param fileName 文件名
     * @return 转换结果
     */
    @PostMapping("/convert/single")
    public R<HtmlConversionResponse> convertSingleFile(
            @RequestParam("taskId") String taskId,
            @RequestParam("fileName") String fileName) {
        
        log.info("转换单个HTML文件，任务ID: {}, 文件名: {}", taskId, fileName);
        
        try {
            String taskDir = converterService.getTaskDirectory(taskId);
            File htmlFile = new File(taskDir, fileName);
            
            if (!htmlFile.exists()) {
                return R.fail("HTML文件不存在: " + fileName);
            }
            
            // 执行转换
            HtmlConversionResponse response = converterService.convertSingleFile(htmlFile, taskId);
            
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("转换单个HTML文件失败", e);
            return R.fail("转换失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量文件转换
     * 
     * @param taskId 任务ID
     * @param fileNames 文件名列表
     * @return 转换结果
     */
    @PostMapping("/convert/batch")
    public R<HtmlConversionResponse> convertBatchFiles(
            @RequestParam("taskId") String taskId,
            @RequestParam("fileNames") List<String> fileNames) {
        
        log.info("批量转换HTML文件，任务ID: {}, 文件数量: {}", taskId, fileNames.size());
        
        try {
            String taskDir = converterService.getTaskDirectory(taskId);
            List<File> htmlFiles = new ArrayList<>();
            
            // 获取文件列表
            for (String fileName : fileNames) {
                File htmlFile = new File(taskDir, fileName);
                if (htmlFile.exists()) {
                    htmlFiles.add(htmlFile);
                } else {
                    log.warn("HTML文件不存在: {}", fileName);
                }
            }
            
            if (htmlFiles.isEmpty()) {
                return R.fail("未找到有效的HTML文件");
            }
            
            // 执行批量转换
            HtmlConversionResponse response = converterService.convertBatchFiles(htmlFiles, taskId);
            
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("批量转换HTML文件失败", e);
            return R.fail("转换失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取转换结果
     * 
     * @param taskId 任务ID
     * @return 转换结果信息
     */
    @GetMapping("/result/{taskId}")
    public R<Map<String, Object>> getConversionResult(@PathVariable String taskId) {
        log.info("获取转换结果，任务ID: {}", taskId);
        
        try {
            String taskDir = converterService.getTaskDirectory(taskId);
            Path taskPath = Paths.get(taskDir);
            
            if (!Files.exists(taskPath)) {
                return R.fail("任务不存在或已过期");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("taskDirectory", taskDir);
            
            // 获取文件列表
            List<Map<String, Object>> files = new ArrayList<>();
            Files.list(taskPath).forEach(filePath -> {
                File file = filePath.toFile();
                Map<String, Object> fileInfo = new HashMap<>();
                fileInfo.put("name", file.getName());
                fileInfo.put("size", file.length());
                fileInfo.put("type", file.getName().toLowerCase().endsWith(".vue") ? "vue" : "html");
                fileInfo.put("lastModified", file.lastModified());
                files.add(fileInfo);
            });
            
            result.put("files", files);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("获取转换结果失败", e);
            return R.fail("获取结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取支持的模块ID列表
     * 
     * @return 模块ID列表
     */
    @GetMapping("/modules")
    public R<List<String>> getSupportedModules() {
        log.info("获取支持的模块ID列表");
        
        try {
            // 直接从验证服务获取支持的模块列表，确保一致性
            List<String> modules = validationService.getSupportedModuleIds();
            
            return R.ok(modules);
            
        } catch (Exception e) {
            log.error("获取支持的模块ID失败", e);
            return R.fail("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取支持的字段名称列表
     * 
     * @return 字段名称列表
     */
    @GetMapping("/fields")
    public R<List<String>> getSupportedFields() {
        log.info("获取支持的字段名称列表");
        
        try {
            // 直接从验证服务获取支持的字段列表，确保一致性
            List<String> fields = validationService.getSupportedFieldNames();
            
            return R.ok(fields);
            
        } catch (Exception e) {
            log.error("获取支持的字段名称失败", e);
            return R.fail("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成验证报告
     * 
     * @param taskId 任务ID
     * @param fileNames 文件名列表
     * @return 验证报告
     */
    @PostMapping("/report")
    public R<String> generateValidationReport(
            @RequestParam("taskId") String taskId,
            @RequestParam("fileNames") List<String> fileNames) {
        
        log.info("生成验证报告，任务ID: {}, 文件数量: {}", taskId, fileNames.size());
        
        try {
            String taskDir = converterService.getTaskDirectory(taskId);
            List<File> htmlFiles = new ArrayList<>();
            
            // 获取文件列表
            for (String fileName : fileNames) {
                File htmlFile = new File(taskDir, fileName);
                if (htmlFile.exists()) {
                    htmlFiles.add(htmlFile);
                }
            }
            
            if (htmlFiles.isEmpty()) {
                return R.fail("未找到有效的HTML文件");
            }
            
            // 生成验证报告
            List<HtmlValidationResult> validationResults = validationService.validateHtmlFiles(htmlFiles);
            
            StringBuilder report = new StringBuilder();
            report.append("HTML验证报告\n");
            report.append("===================\n");
            report.append("任务ID: ").append(taskId).append("\n");
            report.append("验证时间: ").append(java.time.LocalDateTime.now()).append("\n\n");
            
            for (HtmlValidationResult result : validationResults) {
                report.append("文件: ").append(result.getFileName()).append("\n");
                report.append("状态: ").append(result.getValid() ? "通过" : "失败").append("\n");
                report.append("错误数量: ").append(result.getErrorCount()).append("\n");
                report.append("警告数量: ").append(result.getWarningCount()).append("\n");
                
                if (!result.getErrors().isEmpty()) {
                    report.append("错误详情:\n");
                    for (HtmlValidationResult.ValidationError error : result.getErrors()) {
                        report.append("  - ").append(error.getMessage()).append("\n");
                    }
                }
                
                if (!result.getWarnings().isEmpty()) {
                    report.append("警告详情:\n");
                    for (HtmlValidationResult.ValidationWarning warning : result.getWarnings()) {
                        report.append("  - ").append(warning.getMessage()).append("\n");
                    }
                }
                
                report.append("\n");
            }
            
            return R.ok(report.toString());
            
        } catch (Exception e) {
            log.error("生成验证报告失败", e);
            return R.fail("生成报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试下载接口
     * 
     * @param taskId 任务ID
     * @return 测试响应
     */
    @GetMapping("/test-download/{taskId}")
    public R<String> testDownload(@PathVariable String taskId) {
        log.info("测试下载接口，任务ID: {}", taskId);
        return R.ok("下载接口测试成功，任务ID: " + taskId);
    }
    
    /**
     * 下载转换结果
     * 
     * @param taskId 任务ID
     * @return 文件下载
     */
    @GetMapping("/result/{taskId}/download")
    public void downloadResults(@PathVariable String taskId, HttpServletResponse response) {
        log.info("下载转换结果，任务ID: {}", taskId);
        
        try {
            String taskDir = converterService.getTaskDirectory(taskId);
            Path taskPath = Paths.get(taskDir);
            
            if (!Files.exists(taskPath)) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("任务不存在或已过期");
                return;
            }
            
            // 创建临时ZIP文件
            String zipFileName = "conversion_results_" + taskId + ".zip";
            Path tempZipPath = Files.createTempFile("download_", ".zip");
            
            try (FileOutputStream fos = new FileOutputStream(tempZipPath.toFile());
                 ZipOutputStream zos = new ZipOutputStream(fos)) {
                
                // 添加任务目录中的所有文件到ZIP
                Files.walk(taskPath)
                    .filter(Files::isRegularFile)
                    .forEach(filePath -> {
                        try {
                            String entryName = taskPath.relativize(filePath).toString();
                            ZipEntry zipEntry = new ZipEntry(entryName);
                            zos.putNextEntry(zipEntry);
                            Files.copy(filePath, zos);
                            zos.closeEntry();
                        } catch (IOException e) {
                            log.error("添加文件到ZIP失败: {}", filePath, e);
                        }
                    });
            }
            
            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + zipFileName + "\"");
            response.setContentLengthLong(Files.size(tempZipPath));
            
            // 输出文件内容
            try (InputStream is = Files.newInputStream(tempZipPath);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
            
            // 删除临时文件
            Files.deleteIfExists(tempZipPath);
            
            log.info("转换结果下载完成，任务ID: {}", taskId);
            
        } catch (Exception e) {
            log.error("下载转换结果失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("下载失败: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }
    
    /**
     * 清理任务
     * 
     * @param taskId 任务ID
     * @return 清理结果
     */
    @DeleteMapping("/task/{taskId}")
    public R<String> cleanupTask(@PathVariable String taskId) {
        log.info("清理任务，任务ID: {}", taskId);
        
        try {
            boolean success = converterService.cleanupTask(taskId);
            
            if (success) {
                return R.ok("任务清理成功");
            } else {
                return R.fail("任务不存在或清理失败");
            }
            
        } catch (Exception e) {
            log.error("清理任务失败", e);
            return R.fail("清理失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成上传统计信息
     * 
     * @param validationResults 验证结果列表
     * @return 统计信息
     */
    private Map<String, Object> generateUploadStatistics(List<HtmlValidationResult> validationResults) {
        Map<String, Object> statistics = new HashMap<>();
        
        int totalFiles = validationResults.size();
        int validFiles = (int) validationResults.stream().filter(HtmlValidationResult::getValid).count();
        int invalidFiles = totalFiles - validFiles;
        int totalErrors = validationResults.stream().mapToInt(HtmlValidationResult::getErrorCount).sum();
        int totalWarnings = validationResults.stream().mapToInt(HtmlValidationResult::getWarningCount).sum();
        double validationRate = totalFiles > 0 ? (double) validFiles / totalFiles * 100 : 0;
        
        statistics.put("totalFiles", totalFiles);
        statistics.put("validFiles", validFiles);
        statistics.put("invalidFiles", invalidFiles);
        statistics.put("totalErrors", totalErrors);
        statistics.put("totalWarnings", totalWarnings);
        statistics.put("validationRate", Math.round(validationRate * 100.0) / 100.0);
        
        return statistics;
    }
} 