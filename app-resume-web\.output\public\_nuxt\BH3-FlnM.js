import{r as y,k as h,i as C,c as w,a as e,s as p,h as t,t as u,p as B,v as E,o as I}from"./CURHyiUL.js";import{u as M}from"./CEpU8TOc.js";import{_ as J}from"./DlAUqK2U.js";import"./x_rD_Ya3.js";const O={class:"basic-debug"},T={class:"section"},V={class:"status-grid"},z={class:"status-item"},U={class:"status-item"},j={class:"status-item"},q={class:"status-item"},A={class:"status-item"},F={class:"section"},G={class:"test-area"},H={class:"result-area"},K={class:"section"},L={class:"data-dump"},P={__name:"basic-debug",setup(Q){const s=M(),n=y(""),D=y(0),v=h(()=>{var o,a,l;return((l=(a=(o=s==null?void 0:s.currentResumeData)==null?void 0:o.modules)==null?void 0:a.basic_info)==null?void 0:l.name)||"未设置"}),N=()=>{var a,l,c,m,r,i;console.log("更新数据:",n.value),console.log("更新前的状态:",{currentResumeData:s==null?void 0:s.currentResumeData,modules:(a=s==null?void 0:s.currentResumeData)==null?void 0:a.modules,modulesType:typeof((l=s==null?void 0:s.currentResumeData)==null?void 0:l.modules)});const o={name:n.value,title:"测试职位",phone:"13800138000",email:"<EMAIL>"};try{(c=s==null?void 0:s.currentResumeData)!=null&&c.modules||(console.log("modules 不存在，手动创建"),s!=null&&s.currentResumeData&&(s.currentResumeData.modules={})),s.updateModuleData("basic_info",o),D.value++,console.log("更新成功"),console.log("更新后的状态:",{currentResumeData:s==null?void 0:s.currentResumeData,modules:(m=s==null?void 0:s.currentResumeData)==null?void 0:m.modules,basicInfo:(i=(r=s==null?void 0:s.currentResumeData)==null?void 0:r.modules)==null?void 0:i.basic_info})}catch(d){console.error("更新失败:",d)}},k=()=>{console.log("当前数据:",{testName:n.value,storeName:v.value,fullData:s==null?void 0:s.currentResumeData})};return C(async()=>{console.log("初始化...");try{await s.createNewResume(),n.value="张三",console.log("初始化完成")}catch(o){console.error("初始化失败:",o)}}),(o,a)=>{var l,c,m,r,i,d,R,_,f,g,b;return I(),w("div",O,[a[9]||(a[9]=e("h1",null,"基础调试页面",-1)),e("div",T,[a[6]||(a[6]=e("h2",null,"1. Store 状态检查",-1)),e("div",V,[e("div",z,[a[1]||(a[1]=e("span",null,"EditorStore: ",-1)),e("span",{class:p(t(s)?"success":"error")},u(t(s)?"✅ 正常":"❌ 异常"),3)]),e("div",U,[a[2]||(a[2]=e("span",null,"currentResumeData: ",-1)),e("span",{class:p((l=t(s))!=null&&l.currentResumeData?"success":"error")},u((c=t(s))!=null&&c.currentResumeData?"✅ 正常":"❌ 异常"),3)]),e("div",j,[a[3]||(a[3]=e("span",null,"modules: ",-1)),e("span",{class:p((r=(m=t(s))==null?void 0:m.currentResumeData)!=null&&r.modules?"success":"error")},u((d=(i=t(s))==null?void 0:i.currentResumeData)!=null&&d.modules?"✅ 正常":"❌ 异常"),3)]),e("div",q,[a[4]||(a[4]=e("span",null,"modules 类型: ",-1)),e("span",null,u(typeof((_=(R=t(s))==null?void 0:R.currentResumeData)==null?void 0:_.modules)),1)]),e("div",A,[a[5]||(a[5]=e("span",null,"modules 内容: ",-1)),e("span",null,u(JSON.stringify((g=(f=t(s))==null?void 0:f.currentResumeData)==null?void 0:g.modules)),1)])])]),e("div",F,[a[7]||(a[7]=e("h2",null,"2. 数据更新测试",-1)),e("div",G,[B(e("input",{"onUpdate:modelValue":a[0]||(a[0]=x=>n.value=x),placeholder:"输入姓名"},null,512),[[E,n.value]]),e("button",{onClick:N},"更新数据"),e("button",{onClick:k},"打印数据")]),e("div",H,[e("p",null,"输入值: "+u(n.value),1),e("p",null,"Store中的姓名: "+u(v.value),1),e("p",null,"更新次数: "+u(D.value),1)])]),e("div",K,[a[8]||(a[8]=e("h2",null,"3. 完整数据结构",-1)),e("pre",L,u(JSON.stringify((b=t(s))==null?void 0:b.currentResumeData,null,2)),1)])])}}},$=J(P,[["__scopeId","data-v-1652e513"]]);export{$ as default};
