<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简历 - 直小聘</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .resume-template {
            max-width: 210mm;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 297mm;
        }

        /* 头部信息区域 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 20px;
            overflow: hidden;
            background: #ddd;
        }

        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .name {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .job-title {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            font-size: 0.9em;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            min-height: calc(297mm - 200px);
        }

        .left-column {
            width: 35%;
            background: #f8f9fa;
            padding: 30px;
        }

        .right-column {
            width: 65%;
            padding: 30px;
        }

        /* 模块标题 */
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: #764ba2;
        }

        /* 技能模块 */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .skill-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .skill-name {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .skill-level {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            width: 85%;
        }

        /* 语言能力 */
        .language-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .language-item:last-child {
            border-bottom: none;
        }

        .language-level {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        /* 经历项目 */
        .experience-item {
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 1px solid #eee;
            position: relative;
        }

        .experience-item:last-child {
            border-bottom: none;
        }

        .experience-item::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 5px;
            width: 10px;
            height: 10px;
            background: #667eea;
            border-radius: 50%;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .experience-title {
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
        }

        .experience-company {
            color: #667eea;
            font-weight: 500;
        }

        .experience-date {
            color: #666;
            font-size: 0.9em;
        }

        .experience-description {
            margin-top: 10px;
            line-height: 1.6;
        }

        /* 自我评价和自荐信 */
        .text-content {
            line-height: 1.8;
            margin-bottom: 20px;
        }

        /* 获奖证书等列表项 */
        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-item-title {
            font-weight: 500;
            color: #333;
        }

        .list-item-meta {
            color: #666;
            font-size: 0.9em;
        }

        /* 作品集 */
        .portfolio-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .portfolio-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }

        .portfolio-link {
            color: #666;
            text-decoration: none;
            font-size: 0.9em;
        }

        /* 兴趣爱好 */
        .hobbies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
        }

        .hobby-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9em;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
            }
            
            .resume-template {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="resume-template" data-module-root="true">
        
        <!-- 头部信息 -->
        <header class="header" data-module="basic_info">
            <div class="avatar" data-field="photo" data-vue-if="resumeData.basic_info.photo">
                <img :src="resumeData.basic_info.photo" alt="个人照片">
            </div>
            <div class="name" data-field="name" data-vue-text="resumeData.basic_info.name">直小聘</div>
            <div class="job-title" data-field="jobTitle" data-vue-text="resumeData.basic_info.jobTitle">高级前端开发工程师</div>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📱</span>
                    <span data-field="phone" data-vue-text="resumeData.basic_info.phone">16666666666</span>
                </div>
                <div class="contact-item">
                    <span>✉️</span>
                    <span data-field="email" data-vue-text="resumeData.basic_info.email"><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📍</span>
                    <span data-field="currentCity" data-vue-text="resumeData.basic_info.currentCity">北京</span>
                </div>
                <div class="contact-item" data-field="wechat" data-vue-if="resumeData.basic_info.wechat">
                    <span>💬</span>
                    <span data-vue-text="resumeData.basic_info.wechat">wechat_id</span>
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- 左列 -->
            <div class="left-column">
                
                <!-- 技能特长 -->
                <section data-module="skills">
                    <h2 class="section-title">技能特长</h2>
                    <div class="skills-grid">
                        <div class="skill-item" data-vue-for="skill in resumeData.skills" data-vue-key="skill.id">
                            <div class="skill-name" data-field="name" data-vue-text="skill.name">JavaScript</div>
                            <div class="skill-level">
                                <div class="skill-progress"></div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 语言能力 -->
                <section data-module="language">
                    <h2 class="section-title">语言能力</h2>
                    <div class="language-item" data-vue-for="item in resumeData.language" data-vue-key="item.id">
                        <span data-field="name" data-vue-text="item.name">英语</span>
                        <span class="language-level" data-field="level" data-vue-text="item.level">CET-6</span>
                    </div>
                </section>

                <!-- 证书资质 -->
                <section data-module="certificate">
                    <h2 class="section-title">证书资质</h2>
                    <div class="list-item" data-vue-for="item in resumeData.certificate" data-vue-key="item.id">
                        <div class="list-item-title" data-field="name" data-vue-text="item.name">PMP项目管理认证</div>
                        <div class="list-item-meta" data-field="date" data-vue-text="item.date">2023-06</div>
                    </div>
                </section>

                <!-- 兴趣爱好 -->
                <section data-module="hobbies">
                    <h2 class="section-title">兴趣爱好</h2>
                    <div class="hobbies-grid">
                        <div class="hobby-item" data-vue-for="item in resumeData.hobbies" data-vue-key="item.id">
                            <span data-field="name" data-vue-text="item.name">摄影</span>
                        </div>
                    </div>
                </section>

            </div>

            <!-- 右列 -->
            <div class="right-column">
                
                <!-- 自我评价 -->
                <section data-module="self_evaluation">
                    <h2 class="section-title">自我评价</h2>
                    <div class="text-content" data-field="content" data-vue-text="resumeData.self_evaluation.content">
                        本人性格开朗，具有良好的沟通能力和团队协作精神，3年前端开发经验，熟练掌握Vue、React等现代前端框架，对用户体验有深入理解，能够独立完成复杂的前端项目开发。
                    </div>
                </section>

                <!-- 工作经历 -->
                <section data-module="work_experience">
                    <h2 class="section-title">工作经历</h2>
                    <div class="experience-item" data-vue-for="item in resumeData.work_experience" data-vue-key="item.id">
                        <div class="experience-header">
                            <div>
                                <div class="experience-title" data-field="position" data-vue-text="item.position">产品经理</div>
                                <div class="experience-company" data-field="company" data-vue-text="item.company">BOSS直聘</div>
                            </div>
                            <div class="experience-date">
                                <span data-field="startDate" data-vue-text="item.startDate">2021-03</span> - 
                                <span data-field="endDate" data-vue-text="item.endDate">2024-01</span>
                            </div>
                        </div>
                        <div class="experience-description" data-field="description" data-vue-text="item.description">
                            负责搜索策略产品设计和优化，通过数据分析和用户反馈，提升搜索匹配准确率30%，显著改善用户体验。主导完成了智能推荐系统的产品设计，月活跃用户增长45%。
                        </div>
                    </div>
                </section>

                <!-- 项目经验 -->
                <section data-module="project">
                    <h2 class="section-title">项目经验</h2>
                    <div class="experience-item" data-vue-for="item in resumeData.project" data-vue-key="item.id">
                        <div class="experience-header">
                            <div>
                                <div class="experience-title" data-field="name" data-vue-text="item.name">企业级管理平台</div>
                                <div class="experience-company" data-field="role" data-vue-text="item.role">前端架构师</div>
                            </div>
                            <div class="experience-date">
                                <span data-field="startDate" data-vue-text="item.startDate">2023-01</span> - 
                                <span data-field="endDate" data-vue-text="item.endDate">2023-12</span>
                            </div>
                        </div>
                        <div class="experience-description" data-field="description" data-vue-text="item.description">
                            负责前端架构设计和核心功能开发，使用Vue3 + TypeScript技术栈，构建了高性能的企业级管理系统。实现了组件化开发模式，提高了代码复用率60%。
                        </div>
                    </div>
                </section>

                <!-- 教育经历 -->
                <section data-module="education">
                    <h2 class="section-title">教育经历</h2>
                    <div class="experience-item" data-vue-for="item in resumeData.education" data-vue-key="item.id">
                        <div class="experience-header">
                            <div>
                                <div class="experience-title" data-field="school" data-vue-text="item.school">中国矿业大学</div>
                                <div class="experience-company">
                                    <span data-field="major" data-vue-text="item.major">交通运输工程</span> | 
                                    <span data-field="degree" data-vue-text="item.degree">硕士</span>
                                </div>
                            </div>
                            <div class="experience-date">
                                <span data-field="startDate" data-vue-text="item.startDate">2016-09</span> - 
                                <span data-field="endDate" data-vue-text="item.endDate">2019-07</span>
                            </div>
                        </div>
                        <div class="experience-description" data-field="description" data-vue-text="item.description">
                            研究生学生会副主席，GPA 3.8/4.0，获得优秀毕业生称号
                        </div>
                    </div>
                </section>

                <!-- 实习经历 -->
                <section data-module="internship">
                    <h2 class="section-title">实习经历</h2>
                    <div class="experience-item" data-vue-for="item in resumeData.internship" data-vue-key="item.id">
                        <div class="experience-header">
                            <div>
                                <div class="experience-title" data-field="position" data-vue-text="item.position">前端开发实习生</div>
                                <div class="experience-company" data-field="company" data-vue-text="item.company">腾讯科技</div>
                            </div>
                            <div class="experience-date">
                                <span data-field="startDate" data-vue-text="item.startDate">2019-06</span> - 
                                <span data-field="endDate" data-vue-text="item.endDate">2019-09</span>
                            </div>
                        </div>
                        <div class="experience-description" data-field="description" data-vue-text="item.description">
                            参与微信小程序的前端开发工作，负责用户界面设计和交互实现
                        </div>
                    </div>
                </section>

                <!-- 获奖荣誉 -->
                <section data-module="award">
                    <h2 class="section-title">获奖荣誉</h2>
                    <div class="list-item" data-vue-for="item in resumeData.award" data-vue-key="item.id">
                        <div class="list-item-title" data-field="name" data-vue-text="item.name">优秀毕业生</div>
                        <div class="list-item-meta" data-field="date" data-vue-text="item.date">2019-06</div>
                    </div>
                </section>

                <!-- 其他模块（可选）-->
                
                <!-- 培训经历 -->
                <section data-module="training" style="display: none;">
                    <h2 class="section-title">培训经历</h2>
                    <div class="list-item" data-vue-for="item in resumeData.training" data-vue-key="item.id">
                        <div class="list-item-title" data-field="course" data-vue-text="item.course">React高级开发培训</div>
                        <div class="list-item-meta" data-field="date" data-vue-text="item.date">2023-03</div>
                    </div>
                </section>

                <!-- 志愿服务 -->
                <section data-module="volunteer_experience" style="display: none;">
                    <h2 class="section-title">志愿服务</h2>
                    <div class="experience-item" data-vue-for="item in resumeData.volunteer_experience" data-vue-key="item.id">
                        <div class="experience-header">
                            <div>
                                <div class="experience-title" data-field="role" data-vue-text="item.role">志愿者</div>
                                <div class="experience-company" data-field="organization" data-vue-text="item.organization">红十字会</div>
                            </div>
                            <div class="experience-date">
                                <span data-field="startDate" data-vue-text="item.startDate">2022-01</span> - 
                                <span data-field="endDate" data-vue-text="item.endDate">2022-12</span>
                            </div>
                        </div>
                        <div class="experience-description" data-field="description" data-vue-text="item.description">
                            参与社区志愿服务活动，帮助弱势群体
                        </div>
                    </div>
                </section>

                <!-- 研究经历 -->
                <section data-module="research_experience" style="display: none;">
                    <h2 class="section-title">研究经历</h2>
                    <div class="experience-item" data-vue-for="item in resumeData.research_experience" data-vue-key="item.id">
                        <div class="experience-header">
                            <div>
                                <div class="experience-title" data-field="topic" data-vue-text="item.topic">机器学习研究</div>
                                <div class="experience-company" data-field="organization" data-vue-text="item.organization">某研究院</div>
                            </div>
                            <div class="experience-date">
                                <span data-field="startDate" data-vue-text="item.startDate">2018-03</span> - 
                                <span data-field="endDate" data-vue-text="item.endDate">2019-06</span>
                            </div>
                        </div>
                        <div class="experience-description" data-field="description" data-vue-text="item.description">
                            参与深度学习算法研究，发表相关论文2篇
                        </div>
                    </div>
                </section>

                <!-- 论文专利 -->
                <section data-module="publication" style="display: none;">
                    <h2 class="section-title">论文专利</h2>
                    <div class="list-item" data-vue-for="item in resumeData.publication" data-vue-key="item.id">
                        <div class="list-item-title" data-field="title" data-vue-text="item.title">深度学习算法研究</div>
                        <div class="list-item-meta" data-field="date" data-vue-text="item.date">2019-05</div>
                    </div>
                </section>

                <!-- 作品集 -->
                <section data-module="portfolio" style="display: none;">
                    <h2 class="section-title">作品集</h2>
                    <div class="portfolio-item" data-vue-for="item in resumeData.portfolio" data-vue-key="item.id">
                        <div class="portfolio-title" data-field="title" data-vue-text="item.title">个人博客网站</div>
                        <a class="portfolio-link" data-field="url" data-vue-text="item.url" href="#">https://myblog.com</a>
                        <div class="experience-description" data-field="description" data-vue-text="item.description">
                            使用Vue3 + Nuxt3开发的个人博客系统
                        </div>
                    </div>
                </section>

                <!-- 自荐信 -->
                <section data-module="cover_letter" style="display: none;">
                    <h2 class="section-title">自荐信</h2>
                    <div class="text-content" data-field="content" data-vue-text="resumeData.cover_letter.content">
                        尊敬的HR，您好！我是一名有着3年前端开发经验的工程师，对贵公司的前端开发岗位非常感兴趣。我具有扎实的前端技术功底和丰富的项目经验，希望能够加入贵公司，为公司的发展贡献自己的力量。
                    </div>
                </section>

                <!-- 自定义模块 -->
                <section data-module="custom" style="display: none;">
                    <h2 class="section-title">社交主页</h2>
                    <div class="list-item" data-vue-for="item in resumeData.custom" data-vue-key="item.id">
                        <div class="list-item-title" data-field="name" data-vue-text="item.name">知乎专栏</div>
                        <div class="list-item-meta" data-field="description" data-vue-text="item.description">技术分享</div>
                    </div>
                </section>

            </div>
        </div>
    </div>
</body>
</html>