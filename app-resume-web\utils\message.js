/**
 * 消息提示工具
 * @description 提供全局消息提示功能
 */

class MessageService {
  constructor() {
    this.container = null
    this.init()
  }

  init() {
    if (process.client) {
      this.container = document.createElement('div')
      this.container.id = 'message-container'
      this.container.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        pointer-events: none;
      `
      document.body.appendChild(this.container)
    }
  }

  show(message, type = 'info', duration = 3000) {
    if (!process.client) return

    const messageEl = document.createElement('div')
    messageEl.style.cssText = `
      background: ${this.getBackgroundColor(type)};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      margin-bottom: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-20px) scale(0.9);
      opacity: 0;
      transition: all 0.3s ease;
      pointer-events: auto;
      max-width: 300px;
      word-wrap: break-word;
    `
    messageEl.textContent = message

    this.container.appendChild(messageEl)

    // 显示动画
    setTimeout(() => {
      messageEl.style.transform = 'translateY(0) scale(1)'
      messageEl.style.opacity = '1'
    }, 10)

    // 自动隐藏
    setTimeout(() => {
      messageEl.style.transform = 'translateY(-20px) scale(0.9)'
      messageEl.style.opacity = '0'
      setTimeout(() => {
        if (messageEl.parentNode) {
          messageEl.parentNode.removeChild(messageEl)
        }
      }, 300)
    }, duration)
  }

  getBackgroundColor(type) {
    const colors = {
      success: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      info: '#3b82f6'
    }
    return colors[type] || colors.info
  }

  success(message, duration) {
    this.show(message, 'success', duration)
  }

  error(message, duration) {
    this.show(message, 'error', duration)
  }

  warning(message, duration) {
    this.show(message, 'warning', duration)
  }

  info(message, duration) {
    this.show(message, 'info', duration)
  }
}

let messageInstance = null

export const useMessage = () => {
  if (!messageInstance) {
    messageInstance = new MessageService()
  }
  return messageInstance
} 