package com.alan6.resume.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 简历主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@Schema(name = "Resumes对象", description = "简历主表")
public class Resumes implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "简历ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "使用的模板ID")
    private Long templateId;

    @Schema(description = "简历名称")
    private String name;

    @Schema(description = "简历语言（zh-CN,en,ja,ko等）")
    private String language;

    @Schema(description = "父简历ID（从哪个简历生成的）")
    private Long parentResumeId;

    @Schema(description = "MongoDB文档ID")
    private String mongoDocumentId;

    @Schema(description = "是否公开（0:私有,1:公开）")
    private Byte isPublic;

    @Schema(description = "浏览次数")
    private Integer viewCount;

    @Schema(description = "下载次数")
    private Integer downloadCount;

    @Schema(description = "分享次数")
    private Integer shareCount;

    @Schema(description = "状态（0:草稿,1:完成）")
    private Byte status;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "最后保存时间")
    private LocalDateTime lastSaveTime;
}
