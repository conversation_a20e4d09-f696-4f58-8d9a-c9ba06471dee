package com.alan6.resume.config;

import com.alan6.resume.security.filter.TokenAuthenticationFilter;
import com.alan6.resume.security.handler.AuthenticationEntryPointImpl;
import com.alan6.resume.security.handler.AccessDeniedHandlerImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security安全配置类
 * <p>
 * 主要功能：
 * 1. 配置HTTP安全策略
 * 2. 设置认证和授权规则
 * 3. 配置自定义过滤器和异常处理器
 * 4. 配置跨域访问策略
 * 5. 配置密码加密方式
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    /**
     * Token认证过滤器
     */
    @Autowired
    private TokenAuthenticationFilter tokenAuthenticationFilter;

    /**
     * 认证入口点处理器
     */
    @Autowired
    private AuthenticationEntryPointImpl authenticationEntryPoint;

    /**
     * 访问拒绝处理器
     */
    @Autowired
    private AccessDeniedHandlerImpl accessDeniedHandler;

    /**
     * 配置Security过滤器链
     * <p>
     * 安全配置策略：
     * - 禁用CSRF保护（前后端分离项目不需要）
     * - 配置无状态会话管理（使用Token认证）
     * - 设置公开访问路径（登录、注册等接口）
     * - 其他请求需要认证
     * - 配置自定义认证过滤器和异常处理器
     *
     * @param http HttpSecurity配置对象
     * @return 配置好的SecurityFilterChain
     * @throws Exception 配置异常
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF保护，因为前后端分离项目使用Token认证
                .csrf(csrf -> csrf.disable())

                // 配置跨域访问
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))

                // 配置会话管理为无状态（STATELESS）
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))

                // 配置请求授权
                .authorizeHttpRequests(auth -> auth
                        // 允许公开接口访问
                        .requestMatchers(
                                // 认证相关公开接口
                                "/auth/send-code",
                                "/auth/login",
                                "/auth/register",
                                "/auth/password-login",
                                "/auth/login-admin",

                                // 系统接口
                                "/health",
                                "/actuator/health",
                                "/error",

                                // 静态资源
                                "/static/**",
                                "/favicon.ico",

                                // API文档（开发环境）
                                "/swagger-ui/**",
                                "/v3/api-docs/**",
                                "/swagger-ui.html",
                                "/swagger-resources/**",
                                "/webjars/**",

                                // 分享相关公开接口
                                "/share/**",
                                "/public/**",

                                // 简历导出下载接口
                                "/resume/export/download/**",

                                // 占位符图片接口
                                "/admin/placeholder/**",
                                "/api/placeholder/**",

                                // 模板相关公开接口
                                "/template/list",
                                "/template/detail/**",
                                "/template/categories",
                                "/template/search/**",

                                // 管理员模板预览图片（公开访问，便于img标签加载）
                                "/admin/template/preview-image/**",
                                "/api/admin/template/preview-image/**",

                                // 测试接口（开发环境）
                                "/test/**"
                        ).permitAll()

                        // 允许Swagger相关接口公开访问（兼容配置）
                        .requestMatchers("/swagger-resources/**", "/webjars/**").permitAll()
                        .requestMatchers("/doc.html").permitAll()

                        // 允许Druid监控界面公开访问
                        .requestMatchers("/druid/**").permitAll()

                        // 其他所有请求都需要认证
                        .anyRequest().authenticated()
                )

                // 配置异常处理
                .exceptionHandling(exception -> exception
                        // 认证失败时的处理器
                        .authenticationEntryPoint(authenticationEntryPoint)
                        // 权限不足时的处理器
                        .accessDeniedHandler(accessDeniedHandler)
                )

                // 添加Token认证过滤器，放在UsernamePasswordAuthenticationFilter之前
                .addFilterBefore(tokenAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        log.info("Spring Security配置初始化完成");

        return http.build();
    }

    /**
     * 配置跨域访问策略
     * <p>
     * 允许前端项目跨域访问后端API：
     * - 允许所有源地址访问（生产环境应该限制具体域名）
     * - 允许所有HTTP方法
     * - 允许所有请求头
     * - 允许携带认证信息（如Cookie、Authorization头）
     *
     * @return 跨域配置源
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // 允许所有源地址访问（生产环境建议限制具体域名）
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));

        // 允许所有HTTP方法
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));

        // 允许所有请求头
        configuration.setAllowedHeaders(Arrays.asList("*"));

        // 允许携带认证信息
        configuration.setAllowCredentials(true);

        // 预检请求的缓存时间（秒）
        configuration.setMaxAge(3600L);

        // 应用到所有路径
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        log.info("跨域配置初始化完成");

        return source;
    }

    /**
     * 配置密码加密器
     * <p>
     * 使用BCrypt算法进行密码加密：
     * - BCrypt是一种安全的哈希算法
     * - 每次加密结果都不相同，增加安全性
     * - 加密强度可配置，默认为10
     *
     * @return BCrypt密码加密器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        log.info("密码加密器配置完成，使用BCrypt算法");
        return new BCryptPasswordEncoder();
    }
} 