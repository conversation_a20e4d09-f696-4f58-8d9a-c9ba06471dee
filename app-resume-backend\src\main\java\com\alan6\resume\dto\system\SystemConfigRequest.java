package com.alan6.resume.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 系统配置请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "SystemConfigRequest", description = "系统配置请求")
public class SystemConfigRequest {

    /**
     * 配置键
     */
    @NotBlank(message = "配置键不能为空")
    @Size(max = 100, message = "配置键长度不能超过100字符")
    @Schema(description = "配置键", example = "system.file.max_size")
    private String configKey;

    /**
     * 配置值
     */
    @Schema(description = "配置值", example = "52428800")
    private String configValue;

    /**
     * 配置类型
     */
    @Size(max = 20, message = "配置类型长度不能超过20字符")
    @Schema(description = "配置类型（string,number,boolean,json）", example = "number")
    private String configType = "string";

    /**
     * 配置分组
     */
    @Size(max = 50, message = "配置分组长度不能超过50字符")
    @Schema(description = "配置分组", example = "file")
    private String configGroup;

    /**
     * 配置描述
     */
    @Size(max = 200, message = "配置描述长度不能超过200字符")
    @Schema(description = "配置描述", example = "文件上传最大大小限制（字节）")
    private String description;
} 