<!-- 
  认证测试页面
  @description 用于调试认证token获取和传递问题
  <AUTHOR>
-->
<template>
  <div class="auth-test-page">
    <div class="container">
      <h1 class="title">认证状态测试</h1>
      
      <!-- 认证状态显示 -->
      <div class="auth-status">
        <h2>当前认证状态</h2>
        <div class="status-grid">
          <div class="status-item">
            <label>登录状态:</label>
            <span :class="isLoggedIn ? 'status-success' : 'status-error'">
              {{ isLoggedIn ? '已登录' : '未登录' }}
            </span>
          </div>
          
          <div class="status-item">
            <label>初始化状态:</label>
            <span :class="isInitialized ? 'status-success' : 'status-warning'">
              {{ isInitialized ? '已初始化' : '未初始化' }}
            </span>
          </div>
          
          <div class="status-item">
            <label>Token:</label>
            <span :class="token ? 'status-success' : 'status-error'">
              {{ token ? `${token.substring(0, 20)}...` : '无Token' }}
            </span>
          </div>
          
          <div class="status-item">
            <label>用户信息:</label>
            <span :class="currentUser ? 'status-success' : 'status-error'">
              {{ currentUser ? '已获取' : '未获取' }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 用户信息显示 -->
      <div v-if="currentUser" class="user-info">
        <h2>用户信息</h2>
        <pre>{{ JSON.stringify(currentUser, null, 2) }}</pre>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <button @click="refreshToken" class="btn btn-primary">
          刷新Token
        </button>
        <button @click="testApiCall" class="btn btn-secondary">
          测试API调用
        </button>
        <button @click="testDownload" class="btn btn-success">
          测试下载功能
        </button>
      </div>
      
      <!-- 测试结果 -->
      <div class="test-results">
        <h2>测试结果</h2>
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            :class="['log-item', `log-${log.type}`]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// ================================
// 页面元数据
// ================================
definePageMeta({
  title: '认证测试',
  description: '认证状态和Token测试页面'
})

// ================================
// 响应式状态
// ================================
const authService = useAuthService()
const downloadService = useDownloadService()

const logs = ref([])
const token = ref(null)

// ================================
// 计算属性
// ================================
const isLoggedIn = computed(() => authService.isLoggedIn.value)
const isInitialized = computed(() => authService.isInitialized.value)
const currentUser = computed(() => authService.currentUser.value)

// ================================
// 工具方法
// ================================

/**
 * 添加日志
 */
const addLog = (message, type = 'info') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  
  logs.value.unshift({
    time,
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

/**
 * 刷新Token
 */
const refreshToken = async () => {
  try {
    addLog('开始刷新Token...', 'info')
    
    // 获取Token
    const currentToken = authService.getToken()
    token.value = currentToken
    
    addLog(`Token获取结果: ${currentToken ? '成功' : '失败'}`, currentToken ? 'success' : 'error')
    
    if (currentToken) {
      addLog(`Token内容: ${currentToken.substring(0, 50)}...`, 'info')
    }
    
    // 验证Token
    const isValid = await authService.validateToken()
    addLog(`Token验证结果: ${isValid ? '有效' : '无效'}`, isValid ? 'success' : 'error')
    
  } catch (error) {
    addLog(`刷新Token失败: ${error.message}`, 'error')
  }
}

/**
 * 测试API调用
 */
const testApiCall = async () => {
  try {
    addLog('开始测试API调用...', 'info')
    
    const token = authService.getToken()
    addLog(`使用Token: ${token ? 'Yes' : 'No'}`, token ? 'success' : 'warning')
    
    // 构造请求头
    const headers = {
      'Content-Type': 'application/json'
    }
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
      addLog(`添加Authorization头: Bearer ${token.substring(0, 20)}...`, 'info')
    }
    
    // 发送测试请求
    const response = await $fetch('/api/user/profile', {
      method: 'GET',
      headers: headers
    })
    
    addLog('API调用成功', 'success')
    addLog(`响应数据: ${JSON.stringify(response)}`, 'info')
    
  } catch (error) {
    addLog(`API调用失败: ${error.message}`, 'error')
    addLog(`错误详情: ${JSON.stringify(error)}`, 'error')
  }
}

/**
 * 测试下载功能
 */
const testDownload = async () => {
  try {
    addLog('开始测试下载功能...', 'info')
    
    const token = authService.getToken()
    addLog(`当前Token状态: ${token ? '存在' : '不存在'}`, token ? 'success' : 'error')
    
    if (!token) {
      addLog('没有Token，无法进行下载测试', 'error')
      return
    }
    
    // 测试下载
    await downloadService.downloadResume({
      resumeId: '1001',
      format: 'pdf',
      resumeData: {
        basicInfo: {
          name: '测试用户',
          title: '前端工程师'
        }
      },
      settings: {}
    })
    
    addLog('下载测试完成', 'success')
    
  } catch (error) {
    addLog(`下载测试失败: ${error.message}`, 'error')
  }
}

// ================================
// 生命周期
// ================================
onMounted(async () => {
  addLog('页面加载完成', 'info')
  
  // 初始化认证
  if (!authService.isInitialized.value) {
    addLog('开始初始化认证状态...', 'info')
    await authService.initAuth()
    addLog('认证状态初始化完成', 'success')
  }
  
  // 获取当前Token
  await refreshToken()
})
</script>

<style scoped>
.auth-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.title {
  text-align: center;
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 30px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.auth-status,
.user-info,
.actions,
.test-results {
  background: white;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.auth-status h2,
.user-info h2,
.test-results h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
}

.status-item label {
  font-weight: 500;
  color: #374151;
}

.status-success {
  color: #10b981;
  font-weight: 600;
}

.status-error {
  color: #ef4444;
  font-weight: 600;
}

.status-warning {
  color: #f59e0b;
  font-weight: 600;
}

.user-info pre {
  background: #f8fafc;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  font-size: 0.875rem;
  overflow-x: auto;
}

.actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover {
  background: #059669;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
}

.log-item {
  display: flex;
  gap: 15px;
  padding: 10px 15px;
  border-bottom: 1px solid #e5e7eb;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #6b7280;
  font-weight: 500;
  min-width: 80px;
}

.log-message {
  flex: 1;
  color: #1f2937;
}

.log-info .log-message {
  color: #3b82f6;
}

.log-success .log-message {
  color: #10b981;
  font-weight: 500;
}

.log-error .log-message {
  color: #ef4444;
  font-weight: 500;
}

.log-warning .log-message {
  color: #f59e0b;
  font-weight: 500;
}

@media (max-width: 768px) {
  .auth-test-page {
    padding: 15px;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .log-item {
    flex-direction: column;
    gap: 5px;
  }
}
</style> 