<template>
  <div class="user-status">
    <!-- 认证状态初始化中 -->
    <div 
      v-if="!authService.isInitialized.value"
      class="login-actions"
    >
      <div class="loading-btn">
        <div class="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2"></div>
        <span class="text-sm text-gray-500">加载中...</span>
      </div>
    </div>

    <!-- 未登录状态 -->
    <div v-else-if="!isLoggedIn" class="login-actions">
      <button
        class="login-register-btn"
        @click="onRequestLogin"
      >
        登录 | 注册
      </button>
    </div>

    <!-- 已登录状态 -->
    <div v-else class="user-menu">
      <div class="user-avatar" @click="toggleDropdown">
        <img 
          v-if="currentUser?.avatar" 
          :src="currentUser.avatar" 
          :alt="currentUser.nickname || currentUser.username"
          class="avatar-image"
        />
        <div v-else class="avatar-placeholder">
          {{ getCurrentUserInitial() }}
        </div>
        <Icon name="arrow-down" size="xs" class="dropdown-arrow" />
      </div>

      <!-- 用户下拉菜单 -->
      <div v-if="showDropdown" class="dropdown-menu">
        <div class="user-info">
          <div class="user-name">{{ getCurrentUserName() }}</div>
          <div class="user-email">{{ getCurrentUserEmail() }}</div>
        </div>
        
        <div class="menu-divider"></div>
        
        <NuxtLink to="/profile" class="menu-item" @click="closeDropdown">
          <Icon name="dashboard" size="sm" />
          个人中心
        </NuxtLink>
        
        <NuxtLink to="/my-resumes" class="menu-item" @click="closeDropdown">
          <Icon name="document" size="sm" />
          我的简历
        </NuxtLink>
        
        <NuxtLink to="/profile" class="menu-item" @click="closeDropdown">
          <Icon name="settings" size="sm" />
          账户设置
        </NuxtLink>
        
        <div class="menu-divider"></div>
        
        <button class="menu-item logout-item" @click="handleLogout">
          <Icon name="logout" size="sm" />
          退出登录
        </button>
      </div>
    </div>

    <!-- 点击遮罩关闭下拉菜单 -->
    <div 
      v-if="showDropdown" 
      class="dropdown-overlay"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup>
/**
 * 用户状态组件
 * @description 显示用户登录状态，提供登录、注册和用户菜单功能
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'
import { useAuthService } from '~/composables/auth/useAuthService'

// ================================
// 组件事件
// ================================
const emit = defineEmits(['request-login'])

// ================================
// 认证服务
// ================================
const authService = useAuthService()

// ================================
// 响应式数据
// ================================
const showDropdown = ref(false)

// ================================
// 计算属性
// ================================
const isLoggedIn = computed(() => authService.isLoggedIn.value)
const currentUser = computed(() => authService.currentUser.value)

// ================================
// 生命周期
// ================================
onMounted(() => {
  // 添加全局点击事件监听，用于关闭下拉菜单
  document.addEventListener('click', handleGlobalClick)
  
  // 初始化认证状态（如果还没有初始化）
  if (!authService.isInitialized.value) {
    authService.initAuth()
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
})

// ================================
// 方法定义
// ================================

/**
 * 获取当前用户姓名首字母
 */
const getCurrentUserInitial = () => {
  const user = currentUser.value
  if (!user) return '用'
  
  const name = user.nickname || user.username || user.phone
  return name ? name.charAt(0).toUpperCase() : '用'
}

/**
 * 获取当前用户显示名称
 */
const getCurrentUserName = () => {
  const user = currentUser.value
  if (!user) return '用户'
  
  return user.nickname || user.username || '用户'
}

/**
 * 获取当前用户邮箱
 */
const getCurrentUserEmail = () => {
  const user = currentUser.value
  return user?.email || ''
}

/**
 * 触发登录请求
 */
const onRequestLogin = () => {
  emit('request-login')
}

/**
 * 切换下拉菜单显示状态
 */
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

/**
 * 关闭下拉菜单
 */
const closeDropdown = () => {
  showDropdown.value = false
}

/**
 * 处理全局点击事件
 * @param {Event} event - 点击事件
 */
const handleGlobalClick = (event) => {
  const userMenu = event.target.closest('.user-menu')
  if (!userMenu && showDropdown.value) {
    closeDropdown()
  }
}

/**
 * 处理用户退出登录
 */
const handleLogout = async () => {
  try {
    // 使用全局认证服务进行登出
    await authService.logout()
    
    closeDropdown()
    
    console.log('用户已退出登录')
    
    // 可以重定向到首页
    await navigateTo('/')
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}
</script>

<style scoped>
.user-status {
  @apply relative;
}

/* 未登录状态样式 */
.login-actions {
  @apply flex items-center;
}

.login-register-btn {
  @apply px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200;
  @apply bg-primary-600 text-white hover:bg-primary-700 shadow-sm hover:shadow-md;
}

.loading-btn {
  @apply flex items-center px-3 py-1.5 text-sm rounded-md;
  @apply bg-secondary-100 text-secondary-500;
}

/* 已登录状态样式 */
.user-menu {
  @apply relative;
}

.user-avatar {
  @apply flex items-center space-x-2 cursor-pointer hover:bg-gray-100 rounded-lg px-2 py-1.5 transition-colors duration-200;
}

.avatar-image {
  @apply w-8 h-8 rounded-full object-cover;
}

.avatar-placeholder {
  @apply w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-medium;
}

.dropdown-arrow {
  @apply text-gray-400 transition-transform duration-200;
}

.user-avatar:hover .dropdown-arrow {
  @apply text-gray-600;
}

/* 下拉菜单样式 */
.dropdown-menu {
  @apply absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50;
  transform-origin: top right;
  animation: dropdown-enter 0.15s ease-out;
}

.dropdown-overlay {
  @apply fixed inset-0 z-40;
}

.user-info {
  @apply px-4 py-3;
}

.user-name {
  @apply text-sm font-medium text-gray-900;
}

.user-email {
  @apply text-xs text-gray-500 mt-0.5;
}

.menu-divider {
  @apply border-t border-gray-100 my-1;
}

.menu-item {
  @apply w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200;
  @apply no-underline;
}

.menu-item:hover {
  @apply text-gray-900;
}

.logout-item {
  @apply text-red-600 hover:bg-red-50 hover:text-red-700;
}

/* 动画效果 */
@keyframes dropdown-enter {
  from {
    opacity: 0;
    transform: translateY(-4px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .dropdown-menu {
    @apply w-56;
  }
  
  .login-actions {
    @apply space-x-1;
  }
  
  .login-register-btn {
    @apply px-2 py-1 text-xs;
  }
}
</style> 