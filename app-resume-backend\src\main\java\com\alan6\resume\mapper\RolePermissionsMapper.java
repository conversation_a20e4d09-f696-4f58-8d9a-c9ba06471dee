package com.alan6.resume.mapper;

import com.alan6.resume.entity.RolePermissions;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色权限关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Mapper
public interface RolePermissionsMapper extends BaseMapper<RolePermissions> {

    /**
     * 根据角色ID删除权限关联
     *
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据权限ID删除角色关联
     *
     * @param permissionId 权限ID
     * @return 影响行数
     */
    int deleteByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 批量插入角色权限关联
     *
     * @param rolePermissions 角色权限关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<RolePermissions> rolePermissions);

    /**
     * 根据角色ID获取权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> selectPermissionIdsByRoleId(@Param("roleId") Long roleId);
} 