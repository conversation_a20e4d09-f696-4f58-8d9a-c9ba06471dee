import{r as l,i as B,c,a as e,p as g,q as f,v as y,t as u,F as S,g as w,o as d}from"./CURHyiUL.js";import{u as x}from"./BpemGWS8.js";import{_ as U}from"./DlAUqK2U.js";const V={class:"template-test-page"},D={class:"test-section"},I={class:"test-form"},L=["disabled"],F={key:0,class:"result"},M={class:"test-section"},N={class:"test-form"},R=["disabled"],q={key:0,class:"result"},E={class:"test-section"},j={class:"auth-info"},z={class:"test-section"},A={class:"log-area"},G={__name:"admin-template-test",setup(H){const k=x(),r=l("test-template-001"),v=l("resume-template"),m=l(!1),p=l(!1),o=l(""),n=l(""),_=l(""),h=l([]),a=s=>{h.value.push(`${new Date().toLocaleTimeString()}: ${s}`)},$=async()=>{m.value=!0,o.value="",a(`开始检查模板代码: ${r.value}`);try{const s=await k.checkTemplateCode(r.value);o.value=s?"代码可用":"代码已存在",a(`代码检查结果: ${o.value}`)}catch(s){o.value=`错误: ${s.message}`,a(`代码检查失败: ${s.message}`)}finally{m.value=!1}},C=async()=>{p.value=!0,n.value="",a(`开始检查桶状态: ${v.value}`);try{const s=await k.checkBucketStatus(v.value);n.value=s?"桶可用":"桶不可用",a(`桶检查结果: ${n.value}`)}catch(s){n.value=`错误: ${s.message}`,a(`桶检查失败: ${s.message}`)}finally{p.value=!1}},T=()=>{localStorage.setItem("auth_token","test-admin-token-123"),_.value="test-admin-token-123",a("已设置测试令牌")};return B(()=>{_.value=localStorage.getItem("auth_token"),a("页面加载完成")}),(s,t)=>(d(),c("div",V,[t[6]||(t[6]=e("h1",null,"模板上传服务测试",-1)),e("div",D,[t[2]||(t[2]=e("h2",null,"1. 测试模板代码检查",-1)),e("div",I,[g(e("input",{"onUpdate:modelValue":t[0]||(t[0]=i=>r.value=i),placeholder:"输入模板代码"},null,512),[[y,r.value]]),e("button",{onClick:$,disabled:m.value},u(m.value?"检查中...":"检查代码"),9,L),o.value?(d(),c("div",F," 结果: "+u(o.value),1)):f("",!0)])]),e("div",M,[t[3]||(t[3]=e("h2",null,"2. 测试桶状态检查",-1)),e("div",N,[g(e("input",{"onUpdate:modelValue":t[1]||(t[1]=i=>v.value=i),placeholder:"输入桶名称"},null,512),[[y,v.value]]),e("button",{onClick:C,disabled:p.value},u(p.value?"检查中...":"检查桶"),9,R),n.value?(d(),c("div",q," 结果: "+u(n.value),1)):f("",!0)])]),e("div",E,[t[4]||(t[4]=e("h2",null,"3. 认证状态",-1)),e("div",j,[e("p",null,"认证令牌: "+u(_.value?"已设置":"未设置"),1),e("button",{onClick:T},"设置测试令牌")])]),e("div",z,[t[5]||(t[5]=e("h2",null,"4. 测试日志",-1)),e("div",A,[(d(!0),c(S,null,w(h.value,(i,b)=>(d(),c("div",{key:b,class:"log-item"},u(i),1))),128))])])]))}},P=U(G,[["__scopeId","data-v-a8d9304a"]]);export{P as default};
