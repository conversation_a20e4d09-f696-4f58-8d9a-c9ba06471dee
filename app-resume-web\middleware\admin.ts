/**
 * 管理员权限验证中间件
 * 确保只有管理员可以访问后台管理页面
 */
export default defineNuxtRouteMiddleware(async (to, from) => {
  // 获取认证状态
  const authService = useAuthService()
  
  try {
    // 检查用户是否已登录
    if (!authService.isLoggedIn.value) {
      // 尝试初始化认证状态
      await authService.initAuth()
      
      if (!authService.isLoggedIn.value) {
        throw createError({
          statusCode: 401,
          statusMessage: '请先登录'
        })
      }
    }

    // 检查管理员权限
    const token = authService.getToken()
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: '请先登录'
      })
    }

    const response = await $fetch('/admin/check-auth', {
      method: 'GET',
      baseURL: 'http://localhost:9311',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (!response || response.code !== 200) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，需要管理员权限'
      })
    }

    // 将管理员信息存储到状态中
    const adminStore = useAdminStore()
    adminStore.setAdminInfo(response.data)

  } catch (error: any) {
    // 权限验证失败，重定向到登录页面
    console.error('管理员权限验证失败:', error)
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，需要管理员权限'
      })
    }
    
    // 清除认证信息并重定向到登录页面
    await authService.logout()
    return navigateTo('/')
  }
}) 