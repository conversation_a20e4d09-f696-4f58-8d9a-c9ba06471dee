import{af as n,ag as i,ah as o,n as u}from"./CURHyiUL.js";const g=n(async(d,c)=>{let t,e;const s=useAuthService();try{if(!s.isLoggedIn.value&&([t,e]=i(()=>s.initAuth()),await t,e(),!s.isLoggedIn.value))throw o({statusCode:401,statusMessage:"请先登录"});const a=s.getToken();if(!a)throw o({statusCode:401,statusMessage:"请先登录"});const r=([t,e]=i(()=>$fetch("/admin/check-auth",{method:"GET",baseURL:"http://localhost:9311",headers:{Authorization:`Bearer ${a}`}})),t=await t,e(),t);if(!r||r.code!==200)throw o({statusCode:403,statusMessage:"权限不足，需要管理员权限"});useAdminStore().setAdminInfo(r.data)}catch(a){if(console.error("管理员权限验证失败:",a),a.statusCode===403)throw o({statusCode:403,statusMessage:"权限不足，需要管理员权限"});return[t,e]=i(()=>s.logout()),await t,e(),u("/")}});export{g as default};
