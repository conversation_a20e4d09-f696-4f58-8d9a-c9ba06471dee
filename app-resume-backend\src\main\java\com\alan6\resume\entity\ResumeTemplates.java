package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 简历模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("resume_templates")
@Schema(name = "ResumeTemplates对象", description = "简历模板表")
public class ResumeTemplates implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模板ID")
    private Long id;

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "模板代码（对应文件夹名或组件名）")
    private String templateCode;

    @Schema(description = "模板描述")
    private String description;

    @Schema(description = "预览图URL")
    private String previewImageUrl;

    @Schema(description = "关联的模板文件ID（file_upload_records表）")
    private Long templateFileId;

    @Schema(description = "关联的内容ID（resume_template_content表）")
    private Long contentId;

    @Schema(description = "本地文件路径")
    private String localFilePath;

    @Schema(description = "Vue模板文件路径")
    private String vueFilePath;

    @Schema(description = "模板配置数据（JSON格式）")
    private String configData;

    @Schema(description = "支持的功能特性列表（JSON格式）")
    private String features;

    @Schema(description = "模板标签（JSON格式）")
    private String tags;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "适用行业")
    private String industry;

    @Schema(description = "模板风格")
    private String style;

    @Schema(description = "配色方案")
    private String colorScheme;

    @Schema(description = "是否付费模板（0:免费,1:付费）")
    private Byte isPremium;

    @Schema(description = "模板价格")
    private BigDecimal price;

    @Schema(description = "使用次数")
    private Integer useCount;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "状态（0:下架,1:上架）")
    private Byte status;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
