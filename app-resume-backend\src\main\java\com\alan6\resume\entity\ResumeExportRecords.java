package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 简历导出记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("resume_export_records")
@Schema(name = "ResumeExportRecords对象", description = "简历导出记录表")
public class ResumeExportRecords implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "导出记录ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "简历ID")
    private Long resumeId;

    @Schema(description = "导出格式（pdf,word,image）")
    private String exportFormat;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "导出平台")
    private String exportPlatform;

    @Schema(description = "下载链接")
    private String downloadUrl;

    @Schema(description = "关联的导出任务ID")
    private String jobId;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
