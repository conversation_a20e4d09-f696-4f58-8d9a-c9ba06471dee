import{r as P,c as r,o,f as B,a as e,F as z,g as j,s as $,t as h,h as f,k as _,i as A,q as w,d as C,p as F,v as q,R as I,m as N,A as U,z as V,e as L,b as T,N as R}from"./CURHyiUL.js";import{u as E}from"./DCz3uDMC.js";import{_ as H}from"./DlAUqK2U.js";const K={class:"bg-gradient-to-br from-primary-500 via-primary-600 to-orange-600 text-white relative overflow-visible"},G={class:"absolute left-0 right-0 -bottom-20 z-30"},O={class:"max-w-screen-2xl mx-auto px-6 lg:px-8"},J={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 max-w-4xl mx-auto"},Q=["onClick"],W={class:"font-bold text-lg mb-2"},X={class:"text-sm opacity-90"},Y={__name:"HeroBanner",emits:["nav-click"],setup(a,{emit:g}){const n=g,p=P([{id:"import",title:"导入简历",description:"上传已有简历快速编辑",bgClass:"bg-gradient-to-br from-pink-400 to-pink-500 text-white"},{id:"optimize",title:"简历优化",description:"AI智能优化简历内容",bgClass:"bg-gradient-to-br from-cyan-400 to-cyan-500 text-white"},{id:"ai-resume",title:"AI一键简历",description:"智能生成专业简历",bgClass:"bg-gradient-to-br from-purple-400 to-purple-500 text-white"},{id:"score",title:"简历打分",description:"专业简历评分建议",bgClass:"bg-gradient-to-br from-teal-400 to-teal-500 text-white"},{id:"track",title:"简历追踪",description:"简历投递状态跟踪",bgClass:"bg-gradient-to-br from-yellow-400 to-orange-400 text-white"}]),d=u=>{n("nav-click",u)};return(u,m)=>(o(),r("section",K,[m[1]||(m[1]=B('<div class="absolute inset-0 opacity-10"><div class="absolute top-10 left-10 w-32 h-32 border-2 border-white rounded-full"></div><div class="absolute top-32 right-16 w-20 h-20 bg-white/20 rounded-lg rotate-45"></div><div class="absolute bottom-20 left-20 w-16 h-16 bg-white/15 rounded-full"></div><div class="absolute bottom-32 right-32 w-24 h-24 border border-white rounded-lg rotate-12"></div><div class="absolute top-20 right-1/4 w-12 h-12 bg-gradient-to-br from-yellow-400/30 to-orange-500/30 rounded-2xl rotate-6"></div><div class="absolute bottom-40 left-1/3 w-8 h-8 bg-white/20 rounded-full"></div></div><div class="max-w-screen-2xl mx-auto px-6 lg:px-8 py-6 lg:py-8 pb-12 lg:pb-16 relative z-10"><div class="text-center max-w-4xl mx-auto"><h1 class="font-display font-bold text-4xl lg:text-5xl mb-6"> 好简历，从一个好模板开始 </h1><div class="flex flex-wrap justify-center items-center gap-8 text-lg mb-12"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-white rounded-full"></div><span>海量精美模板</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-white rounded-full"></div><span>5分钟极速制作</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-white rounded-full"></div><span>累计创建简历40万份</span></div></div></div></div>',2)),e("div",G,[e("div",O,[e("div",J,[(o(!0),r(z,null,j(f(p),(l,b)=>(o(),r("div",{key:l.id,onClick:x=>d(l),class:"group cursor-pointer"},[e("div",{class:$([l.bgClass,"relative p-6 rounded-2xl transition-all duration-300 transform group-hover:scale-105 group-hover:shadow-2xl min-h-[120px] flex flex-col justify-between shadow-lg"])},[m[0]||(m[0]=e("div",{class:"absolute top-4 right-4 opacity-20"},[e("div",{class:"w-8 h-8 bg-current rounded-lg"})],-1)),e("div",null,[e("h3",W,h(l.title),1),e("p",X,h(l.description),1)])],2)],8,Q))),128))])])])]))}},Z={class:"bg-white border-b border-secondary-200 relative z-20"},D={class:"max-w-screen-2xl mx-auto px-6 lg:px-8 py-6 pt-36"},ee={class:"flex items-center justify-between mb-6"},te={class:"flex flex-wrap gap-3"},se=["onClick"],ae={class:"relative max-w-md"},ne={key:0,class:"flex flex-wrap gap-2"},oe=["onClick"],re={__name:"SearchFilter",emits:["main-category-change","sub-category-change","search"],setup(a,{emit:g}){const n=g,p=P(""),d=P(null),u=P(null),m=P([{id:"hot",name:"热门模板"},{id:"style",name:"设计风格"},{id:"industry",name:"行业职位"},{id:"major",name:"高校专业"},{id:"intern",name:"实习生"}]),l={hot:[{id:"all",name:"所有"},{id:"school-recruit",name:"校招简历模板"},{id:"social-recruit",name:"社招简历模板"},{id:"student-business",name:"大学生商务模板"},{id:"fresh-graduate",name:"应届生简历模板"},{id:"practical-business",name:"实习生商务模板"},{id:"zero-experience",name:"零经验找实习模板"},{id:"top-university",name:"三本大学生简历模板"},{id:"general-interview",name:"通用机门简历模板"},{id:"liberal-arts",name:"土木转行模板"}],style:[{id:"all",name:"所有"},{id:"classic",name:"经典传统"},{id:"business",name:"简约商务"},{id:"creative",name:"创意设计"},{id:"tech",name:"技术极客"},{id:"modern",name:"现代时尚"},{id:"fresh",name:"清新文艺"}],industry:[{id:"all",name:"所有"},{id:"advertising",name:"广告/传媒"},{id:"finance",name:"财务/法律"},{id:"service",name:"服务业/贸易"},{id:"construction",name:"房产建筑"},{id:"sales",name:"销售/客服"},{id:"education",name:"教育/医疗"},{id:"tech-rd",name:"技术/研发"},{id:"product",name:"产品/设计"},{id:"finance-auto",name:"金融/汽车"},{id:"marketing",name:"市场/运营"},{id:"hr",name:"人事/行政"}],major:[{id:"all",name:"所有"},{id:"software",name:"软件工程"},{id:"computer",name:"计算机科学"},{id:"biology",name:"生化环科类"},{id:"journalism",name:"新闻传媒类"},{id:"finance-major",name:"金融会计类"},{id:"economics",name:"经管类"},{id:"normal",name:"师范类"},{id:"language",name:"语言类"},{id:"engineering",name:"工学制造类"},{id:"other",name:"其他"}],intern:[{id:"all",name:"所有"},{id:"fresh-intern",name:"应届生简历模板"},{id:"intern-business",name:"实习生商务模板"},{id:"zero-exp-intern",name:"零经验找实习模板"}]},b=_(()=>d.value?l[d.value.id]||[]:[]),x=s=>{var i;d.value=s;const t=(i=l[s.id])==null?void 0:i[0];t?(u.value=t,n("sub-category-change",{mainCategory:s,subCategory:t})):(u.value=null,n("main-category-change",s))},y=s=>{u.value=s,n("sub-category-change",{mainCategory:d.value,subCategory:s})},v=()=>{n("search",p.value)};return A(()=>{var s;if(m.value.length>0){d.value=m.value[0];const t=m.value[0],i=(s=l[t.id])==null?void 0:s[0];i&&(u.value=i)}}),(s,t)=>{var i;return o(),r("div",Z,[e("div",D,[e("div",ee,[e("div",te,[(o(!0),r(z,null,j(f(m),c=>{var k;return o(),r("button",{key:c.id,onClick:M=>x(c),class:$(["inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200",((k=f(d))==null?void 0:k.id)===c.id?"bg-primary-600 text-white":"bg-secondary-100 text-secondary-700 hover:bg-secondary-200"])},[t[1]||(t[1]=e("div",{class:"w-4 h-4 mr-2 bg-current rounded-full opacity-60"},null,-1)),C(" "+h(c.name),1)],10,se)}),128))]),e("div",ae,[F(e("input",{"onUpdate:modelValue":t[0]||(t[0]=c=>I(p)?p.value=c:null),onInput:v,type:"text",placeholder:"输入简历关键词",class:"w-full px-4 py-2 pl-4 pr-12 rounded-xl border border-secondary-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,544),[[q,f(p)]]),t[2]||(t[2]=e("button",{class:"absolute right-4 top-1/2 transform -translate-y-1/2"},[e("svg",{class:"w-5 h-5 text-secondary-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1))])]),((i=b.value)==null?void 0:i.length)>0?(o(),r("div",ne,[(o(!0),r(z,null,j(b.value,c=>{var k;return o(),r("button",{key:c.id,onClick:M=>y(c),class:$(["px-3 py-1.5 rounded-full text-sm transition-all duration-200",((k=f(u))==null?void 0:k.id)===c.id?"bg-primary-100 text-primary-700 border border-primary-200":"bg-secondary-50 text-secondary-600 hover:bg-secondary-100 border border-transparent"])},h(c.name),11,oe)}),128))])):w("",!0)])])}}},ie={class:"group cursor-pointer"},le={class:"relative aspect-[3/4] bg-white rounded-2xl shadow-card border border-secondary-100 overflow-hidden mb-4 transition-all duration-300 group-hover:shadow-soft group-hover:-translate-y-1"},de={class:"absolute inset-0 bg-gradient-to-br from-secondary-50 to-secondary-100"},ce=["src","alt"],ue={key:1,class:"p-6 h-full flex flex-col"},me={class:"absolute bottom-4 left-4"},ge={class:"inline-flex items-center px-2 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-danger-600"},pe={class:"absolute inset-0 bg-primary-600/90 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center"},ve={class:"flex space-x-3"},he={class:"font-semibold text-lg text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200"},xe={class:"flex items-center justify-between"},be={class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"},ye={class:"flex items-center text-sm text-secondary-500"},fe={key:0,class:"flex items-center space-x-4 mt-3 text-xs text-secondary-600"},we={class:"flex space-x-2"},ke={__name:"TemplateCard",props:{template:{type:Object,required:!0,validator:a=>a&&typeof a=="object"&&a.id&&a.name}},emits:["preview","use-template"],setup(a,{emit:g}){const n=a,p=g,d=()=>{p("preview",n.template)},u=()=>{p("use-template",n.template)};return(m,l)=>{var b;return o(),r("div",ie,[e("div",le,[e("div",de,[a.template.thumbnail?(o(),r("img",{key:0,src:a.template.thumbnail,alt:a.template.name,class:"w-full h-full object-cover",loading:"lazy"},null,8,ce)):(o(),r("div",ue,l[0]||(l[0]=[B('<div class="space-y-3"><div class="flex items-center space-x-3"><div class="w-12 h-12 bg-primary-200 rounded-full flex items-center justify-center"><svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg></div><div class="flex-1"><div class="h-3 bg-secondary-300 rounded w-20 mb-1"></div><div class="h-2 bg-secondary-200 rounded w-16"></div></div></div><div class="space-y-2"><div class="h-2 bg-secondary-300 rounded w-full"></div><div class="h-2 bg-secondary-200 rounded w-4/5"></div><div class="h-2 bg-secondary-200 rounded w-3/4"></div><div class="h-2 bg-secondary-200 rounded w-5/6"></div></div><div class="grid grid-cols-2 gap-3 mt-4"><div><div class="h-2 bg-primary-200 rounded w-full mb-1"></div><div class="h-1.5 bg-secondary-200 rounded w-3/4"></div></div><div><div class="h-2 bg-success-200 rounded w-full mb-1"></div><div class="h-1.5 bg-secondary-200 rounded w-2/3"></div></div></div></div>',1)])))]),e("div",me,[e("span",ge,[l[1]||(l[1]=e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),C(" "+h(a.template.usageCount||0)+"人使用 ",1)])]),e("div",pe,[e("div",ve,[e("button",{onClick:N(u,["stop"]),class:"px-4 py-2 bg-white text-primary-600 rounded-lg hover:bg-secondary-50 transition-colors duration-200 text-sm font-medium"}," 使用模板 "),e("button",{onClick:N(d,["stop"]),class:"px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors duration-200 text-sm font-medium"}," 预览 ")])])]),e("div",null,[e("h3",he,h(a.template.name),1),e("div",xe,[e("span",be,h(a.template.category),1),e("div",ye,[l[2]||(l[2]=e("svg",{class:"w-4 h-4 mr-1 text-warning-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1)),C(" "+h(a.template.rating||0),1)])]),((b=a.template.features)==null?void 0:b.length)>0?(o(),r("div",fe,[l[3]||(l[3]=e("span",{class:"font-medium"},"支持功能：",-1)),e("div",we,[(o(!0),r(z,null,j(a.template.features.slice(0,3),x=>(o(),r("span",{key:x,class:"text-primary-600 hover:text-primary-700 cursor-pointer"},h(x),1))),128))])])):w("",!0)])])}}},Ce={class:"flex flex-col sm:flex-row justify-between items-center py-6 border-t border-secondary-100"},_e={class:"text-sm text-secondary-600 mb-4 sm:mb-0"},$e={class:"font-medium text-secondary-900"},Pe={class:"font-medium text-secondary-900"},ze={class:"font-medium text-secondary-900"},je={class:"flex items-center space-x-2"},Me=["disabled"],Se={class:"flex items-center space-x-1"},Te={key:1,class:"px-3 py-2 text-secondary-400"},Be=["onClick"],Ne={key:2,class:"px-3 py-2 text-secondary-400"},Ve=["disabled"],Ae={__name:"TemplatePagination",props:{currentPage:{type:Number,required:!0},totalPages:{type:Number,required:!0},totalCount:{type:Number,required:!0},maxVisiblePages:{type:Number,default:5}},emits:["page-change"],setup(a,{emit:g}){const n=a,p=g,d=_(()=>{const{currentPage:v,totalPages:s,maxVisiblePages:t}=n;if(s<=t)return Array.from({length:s},(M,S)=>S+1);const i=Math.floor(t/2);let c=Math.max(v-i,1),k=Math.min(c+t-1,s);return k-c+1<t&&(c=Math.max(k-t+1,1)),Array.from({length:k-c+1},(M,S)=>c+S)}),u=_(()=>!d.value.includes(1)),m=_(()=>!d.value.includes(n.totalPages)),l=_(()=>u.value&&d.value[0]>2),b=_(()=>m.value&&d.value[d.value.length-1]<n.totalPages-1),x=v=>["inline-flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg transition-all duration-200",v===n.currentPage?"bg-primary-600 text-white shadow-lg":"text-secondary-700 hover:text-primary-600 hover:bg-primary-50"],y=v=>{v>=1&&v<=n.totalPages&&v!==n.currentPage&&p("page-change",v)};return(v,s)=>(o(),r("div",Ce,[e("div",_e,[s[4]||(s[4]=C(" 共 ")),e("span",$e,h(a.totalCount),1),s[5]||(s[5]=C(" 个模板， 当前第 ")),e("span",Pe,h(a.currentPage),1),s[6]||(s[6]=C(" 页， 共 ")),e("span",ze,h(a.totalPages),1),s[7]||(s[7]=C(" 页 "))]),e("div",je,[e("button",{onClick:s[0]||(s[0]=t=>y(a.currentPage-1)),disabled:a.currentPage<=1,class:$(["inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200",a.currentPage<=1?"text-secondary-400 cursor-not-allowed":"text-secondary-700 hover:text-primary-600 hover:bg-primary-50"])},s[8]||(s[8]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1),C(" 上一页 ")]),10,Me),e("div",Se,[u.value?(o(),r("button",{key:0,onClick:s[1]||(s[1]=t=>y(1)),class:$(x(1))}," 1 ",2)):w("",!0),l.value?(o(),r("span",Te,"...")):w("",!0),(o(!0),r(z,null,j(d.value,t=>(o(),r("button",{key:t,onClick:i=>y(t),class:$(x(t))},h(t),11,Be))),128)),b.value?(o(),r("span",Ne,"...")):w("",!0),m.value?(o(),r("button",{key:3,onClick:s[2]||(s[2]=t=>y(a.totalPages)),class:$(x(a.totalPages))},h(a.totalPages),3)):w("",!0)]),e("button",{onClick:s[3]||(s[3]=t=>y(a.currentPage+1)),disabled:a.currentPage>=a.totalPages,class:$(["inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200",a.currentPage>=a.totalPages?"text-secondary-400 cursor-not-allowed":"text-secondary-700 hover:text-primary-600 hover:bg-primary-50"])},s[9]||(s[9]=[C(" 下一页 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),10,Ve)])]))}},Fe={class:"py-8"},qe={class:"max-w-screen-2xl mx-auto px-6 lg:px-8"},Ie={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8"},Ue={key:0,class:"text-center py-16"},Le={__name:"TemplateGrid",props:{templates:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},pageSize:{type:Number,default:20}},emits:["preview","use-template","page-change"],setup(a,{emit:g}){const n=a,p=g,d=P(1),u=_(()=>n.templates.length),m=_(()=>Math.ceil(u.value/n.pageSize)),l=_(()=>{const s=(d.value-1)*n.pageSize,t=s+n.pageSize;return n.templates.slice(s,t)}),b=s=>{p("preview",s)},x=s=>{p("use-template",s)},y=s=>{d.value=s,p("page-change",s),v()},v=()=>{window.scrollTo({top:0,behavior:"smooth"})};return U(()=>n.templates,()=>{d.value=1},{deep:!0}),(s,t)=>(o(),r("div",Fe,[e("div",qe,[e("div",Ie,[(o(!0),r(z,null,j(l.value,i=>(o(),V(ke,{key:i.id,template:i,onPreview:b,onUseTemplate:x},null,8,["template"]))),128))]),l.value.length===0?(o(),r("div",Ue,t[0]||(t[0]=[B('<div class="w-24 h-24 mx-auto mb-6 bg-secondary-100 rounded-full flex items-center justify-center"><svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><h3 class="text-xl font-semibold text-secondary-900 mb-2">暂无相关模板</h3><p class="text-secondary-600">请尝试其他搜索条件或联系客服获取更多模板</p>',3)]))):w("",!0),m.value>1?(o(),V(Ae,{key:1,"current-page":f(d),"total-pages":m.value,"total-count":u.value,onPageChange:y},null,8,["current-page","total-pages","total-count"])):w("",!0)])]))}},Re={class:"pt-16 min-h-screen bg-gray-50"},Ee={key:0,class:"flex justify-center items-center py-20"},He={key:1,class:"flex justify-center items-center py-20"},Ke={class:"text-center"},Ge={class:"text-secondary-600 mb-4"},Oe={__name:"index",setup(a){L({title:"简历模板库 - 1000+ 专业简历模板 - 火花简历",description:"提供1000+专业简历模板，涵盖各行各业。商务、创意、技术、文艺等多种风格，5分钟快速制作专业简历。",keywords:"简历模板,免费简历模板,简历设计,职场简历,求职简历模板,火花简历,在线制作简历"});const g=R(),n=E(),p=P(20);A(async()=>{await d()});const d=async()=>{try{await n.fetchTemplates()}catch(t){console.error("初始化页面失败:",t)}},u=t=>{switch(console.log("导航点击:",t),t.id){case"import":g.push("/resume/import");break;case"optimize":g.push("/resume/optimize");break;case"ai-resume":g.push("/resume/ai-create");break;case"score":g.push("/resume/score");break;case"track":g.push("/resume/track");break;default:console.log("未知导航:",t)}},m=t=>{t&&t.id&&n.setCategoryFilter(t.id)},l=t=>{t&&t.subCategory&&(t.subCategory.industry?n.setIndustryFilter(t.subCategory.industry):t.subCategory.style&&n.setStyleFilter(t.subCategory.style))},b=t=>{n.setKeywordFilter(t)},x=t=>{console.log("预览模板:",t)},y=async t=>{try{const i={resumeName:`基于${t.name}的简历`,useDefaultContent:!0},c=await n.useTemplate(t.id,i);if(c.success)g.push(`/resume/edit/${c.data.resumeId}`);else throw new Error(c.error||"使用模板失败")}catch(i){console.error("使用模板失败:",i),i.message.includes("登录")&&g.push("/login")}},v=t=>{console.log("分页变更:",t)},s=async()=>{await n.fetchTemplates()};return(t,i)=>(o(),r("div",Re,[T(Y,{onNavClick:u}),T(re,{onMainCategoryChange:m,onSubCategoryChange:l,onSearch:b}),T(Le,{templates:f(n).templates.value,loading:f(n).loading.value,"page-size":p.value,onPreview:x,onUseTemplate:y,onPageChange:v},null,8,["templates","loading","page-size"]),f(n).loading.value?(o(),r("div",Ee,i[0]||(i[0]=[e("div",{class:"flex flex-col items-center"},[e("div",{class:"w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mb-4"}),e("p",{class:"text-secondary-600"},"正在加载模板...")],-1)]))):w("",!0),f(n).error.value?(o(),r("div",He,[e("div",Ke,[i[1]||(i[1]=e("div",{class:"w-16 h-16 bg-danger-100 rounded-full flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-danger-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),i[2]||(i[2]=e("h3",{class:"text-lg font-semibold text-secondary-900 mb-2"},"加载失败",-1)),e("p",Ge,h(f(n).error.value),1),e("button",{onClick:s,class:"btn-primary"}," 重试 ")])])):w("",!0)]))}},Xe=H(Oe,[["__scopeId","data-v-19ff3f10"]]);export{Xe as default};
