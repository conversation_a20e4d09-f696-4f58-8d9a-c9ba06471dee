package com.alan6.resume.service.impl;

import com.alan6.resume.entity.ResumeTemplateCategories;
import com.alan6.resume.mapper.ResumeTemplateCategoriesMapper;
import com.alan6.resume.service.IResumeTemplateCategoriesService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 模板分类关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeTemplateCategoriesServiceImpl extends ServiceImpl<ResumeTemplateCategoriesMapper, ResumeTemplateCategories> implements IResumeTemplateCategoriesService {

    @Override
    public List<Long> getCategoryIdsByTemplateId(Long templateId) {
        log.info("获取模板 {} 的分类ID列表", templateId);
        
        LambdaQueryWrapper<ResumeTemplateCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResumeTemplateCategories::getTemplateId, templateId)
               .eq(ResumeTemplateCategories::getIsDeleted, 0);
        
        List<ResumeTemplateCategories> relations = list(wrapper);
        return relations.stream()
                .map(ResumeTemplateCategories::getCategoryId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getTemplateIdsByCategoryIds(List<Long> categoryIds) {
        log.info("根据分类ID列表获取模板ID列表: {}", categoryIds);
        
        if (categoryIds == null || categoryIds.isEmpty()) {
            return List.of();
        }
        
        LambdaQueryWrapper<ResumeTemplateCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ResumeTemplateCategories::getCategoryId, categoryIds)
               .eq(ResumeTemplateCategories::getIsDeleted, 0);
        
        List<ResumeTemplateCategories> relations = list(wrapper);
        return relations.stream()
                .map(ResumeTemplateCategories::getTemplateId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean updateTemplateCategories(Long templateId, List<Long> categoryIds) {
        log.info("更新模板 {} 的分类关联，新分类: {}", templateId, categoryIds);
        
        // 删除现有关联
        deleteByTemplateId(templateId);
        
        // 创建新关联
        if (categoryIds != null && !categoryIds.isEmpty()) {
            return batchCreateRelations(templateId, categoryIds);
        }
        
        return true;
    }

    @Override
    public boolean deleteByTemplateId(Long templateId) {
        log.info("删除模板 {} 的所有分类关联", templateId);
        
        LambdaQueryWrapper<ResumeTemplateCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResumeTemplateCategories::getTemplateId, templateId);
        
        return remove(wrapper);
    }

    @Override
    public boolean batchCreateRelations(Long templateId, List<Long> categoryIds) {
        log.info("批量创建模板 {} 的分类关联: {}", templateId, categoryIds);
        
        if (categoryIds == null || categoryIds.isEmpty()) {
            return true;
        }
        
        List<ResumeTemplateCategories> relations = categoryIds.stream()
                .distinct() // 去重
                .map(categoryId -> {
                    ResumeTemplateCategories relation = new ResumeTemplateCategories();
                    relation.setTemplateId(templateId);
                    relation.setCategoryId(categoryId);
                    return relation;
                })
                .collect(Collectors.toList());
        
        return saveBatch(relations);
    }
} 