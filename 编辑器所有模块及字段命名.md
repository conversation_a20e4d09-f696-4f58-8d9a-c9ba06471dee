# 简历编辑器所有模块及字段命名清单

## 📋 概述

本文档记录了简历编辑器中所有模块的字段名称和结构，用于前端Vue组件开发和HTML模板规范制定。

**⚠️ 重要说明：** 本文档所有模块ID均以实际代码为准，大部分使用单数形式（如 `education`、`work_experience`），只有 `skills` 和 `hobbies` 使用复数形式。

## 🏷️ 基本信息模块 (`basic_info`)

### 基础信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `photo` | String | 照片URL |
| `name` | String | 姓名 |
| `age` | Number/String | 年龄 |
| `phone` | String | 手机号 |
| `email` | String | 邮箱地址 |
| `currentCity` | String | 所在城市 |
| `intendedCity` | String | 意向城市 |
| `address` | String | 居住地址 |
| `currentStatus` | String | 当前状态 |
| `expectedPosition` | String | 期望岗位 |
| `expectedSalary` | String | 期望薪资 |

### 社交信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `wechat` | String | 微信号 |
| `website` | String | 个人网站 |
| `github` | String | GitHub地址 |
| `customSocials` | Array | 自定义社交信息数组 |

### 其他信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `gender` | String | 性别 |
| `height` | String | 身高 |
| `weight` | String | 体重 |
| `politicalStatus` | String | 政治面貌 |
| `maritalStatus` | String | 婚姻状况 |
| `hometown` | String | 籍贯 |
| `ethnicity` | String | 民族 |
| `customInfos` | Array | 自定义信息数组 |

---

## 💼 工作经历模块 (`work_experience`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `company` | String | 公司名称 |
| `position` | String | 职位 |
| `department` | String | 部门 |
| `startDate` | String | 开始时间 |
| `endDate` | String | 结束时间 |
| `description` | String | 工作描述 |

---

## 🎓 教育经历模块 (`education`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `school` | String | 学校名称 |
| `major` | String | 专业 |
| `startDate` | String | 开始时间 |
| `endDate` | String | 结束时间 |
| `degree` | String | 学历（选择项） |
| `customDegree` | String | 自定义学历 |
| `description` | String | 描述 |

---

## 🚀 项目经历模块 (`project`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `name` | String | 项目名称 |
| `role` | String | 担任角色 |
| `company` | String | 所在公司 |
| `startDate` | String | 开始时间 |
| `endDate` | String | 结束时间 |
| `description` | String | 项目描述 |

---

## 🔧 技能专长模块 (`skills`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `name` | String | 技能名称 |
| `level` | Number | 熟练程度百分比（0-100的数字） |
| `proficiency` | String | 熟练程度文本（精通、擅长、熟练、良好、一般） |
| `displayMode` | String | 该技能项的显示模式（percentage: 百分比, proficiency: 熟练程度） |

### 特性说明
- **个性化显示**：每个技能项都可以独立选择显示模式
- **数据同步**：百分比和熟练程度文本自动双向同步
- **默认设置**：新增技能默认使用百分比模式

### 熟练程度与百分比对应关系
| 熟练程度 | 百分比范围 | 默认值 | 颜色标识 |
|----------|------------|--------|----------|
| 精通 | 90-100% | 95% | 紫色 |
| 擅长 | 75-89% | 82% | 绿色 |
| 熟练 | 60-74% | 67% | 蓝色 |
| 良好 | 40-59% | 50% | 黄色 |
| 一般 | 20-39% | 30% | 灰色 |

---

## 🏆 获奖情况模块 (`award`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `name` | String | 奖项名称 |
| `date` | String | 获奖时间 |
| `organization` | String | 颁发机构 |
| `level` | String | 获奖等级 |
| `description` | String | 描述 |

---

## 📜 证书资质模块 (`certificate`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `name` | String | 证书名称 |
| `date` | String | 获得时间 |
| `organization` | String | 颁发机构 |
| `number` | String | 证书编号 |
| `description` | String | 描述 |

---

## 🌍 语言能力模块 (`language`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `name` | String | 语言名称 |
| `level` | String | 熟练程度 |
| `certificate` | String | 相关证书 |
| `description` | String | 描述 |

---

## 🎯 实习经历模块 (`internship`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `company` | String | 公司名称 |
| `position` | String | 实习职位 |
| `startDate` | String | 开始时间 |
| `endDate` | String | 结束时间 |
| `description` | String | 实习描述 |

---

## 🤝 志愿经历模块 (`volunteer_experience`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `organization` | String | 组织机构 |
| `role` | String | 担任角色 |
| `location` | String | 服务地点 |
| `startDate` | String | 开始时间 |
| `endDate` | String | 结束时间 |
| `description` | String | 描述 |

---

## 📚 论文发表模块 (`publication`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `title` | String | 论文标题 |
| `type` | String | 论文类型 |
| `journal` | String | 发表期刊 |
| `date` | String | 发表时间 |
| `description` | String | 描述 |

---

## 🔬 科研经历模块 (`research_experience`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `topic` | String | 研究课题 |
| `role` | String | 担任角色 |
| `organization` | String | 研究机构 |
| `startDate` | String | 开始时间 |
| `endDate` | String | 结束时间 |
| `description` | String | 描述 |

---

## 📈 培训经历模块 (`training`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `course` | String | 培训课程 |
| `company` | String | 培训机构 |
| `startDate` | String | 开始时间 |
| `endDate` | String | 结束时间 |
| `description` | String | 描述 |

---

## 🎨 作品集模块 (`portfolio`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `title` | String | 作品标题 |
| `type` | String | 作品类型 |
| `url` | String | 作品链接 |
| `date` | String | 创作时间 |
| `description` | String | 描述 |

---

## 🎭 兴趣爱好模块 (`hobbies`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `name` | String | 爱好名称 |
| `level` | String | 熟练程度 |
| `description` | String | 描述 |

---

## 💭 自我评价模块 (`self_evaluation`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `content` | String | 评价内容 |

---

## 📧 自荐信模块 (`cover_letter`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `content` | String | 信件内容 |

---

## ⚙️ 自定义模块 (`custom_*`)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `name` | String | 名称 |
| `role` | String | 角色 |
| `startDate` | String | 开始时间 |
| `endDate` | String | 结束时间 |
| `content` | String | 详细内容 |

---

## 📝 模块命名规则

### 模块ID命名规范
- **主要使用单数**：绝大多数模块使用单数名词（如 `education`、`work_experience`、`project`）
- **个别复数例外**：只有 `skills` 和 `hobbies` 使用复数形式
- **下划线分隔**：多词使用下划线连接（如 `work_experience`、`volunteer_experience`）
- **语义明确**：模块名称应清晰表达功能

### 字段命名规范
- **驼峰命名**：使用camelCase格式（如 `startDate`）
- **语义清晰**：字段名称应准确表达含义
- **类型一致**：相同含义字段在不同模块中保持一致命名

### 数据类型说明
- **String**：文本类型，包括短文本和长文本
- **Array**：数组类型，用于存储多项数据
- **Number**：数字类型，如年龄等
- **Boolean**：布尔类型，用于开关状态

---

## 🔄 更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.5 | 2025-01-16 | 重大更新：更新HTML模板规范文档，包含所有17个模块的完整字段定义，修正模块ID和字段名称，确保与当前系统实现完全一致 |
| 1.4 | 2025-01-16 | 优化技能专长模块：将显示模式从模块级调整为技能项级，每个技能可独立设置显示方式 |
| 1.3 | 2025-01-16 | 增强技能专长模块：支持百分比/熟练程度双显示模式，增加proficiency和displayMode字段 |
| 1.2 | 2025-01-16 | 补充技能专长模块缺失的level字段 |
| 1.1 | 2025-01-16 | 修正模块ID命名，以实际代码中的单数形式为准 |
| 1.0 | 2025-01-16 | 初始版本，包含所有现有模块字段定义 |

---

*本文档基于当前Vue组件实现生成，如有字段更新请及时同步本文档。* 