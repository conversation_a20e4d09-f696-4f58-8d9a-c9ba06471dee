package com.alan6.resume.service.impl;

import com.alan6.resume.service.ITemplateCacheService;
import com.alan6.resume.service.ITemplateFileService;
import com.alan6.resume.common.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 模板缓存服务实现类
 * @description 实现Redis中的模板缓存管理，提供高效的模板内容读写操作
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateCacheServiceImpl implements ITemplateCacheService {

    /**
     * Redis工具类
     * @description 用于操作Redis缓存
     */
    private final RedisUtils redisUtils;

    /**
     * 模板文件服务
     * @description 用于从MinIO或本地加载模板文件
     */
    private final ITemplateFileService templateFileService;

    /**
     * 缓存键前缀
     * @description Redis缓存键的前缀，用于区分不同类型的缓存
     */
    @Value("${app.template.cache.prefix:template:}")
    private String cacheKeyPrefix;

    /**
     * 默认缓存过期时间（小时）
     * @description 模板缓存的默认过期时间
     */
    @Value("${app.template.cache.expire-hours:24}")
    private int defaultExpireHours;

    /**
     * 从Redis缓存中获取模板内容
     * @param templateId 模板ID
     * @return 模板内容，如果不存在则返回null
     */
    @Override
    public String getTemplateFromCache(Long templateId) {
        if (templateId == null) {
            log.warn("模板ID为空，无法从缓存获取模板内容");
            return null;
        }

        try {
            String cacheKey = getCacheKey(templateId);
            String templateContent = (String) redisUtils.get(cacheKey);
            
            if (StringUtils.hasText(templateContent)) {
                log.debug("从Redis缓存成功获取模板内容，templateId: {}", templateId);
                return templateContent;
            } else {
                log.debug("Redis缓存中未找到模板内容，templateId: {}", templateId);
                return null;
            }
        } catch (Exception e) {
            log.error("从Redis缓存获取模板内容失败，templateId: {}", templateId, e);
            return null;
        }
    }

    /**
     * 从Redis缓存中获取模板内容（通过模板代码）
     * @param templateCode 模板代码
     * @return 模板内容，如果不存在则返回null
     */
    @Override
    public String getTemplateFromCache(String templateCode) {
        if (!StringUtils.hasText(templateCode)) {
            log.warn("模板代码为空，无法从缓存获取模板内容");
            return null;
        }

        try {
            String cacheKey = getCacheKey(templateCode);
            String templateContent = (String) redisUtils.get(cacheKey);
            
            if (StringUtils.hasText(templateContent)) {
                log.debug("从Redis缓存成功获取模板内容，templateCode: {}", templateCode);
                return templateContent;
            } else {
                log.debug("Redis缓存中未找到模板内容，templateCode: {}", templateCode);
                return null;
            }
        } catch (Exception e) {
            log.error("从Redis缓存获取模板内容失败，templateCode: {}", templateCode, e);
            return null;
        }
    }

    /**
     * 将模板内容存储到Redis缓存
     * @param templateId 模板ID
     * @param templateContent 模板内容
     * @param expireTime 过期时间
     * @return 是否存储成功
     */
    @Override
    public boolean setTemplateToCache(Long templateId, String templateContent, Duration expireTime) {
        if (templateId == null || !StringUtils.hasText(templateContent)) {
            log.warn("模板ID或内容为空，无法存储到缓存");
            return false;
        }

        try {
            String cacheKey = getCacheKey(templateId);
            
            // 如果未指定过期时间，使用默认值
            Duration actualExpireTime = expireTime != null ? expireTime : Duration.ofHours(defaultExpireHours);
            
            boolean result = redisUtils.set(cacheKey, templateContent, actualExpireTime.getSeconds());
            
            if (result) {
                log.debug("模板内容成功存储到Redis缓存，templateId: {}, expireTime: {}秒", 
                    templateId, actualExpireTime.getSeconds());
            } else {
                log.warn("模板内容存储到Redis缓存失败，templateId: {}", templateId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("存储模板内容到Redis缓存失败，templateId: {}", templateId, e);
            return false;
        }
    }

    /**
     * 将模板内容存储到Redis缓存（通过模板代码）
     * @param templateCode 模板代码
     * @param templateContent 模板内容
     * @param expireTime 过期时间
     * @return 是否存储成功
     */
    @Override
    public boolean setTemplateToCache(String templateCode, String templateContent, Duration expireTime) {
        if (!StringUtils.hasText(templateCode) || !StringUtils.hasText(templateContent)) {
            log.warn("模板代码或内容为空，无法存储到缓存");
            return false;
        }

        try {
            String cacheKey = getCacheKey(templateCode);
            
            // 如果未指定过期时间，使用默认值
            Duration actualExpireTime = expireTime != null ? expireTime : Duration.ofHours(defaultExpireHours);
            
            boolean result = redisUtils.set(cacheKey, templateContent, actualExpireTime.getSeconds());
            
            if (result) {
                log.debug("模板内容成功存储到Redis缓存，templateCode: {}, expireTime: {}秒", 
                    templateCode, actualExpireTime.getSeconds());
            } else {
                log.warn("模板内容存储到Redis缓存失败，templateCode: {}", templateCode);
            }
            
            return result;
        } catch (Exception e) {
            log.error("存储模板内容到Redis缓存失败，templateCode: {}", templateCode, e);
            return false;
        }
    }

    /**
     * 检查模板是否存在于缓存中
     * @param templateId 模板ID
     * @return 是否存在
     */
    @Override
    public boolean isTemplateExistsInCache(Long templateId) {
        if (templateId == null) {
            return false;
        }

        try {
            String cacheKey = getCacheKey(templateId);
            return redisUtils.exists(cacheKey);
        } catch (Exception e) {
            log.error("检查模板缓存存在性失败，templateId: {}", templateId, e);
            return false;
        }
    }

    /**
     * 检查模板是否存在于缓存中（通过模板代码）
     * @param templateCode 模板代码
     * @return 是否存在
     */
    @Override
    public boolean isTemplateExistsInCache(String templateCode) {
        if (!StringUtils.hasText(templateCode)) {
            return false;
        }

        try {
            String cacheKey = getCacheKey(templateCode);
            return redisUtils.exists(cacheKey);
        } catch (Exception e) {
            log.error("检查模板缓存存在性失败，templateCode: {}", templateCode, e);
            return false;
        }
    }

    /**
     * 删除模板缓存
     * @param templateId 模板ID
     * @return 是否删除成功
     */
    @Override
    public boolean deleteTemplateFromCache(Long templateId) {
        if (templateId == null) {
            return false;
        }

        try {
            String cacheKey = getCacheKey(templateId);
            boolean result = redisUtils.delete(cacheKey);
            
            if (result) {
                log.debug("成功删除模板缓存，templateId: {}", templateId);
            } else {
                log.warn("删除模板缓存失败，templateId: {}", templateId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("删除模板缓存失败，templateId: {}", templateId, e);
            return false;
        }
    }

    /**
     * 删除模板缓存（通过模板代码）
     * @param templateCode 模板代码
     * @return 是否删除成功
     */
    @Override
    public boolean deleteTemplateFromCache(String templateCode) {
        if (!StringUtils.hasText(templateCode)) {
            return false;
        }

        try {
            String cacheKey = getCacheKey(templateCode);
            boolean result = redisUtils.delete(cacheKey);
            
            if (result) {
                log.debug("成功删除模板缓存，templateCode: {}", templateCode);
            } else {
                log.warn("删除模板缓存失败，templateCode: {}", templateCode);
            }
            
            return result;
        } catch (Exception e) {
            log.error("删除模板缓存失败，templateCode: {}", templateCode, e);
            return false;
        }
    }

    /**
     * 获取模板缓存的过期时间
     * @param templateId 模板ID
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示不存在
     */
    @Override
    public long getTemplateExpireTime(Long templateId) {
        if (templateId == null) {
            return -2;
        }

        try {
            String cacheKey = getCacheKey(templateId);
            return redisUtils.getExpire(cacheKey);
        } catch (Exception e) {
            log.error("获取模板缓存过期时间失败，templateId: {}", templateId, e);
            return -2;
        }
    }

    /**
     * 刷新模板缓存过期时间
     * @param templateId 模板ID
     * @param expireTime 新的过期时间
     * @return 是否设置成功
     */
    @Override
    public boolean refreshTemplateExpireTime(Long templateId, Duration expireTime) {
        if (templateId == null || expireTime == null) {
            return false;
        }

        try {
            String cacheKey = getCacheKey(templateId);
            boolean result = redisUtils.expire(cacheKey, expireTime.getSeconds());
            
            if (result) {
                log.debug("成功刷新模板缓存过期时间，templateId: {}, expireTime: {}秒", 
                    templateId, expireTime.getSeconds());
            } else {
                log.warn("刷新模板缓存过期时间失败，templateId: {}", templateId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("刷新模板缓存过期时间失败，templateId: {}", templateId, e);
            return false;
        }
    }

    /**
     * 获取缓存键名
     * @param templateId 模板ID
     * @return 缓存键名
     */
    @Override
    public String getCacheKey(Long templateId) {
        return cacheKeyPrefix + "id:" + templateId;
    }

    /**
     * 获取缓存键名（通过模板代码）
     * @param templateCode 模板代码
     * @return 缓存键名
     */
    @Override
    public String getCacheKey(String templateCode) {
        return cacheKeyPrefix + "code:" + templateCode;
    }

    /**
     * 批量预热模板缓存
     * @param templateIds 模板ID列表
     * @return 成功加载的模板数量
     */
    @Override
    public int preloadTemplates(Long[] templateIds) {
        if (templateIds == null || templateIds.length == 0) {
            log.warn("模板ID列表为空，无法预热缓存");
            return 0;
        }

        AtomicInteger successCount = new AtomicInteger(0);
        
        // 使用异步方式批量预热
        CompletableFuture<Void>[] futures = new CompletableFuture[templateIds.length];
        
        for (int i = 0; i < templateIds.length; i++) {
            Long templateId = templateIds[i];
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    // 检查缓存中是否已存在
                    if (isTemplateExistsInCache(templateId)) {
                        log.debug("模板已存在于缓存中，跳过预热，templateId: {}", templateId);
                        return;
                    }
                    
                    // 从MinIO或本地加载模板内容
                    String templateContent = templateFileService.loadTemplateContent(templateId);
                    if (StringUtils.hasText(templateContent)) {
                        // 存储到缓存
                        if (setTemplateToCache(templateId, templateContent, Duration.ofHours(defaultExpireHours))) {
                            successCount.incrementAndGet();
                            log.debug("成功预热模板缓存，templateId: {}", templateId);
                        }
                    }
                } catch (Exception e) {
                    log.error("预热模板缓存失败，templateId: {}", templateId, e);
                }
            });
        }
        
        // 等待所有异步任务完成
        CompletableFuture.allOf(futures).join();
        
        int result = successCount.get();
        log.info("批量预热模板缓存完成，总数: {}, 成功: {}", templateIds.length, result);
        
        return result;
    }

    /**
     * 清理过期的模板缓存
     * @return 清理的缓存数量
     */
    @Override
    public int cleanExpiredTemplates() {
        try {
            // 简化实现，只返回0，因为Redis会自动清理过期的key
            log.debug("Redis会自动清理过期的模板缓存");
            return 0;
        } catch (Exception e) {
            log.error("清理过期模板缓存失败", e);
            return 0;
        }
    }
} 