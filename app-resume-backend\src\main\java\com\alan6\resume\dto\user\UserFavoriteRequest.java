package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;

/**
 * 用户收藏请求DTO
 * 
 * 用于添加收藏时的参数传递和校验。
 * 支持模板收藏和简历收藏两种类型。
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(name = "UserFavoriteRequest", description = "用户收藏请求")
public class UserFavoriteRequest {

    /**
     * 收藏类型
     * 1-模板收藏，2-简历收藏
     */
    @NotNull(message = "收藏类型不能为空")
    @Schema(description = "收藏类型（1:模板, 2:简历）", example = "1", required = true)
    private Byte targetType;

    /**
     * 目标ID（模板ID或简历ID）
     */
    @NotNull(message = "目标ID不能为空")
    @Min(value = 1, message = "目标ID必须大于0")
    @Schema(description = "目标ID（模板ID或简历ID）", example = "3001", required = true)
    private Long targetId;

    /**
     * 校验收藏类型是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isTargetTypeValid() {
        return targetType != null && (targetType == 1 || targetType == 2);
    }

    /**
     * 判断是否为模板收藏
     * 
     * @return true-模板收藏，false-非模板收藏
     */
    public boolean isTemplateTarget() {
        return targetType != null && targetType == 1;
    }

    /**
     * 判断是否为简历收藏
     * 
     * @return true-简历收藏，false-非简历收藏
     */
    public boolean isResumeTarget() {
        return targetType != null && targetType == 2;
    }
} 