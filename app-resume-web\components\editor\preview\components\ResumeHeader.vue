<template>
  <header class="resume-header">
    <!-- 背景装饰 -->
    <div class="header-background">
      <!-- 几何装饰元素 -->
      <div class="geometric-decoration">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="triangle"></div>
      </div>
    </div>

    <!-- 头部内容 -->
    <div class="header-content">
      <!-- 头像区域 -->
      <div class="avatar-section">
        <div class="avatar-container group">
          <div class="avatar">
            <img 
              v-if="basicInfo.photo" 
              :src="basicInfo.photo" 
              :alt="basicInfo.name + '的头像'"
              class="avatar-image"
            />
            <div v-else class="avatar-placeholder">
              <svg class="avatar-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
            </div>
          </div>
          <!-- 头像只读显示 -->
        </div>
      </div>

      <!-- 文字信息区域 -->
      <div class="info-section">
        <!-- 姓名 -->
        <h1 class="name" :style="nameStyles">
          {{ displayName }}
        </h1>

        <!-- 职位 -->
        <div class="job-title" :style="jobTitleStyles">
          {{ displayJobTitle }}
        </div>

        <!-- 联系信息 -->
        <div class="contact-info">
          <ContactItem
            v-if="basicInfo.phone"
            icon="phone"
            :value="basicInfo.phone"
          />
          <ContactItem
            v-if="basicInfo.email"
            icon="email"
            :value="basicInfo.email"
          />
          <ContactItem
            v-if="basicInfo.address"
            icon="location"
            :value="basicInfo.address"
          />
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
/**
 * 简历头部组件
 * @description 现代化的简历头部，包含头像、基本信息和联系方式
 */

// ================================
// 导入依赖
// ================================
import { computed } from 'vue'
import ContactItem from './ContactItem.vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /** 基本信息 */
  basicInfo: {
    type: Object,
    required: true,
    default: () => ({
      name: '',
      title: '',
      phone: '',
      email: '',
      address: '',
      photo: ''
    })
  },
  
  /** 文字样式 */
  textStyle: {
    type: Object,
    default: () => ({})
  },
  
  /** 是否可拖拽交互 */
  isDraggable: {
    type: Boolean,
    default: false
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['update-basic-info'])

// ================================
// 预览区只读显示，不支持内容编辑
// ================================

/**
 * 处理联系信息编辑
 * @param {string} field - 字段名 (phone, email, address)
 * @param {string} currentValue - 当前值
 */
const handleContactEdit = (field, currentValue) => {
  if (!props.isDraggable) return
  
  const labels = {
    phone: '手机号码',
    email: '邮箱地址',
    address: '居住地址'
  }
  
  const newValue = prompt(`请输入${labels[field]}:`, currentValue)
  
  if (newValue !== null) {
    updateBasicInfo(field, newValue.trim())
  }
}

// ================================
// 计算属性
// ================================

/** 显示的姓名 */
const displayName = computed(() => {
  return props.basicInfo.name || '张三'
})

/** 显示的职位 */
const displayJobTitle = computed(() => {
  return props.basicInfo.title || '前端开发工程师'
})

/** 姓名样式 */
const nameStyles = computed(() => ({
  fontSize: props.textStyle.nameFontSize || '2.5rem',
  color: props.textStyle.nameColor || '#ffffff'
}))

/** 职位样式 */
const jobTitleStyles = computed(() => ({
  fontSize: props.textStyle.jobTitleFontSize || '1.2rem',
  color: props.textStyle.jobTitleColor || '#f1f5f9'
}))

// ================================
// 方法
// ================================

/**
 * 更新基本信息
 * @param {string} field - 字段名
 * @param {any} value - 新值
 */
const updateBasicInfo = (field, value) => {
  const updatedBasicInfo = {
    ...props.basicInfo,
    [field]: value
  }
  
  emit('update-basic-info', updatedBasicInfo)
}
</script>

<style scoped>
/* ================================
 * 头部容器
 * ================================ */
.resume-header {
  @apply relative overflow-hidden;
  
  /* 渐变背景 */
  background: linear-gradient(
    135deg,
    #667eea 0%,
    #764ba2 100%
  );
  
  /* 现代化设计增强 */
  background-size: 200% 200%;
  animation: gradientShift 8s ease-in-out infinite;
  
  min-height: 240px;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* ================================
 * 背景装饰
 * ================================ */
.header-background {
  @apply absolute inset-0 overflow-hidden;
}

.geometric-decoration {
  @apply absolute inset-0;
}

.circle {
  @apply absolute rounded-full;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.circle-1 {
  @apply w-32 h-32 -top-8 -right-8;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  @apply w-20 h-20 top-16 -left-4;
  animation: float 4s ease-in-out infinite reverse;
}

.triangle {
  @apply absolute w-0 h-0;
  border-left: 60px solid transparent;
  border-right: 60px solid transparent;
  border-bottom: 100px solid rgba(255, 255, 255, 0.05);
  top: 20px;
  right: 30%;
  animation: rotate 10s linear infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ================================
 * 头部内容
 * ================================ */
.header-content {
  @apply relative z-10 flex items-center justify-center p-10;
  min-height: 240px;
}

/* ================================
 * 头像区域
 * ================================ */
.avatar-section {
  @apply mr-8;
}

.avatar-container {
  @apply relative;
}

.avatar-container.interactive {
  @apply cursor-pointer;
}

.avatar {
  @apply w-32 h-32 rounded-full border-4 border-white shadow-lg;
  @apply bg-white/20 backdrop-blur-sm;
  @apply flex items-center justify-center overflow-hidden;
  @apply transition-all duration-300 ease-out;
}

.avatar-container:hover .avatar {
  @apply transform scale-105 shadow-xl;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

.avatar-image {
  @apply w-full h-full object-cover;
}

.avatar-placeholder {
  @apply w-full h-full flex items-center justify-center;
  @apply text-white/80;
}

.avatar-icon {
  @apply w-16 h-16;
}

.edit-overlay {
  @apply absolute inset-0 rounded-full;
  @apply bg-black/50 backdrop-blur-sm;
  @apply flex items-center justify-center;
  @apply opacity-0 group-hover:opacity-100;
  @apply transition-opacity duration-200;
}

.edit-text {
  @apply text-white text-sm font-medium;
}

/* ================================
 * 信息区域
 * ================================ */
.info-section {
  @apply text-white text-center;
}

.name {
  @apply font-bold mb-3;
  @apply transition-all duration-200;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.name:hover {
  @apply transform scale-105;
}

.job-title {
  @apply opacity-90 mb-6 font-medium;
  @apply transition-all duration-200;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.job-title:hover {
  @apply opacity-100 transform scale-105;
}

/* ================================
 * 联系信息
 * ================================ */
.contact-info {
  @apply flex flex-wrap justify-center gap-6;
}

/* ================================
 * 响应式设计
 * ================================ */
@media (max-width: 768px) {
  .header-content {
    @apply flex-col text-center p-6;
  }
  
  .avatar-section {
    @apply mr-0 mb-6;
  }
  
  .avatar {
    @apply w-24 h-24;
  }
  
  .name {
    @apply text-3xl;
  }
  
  .job-title {
    @apply text-lg;
  }
  
  .contact-info {
    @apply flex-col gap-3;
  }
}
</style> 