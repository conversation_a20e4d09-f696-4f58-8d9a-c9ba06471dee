<template>
  <section class="bg-gradient-to-br from-primary-500 via-primary-600 to-orange-600 text-white relative overflow-visible">
    <!-- 装饰性背景图案 -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute top-10 left-10 w-32 h-32 border-2 border-white rounded-full"></div>
      <div class="absolute top-32 right-16 w-20 h-20 bg-white/20 rounded-lg rotate-45"></div>
      <div class="absolute bottom-20 left-20 w-16 h-16 bg-white/15 rounded-full"></div>
      <div class="absolute bottom-32 right-32 w-24 h-24 border border-white rounded-lg rotate-12"></div>
      <div class="absolute top-20 right-1/4 w-12 h-12 bg-gradient-to-br from-yellow-400/30 to-orange-500/30 rounded-2xl rotate-6"></div>
      <div class="absolute bottom-40 left-1/3 w-8 h-8 bg-white/20 rounded-full"></div>
    </div>

    <div class="max-w-screen-2xl mx-auto px-6 lg:px-8 py-6 lg:py-8 pb-12 lg:pb-16 relative z-10">
      <div class="text-center max-w-4xl mx-auto">
        <h1 class="font-display font-bold text-4xl lg:text-5xl mb-6">
          好简历，从一个好模板开始
        </h1>
        
        <!-- Slogan 三个特色点 -->
        <div class="flex flex-wrap justify-center items-center gap-8 text-lg mb-12">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-white rounded-full"></div>
            <span>海量精美模板</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-white rounded-full"></div>
            <span>5分钟极速制作</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-white rounded-full"></div>
            <span>累计创建简历40万份</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图片导航卡片 - 使用绝对定位实现跨越效果 -->
    <div class="absolute left-0 right-0 -bottom-20 z-30">
      <div class="max-w-screen-2xl mx-auto px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 max-w-4xl mx-auto">
          <div 
            v-for="(nav, index) in navigationCards" 
            :key="nav.id"
            @click="handleNavClick(nav)"
            class="group cursor-pointer"
          >
            <div 
              :class="nav.bgClass"
              class="relative p-6 rounded-2xl transition-all duration-300 transform group-hover:scale-105 group-hover:shadow-2xl min-h-[120px] flex flex-col justify-between shadow-lg"
            >
              <!-- 装饰图标 -->
              <div class="absolute top-4 right-4 opacity-20">
                <div class="w-8 h-8 bg-current rounded-lg"></div>
              </div>
              
              <div>
                <h3 class="font-bold text-lg mb-2">{{ nav.title }}</h3>
                <p class="text-sm opacity-90">{{ nav.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { defineEmits } from 'vue'

// Props and Emits
const emit = defineEmits(['nav-click'])

// 导航卡片数据
const navigationCards = ref([
  {
    id: 'import',
    title: '导入简历',
    description: '上传已有简历快速编辑',
    bgClass: 'bg-gradient-to-br from-pink-400 to-pink-500 text-white'
  },
  {
    id: 'optimize',
    title: '简历优化',
    description: 'AI智能优化简历内容',
    bgClass: 'bg-gradient-to-br from-cyan-400 to-cyan-500 text-white'
  },
  {
    id: 'ai-resume',
    title: 'AI一键简历',
    description: '智能生成专业简历',
    bgClass: 'bg-gradient-to-br from-purple-400 to-purple-500 text-white'
  },
  {
    id: 'score',
    title: '简历打分',
    description: '专业简历评分建议',
    bgClass: 'bg-gradient-to-br from-teal-400 to-teal-500 text-white'
  },
  {
    id: 'track',
    title: '简历追踪',
    description: '简历投递状态跟踪',
    bgClass: 'bg-gradient-to-br from-yellow-400 to-orange-400 text-white'
  }
])

// Event Handlers
const handleNavClick = (nav) => {
  emit('nav-click', nav)
}
</script> 