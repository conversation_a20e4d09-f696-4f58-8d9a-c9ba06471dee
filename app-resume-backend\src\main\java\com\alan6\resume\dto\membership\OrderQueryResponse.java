package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单查询响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "OrderQueryResponse", description = "订单查询响应")
public class OrderQueryResponse {

    /**
     * 订单ID
     */
    @Schema(description = "订单ID", example = "123")
    private Long id;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "ORD20241201123456")
    private String orderNo;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    /**
     * 订单类型（1:会员充值,2:模板购买）
     */
    @Schema(description = "订单类型", example = "1", allowableValues = {"1", "2"})
    private Byte orderType;

    /**
     * 订单类型描述
     */
    @Schema(description = "订单类型描述", example = "会员充值")
    private String orderTypeDesc;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID", example = "2")
    private Long productId;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", example = "年度会员")
    private String productName;

    /**
     * 原价
     */
    @Schema(description = "原价", example = "299.90")
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    @Schema(description = "优惠金额", example = "100.00")
    private BigDecimal discountAmount;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额", example = "199.90")
    private BigDecimal finalAmount;

    /**
     * 支付方式（1:微信,2:支付宝）
     */
    @Schema(description = "支付方式", example = "1", allowableValues = {"1", "2"})
    private Byte paymentMethod;

    /**
     * 支付方式描述
     */
    @Schema(description = "支付方式描述", example = "微信支付")
    private String paymentMethodDesc;

    /**
     * 支付平台
     */
    @Schema(description = "支付平台", example = "wechat")
    private String paymentPlatform;

    /**
     * 第三方交易号
     */
    @Schema(description = "第三方交易号", example = "TXN20241201123456")
    private String tradeNo;

    /**
     * 订单状态（1:待支付,2:已支付,3:已完成,4:已取消,5:已退款）
     */
    @Schema(description = "订单状态", example = "2", allowableValues = {"1", "2", "3", "4", "5"})
    private Byte orderStatus;

    /**
     * 订单状态描述
     */
    @Schema(description = "订单状态描述", example = "已支付")
    private String orderStatusDesc;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间", example = "2024-12-01 10:30:00")
    private LocalDateTime payTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间", example = "2024-12-01 10:31:00")
    private LocalDateTime completeTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-01 10:00:00")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-12-01 10:30:00")
    private LocalDateTime updateTime;
} 