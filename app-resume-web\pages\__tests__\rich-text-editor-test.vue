<template>
  <div class="test-page">
    <div class="container mx-auto p-8">
      <h1 class="text-2xl font-bold mb-6">富文本编辑器测试</h1>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 编辑器区域 -->
        <div class="space-y-6">
          <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">富文本编辑器</h2>
            <RichTextEditor
              v-model="content"
              placeholder="请输入教育经历详情，支持粗体、斜体、列表等格式..."
              min-height="200px"
            />
          </div>
          
          <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">原始HTML内容</h2>
            <div class="bg-gray-100 p-4 rounded border font-mono text-sm">
              <pre>{{ content || '(空内容)' }}</pre>
            </div>
          </div>
        </div>
        
        <!-- 预览区域 -->
        <div class="space-y-6">
          <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">预览效果</h2>
            <div class="border border-gray-200 rounded p-4 min-h-[200px]">
              <div 
                v-if="content" 
                class="rich-text-preview"
                v-html="content"
              ></div>
              <div v-else class="text-gray-500 italic">
                暂无内容
              </div>
            </div>
          </div>
          
          <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">教育经历模拟</h2>
            <div class="education-item bg-gray-50 rounded-lg p-4">
              <div class="education-header mb-3">
                <h3 class="text-lg font-bold text-gray-900">北京大学</h3>
                <div class="flex gap-2 text-gray-700">
                  <span class="font-semibold">本科</span>
                  <span class="text-gray-600">计算机科学与技术</span>
                </div>
                <div class="text-sm text-gray-500 font-medium">2020-09 — 2024-06</div>
              </div>
              <div v-if="content" class="education-description" v-html="content"></div>
            </div>
          </div>
        </div>
      </div>
      
             <!-- 键盘快捷键说明 -->
       <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
         <h2 class="text-lg font-semibold mb-4">键盘快捷键</h2>
         <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
           <div class="flex items-center gap-2">
             <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+B</kbd>
             <span>粗体</span>
           </div>
           <div class="flex items-center gap-2">
             <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+I</kbd>
             <span>斜体</span>
           </div>
           <div class="flex items-center gap-2">
             <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+U</kbd>
             <span>下划线</span>
           </div>
           <div class="flex items-center gap-2">
             <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+K</kbd>
             <span>超链接</span>
           </div>
         </div>
       </div>
       
       <!-- 功能测试按钮 -->
       <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
                   <h2 class="text-lg font-semibold mb-4">快速测试内容</h2>
         <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button 
            @click="setTestContent('bold')" 
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            粗体测试
          </button>
          <button 
            @click="setTestContent('list')" 
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            列表测试
          </button>
          <button 
            @click="setTestContent('link')" 
            class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            链接测试
          </button>
          <button 
            @click="setTestContent('mixed')" 
            class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            混合测试
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

// 响应式数据
const content = ref('')

// 测试内容
const testContents = {
  bold: '<strong>这是粗体文本</strong>，还有<em>斜体文本</em>和<u>下划线文本</u>。',
  list: `<ul>
<li>主修课程：数据结构、算法设计、计算机网络</li>
<li>获得荣誉：国家奖学金、优秀学生干部</li>
<li>参与项目：校园信息管理系统开发</li>
</ul>`,
  link: '访问我的个人网站：<a href="https://example.com">https://example.com</a>',
  mixed: `<div style="text-align: center;"><strong>北京大学计算机科学与技术专业</strong></div>
<br>
<strong>主修课程：</strong>
<ul>
<li>数据结构与算法</li>
<li>计算机网络</li>
<li>操作系统</li>
</ul>
<br>
<strong>获得荣誉：</strong>
<ol>
<li>国家奖学金（2022年）</li>
<li>优秀学生干部（2021年）</li>
<li>ACM程序设计竞赛三等奖</li>
</ol>
<br>
<em>联系方式：</em> <a href="mailto:<EMAIL>"><EMAIL></a>`
}

// 设置测试内容
const setTestContent = (type) => {
  content.value = testContents[type]
}
</script>

<style scoped>
.test-page {
  min-height: 100vh;
  background-color: #f3f4f6;
}

/* 预览区域样式 */
.rich-text-preview {
  line-height: 1.6;
  color: #374151;
}

.rich-text-preview strong,
.rich-text-preview b {
  font-weight: bold;
}

.rich-text-preview em,
.rich-text-preview i {
  font-style: italic;
}

.rich-text-preview u {
  text-decoration: underline;
}

.rich-text-preview a {
  color: #3b82f6;
  text-decoration: underline;
}

.rich-text-preview ul {
  list-style-type: disc;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.rich-text-preview ol {
  list-style-type: decimal;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.rich-text-preview li {
  margin-bottom: 4px;
}

.rich-text-preview div[style*="text-align: center"] {
  text-align: center;
}

.rich-text-preview div[style*="text-align: right"] {
  text-align: right;
}

.rich-text-preview div[style*="text-align: left"] {
  text-align: left;
}

/* 教育经历样式 */
.education-item {
  border: 1px solid #e5e7eb;
}

.education-description {
  line-height: 1.6;
  color: #374151;
}

.education-description strong,
.education-description b {
  font-weight: bold;
}

.education-description em,
.education-description i {
  font-style: italic;
}

.education-description u {
  text-decoration: underline;
}

.education-description a {
  color: #3b82f6;
  text-decoration: underline;
}

.education-description ul {
  list-style-type: disc;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.education-description ol {
  list-style-type: decimal;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.education-description li {
  margin-bottom: 4px;
}

.education-description div[style*="text-align: center"] {
  text-align: center;
}

.education-description div[style*="text-align: right"] {
  text-align: right;
}

.education-description div[style*="text-align: left"] {
  text-align: left;
}
</style> 