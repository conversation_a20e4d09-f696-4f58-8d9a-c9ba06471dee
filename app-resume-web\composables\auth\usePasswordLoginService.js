/**
 * 密码登录服务
 * @description 专门处理基于密码的用户登录功能
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, computed } from 'vue'
import HttpClient, { API_CONFIG } from '~/utils/api-config'
import { useAuthService } from './useAuthService'
import { $message } from '../shared/useGlobalMessage'

// ================================
// 响应式状态管理
// ================================
const loading = ref(false)
const error = ref(null)

// ================================
// 表单验证工具
// ================================
class FormValidator {
  /**
   * 验证手机号格式
   * @param {string} phone - 手机号
   * @returns {Object} 验证结果
   */
  static validatePhone(phone) {
    if (!phone) {
      return { isValid: false, message: '请输入手机号' }
    }
    
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return { isValid: false, message: '请输入正确的手机号格式' }
    }
    
    return { isValid: true, message: '' }
  }

  /**
   * 验证密码格式
   * @param {string} password - 密码
   * @returns {Object} 验证结果
   */
  static validatePassword(password) {
    if (!password) {
      return { isValid: false, message: '请输入密码' }
    }
    
    if (password.length < 6) {
      return { isValid: false, message: '密码长度不能少于6位' }
    }
    
    if (password.length > 20) {
      return { isValid: false, message: '密码长度不能超过20位' }
    }
    
    return { isValid: true, message: '' }
  }

  /**
   * 验证登录表单
   * @param {Object} formData - 表单数据
   * @returns {Object} 验证结果
   */
  static validateLoginForm(formData) {
    const errors = {}
    let isValid = true

    // 验证手机号
    const phoneResult = this.validatePhone(formData.phone)
    if (!phoneResult.isValid) {
      errors.phone = phoneResult.message
      isValid = false
    }

    // 验证密码
    const passwordResult = this.validatePassword(formData.password)
    if (!passwordResult.isValid) {
      errors.password = passwordResult.message
      isValid = false
    }

    return { isValid, errors }
  }
}

// ================================
// 设备信息工具
// ================================
class DeviceInfoHelper {
  /**
   * 生成设备ID
   * @returns {string} 设备ID
   */
  static generateDeviceId() {
    if (process.client) {
      let deviceId = localStorage.getItem('device_id')
      if (!deviceId) {
        deviceId = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
        localStorage.setItem('device_id', deviceId)
      }
      return deviceId
    }
    return 'web_' + Date.now()
  }

  /**
   * 获取用户代理信息
   * @returns {string} 用户代理
   */
  static getUserAgent() {
    return process.client ? navigator.userAgent : 'Server'
  }

  /**
   * 获取客户端IP（前端无法直接获取，由后端处理）
   * @returns {string} 客户端IP占位符
   */
  static getClientIp() {
    return 'auto_detect'
  }
}

// ================================
// API客户端（使用统一的HttpClient）
// ================================
class ApiClient {
  /**
   * 发送HTTP请求
   * @param {string} endpoint - 接口端点
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  static async request(endpoint, options = {}) {
    return await HttpClient.request(endpoint, options)
  }
}

// ================================
// 登录请求构建器
// ================================
class LoginRequestBuilder {
  /**
   * 构建密码登录请求参数
   * @param {Object} formData - 表单数据
   * @returns {Object} 请求参数
   */
  static buildPasswordLoginRequest(formData) {
    return {
      loginType: 2, // 密码登录类型
      platform: 'web',
      phone: formData.phone,
      password: formData.password
    }
  }
}

// ================================
// Token 管理工具
// ================================
class TokenManager {
  /**
   * 存储访问令牌
   * @param {string} token - 访问令牌
   */
  static setAccessToken(token) {
    if (process.client) {
      localStorage.setItem('auth_token', token)
    }
  }

  /**
   * 存储刷新令牌
   * @param {string} refreshToken - 刷新令牌
   */
  static setRefreshToken(refreshToken) {
    if (process.client) {
      localStorage.setItem('refresh_token', refreshToken)
    }
  }

  /**
   * 存储用户信息
   * @param {Object} userInfo - 用户信息
   */
  static setUserInfo(userInfo) {
    if (process.client) {
      localStorage.setItem('user_info', JSON.stringify(userInfo))
    }
  }

  /**
   * 清除所有认证信息
   */
  static clearAuthData() {
    if (process.client) {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user_info')
    }
  }
}

// ================================
// 密码登录服务类
// ================================
class PasswordLoginService {
  /**
   * 执行密码登录
   * @param {Object} formData - 登录表单数据
   * @returns {Promise<Object>} 登录结果
   */
  static async login(formData) {
    try {
      loading.value = true
      error.value = null

      // 1. 表单验证
      const validation = FormValidator.validateLoginForm(formData)
      if (!validation.isValid) {
        throw new Error(Object.values(validation.errors)[0])
      }

      // 2. 构建请求参数
      const loginRequest = LoginRequestBuilder.buildPasswordLoginRequest(formData)

      // 3. 发送登录请求
      const response = await ApiClient.request(API_CONFIG.ENDPOINTS.AUTH.PASSWORD_LOGIN, {
        method: 'POST',
        body: loginRequest
      })

      console.log('🔍 密码登录API响应:', response)

      // 4. 处理响应数据 - 适配后端字段名
      if (response.token) {
        console.log('✅ 找到token，开始处理登录状态')
        // 后端返回的是 token 字段
        TokenManager.setAccessToken(response.token)
        
        // 构建用户信息对象
        const userInfo = {
          id: response.userId,
          userId: response.userId,
          username: response.username,
          nickname: response.nickname,
          phone: response.phone,
          email: response.email,
          avatar: response.avatarUrl,
          gender: response.gender,
          status: response.status,
          isPhoneVerified: response.isPhoneVerified,
          preferredLanguage: response.preferredLanguage,
          registerPlatform: response.registerPlatform,
          lastLoginTime: response.lastLoginTime,
          membershipInfo: response.membershipInfo,
          permissions: response.permissions,
          isNewUser: response.isNewUser
        }
        
        console.log('📝 构建的用户信息:', userInfo)
        
        TokenManager.setUserInfo(userInfo)
        
        // 如果有刷新Token也存储
        if (response.refreshToken) {
          TokenManager.setRefreshToken(response.refreshToken)
        }

        // 5. 同步更新全局认证状态
        const authService = useAuthService()
        console.log('🔄 更新全局认证状态前，当前登录状态:', authService.isLoggedIn.value)
        authService.updateLoginState(userInfo, response.token)
        console.log('✅ 更新全局认证状态后，当前登录状态:', authService.isLoggedIn.value)
      } else {
        console.error('❌ 响应中没有找到token字段')
        console.log('🔍 完整响应内容:', JSON.stringify(response, null, 2))
      }

      // 返回格式化的响应数据，保持与前端期望的格式一致
      const formattedResponse = {
        accessToken: response.token,
        refreshToken: response.refreshToken || null,
        tokenExpireTime: response.tokenExpireTime,
        userInfo: {
          id: response.userId,
          userId: response.userId,
          username: response.username,
          nickname: response.nickname,
          phone: response.phone,
          email: response.email,
          avatar: response.avatarUrl,
          gender: response.gender,
          status: response.status,
          isPhoneVerified: response.isPhoneVerified,
          preferredLanguage: response.preferredLanguage,
          registerPlatform: response.registerPlatform,
          lastLoginTime: response.lastLoginTime,
          membershipInfo: response.membershipInfo,
          permissions: response.permissions,
          isNewUser: response.isNewUser
        }
      }

      // 显示登录成功消息
      $message.success('登录成功！欢迎回来')

      return {
        success: true,
        data: formattedResponse,
        message: '登录成功'
      }
    } catch (err) {
      const errorMessage = this.handleLoginError(err)
      error.value = errorMessage
      
      // 显示登录失败消息
      $message.error(errorMessage)
      
      return {
        success: false,
        error: errorMessage,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理登录错误
   * @param {Error} error - 错误对象
   * @returns {string} 用户友好的错误消息
   */
  static handleLoginError(error) {
    const errorMessage = error.message || '登录失败'
    
    // 根据错误类型返回不同的提示
    if (errorMessage.includes('用户不存在')) {
      return '账号不存在，请先注册'
    } else if (errorMessage.includes('密码错误')) {
      return '密码错误，请重新输入'
    } else if (errorMessage.includes('账户已被禁用')) {
      return '账户已被禁用，请联系客服'
    } else if (errorMessage.includes('登录失败次数过多')) {
      return '登录失败次数过多，请稍后再试'
    } else if (errorMessage.includes('参数验证失败')) {
      return '输入信息有误，请检查后重试'
    } else {
      return errorMessage
    }
  }
}

// ================================
// 组合式函数导出
// ================================
export const usePasswordLoginService = () => {
  // 计算属性
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)

  /**
   * 执行密码登录
   * @param {Object} formData - 登录表单数据
   * @returns {Promise<Object>} 登录结果
   */
  const passwordLogin = async (formData) => {
    const result = await PasswordLoginService.login(formData)
    if (!result.success) {
      error.value = PasswordLoginService.handleLoginError(new Error(result.error))
    }
    return result
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 验证登录表单
   * @param {Object} formData - 表单数据
   * @returns {Object} 验证结果
   */
  const validateForm = (formData) => {
    return FormValidator.validateLoginForm(formData)
  }

  /**
   * 验证单个字段
   * @param {string} field - 字段名
   * @param {string} value - 字段值
   * @returns {Object} 验证结果
   */
  const validateField = (field, value) => {
    switch (field) {
      case 'phone':
        return FormValidator.validatePhone(value)
      case 'password':
        return FormValidator.validatePassword(value)
      default:
        return { isValid: true, message: '' }
    }
  }

  return {
    // 响应式状态
    loading: readonly(loading),
    error: readonly(error),
    isLoading,
    hasError,

    // 核心方法
    passwordLogin,
    clearError,

    // 验证方法
    validateForm,
    validateField
  }
}

/**
 * 只读包装器
 * @param {Ref} ref - 响应式引用
 * @returns {ComputedRef} 只读的计算属性
 */
function readonly(ref) {
  return computed(() => ref.value)
} 