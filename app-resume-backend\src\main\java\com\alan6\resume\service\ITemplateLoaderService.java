package com.alan6.resume.service;

import com.alan6.resume.dto.template.TemplateLoadRequest;
import com.alan6.resume.dto.template.TemplateLoadResponse;

/**
 * 模板加载服务接口
 * @description 核心服务，实现Redis->MinIO->Local的三层存储架构
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ITemplateLoaderService {

    /**
     * 加载模板内容
     * @description 按照Redis->MinIO->Local的优先级加载模板内容
     * @param request 模板加载请求
     * @return 模板加载响应
     */
    TemplateLoadResponse loadTemplate(TemplateLoadRequest request);

    /**
     * 预热模板缓存
     * @description 将热门模板预加载到Redis缓存中
     * @param templateIds 模板ID数组
     * @return 预热成功的模板数量
     */
    int preloadTemplates(Long[] templateIds);

    /**
     * 刷新模板缓存
     * @description 强制刷新指定模板的缓存
     * @param templateId 模板ID
     * @return 是否刷新成功
     */
    boolean refreshTemplateCache(Long templateId);

    /**
     * 检查模板可用性
     * @description 检查模板在各个存储层的可用性
     * @param templateId 模板ID
     * @return 可用性报告
     */
    String checkTemplateAvailability(Long templateId);
} 