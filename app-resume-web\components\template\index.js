/**
 * 模板相关组件的统一导出
 * 方便其他页面引用和管理
 */

// 主要组件
export { default as HeroBanner } from './HeroBanner.vue'
export { default as SearchFilter } from './SearchFilter.vue'
export { default as TemplateGrid } from './TemplateGrid.vue'
export { default as TemplateCard } from './TemplateCard.vue'
export { default as TemplatePagination } from './TemplatePagination.vue'

// 组件配置常量
export const TEMPLATE_CONSTANTS = {
  // 分页配置
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 50,
  
  // 模板类型
  TEMPLATE_TYPES: {
    HOT: 'hot',
    STYLE: 'style', 
    INDUSTRY: 'industry',
    MAJOR: 'major',
    INTERN: 'intern'
  },
  
  // 设计风格
  DESIGN_STYLES: {
    ALL: 'all',
    CLASSIC: 'classic',
    BUSINESS: 'business', 
    CREATIVE: 'creative',
    TECH: 'tech',
    MODERN: 'modern',
    FRESH: 'fresh'
  }
} 