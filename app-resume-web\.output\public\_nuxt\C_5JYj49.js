import{_ as N}from"./BXnw39BI.js";import{u as D}from"./PsJl1LMp.js";import{I as c}from"./D7AOVZt6.js";import{_ as k}from"./DlAUqK2U.js";import{r as u,c as o,o as i,a as s,t as n,h,b as e,F as p,g as f,s as _,f as A,w as r,d as S,z as V,q as B}from"./CURHyiUL.js";const F={class:"admin-dashboard"},z={class:"welcome-section"},L={class:"welcome-content"},T={class:"welcome-title"},$={class:"welcome-subtitle"},E={class:"welcome-actions"},j={class:"action-button primary"},G={class:"action-button secondary"},H={class:"stats-grid"},J={class:"stat-content"},K={class:"stat-value"},M={class:"stat-label"},O={class:"dashboard-grid"},P={class:"dashboard-card"},Q={class:"card-header"},R={class:"card-actions"},U={class:"card-action-btn"},W={class:"dashboard-card"},X={class:"card-header"},Y={class:"card-content"},Z={class:"template-list"},ss={class:"template-preview"},as=["src","alt"],ts={class:"template-info"},es={class:"template-name"},cs={class:"template-stats"},ns={class:"dashboard-card"},is={class:"card-header"},os={class:"card-action-btn"},ds={class:"card-content"},ls={class:"activity-list"},rs={class:"activity-content"},_s={class:"activity-text"},vs={class:"activity-time"},ms={class:"dashboard-card"},us={class:"card-content"},hs={class:"quick-actions"},ps={class:"quick-action-icon users"},fs={class:"quick-action-icon templates"},gs={class:"quick-action-icon orders"},ws={class:"quick-action-icon settings"},bs={__name:"AdminDashboard",setup(y){const{adminInfo:v,isSuperAdmin:g}=D(),m=u([{key:"users",label:"总用户数",value:12580,change:"+12.5%",changeClass:"positive",changeIcon:"arrow-up",icon:"users",iconClass:"users"},{key:"resumes",label:"简历总数",value:8460,change:"+8.2%",changeClass:"positive",changeIcon:"arrow-up",icon:"document",iconClass:"resumes"},{key:"templates",label:"模板数量",value:156,change:"+3.1%",changeClass:"positive",changeIcon:"arrow-up",icon:"document",iconClass:"templates"},{key:"revenue",label:"本月收入",value:45680,change:"-2.4%",changeClass:"negative",changeIcon:"arrow-down",icon:"dollar-sign",iconClass:"revenue"}]),C=u([{id:1,name:"现代简约",preview:"/api/admin/placeholder/120/80",uses:1240,trend:"+15%",trendClass:"positive",trendIcon:"arrow-up"},{id:2,name:"商务专业",preview:"/api/admin/placeholder/120/80",uses:980,trend:"+8%",trendClass:"positive",trendIcon:"arrow-up"},{id:3,name:"创意设计",preview:"/api/admin/placeholder/120/80",uses:756,trend:"-3%",trendClass:"negative",trendIcon:"arrow-down"}]),x=u([{id:1,text:"用户 张三 注册了新账号",time:"2分钟前",icon:"user-plus",iconClass:"user"},{id:2,text:'新模板 "科技风格" 已上架',time:"15分钟前",icon:"plus",iconClass:"template"},{id:3,text:"订单 #12345 支付成功",time:"1小时前",icon:"check-circle",iconClass:"order"},{id:4,text:"系统进行了定期备份",time:"2小时前",icon:"download",iconClass:"system"}]),I=d=>new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"}).format(d),q=d=>d>=1e4?(d/1e4).toFixed(1)+"w":d.toLocaleString();return(d,a)=>{var w,b;const l=N;return i(),o("div",F,[s("div",z,[s("div",L,[s("h1",T," 欢迎回来，"+n(((w=h(v))==null?void 0:w.nickname)||((b=h(v))==null?void 0:b.username)||"管理员")+"！ ",1),s("p",$," 今天是 "+n(I(new Date))+"，祝您工作愉快！ ",1)]),s("div",E,[s("button",j,[e(c,{name:"plus",class:"w-4 h-4"}),a[0]||(a[0]=s("span",null,"新建模板",-1))]),s("button",G,[e(c,{name:"chart-bar",class:"w-4 h-4"}),a[1]||(a[1]=s("span",null,"查看报告",-1))])])]),s("div",H,[(i(!0),o(p,null,f(m.value,t=>(i(),o("div",{class:"stat-card",key:t.key},[s("div",{class:_(["stat-icon",t.iconClass])},[e(c,{name:t.icon,class:"w-6 h-6"},null,8,["name"])],2),s("div",J,[s("div",K,n(q(t.value)),1),s("div",M,n(t.label),1),s("div",{class:_(["stat-change",t.changeClass])},[e(c,{name:t.changeIcon,class:"w-3 h-3"},null,8,["name"]),s("span",null,n(t.change),1)],2)])]))),128))]),s("div",O,[s("div",P,[s("div",Q,[a[2]||(a[2]=s("h3",{class:"card-title"},"用户增长趋势",-1)),s("div",R,[s("button",U,[e(c,{name:"undo",class:"w-4 h-4"})])])]),a[3]||(a[3]=A('<div class="card-content" data-v-50a4cf9c><div class="chart-placeholder" data-v-50a4cf9c><div class="chart-mock" data-v-50a4cf9c><div class="chart-bars" data-v-50a4cf9c><div class="bar" style="height:60%;" data-v-50a4cf9c></div><div class="bar" style="height:80%;" data-v-50a4cf9c></div><div class="bar" style="height:45%;" data-v-50a4cf9c></div><div class="bar" style="height:90%;" data-v-50a4cf9c></div><div class="bar" style="height:70%;" data-v-50a4cf9c></div><div class="bar" style="height:95%;" data-v-50a4cf9c></div><div class="bar" style="height:85%;" data-v-50a4cf9c></div></div><div class="chart-labels" data-v-50a4cf9c><span data-v-50a4cf9c>周一</span><span data-v-50a4cf9c>周二</span><span data-v-50a4cf9c>周三</span><span data-v-50a4cf9c>周四</span><span data-v-50a4cf9c>周五</span><span data-v-50a4cf9c>周六</span><span data-v-50a4cf9c>周日</span></div></div></div></div>',1))]),s("div",W,[s("div",X,[a[5]||(a[5]=s("h3",{class:"card-title"},"热门模板",-1)),e(l,{to:"/admin/template/list",class:"card-link"},{default:r(()=>[a[4]||(a[4]=S(" 查看全部 ")),e(c,{name:"arrow-right",class:"w-4 h-4"})]),_:1,__:[4]})]),s("div",Y,[s("div",Z,[(i(!0),o(p,null,f(C.value,t=>(i(),o("div",{class:"template-item",key:t.id},[s("div",ss,[s("img",{src:t.preview,alt:t.name,class:"template-image"},null,8,as)]),s("div",ts,[s("h4",es,n(t.name),1),s("p",cs,n(t.uses)+" 次使用",1)]),s("div",{class:_(["template-trend",t.trendClass])},[e(c,{name:t.trendIcon,class:"w-3 h-3"},null,8,["name"]),s("span",null,n(t.trend),1)],2)]))),128))])])]),s("div",ns,[s("div",is,[a[6]||(a[6]=s("h3",{class:"card-title"},"最近活动",-1)),s("button",os,[e(c,{name:"menu",class:"w-4 h-4"})])]),s("div",ds,[s("div",ls,[(i(!0),o(p,null,f(x.value,t=>(i(),o("div",{class:"activity-item",key:t.id},[s("div",{class:_(["activity-icon",t.iconClass])},[e(c,{name:t.icon,class:"w-4 h-4"},null,8,["name"])],2),s("div",rs,[s("p",_s,n(t.text),1),s("p",vs,n(t.time),1)])]))),128))])])]),s("div",ms,[a[11]||(a[11]=s("div",{class:"card-header"},[s("h3",{class:"card-title"},"快捷操作")],-1)),s("div",us,[s("div",hs,[e(l,{to:"/admin/users",class:"quick-action"},{default:r(()=>[s("div",ps,[e(c,{name:"users",class:"w-5 h-5"})]),a[7]||(a[7]=s("span",{class:"quick-action-text"},"用户管理",-1))]),_:1,__:[7]}),e(l,{to:"/admin/template/list",class:"quick-action"},{default:r(()=>[s("div",fs,[e(c,{name:"document",class:"w-5 h-5"})]),a[8]||(a[8]=s("span",{class:"quick-action-text"},"模板管理",-1))]),_:1,__:[8]}),e(l,{to:"/admin/orders",class:"quick-action"},{default:r(()=>[s("div",gs,[e(c,{name:"shopping-cart",class:"w-5 h-5"})]),a[9]||(a[9]=s("span",{class:"quick-action-text"},"订单管理",-1))]),_:1,__:[9]}),h(g)?(i(),V(l,{key:0,to:"/admin/settings",class:"quick-action"},{default:r(()=>[s("div",ws,[e(c,{name:"settings",class:"w-5 h-5"})]),a[10]||(a[10]=s("span",{class:"quick-action-text"},"系统设置",-1))]),_:1,__:[10]})):B("",!0)])])])])])}}},ks=k(bs,[["__scopeId","data-v-50a4cf9c"]]),ys={class:"admin-dashboard-page"},Cs={__name:"index",setup(y){return(v,g)=>{const m=ks;return i(),o("div",ys,[e(m)])}}},As=k(Cs,[["__scopeId","data-v-02e83696"]]);export{As as default};
