package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 密码修改请求DTO
 * 
 * 主要功能：
 * 1. 接收用户修改密码的请求参数
 * 2. 支持两种验证方式：原密码验证和短信验证码验证
 * 3. 参数校验和安全性检查
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Schema(name = "密码修改请求", description = "用户修改密码的请求参数")
public class PasswordChangeRequest {

    /**
     * 验证方式
     * 1-原密码验证，2-短信验证码验证
     */
    @Schema(description = "验证方式，1-原密码验证，2-短信验证码验证1")
    @NotNull(message = "验证方式不能为空")
    private Integer verificationType;

    /**
     * 原密码
     * 当验证方式为1时必填
     */
    @Schema(description = "原密码")
    private String oldPassword;

    /**
     * 短信验证码
     * 当验证方式为2时必填
     */
    @Schema(description = "短信验证码")
    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确，应为6位数字")
    private String smsCode;

    /**
     * 新密码
     * 6-20位，包含字母和数字
     */
    @Schema(description = "新密码")
    @NotNull(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$", 
             message = "密码必须包含字母和数字，可包含特殊字符@$!%*?&")
    private String newPassword;

    /**
     * 确认密码
     * 必须与新密码一致
     */
    @Schema(description = "确认密码")
    @NotNull(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 验证是否为原密码验证方式
     * 
     * @return true-原密码验证，false-不是
     */
    public boolean isOldPasswordVerification() {
        return Integer.valueOf(1).equals(verificationType);
    }

    /**
     * 验证是否为短信验证码验证方式
     * 
     * @return true-短信验证码验证，false-不是
     */
    public boolean isSmsCodeVerification() {
        return Integer.valueOf(2).equals(verificationType);
    }

    /**
     * 验证新密码和确认密码是否一致
     * 
     * @return true-一致，false-不一致
     */
    public boolean isPasswordConfirmed() {
        if (newPassword == null || confirmPassword == null) {
            return false;
        }
        return newPassword.equals(confirmPassword);
    }

    /**
     * 验证原密码验证方式的参数是否完整
     * 
     * @return true-参数完整，false-参数不完整
     */
    public boolean isOldPasswordVerificationValid() {
        return isOldPasswordVerification() && oldPassword != null && !oldPassword.trim().isEmpty();
    }

    /**
     * 验证短信验证码验证方式的参数是否完整
     * 
     * @return true-参数完整，false-参数不完整
     */
    public boolean isSmsCodeVerificationValid() {
        return isSmsCodeVerification() && smsCode != null && !smsCode.trim().isEmpty();
    }

    /**
     * 验证请求参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        // 检查验证方式是否有效
        if (verificationType == null || (verificationType != 1 && verificationType != 2)) {
            return false;
        }

        // 检查密码是否确认
        if (!isPasswordConfirmed()) {
            return false;
        }

        // 根据验证方式检查对应参数
        if (isOldPasswordVerification()) {
            return isOldPasswordVerificationValid();
        } else if (isSmsCodeVerification()) {
            return isSmsCodeVerificationValid();
        }

        return false;
    }
} 