package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.system.SystemConfigRequest;
import com.alan6.resume.dto.system.SystemConfigResponse;
import com.alan6.resume.entity.SystemConfigs;
import com.alan6.resume.mapper.SystemConfigsMapper;
import com.alan6.resume.service.ISystemConfigsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
public class SystemConfigsServiceImpl extends ServiceImpl<SystemConfigsMapper, SystemConfigs> implements ISystemConfigsService {

    /**
     * 前端公开配置的分组
     */
    private static final Set<String> FRONTEND_GROUPS = Set.of("frontend", "public", "ui");

    /**
     * 获取系统配置列表（分页）
     *
     * @param group 配置分组（可选）
     * @param page  页码
     * @param size  每页数量
     * @return 分页结果
     */
    @Override
    public Page<SystemConfigResponse> getConfigList(String group, Integer page, Integer size) {
        log.info("获取系统配置列表, group: {}, page: {}, size: {}", group, page, size);
        
        // 构建查询条件
        LambdaQueryWrapper<SystemConfigs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfigs::getIsDeleted, 0);
        
        // 按分组过滤
        if (StringUtils.hasText(group)) {
            queryWrapper.eq(SystemConfigs::getConfigGroup, group);
        }
        
        // 排序
        queryWrapper.orderByAsc(SystemConfigs::getConfigGroup, SystemConfigs::getConfigKey);
        
        // 分页查询
        Page<SystemConfigs> configPage = new Page<>(page, size);
        Page<SystemConfigs> result = page(configPage, queryWrapper);
        
        // 转换为响应DTO
        Page<SystemConfigResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(result, responsePage, "records");
        
        List<SystemConfigResponse> responseList = result.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);
        
        log.info("获取系统配置列表成功, 总数: {}", result.getTotal());
        return responsePage;
    }

    /**
     * 根据配置键获取配置
     *
     * @param configKey 配置键
     * @return 配置响应
     */
    @Override
    public SystemConfigResponse getConfigByKey(String configKey) {
        log.info("根据配置键获取配置, configKey: {}", configKey);
        
        LambdaQueryWrapper<SystemConfigs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfigs::getConfigKey, configKey)
                   .eq(SystemConfigs::getIsDeleted, 0);
        
        SystemConfigs config = getOne(queryWrapper);
        if (config == null) {
            throw new BusinessException("系统配置不存在");
        }
        
        SystemConfigResponse response = convertToResponse(config);
        log.info("获取配置成功, configKey: {}", configKey);
        return response;
    }

    /**
     * 更新配置项
     *
     * @param configKey 配置键
     * @param request   配置请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConfig(String configKey, SystemConfigRequest request) {
        log.info("更新配置项, configKey: {}", configKey);
        
        // 查找现有配置
        LambdaQueryWrapper<SystemConfigs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfigs::getConfigKey, configKey)
                   .eq(SystemConfigs::getIsDeleted, 0);
        
        SystemConfigs existingConfig = getOne(queryWrapper);
        if (existingConfig == null) {
            throw new BusinessException("系统配置不存在");
        }
        
        // 更新配置值
        existingConfig.setConfigValue(request.getConfigValue());
        if (StringUtils.hasText(request.getConfigType())) {
            existingConfig.setConfigType(request.getConfigType());
        }
        if (StringUtils.hasText(request.getConfigGroup())) {
            existingConfig.setConfigGroup(request.getConfigGroup());
        }
        if (StringUtils.hasText(request.getDescription())) {
            existingConfig.setDescription(request.getDescription());
        }
        existingConfig.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(existingConfig);
        if (result) {
            log.info("配置更新成功, configKey: {}", configKey);
        } else {
            log.error("配置更新失败, configKey: {}", configKey);
            throw new BusinessException("配置更新失败");
        }
        
        return result;
    }

    /**
     * 创建配置项
     *
     * @param request 配置请求
     * @return 配置响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SystemConfigResponse createConfig(SystemConfigRequest request) {
        log.info("创建配置项, configKey: {}", request.getConfigKey());
        
        // 检查配置键是否已存在
        LambdaQueryWrapper<SystemConfigs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfigs::getConfigKey, request.getConfigKey())
                   .eq(SystemConfigs::getIsDeleted, 0);
        
        if (count(queryWrapper) > 0) {
            throw new BusinessException("配置键名已存在");
        }
        
        // 创建新配置
        SystemConfigs config = new SystemConfigs();
        BeanUtils.copyProperties(request, config);
        config.setIsDeleted((byte) 0);
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        
        boolean result = save(config);
        if (!result) {
            throw new BusinessException("配置创建失败");
        }
        
        SystemConfigResponse response = convertToResponse(config);
        log.info("配置创建成功, id: {}, configKey: {}", config.getId(), config.getConfigKey());
        return response;
    }

    /**
     * 获取配置分组列表
     *
     * @return 分组列表
     */
    @Override
    public List<String> getConfigGroups() {
        log.info("获取配置分组列表");
        
        LambdaQueryWrapper<SystemConfigs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SystemConfigs::getConfigGroup)
                   .eq(SystemConfigs::getIsDeleted, 0)
                   .isNotNull(SystemConfigs::getConfigGroup)
                   .ne(SystemConfigs::getConfigGroup, "")
                   .groupBy(SystemConfigs::getConfigGroup);
        
        List<SystemConfigs> configs = list(queryWrapper);
        List<String> groups = configs.stream()
                .map(SystemConfigs::getConfigGroup)
                .filter(Objects::nonNull)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        
        log.info("获取到 {} 个配置分组", groups.size());
        return groups;
    }

    /**
     * 获取前端配置（公开配置）
     *
     * @return 前端配置映射
     */
    @Override
    public Map<String, Object> getFrontendConfig() {
        log.info("获取前端配置");
        
        LambdaQueryWrapper<SystemConfigs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfigs::getIsDeleted, 0)
                   .in(SystemConfigs::getConfigGroup, FRONTEND_GROUPS);
        
        List<SystemConfigs> configs = list(queryWrapper);
        Map<String, Object> frontendConfig = new HashMap<>();
        
        for (SystemConfigs config : configs) {
            Object value = parseConfigValue(config.getConfigValue(), config.getConfigType());
            frontendConfig.put(config.getConfigKey(), value);
        }
        
        log.info("获取到 {} 个前端配置项", frontendConfig.size());
        return frontendConfig;
    }

    /**
     * 根据配置键获取配置值
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        try {
            LambdaQueryWrapper<SystemConfigs> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SystemConfigs::getConfigKey, configKey)
                       .eq(SystemConfigs::getIsDeleted, 0);
            
            SystemConfigs config = getOne(queryWrapper);
            return config != null ? config.getConfigValue() : defaultValue;
        } catch (Exception e) {
            log.warn("获取配置值失败, configKey: {}, 使用默认值: {}", configKey, defaultValue, e);
            return defaultValue;
        }
    }

    /**
     * 根据配置键获取配置值并转换为指定类型
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @param type         目标类型
     * @param <T>          泛型类型
     * @return 转换后的值
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T> T getConfigValue(String configKey, T defaultValue, Class<T> type) {
        try {
            String value = getConfigValue(configKey, null);
            if (value == null) {
                return defaultValue;
            }
            
            // 根据类型转换
            if (type == String.class) {
                return (T) value;
            } else if (type == Integer.class) {
                return (T) Integer.valueOf(value);
            } else if (type == Long.class) {
                return (T) Long.valueOf(value);
            } else if (type == Double.class) {
                return (T) Double.valueOf(value);
            } else if (type == Boolean.class) {
                return (T) Boolean.valueOf(value);
            } else {
                return defaultValue;
            }
        } catch (Exception e) {
            log.warn("获取并转换配置值失败, configKey: {}, type: {}, 使用默认值: {}", 
                    configKey, type.getSimpleName(), defaultValue, e);
            return defaultValue;
        }
    }

    /**
     * 转换为响应DTO
     *
     * @param config 配置实体
     * @return 响应DTO
     */
    private SystemConfigResponse convertToResponse(SystemConfigs config) {
        SystemConfigResponse response = new SystemConfigResponse();
        BeanUtils.copyProperties(config, response);
        return response;
    }

    /**
     * 解析配置值
     *
     * @param value 配置值
     * @param type  配置类型
     * @return 解析后的值
     */
    private Object parseConfigValue(String value, String type) {
        if (value == null) {
            return null;
        }
        
        try {
            switch (type.toLowerCase()) {
                case "number":
                    return value.contains(".") ? Double.parseDouble(value) : Long.parseLong(value);
                case "boolean":
                    return Boolean.parseBoolean(value);
                case "json":
                    // 这里可以集成JSON解析库，暂时返回字符串
                    return value;
                default:
                    return value;
            }
        } catch (Exception e) {
            log.warn("解析配置值失败, value: {}, type: {}", value, type, e);
            return value;
        }
    }
}
