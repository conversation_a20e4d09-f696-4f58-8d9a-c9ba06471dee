import{l as B,r as V,A as E,i as M,B as L,c as r,o as i,a as t,F as k,g as h,p as m,v as _,q as y,t as f,s as D,b as U}from"./CURHyiUL.js";import{I as N}from"./D7AOVZt6.js";import{R as Y}from"./Ciwj-CUv.js";import{_ as z}from"./DlAUqK2U.js";const R={class:"project-form"},q={key:0,class:"project-list"},A={class:"project-header"},G={class:"form-group"},H=["onUpdate:modelValue"],J={class:"form-group"},K=["onUpdate:modelValue"],Q=["onClick"],W={class:"project-details"},X={class:"form-group"},Z=["onUpdate:modelValue"],tt={class:"form-group"},et={class:"date-range-group"},st={class:"date-picker-wrapper"},ot=["onUpdate:modelValue","onClick"],nt={key:0,class:"date-picker-dropdown"},lt={class:"date-picker-header"},at={class:"month-grid"},rt=["onClick"],it={class:"date-picker-wrapper"},dt=["onUpdate:modelValue","onClick"],ct={key:0,class:"date-picker-dropdown"},pt={class:"date-picker-header"},ut={class:"month-grid"},mt=["onClick"],_t=["onClick"],vt={class:"form-group"},ft={key:1,class:"empty-state"},bt={class:"form-actions"},kt=["disabled"],ht={key:0,class:"limit-hint"},yt={__name:"ProjectForm",props:{data:{type:Object,default:()=>({projects:[]})}},emits:["update"],setup(P,{emit:I}){const w=P,x=I,o=B({projects:[]});let b=null;const p=V(""),c=V(new Date().getFullYear());E(()=>w.data,l=>{l&&l.projects?o.projects=[...l.projects]:o.projects=[]},{immediate:!0,deep:!0});const d=()=>{b&&clearTimeout(b),b=setTimeout(()=>{x("update",{projects:[...o.projects]})},300)},F=()=>{o.projects.length>=8||(o.projects.push({name:"",role:"",company:"",startDate:"",endDate:"",isOngoing:!1,description:""}),d())},g=l=>{p.value=l;const[s,a]=l.split("_"),n=a==="start"?o.projects[s].startDate:o.projects[s].endDate;if(n&&n!=="至今"){const e=parseInt(n.split("-")[0]);e&&(c.value=e)}},v=l=>{c.value+=l},j=(l,s,a)=>{const n=`${s}-${String(a).padStart(2,"0")}`,[e,u]=l.split("_");u==="start"?o.projects[e].startDate=n:(o.projects[e].endDate=n,o.projects[e].isOngoing=!1),p.value="",d()},O=l=>{const[s]=l.split("_");o.projects[s].endDate="至今",o.projects[s].isOngoing=!0,p.value="",d()},C=(l,s)=>{const[a,n]=l.split("_"),e=n==="start"?o.projects[a].startDate:o.projects[a].endDate;if(!e||e==="至今")return!1;const[u,T]=e.split("-");return parseInt(u)===c.value&&parseInt(T)===s},$=l=>{l.target.closest(".date-picker-wrapper")||(p.value="")};M(()=>{document.addEventListener("click",$)}),L(()=>{document.removeEventListener("click",$)});const S=l=>{o.projects.splice(l,1),d()};return(l,s)=>(i(),r("div",R,[s[12]||(s[12]=t("div",{class:"form-header"},[t("h4",{class:"form-title"},"项目经验"),t("p",{class:"form-desc"},"添加您参与的重要项目经验")],-1)),o.projects&&o.projects.length>0?(i(),r("div",q,[(i(!0),r(k,null,h(o.projects,(a,n)=>(i(),r("div",{key:n,class:"project-item"},[t("div",A,[t("div",G,[s[4]||(s[4]=t("label",{class:"form-label"},"项目名称",-1)),m(t("input",{"onUpdate:modelValue":e=>a.name=e,type:"text",class:"form-input",placeholder:"如：智能社区服务系统",onInput:d},null,40,H),[[_,a.name]])]),t("div",J,[s[5]||(s[5]=t("label",{class:"form-label"},"担任角色",-1)),m(t("input",{"onUpdate:modelValue":e=>a.role=e,type:"text",class:"form-input",placeholder:"如：项目经理",onInput:d},null,40,K),[[_,a.role]])]),t("button",{class:"delete-btn",onClick:e=>S(n),title:"删除项目经验"}," × ",8,Q)]),t("div",W,[t("div",X,[s[6]||(s[6]=t("label",{class:"form-label"},"所在公司",-1)),m(t("input",{"onUpdate:modelValue":e=>a.company=e,type:"text",class:"form-input",placeholder:"可选，方便面试官对照经历",onInput:d},null,40,Z),[[_,a.company]])]),t("div",tt,[s[8]||(s[8]=t("label",{class:"form-label"},"项目时间",-1)),t("div",et,[t("div",st,[m(t("input",{"onUpdate:modelValue":e=>a.startDate=e,type:"text",class:"date-input",placeholder:"2023-01",readonly:"",onClick:e=>g(`${n}_start`)},null,8,ot),[[_,a.startDate]]),p.value===`${n}_start`?(i(),r("div",nt,[t("div",lt,[t("button",{type:"button",onClick:s[0]||(s[0]=e=>v(-1))},"‹"),t("span",null,f(c.value),1),t("button",{type:"button",onClick:s[1]||(s[1]=e=>v(1))},"›")]),t("div",at,[(i(),r(k,null,h(12,e=>t("button",{key:e,type:"button",class:D(["month-btn",{active:C(`${n}_start`,e)}]),onClick:u=>j(`${n}_start`,c.value,e)},f(e)+"月 ",11,rt)),64))])])):y("",!0)]),s[7]||(s[7]=t("span",{class:"date-separator"},"至",-1)),t("div",it,[m(t("input",{"onUpdate:modelValue":e=>a.endDate=e,type:"text",class:"date-input",placeholder:"2024-06 或 至今",readonly:"",onClick:e=>g(`${n}_end`)},null,8,dt),[[_,a.endDate]]),p.value===`${n}_end`?(i(),r("div",ct,[t("div",pt,[t("button",{type:"button",onClick:s[2]||(s[2]=e=>v(-1))},"‹"),t("span",null,f(c.value),1),t("button",{type:"button",onClick:s[3]||(s[3]=e=>v(1))},"›")]),t("div",ut,[(i(),r(k,null,h(12,e=>t("button",{key:e,type:"button",class:D(["month-btn",{active:C(`${n}_end`,e)}]),onClick:u=>j(`${n}_end`,c.value,e)},f(e)+"月 ",11,mt)),64)),t("button",{type:"button",class:"month-btn present-btn",onClick:e=>O(`${n}_end`)}," 至今 ",8,_t)])])):y("",!0)])])]),t("div",vt,[s[9]||(s[9]=t("label",{class:"form-label"},"项目描述",-1)),U(Y,{modelValue:a.description,"onUpdate:modelValue":[e=>a.description=e,d],placeholder:"请输入项目详情、技术栈、负责模块、项目成果等...","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])])])]))),128))])):(i(),r("div",ft,s[10]||(s[10]=[t("p",null,"暂无项目经验信息",-1),t("p",{class:"empty-hint"},"点击下方按钮添加项目经验",-1)]))),t("div",bt,[t("button",{class:"add-btn",onClick:F,disabled:o.projects.length>=8},[U(N,{name:"plus",size:"sm"}),s[11]||(s[11]=t("span",null,"添加项目经验",-1))],8,kt),o.projects.length>=8?(i(),r("p",ht," 最多可添加8个项目经验 ")):y("",!0)])]))}},Vt=z(yt,[["__scopeId","data-v-35272fbf"]]);export{Vt as P};
