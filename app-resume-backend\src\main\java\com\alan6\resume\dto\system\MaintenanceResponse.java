package com.alan6.resume.dto.system;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 维护模式响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
public class MaintenanceResponse {

    /**
     * 是否启用维护模式
     */
    private Boolean enabled;

    /**
     * 维护消息
     */
    private String message;

    /**
     * 预计结束时间
     */
    private LocalDateTime estimatedEndTime;

    /**
     * 允许访问的IP列表
     */
    private String allowedIps;

    /**
     * 维护开始时间
     */
    private LocalDateTime startTime;

    /**
     * 维护结束时间
     */
    private LocalDateTime endTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
} 