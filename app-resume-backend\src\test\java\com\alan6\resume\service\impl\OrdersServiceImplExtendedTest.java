package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.order.OrderDetailResponse;
import com.alan6.resume.dto.order.OrderListResponse;
import com.alan6.resume.entity.Orders;
import com.alan6.resume.mapper.OrdersMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OrdersServiceImpl扩展方法测试类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@ExtendWith(MockitoExtension.class)
public class OrdersServiceImplExtendedTest {

    @Mock
    private OrdersMapper ordersMapper;

    @InjectMocks
    private OrdersServiceImpl ordersService;

    private Orders testOrder1;
    private Orders testOrder2;
    private List<Orders> testOrderList;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testOrder1 = new Orders();
        testOrder1.setId(1L);
        testOrder1.setOrderNo("ORD20241222123456789");
        testOrder1.setUserId(1001L);
        testOrder1.setOrderType((byte) 1);
        testOrder1.setProductId(2L);
        testOrder1.setProductName("年度会员");
        testOrder1.setOriginalAmount(new BigDecimal("299.90"));
        testOrder1.setDiscountAmount(BigDecimal.ZERO);
        testOrder1.setFinalAmount(new BigDecimal("199.90"));
        testOrder1.setPaymentMethod((byte) 1);
        testOrder1.setPaymentPlatform("wechat");
        testOrder1.setOrderStatus((byte) 2);
        testOrder1.setCreateTime(LocalDateTime.now().minusDays(1));
        testOrder1.setUpdateTime(LocalDateTime.now().minusDays(1));
        testOrder1.setPayTime(LocalDateTime.now().minusDays(1).plusMinutes(10));
        testOrder1.setCompleteTime(LocalDateTime.now().minusDays(1).plusMinutes(15));

        testOrder2 = new Orders();
        testOrder2.setId(2L);
        testOrder2.setOrderNo("ORD20241221123456789");
        testOrder2.setUserId(1001L);
        testOrder2.setOrderType((byte) 1);
        testOrder2.setProductId(1L);
        testOrder2.setProductName("月度会员");
        testOrder2.setOriginalAmount(new BigDecimal("29.90"));
        testOrder2.setDiscountAmount(BigDecimal.ZERO);
        testOrder2.setFinalAmount(new BigDecimal("29.90"));
        testOrder2.setPaymentMethod((byte) 2);
        testOrder2.setPaymentPlatform("alipay");
        testOrder2.setOrderStatus((byte) 1);
        testOrder2.setCreateTime(LocalDateTime.now().minusHours(2));
        testOrder2.setUpdateTime(LocalDateTime.now().minusHours(2));

        testOrderList = Arrays.asList(testOrder1, testOrder2);
    }

    @Test
    void testGetUserOrderList_Success() {
        // 准备Mock数据
        Page<Orders> mockPage = new Page<>(1, 10);
        mockPage.setRecords(testOrderList);
        mockPage.setTotal(2L);
        mockPage.setPages(1L);

        when(ordersMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        OrderListResponse response = ordersService.getUserOrderList(1001L, 1, 10, null, null, null, null, null);

        // 验证结果
        assertNotNull(response);
        assertEquals(2L, response.getTotal());
        assertEquals(1, response.getPage());
        assertEquals(10, response.getSize());
        assertEquals(1, response.getPages());
        assertNotNull(response.getOrders());
        assertEquals(2, response.getOrders().size());

        // 验证第一个订单项
        OrderListResponse.OrderItem order1 = response.getOrders().get(0);
        assertEquals("ORD20241222123456789", order1.getOrderNo());
        assertEquals("membership", order1.getOrderType());
        assertEquals("年度会员", order1.getProductName());
        assertEquals(new BigDecimal("199.90"), order1.getAmount());
        assertEquals("CNY", order1.getCurrency());
        assertEquals("paid", order1.getStatus());
        assertEquals("wechat", order1.getPaymentMethod());

        // 验证Mock调用
        verify(ordersMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetUserOrderList_WithFilters() {
        // 准备Mock数据 - 只返回符合筛选条件的订单
        Page<Orders> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(testOrder1));
        mockPage.setTotal(1L);
        mockPage.setPages(1L);

        when(ordersMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试 - 筛选已支付的微信订单
        OrderListResponse response = ordersService.getUserOrderList(1001L, 1, 10, (byte) 2, (byte) 1, null, null, (byte) 1);

        // 验证结果
        assertNotNull(response);
        assertEquals(1L, response.getTotal());
        assertEquals(1, response.getOrders().size());

        OrderListResponse.OrderItem order = response.getOrders().get(0);
        assertEquals("ORD20241222123456789", order.getOrderNo());
        assertEquals("paid", order.getStatus());
        assertEquals("wechat", order.getPaymentMethod());

        // 验证Mock调用
        verify(ordersMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetUserOrderList_InvalidUserId() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.getUserOrderList(null, 1, 10, null, null, null, null, null);
        });

        assertEquals("用户ID不能为空", exception.getMessage());
    }

    @Test
    void testGetUserOrderList_DefaultPageParams() {
        // 准备Mock数据
        Page<Orders> mockPage = new Page<>(1, 10);
        mockPage.setRecords(testOrderList);
        mockPage.setTotal(2L);

        when(ordersMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试 - 传入无效的页码和大小参数
        OrderListResponse response = ordersService.getUserOrderList(1001L, 0, 200, null, null, null, null, null);

        // 验证结果 - 应该使用默认值
        assertNotNull(response);
        assertEquals(1, response.getPage()); // 默认页码为1
        assertEquals(10, response.getSize()); // 默认大小为10

        // 验证Mock调用
        verify(ordersMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetOrderDetail_Success() {
        // 准备Mock数据
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder1);

        // 执行测试
        OrderDetailResponse response = ordersService.getOrderDetail("ORD20241222123456789", 1001L);

        // 验证结果
        assertNotNull(response);
        assertEquals("ORD20241222123456789", response.getOrderNo());
        assertEquals("membership", response.getOrderType());
        assertEquals(new BigDecimal("199.90"), response.getAmount());
        assertEquals("CNY", response.getCurrency());
        assertEquals("paid", response.getStatus());
        assertNotNull(response.getCreateTime());
        assertNotNull(response.getExpireTime());

        // 验证产品信息
        assertNotNull(response.getProductInfo());
        assertEquals("2", response.getProductInfo().getProductId());
        assertEquals("年度会员", response.getProductInfo().getProductName());
        assertEquals(new BigDecimal("299.90"), response.getProductInfo().getOriginalPrice());
        assertEquals(new BigDecimal("199.90"), response.getProductInfo().getDiscountPrice());

        // 验证支付信息
        assertNotNull(response.getPaymentInfo());
        assertEquals("wechat", response.getPaymentInfo().getPaymentMethod());
        assertEquals("paid", response.getPaymentInfo().getPaymentStatus());
        assertNotNull(response.getPaymentInfo().getPaymentTime());

        // 验证用户信息
        assertNotNull(response.getUserInfo());
        assertEquals("U1001", response.getUserInfo().getUserId());

        // 验证时间线
        assertNotNull(response.getTimeline());
        assertTrue(response.getTimeline().size() >= 2); // 至少包含创建和支付

        // 验证Mock调用
        verify(ordersMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetOrderDetail_OrderNotFound() {
        // 准备Mock数据
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.getOrderDetail("ORD20241222123456789", 1001L);
        });

        assertEquals(4001, exception.getErrorCode());
        assertEquals("订单不存在", exception.getMessage());
    }

    @Test
    void testGetOrderDetail_NoPermission() {
        // 准备Mock数据 - 订单属于其他用户
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder1);

        // 执行测试并验证异常 - 用户ID不匹配
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.getOrderDetail("ORD20241222123456789", 2002L);
        });

        assertEquals("无权限查看此订单", exception.getMessage());
    }

    @Test
    void testGetOrderDetail_EmptyOrderNo() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.getOrderDetail("", 1001L);
        });

        assertEquals("订单号不能为空", exception.getMessage());
    }

    @Test
    void testCancelOrder_Success() {
        // 准备Mock数据 - 待支付状态的订单
        testOrder2.setOrderStatus((byte) 1); // 待支付
        testOrder2.setCreateTime(LocalDateTime.now().minusMinutes(10)); // 10分钟前创建，未超时

        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder2);
        when(ordersMapper.updateById(any(Orders.class))).thenReturn(1);

        // 执行测试
        Boolean result = ordersService.cancelOrder("ORD20241221123456789", 1001L, "用户主动取消", "USER_CANCEL");

        // 验证结果
        assertTrue(result);

        // 验证Mock调用
        verify(ordersMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(ordersMapper).updateById(any(Orders.class));
    }

    @Test
    void testCancelOrder_OrderNotFound() {
        // 准备Mock数据
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.cancelOrder("ORD20241221123456789", 1001L, "用户主动取消", "USER_CANCEL");
        });

        assertEquals(4001, exception.getErrorCode());
        assertEquals("订单不存在", exception.getMessage());
    }

    @Test
    void testCancelOrder_NoPermission() {
        // 准备Mock数据
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder2);

        // 执行测试并验证异常 - 用户ID不匹配
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.cancelOrder("ORD20241221123456789", 2002L, "用户主动取消", "USER_CANCEL");
        });

        assertEquals("无权限操作此订单", exception.getMessage());
    }

    @Test
    void testCancelOrder_InvalidStatus() {
        // 准备Mock数据 - 已支付状态的订单
        testOrder1.setOrderStatus((byte) 2); // 已支付
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder1);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.cancelOrder("ORD20241222123456789", 1001L, "用户主动取消", "USER_CANCEL");
        });

        assertEquals(4012, exception.getErrorCode());
        assertEquals("订单状态不允许取消", exception.getMessage());
    }

    @Test
    void testCancelOrder_Timeout() {
        // 准备Mock数据 - 超时的待支付订单
        testOrder2.setOrderStatus((byte) 1); // 待支付
        testOrder2.setCreateTime(LocalDateTime.now().minusHours(2)); // 2小时前创建，已超时

        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder2);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.cancelOrder("ORD20241221123456789", 1001L, "用户主动取消", "USER_CANCEL");
        });

        assertEquals(4013, exception.getErrorCode());
        assertEquals("订单取消时间超期", exception.getMessage());
    }

    @Test
    void testCancelOrder_EmptyReason() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ordersService.cancelOrder("ORD20241221123456789", 1001L, "", "USER_CANCEL");
        });

        assertEquals("取消原因不能为空", exception.getMessage());
    }

    @Test
    void testConvertToOrderItem() {
        // 这是一个私有方法，我们通过公共方法间接测试
        // 准备Mock数据
        Page<Orders> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(testOrder1));
        mockPage.setTotal(1L);

        when(ordersMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        OrderListResponse response = ordersService.getUserOrderList(1001L, 1, 10, null, null, null, null, null);

        // 验证转换结果
        assertNotNull(response.getOrders());
        assertEquals(1, response.getOrders().size());

        OrderListResponse.OrderItem item = response.getOrders().get(0);
        assertEquals("ORD20241222123456789", item.getOrderNo());
        assertEquals("membership", item.getOrderType());
        assertEquals("年度会员", item.getProductName());
        assertEquals("CNY", item.getCurrency());
        assertEquals("paid", item.getStatus());
        assertEquals("paid", item.getPaymentStatus());
        assertEquals("wechat", item.getPaymentMethod());
        assertNotNull(item.getCreateTime());
        assertNotNull(item.getPaymentTime());
        assertNotNull(item.getExpireTime());
    }

    @Test
    void testBuildOrderTimeline_CompletedOrder() {
        // 这是一个私有方法，我们通过公共方法间接测试
        // 准备Mock数据 - 已完成的订单
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder1);

        // 执行测试
        OrderDetailResponse response = ordersService.getOrderDetail("ORD20241222123456789", 1001L);

        // 验证时间线
        assertNotNull(response.getTimeline());
        assertTrue(response.getTimeline().size() >= 3); // 创建、支付、完成

        // 验证时间线包含必要的状态
        List<String> statuses = response.getTimeline().stream()
                .map(OrderDetailResponse.Timeline::getStatus)
                .toList();
        assertTrue(statuses.contains("created"));
        assertTrue(statuses.contains("paid"));
        assertTrue(statuses.contains("completed"));
    }

    @Test
    void testBuildOrderTimeline_CancelledOrder() {
        // 准备Mock数据 - 已取消的订单
        testOrder2.setOrderStatus((byte) 4); // 已取消
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder2);

        // 执行测试
        OrderDetailResponse response = ordersService.getOrderDetail("ORD20241221123456789", 1001L);

        // 验证时间线包含取消状态
        List<String> statuses = response.getTimeline().stream()
                .map(OrderDetailResponse.Timeline::getStatus)
                .toList();
        assertTrue(statuses.contains("created"));
        assertTrue(statuses.contains("cancelled"));
    }
} 