<template>
  <div class="contact-item group">
    <!-- 图标 -->
    <div class="contact-icon">
      <component :is="iconComponent" class="icon" />
    </div>
    
    <!-- 内容 -->
    <span class="contact-value">{{ value }}</span>
    
    <!-- 联系信息只读显示 -->
  </div>
</template>

<script setup>
/**
 * 联系信息项组件
 * @description 显示单个联系信息，支持编辑
 */

// ================================
// 导入依赖
// ================================
import { computed } from 'vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /** 图标类型 */
  icon: {
    type: String,
    required: true,
    validator: (value) => ['phone', 'email', 'location'].includes(value)
  },
  
  /** 显示值 */
  value: {
    type: String,
    required: true
  },
  
  /** 是否可编辑 */
  isEditable: {
    type: Boolean,
    default: false
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['update'])

// ================================
// 计算属性
// ================================

/** 图标组件 */
const iconComponent = computed(() => {
  const iconMap = {
    phone: PhoneIcon,
    email: EmailIcon,
    location: LocationIcon
  }
  return iconMap[props.icon]
})

// ================================
// 方法
// ================================

// 联系信息编辑功能已移除，预览区只读显示

// ================================
// 图标组件
// ================================

/** 手机图标 */
const PhoneIcon = {
  template: `
    <svg viewBox="0 0 20 20" fill="currentColor">
      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
    </svg>
  `
}

/** 邮箱图标 */
const EmailIcon = {
  template: `
    <svg viewBox="0 0 20 20" fill="currentColor">
      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
    </svg>
  `
}

/** 位置图标 */
const LocationIcon = {
  template: `
    <svg viewBox="0 0 20 20" fill="currentColor">
      <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
    </svg>
  `
}
</script>

<style scoped>
/* ================================
 * 联系信息项
 * ================================ */
.contact-item {
  @apply flex items-center gap-2 text-white/90;
  @apply transition-all duration-200;
  @apply relative;
}

.contact-item.editable {
  @apply cursor-pointer;
}

.contact-item:hover {
  @apply text-white;
  transform: scale(1.05);
}

/* ================================
 * 图标
 * ================================ */
.contact-icon {
  @apply flex-shrink-0;
}

.icon {
  @apply w-4 h-4;
}

/* ================================
 * 内容
 * ================================ */
.contact-value {
  @apply text-sm font-medium;
  @apply min-w-0 flex-1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* ================================
 * 编辑提示
 * ================================ */
.edit-hint {
  @apply opacity-0 group-hover:opacity-100;
  @apply transition-opacity duration-200;
  @apply ml-1;
}

.edit-icon {
  @apply w-3 h-3 text-white/60;
}

/* ================================
 * 交互增强
 * ================================ */
.contact-item.editable:hover .contact-value {
  @apply underline decoration-white/50 underline-offset-2;
}

.contact-item.editable:active {
  transform: scale(0.95);
}

/* ================================
 * 动画
 * ================================ */
.contact-item {
  animation: fadeInLeft 0.6s ease-out;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style> 