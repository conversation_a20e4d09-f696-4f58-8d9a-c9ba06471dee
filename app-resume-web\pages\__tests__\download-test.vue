<template>
  <div class="download-test-page">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-center mb-2">下载功能测试</h1>
        <p class="text-gray-600 text-center mb-12">测试新的下载下拉菜单组件</p>
        
        <!-- 下载组件展示区 -->
        <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 class="text-2xl font-semibold mb-6">下载下拉菜单</h2>
          <div class="flex justify-center">
            <DownloadDropdown
              :is-exporting="isExporting"
              :export-status="exportStatus"
              @download="handleDownload"
            />
          </div>
        </div>

        <!-- 状态显示区 -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-semibold mb-4">当前状态</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-white rounded-lg p-4">
              <div class="text-sm text-gray-500">导出状态</div>
              <div class="text-lg font-medium" :class="getStatusColor()">{{ exportStatus }}</div>
            </div>
            <div class="bg-white rounded-lg p-4">
              <div class="text-sm text-gray-500">是否正在导出</div>
              <div class="text-lg font-medium">{{ isExporting ? '是' : '否' }}</div>
            </div>
          </div>
        </div>

        <!-- 快捷测试按钮 -->
        <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h3 class="text-lg font-semibold mb-4">快捷测试</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              @click="testDownload('pdf')"
              :disabled="isExporting"
              class="flex items-center justify-center px-6 py-4 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
            >
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v12H4V4z"/>
              </svg>
              测试PDF下载
            </button>
            <button
              @click="testDownload('word')"
              :disabled="isExporting"
              class="flex items-center justify-center px-6 py-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
            >
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v12H4V4z"/>
              </svg>
              测试Word下载
            </button>
            <button
              @click="testDownload('image')"
              :disabled="isExporting"
              class="flex items-center justify-center px-6 py-4 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
            >
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v12H4V4z"/>
              </svg>
              测试图片下载
            </button>
          </div>
        </div>

        <!-- 操作日志 -->
        <div class="bg-gray-900 rounded-xl p-6">
          <h3 class="text-lg font-semibold text-white mb-4">操作日志</h3>
          <div class="bg-black rounded-lg p-4 h-48 overflow-y-auto">
            <div v-if="logs.length === 0" class="text-gray-500 text-center py-8">
              暂无日志...
            </div>
            <div v-for="(log, index) in logs" :key="index" class="text-sm mb-1">
              <span class="text-gray-400">[{{ log.timestamp }}]</span>
              <span :class="log.type === 'error' ? 'text-red-400' : log.type === 'success' ? 'text-green-400' : 'text-blue-400'">
                {{ log.message }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * 下载功能测试页面
 * @description 用于测试下载下拉菜单组件的交互和样式
 */

// ================================
// 导入依赖
// ================================
import { ref } from 'vue'
import DownloadDropdown from '~/components/editor/layout/DownloadDropdown.vue'

// ================================
// 页面配置
// ================================
definePageMeta({
  layout: 'default'
})

useSeoMeta({
  title: '下载功能测试 - 火花简历',
  description: '测试简历下载功能'
})

// ================================
// 响应式数据
// ================================

// 导出状态
const isExporting = ref(false)
const exportStatus = ref('准备就绪')

// 操作日志
const logs = ref([])

// ================================
// 方法定义
// ================================

/**
 * 添加日志
 * @param {string} message - 日志消息
 * @param {string} type - 日志类型 (info/success/error)
 */
const addLog = (message, type = 'info') => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift({ timestamp, message, type })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value.pop()
  }
}

/**
 * 获取状态颜色类
 */
const getStatusColor = () => {
  if (exportStatus.value.includes('失败')) return 'text-red-500'
  if (exportStatus.value.includes('成功') || exportStatus.value.includes('完成')) return 'text-green-500'
  if (exportStatus.value.includes('正在')) return 'text-blue-500'
  return 'text-gray-500'
}

/**
 * 处理下载请求
 * @param {string} format - 下载格式
 */
const handleDownload = async (format) => {
  addLog(`用户选择下载格式: ${format.toUpperCase()}`, 'info')
  
  // 检查是否为演示模式
  const isDemoMode = true // 演示模式，不调用真实API
  
  if (isDemoMode) {
    // 演示模式：模拟下载过程
    try {
      isExporting.value = true
      exportStatus.value = `正在生成${format.toUpperCase()}文档...`
      
      addLog(`[演示模式] 开始生成${format.toUpperCase()}文档`, 'info')
      
      // 模拟网络请求延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 模拟随机成功/失败
      const success = Math.random() > 0.2 // 80% 成功率
      
      if (success) {
        exportStatus.value = '下载完成'
        addLog(`${format.toUpperCase()}文档生成成功`, 'success')
        
        // 模拟文件下载
        const fileName = `简历_测试_${format}.${format === 'image' ? 'png' : format === 'word' ? 'docx' : 'pdf'}`
        addLog(`[演示模式] 模拟下载文件: ${fileName}`, 'success')
        
        // 创建模拟下载
        const blob = new Blob(['这是一个模拟的' + format.toUpperCase() + '文件内容'], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        URL.revokeObjectURL(url)
        
      } else {
        throw new Error('模拟网络连接超时')
      }
      
    } catch (error) {
      exportStatus.value = '下载失败'
      addLog(`下载失败: ${error.message}`, 'error')
    } finally {
      // 延迟重置状态
      setTimeout(() => {
        isExporting.value = false
        exportStatus.value = '准备就绪'
      }, 1500)
    }
  } else {
    // 真实模式：调用实际的下载服务
    try {
      isExporting.value = true
      exportStatus.value = `正在生成${format.toUpperCase()}文档...`
      
      // 这里可以调用真实的下载服务
      // const downloadService = useDownloadService()
      // await downloadService.downloadResume({...})
      
      addLog(`真实API调用暂未实现`, 'error')
      
    } catch (error) {
      exportStatus.value = '下载失败'
      addLog(`下载失败: ${error.message}`, 'error')
    } finally {
      setTimeout(() => {
        isExporting.value = false
        exportStatus.value = '准备就绪'
      }, 1500)
    }
  }
}

/**
 * 测试下载
 * @param {string} format - 下载格式
 */
const testDownload = (format) => {
  handleDownload(format)
}

// ================================
// 生命周期
// ================================
onMounted(() => {
  addLog('下载功能测试页面已加载', 'success')
})
</script>

<style scoped>
.download-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 按钮悬停效果 */
button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:not(:disabled):active {
  transform: translateY(0);
}

/* 卡片悬停效果 */
.bg-white {
  transition: all 0.3s ease;
}

.bg-white:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
</style> 