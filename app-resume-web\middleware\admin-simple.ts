/**
 * 简化的管理员中间件
 * 只检查基本登录状态，权限检查由后端login-admin接口处理
 */
export default defineNuxtRouteMiddleware(async (to, from) => {
  // 如果是登录页面，直接通过
  if (to.path === '/admin/login') {
    return
  }

  console.log('🔍 检查管理员登录状态...')
  
  // 统一检查管理员Token（支持服务端和客户端）
  const adminToken = process.server 
    ? useCookie('admin_token').value 
    : localStorage.getItem('admin_token')
  
  if (!adminToken) {
    console.log('❌ 未找到管理员Token，重定向到登录页面')
    return navigateTo('/admin/login')
  }

  console.log('✅ 找到管理员Token，允许访问')
}) 