package com.alan6.resume.mapper;

import com.alan6.resume.entity.ResumeTemplateCategories;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 模板分类关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface ResumeTemplateCategoriesMapper extends BaseMapper<ResumeTemplateCategories> {

    /**
     * 根据模板ID获取分类ID列表
     */
    List<Long> getCategoryIdsByTemplateId(@Param("templateId") Long templateId);

    /**
     * 根据分类ID列表获取模板ID列表
     */
    List<Long> getTemplateIdsByCategoryIds(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 批量删除模板的分类关联
     */
    int deleteByTemplateId(@Param("templateId") Long templateId);

    /**
     * 批量插入模板分类关联
     */
    int batchInsert(@Param("relations") List<ResumeTemplateCategories> relations);
} 