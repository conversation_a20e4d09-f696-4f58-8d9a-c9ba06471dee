package com.alan6.resume.dto.template;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 模板预览响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板预览响应DTO")
public class TemplatePreviewResponse {

    /**
     * 模板ID
     */
    @Schema(description = "模板ID", example = "3001")
    private Long templateId;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", example = "简约商务模板")
    private String templateName;

    /**
     * 预览类型
     */
    @Schema(description = "预览类型", example = "image")
    private String previewType;

    /**
     * 预览URL
     */
    @Schema(description = "预览URL", example = "https://cdn.example.com/templates/3001/preview_800.jpg")
    private String previewUrl;

    /**
     * 多尺寸预览URL
     */
    @Schema(description = "多尺寸预览URL")
    private Map<String, String> previewUrls;

    /**
     * 预览图片尺寸
     */
    @Schema(description = "预览图片尺寸")
    private PreviewDimensions dimensions;

    /**
     * 生成时间
     */
    @Schema(description = "生成时间", example = "2024-12-01T10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generateTime;

    /**
     * 过期时间（秒）
     */
    @Schema(description = "过期时间（秒）", example = "3600")
    private Integer expiresIn;

    /**
     * 是否为临时文件
     */
    @Schema(description = "是否为临时文件", example = "false")
    private Boolean isTemporary = false;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）", example = "512000")
    private Long fileSize;

    /**
     * 预览图片尺寸内部类
     */
    @Data
    @Schema(description = "预览图片尺寸")
    public static class PreviewDimensions {
        
        /**
         * 宽度（像素）
         */
        @Schema(description = "宽度（像素）", example = "800")
        private Integer width;
        
        /**
         * 高度（像素）
         */
        @Schema(description = "高度（像素）", example = "1131")
        private Integer height;
        
        /**
         * 宽高比
         */
        @Schema(description = "宽高比", example = "0.707")
        private Double aspectRatio;

        /**
         * 构造函数
         */
        public PreviewDimensions() {}

        /**
         * 构造函数
         * 
         * @param width 宽度
         * @param height 高度
         */
        public PreviewDimensions(Integer width, Integer height) {
            this.width = width;
            this.height = height;
            this.aspectRatio = height != null && height > 0 ? (double) width / height : null;
        }
    }
} 