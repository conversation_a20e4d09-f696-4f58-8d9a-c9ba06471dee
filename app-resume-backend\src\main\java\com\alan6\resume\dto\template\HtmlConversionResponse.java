package com.alan6.resume.dto.template;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * HTML模板转换响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HtmlConversionResponse {
    
    /**
     * 转换是否成功
     */
    private Boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 转换结果列表
     */
    private List<ConversionResult> results;
    
    /**
     * 转换统计信息
     */
    private ConversionStatistics statistics;
    
    /**
     * 转换时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 转换会话ID
     */
    private String sessionId;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 单个文件转换结果
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ConversionResult {
        
        /**
         * 源HTML文件名
         */
        private String sourceFileName;
        
        /**
         * 目标Vue文件名
         */
        private String targetFileName;
        
        /**
         * 转换状态（SUCCESS, FAILED, SKIPPED）
         */
        private String status;
        
        /**
         * 转换消息
         */
        private String message;
        
        /**
         * Vue文件路径
         */
        private String vueFilePath;
        
        /**
         * 原HTML文件路径
         */
        private String htmlFilePath;
        
        /**
         * 文件大小（字节）
         */
        private Long fileSize;
        
        /**
         * 转换耗时（毫秒）
         */
        private Long processingTime;
        
        /**
         * 验证结果
         */
        private HtmlValidationResult validationResult;
        
        /**
         * 转换详情
         */
        private ConversionDetails details;
        
        /**
         * 错误信息
         */
        private String error;
    }
    
    /**
     * 转换详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ConversionDetails {
        
        /**
         * 转换的模块数量
         */
        private Integer moduleCount;
        
        /**
         * 转换的字段数量
         */
        private Integer fieldCount;
        
        /**
         * 生成的Vue代码行数
         */
        private Integer vueCodeLines;
        
        /**
         * 生成的CSS代码行数
         */
        private Integer cssCodeLines;
        
        /**
         * 检测到的组件列表
         */
        private List<String> detectedComponents;
        
        /**
         * 转换的数据绑定
         */
        private Map<String, String> dataBindings;
        
        /**
         * 使用的Vue特性
         */
        private List<String> vueFeatures;
        
        /**
         * 样式处理信息
         */
        private StyleProcessingInfo styleInfo;
    }
    
    /**
     * 样式处理信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StyleProcessingInfo {
        
        /**
         * 原始CSS规则数量
         */
        private Integer originalRules;
        
        /**
         * 处理后CSS规则数量
         */
        private Integer processedRules;
        
        /**
         * 使用的CSS变量
         */
        private List<String> cssVariables;
        
        /**
         * 媒体查询数量
         */
        private Integer mediaQueries;
        
        /**
         * 是否包含响应式设计
         */
        private Boolean responsive;
    }
    
    /**
     * 转换统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ConversionStatistics {
        
        /**
         * 总文件数量
         */
        private Integer totalFiles;
        
        /**
         * 成功转换数量
         */
        private Integer successCount;
        
        /**
         * 失败转换数量
         */
        private Integer failedCount;
        
        /**
         * 跳过转换数量
         */
        private Integer skippedCount;
        
        /**
         * 总转换时间（毫秒）
         */
        private Long totalProcessingTime;
        
        /**
         * 平均转换时间（毫秒）
         */
        private Long averageProcessingTime;
        
        /**
         * 转换成功率
         */
        private Double successRate;
        
        /**
         * 总代码行数
         */
        private Integer totalCodeLines;
        
        /**
         * 总文件大小（字节）
         */
        private Long totalFileSize;
    }
    
    /**
     * 创建成功响应
     * 
     * @param results 转换结果列表
     * @param statistics 统计信息
     * @param sessionId 会话ID
     * @return 成功响应
     */
    public static HtmlConversionResponse success(List<ConversionResult> results, 
                                               ConversionStatistics statistics, 
                                               String sessionId) {
        return HtmlConversionResponse.builder()
                .success(true)
                .message("转换完成")
                .results(results)
                .statistics(statistics)
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建失败响应
     * 
     * @param error 错误信息
     * @param sessionId 会话ID
     * @return 失败响应
     */
    public static HtmlConversionResponse failure(String error, String sessionId) {
        return HtmlConversionResponse.builder()
                .success(false)
                .message("转换失败")
                .error(error)
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建部分成功响应
     * 
     * @param results 转换结果列表
     * @param statistics 统计信息
     * @param sessionId 会话ID
     * @return 部分成功响应
     */
    public static HtmlConversionResponse partialSuccess(List<ConversionResult> results, 
                                                      ConversionStatistics statistics, 
                                                      String sessionId) {
        return HtmlConversionResponse.builder()
                .success(true)
                .message("部分转换成功")
                .results(results)
                .statistics(statistics)
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 判断是否全部成功
     * 
     * @return 是否全部成功
     */
    public boolean isAllSuccess() {
        return success && statistics != null && statistics.getFailedCount() == 0;
    }
    
    /**
     * 判断是否有失败
     * 
     * @return 是否有失败
     */
    public boolean hasFailures() {
        return statistics != null && statistics.getFailedCount() > 0;
    }
    
    /**
     * 获取成功率
     * 
     * @return 成功率
     */
    public Double getSuccessRate() {
        return statistics != null ? statistics.getSuccessRate() : 0.0;
    }
} 