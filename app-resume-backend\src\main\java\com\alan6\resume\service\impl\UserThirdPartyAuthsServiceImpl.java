package com.alan6.resume.service.impl;

import com.alan6.resume.entity.UserThirdPartyAuths;
import com.alan6.resume.mapper.UserThirdPartyAuthsMapper;
import com.alan6.resume.service.IUserThirdPartyAuthsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户第三方授权表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class UserThirdPartyAuthsServiceImpl extends ServiceImpl<UserThirdPartyAuthsMapper, UserThirdPartyAuths> implements IUserThirdPartyAuthsService {

}
