<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标准简历模板</title>
    <style>
        /* === CSS变量系统 - 用户可配置的样式参数 === */
        :root {
            /* 字体设置 */
            --font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-size: 14px;
            --line-height: 1.6;
            
            /* 颜色设置 */
            --text-color: #333333;
            --primary-color: #3498db;
            --secondary-color: #666666;
            
            /* 边距设置 */
            --margin-top: 60px;
            --margin-bottom: 60px;
            --margin-side: 60px;
            
            /* 间距设置 */
            --module-spacing: 24px;
            --item-spacing: 16px;
            --paragraph-spacing: 12px;
            
            /* 内边距设置 */
            --module-padding: 16px;
            --item-padding: 12px;
        }

        /* === 基础布局样式 === */
        .resume-template {
            padding: var(--margin-top) var(--margin-side) var(--margin-bottom);
            font-family: var(--font-family);
            font-size: var(--font-size);
            line-height: var(--line-height);
            color: var(--text-color);
        }

        /* === 基本信息模块样式 === */
        .basic-info-section {
            text-align: center;
            margin-bottom: var(--module-spacing);
            padding: var(--module-padding);
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto var(--item-spacing);
            object-fit: cover;
        }

        .name {
            font-size: calc(var(--font-size) + 6px);
            font-weight: 700;
            margin-bottom: var(--paragraph-spacing);
            color: var(--text-color);
        }

        .expected-position {
            font-size: calc(var(--font-size) + 2px);
            color: var(--primary-color);
            margin-bottom: var(--item-spacing);
            font-weight: 500;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--paragraph-spacing);
            text-align: left;
            margin-bottom: var(--item-spacing);
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: var(--paragraph-spacing);
            padding: var(--item-padding);
        }

        .contact-label {
            font-weight: 600;
            color: var(--secondary-color);
            min-width: 80px;
        }

        .contact-value {
            color: var(--text-color);
        }

        /* === 通用模块样式 === */
        /* === 通用模块样式 === */
        .section {
            margin-bottom: var(--module-spacing);
        }

        .section-title {
            font-size: calc(var(--font-size) + 4px);
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: var(--item-spacing);
        }

        /* === 列表项通用样式 === */
        .list-item {
            margin-bottom: var(--item-spacing);
            padding: var(--item-padding);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--paragraph-spacing);
            flex-wrap: wrap;
            gap: var(--paragraph-spacing);
        }

        .item-title {
            font-weight: 600;
            color: var(--text-color);
            font-size: calc(var(--font-size) + 1px);
        }

        .item-subtitle {
            color: var(--secondary-color);
            font-weight: 500;
            margin-top: 4px;
        }

        .item-date {
            color: var(--secondary-color);
            font-size: var(--font-size);
            font-weight: 500;
        }

        .item-content {
            color: var(--text-color);
            line-height: var(--line-height);
        }

        /* === 技能模块样式 === */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--item-spacing);
        }

        .skill-item {
            padding: var(--item-padding);
            text-align: center;
        }

        .skill-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--paragraph-spacing);
        }

        .skill-level {
            color: var(--primary-color);
            font-size: var(--font-size);
            font-weight: 500;
        }

        .skill-progress {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin-top: var(--paragraph-spacing);
            overflow: hidden;
        }

        .skill-progress-bar {
            height: 100%;
            background: var(--primary-color);
            border-radius: 4px;
        }

        /* === 简单列表样式 === */
        .simple-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--paragraph-spacing);
        }

        .simple-item {
            padding: var(--item-padding);
        }

        /* === 文本内容样式 === */
        .text-content {
            padding: var(--item-padding);
            line-height: var(--line-height);
        }

        /* === 响应式设计 === */
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
            }

            .item-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }

            .simple-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="resume-template" data-module-root="true">
        
        <!-- 1. 基本信息模块 (必需) -->
        <section class="basic-info-section" data-module="basic_info">
            <!-- 头像：可选显示 -->
            <img class="avatar" data-field="photo" data-vue-src="resumeData.basic_info.photo" 
                 data-vue-if="resumeData.basic_info.photo"
                 src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjhGOUZBIi8+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNDAiIHI9IjIwIiBmaWxsPSIjNkM3NTdEIi8+CjxwYXRoIGQ9Ik0yMCA5MEM0MCA3MCA4MCA3MCAxMDAgOTBWMTIwSDIwVjkwWiIgZmlsbD0iIzZDNzU3RCIvPgo8L3N2Zz4=" alt="头像" />
            
            <!-- 核心信息：始终显示 -->
            <h1 class="name" data-field="name" data-vue-text="resumeData.basic_info.name">张三</h1>
            <div class="expected-position" data-field="expectedPosition" data-vue-text="resumeData.basic_info.expectedPosition">软件工程师</div>
            
            <div class="contact-grid">
                <!-- 核心联系方式：始终显示 -->
                <div class="contact-item">
                    <span class="contact-label">电话：</span>
                    <span class="contact-value" data-field="phone" data-vue-text="resumeData.basic_info.phone">138-0000-0000</span>
                </div>
                <div class="contact-item">
                    <span class="contact-label">邮箱：</span>
                    <span class="contact-value" data-field="email" data-vue-text="resumeData.basic_info.email"><EMAIL></span>
                </div>
                
                <!-- 可选基本信息：有数据才显示 -->
                <div class="contact-item" data-vue-if="resumeData.basic_info.age">
                    <span class="contact-label">年龄：</span>
                    <span class="contact-value" data-field="age" data-vue-text="resumeData.basic_info.age">28岁</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.currentCity">
                    <span class="contact-label">现居：</span>
                    <span class="contact-value" data-field="currentCity" data-vue-text="resumeData.basic_info.currentCity">北京市</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.intendedCity">
                    <span class="contact-label">意向：</span>
                    <span class="contact-value" data-field="intendedCity" data-vue-text="resumeData.basic_info.intendedCity">上海市</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.address">
                    <span class="contact-label">地址：</span>
                    <span class="contact-value" data-field="address" data-vue-text="resumeData.basic_info.address">北京市朝阳区</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.currentStatus">
                    <span class="contact-label">状态：</span>
                    <span class="contact-value" data-field="currentStatus" data-vue-text="resumeData.basic_info.currentStatus">在职寻新</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.expectedSalary">
                    <span class="contact-label">薪资：</span>
                    <span class="contact-value" data-field="expectedSalary" data-vue-text="resumeData.basic_info.expectedSalary">15-20K</span>
                </div>
                
                <!-- 社交信息：有数据才显示 -->
                <div class="contact-item" data-vue-if="resumeData.basic_info.wechat">
                    <span class="contact-label">微信：</span>
                    <span class="contact-value" data-field="wechat" data-vue-text="resumeData.basic_info.wechat">zhangsan_wx</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.website">
                    <span class="contact-label">网站：</span>
                    <span class="contact-value" data-field="website" data-vue-text="resumeData.basic_info.website">https://zhangsan.dev</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.github">
                    <span class="contact-label">GitHub：</span>
                    <span class="contact-value" data-field="github" data-vue-text="resumeData.basic_info.github">https://github.com/zhangsan</span>
                </div>
                
                <!-- 详细个人信息：有数据才显示 -->
                <div class="contact-item" data-vue-if="resumeData.basic_info.gender">
                    <span class="contact-label">性别：</span>
                    <span class="contact-value" data-field="gender" data-vue-text="resumeData.basic_info.gender">男</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.height">
                    <span class="contact-label">身高：</span>
                    <span class="contact-value" data-field="height" data-vue-text="resumeData.basic_info.height">175cm</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.weight">
                    <span class="contact-label">体重：</span>
                    <span class="contact-value" data-field="weight" data-vue-text="resumeData.basic_info.weight">70kg</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.politicalStatus">
                    <span class="contact-label">政治面貌：</span>
                    <span class="contact-value" data-field="politicalStatus" data-vue-text="resumeData.basic_info.politicalStatus">群众</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.maritalStatus">
                    <span class="contact-label">婚姻状况：</span>
                    <span class="contact-value" data-field="maritalStatus" data-vue-text="resumeData.basic_info.maritalStatus">未婚</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.hometown">
                    <span class="contact-label">籍贯：</span>
                    <span class="contact-value" data-field="hometown" data-vue-text="resumeData.basic_info.hometown">山东济南</span>
                </div>
                <div class="contact-item" data-vue-if="resumeData.basic_info.ethnicity">
                    <span class="contact-label">民族：</span>
                    <span class="contact-value" data-field="ethnicity" data-vue-text="resumeData.basic_info.ethnicity">汉族</span>
                </div>
                
                <!-- 自定义社交信息：有数据才显示 -->
                <div class="contact-item" data-vue-for="social in resumeData.basic_info.customSocials" data-vue-key="social.id"
                     data-vue-if="resumeData.basic_info.customSocials && resumeData.basic_info.customSocials.length > 0">
                    <span class="contact-label" data-field="label" data-vue-text="social.label">LinkedIn：</span>
                    <span class="contact-value" data-field="value" data-vue-text="social.value">linkedin.com/in/zhangsan</span>
                </div>
                
                <!-- 自定义信息：有数据才显示 -->
                <div class="contact-item" data-vue-for="info in resumeData.basic_info.customInfos" data-vue-key="info.id"
                     data-vue-if="resumeData.basic_info.customInfos && resumeData.basic_info.customInfos.length > 0">
                    <span class="contact-label" data-field="label" data-vue-text="info.label">驾照：</span>
                    <span class="contact-value" data-field="value" data-vue-text="info.value">C1</span>
                </div>
            </div>
        </section>

        <!-- 2. 工作经历模块 -->
        <section class="section" data-module="work_experience" data-vue-if="resumeData.work_experience && resumeData.work_experience.length > 0">
            <h2 class="section-title">工作经历</h2>
            <div class="list-item" data-vue-for="item in resumeData.work_experience" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <!-- 核心字段：始终显示 -->
                        <div class="item-title" data-field="company" data-vue-text="item.company">ABC科技有限公司</div>
                        <div class="item-subtitle">
                            <span data-field="position" data-vue-text="item.position">高级前端工程师</span>
                            <!-- 可选字段：有数据才显示 -->
                            <span data-vue-if="item.department"> · </span>
                            <span data-field="department" data-vue-text="item.department" data-vue-if="item.department">技术部</span>
                        </div>
                    </div>
                    <div class="item-date">
                        <span data-field="startDate" data-vue-text="item.startDate" data-vue-if="item.startDate">2020-03</span>
                        <span data-vue-if="item.startDate && item.endDate"> ~ </span>
                        <span data-field="endDate" data-vue-text="item.endDate" data-vue-if="item.endDate">2024-01</span>
                    </div>
                </div>
                <!-- 工作描述：可选字段 -->
                <div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description">
                    负责公司核心产品的前端开发工作，使用Vue.js、React等技术栈构建高质量的用户界面。
                    参与产品需求分析、技术方案设计、代码开发、测试和上线等全流程工作。
                    优化页面性能，提升用户体验，并负责团队技术培训和代码评审工作。
                </div>
            </div>
        </section>

        <!-- 3. 教育经历模块 -->
        <section class="section" data-module="education" data-vue-if="resumeData.education && resumeData.education.length > 0">
            <h2 class="section-title">教育经历</h2>
            <div class="list-item" data-vue-for="item in resumeData.education" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <!-- 核心字段：始终显示 -->
                        <div class="item-title" data-field="school" data-vue-text="item.school">北京理工大学</div>
                        <div class="item-subtitle">
                            <span data-field="major" data-vue-text="item.major">计算机科学与技术</span>
                            <!-- 可选字段：有数据才显示 -->
                            <span data-vue-if="item.degree"> · </span>
                            <span data-field="degree" data-vue-text="item.degree" data-vue-if="item.degree">本科</span>
                            <span data-vue-if="item.customDegree"> · </span>
                            <span data-field="customDegree" data-vue-text="item.customDegree" data-vue-if="item.customDegree">工学学士</span>
                            <span data-vue-if="item.gpa"> · GPA: </span>
                            <span data-field="gpa" data-vue-text="item.gpa" data-vue-if="item.gpa">3.8</span>
                        </div>
                    </div>
                    <div class="item-date">
                        <span data-field="startDate" data-vue-text="item.startDate" data-vue-if="item.startDate">2016-09</span>
                        <span data-vue-if="item.startDate && item.endDate"> ~ </span>
                        <span data-field="endDate" data-vue-text="item.endDate" data-vue-if="item.endDate">2020-06</span>
                    </div>
                </div>
                <!-- 教育描述：可选字段 -->
                <div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description">
                    主修计算机科学核心课程，包括数据结构与算法、操作系统、计算机网络、数据库系统等。
                    参与多个课程项目和实践活动，培养了扎实的编程基础和系统设计能力。
                </div>
            </div>
        </section>

        <!-- 4. 项目经历模块 -->
        <section class="section" data-module="project" data-vue-if="resumeData.project && resumeData.project.length > 0">
            <h2 class="section-title">项目经历</h2>
            <div class="list-item" data-vue-for="item in resumeData.project" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <!-- 核心字段：始终显示 -->
                        <div class="item-title" data-field="name" data-vue-text="item.name">电商管理后台系统</div>
                        <div class="item-subtitle">
                            <!-- 可选字段：有数据才显示 -->
                            <span data-field="role" data-vue-text="item.role" data-vue-if="item.role">技术负责人</span>
                            <span data-vue-if="item.role && item.company"> · </span>
                            <span data-field="company" data-vue-text="item.company" data-vue-if="item.company">ABC科技有限公司</span>
                        </div>
                    </div>
                    <div class="item-date">
                        <span data-field="startDate" data-vue-text="item.startDate" data-vue-if="item.startDate">2022-06</span>
                        <span data-vue-if="item.startDate && item.endDate"> ~ </span>
                        <span data-field="endDate" data-vue-text="item.endDate" data-vue-if="item.endDate">2023-12</span>
                    </div>
                </div>
                <!-- 项目描述：可选字段 -->
                <div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description">
                    负责电商管理后台系统的前端架构设计与开发，采用Vue3 + TypeScript + Element Plus技术栈。
                    实现了商品管理、订单处理、用户管理、数据统计等核心功能模块。
                    通过组件化开发和代码分割优化，显著提升了系统性能和开发效率。
                </div>
            </div>
        </section>

        <!-- 5. 技能特长模块 -->
        <section class="section" data-module="skills" data-vue-if="resumeData.skills && resumeData.skills.length > 0">
            <h2 class="section-title">技能特长</h2>
            <div class="skills-grid">
                <div class="skill-item" data-vue-for="skill in resumeData.skills" data-vue-key="skill.id">
                    <!-- 核心字段：始终显示 -->
                    <div class="skill-name" data-field="name" data-vue-text="skill.name">JavaScript</div>
                    <!-- 可选字段：有数据才显示 -->
                    <div class="skill-level" data-field="level" data-vue-text="skill.level" data-vue-if="skill.level">90%</div>
                    <div class="skill-proficiency" data-field="proficiency" data-vue-text="skill.proficiency" data-vue-if="skill.proficiency">精通</div>
                    <div class="skill-progress" data-vue-if="skill.displayMode && skill.level">
                        <div class="skill-progress-bar" data-field="displayMode" :style="{ width: skill.displayMode === 'percentage' ? skill.level + '%' : '0%' }"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 6. 获奖情况模块 -->
        <section class="section" data-module="award" data-vue-if="resumeData.award && resumeData.award.length > 0">
            <h2 class="section-title">获奖情况</h2>
            <div class="list-item" data-vue-for="item in resumeData.award" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <!-- 核心字段：始终显示 -->
                        <div class="item-title" data-field="name" data-vue-text="item.name">优秀员工奖</div>
                        <div class="item-subtitle">
                            <span data-field="organization" data-vue-text="item.organization">ABC科技有限公司</span>
                            <span data-vue-if="item.level"> · </span>
                            <span data-field="level" data-vue-text="item.level">公司级</span>
                        </div>
                    </div>
                    <div class="item-date" data-field="date" data-vue-text="item.date">2023-12</div>
                </div>
                <div class="item-content" data-field="description" data-vue-text="item.description">
                    因在项目开发中表现突出，技术能力强，团队协作能力佳，获得公司年度优秀员工奖励。
                </div>
            </div>
        </section>

        <!-- 7. 证书资质模块 -->
        <section class="section" data-module="certificate" data-vue-if="resumeData.certificate && resumeData.certificate.length > 0">
            <h2 class="section-title">证书资质</h2>
            <div class="list-item" data-vue-for="item in resumeData.certificate" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <div class="item-title" data-field="name" data-vue-text="item.name">软件设计师</div>
                        <div class="item-subtitle">
                            <span data-field="organization" data-vue-text="item.organization">中国计算机技术职业资格网</span>
                            <span data-vue-if="item.number"> · 证书编号: </span>
                            <span data-field="number" data-vue-text="item.number">SD202312345</span>
                        </div>
                    </div>
                    <div class="item-date" data-field="date" data-vue-text="item.date">2023-05</div>
                </div>
                <div class="item-content" data-field="description" data-vue-text="item.description">
                    通过国家软件设计师资格考试，具备软件系统分析、设计和开发的专业能力。
                </div>
            </div>
        </section>

        <!-- 8. 语言能力模块 -->
        <section class="section" data-module="language" data-vue-if="resumeData.language && resumeData.language.length > 0">
            <h2 class="section-title">语言能力</h2>
            <div class="simple-list">
                <div class="simple-item" data-vue-for="item in resumeData.language" data-vue-key="item.id">
                    <!-- 核心字段：始终显示 -->
                    <div class="item-title" data-field="name" data-vue-text="item.name">英语</div>
                    <div class="item-subtitle">
                        <!-- 可选字段：有数据才显示 -->
                        <span data-field="level" data-vue-text="item.level" data-vue-if="item.level">熟练</span>
                        <span data-vue-if="item.level && item.certificate"> · </span>
                        <span data-field="certificate" data-vue-text="item.certificate" data-vue-if="item.certificate">CET-6</span>
                    </div>
                    <!-- 语言描述：可选字段 -->
                    <div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description">
                        具备良好的英语读写能力，能够阅读英文技术文档，与外国同事进行日常交流。
                    </div>
                </div>
            </div>
        </section>

        <!-- 9. 实习经历模块 -->
        <section class="section" data-module="internship" data-vue-if="resumeData.internship && resumeData.internship.length > 0">
            <h2 class="section-title">实习经历</h2>
            <div class="list-item" data-vue-for="item in resumeData.internship" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <!-- 核心字段：始终显示 -->
                        <div class="item-title" data-field="company" data-vue-text="item.company">腾讯科技有限公司</div>
                        <div class="item-subtitle" data-field="position" data-vue-text="item.position">前端开发实习生</div>
                    </div>
                    <div class="item-date">
                        <!-- 可选字段：有数据才显示 -->
                        <span data-field="startDate" data-vue-text="item.startDate" data-vue-if="item.startDate">2019-07</span>
                        <span data-vue-if="item.startDate && item.endDate"> ~ </span>
                        <span data-field="endDate" data-vue-text="item.endDate" data-vue-if="item.endDate">2019-09</span>
                    </div>
                </div>
                <!-- 实习描述：可选字段 -->
                <div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description">
                    在微信事业群实习，参与微信小程序开发工具的前端开发工作。
                    学习并实践了React技术栈，参与了代码重构和性能优化项目。
                </div>
            </div>
        </section>

        <!-- 10. 志愿经历模块 -->
        <section class="section" data-module="volunteer_experience" data-vue-if="resumeData.volunteer_experience && resumeData.volunteer_experience.length > 0">
            <h2 class="section-title">志愿经历</h2>
            <div class="list-item" data-vue-for="item in resumeData.volunteer_experience" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <div class="item-title" data-field="organization" data-vue-text="item.organization">北京市红十字会</div>
                        <div class="item-subtitle">
                            <span data-field="role" data-vue-text="item.role">志愿者</span>
                            <span data-vue-if="item.location"> · </span>
                            <span data-field="location" data-vue-text="item.location">北京市朝阳区</span>
                        </div>
                    </div>
                    <div class="item-date">
                        <span data-field="startDate" data-vue-text="item.startDate">2018-06</span> ~ 
                        <span data-field="endDate" data-vue-text="item.endDate">2020-06</span>
                    </div>
                </div>
                <div class="item-content" data-field="description" data-vue-text="item.description">
                    参与社区志愿服务活动，为老人提供技术支持，教授智能手机和电脑的基本使用方法。
                    累计志愿服务时长超过100小时，获得优秀志愿者称号。
                </div>
            </div>
        </section>

        <!-- 11. 论文发表模块 -->
        <section class="section" data-module="publication" data-vue-if="resumeData.publication && resumeData.publication.length > 0">
            <h2 class="section-title">论文发表</h2>
            <div class="list-item" data-vue-for="item in resumeData.publication" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <div class="item-title" data-field="title" data-vue-text="item.title">基于深度学习的图像识别算法研究</div>
                        <div class="item-subtitle">
                            <span data-field="type" data-vue-text="item.type">学术论文</span> · 
                            <span data-field="journal" data-vue-text="item.journal">《计算机工程》</span>
                        </div>
                    </div>
                    <div class="item-date" data-field="date" data-vue-text="item.date">2020-03</div>
                </div>
                <div class="item-content" data-field="description" data-vue-text="item.description">
                    第一作者发表学术论文，研究基于卷积神经网络的图像分类算法，
                    在CIFAR-10数据集上取得了较好的实验结果。
                </div>
            </div>
        </section>

        <!-- 12. 科研经历模块 -->
        <section class="section" data-module="research_experience" data-vue-if="resumeData.research_experience && resumeData.research_experience.length > 0">
            <h2 class="section-title">科研经历</h2>
            <div class="list-item" data-vue-for="item in resumeData.research_experience" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <div class="item-title" data-field="topic" data-vue-text="item.topic">机器学习在图像处理中的应用</div>
                        <div class="item-subtitle">
                            <span data-field="role" data-vue-text="item.role">主要研究员</span> · 
                            <span data-field="organization" data-vue-text="item.organization">北京理工大学计算机学院</span>
                        </div>
                    </div>
                    <div class="item-date">
                        <span data-field="startDate" data-vue-text="item.startDate">2019-03</span> ~ 
                        <span data-field="endDate" data-vue-text="item.endDate">2020-06</span>
                    </div>
                </div>
                <div class="item-content" data-field="description" data-vue-text="item.description">
                    参与导师的科研项目，研究深度学习算法在图像识别和处理中的应用。
                    负责算法实现、实验设计和数据分析，取得了较好的研究成果。
                </div>
            </div>
        </section>

        <!-- 13. 培训经历模块 -->
        <section class="section" data-module="training" data-vue-if="resumeData.training && resumeData.training.length > 0">
            <h2 class="section-title">培训经历</h2>
            <div class="list-item" data-vue-for="item in resumeData.training" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <div class="item-title" data-field="name" data-vue-text="item.name">React高级开发培训</div>
                        <div class="item-subtitle" data-field="organization" data-vue-text="item.organization">极客时间</div>
                    </div>
                    <div class="item-date">
                        <span data-field="startDate" data-vue-text="item.startDate">2021-06</span> ~ 
                        <span data-field="endDate" data-vue-text="item.endDate">2021-08</span>
                    </div>
                </div>
                <div class="item-content" data-field="description" data-vue-text="item.description">
                    系统学习React高级特性，包括Hooks、Context、性能优化等内容。
                    完成了多个实战项目，提升了React开发能力。
                </div>
            </div>
        </section>

        <!-- 14. 作品集模块 -->
        <section class="section" data-module="portfolio" data-vue-if="resumeData.portfolio && resumeData.portfolio.length > 0">
            <h2 class="section-title">作品集</h2>
            <div class="list-item" data-vue-for="item in resumeData.portfolio" data-vue-key="item.id">
                <div class="item-header">
                    <div>
                        <div class="item-title" data-field="title" data-vue-text="item.title">个人博客网站</div>
                        <div class="item-subtitle" data-field="type" data-vue-text="item.type">网站开发</div>
                    </div>
                    <div class="item-date" data-field="date" data-vue-text="item.date">2023-01</div>
                </div>
                <div class="item-content">
                    <p data-field="description" data-vue-text="item.description">
                        使用Nuxt.js开发的个人博客网站，支持文章发布、评论、搜索等功能。
                        采用响应式设计，支持多终端访问。
                    </p>
                    <p data-vue-if="item.link">
                        <strong>链接：</strong>
                        <a href="#" data-field="link" data-vue-text="item.link" data-vue-href="item.link">https://zhangsan.blog</a>
                    </p>
                </div>
            </div>
        </section>

        <!-- 15. 兴趣爱好模块 -->
        <section class="section" data-module="hobbies" data-vue-if="resumeData.hobbies && resumeData.hobbies.length > 0">
            <h2 class="section-title">兴趣爱好</h2>
            <div class="simple-list">
                <div class="simple-item" data-vue-for="hobby in resumeData.hobbies" data-vue-key="hobby.id">
                    <div class="item-title" data-field="name" data-vue-text="hobby.name">摄影</div>
                    <div class="item-content" data-field="description" data-vue-text="hobby.description">
                        热爱摄影，擅长风景和人像摄影，具有较好的审美能力和创意思维。
                    </div>
                </div>
            </div>
        </section>

        <!-- 16. 自我评价模块 -->
        <section class="section" data-module="self_evaluation" data-vue-if="resumeData.self_evaluation && resumeData.self_evaluation.content">
            <h2 class="section-title">自我评价</h2>
            <div class="text-content" data-field="content" data-vue-text="resumeData.self_evaluation.content">
                本人性格开朗，工作认真负责，具有较强的学习能力和团队协作精神。
                在前端开发领域有着扎实的技术基础和丰富的项目经验，
                能够快速适应新技术和新环境，具备良好的问题解决能力。
                希望能够在新的工作岗位上发挥自己的专业技能，与团队共同成长。
            </div>
        </section>

        <!-- 17. 自荐信模块 -->
        <section class="section" data-module="cover_letter" data-vue-if="resumeData.cover_letter && resumeData.cover_letter.content">
            <h2 class="section-title">自荐信</h2>
            <div class="text-content" data-field="content" data-vue-text="resumeData.cover_letter.content">
                尊敬的招聘负责人，您好！我是一名有着4年前端开发经验的工程师，
                在看到贵公司的招聘信息后，我对这个职位非常感兴趣。
                我具备扎实的技术基础和丰富的项目经验，相信能够为贵公司的发展贡献自己的力量。
                期待能有机会与您面谈，详细介绍我的技能和经验。谢谢！
            </div>
        </section>

        <!-- 18. 自定义模块 -->
        <section class="section" data-module="custom_1704067200000" data-vue-if="customModule && customModule.content">
            <h2 class="section-title" data-field="title" data-vue-text="customModule.title">其他信息</h2>
            <div class="text-content" data-field="content" data-vue-text="customModule.content">
                这里可以添加任何自定义的内容，比如特殊经历、补充说明等。
                自定义模块的ID格式为 custom_ + 时间戳，确保唯一性。
            </div>
        </section>

    </div>
</body>
</html>