<template>
  <div class="download-dropdown-container">
    <!-- 下载按钮 -->
    <button
      ref="downloadButton"
      class="download-btn"
      :class="{ 'is-active': isOpen }"
      @click="toggleDropdown"
      @blur="handleBlur"
      title="下载简历"
    >
      <Icon name="download" />
      <span class="btn-text">下载</span>
      <Icon 
        name="chevron-down" 
        class="dropdown-icon"
        :class="{ 'is-rotated': isOpen }"
      />
    </button>

    <!-- 下拉菜单 -->
    <Transition name="dropdown">
      <div
        v-if="isOpen"
        ref="dropdownMenu"
        class="dropdown-menu"
        @click="handleMenuClick"
      >
        <div class="dropdown-content">
          <!-- PDF选项 -->
          <button
            class="dropdown-item"
            :disabled="isExporting"
            @click="handleDownload('pdf')"
          >
            <div class="item-icon pdf-icon">
              <Icon name="file-pdf" />
            </div>
            <div class="item-content">
              <div class="item-title">PDF文档</div>
              <div class="item-description">适合打印和正式投递</div>
            </div>
            <div class="item-badge">推荐</div>
          </button>

          <!-- Word选项 -->
          <button
            class="dropdown-item"
            :disabled="isExporting"
            @click="handleDownload('word')"
          >
            <div class="item-icon word-icon">
              <Icon name="file-word" />
            </div>
            <div class="item-content">
              <div class="item-title">Word文档</div>
              <div class="item-description">方便后续编辑修改</div>
            </div>
          </button>

          <!-- 图片选项 -->
          <button
            class="dropdown-item"
            :disabled="isExporting"
            @click="handleDownload('image')"
          >
            <div class="item-icon image-icon">
              <Icon name="file-image" />
            </div>
            <div class="item-content">
              <div class="item-title">图片格式</div>
              <div class="item-description">PNG格式，适合在线分享</div>
            </div>
          </button>

          <!-- 分割线 -->
          <div class="dropdown-divider"></div>

          <!-- 邮箱发送选项 -->
          <button
            class="dropdown-item"
            :disabled="isExporting"
            @click="handleSendEmail"
          >
            <div class="item-icon email-icon">
              <Icon name="email" />
            </div>
            <div class="item-content">
              <div class="item-title">发送至邮箱</div>
              <div class="item-description">通过邮件分享简历</div>
            </div>
          </button>
        </div>

        <!-- 导出状态提示 -->
        <div v-if="isExporting" class="export-status">
          <div class="status-icon">
            <Icon name="loading" class="animate-spin" />
          </div>
          <div class="status-text">{{ exportStatus }}</div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
/**
 * 下载下拉菜单组件
 * @description 提供PDF、Word、图片三种格式的下载选项，现代化扁平设计
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 是否正在导出
   */
  isExporting: {
    type: Boolean,
    default: false
  },
  
  /**
   * 导出状态文本
   */
  exportStatus: {
    type: String,
    default: '正在导出...'
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits([
  'download', // 下载事件，参数为格式类型
  'send-email' // 发送邮箱事件
])

// ================================
// 响应式数据
// ================================

// 下拉菜单状态
const isOpen = ref(false)

// DOM引用
const downloadButton = ref(null)
const dropdownMenu = ref(null)

// ================================
// 方法定义
// ================================

/**
 * 切换下拉菜单显示状态
 */
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

/**
 * 处理按钮失焦事件
 * @param {Event} event - 失焦事件
 */
const handleBlur = (event) => {
  // 延迟关闭，确保点击菜单项时不会立即关闭
  setTimeout(() => {
    if (!dropdownMenu.value?.contains(event.relatedTarget)) {
      isOpen.value = false
    }
  }, 150)
}

/**
 * 处理菜单点击事件
 * @param {Event} event - 点击事件
 */
const handleMenuClick = (event) => {
  // 阻止事件冒泡，防止菜单关闭
  event.stopPropagation()
}

/**
 * 处理下载请求
 * @param {string} format - 下载格式 (pdf/word/image)
 */
const handleDownload = (format) => {
  // 触发下载事件
  emit('download', format)
  
  // 关闭下拉菜单
  isOpen.value = false
}

/**
 * 处理邮箱发送请求
 */
const handleSendEmail = () => {
  // 触发发送邮箱事件
  emit('send-email')
  
  // 关闭下拉菜单
  isOpen.value = false
}

/**
 * 处理全局点击事件，用于关闭下拉菜单
 * @param {Event} event - 点击事件
 */
const handleGlobalClick = (event) => {
  if (!downloadButton.value?.contains(event.target) && 
      !dropdownMenu.value?.contains(event.target)) {
    isOpen.value = false
  }
}

// ================================
// 生命周期
// ================================
onMounted(() => {
  // 监听全局点击事件
  document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
  // 移除全局点击事件监听
  document.removeEventListener('click', handleGlobalClick)
})
</script>

<style scoped>
/**
 * 下载下拉菜单样式
 * 采用现代化扁平设计，注重交互体验
 */

/* ================================ */
/* 容器样式 */
/* ================================ */
.download-dropdown-container {
  position: relative;
  display: inline-block;
}

/* ================================ */
/* 下载按钮样式 */
/* ================================ */
.download-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.download-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

.download-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.download-btn.is-active {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.download-btn .btn-text {
  font-size: 14px;
  font-weight: 500;
}

.download-btn .dropdown-icon {
  width: 14px;
  height: 14px;
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.download-btn .dropdown-icon.is-rotated {
  transform: rotate(180deg);
}

/* ================================ */
/* 下拉菜单样式 */
/* ================================ */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 280px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.08);
  z-index: 1000;
  overflow: hidden;
}

.dropdown-content {
  padding: 8px;
}

/* ================================ */
/* 下拉菜单项样式 */
/* ================================ */
.dropdown-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  position: relative;
}

.dropdown-item:hover {
  background: rgba(102, 126, 234, 0.06);
  transform: translateY(-1px);
}

.dropdown-item:active {
  transform: translateY(0);
}

.dropdown-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.dropdown-item:disabled:hover {
  background: transparent;
}

/* ================================ */
/* 分割线样式 */
/* ================================ */
.dropdown-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 6px 12px;
}

/* ================================ */
/* 菜单项图标样式 */
/* ================================ */
.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
  flex-shrink: 0;
}

.pdf-icon {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.word-icon {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
}

.image-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #667eea;
}

.email-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #ed8936;
}

.item-icon :deep(svg) {
  width: 20px;
  height: 20px;
}

/* ================================ */
/* 菜单项内容样式 */
/* ================================ */
.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 2px;
}

.item-description {
  font-size: 12px;
  color: #718096;
  line-height: 1.4;
}

/* ================================ */
/* 推荐标签样式 */
/* ================================ */
.item-badge {
  position: absolute;
  top: 8px;
  right: 12px;
  padding: 2px 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 10px;
  font-weight: 600;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ================================ */
/* 导出状态样式 */
/* ================================ */
.export-status {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(102, 126, 234, 0.04);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  margin-top: 8px;
}

.status-icon {
  margin-right: 12px;
  color: #667eea;
}

.status-icon :deep(svg) {
  width: 16px;
  height: 16px;
}

.status-text {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

/* ================================ */
/* 动画效果 */
/* ================================ */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-enter-from {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

/* ================================ */
/* 响应式设计 */
/* ================================ */
@media (max-width: 768px) {
  .dropdown-menu {
    min-width: 260px;
    right: -20px;
  }
  
  .dropdown-item {
    padding: 10px 12px;
  }
  
  .item-icon {
    width: 36px;
    height: 36px;
    margin-right: 10px;
  }
  
  .item-icon :deep(svg) {
    width: 18px;
    height: 18px;
  }
}

/* ================================ */
/* 动画辅助类 */
/* ================================ */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 