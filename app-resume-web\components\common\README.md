# 通用组件说明

## RichTextEditor 富文本编辑器

### 功能特性

- ✅ **文本格式化**：粗体、斜体、下划线
- ✅ **超链接**：支持添加和编辑链接
- ✅ **列表**：无序列表、有序列表
- ✅ **对齐方式**：左对齐、居中对齐、右对齐
- ✅ **清除样式**：一键清除所有格式
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **Vue 3 兼容**：使用 Composition API

### 使用方法

#### 基本用法

```vue
<template>
  <RichTextEditor
    v-model="content"
    placeholder="请输入内容..."
    min-height="120px"
  />
</template>

<script setup>
import { ref } from 'vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const content = ref('')
</script>
```

#### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | String | `''` | 编辑器内容（HTML格式） |
| `placeholder` | String | `'请输入内容...'` | 占位符文本 |
| `minHeight` | String | `'100px'` | 编辑器最小高度 |

#### 事件说明

| 事件 | 参数 | 说明 |
|------|------|------|
| `update:modelValue` | `content: string` | 内容变化时触发 |
| `focus` | - | 获得焦点时触发 |
| `blur` | - | 失去焦点时触发 |

### 工具栏功能

#### 文本格式
- **粗体 (B)**：使用 `<strong>` 标签
- **斜体 (I)**：使用 `<em>` 标签  
- **下划线 (U)**：使用 `<u>` 标签

#### 链接
- **超链接 (🔗)**：选中文本后点击，可添加链接

#### 列表
- **无序列表 (•)**：使用 `<ul>` 和 `<li>` 标签
- **有序列表 (1.)**：使用 `<ol>` 和 `<li>` 标签

#### 对齐
- **左对齐 (⬅)**：`text-align: left`
- **居中对齐 (⬌)**：`text-align: center`
- **右对齐 (➡)**：`text-align: right`

#### 清除
- **清除样式 (🗑)**：移除所有格式，保留纯文本

### 数据格式

编辑器输出标准的HTML格式，示例：

```html
<strong>粗体文本</strong>
<em>斜体文本</em>
<u>下划线文本</u>
<a href="https://example.com">链接文本</a>

<ul>
<li>无序列表项1</li>
<li>无序列表项2</li>
</ul>

<ol>
<li>有序列表项1</li>
<li>有序列表项2</li>
</ol>

<div style="text-align: center;">居中文本</div>
```

### 样式定制

编辑器提供了完整的CSS样式，可以通过修改CSS变量来定制外观：

```css
.rich-text-editor {
  --editor-border-color: #e5e7eb;
  --editor-bg-color: white;
  --toolbar-bg-color: #f9fafb;
  --button-hover-color: #e5e7eb;
  --button-active-color: #3b82f6;
}
```

### 在其他模块中使用

该组件已经在教育经历模块中集成，其他模块也可以类似使用：

```vue
<!-- 工作经历模块 -->
<RichTextEditor
  v-model="experience.description"
  placeholder="请输入工作职责和成就..."
  min-height="150px"
/>

<!-- 项目经历模块 -->
<RichTextEditor
  v-model="project.description"
  placeholder="请输入项目详情..."
  min-height="120px"
/>

<!-- 自我评价模块 -->
<RichTextEditor
  v-model="selfEvaluation.content"
  placeholder="请输入自我评价..."
  min-height="100px"
/>
```

### 注意事项

1. **XSS安全**：输出的HTML内容使用 `v-html` 渲染，请确保内容来源可信
2. **浏览器兼容**：使用 `document.execCommand` API，现代浏览器支持良好
3. **性能优化**：大量文本编辑时，建议添加防抖处理
4. **移动端**：在移动设备上工具栏会自动换行适配

### 测试页面

访问 `/test/rich-text-editor-test` 可以查看组件的完整功能演示。 