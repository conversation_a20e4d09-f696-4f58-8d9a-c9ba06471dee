package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.dto.template.TemplateListRequest;
import com.alan6.resume.dto.template.TemplateListResponse;
import com.alan6.resume.dto.template.TemplateCategoryResponse;
import com.alan6.resume.security.UserPrincipal;
import com.alan6.resume.service.IUserRolesService;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.ITemplateCategoriesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 * 后台管理-系统管理 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Slf4j
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@Tag(name = "后台管理-系统管理", description = "后台管理-系统管理相关接口")
public class AdminController {

    private final IUserRolesService userRolesService;
    private final IResumeTemplatesService resumeTemplatesService;
    private final ITemplateCategoriesService templateCategoriesService;

    /**
     * 检查管理员权限
     */
    @GetMapping("/check-auth")
    @Operation(summary = "检查管理员权限", description = "验证当前用户是否具有管理员权限")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Map<String, Object>> checkAuth(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return R.fail("用户未登录");
            }

            boolean isAdmin = userRolesService.isAdmin(userPrincipal.getUserId());
            if (!isAdmin) {
                return R.fail("权限不足，需要管理员权限");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userPrincipal.getUserId());
            result.put("username", userPrincipal.getUsername());
            result.put("nickname", userPrincipal.getNickname());
            result.put("isAdmin", true);
            result.put("roles", userPrincipal.getRoles());
            result.put("permissions", userPrincipal.getPermissions());

            log.info("管理员权限验证成功，用户ID: {}", userPrincipal.getUserId());
            return R.ok(result);

        } catch (Exception e) {
            log.error("检查管理员权限失败", e);
            return R.fail("权限验证失败");
        }
    }

    /**
     * 获取管理员信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取管理员信息", description = "获取当前登录管理员的详细信息")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Map<String, Object>> getAdminInfo(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return R.fail("用户未登录");
            }

            Map<String, Object> adminInfo = new HashMap<>();
            adminInfo.put("userId", userPrincipal.getUserId());
            adminInfo.put("username", userPrincipal.getUsername());
            adminInfo.put("nickname", userPrincipal.getNickname());
            adminInfo.put("email", userPrincipal.getEmail());
            adminInfo.put("phone", userPrincipal.getPhone());
            adminInfo.put("avatarUrl", userPrincipal.getAvatarUrl());
            adminInfo.put("roles", userPrincipal.getRoles());
            adminInfo.put("permissions", userPrincipal.getPermissions());

            return R.ok(adminInfo);

        } catch (Exception e) {
            log.error("获取管理员信息失败", e);
            return R.fail("获取管理员信息失败");
        }
    }
    
    /**
     * 生成占位符图片
     * @param width 图片宽度
     * @param height 图片高度
     * @return 占位符图片
     */
    @GetMapping("/placeholder/{width}/{height}")
    public ResponseEntity<byte[]> generatePlaceholder(
            @PathVariable int width, 
            @PathVariable int height) {
        
        try {
            // 生成简单的SVG占位符
            String svg = String.format(
                "<svg width=\"%d\" height=\"%d\" xmlns=\"http://www.w3.org/2000/svg\">" +
                "<rect width=\"%d\" height=\"%d\" fill=\"#f5f5f5\"/>" +
                "<text x=\"50%%\" y=\"50%%\" font-family=\"Arial\" font-size=\"14\" fill=\"#999999\" text-anchor=\"middle\" dy=\".3em\">%dx%d</text>" +
                "</svg>",
                width, height, width, height, width, height
            );
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.valueOf("image/svg+xml"));
            headers.setCacheControl("public, max-age=3600");
            
            return new ResponseEntity<>(svg.getBytes(), headers, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("生成占位符图片失败: {}x{}", width, height, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 