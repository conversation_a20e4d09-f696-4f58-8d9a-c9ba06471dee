package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 账号合并记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("user_merge_records")
@Schema(name = "UserMergeRecords对象", description = "账号合并记录表")
public class UserMergeRecords implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "合并记录ID")
    private Long id;

    @Schema(description = "主账号ID（保留的账号）")
    private Long masterUserId;

    @Schema(description = "从账号ID（被合并的账号）")
    private Long slaveUserId;

    @Schema(description = "合并类型（1:微信绑定手机号,2:手机号绑定微信,3:管理员操作）")
    private Byte mergeType;

    @Schema(description = "合并原因")
    private String mergeReason;

    @Schema(description = "操作员ID（如果是管理员操作）")
    private Long operatorId;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
