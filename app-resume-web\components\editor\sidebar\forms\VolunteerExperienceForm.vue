<template>
  <div class="volunteer-experience-form">
    <div class="form-header">
      <h4 class="form-title">志愿服务</h4>
      <p class="form-desc">添加您参与的志愿服务活动和公益经历</p>
    </div>
    
    <div v-if="formData.volunteerExperience && formData.volunteerExperience.length > 0" class="volunteer-list">
      <div v-for="(volunteer, index) in formData.volunteerExperience" :key="index" class="volunteer-item">
        <div class="volunteer-header">
          <div class="form-group">
            <label class="form-label">组织名称</label>
            <input
              v-model="volunteer.organization"
              type="text"
              class="form-input"
              placeholder="如：蓝天救援志愿队"
              @input="handleVolunteerChange"
            />
          </div>
          <div class="form-group">
            <label class="form-label">服务角色</label>
            <input
              v-model="volunteer.role"
              type="text"
              class="form-input"
              placeholder="如：志愿者、队长"
              @input="handleVolunteerChange"
            />
          </div>
          <button 
            class="delete-btn"
            @click="removeVolunteerExperience(index)"
            title="删除志愿服务经历"
          >
            ×
          </button>
        </div>
        
        <div class="volunteer-details">
          <div class="form-group">
            <label class="form-label">服务地点</label>
            <input
              v-model="volunteer.location"
              type="text"
              class="form-input"
              placeholder="如：北京市朝阳区"
              @input="handleVolunteerChange"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">服务时间</label>
            <div class="date-range-group">
              <div class="date-picker-wrapper">
                <input
                  v-model="volunteer.startDate"
                  type="text"
                  class="date-input"
                  placeholder="2020-01"
                  readonly
                  @click="showDatePicker(`${index}_start`)"
                />
                <div 
                  v-if="activeDatePicker === `${index}_start`" 
                  class="date-picker-dropdown"
                >
                  <div class="date-picker-header">
                    <button type="button" @click="changeYear(-1)">‹</button>
                    <span>{{ currentYear }}</span>
                    <button type="button" @click="changeYear(1)">›</button>
                  </div>
                  <div class="month-grid">
                    <button
                      v-for="month in 12"
                      :key="month"
                      type="button"
                      class="month-btn"
                      :class="{ active: isSelectedMonth(`${index}_start`, month) }"
                      @click="selectDate(`${index}_start`, currentYear, month)"
                    >
                      {{ month }}月
                    </button>
                  </div>
                </div>
              </div>
              
              <span class="date-separator">至</span>
              
              <div class="date-picker-wrapper">
                <input
                  v-model="volunteer.endDate"
                  type="text"
                  class="date-input"
                  placeholder="2021-01 或 至今"
                  readonly
                  @click="showDatePicker(`${index}_end`)"
                />
                <div 
                  v-if="activeDatePicker === `${index}_end`" 
                  class="date-picker-dropdown"
                >
                  <div class="date-picker-header">
                    <button type="button" @click="changeYear(-1)">‹</button>
                    <span>{{ currentYear }}</span>
                    <button type="button" @click="changeYear(1)">›</button>
                  </div>
                  <div class="month-grid">
                    <button
                      v-for="month in 12"
                      :key="month"
                      type="button"
                      class="month-btn"
                      :class="{ active: isSelectedMonth(`${index}_end`, month) }"
                      @click="selectDate(`${index}_end`, currentYear, month)"
                    >
                      {{ month }}月
                    </button>
                    <button
                      type="button"
                      class="month-btn present-btn"
                      @click="selectPresent(`${index}_end`)"
                    >
                      至今
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">服务描述</label>
            <RichTextEditor
              v-model="volunteer.description"
              placeholder="请输入服务内容、参与活动、个人贡献等..."
              min-height="120px"
              @update:modelValue="handleVolunteerChange"
            />
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无志愿服务信息</p>
      <p class="empty-hint">点击下方按钮添加志愿服务经历</p>
    </div>
    
    <div class="form-actions">
      <button 
        class="add-btn" 
        @click="addVolunteerExperience"
        :disabled="formData.volunteerExperience.length >= 10"
      >
        <Icon name="plus" size="sm" />
        <span>添加志愿服务</span>
      </button>
      <p v-if="formData.volunteerExperience.length >= 10" class="limit-hint">
        最多可添加10个志愿服务经历
      </p>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, ref, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ volunteerExperience: [] })
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = reactive({
  volunteerExperience: []
})

// 防抖定时器
let debounceTimer = null

// 日期选择器状态
const activeDatePicker = ref('')
const currentYear = ref(new Date().getFullYear())

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.volunteerExperience) {
    formData.volunteerExperience = [...newData.volunteerExperience]
  } else {
    formData.volunteerExperience = []
  }
}, { immediate: true, deep: true })

/**
 * 处理志愿服务经历变化 - 实时更新
 */
const handleVolunteerChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的定时器，300ms后触发更新
  debounceTimer = setTimeout(() => {
    emit('update', { volunteerExperience: [...formData.volunteerExperience] })
  }, 300)
}

/**
 * 添加志愿服务经历
 */
const addVolunteerExperience = () => {
  if (formData.volunteerExperience.length >= 10) {
    return
  }
  
  formData.volunteerExperience.push({
    organization: '',
    role: '',
    location: '',
    startDate: '',
    endDate: '',
    description: ''
  })
  handleVolunteerChange()
}

/**
 * 删除志愿服务经历
 */
const removeVolunteerExperience = (index) => {
  formData.volunteerExperience.splice(index, 1)
  handleVolunteerChange()
}

/**
 * 显示日期选择器
 */
const showDatePicker = (pickerId) => {
  activeDatePicker.value = pickerId
  // 根据当前值设置年份
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.volunteerExperience[index].startDate 
    : formData.volunteerExperience[index].endDate
  
  if (currentValue && currentValue !== '至今') {
    const year = parseInt(currentValue.split('-')[0])
    if (year) {
      currentYear.value = year
    }
  }
}

/**
 * 改变年份
 */
const changeYear = (delta) => {
  currentYear.value += delta
}

/**
 * 选择日期
 */
const selectDate = (pickerId, year, month) => {
  const dateStr = `${year}-${String(month).padStart(2, '0')}`
  const [index, type] = pickerId.split('_')
  
  if (type === 'start') {
    formData.volunteerExperience[index].startDate = dateStr
  } else {
    formData.volunteerExperience[index].endDate = dateStr
  }
  
  activeDatePicker.value = ''
  handleVolunteerChange()
}

/**
 * 选择"至今"
 */
const selectPresent = (pickerId) => {
  const [index] = pickerId.split('_')
  formData.volunteerExperience[index].endDate = '至今'
  activeDatePicker.value = ''
  handleVolunteerChange()
}

/**
 * 判断是否为选中的月份
 */
const isSelectedMonth = (pickerId, month) => {
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.volunteerExperience[index].startDate 
    : formData.volunteerExperience[index].endDate
  
  if (!currentValue || currentValue === '至今') return false
  
  const [year, selectedMonth] = currentValue.split('-')
  return parseInt(year) === currentYear.value && parseInt(selectedMonth) === month
}

/**
 * 点击外部关闭日期选择器
 */
const handleClickOutside = (event) => {
  if (!event.target.closest('.date-picker-wrapper')) {
    activeDatePicker.value = ''
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.volunteer-experience-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.volunteer-list {
  @apply space-y-6 mb-6;
}

.volunteer-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-4;
}

.volunteer-header {
  @apply flex items-start gap-4;
}

.volunteer-details {
  @apply space-y-4;
}

.volunteer-details .form-group {
  @apply flex flex-col space-y-2;
}

.form-group {
  @apply flex-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.delete-btn {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold mt-6;
}

.date-range-group {
  @apply flex items-center gap-3;
}

.date-picker-wrapper {
  @apply relative flex-1;
}

.date-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer;
}

.date-separator {
  @apply text-sm text-gray-500 flex-shrink-0;
}

.date-picker-dropdown {
  @apply absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10;
  min-width: 240px;
}

.date-picker-header {
  @apply flex items-center justify-between px-4 py-3 border-b border-gray-200;
}

.date-picker-header button {
  @apply w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors duration-200;
}

.month-grid {
  @apply grid grid-cols-4 gap-2 p-3;
}

.month-btn {
  @apply px-3 py-2 text-sm text-gray-700 hover:bg-blue-100 hover:text-blue-800 rounded transition-colors duration-200;
  white-space: nowrap;
  min-width: 48px;
}

.month-btn.active {
  @apply bg-blue-500 text-white;
}

.month-btn.present-btn {
  @apply col-span-4 bg-green-100 text-green-800 hover:bg-green-200;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-xs text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}

.add-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.limit-hint {
  @apply text-xs text-gray-500 mt-2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .volunteer-header {
    @apply flex-col gap-3;
  }
  
  .date-range-group {
    @apply flex-col gap-2 items-stretch;
  }
  
  .date-separator {
    @apply text-center;
  }
}

/* 滚动条样式 */
.volunteer-experience-form::-webkit-scrollbar {
  @apply w-2;
}

.volunteer-experience-form::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

.volunteer-experience-form::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.volunteer-experience-form::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
</style> 