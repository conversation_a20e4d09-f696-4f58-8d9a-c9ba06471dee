# 简历预览组件架构说明

## 组件结构

```
ResumePreview.vue (主预览容器)
├── ResumeTemplate.vue (简历模板主组件)
    ├── ResumeHeader.vue (头部组件)
    │   └── ContactItem.vue (联系信息项)
    └── composables/
        ├── useResumeTemplateLogic.js (模板逻辑)
        └── useResumeAnimations.js (动画效果)
```

## 数据同步机制

### 1. 统一状态管理
所有组件都通过 `useEditorStore()` 共享同一份简历数据：

```javascript
// 全局状态
const editorStore = useEditorStore()
const resumeData = editorStore.resumeData.value
```

### 2. 事件传递链路

```
预览区操作 → ResumeTemplate → ResumePreview → EditorStore
                ↓
模块管理栏 ← EditorLayout ← EditorStore
                ↓  
编辑区域 ← ContentEditor ← EditorStore
```

### 3. 支持的交互操作

#### 模块级操作
- ✅ 模块上移/下移 (`moveModuleUp`, `moveModuleDown`)
- ✅ 模块删除 (`deleteModule`)
- ✅ 实时同步到模块管理栏

#### 技能模块操作
- ✅ 技能项上移/下移 (`moveSkillUp`, `moveSkillDown`)
- ✅ 技能项删除 (`deleteSkill`)
- ✅ 技能名称编辑 (`editSkillName`)
- ✅ 技能等级调节 (`updateSkillLevel`)
- ✅ 添加新技能 (`addSkill`)

#### 工作经历操作
- ✅ 经历项上移/下移 (`moveExperienceUp`, `moveExperienceDown`)
- ✅ 经历项删除 (`deleteExperience`)
- ✅ 职位名称编辑 (`editJobTitle`)
- ✅ 公司名称编辑 (`editCompanyName`)
- ✅ 工作描述编辑 (`editWorkDescription`)
- ✅ 添加新经历 (`addExperience`)

#### 基本信息操作
- ✅ 头像上传 (`handleAvatarClick`)
- ✅ 姓名编辑 (`handleNameEdit`)
- ✅ 职位编辑 (`handleJobTitleEdit`)
- ✅ 联系方式编辑 (`handleContactEdit`)

## 事件流程示例

### 技能排序操作
1. 用户在预览区点击技能的"上移"按钮
2. `ResumeTemplate.vue` 触发 `moveSkillUp(index)` 方法
3. 方法内部更新技能数组顺序，触发 `emit('skill-update', newSkills)`
4. `ResumePreview.vue` 接收事件，调用 `handleSkillUpdate(skills)`
5. `handleSkillUpdate` 调用 `editorStore.updateSkills(skills)`
6. EditorStore 更新全局状态
7. 所有监听该状态的组件自动重新渲染：
   - 模块管理栏的技能模块显示新顺序
   - 编辑区域的技能表单显示新顺序
   - 预览区域显示新顺序

### 模块删除操作
1. 用户在预览区点击模块的"删除"按钮
2. `ResumeTemplate.vue` 触发 `deleteModule(moduleType)` 方法
3. 确认对话框后，触发 `emit('module-delete', moduleType)`
4. `ResumePreview.vue` 接收事件，调用 `handleModuleDelete(moduleType)`
5. `handleModuleDelete` 调用 `editorStore.deleteModule(moduleType)`
6. EditorStore 更新全局状态，移除该模块
7. 所有相关组件自动更新：
   - 模块管理栏移除该模块
   - 编辑区域隐藏该模块的编辑表单
   - 预览区域不再显示该模块

## 技术特点

### 1. 现代化设计
- 扁平化设计风格
- 渐变色彩搭配
- 微妙的阴影和动画效果
- 响应式布局

### 2. 交互体验
- 悬浮显示操作按钮
- 平滑的过渡动画
- 直观的视觉反馈
- 确认对话框防止误操作

### 3. 代码质量
- TypeScript 类型检查
- 详细的 JSDoc 注释
- 符合阿里巴巴编码规范
- 组件化和可复用设计

### 4. 性能优化
- 计算属性缓存
- 事件委托
- 条件渲染
- 懒加载动画

## 使用示例

```vue
<template>
  <ResumePreview
    :resume-data="resumeData"
    :settings="styleSettings"
    @module-order-change="handleModuleOrderChange"
    @module-delete="handleModuleDelete"
  />
</template>

<script setup>
import { useEditorStore } from '~/composables/useEditorStore'

const editorStore = useEditorStore()

// 处理模块顺序变更
const handleModuleOrderChange = ({ moduleType, direction }) => {
  console.log(`模块 ${moduleType} ${direction > 0 ? '下移' : '上移'}`)
}

// 处理模块删除
const handleModuleDelete = (moduleType) => {
  console.log(`删除模块 ${moduleType}`)
}
</script>
```

## 扩展指南

### 添加新模块
1. 在 `ResumeTemplate.vue` 中添加新的模块 section
2. 实现对应的操作方法
3. 在 `useResumeTemplateLogic.js` 中添加模块逻辑
4. 在 EditorStore 中添加对应的状态管理

### 添加新的交互功能
1. 在模板中添加交互元素
2. 实现事件处理方法
3. 通过 emit 向上传递事件
4. 在 ResumePreview 中处理事件
5. 更新 EditorStore 状态

这样的架构确保了预览区的任何操作都能实时同步到模块管理栏和编辑区域，提供了完整的双向数据绑定体验。 