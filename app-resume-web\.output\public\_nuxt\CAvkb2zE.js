import{u as m}from"./CEpU8TOc.js";import{R as g}from"./D729gr2z.js";import{_ as v}from"./DlAUqK2U.js";import{r as f,i as D,c as k,a as e,t as n,h as l,b as _,o as S}from"./CURHyiUL.js";import"./x_rD_Ya3.js";import"./JeH0SXlh.js";const b={class:"preview-test-page"},h={class:"test-content"},w={class:"test-section"},y={class:"data-display"},M={class:"test-section"},x={class:"preview-container"},C={__name:"preview-test",setup(N){const o=m(),a=f({fontFamily:"system-ui",fontSize:"14px",textColor:"#333333",margins:{top:60,bottom:60,side:60},spacings:{module:24,item:16,line:1.6}});D(async()=>{console.log("🚀 初始化预览测试页面"),await o.createNewResume(),o.updateModuleData("basic_info",{name:"张三",title:"前端开发工程师",phone:"13800138000",email:"<EMAIL>"}),o.updateModuleData("skills",{skills:[{name:"JavaScript",level:90},{name:"Vue.js",level:85},{name:"React",level:80}]}),console.log("✅ 测试数据已初始化")});const i=()=>{console.log("🧪 测试基本信息更新");const t={name:"李四",title:"高级前端开发工程师",phone:"13900139000",email:"<EMAIL>"};o.updateModuleData("basic_info",t),console.log("✅ 基本信息已更新:",t)},r=()=>{console.log("🧪 测试技能更新");const t={skills:[{name:"TypeScript",level:85},{name:"Node.js",level:80},{name:"Python",level:75},{name:"Docker",level:70}]};o.updateModuleData("skills",t),console.log("✅ 技能已更新:",t)},c=()=>{console.log("🧪 测试工作经历更新");const t=[{company:"阿里巴巴",position:"高级前端开发工程师",startDate:"2022-01",endDate:"至今",description:"负责前端架构设计和核心功能开发"},{company:"腾讯",position:"前端开发工程师",startDate:"2020-06",endDate:"2021-12",description:"参与多个重要项目的前端开发工作"}];o.updateModuleData("work_experience",t),console.log("✅ 工作经历已更新:",t)},d=()=>{console.log("🔍 检查数据流"),console.log("Store数据:",o.resumeData),console.log("模块配置:",o.moduleConfigs),console.log("当前模块:",o.currentModuleId)},u=t=>{console.log("📋 模块顺序变更:",t)},p=t=>{console.log("🗑️ 模块删除:",t)};return(t,s)=>(S(),k("div",b,[s[5]||(s[5]=e("div",{class:"test-header"},[e("h1",null,"实时预览测试"),e("p",null,"测试简历编辑器的实时预览功能")],-1)),e("div",h,[e("div",{class:"test-section"},[s[0]||(s[0]=e("h2",null,"测试控制台",-1)),e("div",{class:"test-controls"},[e("button",{onClick:i,class:"test-btn"},"测试基本信息更新"),e("button",{onClick:r,class:"test-btn"},"测试技能更新"),e("button",{onClick:c,class:"test-btn"},"测试工作经历更新"),e("button",{onClick:d,class:"test-btn"},"检查数据流")])]),e("div",w,[s[3]||(s[3]=e("h2",null,"当前数据状态",-1)),e("div",y,[s[1]||(s[1]=e("h3",null,"Store数据:",-1)),e("pre",null,n(JSON.stringify(l(o).resumeData,null,2)),1),s[2]||(s[2]=e("h3",null,"模块配置:",-1)),e("pre",null,n(JSON.stringify(l(o).moduleConfigs,null,2)),1)])]),e("div",M,[s[4]||(s[4]=e("h2",null,"预览组件",-1)),e("div",x,[_(g,{"resume-data":l(o).resumeData,settings:a.value,onModuleOrderChange:u,onModuleDelete:p},null,8,["resume-data","settings"])])])])]))}},V=v(C,[["__scopeId","data-v-742111a0"]]);export{V as default};
