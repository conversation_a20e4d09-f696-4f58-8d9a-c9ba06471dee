import{r as v,l as k,A as f,i as x,c as m,a as t,t as i,h as p,d as C,p as _,v as b,b as O,F as R,g as V,o as c,q as T}from"./CURHyiUL.js";import{u as B}from"./CEpU8TOc.js";import{R as F}from"./D729gr2z.js";import{_ as L}from"./DlAUqK2U.js";import"./x_rD_Ya3.js";import"./JeH0SXlh.js";const J={class:"data-flow-test"},P={class:"test-content"},U={class:"test-section"},w={class:"debug-info"},D={class:"test-section"},E={class:"form-test"},j={class:"test-section"},q={class:"preview-test"},z={class:"test-section"},A={class:"log-section"},G={class:"logs"},H={class:"log-time"},K={class:"log-message"},Q={key:0,class:"log-data"},W={__name:"data-flow-test",setup(X){const l=B(),d=v(""),u=v(""),g=v([]),h=k({fontFamily:"system-ui",fontSize:"14px",textColor:"#333333",margins:{top:60,bottom:60,side:60},spacings:{module:24,modulePadding:16,item:16,itemPadding:12,line:1.6,paragraph:12}}),o=(s,e=null)=>{g.value.push({time:new Date().toLocaleTimeString(),message:s,data:e}),console.log("📝 测试日志:",s,e)},y=()=>{g.value=[]},r=()=>{const s={name:d.value,title:u.value,phone:"13800138000",email:"<EMAIL>",address:"北京市朝阳区",website:"",linkedin:"",github:"",photo:""};o("更新基本信息",s),l.updateModuleData("basic_info",s)},M=()=>{o("手动触发数据更新"),r()},S=s=>{o("模块顺序变更",s)},I=s=>{o("模块删除",{moduleId:s})};return f(()=>l.currentResumeData,(s,e)=>{var a;o("Store数据变化",{hasModules:Object.keys((s==null?void 0:s.modules)||{}).length,basicInfo:(a=s==null?void 0:s.modules)==null?void 0:a.basic_info})},{deep:!0}),f(()=>l.currentModuleId,(s,e)=>{o("当前模块变化",{from:e,to:s})}),x(async()=>{o("页面初始化开始");try{await l.createNewResume(),o("新简历创建成功"),l.setCurrentModule("basic_info"),o("设置当前模块为基本信息"),d.value="张三",u.value="前端开发工程师",r()}catch(s){o("初始化失败",s)}}),(s,e)=>{var a;return c(),m("div",J,[e[11]||(e[11]=t("div",{class:"test-header"},[t("h1",null,"数据流测试页面"),t("p",null,"测试简历编辑器的实时预览功能")],-1)),t("div",P,[t("div",U,[e[5]||(e[5]=t("h2",null,"1. 编辑器Store状态",-1)),t("div",w,[e[3]||(e[3]=t("p",null,[t("strong",null,"当前简历数据:")],-1)),t("pre",null,i(JSON.stringify(p(l).currentResumeData,null,2)),1),t("p",null,[e[2]||(e[2]=t("strong",null,"当前模块ID:",-1)),C(" "+i(p(l).currentModuleId),1)]),e[4]||(e[4]=t("p",null,[t("strong",null,"模块配置:")],-1)),t("pre",null,i(JSON.stringify((a=p(l).moduleConfigs)==null?void 0:a.slice(0,3),null,2)),1)])]),t("div",D,[e[8]||(e[8]=t("h2",null,"2. 基本信息表单测试",-1)),t("div",E,[e[6]||(e[6]=t("label",null,"姓名:",-1)),_(t("input",{"onUpdate:modelValue":e[0]||(e[0]=n=>d.value=n),onInput:r,placeholder:"输入姓名测试"},null,544),[[b,d.value]]),e[7]||(e[7]=t("label",null,"职位:",-1)),_(t("input",{"onUpdate:modelValue":e[1]||(e[1]=n=>u.value=n),onInput:r,placeholder:"输入职位测试"},null,544),[[b,u.value]]),t("button",{onClick:M},"手动更新数据")])]),t("div",j,[e[9]||(e[9]=t("h2",null,"3. 预览组件测试",-1)),t("div",q,[O(F,{"resume-data":p(l).currentResumeData,settings:h,onModuleOrderChange:S,onModuleDelete:I},null,8,["resume-data","settings"])])]),t("div",z,[e[10]||(e[10]=t("h2",null,"4. 数据流日志",-1)),t("div",A,[t("button",{onClick:y},"清除日志"),t("div",G,[(c(!0),m(R,null,V(g.value,(n,N)=>(c(),m("div",{key:N,class:"log-item"},[t("span",H,i(n.time),1),t("span",K,i(n.message),1),n.data?(c(),m("pre",Q,i(JSON.stringify(n.data,null,2)),1)):T("",!0)]))),128))])])])])])}}},ot=L(W,[["__scopeId","data-v-bef82953"]]);export{ot as default};
