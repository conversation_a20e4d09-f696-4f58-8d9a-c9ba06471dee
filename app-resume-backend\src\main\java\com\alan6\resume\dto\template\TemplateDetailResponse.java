package com.alan6.resume.dto.template;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 模板详情响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板详情响应DTO")
public class TemplateDetailResponse {

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    private Long id;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    private String name;

    /**
     * 模板代码
     */
    @Schema(description = "模板代码")
    private String templateCode;

    /**
     * 模板描述
     */
    @Schema(description = "模板描述")
    private String description;

    /**
     * 预览图URL
     */
    @Schema(description = "预览图URL")
    private String previewImageUrl;

    /**
     * 多尺寸预览图
     */
    @Schema(description = "多尺寸预览图")
    private Map<String, String> previewImages;

    /**
     * 模板结构数据
     */
    @Schema(description = "模板结构数据")
    private Object templateData;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Long categoryId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String categoryName;

    /**
     * 适用行业
     */
    @Schema(description = "适用行业")
    private String industry;

    /**
     * 模板风格
     */
    @Schema(description = "模板风格")
    private String style;

    /**
     * 配色方案
     */
    @Schema(description = "配色方案")
    private String colorScheme;

    /**
     * 是否付费模板
     */
    @Schema(description = "是否付费模板")
    private Integer isPremium;

    /**
     * 模板价格
     */
    @Schema(description = "模板价格")
    private BigDecimal price;

    /**
     * 原价
     */
    @Schema(description = "原价")
    private BigDecimal originalPrice;

    /**
     * 使用次数
     */
    @Schema(description = "使用次数")
    private Integer useCount;

    /**
     * 评分
     */
    @Schema(description = "评分")
    private Double rating;

    /**
     * 评分数量
     */
    @Schema(description = "评分数量")
    private Integer ratingCount;

    /**
     * 是否收藏
     */
    @Schema(description = "是否收藏")
    private Boolean isFavorited;

    /**
     * 是否热门
     */
    @Schema(description = "是否热门")
    private Boolean isHot;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表")
    private List<String> tags;

    /**
     * 功能特性
     */
    @Schema(description = "功能特性")
    private List<String> features;

    /**
     * 适用人群
     */
    @Schema(description = "适用人群")
    private List<String> suitableFor;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 相关模板
     */
    @Schema(description = "相关模板")
    private List<TemplateResponse> relatedTemplates;

    /**
     * 评价信息
     */
    @Schema(description = "评价信息")
    private Object reviews;

    /**
     * 是否可使用
     */
    @Schema(description = "是否可使用")
    private Boolean canUse;

    /**
     * 使用要求
     */
    @Schema(description = "使用要求")
    private Object usageRequirement;
} 