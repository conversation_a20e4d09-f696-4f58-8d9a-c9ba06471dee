package com.alan6.resume.service;

import com.alan6.resume.dto.template.DefaultContentRequest;
import com.alan6.resume.dto.template.DefaultContentResponse;

/**
 * 默认内容服务接口
 * @description 管理简历模板的默认内容，支持按模板类别、行业、职位获取对应的默认内容
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IDefaultContentService {

    /**
     * 获取默认简历内容
     * @description 根据模板类别、行业、职位等条件获取对应的默认简历内容
     * @param request 默认内容请求参数
     * @return 默认内容响应，包含JSON格式的简历内容
     */
    DefaultContentResponse getDefaultContent(DefaultContentRequest request);

    /**
     * 检查默认内容是否存在
     * @description 检查指定条件的默认内容是否存在
     * @param templateCategory 模板类别
     * @param industry 行业类型
     * @param position 职位类型
     * @return 是否存在对应的默认内容
     */
    boolean hasDefaultContent(String templateCategory, String industry, String position);

    /**
     * 获取支持的模板类别列表
     * @description 获取系统支持的所有模板类别
     * @return 模板类别列表
     */
    java.util.List<String> getSupportedCategories();

    /**
     * 获取支持的行业列表
     * @description 获取系统支持的所有行业类型
     * @return 行业类型列表
     */
    java.util.List<String> getSupportedIndustries();

    /**
     * 获取支持的职位列表
     * @description 获取系统支持的所有职位类型
     * @param industry 行业类型，可以为null获取所有职位
     * @return 职位类型列表
     */
    java.util.List<String> getSupportedPositions(String industry);

    /**
     * 刷新默认内容缓存
     * @description 清除并重新加载默认内容的缓存
     * @return 是否刷新成功
     */
    boolean refreshContentCache();
} 