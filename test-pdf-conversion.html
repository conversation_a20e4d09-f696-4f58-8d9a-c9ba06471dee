<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            font-size: 14px;
        }
        
        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .header .contact-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            font-size: 12px;
            color: #666;
        }
        
        .main-content {
            display: flex;
            gap: 30px;
        }
        
        .left-column {
            flex: 1;
            min-width: 200px;
        }
        
        .right-column {
            flex: 2;
            min-width: 300px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #007bff;
        }
        
        .skill-item {
            margin-bottom: 12px;
        }
        
        .skill-name {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .skill-progress {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .skill-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            transition: width 0.3s ease;
        }
        
        .education-item, .work-item, .project-item {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .item-header {
            margin-bottom: 10px;
        }
        
        .item-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .item-subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .item-date {
            font-size: 12px;
            color: #999;
            font-style: italic;
        }
        
        .item-description {
            font-size: 13px;
            line-height: 1.5;
            color: #555;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        @media print {
            .resume-container {
                box-shadow: none;
                margin: 0;
                padding: 15mm;
            }
            
            body {
                font-size: 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .section-title {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <div class="header">
            <h1>张三</h1>
            <p class="job-title">高级前端开发工程师</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📞 138-0000-0000</span>
                </div>
                <div class="contact-item">
                    <span>✉️ <EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📍 北京市朝阳区</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="left-column">
                <div class="section">
                    <h2 class="section-title">专业技能</h2>
                    <div class="skill-item">
                        <div class="skill-name">JavaScript</div>
                        <div class="skill-progress">
                            <div class="skill-progress-bar" style="width: 90%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Vue.js</div>
                        <div class="skill-progress">
                            <div class="skill-progress-bar" style="width: 85%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">React</div>
                        <div class="skill-progress">
                            <div class="skill-progress-bar" style="width: 80%;"></div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">教育经历</h2>
                    <div class="education-item">
                        <div class="item-header">
                            <div class="item-title">北京理工大学</div>
                            <div class="item-subtitle">计算机科学与技术 - 本科</div>
                            <div class="item-date">2018-09 - 2022-06</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="right-column">
                <div class="section">
                    <h2 class="section-title">工作经历</h2>
                    <div class="work-item">
                        <div class="item-header">
                            <div class="item-title">高级前端开发工程师</div>
                            <div class="item-subtitle">字节跳动</div>
                            <div class="item-date">2022-07 - 至今</div>
                        </div>
                        <div class="item-description">
                            负责公司核心产品的前端开发工作，使用Vue.js和React构建高性能的用户界面。
                            参与架构设计，优化前端性能，提升用户体验。
                        </div>
                    </div>
                    
                    <div class="work-item">
                        <div class="item-header">
                            <div class="item-title">前端开发工程师</div>
                            <div class="item-subtitle">腾讯</div>
                            <div class="item-date">2021-06 - 2022-06</div>
                        </div>
                        <div class="item-description">
                            参与微信小程序开发，负责前端页面的设计和实现。
                            与后端团队协作，完成API接口对接和数据处理。
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">项目经历</h2>
                    <div class="project-item">
                        <div class="item-header">
                            <div class="item-title">在线简历编辑器</div>
                            <div class="item-subtitle">技术负责人</div>
                            <div class="item-date">2023-01 - 2023-06</div>
                        </div>
                        <div class="item-description">
                            使用Vue.js和Nuxt.js构建的在线简历编辑器，支持实时预览和PDF导出功能。
                            项目获得了用户的高度认可，月活跃用户超过10万。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 