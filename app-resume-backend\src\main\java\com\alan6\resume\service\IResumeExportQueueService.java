package com.alan6.resume.service;

import com.alan6.resume.entity.ResumeExportQueue;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 简历导出队列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface IResumeExportQueueService extends IService<ResumeExportQueue> {

    /**
     * 提交导出任务
     * 
     * @param userId 用户ID
     * @param resumeId 简历ID
     * @param exportFormat 导出格式
     * @return 任务ID
     */
    String submitExportJob(Long userId, Long resumeId, String exportFormat);

    /**
     * 根据任务ID查询导出任务
     * 
     * @param jobId 任务ID
     * @return 导出任务信息
     */
    ResumeExportQueue getByJobId(String jobId);

    /**
     * 更新任务状态
     * 
     * @param jobId 任务ID
     * @param status 状态
     */
    void updateStatus(String jobId, String status);

    /**
     * 更新下载信息
     * 
     * @param jobId 任务ID
     * @param downloadUrl 下载链接
     * @param fileSize 文件大小
     */
    void updateDownloadInfo(String jobId, String downloadUrl, Long fileSize);

    /**
     * 更新错误信息
     * 
     * @param jobId 任务ID
     * @param errorMessage 错误信息
     */
    void updateError(String jobId, String errorMessage);

    /**
     * 获取待处理的任务
     * 
     * @param limit 限制数量
     * @return 待处理任务列表
     */
    List<ResumeExportQueue> getPendingJobs(int limit);

    /**
     * 根据用户ID获取导出记录
     * 
     * @param userId 用户ID
     * @return 导出记录列表
     */
    List<ResumeExportQueue> getByUserId(Long userId);

} 