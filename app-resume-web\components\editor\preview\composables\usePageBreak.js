/**
 * 分页检测组合式函数
 * @description 检测简历内容是否超过A4页面高度，自动插入分页线
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, onMounted, onUpdated, nextTick } from 'vue'

export function usePageBreak() {
  // ================================
  // 响应式数据
  // ================================
  const pageBreaks = ref([])
  const currentPage = ref(1)
  const totalPages = ref(1)
  
  // A4纸张高度（毫米转像素，约 96 DPI）
  const A4_HEIGHT_PX = 1122 // 297mm * 96/25.4 ≈ 1122px
  const PAGE_MARGIN = 40 // 页面边距
  const EFFECTIVE_HEIGHT = A4_HEIGHT_PX - PAGE_MARGIN * 2
  
  // ================================
  // 方法定义
  // ================================
  
  /**
   * 检测并插入分页线
   * @param {HTMLElement} container - 简历容器元素
   */
  const detectPageBreaks = async (container) => {
    if (!container) return
    
    await nextTick()
    
    // 清除之前的分页线
    clearPageBreaks(container)
    
    // 获取所有模块元素
    const modules = container.querySelectorAll('.module-container')
    let currentHeight = 0
    let currentPageNumber = 1
    const breaks = []
    
    modules.forEach((module, index) => {
      const moduleHeight = module.offsetHeight
      const moduleTop = module.offsetTop
      
      // 检查是否需要分页
      if (currentHeight + moduleHeight > EFFECTIVE_HEIGHT && currentHeight > 0) {
        // 需要分页，在当前模块前插入分页线
        const pageBreak = createPageBreak(currentPageNumber + 1)
        module.parentNode.insertBefore(pageBreak, module)
        
        breaks.push({
          element: pageBreak,
          pageNumber: currentPageNumber + 1,
          moduleIndex: index
        })
        
        currentPageNumber++
        currentHeight = moduleHeight
      } else {
        currentHeight += moduleHeight
      }
    })
    
    pageBreaks.value = breaks
    totalPages.value = currentPageNumber
  }
  
  /**
   * 创建分页线元素
   * @param {number} pageNumber - 页码
   * @returns {HTMLElement} 分页线元素
   */
  const createPageBreak = (pageNumber) => {
    const pageBreak = document.createElement('div')
    pageBreak.className = 'page-break'
    pageBreak.setAttribute('data-page', pageNumber)
    
    // 添加分页提示
    const pageInfo = document.createElement('div')
    pageInfo.className = 'page-break-info'
    pageInfo.innerHTML = `
      <span class="page-break-line"></span>
      <span class="page-break-text">第 ${pageNumber} 页</span>
      <span class="page-break-line"></span>
    `
    
    pageBreak.appendChild(pageInfo)
    return pageBreak
  }
  
  /**
   * 清除所有分页线
   * @param {HTMLElement} container - 简历容器元素
   */
  const clearPageBreaks = (container) => {
    const existingBreaks = container.querySelectorAll('.page-break')
    existingBreaks.forEach(breakEl => breakEl.remove())
    pageBreaks.value = []
  }
  
  /**
   * 获取指定页码的内容
   * @param {number} pageNum - 页码
   * @returns {Array} 该页的模块列表
   */
  const getPageContent = (pageNum) => {
    // TODO: 实现按页码获取内容的逻辑
    return []
  }
  
  /**
   * 跳转到指定页码
   * @param {number} pageNum - 目标页码
   */
  const goToPage = (pageNum) => {
    if (pageNum < 1 || pageNum > totalPages.value) return
    
    const targetBreak = pageBreaks.value.find(b => b.pageNumber === pageNum)
    if (targetBreak) {
      targetBreak.element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
      })
    }
    
    currentPage.value = pageNum
  }
  
  /**
   * 上一页
   */
  const prevPage = () => {
    if (currentPage.value > 1) {
      goToPage(currentPage.value - 1)
    }
  }
  
  /**
   * 下一页
   */
  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      goToPage(currentPage.value + 1)
    }
  }
  
  /**
   * 重新计算分页
   * @param {HTMLElement} container - 简历容器元素
   */
  const recalculatePages = (container) => {
    // 延迟执行，确保DOM更新完成
    setTimeout(() => {
      detectPageBreaks(container)
    }, 100)
  }
  
  // ================================
  // 返回接口
  // ================================
  return {
    // 响应式数据
    pageBreaks,
    currentPage,
    totalPages,
    
    // 方法
    detectPageBreaks,
    clearPageBreaks,
    getPageContent,
    goToPage,
    prevPage,
    nextPage,
    recalculatePages,
    
    // 常量
    A4_HEIGHT_PX,
    EFFECTIVE_HEIGHT
  }
} 