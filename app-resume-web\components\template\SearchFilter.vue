<template>
  <div class="bg-white border-b border-secondary-200 relative z-20">
    <div class="max-w-screen-2xl mx-auto px-6 lg:px-8 py-6 pt-36">
      <!-- 主搜索项和搜索框的容器 -->
      <div class="flex items-center justify-between mb-6">
        <!-- 主搜索项 -->
        <div class="flex flex-wrap gap-3">
          <button
            v-for="category in mainCategories"
            :key="category.id"
            @click="handleMainCategoryClick(category)"
            :class="[
              'inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200',
              activeMainCategory?.id === category.id
                ? 'bg-primary-600 text-white'
                : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'
            ]"
          >
            <!-- 使用简单的圆点图标替代复杂图标 -->
            <div class="w-4 h-4 mr-2 bg-current rounded-full opacity-60"></div>
            {{ category.name }}
          </button>
        </div>

        <!-- 搜索框 -->
        <div class="relative max-w-md">
          <input 
            v-model="searchKeyword"
            @input="handleSearch"
            type="text" 
            placeholder="输入简历关键词" 
            class="w-full px-4 py-2 pl-4 pr-12 rounded-xl border border-secondary-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
          <button class="absolute right-4 top-1/2 transform -translate-y-1/2">
            <svg class="w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 副搜索项 -->
      <div v-if="subCategories?.length > 0" class="flex flex-wrap gap-2">
        <button
          v-for="subCategory in subCategories"
          :key="subCategory.id"
          @click="handleSubCategoryClick(subCategory)"
          :class="[
            'px-3 py-1.5 rounded-full text-sm transition-all duration-200',
            activeSubCategory?.id === subCategory.id
              ? 'bg-primary-100 text-primary-700 border border-primary-200'
              : 'bg-secondary-50 text-secondary-600 hover:bg-secondary-100 border border-transparent'
          ]"
        >
          {{ subCategory.name }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineEmits, computed } from 'vue'

// Emits
const emit = defineEmits(['main-category-change', 'sub-category-change', 'search'])

// Reactive Data
const searchKeyword = ref('')
const activeMainCategory = ref(null)
const activeSubCategory = ref(null)

// 主搜索分类数据
const mainCategories = ref([
  {
    id: 'hot',
    name: '热门模板'
  },
  {
    id: 'style',
    name: '设计风格'
  },
  {
    id: 'industry',
    name: '行业职位'
  },
  {
    id: 'major',
    name: '高校专业'
  },
  {
    id: 'intern',
    name: '实习生'
  }
])

// 主搜索和副搜索的关联数据
const categoryMappings = {
  hot: [
    { id: 'all', name: '所有' },
    { id: 'school-recruit', name: '校招简历模板' },
    { id: 'social-recruit', name: '社招简历模板' },
    { id: 'student-business', name: '大学生商务模板' },
    { id: 'fresh-graduate', name: '应届生简历模板' },
    { id: 'practical-business', name: '实习生商务模板' },
    { id: 'zero-experience', name: '零经验找实习模板' },
    { id: 'top-university', name: '三本大学生简历模板' },
    { id: 'general-interview', name: '通用机门简历模板' },
    { id: 'liberal-arts', name: '土木转行模板' }
  ],
  style: [
    { id: 'all', name: '所有' },
    { id: 'classic', name: '经典传统' },
    { id: 'business', name: '简约商务' },
    { id: 'creative', name: '创意设计' },
    { id: 'tech', name: '技术极客' },
    { id: 'modern', name: '现代时尚' },
    { id: 'fresh', name: '清新文艺' }
  ],
  industry: [
    { id: 'all', name: '所有' },
    { id: 'advertising', name: '广告/传媒' },
    { id: 'finance', name: '财务/法律' },
    { id: 'service', name: '服务业/贸易' },
    { id: 'construction', name: '房产建筑' },
    { id: 'sales', name: '销售/客服' },
    { id: 'education', name: '教育/医疗' },
    { id: 'tech-rd', name: '技术/研发' },
    { id: 'product', name: '产品/设计' },
    { id: 'finance-auto', name: '金融/汽车' },
    { id: 'marketing', name: '市场/运营' },
    { id: 'hr', name: '人事/行政' }
  ],
  major: [
    { id: 'all', name: '所有' },
    { id: 'software', name: '软件工程' },
    { id: 'computer', name: '计算机科学' },
    { id: 'biology', name: '生化环科类' },
    { id: 'journalism', name: '新闻传媒类' },
    { id: 'finance-major', name: '金融会计类' },
    { id: 'economics', name: '经管类' },
    { id: 'normal', name: '师范类' },
    { id: 'language', name: '语言类' },
    { id: 'engineering', name: '工学制造类' },
    { id: 'other', name: '其他' }
  ],
  intern: [
    { id: 'all', name: '所有' },
    { id: 'fresh-intern', name: '应届生简历模板' },
    { id: 'intern-business', name: '实习生商务模板' },
    { id: 'zero-exp-intern', name: '零经验找实习模板' }
  ]
}

// 计算属性：当前副搜索项列表
const subCategories = computed(() => {
  return activeMainCategory.value ? categoryMappings[activeMainCategory.value.id] || [] : []
})

// Methods
const handleMainCategoryClick = (category) => {
  activeMainCategory.value = category
  // 自动选中对应的"所有"子项
  const allSubCategory = categoryMappings[category.id]?.[0]
  if (allSubCategory) {
    activeSubCategory.value = allSubCategory
    // 立即触发筛选，传递主分类和默认的"所有"子分类
    emit('sub-category-change', {
      mainCategory: category,
      subCategory: allSubCategory
    })
  } else {
    activeSubCategory.value = null
    emit('main-category-change', category)
  }
}

const handleSubCategoryClick = (subCategory) => {
  activeSubCategory.value = subCategory
  emit('sub-category-change', {
    mainCategory: activeMainCategory.value,
    subCategory
  })
}

const handleSearch = () => {
  emit('search', searchKeyword.value)
}

// 初始化默认选中第一个主分类
onMounted(() => {
  if (mainCategories.value.length > 0) {
    activeMainCategory.value = mainCategories.value[0]
    // 自动选中第一个主分类的"所有"子项
    const firstCategory = mainCategories.value[0]
    const allSubCategory = categoryMappings[firstCategory.id]?.[0]
    if (allSubCategory) {
      activeSubCategory.value = allSubCategory
    }
  }
})
</script> 