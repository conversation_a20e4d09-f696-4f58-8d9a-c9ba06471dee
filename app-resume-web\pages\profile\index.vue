<template>
  <div class="min-h-screen bg-gray-50 pt-16">
    <div class="max-w-screen-2xl mx-auto px-6 lg:px-8 py-8">
      <div class="flex gap-10">
        <!-- 左侧导航栏 -->
        <aside class="w-72 flex-shrink-0">
          <!-- 新建简历按钮 -->
          <button
            @click="handleCreateResume"
            class="w-full mb-8 px-6 py-4 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center space-x-3 text-base"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <span>新建简历</span>
          </button>

          <!-- 导航菜单 -->
          <nav class="space-y-2">
            <NuxtLink
              v-for="item in menuItems"
              :key="item.key"
              :to="`/profile?tab=${item.key}`"
              @click="activeTab = item.key"
              class="flex items-center space-x-4 px-6 py-4 text-base font-medium transition-all duration-150 rounded-lg relative group"
              :class="activeTab === item.key 
                ? 'bg-blue-50 text-blue-700' 
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'"
            >
              <component :is="item.icon" class="w-6 h-6 flex-shrink-0" 
                :class="activeTab === item.key ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'"
              />
              <span class="flex-1 text-base">{{ item.label }}</span>
              
              <!-- 右侧箭头 -->
              <svg 
                class="w-5 h-5 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" 
                :class="activeTab === item.key && 'opacity-100'"
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </NuxtLink>
          </nav>
        </aside>

        <!-- 右侧内容区域 -->
        <main class="flex-1 bg-white rounded-lg shadow-sm p-8">
          <!-- 动态内容组件 -->
          <component :is="currentComponent" />
        </main>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, h, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MyResumes from './components/MyResumes.vue'
import MyOrders from './components/MyOrders.vue'
import MyFavorites from './components/MyFavorites.vue'
import MembershipPurchase from './components/MembershipPurchase.vue'
import AccountSettings from './components/AccountSettings.vue'

// 路由
const route = useRoute()
const router = useRouter()

// 页面元数据
useSeoMeta({
  title: '个人中心 - 火花简历',
  description: '管理您的简历、订单、收藏和账户设置'
})

// 状态
const activeTab = ref('resumes')

// 菜单配置
const menuItems = [
  {
    key: 'resumes',
    label: '我的简历',
    icon: h('svg', { fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' 
      })
    ])
  },
  {
    key: 'orders',
    label: '我的订单',
    icon: h('svg', { fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z' 
      })
    ])
  },
  {
    key: 'favorites',
    label: '我的收藏',
    icon: h('svg', { fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z' 
      })
    ])
  },
  {
    key: 'membership',
    label: '会员购买',
    icon: h('svg', { fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z' 
      })
    ])
  },
  {
    key: 'settings',
    label: '账户设置',
    icon: h('svg', { fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' 
      }),
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z' 
      })
    ])
  }
]

// 组件映射
const componentMap = {
  resumes: MyResumes,
  orders: MyOrders,
  favorites: MyFavorites,
  membership: MembershipPurchase,
  settings: AccountSettings
}

// 计算当前组件
const currentComponent = computed(() => {
  return componentMap[activeTab.value] || MyResumes
})

// 处理新建简历
const handleCreateResume = () => {
  router.push('/templates')
}

// 监听路由参数变化
watch(() => route.query.tab, (newTab) => {
  if (newTab && menuItems.some(item => item.key === newTab)) {
    activeTab.value = newTab
  }
}, { immediate: true })

// 初始化
onMounted(() => {
  // 从路由参数获取当前标签
  const tab = route.query.tab
  if (tab && menuItems.some(item => item.key === tab)) {
    activeTab.value = tab
  }
})
</script>

<style scoped>
/* 添加一些过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 