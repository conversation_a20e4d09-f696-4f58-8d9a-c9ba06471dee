import{u as _e}from"./CEpU8TOc.js";import{u as ke}from"./D1FrdRFX.js";import{u as Ce}from"./12cCxHYh.js";import{_ as be}from"./BXnw39BI.js";import{k as A,c,o as i,a as e,q as L,t as I,r as T,i as G,B as X,h as ne,b as w,w as le,d as me,n as Ve,p as U,v as E,C as ce,D as ie,E as he,G as de,f as ae,m as $e,z as W,F as O,g as F,s as R,l as J,A as H,H as re,I as ve,J as fe,T as ge}from"./CURHyiUL.js";import{I as P}from"./D7AOVZt6.js";import{_ as N}from"./DlAUqK2U.js";import{u as we}from"./CKDEb6Y1.js";import{D as xe}from"./iG63TLyk.js";import{B as De}from"./CG_vNifS.js";import{R as te}from"./Ciwj-CUv.js";import{P as Ue}from"./DdM5dVZ_.js";import{R as Me}from"./D729gr2z.js";import{G as Se,L as Ie}from"./D3iq3ewo.js";const Ee=()=>({formatDate:(M,g="YYYY-MM-DD HH:mm")=>new Date(M).toLocaleString()}),Te={class:"save-status"},ze={class:"status-indicator"},Le={key:0,class:"status-item saving"},Pe={key:1,class:"status-item dirty"},Oe={key:2,class:"status-item saved"},Fe={key:0,class:"last-saved"},je={class:"time-text"},Be={__name:"SaveStatus",props:{isDirty:{type:Boolean,default:!1},isSaving:{type:Boolean,default:!1},lastSaved:{type:[String,Date],default:null}},setup(x){const M=x,g=Ee(),$=A(()=>{if(!M.lastSaved)return"";try{const s=new Date(M.lastSaved),r=new Date-s,u=Math.floor(r/(1e3*60));return u<1?"刚刚保存":u<60?`${u}分钟前保存`:u<24*60?`${Math.floor(u/60)}小时前保存`:g.formatDate(s,"MM-DD HH:mm")}catch(s){return console.error("格式化保存时间失败:",s),"时间格式错误"}});return(s,p)=>(i(),c("div",Te,[e("div",ze,[x.isSaving?(i(),c("div",Le,p[0]||(p[0]=[e("div",{class:"loading-spinner"},null,-1),e("span",{class:"status-text"},"正在保存...",-1)]))):x.isDirty?(i(),c("div",Pe,p[1]||(p[1]=[e("div",{class:"status-icon unsaved"},null,-1),e("span",{class:"status-text"},"有未保存的更改",-1)]))):(i(),c("div",Oe,p[2]||(p[2]=[e("div",{class:"status-icon saved-icon"},null,-1),e("span",{class:"status-text"},"已保存",-1)])))]),x.lastSaved&&!x.isSaving?(i(),c("div",Fe,[e("span",je,I($.value),1)])):L("",!0)]))}},Ne=N(Be,[["__scopeId","data-v-3e5e8523"]]),Re={class:"user-status"},Ye={key:0,class:"login-actions"},We={key:1,class:"login-actions"},qe={key:2,class:"user-menu"},Ae=["src","alt"],He={key:1,class:"avatar-placeholder"},Ge={key:0,class:"dropdown-menu"},Ke={class:"user-info"},Je={class:"user-name"},Xe={class:"user-email"},Qe={__name:"UserStatus",emits:["request-login"],setup(x,{emit:M}){const g=M,$=we(),s=T(!1),p=A(()=>$.isLoggedIn.value),r=A(()=>$.currentUser.value);G(()=>{document.addEventListener("click",f),$.isInitialized.value||$.initAuth()}),X(()=>{document.removeEventListener("click",f)});const u=()=>{const _=r.value;if(!_)return"用";const d=_.nickname||_.username||_.phone;return d?d.charAt(0).toUpperCase():"用"},m=()=>{const _=r.value;return _&&(_.nickname||_.username)||"用户"},k=()=>{const _=r.value;return(_==null?void 0:_.email)||""},h=()=>{g("request-login")},b=()=>{s.value=!s.value},v=()=>{s.value=!1},f=_=>{!_.target.closest(".user-menu")&&s.value&&v()},C=async()=>{try{await $.logout(),v(),console.log("用户已退出登录"),await Ve("/")}catch(_){console.error("退出登录失败:",_)}};return(_,d)=>{var t;const n=be;return i(),c("div",Re,[ne($).isInitialized.value?p.value?(i(),c("div",qe,[e("div",{class:"user-avatar",onClick:b},[(t=r.value)!=null&&t.avatar?(i(),c("img",{key:0,src:r.value.avatar,alt:r.value.nickname||r.value.username,class:"avatar-image"},null,8,Ae)):(i(),c("div",He,I(u()),1)),w(P,{name:"arrow-down",size:"xs",class:"dropdown-arrow"})]),s.value?(i(),c("div",Ge,[e("div",Ke,[e("div",Je,I(m()),1),e("div",Xe,I(k()),1)]),d[5]||(d[5]=e("div",{class:"menu-divider"},null,-1)),w(n,{to:"/profile",class:"menu-item",onClick:v},{default:le(()=>[w(P,{name:"dashboard",size:"sm"}),d[1]||(d[1]=me(" 个人中心 "))]),_:1,__:[1]}),w(n,{to:"/my-resumes",class:"menu-item",onClick:v},{default:le(()=>[w(P,{name:"document",size:"sm"}),d[2]||(d[2]=me(" 我的简历 "))]),_:1,__:[2]}),w(n,{to:"/profile",class:"menu-item",onClick:v},{default:le(()=>[w(P,{name:"settings",size:"sm"}),d[3]||(d[3]=me(" 账户设置 "))]),_:1,__:[3]}),d[6]||(d[6]=e("div",{class:"menu-divider"},null,-1)),e("button",{class:"menu-item logout-item",onClick:C},[w(P,{name:"logout",size:"sm"}),d[4]||(d[4]=me(" 退出登录 "))])])):L("",!0)])):(i(),c("div",We,[e("button",{class:"login-register-btn",onClick:h}," 登录 | 注册 ")])):(i(),c("div",Ye,d[0]||(d[0]=[e("div",{class:"loading-btn"},[e("div",{class:"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2"}),e("span",{class:"text-sm text-gray-500"},"加载中...")],-1)]))),s.value?(i(),c("div",{key:3,class:"dropdown-overlay",onClick:v})):L("",!0)])}}},Ze=N(Qe,[["__scopeId","data-v-bb7e4a75"]]),et={class:"editor-header"},tt={class:"header-left"},st={class:"header-logo"},ot={class:"file-name-editor"},nt={class:"title-text"},lt={class:"header-right"},at={class:"action-buttons"},it=["disabled"],dt={__name:"EditorHeader",props:{resumeData:{type:Object,required:!0},isDirty:{type:Boolean,default:!1},isSaving:{type:Boolean,default:!1}},emits:["save","download","send-email","title-change","request-login"],setup(x,{emit:M}){const g=x,$=M,s=T(!1),p=T(""),r=T(null),u=T(!1),m=T("正在导出..."),k=()=>{s.value=!0,p.value=g.resumeData.title||"未命名简历",ie(()=>{r.value&&(r.value.focus(),r.value.select())})},h=()=>{const C=p.value.trim();C&&C!==g.resumeData.title&&$("title-change",C),s.value=!1},b=()=>{p.value=g.resumeData.title||"未命名简历",s.value=!1},v=()=>{h()},f=C=>{u.value=!0;const _={pdf:"正在生成PDF文档...",word:"正在生成Word文档...",image:"正在生成图片..."};m.value=_[C]||"正在导出...",$("download",{format:C})};return(C,_)=>{const d=be;return i(),c("header",et,[e("div",tt,[e("div",st,[w(d,{to:"/",class:"logo-link"},{default:le(()=>_[4]||(_[4]=[e("div",{class:"logo-icon"},[e("svg",{class:"w-6 h-6 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M12 2c-1.1 0-2 .9-2 2 0 .74.4 1.39 1 1.73v1.77L9 9.5c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2s2 .9 2 2c0 .74-.4 1.39-1 1.73V7.5h1c.55 0 1 .45 1 1s-.45 1-1 1l-2 2v1.77c.6.34 1 .99 1 1.73 0 1.1-.9 2-2 2s-2-.9-2-2c0-.74.4-1.39 1-1.73V11.5l-2-2c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2z"})]),e("div",{class:"logo-spark"})],-1),e("span",{class:"logo-text"},"火花简历",-1)])),_:1,__:[4]})]),e("div",ot,[s.value?U((i(),c("input",{key:0,ref_key:"titleInput",ref:r,"onUpdate:modelValue":_[0]||(_[0]=n=>p.value=n),class:"title-input",type:"text",onBlur:v,onKeydown:[ce(h,["enter"]),ce(b,["esc"])]},null,544)),[[E,p.value]]):(i(),c("div",{key:1,class:"title-display",onClick:k},[e("span",nt,I(x.resumeData.title||"未命名简历"),1),w(P,{name:"edit",class:"edit-icon"})]))])]),e("div",lt,[w(Ne,{"is-dirty":x.isDirty,"last-saved":x.resumeData.lastSaved,"is-saving":x.isSaving},null,8,["is-dirty","last-saved","is-saving"]),e("div",at,[e("button",{class:"action-btn save-btn",disabled:!x.isDirty||x.isSaving,onClick:_[1]||(_[1]=n=>C.$emit("save")),title:"保存 (Ctrl+S)"},[w(P,{name:"save"}),_[5]||(_[5]=e("span",{class:"btn-text"},"保存",-1))],8,it),w(xe,{"is-exporting":u.value,"export-status":m.value,onDownload:f,onSendEmail:_[2]||(_[2]=n=>C.$emit("send-email"))},null,8,["is-exporting","export-status"]),_[6]||(_[6]=e("div",{class:"divider"},null,-1)),w(Ze,{onRequestLogin:_[3]||(_[3]=n=>C.$emit("request-login"))})])])])}}},rt=N(dt,[["__scopeId","data-v-6e2d5abb"]]),ct={__name:"SplitPanel",props:{leftWidth:{type:Number,default:50,validator:x=>x>=20&&x<=80},minLeftWidth:{type:Number,default:25},maxLeftWidth:{type:Number,default:75}},emits:["resize"],setup(x,{expose:M,emit:g}){const $=x,s=g,p=T(null),r=T(!1),u=T(0),m=T(0),k=A(()=>100-$.leftWidth);G(()=>{document.addEventListener("mousemove",f),document.addEventListener("mouseup",d),document.addEventListener("touchmove",C),document.addEventListener("touchend",n)}),X(()=>{document.removeEventListener("mousemove",f),document.removeEventListener("mouseup",d),document.removeEventListener("touchmove",C),document.removeEventListener("touchend",n)});const h=y=>{y.preventDefault(),v(y.clientX)},b=y=>{y.preventDefault();const B=y.touches[0];v(B.clientX)},v=y=>{p.value&&(r.value=!0,u.value=y,m.value=$.leftWidth,document.body.style.cursor="col-resize",document.body.style.userSelect="none")},f=y=>{r.value&&_(y.clientX)},C=y=>{if(!r.value)return;const B=y.touches[0];_(B.clientX)},_=y=>{if(!p.value||!r.value)return;const oe=p.value.getBoundingClientRect().width,Q=(y-u.value)/oe*100;let S=m.value+Q;S=Math.max($.minLeftWidth,Math.min($.maxLeftWidth,S)),s("resize",S)},d=()=>{t()},n=()=>{t()},t=()=>{r.value&&(r.value=!1,document.body.style.cursor="",document.body.style.userSelect="")};return M({resetSize:()=>{s("resize",50)},setLeftPrimary:()=>{s("resize",70)},setRightPrimary:()=>{s("resize",30)}}),(y,B)=>(i(),c("div",{class:"split-panel",ref_key:"splitPanelRef",ref:p},[e("div",{class:"split-left",style:de({width:`${x.leftWidth}%`})},[he(y.$slots,"left",{},void 0,!0)],4),e("div",{class:"split-divider",onMousedown:h,onTouchstart:b},B[0]||(B[0]=[ae('<div class="divider-line" data-v-226d9ee7><div class="divider-handle" data-v-226d9ee7><div class="handle-dots" data-v-226d9ee7><div class="dot" data-v-226d9ee7></div><div class="dot" data-v-226d9ee7></div><div class="dot" data-v-226d9ee7></div></div></div></div>',1)]),32),e("div",{class:"split-right",style:de({width:`${k.value}%`})},[he(y.$slots,"right",{},void 0,!0)],4)],512))}},ut=N(ct,[["__scopeId","data-v-226d9ee7"]]),pt={class:"dialog-header"},mt={class:"dialog-body"},vt={class:"form-group"},_t={class:"module-info"},ft={class:"info-item"},gt={class:"info-item"},ht={class:"dialog-footer"},bt=["disabled"],$t={__name:"CreateCustomModuleDialog",emits:["confirm","cancel"],setup(x,{emit:M}){const g=M,$=T(""),s=T(null);G(()=>{ie(()=>{var r;(r=s.value)==null||r.focus()})});const p=()=>{$.value.trim()&&g("confirm",$.value.trim())};return(r,u)=>(i(),c("div",{class:"dialog-overlay",onClick:u[5]||(u[5]=m=>r.$emit("cancel"))},[e("div",{class:"dialog-content",onClick:u[4]||(u[4]=$e(()=>{},["stop"]))},[e("div",pt,[u[6]||(u[6]=e("h3",{class:"dialog-title"},"创建自定义模块",-1)),e("button",{class:"close-btn",onClick:u[0]||(u[0]=m=>r.$emit("cancel"))},[w(P,{name:"close",size:"sm"})])]),e("div",mt,[e("div",vt,[u[7]||(u[7]=e("label",{class:"form-label"},"模块名称",-1)),U(e("input",{ref_key:"nameInput",ref:s,"onUpdate:modelValue":u[1]||(u[1]=m=>$.value=m),type:"text",class:"form-input",placeholder:"请输入自定义模块名称",maxlength:"20",onKeyup:[ce(p,["enter"]),u[2]||(u[2]=ce(m=>r.$emit("cancel"),["escape"]))]},null,544),[[E,$.value]]),u[8]||(u[8]=e("p",{class:"form-hint"},"最多20个字符",-1))]),e("div",_t,[e("div",ft,[w(P,{name:"info",size:"sm",class:"info-icon"}),u[9]||(u[9]=e("span",{class:"info-text"},"自定义模块包含：名称、角色、时间范围、详细内容",-1))]),e("div",gt,[w(P,{name:"info",size:"sm",class:"info-icon"}),u[10]||(u[10]=e("span",{class:"info-text"},"最多可创建3个自定义模块",-1))])])]),e("div",ht,[e("button",{class:"cancel-btn",onClick:u[3]||(u[3]=m=>r.$emit("cancel"))}," 取消 "),e("button",{class:"confirm-btn",onClick:p,disabled:!$.value.trim()}," 确定 ",8,bt)])])]))}},yt=N($t,[["__scopeId","data-v-5c869b2b"]]),kt={class:"dialog-header"},Ct={class:"dialog-content"},Vt={class:"section"},wt={key:0,class:"empty-state"},xt={key:1,class:"module-list"},Dt=["draggable","onDragstart","onDrop"],Ut={class:"module-drag-handle"},Mt={class:"module-content"},St={class:"module-info"},It={class:"module-name"},Et={key:0,class:"required-badge"},Tt={class:"module-desc"},zt={class:"module-actions"},Lt={key:0,class:"action-buttons"},Pt=["onClick"],Ot=["onClick"],Ft=["onClick"],jt=["onClick"],Bt={key:1,class:"required-text"},Nt={class:"section"},Rt={key:0,class:"empty-state"},Yt={class:"module-grid"},Wt={class:"card-content"},qt={class:"card-title"},At={class:"card-desc"},Ht={class:"card-action"},Gt=["onClick"],Kt={class:"card-content"},Jt={class:"card-title"},Xt={class:"card-desc"},Qt={class:"card-action"},Zt={class:"custom-module-actions"},es=["onClick"],ts=["onClick"],ss={class:"card-content"},os={class:"card-desc"},ns={class:"card-action"},ls={class:"creator-count"},as={class:"dialog-footer"},is={__name:"ModuleManager",props:{modules:{type:Array,required:!0}},emits:["close","update"],setup(x,{emit:M}){const g=x,$=M,s=T([]),p=T(-1),r=T(!1),u=T(null);let m=null;const k=_e(),h=A(()=>{const S=s.value.filter(D=>D.enabled).sort((D,j)=>D.order-j.order),V=f.value.filter(D=>D.enabled).sort((D,j)=>D.order-j.order);return[...S,...V].sort((D,j)=>D.order-j.order)}),b=A(()=>s.value.filter(S=>!S.enabled&&!S.id.startsWith("custom_")).sort((S,V)=>S.name.localeCompare(V.name))),v=A(()=>f.value.filter(S=>!S.enabled)),f=A(()=>{var S;return((S=k.customModules)==null?void 0:S.value)||[]});G(()=>{s.value=JSON.parse(JSON.stringify(g.modules)).filter(S=>!S.id.startsWith("custom_"))});const C=()=>{$("close")},_=S=>{if(S.required&&S.enabled)return;const V=document.querySelector(".dialog-content"),D=V?V.scrollTop:0,j=V?V.style.overflow:"",Y=V?V.style.scrollBehavior:"";if(V&&(V.style.overflow="hidden",V.style.scrollBehavior="auto"),S.id.startsWith("custom_"))k.toggleCustomModule(S.id);else{const K=s.value.findIndex(Z=>Z.id===S.id);if(K!==-1&&(s.value[K].enabled=!s.value[K].enabled,s.value[K].enabled)){const Z=Math.max(...s.value.filter(q=>q.enabled).map(q=>q.order));s.value[K].order=Z+1}}ie(()=>{setTimeout(()=>{V&&(V.scrollTop=D,V.style.overflow=j,V.style.scrollBehavior=Y)},0)})},d=S=>{h.value[S].id!=="basic_info"&&(p.value=S)},n=S=>{S.preventDefault()},t=S=>{if(event.preventDefault(),p.value===-1||p.value===S)return;const V=[...h.value],D=V[p.value],j=V[S];if(D.id==="basic_info"||j.id==="basic_info"){p.value=-1;return}const Y=V.filter(q=>q.id!=="basic_info"),K=Y.findIndex(q=>q.id===D.id),Z=Y.findIndex(q=>q.id===j.id);K!==-1&&Z!==-1&&(Y.splice(K,1),Y.splice(Z,0,D),Y.forEach((q,ee)=>{const se=s.value.findIndex(ue=>ue.id===q.id);se!==-1&&(s.value[se].order=ee+2)})),p.value=-1},l=()=>{s.value.forEach(S=>{const V=k.moduleConfigs.value.find(D=>D.id===S.id);V&&(V.enabled=S.enabled,V.order=S.order)}),s.value.forEach(S=>{var V,D;if(S.type==="custom"){const j=(D=(V=k.customModules)==null?void 0:V.value)==null?void 0:D.find(Y=>Y.id===S.id);j&&(j.enabled=S.enabled,j.order=S.order)}}),k.moduleConfigs.value=[...k.moduleConfigs.value],$("update",s.value)},a=S=>{if(S>0){const V=[...h.value],D=V[S],j=V[S-1];if(j.id==="basic_info")return;const Y=D.order,K=(Z,q)=>{if(Z.id.startsWith("custom_")){const ee=f.value.find(se=>se.id===Z.id);ee&&(ee.order=q)}else{const ee=s.value.findIndex(se=>se.id===Z.id);ee!==-1&&(s.value[ee].order=q)}};K(D,j.order),K(j,Y),B(D.id),y(D.id,S-1)}},o=S=>{if(S<h.value.length-1){const V=[...h.value],D=V[S],j=V[S+1];if(D.id==="basic_info")return;const Y=D.order,K=(Z,q)=>{if(Z.id.startsWith("custom_")){const ee=f.value.find(se=>se.id===Z.id);ee&&(ee.order=q)}else{const ee=s.value.findIndex(se=>se.id===Z.id);ee!==-1&&(s.value[ee].order=q)}};K(D,j.order),K(j,Y),B(D.id),y(D.id,S+1)}},y=(S,V)=>{ie(()=>{setTimeout(()=>{const D=document.querySelector(".dialog-content");if(!D)return;const j=D.querySelectorAll(".module-item");if(!j||j.length===0)return;const Y=j[V];if(!Y)return;const K=D.scrollTop,Z=D.clientHeight;Y.getBoundingClientRect(),D.getBoundingClientRect();const q=Y.offsetTop,ee=Y.offsetHeight,se=60,ue=80;let pe=K;q<K+se?pe=Math.max(0,q-ue):q+ee>K+Z-se&&(pe=q+ee-Z+ue),Math.abs(pe-K)>5&&D.scrollTo({top:pe,behavior:"smooth"})},100)})},B=S=>{m&&clearTimeout(m),u.value=S,m=setTimeout(()=>{u.value=null},2e3)},oe=S=>{try{k.createCustomModule(S),r.value=!1}catch(V){alert(V.message)}},z=()=>{if(f.value.length>=3){alert("已达到创建上限，请删除后再创建");return}r.value=!0},Q=S=>{if(confirm("确定要删除此自定义模块吗？")){const V=document.querySelector(".dialog-content"),D=V?V.scrollTop:0,j=V?V.style.overflow:"",Y=V?V.style.scrollBehavior:"";V&&(V.style.overflow="hidden",V.style.scrollBehavior="auto"),k.deleteCustomModule(S),ie(()=>{setTimeout(()=>{V&&(V.scrollTop=D,V.style.overflow=j,V.style.scrollBehavior=Y)},0)})}};return(S,V)=>(i(),c("div",{class:"module-manager-overlay",onClick:C},[e("div",{class:"module-manager-dialog",onClick:V[2]||(V[2]=$e(()=>{},["stop"]))},[e("div",kt,[V[4]||(V[4]=e("h3",{class:"dialog-title"},"管理简历模块",-1)),e("button",{class:"close-btn",onClick:V[0]||(V[0]=D=>S.$emit("close"))},[w(P,{name:"close",size:"sm"})])]),e("div",Ct,[e("div",Vt,[V[6]||(V[6]=e("h4",{class:"section-title"},"已启用模块",-1)),V[7]||(V[7]=e("p",{class:"section-desc"},"拖拽调整模块顺序，点击切换启用状态",-1)),h.value.length===0?(i(),c("div",wt,[w(P,{name:"info",size:"lg",class:"empty-icon"}),V[5]||(V[5]=e("p",{class:"empty-text"},"暂无已启用的模块",-1))])):(i(),c("div",xt,[(i(!0),c(O,null,F(h.value,(D,j)=>(i(),c("div",{key:D.id,class:R(["module-item",{required:D.required},{"no-drag":D.id==="basic_info"},{"recently-moved":u.value===D.id}]),draggable:D.id!=="basic_info",onDragstart:Y=>d(j),onDragover:n,onDrop:Y=>t(j)},[e("div",Ut,[w(P,{name:"drag",size:"sm"})]),e("div",Mt,[e("div",St,[w(P,{name:D.icon,size:"sm",class:"module-icon"},null,8,["name"]),e("span",It,I(D.name),1),D.required?(i(),c("span",Et,"必填")):L("",!0)]),e("p",Tt,I(D.description),1)]),e("div",zt,[D.required?(i(),c("span",Bt,"必需")):(i(),c("div",Lt,[D.id!=="basic_info"&&j>0?(i(),c("button",{key:0,class:"action-btn sort-btn",onClick:Y=>a(j),title:"上移"},[w(P,{name:"arrow-up",size:"sm"})],8,Pt)):L("",!0),D.id!=="basic_info"&&j<h.value.length-1?(i(),c("button",{key:1,class:"action-btn sort-btn",onClick:Y=>o(j),title:"下移"},[w(P,{name:"arrow-down",size:"sm"})],8,Ot)):L("",!0),D.id.startsWith("custom_")?L("",!0):(i(),c("button",{key:2,class:"action-btn delete-btn",onClick:Y=>_(D),title:"删除模块"},[w(P,{name:"delete",size:"sm"})],8,Ft)),D.id.startsWith("custom_")?(i(),c("button",{key:3,class:"action-btn delete-btn",onClick:Y=>Q(D.id),title:"删除自定义模块"},[w(P,{name:"delete",size:"sm"})],8,jt)):L("",!0)]))])],42,Dt))),128))]))]),e("div",Nt,[V[12]||(V[12]=e("h4",{class:"section-title"},"可用模块",-1)),V[13]||(V[13]=e("p",{class:"section-desc"},"点击启用更多模块丰富你的简历",-1)),b.value.length===0&&v.value.length===0?(i(),c("div",Rt,[w(P,{name:"check-circle",size:"lg",class:"empty-icon text-green-500"}),V[8]||(V[8]=e("p",{class:"empty-text"},"所有模块均已启用",-1))])):L("",!0),e("div",Yt,[(i(!0),c(O,null,F(b.value,D=>(i(),c("div",{key:D.id,class:"module-card"},[e("div",Wt,[w(P,{name:D.icon,size:"md",class:"card-icon"},null,8,["name"]),e("h5",qt,I(D.name),1),e("p",At,I(D.description),1)]),e("div",Ht,[e("button",{class:"enable-btn",onClick:j=>_(D)},[w(P,{name:"plus",size:"sm"}),V[9]||(V[9]=e("span",null,"启用",-1))],8,Gt)])]))),128)),(i(!0),c(O,null,F(v.value,D=>(i(),c("div",{key:D.id,class:"module-card custom-module"},[e("div",Kt,[w(P,{name:D.icon,size:"md",class:"card-icon"},null,8,["name"]),e("h5",Jt,I(D.name),1),e("p",Xt,I(D.description),1)]),e("div",Qt,[e("div",Zt,[e("button",{class:"enable-btn",onClick:j=>_(D)},[w(P,{name:"plus",size:"sm"}),V[10]||(V[10]=e("span",null,"启用",-1))],8,es),e("button",{class:"delete-btn",onClick:j=>Q(D.id),title:"删除自定义模块"},[w(P,{name:"delete",size:"sm"})],8,ts)])])]))),128)),e("div",{class:R(["module-card custom-creator",{disabled:f.value.length>=3}]),onClick:z},[e("div",ss,[w(P,{name:"plus",size:"md",class:"card-icon creator-icon"}),V[11]||(V[11]=e("h5",{class:"card-title"},"创建自定义模块",-1)),e("p",os,I(f.value.length>=3?"已达到创建上限，请删除后再创建":"创建个性化的简历模块"),1)]),e("div",ns,[e("span",ls,I(f.value.length)+"/3",1)])],2)])])]),e("div",as,[e("button",{class:"cancel-btn",onClick:V[1]||(V[1]=D=>S.$emit("close"))}," 取消 "),e("button",{class:"confirm-btn",onClick:l}," 确定 ")])]),r.value?(i(),W(yt,{key:0,onConfirm:oe,onCancel:V[3]||(V[3]=D=>r.value=!1)})):L("",!0)]))}},ye=N(is,[["__scopeId","data-v-1f1957ec"]]),ds={class:"module-navigation"},rs={class:"nav-header"},cs={key:0,class:"empty-state"},us=["onClick"],ps={class:"module-content"},ms={class:"module-header"},vs={class:"module-name-wrapper"},_s={key:0,class:"required-mark"},fs={class:"module-name"},gs={__name:"ModuleNavigation",setup(x){const M=T(!1),g=T(null);let $=null;const s=_e(),p=A(()=>{const C=s.currentModuleId.value;return console.log("ModuleNavigation - 当前模块ID:",C),C}),r=A(()=>{console.log("ModuleNavigation - 计算 allModules");const C=s.moduleConfigs.value;return console.log("ModuleNavigation - moduleConfigs.value 结果:",C),C||[]}),u=A(()=>{var a;const C=r.value;if(console.log("ModuleNavigation - allModules.value:",C),!Array.isArray(C))return console.warn("ModuleNavigation - modules 不是数组:",C),[];const _=C.filter(o=>o&&o.enabled),n=(((a=s.customModules)==null?void 0:a.value)||[]).filter(o=>o&&o.enabled),l=[..._,...n].sort((o,y)=>(o.order||0)-(y.order||0));return console.log("ModuleNavigation - 启用的模块数量:",l.length),console.log("ModuleNavigation - 启用的模块:",l.map(o=>`${o.name}(${o.order})`)),l}),m=(C,_)=>{if(console.log("ModuleNavigation - selectModule 被调用:",C),!C){console.warn("ModuleNavigation - 模块对象为空");return}if(!C.enabled){console.warn("ModuleNavigation - 尝试选择已禁用的模块:",C.name);return}console.log("ModuleNavigation - 选择模块:",C.id,C.name),console.log("ModuleNavigation - 模块类型:",C.type),console.log("ModuleNavigation - 是否为自定义模块:",C.id.startsWith("custom_")),s.setCurrentModule(C.id),k(_),setTimeout(()=>{console.log("ModuleNavigation - 设置后的当前模块ID:",s.currentModuleId.value)},100)},k=async C=>{$&&clearTimeout($),$=setTimeout(async()=>{if(await ie(),!g.value)return;const _=g.value,d=_.querySelectorAll(".nav-item");if(!d||d.length===0)return;const n=d[C];if(!n)return;const t=_.getBoundingClientRect(),l=n.getBoundingClientRect(),a=l.left-t.left+_.scrollLeft,o=l.right-t.left+_.scrollLeft,y=_.clientWidth,B=120;if(a-_.scrollLeft<B){const oe=Math.max(0,a-B);h(_,oe)}else if(o-_.scrollLeft>y-B){const oe=o-y+B;h(_,oe)}},50)},h=(C,_)=>{C.scrollTo({left:_,behavior:"smooth"})},b=()=>{M.value=!0},v=()=>{M.value=!1},f=C=>{s.moduleConfigs.value=C,v()};return(C,_)=>(i(),c("div",ds,[e("div",rs,[_[1]||(_[1]=e("h3",{class:"nav-title"},"简历模块",-1)),e("button",{class:"manage-btn",onClick:b,title:"管理模块"},[w(P,{name:"plus",size:"sm"}),_[0]||(_[0]=e("span",{class:"manage-text"},"管理模块",-1))])]),e("div",{ref_key:"navListRef",ref:g,class:"nav-list"},[u.value.length===0?(i(),c("div",cs,_[2]||(_[2]=[e("p",{class:"empty-text"},"正在加载模块...",-1)]))):L("",!0),(i(!0),c(O,null,F(u.value,(d,n)=>(i(),c("div",{key:d.id,class:R(["nav-item",{active:p.value===d.id},{disabled:!d.enabled}]),onClick:t=>m(d,n)},[e("div",ps,[e("div",ms,[w(P,{name:d.icon,size:"sm",class:"module-icon"},null,8,["name"]),e("div",vs,[d.required?(i(),c("span",_s,"*")):L("",!0),e("span",fs,I(d.name),1)])])])],10,us))),128))],512),M.value?(i(),W(ye,{key:0,modules:r.value,onClose:v,onUpdate:f},null,8,["modules"])):L("",!0)]))}},hs=N(gs,[["__scopeId","data-v-9b9bd4dc"]]),bs={class:"education-form"},$s={key:0,class:"education-list"},ys={class:"education-header"},ks={class:"form-group"},Cs=["onUpdate:modelValue"],Vs={class:"form-group"},ws=["onUpdate:modelValue"],xs=["onClick"],Ds={class:"education-details"},Us={class:"form-group"},Ms={class:"date-range-group"},Ss={class:"date-picker-wrapper"},Is=["onUpdate:modelValue","onClick"],Es={key:0,class:"date-picker-dropdown"},Ts={class:"date-picker-header"},zs={class:"month-grid"},Ls=["onClick"],Ps={class:"date-picker-wrapper"},Os=["onUpdate:modelValue","onClick"],Fs={key:0,class:"date-picker-dropdown"},js={class:"date-picker-header"},Bs={class:"month-grid"},Ns=["onClick"],Rs=["onClick"],Ys={class:"form-group"},Ws={class:"degree-group"},qs=["onUpdate:modelValue"],As=["onUpdate:modelValue"],Hs={class:"form-group"},Gs={key:1,class:"empty-state"},Ks={class:"form-actions"},Js=["disabled"],Xs={key:0,class:"limit-hint"},Qs={__name:"EducationForm",props:{data:{type:Object,default:()=>({education:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({education:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,n=>{n&&n.education?s.education=[...n.education]:s.education=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{education:[...s.education]})},300)},k=()=>{s.education.length>=5||(s.education.push({school:"",major:"",startDate:"",endDate:"",degree:"",customDegree:"",description:""}),m())},h=n=>{r.value=n;const[t,l]=n.split("_"),a=l==="start"?s.education[t].startDate:s.education[t].endDate;if(a&&a!=="至今"){const o=parseInt(a.split("-")[0]);o&&(u.value=o)}},b=n=>{u.value+=n},v=(n,t)=>{const[l,a]=n.split("_"),o=a==="start"?s.education[l].startDate:s.education[l].endDate;if(!o||o==="至今")return!1;const[y,B]=o.split("-");return parseInt(y)===u.value&&parseInt(B)===t},f=(n,t,l)=>{const a=`${t}-${String(l).padStart(2,"0")}`,[o,y]=n.split("_");y==="start"?s.education[o].startDate=a:s.education[o].endDate=a,r.value="",m()},C=n=>{const[t]=n.split("_");s.education[t].endDate="至今",r.value="",m()},_=n=>{n.target.closest(".date-picker-wrapper")||(r.value="")};G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)});const d=n=>{s.education.splice(n,1),m()};return(n,t)=>(i(),c("div",bs,[t[13]||(t[13]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"教育经历"),e("p",{class:"form-desc"},"添加您的教育背景信息")],-1)),s.education&&s.education.length>0?(i(),c("div",$s,[(i(!0),c(O,null,F(s.education,(l,a)=>(i(),c("div",{key:a,class:"education-item"},[e("div",ys,[e("div",ks,[t[4]||(t[4]=e("label",{class:"form-label"},"学校",-1)),U(e("input",{"onUpdate:modelValue":o=>l.school=o,type:"text",class:"form-input",placeholder:"如：北京大学",onInput:m},null,40,Cs),[[E,l.school]])]),e("div",Vs,[t[5]||(t[5]=e("label",{class:"form-label"},"专业",-1)),U(e("input",{"onUpdate:modelValue":o=>l.major=o,type:"text",class:"form-input",placeholder:"如：市场营销",onInput:m},null,40,ws),[[E,l.major]])]),e("button",{class:"delete-btn",onClick:o=>d(a),title:"删除教育经历"}," × ",8,xs)]),e("div",Ds,[e("div",Us,[t[7]||(t[7]=e("label",{class:"form-label"},"就读时间",-1)),e("div",Ms,[e("div",Ss,[U(e("input",{"onUpdate:modelValue":o=>l.startDate=o,type:"text",class:"date-input",placeholder:"2020-09",readonly:"",onClick:o=>h(`${a}_start`)},null,8,Is),[[E,l.startDate]]),r.value===`${a}_start`?(i(),c("div",Es,[e("div",Ts,[e("button",{type:"button",onClick:t[0]||(t[0]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[1]||(t[1]=o=>b(1))},"›")]),e("div",zs,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:v(`${a}_start`,o)}]),onClick:y=>f(`${a}_start`,u.value,o)},I(o)+"月 ",11,Ls)),64))])])):L("",!0)]),t[6]||(t[6]=e("span",{class:"date-separator"},"至",-1)),e("div",Ps,[U(e("input",{"onUpdate:modelValue":o=>l.endDate=o,type:"text",class:"date-input",placeholder:"2024-06 或 至今",readonly:"",onClick:o=>h(`${a}_end`)},null,8,Os),[[E,l.endDate]]),r.value===`${a}_end`?(i(),c("div",Fs,[e("div",js,[e("button",{type:"button",onClick:t[2]||(t[2]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[3]||(t[3]=o=>b(1))},"›")]),e("div",Bs,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:v(`${a}_end`,o)}]),onClick:y=>f(`${a}_end`,u.value,o)},I(o)+"月 ",11,Ns)),64)),e("button",{type:"button",class:"month-btn present-btn",onClick:o=>C(`${a}_end`)}," 至今 ",8,Rs)])])):L("",!0)])])]),e("div",Ys,[t[9]||(t[9]=e("label",{class:"form-label"},"学历",-1)),e("div",Ws,[U(e("select",{"onUpdate:modelValue":o=>l.degree=o,class:"degree-select",onChange:m},t[8]||(t[8]=[ae('<option value="" data-v-1ce03488>选择学历</option><option value="初中" data-v-1ce03488>初中</option><option value="中专" data-v-1ce03488>中专</option><option value="高中" data-v-1ce03488>高中</option><option value="大专" data-v-1ce03488>大专</option><option value="本科" data-v-1ce03488>本科</option><option value="硕士" data-v-1ce03488>硕士</option><option value="博士" data-v-1ce03488>博士</option><option value="MBA" data-v-1ce03488>MBA</option><option value="custom" data-v-1ce03488>自定义</option>',10)]),40,qs),[[re,l.degree]]),l.degree==="custom"?U((i(),c("input",{key:0,"onUpdate:modelValue":o=>l.customDegree=o,type:"text",class:"custom-degree-input",placeholder:"请输入自定义学历",onInput:m},null,40,As)),[[E,l.customDegree]]):L("",!0)])]),e("div",Hs,[t[10]||(t[10]=e("label",{class:"form-label"},"教育经历",-1)),w(te,{modelValue:l.description,"onUpdate:modelValue":[o=>l.description=o,m],placeholder:"请输入教育经历详情，如：主修课程、获得荣誉、参与项目等...","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])])])]))),128))])):(i(),c("div",Gs,t[11]||(t[11]=[e("p",null,"暂无教育经历信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加教育经历",-1)]))),e("div",Ks,[e("button",{class:"add-btn",onClick:k,disabled:s.education.length>=5},[w(P,{name:"plus",size:"sm"}),t[12]||(t[12]=e("span",null,"添加教育经历",-1))],8,Js),s.education.length>=5?(i(),c("p",Xs," 最多可添加5个教育经历 ")):L("",!0)])]))}},Zs=N(Qs,[["__scopeId","data-v-1ce03488"]]),eo={class:"work-experience-form"},to={key:0,class:"work-list"},so={class:"work-header"},oo={class:"form-group"},no=["onUpdate:modelValue"],lo={class:"form-group"},ao=["onUpdate:modelValue"],io=["onClick"],ro={class:"work-details"},co={class:"form-group"},uo=["onUpdate:modelValue"],po={class:"form-group"},mo={class:"date-range-group"},vo={class:"date-picker-wrapper"},_o=["onUpdate:modelValue","onClick"],fo={key:0,class:"date-picker-dropdown"},go={class:"date-picker-header"},ho={class:"month-grid"},bo=["onClick"],$o={class:"date-picker-wrapper"},yo=["onUpdate:modelValue","onClick"],ko={key:0,class:"date-picker-dropdown"},Co={class:"date-picker-header"},Vo={class:"month-grid"},wo=["onClick"],xo=["onClick"],Do={class:"form-group"},Uo={key:1,class:"empty-state"},Mo={class:"form-actions"},So=["disabled"],Io={key:0,class:"limit-hint"},Eo={__name:"WorkExperienceForm",props:{data:{type:Object,default:()=>({workExperience:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({workExperience:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,n=>{n&&n.workExperience?s.workExperience=[...n.workExperience]:s.workExperience=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{workExperience:[...s.workExperience]})},300)},k=()=>{s.workExperience.length>=10||(s.workExperience.push({company:"",position:"",department:"",startDate:"",endDate:"",description:""}),m())},h=n=>{r.value=n;const[t,l]=n.split("_"),a=l==="start"?s.workExperience[t].startDate:s.workExperience[t].endDate;if(a&&a!=="至今"){const o=parseInt(a.split("-")[0]);o&&(u.value=o)}},b=n=>{u.value+=n},v=(n,t)=>{const[l,a]=n.split("_"),o=a==="start"?s.workExperience[l].startDate:s.workExperience[l].endDate;if(!o||o==="至今")return!1;const[y,B]=o.split("-");return parseInt(y)===u.value&&parseInt(B)===t},f=(n,t,l)=>{const a=`${t}-${String(l).padStart(2,"0")}`,[o,y]=n.split("_");y==="start"?s.workExperience[o].startDate=a:s.workExperience[o].endDate=a,r.value="",m()},C=n=>{const[t]=n.split("_");s.workExperience[t].endDate="至今",r.value="",m()},_=n=>{n.target.closest(".date-picker-wrapper")||(r.value="")};G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)});const d=n=>{s.workExperience.splice(n,1),m()};return(n,t)=>(i(),c("div",eo,[t[12]||(t[12]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"工作经历"),e("p",{class:"form-desc"},"添加您的工作经验和职业发展历程")],-1)),s.workExperience&&s.workExperience.length>0?(i(),c("div",to,[(i(!0),c(O,null,F(s.workExperience,(l,a)=>(i(),c("div",{key:a,class:"work-item"},[e("div",so,[e("div",oo,[t[4]||(t[4]=e("label",{class:"form-label"},"公司名称",-1)),U(e("input",{"onUpdate:modelValue":o=>l.company=o,type:"text",class:"form-input",placeholder:"如：火花科技有限公司",onInput:m},null,40,no),[[E,l.company]])]),e("div",lo,[t[5]||(t[5]=e("label",{class:"form-label"},"职位名称",-1)),U(e("input",{"onUpdate:modelValue":o=>l.position=o,type:"text",class:"form-input",placeholder:"如：软件工程师",onInput:m},null,40,ao),[[E,l.position]])]),e("button",{class:"delete-btn",onClick:o=>d(a),title:"删除工作经历"}," × ",8,io)]),e("div",ro,[e("div",co,[t[6]||(t[6]=e("label",{class:"form-label"},"部门",-1)),U(e("input",{"onUpdate:modelValue":o=>l.department=o,type:"text",class:"form-input",placeholder:"如：技术开发部",onInput:m},null,40,uo),[[E,l.department]])]),e("div",po,[t[8]||(t[8]=e("label",{class:"form-label"},"在职时间",-1)),e("div",mo,[e("div",vo,[U(e("input",{"onUpdate:modelValue":o=>l.startDate=o,type:"text",class:"date-input",placeholder:"2020-01",readonly:"",onClick:o=>h(`${a}_start`)},null,8,_o),[[E,l.startDate]]),r.value===`${a}_start`?(i(),c("div",fo,[e("div",go,[e("button",{type:"button",onClick:t[0]||(t[0]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[1]||(t[1]=o=>b(1))},"›")]),e("div",ho,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:v(`${a}_start`,o)}]),onClick:y=>f(`${a}_start`,u.value,o)},I(o)+"月 ",11,bo)),64))])])):L("",!0)]),t[7]||(t[7]=e("span",{class:"date-separator"},"至",-1)),e("div",$o,[U(e("input",{"onUpdate:modelValue":o=>l.endDate=o,type:"text",class:"date-input",placeholder:"2024-06 或 至今",readonly:"",onClick:o=>h(`${a}_end`)},null,8,yo),[[E,l.endDate]]),r.value===`${a}_end`?(i(),c("div",ko,[e("div",Co,[e("button",{type:"button",onClick:t[2]||(t[2]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[3]||(t[3]=o=>b(1))},"›")]),e("div",Vo,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:v(`${a}_end`,o)}]),onClick:y=>f(`${a}_end`,u.value,o)},I(o)+"月 ",11,wo)),64)),e("button",{type:"button",class:"month-btn present-btn",onClick:o=>C(`${a}_end`)}," 至今 ",8,xo)])])):L("",!0)])])]),e("div",Do,[t[9]||(t[9]=e("label",{class:"form-label"},"工作描述",-1)),w(te,{modelValue:l.description,"onUpdate:modelValue":[o=>l.description=o,m],placeholder:"请输入工作职责、成就和贡献...","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])])])]))),128))])):(i(),c("div",Uo,t[10]||(t[10]=[e("p",null,"暂无工作经历信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加工作经历",-1)]))),e("div",Mo,[e("button",{class:"add-btn",onClick:k,disabled:s.workExperience.length>=10},[w(P,{name:"plus",size:"sm"}),t[11]||(t[11]=e("span",null,"添加工作经历",-1))],8,So),s.workExperience.length>=10?(i(),c("p",Io," 最多可添加10个工作经历 ")):L("",!0)])]))}},To=N(Eo,[["__scopeId","data-v-fb0e1f13"]]),zo={class:"skills-form"},Lo={key:0,class:"skills-list"},Po={class:"skill-header"},Oo=["onUpdate:modelValue"],Fo=["onClick"],jo={key:0,class:"skill-level-control"},Bo={class:"level-input-row"},No={class:"item-display-mode"},Ro={class:"mode-option"},Yo=["onUpdate:modelValue","onChange"],Wo={class:"mode-option"},qo=["onUpdate:modelValue","onChange"],Ao={class:"level-label"},Ho=["onUpdate:modelValue","onInput"],Go={class:"skill-progress"},Ko={key:1,class:"skill-proficiency-control"},Jo={class:"proficiency-input-row"},Xo={class:"item-display-mode"},Qo={class:"mode-option"},Zo=["onUpdate:modelValue","onChange"],en={class:"mode-option"},tn=["onUpdate:modelValue","onChange"],sn=["onUpdate:modelValue","onChange"],on={class:"proficiency-display"},nn={class:"proficiency-text"},ln={class:"proficiency-bar"},an={key:1,class:"empty-state"},dn={class:"form-actions"},rn={__name:"SkillsForm",props:{data:{type:Object,default:()=>({skills:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({skills:[]});let p=null;const r={精通:95,擅长:82,熟练:67,良好:50,一般:30},u=_=>_>=90?"精通":_>=75?"擅长":_>=60?"熟练":_>=40?"良好":(_>=20,"一般"),m=_=>({精通:"expert",擅长:"skilled",熟练:"proficient",良好:"good",一般:"basic"})[_]||"basic";H(()=>g.data,_=>{_&&_.skills?s.skills=_.skills.map(d=>({...d,displayMode:d.displayMode||"percentage",proficiency:d.proficiency||u(d.level||50)})):s.skills=[]},{immediate:!0,deep:!0});const k=_=>{const d=s.skills[_];d.displayMode==="proficiency"&&!d.proficiency&&(d.proficiency=u(d.level||50)),h()},h=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{skills:[...s.skills]})},300)},b=_=>{const d=s.skills[_];d.proficiency=u(d.level),h()},v=_=>{const d=s.skills[_];d.level=r[d.proficiency]||50,h()},f=()=>{const _={name:"",level:50,proficiency:"良好",displayMode:"percentage"};s.skills.push(_),h()},C=_=>{s.skills.splice(_,1),h()};return(_,d)=>(i(),c("div",zo,[d[8]||(d[8]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"技能特长"),e("p",{class:"form-desc"},"展示您的专业技能和水平")],-1)),s.skills&&s.skills.length>0?(i(),c("div",Lo,[(i(!0),c(O,null,F(s.skills,(n,t)=>(i(),c("div",{key:t,class:"skill-item"},[e("div",Po,[U(e("input",{"onUpdate:modelValue":l=>n.name=l,type:"text",class:"skill-name-input",placeholder:"技能名称",onInput:h},null,40,Oo),[[E,n.name]]),e("button",{class:"delete-btn",onClick:l=>C(t),title:"删除技能"}," × ",8,Fo)]),n.displayMode==="percentage"?(i(),c("div",jo,[e("div",Bo,[e("div",No,[e("label",Ro,[U(e("input",{"onUpdate:modelValue":l=>n.displayMode=l,type:"radio",value:"percentage",onChange:l=>k(t)},null,40,Yo),[[ve,n.displayMode]]),d[0]||(d[0]=e("span",null,"百分比",-1))]),e("label",Wo,[U(e("input",{"onUpdate:modelValue":l=>n.displayMode=l,type:"radio",value:"proficiency",onChange:l=>k(t)},null,40,qo),[[ve,n.displayMode]]),d[1]||(d[1]=e("span",null,"熟练程度",-1))])]),e("label",Ao,"熟练度: "+I(n.level)+"%",1)]),U(e("input",{"onUpdate:modelValue":l=>n.level=l,type:"range",min:"0",max:"100",step:"5",class:"level-slider",onInput:l=>b(t)},null,40,Ho),[[E,n.level,void 0,{number:!0}]]),e("div",Go,[e("div",{class:"progress-bar",style:de({width:n.level+"%"})},null,4)])])):(i(),c("div",Ko,[e("div",Jo,[e("div",Xo,[e("label",Qo,[U(e("input",{"onUpdate:modelValue":l=>n.displayMode=l,type:"radio",value:"percentage",onChange:l=>k(t)},null,40,Zo),[[ve,n.displayMode]]),d[2]||(d[2]=e("span",null,"百分比",-1))]),e("label",en,[U(e("input",{"onUpdate:modelValue":l=>n.displayMode=l,type:"radio",value:"proficiency",onChange:l=>k(t)},null,40,tn),[[ve,n.displayMode]]),d[3]||(d[3]=e("span",null,"熟练程度",-1))])]),d[5]||(d[5]=e("label",{class:"proficiency-label"},"熟练程度：",-1)),U(e("select",{"onUpdate:modelValue":l=>n.proficiency=l,class:"proficiency-select",onChange:l=>v(t)},d[4]||(d[4]=[ae('<option value="" data-v-85db4749>请选择熟练程度</option><option value="精通" data-v-85db4749>精通</option><option value="擅长" data-v-85db4749>擅长</option><option value="熟练" data-v-85db4749>熟练</option><option value="良好" data-v-85db4749>良好</option><option value="一般" data-v-85db4749>一般</option>',6)]),40,sn),[[re,n.proficiency]])]),e("div",on,[e("span",nn,I(n.proficiency||"未设置"),1),e("div",ln,[e("div",{class:R(["proficiency-fill",m(n.proficiency)]),style:de({width:n.level+"%"})},null,6)])])]))]))),128))])):(i(),c("div",an,d[6]||(d[6]=[e("p",null,"暂无技能信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加您的技能",-1)]))),e("div",dn,[e("button",{class:"add-btn",onClick:f},[w(P,{name:"plus",size:"sm"}),d[7]||(d[7]=e("span",null,"添加技能",-1))])])]))}},cn=N(rn,[["__scopeId","data-v-85db4749"]]),un={class:"language-form"},pn={key:0,class:"languages-list"},mn={class:"language-controls"},vn=["onUpdate:modelValue"],_n=["onUpdate:modelValue"],fn=["onClick"],gn={class:"language-details"},hn=["onUpdate:modelValue"],bn={key:1,class:"empty-state"},$n={class:"form-actions"},yn={__name:"LanguageForm",props:{data:{type:Object,default:()=>({languages:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({languages:[]});let p=null;H(()=>g.data,k=>{k&&k.languages?s.languages=[...k.languages]:s.languages=[]},{immediate:!0,deep:!0});const r=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{languages:[...s.languages]})},300)},u=()=>{s.languages.push({name:"",level:"",certificate:"",description:""}),r()},m=k=>{s.languages.splice(k,1),r()};return(k,h)=>(i(),c("div",un,[h[3]||(h[3]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"语言能力"),e("p",{class:"form-desc"},"展示您的语言水平和相关证书")],-1)),s.languages&&s.languages.length>0?(i(),c("div",pn,[(i(!0),c(O,null,F(s.languages,(b,v)=>(i(),c("div",{key:v,class:"language-item"},[e("div",mn,[U(e("input",{"onUpdate:modelValue":f=>b.name=f,type:"text",class:"language-name-input",placeholder:"语言名称（如：英语、日语）",onInput:r},null,40,vn),[[E,b.name]]),U(e("select",{"onUpdate:modelValue":f=>b.level=f,class:"language-level-select",onChange:r},h[0]||(h[0]=[ae('<option value="" data-v-991da186>选择水平</option><option value="初级" data-v-991da186>初级</option><option value="中级" data-v-991da186>中级</option><option value="高级" data-v-991da186>高级</option><option value="母语" data-v-991da186>母语</option><option value="流利" data-v-991da186>流利</option>',6)]),40,_n),[[re,b.level]]),e("button",{class:"delete-btn",onClick:f=>m(v),title:"删除语言"}," × ",8,fn)]),e("div",gn,[U(e("input",{"onUpdate:modelValue":f=>b.certificate=f,type:"text",class:"certificate-input",placeholder:"相关证书（如：CET-6、JLPT N2）",onInput:r},null,40,hn),[[E,b.certificate]]),w(te,{modelValue:b.description,"onUpdate:modelValue":[f=>b.description=f,r],placeholder:"详细描述（如：能够进行商务交流、阅读技术文档等）","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])])]))),128))])):(i(),c("div",bn,h[1]||(h[1]=[e("p",null,"暂无语言能力信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加您的语言能力",-1)]))),e("div",$n,[e("button",{class:"add-btn",onClick:u},[w(P,{name:"plus",size:"sm"}),h[2]||(h[2]=e("span",null,"添加语言",-1))])])]))}},kn=N(yn,[["__scopeId","data-v-991da186"]]),Cn={class:"award-form"},Vn={key:0,class:"awards-list"},wn={class:"award-header"},xn=["onUpdate:modelValue"],Dn={class:"date-picker-wrapper"},Un=["onUpdate:modelValue","onClick"],Mn={key:0,class:"date-picker-dropdown"},Sn={class:"date-picker-header"},In={class:"month-grid"},En=["onClick"],Tn=["onClick"],zn={class:"award-details"},Ln=["onUpdate:modelValue"],Pn=["onUpdate:modelValue"],On={key:1,class:"empty-state"},Fn={class:"form-actions"},jn={__name:"AwardForm",props:{data:{type:Object,default:()=>({awards:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({awards:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,d=>{d&&d.awards?s.awards=[...d.awards]:s.awards=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{awards:[...s.awards]})},300)},k=()=>{s.awards.push({name:"",date:"",organization:"",level:"",description:""}),m()},h=d=>{s.awards.splice(d,1),m()},b=d=>{r.value=d;const n=s.awards[d].date;if(n){const t=parseInt(n.split("-")[0]);t&&(u.value=t)}},v=d=>{u.value+=d},f=(d,n)=>{const t=s.awards[d].date;if(!t)return!1;const[l,a]=t.split("-");return parseInt(l)===u.value&&parseInt(a)===n},C=(d,n,t)=>{const l=`${n}-${t.toString().padStart(2,"0")}`;s.awards[d].date=l,r.value="",m()},_=d=>{d.target.closest(".date-picker-wrapper")||(r.value="")};return G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)}),(d,n)=>(i(),c("div",Cn,[n[5]||(n[5]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"获奖荣誉"),e("p",{class:"form-desc"},"展示您获得的奖项和荣誉")],-1)),s.awards&&s.awards.length>0?(i(),c("div",Vn,[(i(!0),c(O,null,F(s.awards,(t,l)=>(i(),c("div",{key:l,class:"award-item"},[e("div",wn,[U(e("input",{"onUpdate:modelValue":a=>t.name=a,type:"text",class:"award-name-input",placeholder:"奖项名称",onInput:m},null,40,xn),[[E,t.name]]),e("div",Dn,[U(e("input",{"onUpdate:modelValue":a=>t.date=a,type:"text",class:"award-date-input",placeholder:"获奖时间",readonly:"",onClick:a=>b(l)},null,8,Un),[[E,t.date]]),r.value===l?(i(),c("div",Mn,[e("div",Sn,[e("button",{type:"button",onClick:n[0]||(n[0]=a=>v(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:n[1]||(n[1]=a=>v(1))},"›")]),e("div",In,[(i(),c(O,null,F(12,a=>e("button",{key:a,type:"button",class:R(["month-btn",{active:f(l,a)}]),onClick:o=>C(l,u.value,a)},I(a)+"月 ",11,En)),64))])])):L("",!0)]),e("button",{class:"delete-btn",onClick:a=>h(l),title:"删除奖项"}," × ",8,Tn)]),e("div",zn,[U(e("input",{"onUpdate:modelValue":a=>t.organization=a,type:"text",class:"organization-input",placeholder:"颁发机构",onInput:m},null,40,Ln),[[E,t.organization]]),U(e("select",{"onUpdate:modelValue":a=>t.level=a,class:"level-select",onChange:m},n[2]||(n[2]=[ae('<option value="" data-v-5224a01d>选择级别</option><option value="国际级" data-v-5224a01d>国际级</option><option value="国家级" data-v-5224a01d>国家级</option><option value="省级" data-v-5224a01d>省级</option><option value="市级" data-v-5224a01d>市级</option><option value="校级" data-v-5224a01d>校级</option><option value="公司级" data-v-5224a01d>公司级</option><option value="其他" data-v-5224a01d>其他</option>',8)]),40,Pn),[[re,t.level]])]),w(te,{modelValue:t.description,"onUpdate:modelValue":[a=>t.description=a,m],placeholder:"详细描述（如：获奖原因、竞争情况等）","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])]))),128))])):(i(),c("div",On,n[3]||(n[3]=[e("p",null,"暂无获奖荣誉信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加您的获奖荣誉",-1)]))),e("div",Fn,[e("button",{class:"add-btn",onClick:k},[w(P,{name:"plus",size:"sm"}),n[4]||(n[4]=e("span",null,"添加奖项",-1))])])]))}},Bn=N(jn,[["__scopeId","data-v-5224a01d"]]),Nn={class:"hobbies-form"},Rn={key:0,class:"hobbies-list"},Yn={class:"hobby-header"},Wn=["onUpdate:modelValue"],qn=["onUpdate:modelValue"],An=["onClick"],Hn={key:1,class:"empty-state"},Gn={class:"form-actions"},Kn={__name:"HobbiesForm",props:{data:{type:Object,default:()=>({hobbies:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({hobbies:[]});let p=null;H(()=>g.data,k=>{k&&k.hobbies?s.hobbies=[...k.hobbies]:s.hobbies=[]},{immediate:!0,deep:!0});const r=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{hobbies:[...s.hobbies]})},300)},u=()=>{s.hobbies.push({name:"",level:"",description:""}),r()},m=k=>{s.hobbies.splice(k,1),r()};return(k,h)=>(i(),c("div",Nn,[h[3]||(h[3]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"兴趣爱好"),e("p",{class:"form-desc"},"展示您的个人兴趣和业余爱好")],-1)),s.hobbies&&s.hobbies.length>0?(i(),c("div",Rn,[(i(!0),c(O,null,F(s.hobbies,(b,v)=>(i(),c("div",{key:v,class:"hobby-item"},[e("div",Yn,[U(e("input",{"onUpdate:modelValue":f=>b.name=f,type:"text",class:"hobby-name-input",placeholder:"兴趣爱好名称",onInput:r},null,40,Wn),[[E,b.name]]),U(e("select",{"onUpdate:modelValue":f=>b.level=f,class:"hobby-level-select",onChange:r},h[0]||(h[0]=[ae('<option value="" data-v-2f5e1901>选择水平</option><option value="初学者" data-v-2f5e1901>初学者</option><option value="爱好者" data-v-2f5e1901>爱好者</option><option value="熟练" data-v-2f5e1901>熟练</option><option value="专业" data-v-2f5e1901>专业</option>',5)]),40,qn),[[re,b.level]]),e("button",{class:"delete-btn",onClick:f=>m(v),title:"删除兴趣爱好"}," × ",8,An)]),w(te,{modelValue:b.description,"onUpdate:modelValue":[f=>b.description=f,r],placeholder:"详细描述（如：参与的活动、获得的成就等）","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])]))),128))])):(i(),c("div",Hn,h[1]||(h[1]=[e("p",null,"暂无兴趣爱好信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加您的兴趣爱好",-1)]))),e("div",Gn,[e("button",{class:"add-btn",onClick:u},[w(P,{name:"plus",size:"sm"}),h[2]||(h[2]=e("span",null,"添加兴趣爱好",-1))])])]))}},Jn=N(Kn,[["__scopeId","data-v-2f5e1901"]]),Xn={class:"self-evaluation-form"},Qn={class:"form-content"},Zn={__name:"SelfEvaluationForm",props:{data:{type:Object,default:()=>({content:""})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({content:""});let p=null;H(()=>g.data,u=>{u&&u.content?s.content=u.content:s.content=""},{immediate:!0,deep:!0});const r=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{content:s.content})},300)};return(u,m)=>(i(),c("div",Xn,[m[1]||(m[1]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"自我评价"),e("p",{class:"form-desc"},"简洁地描述您的个人特点和职业优势")],-1)),e("div",Qn,[w(te,{modelValue:s.content,"onUpdate:modelValue":[m[0]||(m[0]=k=>s.content=k),r],placeholder:"在这里写下您的自我评价...","min-height":"200px"},null,8,["modelValue"])])]))}},el=N(Zn,[["__scopeId","data-v-01000680"]]),tl={class:"internship-form"},sl={key:0,class:"internship-list"},ol={class:"internship-header"},nl={class:"form-group"},ll=["onUpdate:modelValue"],al={class:"form-group"},il=["onUpdate:modelValue"],dl=["onClick"],rl={class:"internship-details"},cl={class:"form-group"},ul={class:"date-range-group"},pl={class:"date-picker-wrapper"},ml=["onUpdate:modelValue","onClick"],vl={key:0,class:"date-picker-dropdown"},_l={class:"date-picker-header"},fl={class:"month-grid"},gl=["onClick"],hl={class:"date-picker-wrapper"},bl=["onUpdate:modelValue","onClick"],$l={key:0,class:"date-picker-dropdown"},yl={class:"date-picker-header"},kl={class:"month-grid"},Cl=["onClick"],Vl=["onClick"],wl={class:"form-group"},xl={key:1,class:"empty-state"},Dl={class:"form-actions"},Ul=["disabled"],Ml={__name:"InternshipForm",props:{data:{type:Object,default:()=>({internship:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({internship:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,n=>{n&&n.internship?s.internship=[...n.internship]:s.internship=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{internship:[...s.internship]})},300)},k=()=>{s.internship.length>=10||(s.internship.push({company:"",position:"",startDate:"",endDate:"",isOngoing:!1,description:""}),m())},h=n=>{r.value=n;const[t,l]=n.split("_"),a=l==="start"?s.internship[t].startDate:s.internship[t].endDate;if(a&&a!=="至今"){const o=parseInt(a.split("-")[0]);o&&(u.value=o)}},b=n=>{u.value+=n},v=(n,t,l)=>{const a=`${t}-${String(l).padStart(2,"0")}`,[o,y]=n.split("_");y==="start"?s.internship[o].startDate=a:(s.internship[o].endDate=a,s.internship[o].isOngoing=!1),r.value="",m()},f=n=>{const[t]=n.split("_");s.internship[t].endDate="至今",s.internship[t].isOngoing=!0,r.value="",m()},C=(n,t)=>{const[l,a]=n.split("_"),o=a==="start"?s.internship[l].startDate:s.internship[l].endDate;if(!o||o==="至今")return!1;const[y,B]=o.split("-");return parseInt(y)===u.value&&parseInt(B)===t},_=n=>{n.target.closest(".date-picker-wrapper")||(r.value="")};G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)});const d=n=>{s.internship.splice(n,1),m()};return(n,t)=>(i(),c("div",tl,[t[11]||(t[11]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"实习经历"),e("p",{class:"form-desc"},"添加您的实习经验和学习成果")],-1)),s.internship&&s.internship.length>0?(i(),c("div",sl,[(i(!0),c(O,null,F(s.internship,(l,a)=>(i(),c("div",{key:a,class:"internship-item"},[e("div",ol,[e("div",nl,[t[4]||(t[4]=e("label",{class:"form-label"},"公司名称",-1)),U(e("input",{"onUpdate:modelValue":o=>l.company=o,type:"text",class:"form-input",placeholder:"如：火花科技有限公司",onInput:m},null,40,ll),[[E,l.company]])]),e("div",al,[t[5]||(t[5]=e("label",{class:"form-label"},"职位名称",-1)),U(e("input",{"onUpdate:modelValue":o=>l.position=o,type:"text",class:"form-input",placeholder:"如：行政实习生",onInput:m},null,40,il),[[E,l.position]])]),e("button",{class:"delete-btn",onClick:o=>d(a),title:"删除实习经历"}," × ",8,dl)]),e("div",rl,[e("div",cl,[t[7]||(t[7]=e("label",{class:"form-label"},"实习时间",-1)),e("div",ul,[e("div",pl,[U(e("input",{"onUpdate:modelValue":o=>l.startDate=o,type:"text",class:"date-input",placeholder:"2023-01",readonly:"",onClick:o=>h(`${a}_start`)},null,8,ml),[[E,l.startDate]]),r.value===`${a}_start`?(i(),c("div",vl,[e("div",_l,[e("button",{type:"button",onClick:t[0]||(t[0]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[1]||(t[1]=o=>b(1))},"›")]),e("div",fl,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:C(`${a}_start`,o)}]),onClick:y=>v(`${a}_start`,u.value,o)},I(o)+"月 ",11,gl)),64))])])):L("",!0)]),t[6]||(t[6]=e("span",{class:"date-separator"},"至",-1)),e("div",hl,[U(e("input",{"onUpdate:modelValue":o=>l.endDate=o,type:"text",class:"date-input",placeholder:"2023-06 或 至今",readonly:"",onClick:o=>h(`${a}_end`)},null,8,bl),[[E,l.endDate]]),r.value===`${a}_end`?(i(),c("div",$l,[e("div",yl,[e("button",{type:"button",onClick:t[2]||(t[2]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[3]||(t[3]=o=>b(1))},"›")]),e("div",kl,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:C(`${a}_end`,o)}]),onClick:y=>v(`${a}_end`,u.value,o)},I(o)+"月 ",11,Cl)),64)),e("button",{type:"button",class:"month-btn present-btn",onClick:o=>f(`${a}_end`)}," 至今 ",8,Vl)])])):L("",!0)])])]),e("div",wl,[t[8]||(t[8]=e("label",{class:"form-label"},"实习描述",-1)),w(te,{modelValue:l.description,"onUpdate:modelValue":[o=>l.description=o,m],placeholder:"请输入实习工作内容、学习收获、项目经验等...","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])])])]))),128))])):(i(),c("div",xl,t[9]||(t[9]=[e("p",null,"暂无实习经历信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加实习经历",-1)]))),e("div",Dl,[e("button",{class:"add-btn",onClick:k,disabled:s.internship.length>=10},[w(P,{name:"plus",size:"sm"}),t[10]||(t[10]=e("span",null,"添加实习经历",-1))],8,Ul)])]))}},Sl=N(Ml,[["__scopeId","data-v-6b26d089"]]),Il={class:"training-form"},El={key:0,class:"training-list"},Tl={class:"training-header"},zl={class:"form-group"},Ll=["onUpdate:modelValue"],Pl={class:"form-group"},Ol=["onUpdate:modelValue"],Fl=["onClick"],jl={class:"training-details"},Bl={class:"form-group"},Nl={class:"date-range-group"},Rl={class:"date-picker-wrapper"},Yl=["onUpdate:modelValue","onClick"],Wl={key:0,class:"date-picker-dropdown"},ql={class:"date-picker-header"},Al={class:"month-grid"},Hl=["onClick"],Gl={class:"date-picker-wrapper"},Kl=["onUpdate:modelValue","onClick"],Jl={key:0,class:"date-picker-dropdown"},Xl={class:"date-picker-header"},Ql={class:"month-grid"},Zl=["onClick"],ea=["onClick"],ta={class:"form-group"},sa={key:1,class:"empty-state"},oa={class:"form-actions"},na=["disabled"],la={__name:"TrainingForm",props:{data:{type:Object,default:()=>({training:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({training:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,n=>{n&&n.training?s.training=[...n.training]:s.training=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{training:[...s.training]})},300)},k=()=>{s.training.length>=10||(s.training.push({course:"",company:"",startDate:"",endDate:"",isOngoing:!1,description:""}),m())},h=n=>{r.value=n;const[t,l]=n.split("_"),a=l==="start"?s.training[t].startDate:s.training[t].endDate;if(a&&a!=="至今"){const o=parseInt(a.split("-")[0]);o&&(u.value=o)}},b=n=>{u.value+=n},v=(n,t,l)=>{const a=`${t}-${String(l).padStart(2,"0")}`,[o,y]=n.split("_");y==="start"?s.training[o].startDate=a:(s.training[o].endDate=a,s.training[o].isOngoing=!1),r.value="",m()},f=n=>{const[t]=n.split("_");s.training[t].endDate="至今",s.training[t].isOngoing=!0,r.value="",m()},C=(n,t)=>{const[l,a]=n.split("_"),o=a==="start"?s.training[l].startDate:s.training[l].endDate;if(!o||o==="至今")return!1;const[y,B]=o.split("-");return parseInt(y)===u.value&&parseInt(B)===t},_=n=>{n.target.closest(".date-picker-wrapper")||(r.value="")};G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)});const d=n=>{s.training.splice(n,1),m()};return(n,t)=>(i(),c("div",Il,[t[11]||(t[11]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"培训经历"),e("p",{class:"form-desc"},"添加您参加的培训课程和学习经历")],-1)),s.training&&s.training.length>0?(i(),c("div",El,[(i(!0),c(O,null,F(s.training,(l,a)=>(i(),c("div",{key:a,class:"training-item"},[e("div",Tl,[e("div",zl,[t[4]||(t[4]=e("label",{class:"form-label"},"培训项目/课程",-1)),U(e("input",{"onUpdate:modelValue":o=>l.course=o,type:"text",class:"form-input",placeholder:"如：职场沟通技巧",onInput:m},null,40,Ll),[[E,l.course]])]),e("div",Pl,[t[5]||(t[5]=e("label",{class:"form-label"},"所在公司",-1)),U(e("input",{"onUpdate:modelValue":o=>l.company=o,type:"text",class:"form-input",placeholder:"可选，方便面试官对照经历",onInput:m},null,40,Ol),[[E,l.company]])]),e("button",{class:"delete-btn",onClick:o=>d(a),title:"删除培训经历"}," × ",8,Fl)]),e("div",jl,[e("div",Bl,[t[7]||(t[7]=e("label",{class:"form-label"},"培训时间",-1)),e("div",Nl,[e("div",Rl,[U(e("input",{"onUpdate:modelValue":o=>l.startDate=o,type:"text",class:"date-input",placeholder:"2023-01",readonly:"",onClick:o=>h(`${a}_start`)},null,8,Yl),[[E,l.startDate]]),r.value===`${a}_start`?(i(),c("div",Wl,[e("div",ql,[e("button",{type:"button",onClick:t[0]||(t[0]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[1]||(t[1]=o=>b(1))},"›")]),e("div",Al,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:C(`${a}_start`,o)}]),onClick:y=>v(`${a}_start`,u.value,o)},I(o)+"月 ",11,Hl)),64))])])):L("",!0)]),t[6]||(t[6]=e("span",{class:"date-separator"},"至",-1)),e("div",Gl,[U(e("input",{"onUpdate:modelValue":o=>l.endDate=o,type:"text",class:"date-input",placeholder:"2023-03 或 至今",readonly:"",onClick:o=>h(`${a}_end`)},null,8,Kl),[[E,l.endDate]]),r.value===`${a}_end`?(i(),c("div",Jl,[e("div",Xl,[e("button",{type:"button",onClick:t[2]||(t[2]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[3]||(t[3]=o=>b(1))},"›")]),e("div",Ql,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:C(`${a}_end`,o)}]),onClick:y=>v(`${a}_end`,u.value,o)},I(o)+"月 ",11,Zl)),64)),e("button",{type:"button",class:"month-btn present-btn",onClick:o=>f(`${a}_end`)}," 至今 ",8,ea)])])):L("",!0)])])]),e("div",ta,[t[8]||(t[8]=e("label",{class:"form-label"},"培训描述",-1)),w(te,{modelValue:l.description,"onUpdate:modelValue":[o=>l.description=o,m],placeholder:"请输入培训内容、学习收获、获得证书等...","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])])])]))),128))])):(i(),c("div",sa,t[9]||(t[9]=[e("p",null,"暂无培训经历信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加培训经历",-1)]))),e("div",oa,[e("button",{class:"add-btn",onClick:k,disabled:s.training.length>=10},[w(P,{name:"plus",size:"sm"}),t[10]||(t[10]=e("span",null,"添加培训经历",-1))],8,na)])]))}},aa=N(la,[["__scopeId","data-v-978958d9"]]),ia={class:"research-experience-form"},da={key:0,class:"research-list"},ra={class:"research-header"},ca={class:"form-group"},ua=["onUpdate:modelValue"],pa={class:"form-group"},ma=["onUpdate:modelValue"],va=["onClick"],_a={class:"research-details"},fa={class:"form-group"},ga=["onUpdate:modelValue"],ha={class:"form-group"},ba={class:"date-range-group"},$a={class:"date-picker-wrapper"},ya=["onUpdate:modelValue","onClick"],ka={key:0,class:"date-picker-dropdown"},Ca={class:"date-picker-header"},Va={class:"month-grid"},wa=["onClick"],xa={class:"date-picker-wrapper"},Da=["onUpdate:modelValue","onClick"],Ua={key:0,class:"date-picker-dropdown"},Ma={class:"date-picker-header"},Sa={class:"month-grid"},Ia=["onClick"],Ea=["onClick"],Ta={class:"form-group"},za={key:1,class:"empty-state"},La={class:"form-actions"},Pa=["disabled"],Oa={__name:"ResearchExperienceForm",props:{data:{type:Object,default:()=>({research:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({research:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,n=>{n&&n.research?s.research=[...n.research]:s.research=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{research:[...s.research]})},300)},k=()=>{s.research.length>=10||(s.research.push({topic:"",role:"",organization:"",startDate:"",endDate:"",isOngoing:!1,description:""}),m())},h=n=>{r.value=n;const[t,l]=n.split("_"),a=l==="start"?s.research[t].startDate:s.research[t].endDate;if(a&&a!=="至今"){const o=parseInt(a.split("-")[0]);o&&(u.value=o)}},b=n=>{u.value+=n},v=(n,t,l)=>{const a=`${t}-${String(l).padStart(2,"0")}`,[o,y]=n.split("_");y==="start"?s.research[o].startDate=a:(s.research[o].endDate=a,s.research[o].isOngoing=!1),r.value="",m()},f=n=>{const[t]=n.split("_");s.research[t].endDate="至今",s.research[t].isOngoing=!0,r.value="",m()},C=(n,t)=>{const[l,a]=n.split("_"),o=a==="start"?s.research[l].startDate:s.research[l].endDate;if(!o||o==="至今")return!1;const[y,B]=o.split("-");return parseInt(y)===u.value&&parseInt(B)===t},_=n=>{n.target.closest(".date-picker-wrapper")||(r.value="")};G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)});const d=n=>{s.research.splice(n,1),m()};return(n,t)=>(i(),c("div",ia,[t[12]||(t[12]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"研究经历"),e("p",{class:"form-desc"},"添加您参与的研究项目和学术活动")],-1)),s.research&&s.research.length>0?(i(),c("div",da,[(i(!0),c(O,null,F(s.research,(l,a)=>(i(),c("div",{key:a,class:"research-item"},[e("div",ra,[e("div",ca,[t[4]||(t[4]=e("label",{class:"form-label"},"研究课题/方向",-1)),U(e("input",{"onUpdate:modelValue":o=>l.topic=o,type:"text",class:"form-input",placeholder:"如：绿色社区创建方案体系研究",onInput:m},null,40,ua),[[E,l.topic]])]),e("div",pa,[t[5]||(t[5]=e("label",{class:"form-label"},"担任角色",-1)),U(e("input",{"onUpdate:modelValue":o=>l.role=o,type:"text",class:"form-input",placeholder:"如：实验室成员",onInput:m},null,40,ma),[[E,l.role]])]),e("button",{class:"delete-btn",onClick:o=>d(a),title:"删除研究经历"}," × ",8,va)]),e("div",_a,[e("div",fa,[t[6]||(t[6]=e("label",{class:"form-label"},"所在公司/组织/实验室",-1)),U(e("input",{"onUpdate:modelValue":o=>l.organization=o,type:"text",class:"form-input",placeholder:"如：***实验室",onInput:m},null,40,ga),[[E,l.organization]])]),e("div",ha,[t[8]||(t[8]=e("label",{class:"form-label"},"研究时间",-1)),e("div",ba,[e("div",$a,[U(e("input",{"onUpdate:modelValue":o=>l.startDate=o,type:"text",class:"date-input",placeholder:"2023-01",readonly:"",onClick:o=>h(`${a}_start`)},null,8,ya),[[E,l.startDate]]),r.value===`${a}_start`?(i(),c("div",ka,[e("div",Ca,[e("button",{type:"button",onClick:t[0]||(t[0]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[1]||(t[1]=o=>b(1))},"›")]),e("div",Va,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:C(`${a}_start`,o)}]),onClick:y=>v(`${a}_start`,u.value,o)},I(o)+"月 ",11,wa)),64))])])):L("",!0)]),t[7]||(t[7]=e("span",{class:"date-separator"},"至",-1)),e("div",xa,[U(e("input",{"onUpdate:modelValue":o=>l.endDate=o,type:"text",class:"date-input",placeholder:"2024-06 或 至今",readonly:"",onClick:o=>h(`${a}_end`)},null,8,Da),[[E,l.endDate]]),r.value===`${a}_end`?(i(),c("div",Ua,[e("div",Ma,[e("button",{type:"button",onClick:t[2]||(t[2]=o=>b(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[3]||(t[3]=o=>b(1))},"›")]),e("div",Sa,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:C(`${a}_end`,o)}]),onClick:y=>v(`${a}_end`,u.value,o)},I(o)+"月 ",11,Ia)),64)),e("button",{type:"button",class:"month-btn present-btn",onClick:o=>f(`${a}_end`)}," 至今 ",8,Ea)])])):L("",!0)])])]),e("div",Ta,[t[9]||(t[9]=e("label",{class:"form-label"},"研究描述",-1)),w(te,{modelValue:l.description,"onUpdate:modelValue":[o=>l.description=o,m],placeholder:"请输入研究内容、主要成果、发表论文、获得奖项等...","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])])])]))),128))])):(i(),c("div",za,t[10]||(t[10]=[e("p",null,"暂无研究经历信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加研究经历",-1)]))),e("div",La,[e("button",{class:"add-btn",onClick:k,disabled:s.research.length>=10},[w(P,{name:"plus",size:"sm"}),t[11]||(t[11]=e("span",null,"添加研究经历",-1))],8,Pa)])]))}},Fa=N(Oa,[["__scopeId","data-v-3bc48abe"]]),ja={class:"publication-form"},Ba={key:0,class:"publications-list"},Na={class:"publication-header"},Ra=["onUpdate:modelValue"],Ya=["onUpdate:modelValue"],Wa=["onClick"],qa={class:"publication-details"},Aa=["onUpdate:modelValue"],Ha={class:"date-picker-wrapper"},Ga=["onUpdate:modelValue","onClick"],Ka={key:0,class:"date-picker-dropdown"},Ja={class:"date-picker-header"},Xa={class:"month-grid"},Qa=["onClick"],Za={key:1,class:"empty-state"},ei={class:"form-actions"},ti={__name:"PublicationForm",props:{data:{type:Object,default:()=>({publications:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({publications:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,d=>{d&&d.publications?s.publications=[...d.publications]:s.publications=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{publications:[...s.publications]})},300)},k=()=>{s.publications.push({title:"",type:"",journal:"",date:"",description:""}),m()},h=d=>{s.publications.splice(d,1),m()},b=d=>{r.value=d;const n=s.publications[d].date;if(n){const t=parseInt(n.split("-")[0]);t&&(u.value=t)}},v=d=>{u.value+=d},f=(d,n)=>{const t=s.publications[d].date;if(!t)return!1;const[l,a]=t.split("-");return parseInt(l)===u.value&&parseInt(a)===n},C=(d,n,t)=>{const l=`${n}-${t.toString().padStart(2,"0")}`;s.publications[d].date=l,r.value="",m()},_=d=>{d.target.closest(".date-picker-wrapper")||(r.value="")};return G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)}),(d,n)=>(i(),c("div",ja,[n[5]||(n[5]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"论文专利"),e("p",{class:"form-desc"},"添加您发表的论文和申请的专利")],-1)),s.publications&&s.publications.length>0?(i(),c("div",Ba,[(i(!0),c(O,null,F(s.publications,(t,l)=>(i(),c("div",{key:l,class:"publication-item"},[e("div",Na,[U(e("input",{"onUpdate:modelValue":a=>t.title=a,type:"text",class:"title-input",placeholder:"论文/专利标题",onInput:m},null,40,Ra),[[E,t.title]]),U(e("select",{"onUpdate:modelValue":a=>t.type=a,class:"type-select",onChange:m},n[2]||(n[2]=[ae('<option value="" data-v-b9b1b3d9>选择类型</option><option value="期刊论文" data-v-b9b1b3d9>期刊论文</option><option value="会议论文" data-v-b9b1b3d9>会议论文</option><option value="发明专利" data-v-b9b1b3d9>发明专利</option><option value="实用新型" data-v-b9b1b3d9>实用新型</option><option value="外观设计" data-v-b9b1b3d9>外观设计</option>',6)]),40,Ya),[[re,t.type]]),e("button",{class:"delete-btn",onClick:a=>h(l),title:"删除"}," × ",8,Wa)]),e("div",qa,[U(e("input",{"onUpdate:modelValue":a=>t.journal=a,type:"text",class:"journal-input",placeholder:"期刊/会议名称",onInput:m},null,40,Aa),[[E,t.journal]]),e("div",Ha,[U(e("input",{"onUpdate:modelValue":a=>t.date=a,type:"text",class:"date-input",placeholder:"发表/申请时间",readonly:"",onClick:a=>b(l)},null,8,Ga),[[E,t.date]]),r.value===l?(i(),c("div",Ka,[e("div",Ja,[e("button",{type:"button",onClick:n[0]||(n[0]=a=>v(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:n[1]||(n[1]=a=>v(1))},"›")]),e("div",Xa,[(i(),c(O,null,F(12,a=>e("button",{key:a,type:"button",class:R(["month-btn",{active:f(l,a)}]),onClick:o=>C(l,u.value,a)},I(a)+"月 ",11,Qa)),64))])])):L("",!0)])]),w(te,{modelValue:t.description,"onUpdate:modelValue":[a=>t.description=a,m],placeholder:"详细描述","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])]))),128))])):(i(),c("div",Za,n[3]||(n[3]=[e("p",null,"暂无论文专利信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加论文专利",-1)]))),e("div",ei,[e("button",{class:"add-btn",onClick:k},[w(P,{name:"plus",size:"sm"}),n[4]||(n[4]=e("span",null,"添加论文专利",-1))])])]))}},si=N(ti,[["__scopeId","data-v-b9b1b3d9"]]),oi={class:"certificate-form"},ni={key:0,class:"certificates-list"},li={class:"certificate-header"},ai=["onUpdate:modelValue"],ii={class:"date-picker-wrapper"},di=["onUpdate:modelValue","onClick"],ri={key:0,class:"date-picker-dropdown"},ci={class:"date-picker-header"},ui={class:"month-grid"},pi=["onClick"],mi=["onClick"],vi={class:"certificate-details"},_i=["onUpdate:modelValue"],fi=["onUpdate:modelValue"],gi={key:1,class:"empty-state"},hi={class:"form-actions"},bi={__name:"CertificateForm",props:{data:{type:Object,default:()=>({certificates:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({certificates:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,d=>{d&&d.certificates?s.certificates=[...d.certificates]:s.certificates=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{certificates:[...s.certificates]})},300)},k=()=>{s.certificates.push({name:"",date:"",organization:"",number:"",description:""}),m()},h=d=>{s.certificates.splice(d,1),m()},b=d=>{r.value=d;const n=s.certificates[d].date;if(n){const t=parseInt(n.split("-")[0]);t&&(u.value=t)}},v=d=>{u.value+=d},f=(d,n)=>{const t=s.certificates[d].date;if(!t)return!1;const[l,a]=t.split("-");return parseInt(l)===u.value&&parseInt(a)===n},C=(d,n,t)=>{const l=`${n}-${t.toString().padStart(2,"0")}`;s.certificates[d].date=l,r.value="",m()},_=d=>{d.target.closest(".date-picker-wrapper")||(r.value="")};return G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)}),(d,n)=>(i(),c("div",oi,[n[4]||(n[4]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"证书资质"),e("p",{class:"form-desc"},"添加您获得的专业证书和资质")],-1)),s.certificates&&s.certificates.length>0?(i(),c("div",ni,[(i(!0),c(O,null,F(s.certificates,(t,l)=>(i(),c("div",{key:l,class:"certificate-item"},[e("div",li,[U(e("input",{"onUpdate:modelValue":a=>t.name=a,type:"text",class:"name-input",placeholder:"证书名称",onInput:m},null,40,ai),[[E,t.name]]),e("div",ii,[U(e("input",{"onUpdate:modelValue":a=>t.date=a,type:"text",class:"date-input",placeholder:"获得时间",readonly:"",onClick:a=>b(l)},null,8,di),[[E,t.date]]),r.value===l?(i(),c("div",ri,[e("div",ci,[e("button",{type:"button",onClick:n[0]||(n[0]=a=>v(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:n[1]||(n[1]=a=>v(1))},"›")]),e("div",ui,[(i(),c(O,null,F(12,a=>e("button",{key:a,type:"button",class:R(["month-btn",{active:f(l,a)}]),onClick:o=>C(l,u.value,a)},I(a)+"月 ",11,pi)),64))])])):L("",!0)]),e("button",{class:"delete-btn",onClick:a=>h(l),title:"删除证书"}," × ",8,mi)]),e("div",vi,[U(e("input",{"onUpdate:modelValue":a=>t.organization=a,type:"text",class:"organization-input",placeholder:"颁发机构",onInput:m},null,40,_i),[[E,t.organization]]),U(e("input",{"onUpdate:modelValue":a=>t.number=a,type:"text",class:"number-input",placeholder:"证书编号（可选）",onInput:m},null,40,fi),[[E,t.number]])]),w(te,{modelValue:t.description,"onUpdate:modelValue":[a=>t.description=a,m],placeholder:"详细描述","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])]))),128))])):(i(),c("div",gi,n[2]||(n[2]=[e("p",null,"暂无证书资质信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加证书资质",-1)]))),e("div",hi,[e("button",{class:"add-btn",onClick:k},[w(P,{name:"plus",size:"sm"}),n[3]||(n[3]=e("span",null,"添加证书",-1))])])]))}},$i=N(bi,[["__scopeId","data-v-c98dcde7"]]),yi={class:"volunteer-experience-form"},ki={key:0,class:"volunteer-list"},Ci={class:"volunteer-header"},Vi={class:"form-group"},wi=["onUpdate:modelValue"],xi={class:"form-group"},Di=["onUpdate:modelValue"],Ui=["onClick"],Mi={class:"volunteer-details"},Si={class:"form-group"},Ii=["onUpdate:modelValue"],Ei={class:"form-group"},Ti={class:"date-range-group"},zi={class:"date-picker-wrapper"},Li=["onUpdate:modelValue","onClick"],Pi={key:0,class:"date-picker-dropdown"},Oi={class:"date-picker-header"},Fi={class:"month-grid"},ji=["onClick"],Bi={class:"date-picker-wrapper"},Ni=["onUpdate:modelValue","onClick"],Ri={key:0,class:"date-picker-dropdown"},Yi={class:"date-picker-header"},Wi={class:"month-grid"},qi=["onClick"],Ai=["onClick"],Hi={class:"form-group"},Gi={key:1,class:"empty-state"},Ki={class:"form-actions"},Ji=["disabled"],Xi={key:0,class:"limit-hint"},Qi={__name:"VolunteerExperienceForm",props:{data:{type:Object,default:()=>({volunteerExperience:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({volunteerExperience:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,n=>{n&&n.volunteerExperience?s.volunteerExperience=[...n.volunteerExperience]:s.volunteerExperience=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{volunteerExperience:[...s.volunteerExperience]})},300)},k=()=>{s.volunteerExperience.length>=10||(s.volunteerExperience.push({organization:"",role:"",location:"",startDate:"",endDate:"",description:""}),m())},h=n=>{s.volunteerExperience.splice(n,1),m()},b=n=>{r.value=n;const[t,l]=n.split("_"),a=l==="start"?s.volunteerExperience[t].startDate:s.volunteerExperience[t].endDate;if(a&&a!=="至今"){const o=parseInt(a.split("-")[0]);o&&(u.value=o)}},v=n=>{u.value+=n},f=(n,t,l)=>{const a=`${t}-${String(l).padStart(2,"0")}`,[o,y]=n.split("_");y==="start"?s.volunteerExperience[o].startDate=a:s.volunteerExperience[o].endDate=a,r.value="",m()},C=n=>{const[t]=n.split("_");s.volunteerExperience[t].endDate="至今",r.value="",m()},_=(n,t)=>{const[l,a]=n.split("_"),o=a==="start"?s.volunteerExperience[l].startDate:s.volunteerExperience[l].endDate;if(!o||o==="至今")return!1;const[y,B]=o.split("-");return parseInt(y)===u.value&&parseInt(B)===t},d=n=>{n.target.closest(".date-picker-wrapper")||(r.value="")};return G(()=>{document.addEventListener("click",d)}),X(()=>{document.removeEventListener("click",d)}),(n,t)=>(i(),c("div",yi,[t[12]||(t[12]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"志愿服务"),e("p",{class:"form-desc"},"添加您参与的志愿服务活动和公益经历")],-1)),s.volunteerExperience&&s.volunteerExperience.length>0?(i(),c("div",ki,[(i(!0),c(O,null,F(s.volunteerExperience,(l,a)=>(i(),c("div",{key:a,class:"volunteer-item"},[e("div",Ci,[e("div",Vi,[t[4]||(t[4]=e("label",{class:"form-label"},"组织名称",-1)),U(e("input",{"onUpdate:modelValue":o=>l.organization=o,type:"text",class:"form-input",placeholder:"如：蓝天救援志愿队",onInput:m},null,40,wi),[[E,l.organization]])]),e("div",xi,[t[5]||(t[5]=e("label",{class:"form-label"},"服务角色",-1)),U(e("input",{"onUpdate:modelValue":o=>l.role=o,type:"text",class:"form-input",placeholder:"如：志愿者、队长",onInput:m},null,40,Di),[[E,l.role]])]),e("button",{class:"delete-btn",onClick:o=>h(a),title:"删除志愿服务经历"}," × ",8,Ui)]),e("div",Mi,[e("div",Si,[t[6]||(t[6]=e("label",{class:"form-label"},"服务地点",-1)),U(e("input",{"onUpdate:modelValue":o=>l.location=o,type:"text",class:"form-input",placeholder:"如：北京市朝阳区",onInput:m},null,40,Ii),[[E,l.location]])]),e("div",Ei,[t[8]||(t[8]=e("label",{class:"form-label"},"服务时间",-1)),e("div",Ti,[e("div",zi,[U(e("input",{"onUpdate:modelValue":o=>l.startDate=o,type:"text",class:"date-input",placeholder:"2020-01",readonly:"",onClick:o=>b(`${a}_start`)},null,8,Li),[[E,l.startDate]]),r.value===`${a}_start`?(i(),c("div",Pi,[e("div",Oi,[e("button",{type:"button",onClick:t[0]||(t[0]=o=>v(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[1]||(t[1]=o=>v(1))},"›")]),e("div",Fi,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:_(`${a}_start`,o)}]),onClick:y=>f(`${a}_start`,u.value,o)},I(o)+"月 ",11,ji)),64))])])):L("",!0)]),t[7]||(t[7]=e("span",{class:"date-separator"},"至",-1)),e("div",Bi,[U(e("input",{"onUpdate:modelValue":o=>l.endDate=o,type:"text",class:"date-input",placeholder:"2021-01 或 至今",readonly:"",onClick:o=>b(`${a}_end`)},null,8,Ni),[[E,l.endDate]]),r.value===`${a}_end`?(i(),c("div",Ri,[e("div",Yi,[e("button",{type:"button",onClick:t[2]||(t[2]=o=>v(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:t[3]||(t[3]=o=>v(1))},"›")]),e("div",Wi,[(i(),c(O,null,F(12,o=>e("button",{key:o,type:"button",class:R(["month-btn",{active:_(`${a}_end`,o)}]),onClick:y=>f(`${a}_end`,u.value,o)},I(o)+"月 ",11,qi)),64)),e("button",{type:"button",class:"month-btn present-btn",onClick:o=>C(`${a}_end`)}," 至今 ",8,Ai)])])):L("",!0)])])]),e("div",Hi,[t[9]||(t[9]=e("label",{class:"form-label"},"服务描述",-1)),w(te,{modelValue:l.description,"onUpdate:modelValue":[o=>l.description=o,m],placeholder:"请输入服务内容、参与活动、个人贡献等...","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])])])]))),128))])):(i(),c("div",Gi,t[10]||(t[10]=[e("p",null,"暂无志愿服务信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加志愿服务经历",-1)]))),e("div",Ki,[e("button",{class:"add-btn",onClick:k,disabled:s.volunteerExperience.length>=10},[w(P,{name:"plus",size:"sm"}),t[11]||(t[11]=e("span",null,"添加志愿服务",-1))],8,Ji),s.volunteerExperience.length>=10?(i(),c("p",Xi," 最多可添加10个志愿服务经历 ")):L("",!0)])]))}},Zi=N(Qi,[["__scopeId","data-v-fd6103cf"]]),ed={class:"portfolio-form"},td={key:0,class:"portfolio-list"},sd={class:"item-header"},od=["onUpdate:modelValue"],nd=["onUpdate:modelValue"],ld=["onClick"],ad={class:"item-details"},id=["onUpdate:modelValue"],dd={class:"date-picker-wrapper"},rd=["onUpdate:modelValue","onClick"],cd={key:0,class:"date-picker-dropdown"},ud={class:"date-picker-header"},pd={class:"month-grid"},md=["onClick"],vd={key:1,class:"empty-state"},_d={class:"form-actions"},fd={__name:"PortfolioForm",props:{data:{type:Object,default:()=>({items:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({items:[]});let p=null;const r=T(""),u=T(new Date().getFullYear());H(()=>g.data,d=>{d&&d.items?s.items=[...d.items]:s.items=[]},{immediate:!0,deep:!0});const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{items:[...s.items]})},300)},k=()=>{s.items.push({title:"",type:"",url:"",date:"",description:""}),m()},h=d=>{s.items.splice(d,1),m()},b=d=>{r.value=d;const n=s.items[d].date;if(n){const t=parseInt(n.split("-")[0]);t&&(u.value=t)}},v=d=>{u.value+=d},f=(d,n)=>{const t=s.items[d].date;if(!t)return!1;const[l,a]=t.split("-");return parseInt(l)===u.value&&parseInt(a)===n},C=(d,n,t)=>{const l=`${n}-${t.toString().padStart(2,"0")}`;s.items[d].date=l,r.value="",m()},_=d=>{d.target.closest(".date-picker-wrapper")||(r.value="")};return G(()=>{document.addEventListener("click",_)}),X(()=>{document.removeEventListener("click",_)}),(d,n)=>(i(),c("div",ed,[n[5]||(n[5]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"作品集"),e("p",{class:"form-desc"},"展示您的个人作品和项目")],-1)),s.items&&s.items.length>0?(i(),c("div",td,[(i(!0),c(O,null,F(s.items,(t,l)=>(i(),c("div",{key:l,class:"portfolio-item"},[e("div",sd,[U(e("input",{"onUpdate:modelValue":a=>t.title=a,type:"text",class:"title-input",placeholder:"作品标题",onInput:m},null,40,od),[[E,t.title]]),U(e("select",{"onUpdate:modelValue":a=>t.type=a,class:"type-select",onChange:m},n[2]||(n[2]=[ae('<option value="" data-v-c27cd331>选择类型</option><option value="网站" data-v-c27cd331>网站</option><option value="移动应用" data-v-c27cd331>移动应用</option><option value="设计作品" data-v-c27cd331>设计作品</option><option value="开源项目" data-v-c27cd331>开源项目</option><option value="其他" data-v-c27cd331>其他</option>',6)]),40,nd),[[re,t.type]]),e("button",{class:"delete-btn",onClick:a=>h(l),title:"删除作品"}," × ",8,ld)]),e("div",ad,[U(e("input",{"onUpdate:modelValue":a=>t.url=a,type:"url",class:"url-input",placeholder:"作品链接",onInput:m},null,40,id),[[E,t.url]]),e("div",dd,[U(e("input",{"onUpdate:modelValue":a=>t.date=a,type:"text",class:"date-input",placeholder:"完成时间",readonly:"",onClick:a=>b(l)},null,8,rd),[[E,t.date]]),r.value===l?(i(),c("div",cd,[e("div",ud,[e("button",{type:"button",onClick:n[0]||(n[0]=a=>v(-1))},"‹"),e("span",null,I(u.value),1),e("button",{type:"button",onClick:n[1]||(n[1]=a=>v(1))},"›")]),e("div",pd,[(i(),c(O,null,F(12,a=>e("button",{key:a,type:"button",class:R(["month-btn",{active:f(l,a)}]),onClick:o=>C(l,u.value,a)},I(a)+"月 ",11,md)),64))])])):L("",!0)])]),w(te,{modelValue:t.description,"onUpdate:modelValue":[a=>t.description=a,m],placeholder:"作品描述","min-height":"120px"},null,8,["modelValue","onUpdate:modelValue"])]))),128))])):(i(),c("div",vd,n[3]||(n[3]=[e("p",null,"暂无作品信息",-1),e("p",{class:"empty-hint"},"点击下方按钮添加作品",-1)]))),e("div",_d,[e("button",{class:"add-btn",onClick:k},[w(P,{name:"plus",size:"sm"}),n[4]||(n[4]=e("span",null,"添加作品",-1))])])]))}},gd=N(fd,[["__scopeId","data-v-c27cd331"]]),hd={class:"cover-letter-form"},bd={class:"form-content"},$d={__name:"CoverLetterForm",props:{data:{type:Object,default:()=>({content:""})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({content:""});let p=null;H(()=>g.data,u=>{u&&u.content?s.content=u.content:s.content=""},{immediate:!0,deep:!0});const r=()=>{p&&clearTimeout(p),p=setTimeout(()=>{$("update",{content:s.content})},300)};return(u,m)=>(i(),c("div",hd,[m[1]||(m[1]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"自荐信"),e("p",{class:"form-desc"},"撰写针对性的求职自荐信")],-1)),e("div",bd,[w(te,{modelValue:s.content,"onUpdate:modelValue":[m[0]||(m[0]=k=>s.content=k),r],placeholder:"在这里撰写您的自荐信内容...","min-height":"300px"},null,8,["modelValue"])])]))}},yd=N($d,[["__scopeId","data-v-c46026cc"]]),kd={class:"custom-form"},Cd={class:"form-content"},Vd={key:0,class:"items-list"},wd={class:"item-header"},xd={class:"form-group"},Dd=["onUpdate:modelValue"],Ud={class:"form-group"},Md=["onUpdate:modelValue"],Sd=["onClick"],Id={class:"item-details"},Ed={class:"form-group"},Td={class:"date-range-group"},zd={class:"date-picker-wrapper"},Ld=["onUpdate:modelValue","onClick"],Pd={key:0,class:"date-picker-dropdown"},Od={class:"date-picker-header"},Fd={class:"month-grid"},jd=["onClick"],Bd={class:"date-picker-wrapper"},Nd=["onUpdate:modelValue","onClick"],Rd={key:0,class:"date-picker-dropdown"},Yd={class:"date-picker-header"},Wd={class:"month-grid"},qd=["onClick"],Ad=["onClick"],Hd={class:"form-group"},Gd={key:1,class:"empty-state"},Kd={__name:"CustomForm",props:{data:{type:Object,default:()=>({items:[]})}},emits:["update"],setup(x,{emit:M}){const g=x,$=M,s=J({items:[]}),p=T(""),r=T(new Date().getFullYear());A(()=>{const t=new Date().getFullYear(),l=[];for(let a=t+2;a>=t-50;a--)l.push(a);return l});let u=null;H(()=>g.data,t=>{t&&t.items&&Array.isArray(t.items)?s.items=t.items.map(l=>({name:l.name||"",role:l.role||"",startDate:l.startDate||"",endDate:l.endDate||"",isOngoing:l.isOngoing||!1,content:l.content||""})):s.items=[]},{immediate:!0,deep:!0});const m=()=>{const t={name:"",role:"",startDate:"",endDate:"",isOngoing:!1,content:""};s.items.push(t),b()},k=t=>{confirm("确定要删除这个内容项吗？")&&(s.items.splice(t,1),b())},h=()=>{u&&clearTimeout(u),u=setTimeout(()=>{b()},300)},b=()=>{$("update",{items:s.items})},v=t=>{p.value=t;const[l,a]=t.split("_"),o=a==="start"?s.items[l].startDate:s.items[l].endDate;if(o&&o!=="至今"){const y=parseInt(o.split("-")[0]);y&&(r.value=y)}},f=t=>{r.value+=t},C=(t,l,a)=>{const o=`${l}-${String(a).padStart(2,"0")}`,[y,B]=t.split("_");B==="start"?s.items[y].startDate=o:(s.items[y].endDate=o,s.items[y].isOngoing=!1),p.value="",h()},_=t=>{const[l]=t.split("_");s.items[l].endDate="至今",s.items[l].isOngoing=!0,p.value="",h()},d=(t,l)=>{const[a,o]=t.split("_"),y=o==="start"?s.items[a].startDate:s.items[a].endDate;if(!y||y==="至今")return!1;const[B,oe]=y.split("-");return parseInt(B)===r.value&&parseInt(oe)===l},n=t=>{t.target.closest(".date-picker-wrapper")||(p.value="")};return G(()=>{document.addEventListener("click",n)}),X(()=>{document.removeEventListener("click",n)}),(t,l)=>(i(),c("div",kd,[l[12]||(l[12]=e("div",{class:"form-header"},[e("h4",{class:"form-title"},"自定义内容"),e("p",{class:"form-desc"},"添加您的个性化简历内容")],-1)),e("div",Cd,[s.items&&s.items.length>0?(i(),c("div",Vd,[(i(!0),c(O,null,F(s.items,(a,o)=>(i(),c("div",{key:o,class:"item-card"},[e("div",wd,[e("div",xd,[l[4]||(l[4]=e("label",{class:"form-label"},"名称",-1)),U(e("input",{"onUpdate:modelValue":y=>a.name=y,type:"text",class:"form-input",placeholder:"请输入名称",onInput:h},null,40,Dd),[[E,a.name]])]),e("div",Ud,[l[5]||(l[5]=e("label",{class:"form-label"},"角色",-1)),U(e("input",{"onUpdate:modelValue":y=>a.role=y,type:"text",class:"form-input",placeholder:"请输入担任的角色",onInput:h},null,40,Md),[[E,a.role]])]),e("button",{type:"button",class:"delete-btn",onClick:y=>k(o),title:"删除此项"}," × ",8,Sd)]),e("div",Id,[e("div",Ed,[l[7]||(l[7]=e("label",{class:"form-label"},"时间",-1)),e("div",Td,[e("div",zd,[U(e("input",{"onUpdate:modelValue":y=>a.startDate=y,type:"text",class:"date-input",placeholder:"2023-01",readonly:"",onClick:y=>v(`${o}_start`)},null,8,Ld),[[E,a.startDate]]),p.value===`${o}_start`?(i(),c("div",Pd,[e("div",Od,[e("button",{type:"button",onClick:l[0]||(l[0]=y=>f(-1))},"‹"),e("span",null,I(r.value),1),e("button",{type:"button",onClick:l[1]||(l[1]=y=>f(1))},"›")]),e("div",Fd,[(i(),c(O,null,F(12,y=>e("button",{key:y,type:"button",class:R(["month-btn",{active:d(`${o}_start`,y)}]),onClick:B=>C(`${o}_start`,r.value,y)},I(y)+"月 ",11,jd)),64))])])):L("",!0)]),l[6]||(l[6]=e("span",{class:"date-separator"},"至",-1)),e("div",Bd,[U(e("input",{"onUpdate:modelValue":y=>a.endDate=y,type:"text",class:"date-input",placeholder:"2024-06 或 至今",readonly:"",onClick:y=>v(`${o}_end`)},null,8,Nd),[[E,a.endDate]]),p.value===`${o}_end`?(i(),c("div",Rd,[e("div",Yd,[e("button",{type:"button",onClick:l[2]||(l[2]=y=>f(-1))},"‹"),e("span",null,I(r.value),1),e("button",{type:"button",onClick:l[3]||(l[3]=y=>f(1))},"›")]),e("div",Wd,[(i(),c(O,null,F(12,y=>e("button",{key:y,type:"button",class:R(["month-btn",{active:d(`${o}_end`,y)}]),onClick:B=>C(`${o}_end`,r.value,y)},I(y)+"月 ",11,qd)),64)),e("button",{type:"button",class:"month-btn present-btn",onClick:y=>_(`${o}_end`)}," 至今 ",8,Ad)])])):L("",!0)])])]),e("div",Hd,[l[8]||(l[8]=e("label",{class:"form-label"},"内容描述",-1)),w(te,{modelValue:a.content,"onUpdate:modelValue":y=>a.content=y,placeholder:"请输入详细内容...",onUpdate:h},null,8,["modelValue","onUpdate:modelValue"])])])]))),128))])):(i(),c("div",Gd,[w(P,{name:"plus",size:"lg",class:"empty-icon"}),l[9]||(l[9]=e("p",{class:"empty-text"},"暂无内容项",-1)),l[10]||(l[10]=e("p",{class:"empty-desc"},"点击下方按钮添加您的第一个内容项",-1))])),e("button",{type:"button",class:"add-btn",onClick:m},[w(P,{name:"plus",size:"sm"}),l[11]||(l[11]=e("span",null,"添加内容项",-1))])])]))}},Jd=N(Kd,[["__scopeId","data-v-c5a07a2e"]]),Xd={class:"text-form"},Qd={__name:"TextForm",props:{data:{type:Object,default:()=>({content:""})},config:{type:Object,default:()=>({})}},emits:["update"],setup(x,{emit:M}){return(g,$)=>(i(),c("div",Xd,$[0]||($[0]=[e("h4",null,"文本表单 - 开发中",-1),e("p",null,"此组件正在开发中，即将上线...",-1)])))}},Zd=N(Qd,[["__scopeId","data-v-2a9cd809"]]),er={class:"content-editor"},tr={class:"editor-header"},sr={class:"module-info"},or={class:"module-title"},nr={class:"editor-content"},lr={key:0,class:"help-tips"},ar={class:"tips-content"},ir={class:"tips-description"},dr={key:0,class:"tips-examples"},rr={key:1,class:"help-panel-toggle"},cr={key:18,class:"debug-info"},ur={key:20,class:"empty-state"},pr={key:0,class:"module-navigation-footer"},mr={class:"nav-buttons-group"},vr={__name:"ContentEditor",setup(x){const M=T(!0),g=T(!1),$=_e(),s=A(()=>{const d=$.currentModuleId.value;return console.log("ContentEditor - 当前模块ID:",d),d}),p=A(()=>{var t;const d=$.moduleConfigs.value;if(!Array.isArray(d))return console.warn("ContentEditor - 模块配置不是数组:",d),null;let n=d.find(l=>l.id===s.value);return n||(n=(((t=$.customModules)==null?void 0:t.value)||[]).find(a=>a.id===s.value)),console.log("ContentEditor - 找到模块配置:",(n==null?void 0:n.name)||"未找到"),n}),r=A(()=>{const d=$.getModuleData(s.value),n=b();if(!d||typeof d=="object"&&Object.keys(d).length===0)return console.log("ContentEditor - 使用默认数据:",s.value,n),n;const t=typeof n=="object"&&n!==null?{...n,...d}:d;return console.log("ContentEditor - 合并后数据:",s.value,t),t}),u=A(()=>{var a;const d=$.moduleConfigs.value;if(!Array.isArray(d))return[];const n=d.filter(o=>o&&o.enabled),l=(((a=$.customModules)==null?void 0:a.value)||[]).filter(o=>o&&o.enabled);return[...n,...l].sort((o,y)=>(o.order||0)-(y.order||0))}),m=A(()=>u.value.findIndex(n=>n.id===s.value)),k=A(()=>m.value===0),h=A(()=>{const d=u.value;return m.value===d.length-1});H(s,()=>{M.value=!0});const b=()=>{if(!p.value)return null;switch(p.value.type){case"basic_info":return{photo:"",name:"张三",phone:"13800138000",email:"<EMAIL>",age:"25",currentCity:"北京",address:"北京市朝阳区***路***号**小区",currentStatus:"已离职-随时到岗",intendedCity:"北京",expectedPosition:"前端开发工程师",expectedSalary:"面议",wechat:"",website:"",github:"",gender:"",height:"",weight:"",politicalStatus:"",maritalStatus:"",hometown:"",ethnicity:"",customSocials:[],customInfos:[]};case"education":case"work_experience":case"project":case"internship":case"training":case"research_experience":case"volunteer_experience":return[];case"skills":return{skills:[]};case"language":return{languages:[]};case"award":return{awards:[]};case"hobbies":return{hobbies:[]};case"publication":return{publications:[]};case"certificate":return{certificates:[]};case"portfolio":return{items:[]};case"text":case"self_evaluation":case"cover_letter":return{content:""};case"custom":return p.value&&p.value.id.startsWith("custom_")?{items:[]}:{content:""};default:return p.value&&p.value.id.startsWith("custom_")?{items:[]}:{}}},v=d=>{console.log("📝 ContentEditor - handleDataUpdate 被调用:",{moduleId:s.value,updatedData:d,timestamp:new Date().toISOString()});const n=s.value;$.updateModuleData(n,d),ie(()=>{console.log("✅ 数据更新后的store状态:",$.currentResumeData)})},f=()=>{g.value=!g.value},C=()=>{const d=u.value,n=m.value;if(n>0){const t=d[n-1];$.setCurrentModule(t.id)}},_=()=>{const d=u.value,n=m.value;if(n<d.length-1){const t=d[n+1];$.setCurrentModule(t.id)}};return(d,n)=>{var t,l,a;return i(),c("div",er,[e("div",tr,[e("div",sr,[w(P,{name:((t=p.value)==null?void 0:t.icon)||"edit",size:"sm",class:"module-icon"},null,8,["name"]),e("h3",or,I(((l=p.value)==null?void 0:l.name)||"请选择模块"),1)])]),e("div",nr,[M.value&&p.value?(i(),c("div",{key:0,class:R(["help-panel",{"help-panel-collapsed":g.value}])},[g.value?(i(),c("div",rr,[e("button",{class:"expand-btn",onClick:f,title:"展开编写建议"},n[2]||(n[2]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[e("polyline",{points:"9,18 15,12 9,6"})],-1),e("span",{class:"expand-text"},"编写建议",-1)]))])):(i(),c("div",lr,[e("div",{class:"tips-header"},[n[1]||(n[1]=e("span",{class:"tips-title"},"编写建议",-1)),e("button",{class:"collapse-btn",onClick:f,title:"折叠编写建议"},n[0]||(n[0]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[e("polyline",{points:"15,18 9,12 15,6"})],-1)]))]),e("div",ar,[e("p",ir,I(p.value.tips||"暂无编写建议"),1),p.value.examples&&p.value.examples.length?(i(),c("ul",dr,[(i(!0),c(O,null,F(p.value.examples,(o,y)=>(i(),c("li",{key:y},I(o),1))),128))])):L("",!0)])]))],2)):L("",!0),e("div",{class:R(["form-container",{"form-container-full":!M.value||!p.value||g.value}])},[s.value==="basic_info"?(i(),W(De,{key:0,data:r.value,onUpdate:v},null,8,["data"])):s.value==="education"?(i(),W(Zs,{key:1,data:r.value,onUpdate:v},null,8,["data"])):s.value==="work_experience"?(i(),W(To,{key:2,data:r.value,onUpdate:v},null,8,["data"])):s.value==="project"?(i(),W(Ue,{key:3,data:r.value,onUpdate:v},null,8,["data"])):s.value==="skills"?(i(),W(cn,{key:4,data:r.value,onUpdate:v},null,8,["data"])):s.value==="language"?(i(),W(kn,{key:5,data:r.value,onUpdate:v},null,8,["data"])):s.value==="award"?(i(),W(Bn,{key:6,data:r.value,onUpdate:v},null,8,["data"])):s.value==="hobbies"?(i(),W(Jn,{key:7,data:r.value,onUpdate:v},null,8,["data"])):s.value==="self_evaluation"?(i(),W(el,{key:8,data:r.value,onUpdate:v},null,8,["data"])):s.value==="internship"?(i(),W(Sl,{key:9,data:r.value,onUpdate:v},null,8,["data"])):s.value==="training"?(i(),W(aa,{key:10,data:r.value,onUpdate:v},null,8,["data"])):s.value==="research_experience"?(i(),W(Fa,{key:11,data:r.value,onUpdate:v},null,8,["data"])):s.value==="publication"?(i(),W(si,{key:12,data:r.value,onUpdate:v},null,8,["data"])):s.value==="certificate"?(i(),W($i,{key:13,data:r.value,onUpdate:v},null,8,["data"])):s.value==="volunteer_experience"?(i(),W(Zi,{key:14,data:r.value,onUpdate:v},null,8,["data"])):s.value==="portfolio"?(i(),W(gd,{key:15,data:r.value,onUpdate:v},null,8,["data"])):s.value==="cover_letter"?(i(),W(yd,{key:16,data:r.value,onUpdate:v},null,8,["data"])):s.value.startsWith("custom_")?(i(),W(Jd,{key:17,data:r.value,onUpdate:v},null,8,["data"])):s.value&&s.value.includes("custom")?(i(),c("div",cr,[n[3]||(n[3]=e("h4",null,"调试信息",-1)),e("p",null,"当前模块ID: "+I(s.value),1),e("p",null,"是否以custom_开头: "+I(s.value.startsWith("custom_")),1),e("p",null,"当前模块配置: "+I(JSON.stringify(p.value,null,2)),1),e("p",null,"模块数据: "+I(JSON.stringify(r.value,null,2)),1),e("p",null,"所有自定义模块: "+I(JSON.stringify(((a=ne($).customModules)==null?void 0:a.value)||[],null,2)),1)])):p.value&&p.value.type==="text"?(i(),W(Zd,{key:19,data:r.value,config:p.value,onUpdate:v},null,8,["data","config"])):(i(),c("div",ur,[w(P,{name:"edit",size:"xl",class:"empty-icon"}),n[4]||(n[4]=e("h4",{class:"empty-title"},"选择一个模块开始编辑",-1)),n[5]||(n[5]=e("p",{class:"empty-desc"},"在左侧选择要编辑的简历模块，在这里填写详细信息",-1))]))],2)]),p.value?(i(),c("div",pr,[n[8]||(n[8]=e("div",{class:"nav-spacer"},null,-1)),e("div",mr,[k.value?L("",!0):(i(),c("button",{key:0,class:"nav-btn nav-btn-prev",onClick:C},n[6]||(n[6]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[e("polyline",{points:"15,18 9,12 15,6"})],-1),e("span",null,"上一步",-1)]))),h.value?L("",!0):(i(),c("button",{key:1,class:"nav-btn nav-btn-next",onClick:_},n[7]||(n[7]=[e("span",null,"下一步",-1),e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[e("polyline",{points:"9,18 15,12 9,6"})],-1)])))])])):L("",!0)])}}},_r=N(vr,[["__scopeId","data-v-eb547792"]]),fr={class:"font-selector w-36"},gr=["value"],hr=["value"],br={__name:"FontSelector",props:{modelValue:{type:String,default:"system-ui"}},emits:["update:modelValue","change"],setup(x,{emit:M}){const g=M,$=[{label:"系统默认",value:"system-ui"},{label:"微软雅黑",value:"'Microsoft YaHei', sans-serif"},{label:"黑体",value:"'SimHei', sans-serif"},{label:"宋体",value:"'SimSun', serif"},{label:"楷体",value:"'KaiTi', serif"},{label:"Arial",value:"'Arial', sans-serif"},{label:"Times New Roman",value:"'Times New Roman', serif"},{label:"Helvetica",value:"'Helvetica', sans-serif"},{label:"Georgia",value:"'Georgia', serif"}],s=p=>{const r=p.target.value;g("update:modelValue",r),g("change",r)};return(p,r)=>(i(),c("div",fr,[e("select",{value:x.modelValue,onChange:s,class:"font-selector-dropdown"},[(i(),c(O,null,F($,u=>e("option",{key:u.value,value:u.value,style:de({fontFamily:u.value})},I(u.label),13,hr)),64))],40,gr),r[0]||(r[0]=e("svg",{class:"dropdown-icon",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[e("polyline",{points:"6,9 12,15 18,9"})],-1))]))}},$r=N(br,[["__scopeId","data-v-d07c3821"]]),yr={class:"font-size-control"},kr={class:"size-control-group"},Cr=["disabled"],Vr=["min","max"],wr=["disabled"],xr={__name:"FontSizeControl",props:{modelValue:{type:String,default:"14px"},minSize:{type:Number,default:8},maxSize:{type:Number,default:72},step:{type:Number,default:1}},emits:["update:modelValue","change"],setup(x,{emit:M}){const g=x,$=M,s=T(""),p=A(()=>{const h=g.modelValue.match(/\d+/);return h?parseInt(h[0]):14});H(()=>g.modelValue,h=>{s.value=p.value.toString()},{immediate:!0});const r=()=>{const h=Math.max(p.value-g.step,g.minSize);k(h)},u=()=>{const h=Math.min(p.value+g.step,g.maxSize);k(h)},m=()=>{const h=parseInt(s.value);if(isNaN(h)){s.value=p.value.toString();return}const b=Math.max(g.minSize,Math.min(h,g.maxSize));b!==h&&(s.value=b.toString()),k(b)},k=h=>{const b=`${h}px`;$("update:modelValue",b),$("change",b)};return(h,b)=>(i(),c("div",yr,[e("div",kr,[e("button",{class:"size-btn decrease",onClick:r,disabled:p.value<=x.minSize,title:"减小字号"},b[1]||(b[1]=[e("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[e("line",{x1:"5",y1:"12",x2:"19",y2:"12"})],-1)]),8,Cr),U(e("input",{"onUpdate:modelValue":b[0]||(b[0]=v=>s.value=v),type:"number",min:x.minSize,max:x.maxSize,class:"size-input",onBlur:m,onKeydown:ce(m,["enter"]),title:"字体大小"},null,40,Vr),[[E,s.value]]),e("button",{class:"size-btn increase",onClick:u,disabled:p.value>=x.maxSize,title:"增大字号"},b[2]||(b[2]=[e("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[e("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),e("line",{x1:"5",y1:"12",x2:"19",y2:"12"})],-1)]),8,wr)])]))}},Dr=N(xr,[["__scopeId","data-v-f1215563"]]),Ur={class:"color-selector"},Mr=["title"],Sr={class:"color-panel"},Ir={class:"color-options"},Er=["title","onClick"],Tr={key:0,class:"check-icon",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},zr={__name:"ColorSelector",props:{modelValue:{type:String,default:"#333333"}},emits:["update:modelValue","change"],setup(x,{emit:M}){const g=M,$=T(!1),s=[{label:"纯黑",value:"#000000"},{label:"深灰",value:"#333333"},{label:"中灰",value:"#666666"},{label:"浅灰",value:"#999999"},{label:"淡灰",value:"#CCCCCC"}];G(()=>{document.addEventListener("click",m)}),X(()=>{document.removeEventListener("click",m)});const p=()=>{$.value=!$.value},r=()=>{$.value=!1},u=k=>{g("update:modelValue",k),g("change",k),r()},m=k=>{!k.target.closest(".color-selector")&&$.value&&r()};return(k,h)=>(i(),c("div",Ur,[e("div",{class:"color-trigger",onClick:p},[e("div",{class:"color-preview",style:de({backgroundColor:x.modelValue}),title:`当前颜色: ${x.modelValue}`},null,12,Mr),h[0]||(h[0]=e("svg",{class:"dropdown-icon",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[e("polyline",{points:"6,9 12,15 18,9"})],-1))]),w(ge,{name:"color-panel"},{default:le(()=>[U(e("div",Sr,[e("div",Ir,[(i(),c(O,null,F(s,(b,v)=>e("button",{key:b.value,class:R(["color-option",{active:x.modelValue===b.value}]),style:de({backgroundColor:b.value}),title:b.label,onClick:f=>u(b.value)},[x.modelValue===b.value?(i(),c("svg",Tr,h[1]||(h[1]=[e("polyline",{points:"20,6 9,17 4,12"},null,-1)]))):L("",!0)],14,Er)),64))])],512),[[fe,$.value]])]),_:1}),$.value?(i(),c("div",{key:0,class:"color-overlay",onClick:r})):L("",!0)]))}},Lr=N(zr,[["__scopeId","data-v-5f74cf71"]]),Pr={class:"margin-control"},Or={class:"margin-panel"},Fr={class:"margin-sliders"},jr={class:"slider-group"},Br={class:"slider-container"},Nr=["min","max","step"],Rr={class:"slider-value"},Yr={class:"slider-group"},Wr={class:"slider-container"},qr=["min","max","step"],Ar={class:"slider-value"},Hr={__name:"MarginControl",props:{modelValue:{type:Object,default:()=>({top:60,side:60})}},emits:["update:modelValue","change"],setup(x,{emit:M}){const g=x,$=M,s=T(!1),p=J({top:60,side:60}),r={top:{min:20,max:120,step:5,default:60},side:{min:20,max:120,step:5,default:60}};G(()=>{Object.assign(p,g.modelValue),H(()=>g.modelValue,v=>{Object.assign(p,v)},{deep:!0}),document.addEventListener("click",b)}),X(()=>{document.removeEventListener("click",b)});const u=()=>{s.value=!s.value},m=()=>{s.value=!1},k=()=>{const v={...p};$("update:modelValue",v),$("change",v)},h=()=>{p.top=r.top.default,p.side=r.side.default,k()},b=v=>{!v.target.closest(".margin-control")&&s.value&&m()};return(v,f)=>(i(),c("div",Pr,[e("button",{class:R(["control-btn",{active:s.value}]),onClick:u,title:"页面边距设置"},f[2]||(f[2]=[ae('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-cc3b3454><rect x="3" y="3" width="18" height="18" rx="2" ry="2" data-v-cc3b3454></rect><line x1="9" y1="9" x2="15" y2="9" data-v-cc3b3454></line><line x1="9" y1="15" x2="15" y2="15" data-v-cc3b3454></line></svg><span class="btn-label" data-v-cc3b3454>页面边距</span><svg class="dropdown-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-cc3b3454><polyline points="6,9 12,15 18,9" data-v-cc3b3454></polyline></svg>',3)]),2),w(ge,{name:"margin-panel"},{default:le(()=>[U(e("div",Or,[f[5]||(f[5]=e("div",{class:"panel-header"},[e("h4",{class:"panel-title"},"页面边距")],-1)),e("div",Fr,[e("div",jr,[f[3]||(f[3]=e("label",{class:"slider-label"},"页面-上边距",-1)),e("div",Br,[U(e("input",{"onUpdate:modelValue":f[0]||(f[0]=C=>p.top=C),type:"range",min:r.top.min,max:r.top.max,step:r.top.step,class:"slider",onInput:k},null,40,Nr),[[E,p.top]]),e("div",Rr,I(p.top)+"px",1)])]),e("div",Yr,[f[4]||(f[4]=e("label",{class:"slider-label"},"页面-左右边距",-1)),e("div",Wr,[U(e("input",{"onUpdate:modelValue":f[1]||(f[1]=C=>p.side=C),type:"range",min:r.side.min,max:r.side.max,step:r.side.step,class:"slider",onInput:k},null,40,qr),[[E,p.side]]),e("div",Ar,I(p.side)+"px",1)])])]),e("div",{class:"panel-footer"},[e("button",{class:"reset-btn",onClick:h}," 重置默认 ")])],512),[[fe,s.value]])]),_:1}),s.value?(i(),c("div",{key:0,class:"margin-overlay",onClick:m})):L("",!0)]))}},Gr=N(Hr,[["__scopeId","data-v-cc3b3454"]]),Kr={class:"spacing-control"},Jr={class:"spacing-panel"},Xr={class:"spacing-sliders"},Qr={class:"slider-group"},Zr={class:"slider-container"},ec=["min","max","step"],tc={class:"slider-value"},sc={class:"slider-group"},oc={class:"slider-container"},nc=["min","max","step"],lc={class:"slider-value"},ac={class:"slider-group"},ic={class:"slider-container"},dc=["min","max","step"],rc={class:"slider-value"},cc={class:"slider-group"},uc={class:"slider-container"},pc=["min","max","step"],mc={class:"slider-value"},vc={__name:"SpacingControl",props:{modelValue:{type:Object,default:()=>({module:24,item:16,line:1.6,modulePadding:16})}},emits:["update:modelValue","change"],setup(x,{emit:M}){const g=x,$=M,s=T(!1),p=J({module:24,item:16,line:1.6,modulePadding:16}),r={module:{min:8,max:48,step:2,default:24},item:{min:4,max:32,step:2,default:16},modulePadding:{min:0,max:40,step:2,default:16},line:{min:1,max:2.5,step:.1,default:1.6}};G(()=>{Object.assign(p,g.modelValue),H(()=>g.modelValue,v=>{Object.assign(p,v)},{deep:!0}),document.addEventListener("click",b)}),X(()=>{document.removeEventListener("click",b)});const u=()=>{s.value=!s.value},m=()=>{s.value=!1},k=()=>{const v={...p};v.line=parseFloat(v.line),console.log("🐛 SpacingControl 间距变化:",{moduleSpacing:v.module,itemSpacing:v.item,lineHeight:v.line,fullValue:v}),$("update:modelValue",v),$("change",v)},h=()=>{p.module=r.module.default,p.item=r.item.default,p.line=r.line.default,p.modulePadding=r.modulePadding.default,k()},b=v=>{!v.target.closest(".spacing-control")&&s.value&&m()};return(v,f)=>(i(),c("div",Kr,[e("button",{class:R(["control-btn",{active:s.value}]),onClick:u,title:"模块间距设置"},f[4]||(f[4]=[ae('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-f9f69dc2><line x1="8" y1="6" x2="21" y2="6" data-v-f9f69dc2></line><line x1="8" y1="12" x2="21" y2="12" data-v-f9f69dc2></line><line x1="8" y1="18" x2="21" y2="18" data-v-f9f69dc2></line><line x1="3" y1="6" x2="3.01" y2="6" data-v-f9f69dc2></line><line x1="3" y1="12" x2="3.01" y2="12" data-v-f9f69dc2></line><line x1="3" y1="18" x2="3.01" y2="18" data-v-f9f69dc2></line></svg><span class="btn-label" data-v-f9f69dc2>模块间距</span><svg class="dropdown-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-f9f69dc2><polyline points="6,9 12,15 18,9" data-v-f9f69dc2></polyline></svg>',3)]),2),w(ge,{name:"spacing-panel"},{default:le(()=>[U(e("div",Jr,[f[9]||(f[9]=e("div",{class:"panel-header"},[e("h4",{class:"panel-title"},"模块间距设置")],-1)),e("div",Xr,[e("div",Qr,[f[5]||(f[5]=e("label",{class:"slider-label"},"模块间-间距",-1)),e("div",Zr,[U(e("input",{"onUpdate:modelValue":f[0]||(f[0]=C=>p.module=C),type:"range",min:r.module.min,max:r.module.max,step:r.module.step,class:"slider",onInput:k},null,40,ec),[[E,p.module]]),e("div",tc,I(p.module)+"px",1)])]),e("div",sc,[f[6]||(f[6]=e("label",{class:"slider-label"},"模块项-间距",-1)),e("div",oc,[U(e("input",{"onUpdate:modelValue":f[1]||(f[1]=C=>p.item=C),type:"range",min:r.item.min,max:r.item.max,step:r.item.step,class:"slider",onInput:k},null,40,nc),[[E,p.item]]),e("div",lc,I(p.item)+"px",1)])]),e("div",ac,[f[7]||(f[7]=e("label",{class:"slider-label"},"模块内-边距",-1)),e("div",ic,[U(e("input",{"onUpdate:modelValue":f[2]||(f[2]=C=>p.modulePadding=C),type:"range",min:r.modulePadding.min,max:r.modulePadding.max,step:r.modulePadding.step,class:"slider",onInput:k},null,40,dc),[[E,p.modulePadding]]),e("div",rc,I(p.modulePadding)+"px",1)])]),e("div",cc,[f[8]||(f[8]=e("label",{class:"slider-label"},"模块内-行距",-1)),e("div",uc,[U(e("input",{"onUpdate:modelValue":f[3]||(f[3]=C=>p.line=C),type:"range",min:r.line.min,max:r.line.max,step:r.line.step,class:"slider",onInput:k},null,40,pc),[[E,p.line]]),e("div",mc,I(p.line),1)])])]),e("div",{class:"panel-footer"},[e("button",{class:"reset-btn",onClick:h}," 重置默认 ")])],512),[[fe,s.value]])]),_:1}),s.value?(i(),c("div",{key:0,class:"spacing-overlay",onClick:m})):L("",!0)]))}},_c=N(vc,[["__scopeId","data-v-f9f69dc2"]]),fc={class:"text-toolbar"},gc={class:"toolbar-header"},hc={class:"header-left"},bc={for:"color-mode-toggle",class:"color-mode-switch"},$c=["checked"],yc={class:"label-text"},kc={class:"toolbar-content"},Cc={class:"toolbar-sections"},Vc={__name:"TextToolbar",props:{settings:{type:Object,default:()=>({})},templateDefaults:{type:Object,default:()=>null}},emits:["settings-change","color-mode-change"],setup(x,{emit:M}){const g=x,$=M,s=T(!1),p=T(!1),r=()=>{var b,v,f,C,_,d,n,t,l;return{fontFamily:"system-ui",fontSize:"14px",textColor:"#333333",margins:{top:((b=g.templateDefaults)==null?void 0:b.marginTop)||60,bottom:((v=g.templateDefaults)==null?void 0:v.marginBottom)||60,side:((f=g.templateDefaults)==null?void 0:f.marginSide)||60},spacings:{module:((C=g.templateDefaults)==null?void 0:C.moduleSpacing)||24,modulePadding:((_=g.templateDefaults)==null?void 0:_.modulePadding)||16,item:((d=g.templateDefaults)==null?void 0:d.itemSpacing)||16,itemPadding:((n=g.templateDefaults)==null?void 0:n.itemPadding)||12,line:((t=g.templateDefaults)==null?void 0:t.lineHeight)||1.6,paragraph:((l=g.templateDefaults)==null?void 0:l.paragraphSpacing)||12,columnGap:40}}},u=J({...r(),...g.settings});G(()=>{H(()=>g.settings,b=>{Object.assign(u,{...r(),...b})},{deep:!0}),H(()=>g.templateDefaults,b=>{b&&(console.log("🎨 TextToolbar 接收到模板默认值变化:",b),Object.assign(u,{...r(),...g.settings}))},{deep:!0})}),X(()=>{});const m=()=>{p.value=!p.value},k=()=>{s.value=!s.value,$("color-mode-change",s.value)},h=()=>{$("settings-change",{...u})};return(b,v)=>(i(),c("div",fc,[e("div",gc,[e("div",hc,[e("button",{class:R(["collapse-btn",{collapsed:p.value}]),onClick:m},v[5]||(v[5]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[e("polyline",{points:"6,9 12,15 18,9"})],-1),e("span",{class:"collapse-text"},"样式工具",-1)]),2),e("label",bc,[e("input",{type:"checkbox",id:"color-mode-toggle",checked:s.value,onChange:k},null,40,$c),v[6]||(v[6]=e("span",{class:"slider"},null,-1)),e("span",yc,I(s.value?"黑白":"原色"),1)])]),v[7]||(v[7]=e("div",{class:"header-right"},null,-1))]),w(ge,{name:"toolbar-collapse"},{default:le(()=>[U(e("div",kc,[e("div",Cc,[w($r,{modelValue:u.fontFamily,"onUpdate:modelValue":v[0]||(v[0]=f=>u.fontFamily=f),onChange:h},null,8,["modelValue"]),w(Dr,{modelValue:u.fontSize,"onUpdate:modelValue":v[1]||(v[1]=f=>u.fontSize=f),onChange:h},null,8,["modelValue"]),w(Lr,{modelValue:u.textColor,"onUpdate:modelValue":v[2]||(v[2]=f=>u.textColor=f),onChange:h},null,8,["modelValue"]),v[8]||(v[8]=e("div",{class:"toolbar-divider"},null,-1)),w(_c,{modelValue:u.spacings,"onUpdate:modelValue":v[3]||(v[3]=f=>u.spacings=f),onChange:h},null,8,["modelValue"]),w(Gr,{modelValue:u.margins,"onUpdate:modelValue":v[4]||(v[4]=f=>u.margins=f),onChange:h},null,8,["modelValue"])])],512),[[fe,!p.value]])]),_:1})]))}},wc=N(Vc,[["__scopeId","data-v-03e6d97d"]]),xc={class:"editor-container"},Dc={class:"editor-main"},Uc={class:"editor-sidebar"},Mc={__name:"EditorLayout",props:{resumeId:{type:String,default:null}},setup(x){const M=x,g=_e(),$=ke(),s=T(!1),p=T(50),r=T(!1),u=T(!1),m=T(null),k=T({fontFamily:"system-ui",fontSize:"14px",textColor:"#333333",margins:{top:60,bottom:60,side:60},spacings:{module:24,modulePadding:16,item:16,itemPadding:12,line:1.6,paragraph:12}}),h=A(()=>{const z=g.currentResumeData.value||{};return console.log("🔍 EditorLayout - currentResumeData 计算:",z),z});A(()=>g.getModuleData(g.currentModuleId.value)||{}),G(async()=>{try{await b(),document.addEventListener("keydown",oe)}catch(z){console.error("编辑器初始化失败:",z)}}),X(()=>{document.removeEventListener("keydown",oe)});const b=async()=>{console.log("开始初始化编辑器...");try{M.resumeId?(await g.loadResume(M.resumeId),console.log("已加载简历:",M.resumeId)):(await g.createNewResume(),console.log("已创建新简历")),console.log("编辑器初始化完成，当前简历数据:",g.resumeData)}catch(z){console.error("编辑器初始化失败:",z),g.reset()}},v=z=>{p.value=z},f=z=>{k.value={...k.value,...z},g.updateSettings(z)},C=z=>{s.value=z},_=z=>{console.log("🎨 接收到模板默认值:",z),m.value=z,k.value={...k.value,margins:{top:z.marginTop||60,bottom:z.marginBottom||60,side:z.marginSide||60},spacings:{module:z.moduleSpacing||24,modulePadding:z.modulePadding||16,item:z.itemSpacing||16,itemPadding:z.itemPadding||12,line:z.lineHeight||1.6,paragraph:z.paragraphSpacing||12}},console.log("🎯 预览设置已更新为模板默认值:",k.value)},d=z=>{const Q=g.moduleConfigs.value.map((S,V)=>({...S,order:z.indexOf(S.id)!==-1?z.indexOf(S.id):V}));g.moduleConfigs.value=Q},n=z=>{g.deleteModule(z)},t=z=>{g.moduleConfigs.value=z,r.value=!1},l=async()=>{console.log("保存简历功能 - 开发中")},a=async z=>{var S;const{format:Q}=z;console.log(`开始下载简历，格式: ${Q}`);try{const V=M.resumeId||((S=g.resumeData.value)==null?void 0:S.id)||"1001";let D=g.resumeData.value;(!D||Object.keys(D).length===0)&&(D={basicInfo:{name:"张三",title:"前端开发工程师",email:"<EMAIL>",phone:"13800138000"},workExperience:[{company:"火花简历",position:"高级前端工程师",duration:"2022-至今",description:"负责简历编辑器的开发和维护"}]},console.warn("使用默认简历数据进行测试")),$.showMessage({type:"info",message:`正在生成${Q.toUpperCase()}文档...`}),await Ce().downloadResume({resumeId:V,format:Q,resumeData:D,settings:k.value}),console.log(`${Q.toUpperCase()}下载完成`),$.showMessage({type:"success",message:`${Q.toUpperCase()}下载完成`})}catch(V){console.error("下载失败:",V),$.showMessage({type:"error",message:`下载失败: ${V.message}`})}},o=()=>{console.log("发送邮件")},y=z=>{g.updateTitle(z)},B=()=>{u.value=!0},oe=z=>{if(z.ctrlKey||z.metaKey)switch(z.key){case"s":z.preventDefault(),l();break;case"z":z.preventDefault(),z.shiftKey?handleRedo():handleUndo();break}};return(z,Q)=>(i(),c("div",xc,[w(Se,{visible:ne($).messageState.visible,type:ne($).messageState.type,message:ne($).messageState.message,duration:ne($).messageState.duration,onClose:ne($).hideMessage},null,8,["visible","type","message","duration","onClose"]),w(rt,{"resume-data":ne(g).resumeData.value,"is-dirty":ne(g).isDirty.value,"is-saving":ne(g).isLoading.value,onSave:l,onDownload:a,onSendEmail:o,onTitleChange:y,onRequestLogin:B},null,8,["resume-data","is-dirty","is-saving"]),e("div",Dc,[w(ut,{"left-width":p.value,onResize:v},{left:le(()=>[e("div",Uc,[w(hs),w(_r)])]),right:le(()=>[e("div",{class:R(["editor-preview",{"grayscale-mode":s.value}])},[w(wc,{settings:k.value,"template-defaults":m.value,onSettingsChange:f,onColorModeChange:C},null,8,["settings","template-defaults"]),w(Me,{"resume-data":h.value,settings:k.value,onModuleOrderChange:d,onModuleDelete:n,onTemplateDefaultsLoaded:_},null,8,["resume-data","settings"])],2)]),_:1},8,["left-width"])]),r.value?(i(),W(ye,{key:0,modules:Array.isArray(ne(g).moduleConfigs.value)?ne(g).moduleConfigs.value:[],onClose:Q[0]||(Q[0]=()=>r.value=!1),onUpdate:t},null,8,["modules"])):L("",!0),u.value?(i(),W(Ie,{key:1,modelValue:u.value,"onUpdate:modelValue":Q[1]||(Q[1]=S=>u.value=S)},null,8,["modelValue"])):L("",!0)]))}},Wc=N(Mc,[["__scopeId","data-v-1737cb93"]]);export{Wc as E};
