/**
 * 统一API配置
 * 提供统一的请求配置、错误处理、认证等
 */

import { ref } from 'vue'

// 全局配置
const config = {
  baseURL: '/api',
  timeout: 10000,
  retryAttempts: 3,
  retryDelay: 1000
}

// 响应式状态
const globalLoading = ref(false)
const globalError = ref(null)

/**
 * 统一的API请求方法
 */
export const useApiConfig = () => {
  
  /**
   * 通用请求方法
   */
  const request = async (url, options = {}) => {
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }

    // 添加认证token
    const token = localStorage.getItem('admin_token') || localStorage.getItem('token')
    if (token) {
      defaultOptions.headers.Authorization = `Bearer ${token}`
    }

    try {
      globalLoading.value = true
      globalError.value = null

      const response = await $fetch(url, defaultOptions)
      
      // 统一响应处理
      if (response.code === 200) {
        return {
          success: true,
          data: response.data,
          message: response.msg
        }
      } else {
        throw new Error(response.msg || '请求失败')
      }
    } catch (error) {
      console.error('API请求失败:', error)
      globalError.value = error.message || '请求失败'
      
      return {
        success: false,
        error: globalError.value
      }
    } finally {
      globalLoading.value = false
    }
  }

  /**
   * GET 请求
   */
  const get = (url, params = {}) => {
    return request(url, {
      method: 'GET',
      params
    })
  }

  /**
   * POST 请求
   */
  const post = (url, data = {}) => {
    return request(url, {
      method: 'POST',
      body: data
    })
  }

  /**
   * PUT 请求
   */
  const put = (url, data = {}) => {
    return request(url, {
      method: 'PUT',
      body: data
    })
  }

  /**
   * DELETE 请求
   */
  const del = (url) => {
    return request(url, {
      method: 'DELETE'
    })
  }

  /**
   * 上传文件
   */
  const upload = (url, formData, onProgress) => {
    return request(url, {
      method: 'POST',
      body: formData,
      onUploadProgress: onProgress
    })
  }

  /**
   * 获取完整的API URL
   */
  const getApiUrl = (endpoint) => {
    // 如果endpoint已经是完整URL，直接返回
    if (endpoint.startsWith('http')) {
      return endpoint
    }
    
    // 如果endpoint已经包含/api前缀，直接返回
    if (endpoint.startsWith('/api')) {
      return endpoint
    }
    
    // 否则添加/api前缀
    return `/api${endpoint.startsWith('/') ? '' : '/'}${endpoint}`
  }

  /**
   * 清除全局错误
   */
  const clearError = () => {
    globalError.value = null
  }

  return {
    // 状态
    globalLoading,
    globalError,
    
    // 方法
    request,
    get,
    post,
    put,
    del,
    upload,
    clearError,
    getApiUrl,
    
    // 配置
    config
  }
} 