<template>
  <div class="template-test-page">
    <h1>模板上传服务测试</h1>
    
    <div class="test-section">
      <h2>1. 测试模板代码检查</h2>
      <div class="test-form">
        <input v-model="testCode" placeholder="输入模板代码" />
        <button @click="testCheckCode" :disabled="checking">
          {{ checking ? '检查中...' : '检查代码' }}
        </button>
        <div v-if="codeResult" class="result">
          结果: {{ codeResult }}
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>2. 测试桶状态检查</h2>
      <div class="test-form">
        <input v-model="testBucket" placeholder="输入桶名称" />
        <button @click="testCheckBucket" :disabled="checkingBucket">
          {{ checkingBucket ? '检查中...' : '检查桶' }}
        </button>
        <div v-if="bucketResult" class="result">
          结果: {{ bucketResult }}
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>3. 认证状态</h2>
      <div class="auth-info">
        <p>认证令牌: {{ authToken ? '已设置' : '未设置' }}</p>
        <button @click="setTestToken">设置测试令牌</button>
      </div>
    </div>

    <div class="test-section">
      <h2>4. 测试日志</h2>
      <div class="log-area">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useTemplateUploadService } from '~/composables/admin/useTemplateUploadService'

const templateUploadService = useTemplateUploadService()

const testCode = ref('test-template-001')
const testBucket = ref('resume-template')
const checking = ref(false)
const checkingBucket = ref(false)
const codeResult = ref('')
const bucketResult = ref('')
const authToken = ref('')
const logs = ref([])

const addLog = (message) => {
  logs.value.push(`${new Date().toLocaleTimeString()}: ${message}`)
}

const testCheckCode = async () => {
  checking.value = true
  codeResult.value = ''
  addLog(`开始检查模板代码: ${testCode.value}`)
  
  try {
    const result = await templateUploadService.checkTemplateCode(testCode.value)
    codeResult.value = result ? '代码可用' : '代码已存在'
    addLog(`代码检查结果: ${codeResult.value}`)
  } catch (error) {
    codeResult.value = `错误: ${error.message}`
    addLog(`代码检查失败: ${error.message}`)
  } finally {
    checking.value = false
  }
}

const testCheckBucket = async () => {
  checkingBucket.value = true
  bucketResult.value = ''
  addLog(`开始检查桶状态: ${testBucket.value}`)
  
  try {
    const result = await templateUploadService.checkBucketStatus(testBucket.value)
    bucketResult.value = result ? '桶可用' : '桶不可用'
    addLog(`桶检查结果: ${bucketResult.value}`)
  } catch (error) {
    bucketResult.value = `错误: ${error.message}`
    addLog(`桶检查失败: ${error.message}`)
  } finally {
    checkingBucket.value = false
  }
}

const setTestToken = () => {
  // 设置一个测试令牌
  localStorage.setItem('auth_token', 'test-admin-token-123')
  authToken.value = 'test-admin-token-123'
  addLog('已设置测试令牌')
}

onMounted(() => {
  authToken.value = localStorage.getItem('auth_token')
  addLog('页面加载完成')
})
</script>

<style scoped>
.template-test-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-form {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 15px;
}

.test-form input {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-width: 200px;
}

.test-form button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-form button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.result {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-top: 10px;
}

.auth-info {
  display: flex;
  gap: 15px;
  align-items: center;
}

.log-area {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  font-family: monospace;
  font-size: 12px;
  margin-bottom: 5px;
}
</style> 