-- 快速创建测试用户脚本
-- 使用明文密码，方便测试

-- 删除现有测试用户（如果存在）
DELETE FROM user_roles WHERE user_id IN (SELECT id FROM users WHERE phone IN ('13800138001', '13800138002'));
DELETE FROM users WHERE phone IN ('13800138001', '13800138002');

-- 创建测试用户
INSERT INTO users (id, phone, username, password, nickname, status, register_platform, create_time, update_time) VALUES
(1001, '13800138001', 'admin', '123456', '系统管理员', 1, 'web', NOW(), NOW()),
(1002, '13800138002', 'superadmin', '123456', '超级管理员', 1, 'web', NOW(), NOW());

-- 创建角色（如果不存在）
INSERT IGNORE INTO roles (id, role_name, role_code, description, status, sort_order, create_time, update_time) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, 1, NOW(), NOW()),
(2, '管理员', 'ADMIN', '系统管理员，拥有大部分管理权限', 1, 2, NOW(), NOW()),
(3, '普通用户', 'USER', '普通用户，只能使用基础功能', 1, 3, NOW(), NOW());

-- 分配角色
INSERT IGNORE INTO user_roles (user_id, role_id, create_time) VALUES
(1001, 2, NOW()),  -- 管理员角色
(1002, 1, NOW());  -- 超级管理员角色

-- 创建权限（如果不存在）
INSERT IGNORE INTO permissions (id, permission_name, permission_code, resource_type, resource_url, parent_id, status, sort_order, create_time, update_time) VALUES
(1, '后台管理', 'admin', 'menu', '/admin', 0, 1, 1, NOW(), NOW()),
(2, '用户管理', 'admin:user', 'menu', '/admin/users', 1, 1, 2, NOW(), NOW()),
(3, '模板管理', 'admin:template', 'menu', '/admin/templates', 1, 1, 3, NOW(), NOW()),
(4, '订单管理', 'admin:order', 'menu', '/admin/orders', 1, 1, 4, NOW(), NOW()),
(5, '系统设置', 'admin:system', 'menu', '/admin/system', 1, 1, 5, NOW(), NOW());

-- 分配权限给角色
INSERT IGNORE INTO role_permissions (role_id, permission_id, create_time) VALUES
-- 超级管理员拥有所有权限
(1, 1, NOW()),
(1, 2, NOW()),
(1, 3, NOW()),
(1, 4, NOW()),
(1, 5, NOW()),
-- 管理员拥有除系统设置外的权限
(2, 1, NOW()),
(2, 2, NOW()),
(2, 3, NOW()),
(2, 4, NOW());

-- 验证创建结果
SELECT 'Users created:' as info;
SELECT id, phone, username, nickname FROM users WHERE phone IN ('13800138001', '13800138002');

SELECT 'User roles assigned:' as info;
SELECT ur.user_id, u.phone, r.role_name, r.role_code 
FROM user_roles ur 
JOIN users u ON ur.user_id = u.id 
JOIN roles r ON ur.role_id = r.id 
WHERE u.phone IN ('13800138001', '13800138002'); 