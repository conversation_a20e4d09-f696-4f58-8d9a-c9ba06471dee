package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建支付订单请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "PaymentCreateRequest", description = "创建支付订单请求")
public class PaymentCreateRequest {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Schema(description = "订单号", required = true, example = "ORD20241222123456789")
    private String orderNo;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    @Schema(description = "支付方式", required = true, example = "wechat", allowableValues = {"wechat", "alipay", "unionpay"})
    private String paymentMethod;

    /**
     * 支付类型
     */
    @NotBlank(message = "支付类型不能为空")
    @Schema(description = "支付类型", required = true, example = "qrcode", allowableValues = {"qrcode", "h5", "app", "web"})
    private String paymentType;

    /**
     * 客户端IP地址
     */
    @NotBlank(message = "客户端IP不能为空")
    @Schema(description = "客户端IP地址", required = true, example = "*************")
    private String clientIp;

    /**
     * 支付成功后跳转地址
     */
    @Schema(description = "支付成功后跳转地址", example = "https://app.resume.com/payment/success")
    private String returnUrl;

    /**
     * 支付回调通知地址
     */
    @NotBlank(message = "回调通知地址不能为空")
    @Schema(description = "支付回调通知地址", required = true, example = "https://api.resume.com/payment/callback/wechat")
    private String notifyUrl;

    /**
     * 设备信息
     */
    @Schema(description = "设备信息")
    private DeviceInfo deviceInfo;

    /**
     * 设备信息内部类
     */
    @Data
    @Schema(name = "DeviceInfo", description = "设备信息")
    public static class DeviceInfo {
        
        /**
         * 平台类型
         */
        @Schema(description = "平台类型", example = "web", allowableValues = {"web", "ios", "android", "h5"})
        private String platform;

        /**
         * 用户代理
         */
        @Schema(description = "用户代理", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        private String userAgent;

        /**
         * 设备ID
         */
        @Schema(description = "设备ID", example = "device_123456")
        private String deviceId;
    }
} 