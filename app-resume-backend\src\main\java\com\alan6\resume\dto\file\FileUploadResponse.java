package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文件上传响应DTO
 * 
 * 主要功能：
 * 1. 返回文件上传成功后的完整信息
 * 2. 包含MinIO对象的详细信息
 * 3. 提供文件访问和管理所需的数据
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "文件上传响应", description = "文件上传成功后的响应信息")
public class FileUploadResponse {

    /**
     * 文件ID
     * 系统生成的唯一标识符
     */
    @Schema(description = "文件ID", example = "F20241222123456789")
    private String fileId;

    /**
     * 文件名
     * 服务器端存储的文件名
     */
    @Schema(description = "文件名", example = "avatar_20241222123456.jpg")
    private String fileName;

    /**
     * 原始文件名
     * 用户上传时的原始文件名
     */
    @Schema(description = "原始文件名", example = "我的头像.jpg")
    private String originalName;

    /**
     * 文件路径
     * 文件在存储系统中的相对路径
     */
    @Schema(description = "文件路径", example = "avatar/2024/12/22/avatar_20241222123456.jpg")
    private String filePath;

    /**
     * 文件URL
     * 文件的访问地址
     */
    @Schema(description = "文件URL", example = "https://minio.example.com/avatar/2024/12/22/avatar_20241222123456.jpg")
    private String fileUrl;

    /**
     * 存储桶名称
     * MinIO存储桶名称
     */
    @Schema(description = "存储桶名称", example = "avatar-bucket")
    private String bucketName;

    /**
     * 对象键
     * MinIO对象的完整键值
     */
    @Schema(description = "对象键", example = "avatar/2024/12/22/avatar_20241222123456.jpg")
    private String objectKey;

    /**
     * ETag
     * MinIO对象的ETag值，用于完整性验证
     */
    @Schema(description = "ETag", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String etag;

    /**
     * 版本ID
     * 对象版本标识符
     */
    @Schema(description = "版本ID", example = "v1.0.0")
    private String versionId;

    /**
     * 文件大小
     * 文件大小，单位：字节
     */
    @Schema(description = "文件大小", example = "102400")
    private Long fileSize;

    /**
     * 文件类型
     * MIME类型
     */
    @Schema(description = "文件类型", example = "image/jpeg")
    private String fileType;

    /**
     * 存储类别
     * MinIO存储类别
     */
    @Schema(description = "存储类别", example = "STANDARD")
    private String storageClass;

    /**
     * 访问策略
     * 文件访问权限策略
     */
    @Schema(description = "访问策略", example = "private")
    private String accessPolicy;

    /**
     * 加密类型
     * 文件加密方式
     */
    @Schema(description = "加密类型", example = "SSE-S3")
    private String encryptionType;

    /**
     * 上传时间
     * 文件上传完成时间
     */
    @Schema(description = "上传时间", example = "2024-12-22 15:30:00")
    private LocalDateTime uploadTime;

    /**
     * 格式化文件大小为可读格式
     * 
     * @return 格式化后的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "未知";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 检查文件是否为图片类型
     * 
     * @return true-图片文件，false-非图片文件
     */
    public boolean isImageFile() {
        return fileType != null && fileType.startsWith("image/");
    }

    /**
     * 检查文件是否为文档类型
     * 
     * @return true-文档文件，false-非文档文件
     */
    public boolean isDocumentFile() {
        return fileType != null && 
               (fileType.equals("application/pdf") || 
                fileType.equals("application/msword") || 
                fileType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                fileType.equals("text/plain"));
    }

    /**
     * 检查文件是否为公开访问
     * 
     * @return true-公开访问，false-私有访问
     */
    public boolean isPublicAccess() {
        return accessPolicy != null && 
               (accessPolicy.equals("public-read") || accessPolicy.equals("public-read-write"));
    }

    /**
     * 创建文件上传响应对象
     * 
     * @param fileId 文件ID
     * @param fileName 文件名
     * @param originalName 原始文件名
     * @param filePath 文件路径
     * @param fileUrl 文件URL
     * @param bucketName 存储桶名称
     * @param objectKey 对象键
     * @param etag ETag
     * @param versionId 版本ID
     * @param fileSize 文件大小
     * @param fileType 文件类型
     * @param storageClass 存储类别
     * @param accessPolicy 访问策略
     * @param encryptionType 加密类型
     * @param uploadTime 上传时间
     * @return 文件上传响应对象
     */
    public static FileUploadResponse create(String fileId, String fileName, String originalName, 
                                          String filePath, String fileUrl, String bucketName, 
                                          String objectKey, String etag, String versionId, 
                                          Long fileSize, String fileType, String storageClass, 
                                          String accessPolicy, String encryptionType, 
                                          LocalDateTime uploadTime) {
        return FileUploadResponse.builder()
                .fileId(fileId)
                .fileName(fileName)
                .originalName(originalName)
                .filePath(filePath)
                .fileUrl(fileUrl)
                .bucketName(bucketName)
                .objectKey(objectKey)
                .etag(etag)
                .versionId(versionId)
                .fileSize(fileSize)
                .fileType(fileType)
                .storageClass(storageClass)
                .accessPolicy(accessPolicy)
                .encryptionType(encryptionType)
                .uploadTime(uploadTime)
                .build();
    }
} 