<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import Icon from '~/components/common/Icon.vue'
import BasicInfoForm from '~/components/editor/sidebar/forms/BasicInfoForm.vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'
import ResumeTemplate from '~/components/editor/preview/ResumeTemplate.vue'

// 测试数据
const testData = reactive({
  name: '',
  title: '',
  phone: '',
  email: '',
  address: '',
  website: '',
  linkedin: '',
  github: '',
  other: '',
  photo: ''
})

// 处理表单更新
const handleFormUpdate = (data) => {
  testData.value = { ...data }
  console.log('表单数据更新:', data)
}

// 页面标题
useHead({
  title: '组件测试 - 火花简历'
})

const editorStore = useEditorStore()

// 获取模块数据的方法
const getModuleData = (moduleId) => {
  const modules = editorStore.resumeData?.value?.modules || {}
  return modules[moduleId] || {}
}

// 初始化数据
const initializeData = async () => {
  console.log('开始初始化数据...')
  try {
    await editorStore.createNewResume()
    console.log('数据初始化完成')
  } catch (error) {
    console.error('初始化失败:', error)
  }
}

// 更新基本信息测试
const updateBasicInfoTest = () => {
  const newBasicInfo = {
    name: '测试用户',
    title: '高级前端工程师',
    phone: '13999999999',
    email: '<EMAIL>',
    address: '上海市浦东新区'
  }
  
  editorStore.updateModuleData('basic', newBasicInfo)
  console.log('基本信息已更新')
}

// 更新技能测试
const updateSkills = () => {
  const newSkills = [
    { name: 'Vue.js', level: 95 },
    { name: 'React', level: 88 },
    { name: 'TypeScript', level: 82 }
  ]
  
  editorStore.updateModuleData('skills', newSkills)
  console.log('技能信息已更新')
}

// 添加大量内容测试分页
const addLargeContent = () => {
  // 添加多个工作经历
  const workExperiences = []
  for (let i = 1; i <= 8; i++) {
    workExperiences.push({
      id: `work-${i}`,
      position: `高级前端工程师 ${i}`,
      company: `科技公司 ${i}`,
      startDate: '2020-01',
      endDate: i === 8 ? '' : '2021-12',
      description: `负责公司${i}的前端开发工作，包括但不限于：\n1. 使用Vue.js、React等现代前端框架开发用户界面\n2. 与后端团队协作，完成API接口对接和数据交互\n3. 优化前端性能，提升用户体验\n4. 参与产品需求讨论，提供技术方案\n5. 指导初级开发人员，进行代码审查\n6. 维护和优化现有项目代码`
    })
  }
  
  editorStore.updateModuleData('work', workExperiences)
  console.log('已添加大量工作经历，测试分页功能')
}

// 页面加载时初始化
onMounted(async () => {
  console.log('测试页面加载，开始初始化...')
  await initializeData()
})

// 从EditorStore获取显示数据
const displayData = computed(() => {
  const basicData = editorStore.resumeData.value?.modules?.basic
  return basicData || {}
})

// EditorStore的完整数据
const storeData = computed(() => editorStore.resumeData.value)

// 防抖更新
let updateTimer = null
const updateBasicInfo = () => {
  if (updateTimer) {
    clearTimeout(updateTimer)
  }
  
  updateTimer = setTimeout(() => {
    console.log('更新基本信息:', testData)
    editorStore.updateModuleData('basic', { ...testData })
  }, 300)
}

// 处理模块操作
const handleModuleMove = (moduleId, direction) => {
  console.log('模块移动:', moduleId, direction)
}

const handleModuleDelete = (moduleId) => {
  console.log('模块删除:', moduleId)
}

// 监听EditorStore数据变化
watch(() => editorStore.resumeData.value, (newData) => {
  console.log('EditorStore数据变化:', newData)
}, { deep: true })

// 测试模块顺序变更
const testModuleOrderChange = () => {
  const currentModules = editorStore.moduleConfigs.value || []
  const updatedModules = currentModules.map(module => ({ ...module }))
  
  // 交换前两个模块的顺序
  if (updatedModules.length >= 2) {
    const temp = updatedModules[0].order
    updatedModules[0].order = updatedModules[1].order
    updatedModules[1].order = temp
    
    console.log('测试模块顺序变更，更新前:', currentModules)
    console.log('测试模块顺序变更，更新后:', updatedModules)
    
    // 更新模块配置
    editorStore.moduleConfigs.value = updatedModules
  }
}

// 测试数据流
const testDataFlow = () => {
  console.log('=== 测试数据流 ===')
  console.log('EditorStore resumeData:', editorStore.resumeData.value)
  console.log('EditorStore currentModuleId:', editorStore.currentModuleId.value)
  
  // 测试更新基本信息
  const testData = {
    name: '测试姓名 - ' + Date.now(),
    title: '测试职位',
    phone: '13800138000',
    email: '<EMAIL>'
  }
  
  console.log('正在更新基本信息:', testData)
  editorStore.updateModuleData('basic_info', testData)
  
  // 检查更新后的数据
  setTimeout(() => {
    console.log('更新后的数据:', editorStore.getModuleData('basic_info'))
    console.log('完整的resumeData:', editorStore.resumeData.value)
  }, 100)
}
</script>

<template>
  <div class="test-page">
    <h1>数据同步测试页面</h1>
    
    <!-- 左侧：编辑区模拟 -->
    <div class="test-section">
      <h2>左侧编辑区（模拟BasicInfoForm）</h2>
      <div class="form-group">
        <label>姓名：</label>
        <input 
          v-model="testData.name" 
          type="text" 
          @input="updateBasicInfo"
          placeholder="输入姓名"
        />
      </div>
      
      <div class="form-group">
        <label>职位：</label>
        <input 
          v-model="testData.title" 
          type="text" 
          @input="updateBasicInfo"
          placeholder="输入职位"
        />
      </div>
      
      <div class="form-group">
        <label>手机：</label>
        <input 
          v-model="testData.phone" 
          type="text" 
          @input="updateBasicInfo"
          placeholder="输入手机号"
        />
      </div>
      
      <div class="form-group">
        <label>邮箱：</label>
        <input 
          v-model="testData.email" 
          type="text" 
          @input="updateBasicInfo"
          placeholder="输入邮箱"
        />
      </div>
    </div>
    
    <!-- 右侧：预览区模拟 -->
    <div class="test-section">
      <h2>右侧预览区（实时显示）</h2>
      <div class="preview-content">
        <p><strong>姓名：</strong>{{ displayData.name || '未填写' }}</p>
        <p><strong>职位：</strong>{{ displayData.title || '未填写' }}</p>
        <p><strong>手机：</strong>{{ displayData.phone || '未填写' }}</p>
        <p><strong>邮箱：</strong>{{ displayData.email || '未填写' }}</p>
      </div>
    </div>
    
    <!-- EditorStore状态显示 -->
    <div class="test-section">
      <h2>EditorStore状态</h2>
      <pre>{{ JSON.stringify(storeData, null, 2) }}</pre>
    </div>
    
    <!-- 模块数据详情 -->
    <div class="test-section">
      <h2>模块数据详情</h2>
      <button @click="testModuleOrderChange" class="test-btn">测试模块顺序变更</button>
      <button @click="addLargeContent" class="test-btn">添加大量内容测试分页</button>
      <div v-if="storeData?.modules" class="modules-info">
        <div v-for="(moduleData, moduleId) in storeData.modules" :key="moduleId" class="module-item">
          <h3>{{ moduleId }}</h3>
          <p><strong>数据：</strong></p>
          <pre>{{ JSON.stringify(moduleData, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- 实际ResumeTemplate组件测试 -->
    <div class="test-section">
      <h2>实际ResumeTemplate组件</h2>
      <div class="template-container">
        <ResumeTemplate 
          :resumeData="storeData" 
          :isDraggable="false"
          @module-move="handleModuleMove"
          @module-delete="handleModuleDelete"
        />
      </div>
    </div>

    <!-- 测试按钮区域 -->
    <div class="test-section">
      <h2>测试数据更新</h2>
      <div class="button-group">
        <button @click="updateBasicInfoTest" class="test-btn">更新基本信息</button>
        <button @click="updateSkills" class="test-btn">更新技能</button>
        <button @click="addLargeContent" class="test-btn">添加大量内容</button>
        <button @click="testDataFlow" class="test-btn">测试数据流</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: inline-block;
  width: 80px;
  font-weight: bold;
}

.form-group input {
  width: 300px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.preview-content {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #eee;
}

.modules-info {
  display: grid;
  gap: 15px;
}

.module-item {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #eee;
}

.module-item h3 {
  margin-top: 0;
  color: #2563eb;
}

.module-item pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.template-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>

<style scoped>
.test-page {
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.test-page h1 {
  grid-column: 1 / -1;
  text-align: center;
  margin-bottom: 2rem;
}

.test-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  background: white;
}

.test-section h2 {
  margin-bottom: 1rem;
  color: #374151;
  font-size: 1.25rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.preview-content p {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 4px;
}

.template-container {
  grid-column: 1 / -1;
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: #f8fafc;
}

pre {
  background: #f3f4f6;
  padding: 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.test-btn {
  background: #3B82F6;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 16px;
  font-size: 14px;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background: #2563EB;
}
</style>
