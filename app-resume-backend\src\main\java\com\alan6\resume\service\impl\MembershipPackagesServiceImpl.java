package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.membership.MembershipPackageResponse;
import com.alan6.resume.entity.MembershipPackages;
import com.alan6.resume.mapper.MembershipPackagesMapper;
import com.alan6.resume.service.IMembershipPackagesService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员套餐表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
public class MembershipPackagesServiceImpl extends ServiceImpl<MembershipPackagesMapper, MembershipPackages> implements IMembershipPackagesService {

    @Override
    public List<MembershipPackageResponse> getPackageList(Byte status, Boolean recommended) {
        log.info("获取会员套餐列表，状态：{}，是否推荐：{}", status, recommended);
        
        // 构建查询条件
        LambdaQueryWrapper<MembershipPackages> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MembershipPackages::getIsDeleted, 0); // 未删除
        
        // 状态过滤
        if (status != null) {
            queryWrapper.eq(MembershipPackages::getStatus, status);
        } else {
            queryWrapper.eq(MembershipPackages::getStatus, 1); // 默认只查询上架状态
        }
        
        // 推荐过滤
        if (recommended != null && recommended) {
            queryWrapper.eq(MembershipPackages::getIsRecommended, 1);
        }
        
        // 按排序权重排序
        queryWrapper.orderByDesc(MembershipPackages::getSortOrder);
        queryWrapper.orderByAsc(MembershipPackages::getId);
        
        List<MembershipPackages> packages = list(queryWrapper);
        
        // 转换为响应DTO
        List<MembershipPackageResponse> responses = packages.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        log.info("获取到{}个套餐", responses.size());
        return responses;
    }

    @Override
    public MembershipPackageResponse getPackageDetail(Long packageId) {
        log.info("获取套餐详情，套餐ID：{}", packageId);
        
        if (packageId == null) {
            throw new BusinessException("套餐ID不能为空");
        }
        
        MembershipPackages membershipPackage = getById(packageId);
        if (membershipPackage == null || membershipPackage.getIsDeleted() == 1) {
            throw new BusinessException("套餐不存在");
        }
        
        if (membershipPackage.getStatus() == 0) {
            throw new BusinessException("套餐已下架");
        }
        
        return convertToResponse(membershipPackage);
    }

    @Override
    public MembershipPackageResponse getRecommendedPackage() {
        log.info("获取推荐套餐");
        
        LambdaQueryWrapper<MembershipPackages> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MembershipPackages::getIsDeleted, 0)
                .eq(MembershipPackages::getStatus, 1)
                .eq(MembershipPackages::getIsRecommended, 1)
                .orderByDesc(MembershipPackages::getSortOrder)
                .last("LIMIT 1");
        
        MembershipPackages recommendedPackage = getOne(queryWrapper);
        if (recommendedPackage == null) {
            throw new BusinessException("暂无推荐套餐");
        }
        
        return convertToResponse(recommendedPackage);
    }

    @Override
    public MembershipPackages getByPackageCode(String packageCode) {
        log.info("根据套餐代码获取套餐信息，代码：{}", packageCode);
        
        if (packageCode == null || packageCode.trim().isEmpty()) {
            throw new BusinessException("套餐代码不能为空");
        }
        
        LambdaQueryWrapper<MembershipPackages> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MembershipPackages::getPackageCode, packageCode)
                .eq(MembershipPackages::getIsDeleted, 0);
        
        return getOne(queryWrapper);
    }

    @Override
    public Boolean isPackageAvailable(Long packageId) {
        log.info("检查套餐是否可用，套餐ID：{}", packageId);
        
        if (packageId == null) {
            return false;
        }
        
        MembershipPackages membershipPackage = getById(packageId);
        return membershipPackage != null 
                && membershipPackage.getIsDeleted() == 0 
                && membershipPackage.getStatus() == 1;
    }

    /**
     * 将实体转换为响应DTO
     * 
     * @param membershipPackage 套餐实体
     * @return 响应DTO
     */
    private MembershipPackageResponse convertToResponse(MembershipPackages membershipPackage) {
        if (membershipPackage == null) {
            return null;
        }
        
        // 构建特性列表
        List<String> features = buildFeatures(membershipPackage);
        
        return MembershipPackageResponse.builder()
                .id(membershipPackage.getId())
                .packageName(membershipPackage.getPackageName())
                .packageCode(membershipPackage.getPackageCode())
                .description(membershipPackage.getDescription())
                .durationType(membershipPackage.getDurationType())
                .durationValue(membershipPackage.getDurationValue())
                .originalPrice(membershipPackage.getOriginalPrice())
                .currentPrice(membershipPackage.getCurrentPrice())
                .maxResumeCount(membershipPackage.getMaxResumeCount())
                .maxExportCount(membershipPackage.getMaxExportCount())
                .premiumTemplates(membershipPackage.getPremiumTemplates() == 1)
                .isRecommended(membershipPackage.getIsRecommended() == 1)
                .sortOrder(membershipPackage.getSortOrder())
                .features(features)
                .build();
    }

    /**
     * 构建套餐特性列表
     * 
     * @param membershipPackage 套餐实体
     * @return 特性列表
     */
    private List<String> buildFeatures(MembershipPackages membershipPackage) {
        List<String> features = Arrays.asList();
        
        // 根据套餐类型构建不同的特性列表
        if ("monthly".equals(membershipPackage.getPackageCode())) {
            features = Arrays.asList(
                "无限制创建简历",
                "使用所有付费模板", 
                "无限制导出次数",
                "优先客服支持"
            );
        } else if ("yearly".equals(membershipPackage.getPackageCode())) {
            features = Arrays.asList(
                "包含月度会员所有特权",
                "专属客服一对一服务",
                "优先体验新功能",
                "专属会员标识"
            );
        } else {
            // 通用特性构建逻辑
            features = Arrays.asList(
                membershipPackage.getMaxResumeCount() == -1 ? "无限制创建简历" : "最多创建" + membershipPackage.getMaxResumeCount() + "份简历",
                membershipPackage.getPremiumTemplates() == 1 ? "使用所有付费模板" : "仅限免费模板",
                membershipPackage.getMaxExportCount() == -1 ? "无限制导出次数" : "每月最多导出" + membershipPackage.getMaxExportCount() + "次",
                "客服支持"
            );
        }
        
        return features;
    }

}
