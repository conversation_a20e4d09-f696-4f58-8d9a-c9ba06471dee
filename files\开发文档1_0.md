# 简历制作应用开发文档 v1.0

## 1. 项目概述

### 1.1 项目介绍
本项目是一个功能完善的在线简历制作平台，采用前后端分离架构，支持多端访问。用户可以通过Web端、小程序、App等多种方式访问平台，选择专业模板，在线编辑个人简历，并支持多种格式导出和分享功能。

### 1.2 项目特色
- **多端支持**: Web端、微信小程序、支付宝小程序、App等全平台覆盖
- **国际化**: 支持中文、英文、日文、韩文等多语言
- **多样化模板**: 提供丰富的免费和付费简历模板
- **智能编辑**: 所见即所得的在线编辑体验
- **会员体系**: 完善的付费会员机制和权限控制
- **云端同步**: 数据云端存储，多设备同步访问
- **安全可靠**: 完善的用户认证和数据安全保护

### 1.3 目标用户
- 求职者：应届毕业生、职场人士、转行人群
- 学生群体：在校学生、实习生
- 自由职业者：设计师、程序员、咨询师等
- HR招聘人员：查看和评估候选人简历

## 2. 技术架构

### 2.1 整体架构
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│   Web前端   │  │ 小程序前端  │  │   App前端   │  │  管理后台   │
│  (Vue.js)   │  │  (UniApp)   │  │  (UniApp)   │  │  (Vue.js)   │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘
       │                │                │                │
       └────────────────┼────────────────┼────────────────┘
                        │                │
                        ▼                ▼
                 ┌─────────────────────────────┐
                 │        API网关             │
                 │       (Nginx)              │
                 └─────────────────────────────┘
                              │
                              ▼
                 ┌─────────────────────────────┐
                 │       后端服务集群          │
                 │    (Spring Boot)           │
                 │  ┌─────────┐ ┌─────────┐   │
                 │  │用户服务 │ │简历服务 │   │
                 │  └─────────┘ └─────────┘   │
                 │  ┌─────────┐ ┌─────────┐   │
                 │  │支付服务 │ │文件服务 │   │
                 │  └─────────┘ └─────────┘   │
                 └─────────────────────────────┘
                              │
                    ┌─────────┼─────────┐
                    ▼         ▼         ▼
            ┌─────────┐ ┌─────────┐ ┌─────────┐
            │  MySQL  │ │  Redis  │ │   OSS   │
            │  数据库  │ │  缓存   │ │ 文件存储 │
            └─────────┘ └─────────┘ └─────────┘
```

### 2.2 微服务划分
- **用户服务**: 用户注册、登录、认证、权限管理
- **简历服务**: 简历创建、编辑、模板管理、导出
- **支付服务**: 订单管理、支付处理、会员管理
- **文件服务**: 文件上传、存储、CDN分发
- **通知服务**: 消息推送、邮件发送、短信发送

## 3. 技术栈详情

### 3.1 后端技术栈
- **开发框架**: Spring Boot 3.2.12
- **开发语言**: Java 17
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **持久层框架**: MyBatis-Plus 3.5+
- **安全框架**: Spring Security 6.0 + JWT
- **API文档**: Swagger 3 + Knife4j
- **对象映射**: MapStruct
- **工具库**: Lombok、Hutool
- **消息队列**: RabbitMQ 3.12+
- **搜索引擎**: Elasticsearch 8.0+
- **文件存储**: 阿里云OSS / 腾讯云COS
- **支付集成**: 微信支付v3 + 支付宝开放平台
- **容器化**: Docker + Docker Compose
- **监控**: Spring Boot Actuator + Micrometer

### 3.2 前端技术栈

#### 3.2.1 Web端技术栈
- **开发框架**: Vue 3.4+ + TypeScript
- **构建工具**: Vite 5.0+
- **UI组件库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.6+
- **CSS预处理器**: Sass/SCSS
- **代码规范**: ESLint + Prettier
- **打包优化**: Rollup
- **图表库**: ECharts 5.4+
- **富文本编辑器**: TinyMCE / Quill
- **PDF生成**: html2canvas + jsPDF

#### 3.2.2 UniApp端技术栈
- **开发框架**: UniApp + Vue 3 + TypeScript
- **UI组件库**: uView Plus 3.0+
- **状态管理**: Pinia
- **HTTP请求**: uni.request封装
- **小程序SDK**: 微信小程序SDK、支付宝小程序SDK
- **支付集成**: 微信支付、支付宝支付
- **地图服务**: 腾讯地图、高德地图
- **统计分析**: 微信小程序统计、友盟统计

### 3.3 DevOps工具链
- **版本控制**: Git + GitLab
- **CI/CD**: GitLab CI / Jenkins
- **容器编排**: Docker Swarm / Kubernetes
- **服务网格**: Istio
- **日志收集**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **监控告警**: Prometheus + Grafana + AlertManager
- **链路追踪**: Jaeger / SkyWalking

## 4. 数据库设计详解

### 4.1 数据库设计原则
- **标准化设计**: 遵循第三范式，避免数据冗余
- **性能优化**: 合理设置索引，优化查询性能
- **扩展性**: 预留扩展字段，支持业务发展
- **安全性**: 敏感数据加密存储，设置访问权限
- **国际化**: 支持UTF8mb4字符集，兼容多语言

### 4.2 核心表组说明

#### 4.2.1 用户管理表组
- **users**: 用户基础信息表，存储用户账号、个人信息、偏好设置
- **user_third_party_auths**: 第三方授权表，支持微信、QQ、支付宝等平台登录
- **user_merge_records**: 账号合并记录表，处理用户多账号合并场景

#### 4.2.2 会员体系表组
- **membership_packages**: 会员套餐表，定义不同会员等级和权益
- **user_memberships**: 用户会员表，记录用户会员状态和使用情况

#### 4.2.3 简历模板表组
- **template_categories**: 模板分类表，按行业、风格等维度分类
- **resume_templates**: 简历模板表，存储模板信息和配置数据

#### 4.2.4 简历数据表组
- **resumes**: 简历主表，存储简历基本信息和配置
- **resume_basic_info**: 基本信息表，个人联系方式和求职意向
- **resume_educations**: 教育经历表，学校、专业、学历信息
- **resume_work_experiences**: 工作经历表，公司、职位、工作描述
- **resume_projects**: 项目经验表，项目名称、技术栈、个人贡献
- **resume_skills**: 技能标签表，技能名称、熟练程度、分类
- **resume_certificates**: 证书资质表，证书名称、颁发机构、有效期
- **resume_awards**: 获奖荣誉表，奖项名称、级别、颁发机构
- **resume_languages**: 语言能力表，语言种类、水平、证书成绩
- **其他简历模块表**: 实习经历、研究经历、论文专利、培训经历、志愿服务、作品集、自我评价、推荐人等

#### 4.2.5 用户行为表组
- **user_favorites**: 收藏表，用户收藏的模板和简历
- **share_records**: 分享记录表，简历分享链接和统计
- **resume_export_records**: 导出记录表，简历导出历史和格式
- **user_operation_logs**: 操作日志表，用户行为轨迹记录
- **user_access_logs**: 访问日志表，IP地理位置和语言检测

#### 4.2.6 支付订单表组
- **orders**: 订单表，会员充值和模板购买订单

#### 4.2.7 系统配置表组
- **system_configs**: 系统配置表，全局参数配置
- **supported_languages**: 支持语言表，多语言配置信息

### 4.3 索引设计策略
- **主键索引**: 所有表使用BIGINT自增主键
- **唯一索引**: 用户邮箱、手机号、第三方OpenID等唯一标识
- **复合索引**: 常用查询条件组合，如用户ID+时间范围
- **覆盖索引**: 避免回表查询，提升查询性能
- **前缀索引**: 长字符串字段使用前缀索引节省空间

## 5. 核心功能模块

### 5.1 用户管理模块

#### 5.1.1 功能特性
- 多种注册方式（邮箱、手机、第三方授权）
- 第三方登录（微信、QQ、支付宝等）
- 账号合并机制
- 用户信息管理
- 多语言支持
- 用户权限控制
- 登录状态管理
- 密码安全策略

#### 5.1.2 主要接口
- 用户认证相关
  - POST /api/auth/register - 用户注册
  - POST /api/auth/login - 用户登录
  - POST /api/auth/logout - 用户登出
  - POST /api/auth/refresh - 刷新Token
  - POST /api/auth/send-code - 发送验证码
  - POST /api/auth/third-party - 第三方登录

- 用户信息相关
  - GET /api/user/profile - 获取用户信息
  - PUT /api/user/profile - 更新用户信息
  - POST /api/user/avatar - 上传头像
  - PUT /api/user/password - 修改密码
  - GET /api/user/settings - 获取用户设置
  - PUT /api/user/settings - 更新用户设置

### 5.2 简历管理模块

#### 5.2.1 功能特性
- 简历创建与编辑
- 多种简历模块（基本信息、教育经历、工作经验等）
- 模块显示/隐藏配置
- 简历预览与导出
- 多语言简历支持
- 简历版本管理
- 简历模板应用
- 智能内容推荐

#### 5.2.2 主要接口
- 简历管理
  - GET /api/resume/list - 获取简历列表
  - POST /api/resume - 创建简历
  - GET /api/resume/detail/{id} - 获取简历详情
  - PUT /api/resume/update/{id} - 更新简历
  - DELETE /api/resume/delete/{id} - 删除简历
  - POST /api/resume/copy/{id} - 复制简历

- 简历模块管理
  - GET /api/resume/modules/{id} - 获取简历模块
  - PUT /api/resume/modules/{id} - 更新模块配置
  - POST /api/resume/basic/{id} - 更新基本信息
  - POST /api/resume/education/{id} - 添加教育经历
  - PUT /api/resume/education/{id}/{eduId} - 更新教育经历
  - DELETE /api/resume/education/{eduId} - 删除教育经历
  - POST /api/resume/work/{id} - 添加工作经历
  - PUT /api/resume/work/{id}/{workId} - 更新工作经历
  - DELETE /api/resume/work/{workId} - 删除工作经历
  - POST /api/resume/project/{id} - 添加项目经验
  - PUT /api/resume/project/{id}/{projectId} - 更新项目经验
  - DELETE /api/resume/project/{projectId} - 删除项目经验
  - POST /api/resume/skill/{id} - 添加技能
  - PUT /api/resume/skill/{id}/{skillId} - 更新技能
  - DELETE /api/resume/skill/{skillId} - 删除技能

- 简历导出分享
  - POST /api/resume/export/{id} - 导出简历
  - GET /api/resume/preview/{id} - 预览简历
  - POST /api/resume/share/{id} - 创建分享链接
  - GET /api/resume/share/view/{code} - 访问分享简历

### 5.3 模板管理模块

#### 5.3.1 功能特性
- 模板分类管理
- 免费/付费模板
- 模板预览与使用
- 模板收藏功能
- 模板搜索筛选
- 模板使用统计
- 模板个性化定制
- 模板评价系统

#### 5.3.2 主要接口
- 模板相关
  - GET /api/template/categories - 获取模板分类
  - GET /api/template/list - 获取模板列表
  - GET /api/template/detail/{id} - 获取模板详情
  - POST /api/template/use/{id} - 使用模板创建简历
  - GET /api/template/search - 搜索模板
  - GET /api/template/recommend - 推荐模板

- 收藏相关
  - POST /api/favorite/template/{id} - 收藏模板
  - DELETE /api/favorite/template/{id} - 取消收藏
  - GET /api/favorite/templates - 获取收藏模板列表

### 5.4 会员体系模块

#### 5.4.1 功能特性
- 会员套餐管理
- 权限控制（简历数量、导出次数、付费模板）
- 会员到期提醒
- 使用量统计
- 会员特权展示
- 升级引导流程

#### 5.4.2 主要接口
- 会员相关
  - GET /api/membership/packages - 获取会员套餐
  - GET /api/membership/current - 获取当前会员信息
  - POST /api/membership/purchase - 购买会员
  - GET /api/membership/usage - 获取使用统计
  - GET /api/membership/benefits - 获取会员权益

### 5.5 支付订单模块

#### 5.5.1 功能特性
- 微信支付集成
- 支付宝支付集成
- 订单管理
- 支付回调处理
- 退款处理
- 发票管理
- 支付安全保障

#### 5.5.2 主要接口
- 支付相关
  - POST /api/payment/create - 创建支付订单
  - GET /api/payment/status/{orderNo} - 查询支付状态
  - POST /api/payment/callback/wechat - 微信支付回调
  - POST /api/payment/callback/alipay - 支付宝支付回调
  - POST /api/payment/refund/{orderNo} - 申请退款

- 订单相关
  - GET /api/order/list - 获取订单列表
  - GET /api/order/detail/{orderNo} - 获取订单详情
  - PUT /api/order/cancel/{orderNo} - 取消订单

### 5.6 文件管理模块

#### 5.6.1 功能特性
- 文件上传下载
- 图片压缩处理
- 文件格式验证
- 存储空间管理
- CDN加速分发
- 文件安全扫描

#### 5.6.2 主要接口
- 文件相关
  - POST /api/file/upload - 文件上传
  - GET /api/file/download/{fileId} - 文件下载
  - DELETE /api/file/delete/{fileId} - 删除文件
  - GET /api/file/list - 获取文件列表

## 6. 开发规范

### 6.1 代码规范

#### 6.1.1 命名规范
- **类名**: PascalCase（如：UserController、ResumeService）
- **方法名**: camelCase（如：getUserInfo、createResume）
- **变量名**: camelCase（如：userId、resumeList）
- **常量**: UPPER_SNAKE_CASE（如：MAX_FILE_SIZE、DEFAULT_PAGE_SIZE）
- **包名**: 全小写，用点分隔（如：com.alan6.resume.service）
- **数据库表名**: 小写下划线（如：user_profiles、resume_templates）
- **数据库字段名**: 小写下划线（如：user_id、created_time）

#### 6.1.2 注释规范
- 类和公共方法必须有Javadoc注释
- 复杂业务逻辑添加行内注释
- 注释内容要准确描述功能和参数
- 及时更新注释内容，保持与代码同步

#### 6.1.3 异常处理
- 统一异常处理机制
- 自定义业务异常
- 规范错误码定义
- 记录详细错误日志

#### 6.1.4 日志规范
- 使用统一的日志格式
- 合理设置日志级别
- 敏感信息脱敏处理
- 性能关键点记录耗时

### 6.2 API设计规范

#### 6.2.1 RESTful设计
- **GET**: 查询操作，幂等操作
- **POST**: 创建操作，非幂等操作
- **PUT**: 更新操作，幂等操作
- **DELETE**: 删除操作，幂等操作
- **PATCH**: 部分更新操作

#### 6.2.2 URL设计规范
- 使用名词而非动词
- 资源层级不超过3层
- 使用复数形式表示集合
- 路径参数置于URL末尾
- 使用连字符分隔单词

#### 6.2.3 统一返回格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1234567890
}
```

#### 6.2.4 分页查询格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  },
  "timestamp": 1234567890
}
```

#### 6.2.5 错误码定义
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误
- 1001-1999: 用户相关错误
- 2001-2999: 简历相关错误
- 3001-3999: 模板相关错误
- 4001-4999: 支付相关错误

### 6.3 数据库规范

#### 6.3.1 表设计规范
- 表名使用小写下划线命名
- 主键统一使用id，类型为BIGINT
- 创建时间字段命名为create_time
- 更新时间字段命名为update_time
- 软删除字段命名为is_deleted
- 状态字段使用TINYINT类型

#### 6.3.2 索引规范
- 主键自动创建聚集索引
- 外键字段创建普通索引
- 查询条件字段创建索引
- 复合索引遵循最左前缀原则
- 避免在小表上创建索引

#### 6.3.3 SQL编写规范
- 禁止使用SELECT *
- 合理使用LIMIT限制返回记录数
- 复杂查询拆分为多个简单查询
- 使用预编译语句防止SQL注入

### 6.4 安全规范

#### 6.4.1 认证授权
- 使用普通Token进行身份认证
- Token设置合理的过期时间
- 敏感操作要求重新认证
- 实现细粒度的权限控制

#### 6.4.2 数据安全
- 密码使用BCrypt加密存储
- 敏感信息传输使用HTTPS
- 个人信息脱敏处理
- 定期备份重要数据

#### 6.4.3 接口安全
- 实现接口访问频率限制
- 验证请求参数的合法性
- 防止SQL注入和XSS攻击
- 记录安全相关的操作日志

## 7. 部署配置

### 7.1 开发环境

#### 7.1.1 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 7.0+
- Node.js 18+
- Git

#### 7.1.2 启动步骤
1. **数据库初始化**
   - 创建数据库
   - 执行建表脚本
   - 导入初始数据

2. **后端启动**
   - 配置数据库连接
   - 配置Redis连接
   - 启动Spring Boot应用

3. **Web前端启动**
   - 安装依赖包
   - 配置API接口地址
   - 启动开发服务器

4. **UniApp启动**
   - 使用HBuilderX导入项目
   - 配置小程序AppID
   - 运行到相应平台

### 7.2 测试环境

#### 7.2.1 环境配置
- 使用Docker容器化部署
- 配置CI/CD自动部署
- 集成自动化测试
- 配置监控告警

#### 7.2.2 测试数据
- 准备完整的测试数据集
- 模拟各种业务场景
- 压力测试和性能测试
- 安全漏洞扫描

### 7.3 生产环境

#### 7.3.1 基础架构
- 负载均衡配置
- 数据库主从复制
- Redis集群部署
- CDN加速配置
- 备份恢复策略

#### 7.3.2 监控运维
- 应用性能监控
- 数据库性能监控
- 服务器资源监控
- 日志收集分析
- 告警通知机制

## 8. 性能优化

### 8.1 后端优化

#### 8.1.1 数据库优化
- 合理设计索引
- 优化SQL查询语句
- 使用连接池管理连接
- 读写分离配置
- 分库分表策略

#### 8.1.2 缓存优化
- Redis缓存热点数据
- 多级缓存架构
- 缓存更新策略
- 缓存穿透防护
- 缓存雪崩防护

#### 8.1.3 接口优化
- 异步处理耗时操作
- 接口响应数据压缩
- 分页查询优化
- 批量操作接口
- 接口版本管理

### 8.2 前端优化

#### 8.2.1 加载优化
- 代码分割和懒加载
- 图片懒加载
- 资源预加载
- CDN加速
- 浏览器缓存策略

#### 8.2.2 渲染优化
- 虚拟滚动
- 防抖和节流
- 避免频繁DOM操作
- 优化动画性能
- 减少重绘重排

#### 8.2.3 体验优化
- 骨架屏加载
- 离线缓存
- 错误边界处理
- 用户操作反馈
- 无障碍访问支持

## 9. 测试策略

### 9.1 单元测试
- 使用JUnit进行Java单元测试
- 使用Jest进行前端单元测试
- 测试覆盖率要求达到80%以上
- 核心业务逻辑必须有单元测试

### 9.2 集成测试
- API接口集成测试
- 数据库集成测试
- 第三方服务集成测试
- 端到端业务流程测试

### 9.3 性能测试
- 接口压力测试
- 数据库性能测试
- 前端页面性能测试
- 并发用户测试

### 9.4 安全测试
- SQL注入测试
- XSS攻击测试
- CSRF攻击测试
- 权限越权测试
- 敏感信息泄露测试

## 10. 版本管理

### 10.1 版本号规范
- 使用语义化版本号：MAJOR.MINOR.PATCH
- MAJOR：不兼容的API修改
- MINOR：向下兼容的功能新增
- PATCH：向下兼容的问题修正

### 10.2 分支管理
- master分支：生产环境代码
- develop分支：开发环境代码
- feature分支：功能开发分支
- hotfix分支：紧急修复分支
- release分支：版本发布分支

### 10.3 发布流程
1. 功能开发完成合并到develop分支
2. 创建release分支进行测试
3. 测试通过后合并到master分支
4. 打标签发布新版本
5. 部署到生产环境

## 11. 常见问题

### 11.1 开发问题
- **跨域问题**: 配置CORS允许跨域访问
- **文件上传大小限制**: 调整服务器和应用配置
- **数据库连接超时**: 优化连接池配置
- **内存溢出**: 调整JVM参数和优化代码

### 11.2 部署问题
- **端口冲突**: 检查并修改端口配置
- **权限不足**: 设置正确的文件和目录权限
- **服务启动失败**: 查看日志文件排查错误
- **数据库连接失败**: 检查网络和账号权限

### 11.3 性能问题
- **接口响应慢**: 优化SQL查询和业务逻辑
- **页面加载慢**: 优化前端资源和缓存策略
- **并发处理能力不足**: 增加服务器资源和优化架构
- **数据库查询慢**: 添加索引和优化查询语句

## 12. 联系方式

### 12.1 开发团队
- **技术负责人**: [姓名] - [邮箱]
- **后端开发**: [姓名] - [邮箱]
- **前端开发**: [姓名] - [邮箱]
- **UI设计**: [姓名] - [邮箱]
- **测试工程师**: [姓名] - [邮箱]

### 12.2 技术支持
- **项目仓库**: [Git仓库地址]
- **文档中心**: [文档地址]
- **问题反馈**: [问题跟踪地址]
- **技术交流群**: [群号/二维码]

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**更新人员**: 开发团队  
**下次更新**: 根据项目进展更新

