package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量设置过期时间请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareBatchExpireRequest", description = "批量设置过期时间请求")
public class ShareBatchExpireRequest {

    /**
     * 分享记录ID数组
     */
    @Schema(description = "分享记录ID数组", example = "[6001, 6002, 6003]", required = true)
    @NotEmpty(message = "分享ID列表不能为空")
    private List<Long> shareIds;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间", example = "2024-12-31 23:59:59", required = true)
    @NotNull(message = "过期时间不能为空")
    private LocalDateTime expireTime;
} 