<template>
  <div class="resume-container">
    <div class="resume-template">
            <!-- 简历头部 -->
            <div class="resume-header">
                <div class="header-left">
                    <div class="title-section">
                        <h1 class="resume-title">个人简历</h1>
                        <h1 class="resume-title-en">RESUME</h1>
                    </div>
                    <div class="divider-line"></div>
                </div>
                <div class="header-right">
                    <div class="profile-photo"  >照片位置</div>
                </div>
            </div>

            <!-- 基本信息区域 -->
            <section class="basic-info-section" data-module="basic_info">
                <div class="basic-info-grid">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">姓名</span>
                            <span class="info-value"  >姚亮</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">求职意向:</span>
                            <span class="info-value"  >CAD设计师</span>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">电话:</span>
                            <span class="info-value"  >13333333333</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">邮箱:</span>
                            <span class="info-value"  ><EMAIL></span>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">工作地点:</span>
                            <span class="info-value"  >深圳</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">个人网站:</span>
                            <span class="info-value"  >www.example.com</span>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">LinkedIn:</span>
                            <span class="info-value"  >linkedin.com/in/example</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">GitHub:</span>
                            <span class="info-value"  >github.com/example</span>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">个人简介:</span>
                            <span class="info-value"  >5年工作经验，擅长CAD设计和项目管理</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 工作经历模块 -->
            <section class="section-block work-section" data-module="work_experiences">
                <div class="section-header">
                    <div class="section-icon">💼</div>
                    <h2 class="section-title">工作经历</h2>
                </div>

                <div class="section-content">
                    <div class="experience-item" v-for="item in resumeData.work_experiences" data-vue-key="item.id">
                        <div class="experience-header">
                            <h3 class="company-position">
                                <span  >深圳湖田设计院</span> - 
                                <span  >平面设计师</span>
                            </h3>
                            <div class="date-range">
                                <span  >2014.05</span> ~ 
                                <span  >至今</span>
                            </div>
                        </div>

                        <div class="experience-content">
                            <div class="achievements-list">
                                <div class="achievement-item"  >
                                    1. 精准绘制机械零件，建筑平面图等各类CAD图纸，图纸一次通过率超85%，有效减少设计修改时间与成本。
                                    2. 与跨部门团队紧密协作，负责项目设计的沟通与协调，提出的20余条设计优化建议，多数被采用，有效提升产品质量与用户体验。
                                    3. 主导设计了多个项目，完成50余套CAD图纸，其中3个项目获公司年度优秀设计奖。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 教育经历模块 -->
            <section class="section-block education-section" data-module="educations">
                <div class="section-header">
                    <div class="section-icon">🎓</div>
                    <h2 class="section-title">教育经历</h2>
                </div>

                <div class="section-content">
                    <div class="education-item" v-for="item in resumeData.educations" data-vue-key="item.id">
                        <div class="education-header">
                            <h3 class="school-degree">
                                <span  >武汉大学</span> - 
                                <span  >本科</span>
                                （<span  >土木工程</span>）
                            </h3>
                            <div class="date-range">
                                <span  >2019.12</span> ~ 
                                <span  >2023.06</span>
                            </div>
                        </div>

                        <div class="courses">
                            <div class="course-item"  >
                                主修课程：工程设计、施工安全、结构力学、建筑材料学
                            </div>
                            <div class="course-item">
                                GPA: <span  >3.8/4.0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 项目经历模块 -->
            <section class="section-block project-section" data-module="projects">
                <div class="section-header">
                    <div class="section-icon">📊</div>
                    <h2 class="section-title">项目经历</h2>
                </div>

                <div class="section-content">
                    <div class="project-item" v-for="item in resumeData.projects" data-vue-key="item.id">
                        <div class="project-header">
                            <h3 class="project-name">
                                <span  >电商平台优化</span>
                            </h3>
                            <div class="date-range">
                                <span  >2022.12</span> ~ 
                                <span  >2024.06</span>
                            </div>
                        </div>

                        <div class="project-content">
                            <div class="responsibilities">
                                <div class="responsibility-item">
                                    <strong>使用技术：</strong>
                                    <span  >React, Vue.js, Node.js, MySQL</span>
                                </div>
                                <div class="responsibility-item"  >
                                    <strong>项目描述：</strong>
                                    某电商平台为提高用户体验与转化率，需对界面进行优化。分析用户行为数据，找出界面痛点，重新规划导航栏，优化核心功能模块。
                                    优化后平台用户停留时间延长20%，转化率提升15%。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 技能特长模块 -->
            <section class="section-block skills-section" data-module="skills">
                <div class="section-header">
                    <div class="section-icon">🛠️</div>
                    <h2 class="section-title">技能特长</h2>
                </div>

                <div class="section-content">
                    <div class="skills-grid">
                        <div class="skill-item" v-for="skill in resumeData.skills" data-vue-key="skill.id">
                            <div class="skill-name"  >CAD设计</div>
                            <div class="skill-level">
                                <div class="skill-bar">
                                    <div class="skill-progress" style="width: 90%"></div>
                                </div>
                                <div class="skill-percentage"  >90%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
  </div>
</template>

<script setup>
import { useResumeTemplateBase } from '~/composables/template/useResumeTemplateBase'

// 使用基础组合式函数
const {
  resumeData,
  primaryColor,
  secondaryColor,
  fontFamily,
  fontSize,
  themeClass,
  // 其他响应式数据和方法
} = useResumeTemplateBase()
</script>

<style scoped>
/* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 主容器样式 */
        body {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .resume-template {
            width: 100%;
            max-width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 20mm 15mm;
            background: white;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        /* 简历头部 */
        .resume-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .header-left {
            flex: 1;
        }

        .title-section {
            display: flex;
            align-items: baseline;
            gap: 40px;
            margin-bottom: 10px;
        }

        .resume-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .resume-title-en {
            font-size: 36px;
            font-weight: bold;
            color: #666;
            margin: 0;
            letter-spacing: 8px;
        }

        .divider-line {
            height: 3px;
            background: linear-gradient(to right, #333 0%, #333 30%, #666 100%);
            margin-top: 5px;
        }

        .header-right {
            width: 120px;
            height: 160px;
        }

        .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 2px solid #ddd;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        /* 基本信息区域 */
        .basic-info-section {
            margin-bottom: 25px;
        }

        .basic-info-grid {
            display: grid;
            gap: 8px;
        }

        .info-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .info-item {
            display: flex;
            align-items: center;
        }

        .info-label {
            font-weight: bold;
            color: #333;
            min-width: 80px;
        }

        .info-value {
            color: #555;
            margin-left: 8px;
        }

        /* 模块通用样式 */
        .section-block {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #4a5568;
        }

        .section-icon {
            font-size: 18px;
            margin-right: 8px;
            background: #4a5568;
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            min-width: 36px;
            text-align: center;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: #4a5568;
            padding: 6px 15px;
            margin: 0;
            margin-left: -2px;
        }

        .section-content {
            padding-left: 0;
        }

        /* 工作经历样式 */
        .experience-item {
            margin-bottom: 20px;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .company-position {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .date-range {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
        }

        .achievements-list {
            margin-left: 0;
        }

        .achievement-item {
            margin-bottom: 4px;
            color: #555;
            line-height: 1.6;
        }

        /* 教育经历样式 */
        .education-item {
            margin-bottom: 15px;
        }

        .education-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .school-degree {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .courses {
            display: grid;
            gap: 4px;
        }

        .course-item {
            color: #555;
            line-height: 1.6;
        }

        /* 项目经历样式 */
        .project-item {
            margin-bottom: 20px;
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .project-name {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .responsibilities, .achievements {
            margin-bottom: 8px;
        }

        .responsibility-item, .achievement-item {
            margin-bottom: 4px;
            color: #555;
            line-height: 1.6;
        }

        /* 技能样式 */
        .skills-grid {
            display: grid;
            gap: 10px;
        }

        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .skill-name {
            font-weight: 500;
            color: #333;
            min-width: 100px;
        }

        .skill-level {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .skill-bar {
            flex: 1;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(to right, #4a5568, #718096);
            transition: width 0.3s ease;
        }

        .skill-percentage {
            font-size: 11px;
            color: #666;
            min-width: 35px;
            text-align: right;
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .resume-template {
                margin: 0;
                box-shadow: none;
                page-break-inside: avoid;
            }
            
            .section-block {
                page-break-inside: avoid;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .resume-template {
                padding: 15mm 10mm;
                font-size: 11px;
            }
            
            .resume-header {
                flex-direction: column;
                gap: 20px;
            }
            
            .title-section {
                flex-direction: column;
                gap: 10px;
            }
            
            .resume-title-en {
                font-size: 24px;
                letter-spacing: 4px;
            }
            
            .info-row {
                grid-template-columns: 1fr;
                gap: 8px;
            }
            
            .header-right {
                width: 100px;
                height: 130px;
            }
        }
</style>
