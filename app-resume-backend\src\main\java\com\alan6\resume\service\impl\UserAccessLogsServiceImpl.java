package com.alan6.resume.service.impl;

import com.alan6.resume.entity.UserAccessLogs;
import com.alan6.resume.mapper.UserAccessLogsMapper;
import com.alan6.resume.service.IUserAccessLogsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户访问日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class UserAccessLogsServiceImpl extends ServiceImpl<UserAccessLogsMapper, UserAccessLogs> implements IUserAccessLogsService {

}
