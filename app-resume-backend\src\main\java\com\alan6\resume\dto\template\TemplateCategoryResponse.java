package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模板分类响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板分类响应DTO")
public class TemplateCategoryResponse {

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Long id;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String name;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述")
    private String description;

    /**
     * 分类图标URL
     */
    @Schema(description = "分类图标URL")
    private String iconUrl;

    /**
     * 父分类ID（0为顶级分类）
     */
    @Schema(description = "父分类ID（0为顶级分类）")
    private Long parentId;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重")
    private Integer sortOrder;

    /**
     * 模板数量
     */
    @Schema(description = "模板数量")
    private Integer templateCount;
} 