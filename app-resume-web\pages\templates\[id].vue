<template>
  <div class="min-h-screen bg-gray-50 pt-20">
    <!-- 面包屑导航 -->
    <div class="bg-white border-b border-gray-200 shadow-sm min-h-[60px] flex items-center">
      <div class="container-width px-4 py-3 w-full">
        <nav class="flex items-center space-x-2 text-sm" aria-label="面包屑导航">
          <div class="flex items-center space-x-2 text-gray-600">
            <!-- 首页 -->
            <NuxtLink 
              to="/"
              class="hover:text-blue-600 transition-colors duration-200 flex items-center text-blue-500"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
              </svg>
              首页
            </NuxtLink>

            <!-- 分隔符 -->
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>

            <!-- 简历模板库 -->
            <NuxtLink 
              to="/templates"
              class="hover:text-blue-600 transition-colors duration-200 text-blue-500"
            >
              简历模板库
            </NuxtLink>

            <!-- 分隔符 -->
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>

            <!-- 当前模板名称 -->
            <span class="text-gray-900 font-medium">
              {{ templateDetailService.currentTemplate.value ? templateDisplayName : '模板详情' }}
            </span>
          </div>
        </nav>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container-width section-padding py-8">
      <!-- 加载状态 -->
      <div v-if="templateDetailService.isLoading.value" class="flex justify-center items-center py-20">
        <div class="flex flex-col items-center">
          <div class="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p class="text-secondary-600">正在加载模板详情...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="templateDetailService.hasError.value" class="flex justify-center items-center py-20">
        <div class="text-center">
          <div class="w-16 h-16 bg-danger-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-danger-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-secondary-900 mb-2">加载失败</h3>
          <p class="text-secondary-600 mb-4">{{ templateDetailService.error.value }}</p>
          <button 
            @click="retryLoadTemplate"
            class="btn-primary"
          >
            重试
          </button>
        </div>
      </div>

      <!-- 模板详情内容 -->
      <div v-else-if="templateDetailService.currentTemplate.value">
        <!-- 模板详情主体 -->
        <div class="mb-12">
          <TemplateDetail
            :template="templateDetailService.currentTemplate.value"
            @image-click="handleImageClick"
            @use-template="handleUseTemplate"
          />
        </div>

        <!-- 更多相似模板轮播 -->
        <div v-if="templateDetailService.similarTemplates.value.length > 0" class="mb-12">
          <TemplateCarousel
            title="更多相似模板"
            :templates="templateDetailService.similarTemplates.value"
            :title-link="similarTemplatesLink"
            :slides-to-show="6"
            @template-click="handleTemplateClick"
            @use-template="handleUseTemplate"
            @preview="handlePreview"
          />
        </div>

        <!-- 更多热门模板轮播 -->
        <div v-if="templateDetailService.hotTemplates.value.length > 0">
          <TemplateCarousel
            title="更多热门模板"
            :templates="templateDetailService.hotTemplates.value"
            :title-link="hotTemplatesLink"
            :slides-to-show="6"
            @template-click="handleTemplateClick"
            @use-template="handleUseTemplate"
            @preview="handlePreview"
          />
        </div>
      </div>
    </div>

    <!-- 图片查看器 -->
    <ImageViewer
      :visible="imageViewerVisible"
      :image-url="currentImageUrl"
      :image-alt="currentImageAlt"
      @close="closeImageViewer"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTemplateDetailService } from '~/composables/template/useTemplateDetailService'
import BreadcrumbNavigation from '~/components/template/BreadcrumbNavigation.vue'
import TemplateDetail from '~/components/template/TemplateDetail.vue'
import TemplateCarousel from '~/components/template/TemplateCarousel.vue'
import ImageViewer from '~/components/template/ImageViewer.vue'

// ================================
// 页面配置
// ================================

// 路由和服务
const route = useRoute()
const router = useRouter()
const templateDetailService = useTemplateDetailService()

// 图片查看器相关
const imageViewerVisible = ref(false)
const currentImageUrl = ref('')
const currentImageAlt = ref('')

// ================================
// 计算属性
// ================================

// 相似模板跳转链接
const similarTemplatesLink = computed(() => {
  const template = templateDetailService.currentTemplate.value
  if (!template) return '/templates'
  
  // 构建筛选参数，按当前模板的属性进行筛选
  const params = new URLSearchParams()
  if (template.industry) params.set('industry', template.industry)
  if (template.style) params.set('style', template.style)
  if (template.category) params.set('category', template.category)
  
  return `/templates?${params.toString()}`
})

// 热门模板跳转链接
const hotTemplatesLink = computed(() => {
  return '/templates?category=hot'
})

// 模板显示名称：行业-风格
const templateDisplayName = computed(() => {
  const template = templateDetailService.currentTemplate.value
  if (!template) return '模板详情'
  
  const { name, industry, style } = template
  
  // 如果有完整名称，直接使用
  if (name) return name
  
  // 否则组合行业和风格
  const industryText = industry || '通用'
  const styleText = style || '经典'
  return `${industryText}-${styleText}`
})

// ================================
// 生命周期
// ================================

onMounted(async () => {
  await initializePage()
})

onUnmounted(() => {
  templateDetailService.clearData()
})

// 监听路由变化
watch(
  () => route.params.id,
  async (newId) => {
    if (newId) {
      await initializePage()
    }
  }
)

// ================================
// 方法
// ================================

/**
 * 初始化页面
 */
const initializePage = async () => {
  const templateId = route.params.id
  
  if (!templateId) {
    router.push('/templates')
    return
  }

  try {
    await templateDetailService.initializePageData(templateId)
    
    // 设置SEO信息
    updateSeoMeta()
  } catch (error) {
    console.error('初始化页面失败:', error)
  }
}

/**
 * 更新SEO Meta信息
 */
const updateSeoMeta = () => {
  const template = templateDetailService.currentTemplate.value
  if (!template) return

  const title = `${templateDetailService.templateDisplayName.value} - 简历模板详情 - 火花简历`
  const description = template.description || `专业的${template.industry || '通用'}简历模板，${template.style || '经典'}风格设计，已有${template.usageCount || 0}人使用。`
  
  useSeoMeta({
    title,
    description,
    keywords: `简历模板,${template.industry},${template.style},${template.category},简历设计,求职简历,火花简历`,
    ogTitle: title,
    ogDescription: description,
    ogImage: template.thumbnail,
    twitterCard: 'summary_large_image'
  })
}

/**
 * 重试加载模板
 */
const retryLoadTemplate = async () => {
  await initializePage()
}

/**
 * 处理图片点击
 * @param {object} imageData - 图片数据
 */
const handleImageClick = (imageData) => {
  currentImageUrl.value = imageData.imageUrl
  currentImageAlt.value = imageData.imageAlt
  imageViewerVisible.value = true
}

/**
 * 关闭图片查看器
 */
const closeImageViewer = () => {
  imageViewerVisible.value = false
  currentImageUrl.value = ''
  currentImageAlt.value = ''
}

/**
 * 处理使用模板
 * @param {object} template - 模板对象
 */
const handleUseTemplate = async (template) => {
  try {
    // 记录模板使用
    await templateDetailService.recordTemplateUsage(template.id)
    
    // 跳转到简历编辑页
    router.push(`/resume/edit?templateId=${template.id}`)
  } catch (error) {
    console.error('使用模板失败:', error)
    // TODO: 显示错误提示
  }
}

/**
 * 处理模板点击（跳转到详情页）
 * @param {object} template - 模板对象
 */
const handleTemplateClick = (template) => {
  router.push(`/templates/${template.id}`)
}

/**
 * 处理模板预览
 * @param {object} template - 模板对象
 */
const handlePreview = (template) => {
  // TODO: 实现模板预览逻辑
  console.log('预览模板:', template)
  
  // 可以打开模态框或跳转到预览页面
  // router.push(`/templates/${template.id}/preview`)
}

// ================================
// 页面Meta
// ================================
definePageMeta({
  layout: 'default',
  key: route => route.fullPath
})
</script>

<style scoped>
/* 自定义样式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.container-width {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply px-4 sm:px-6 lg:px-8;
}

.btn-primary {
  @apply inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200;
}

.shadow-card {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-soft {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style> 