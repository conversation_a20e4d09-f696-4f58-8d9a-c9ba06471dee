package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 默认内容响应DTO
 * @description 返回给前端的默认简历内容JSON数据
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DefaultContentResponse", description = "默认内容响应")
public class DefaultContentResponse {

    /**
     * 默认简历内容
     * @description 根据模板类别、行业、职位生成的默认简历内容JSON数据
     */
    @Schema(description = "默认简历内容JSON数据")
    private String defaultContent;

    /**
     * 内容来源
     * @description 内容的来源类型：template_specific（模板专用）、industry_position（行业职位）、
     *              industry（行业）、category（类别）、default（系统默认）
     */
    @Schema(description = "内容来源", example = "industry_position")
    private String contentSource;

    /**
     * 模板类别
     * @description 使用的模板类别
     */
    @Schema(description = "模板类别", example = "modern")
    private String templateCategory;

    /**
     * 行业类型
     * @description 使用的行业类型
     */
    @Schema(description = "行业类型", example = "tech")
    private String industry;

    /**
     * 职位类型
     * @description 使用的职位类型
     */
    @Schema(description = "职位类型", example = "frontend")
    private String position;

    /**
     * 语言
     * @description 内容语言
     */
    @Schema(description = "内容语言", example = "zh-CN")
    private String language;

    /**
     * 内容版本
     * @description 默认内容的版本号
     */
    @Schema(description = "内容版本", example = "1.0.0")
    private String contentVersion;

    /**
     * 缓存时间
     * @description 内容的缓存时间
     */
    @Schema(description = "缓存时间")
    private LocalDateTime cacheTime;

    /**
     * 生成时间
     * @description 内容的生成时间
     */
    @Schema(description = "生成时间")
    private LocalDateTime generateTime;
} 