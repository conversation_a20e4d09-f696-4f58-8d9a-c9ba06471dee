package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 当前会员信息响应DTO
 * 
 * 主要功能：
 * 1. 返回用户当前会员状态和信息
 * 2. 包含会员权限和使用情况
 * 3. 提供会员到期计算功能
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "当前会员信息响应", description = "用户当前会员状态和详细信息")
public class MembershipCurrentResponse {

    /**
     * 是否为会员
     */
    @Schema(description = "是否为会员", example = "true")
    private Boolean isMember;

    /**
     * 会员记录ID
     */
    @Schema(description = "会员记录ID", example = "123")
    private Long membershipId;

    /**
     * 套餐信息
     */
    @Schema(description = "套餐信息")
    private PackageInfo packageInfo;

    /**
     * 会员开始时间
     */
    @Schema(description = "会员开始时间", example = "2024-01-01 10:00:00")
    private LocalDateTime startTime;

    /**
     * 会员结束时间
     */
    @Schema(description = "会员结束时间", example = "2024-12-31 23:59:59")
    private LocalDateTime endTime;

    /**
     * 剩余天数
     */
    @Schema(description = "会员剩余天数", example = "180")
    private Long remainingDays;

    /**
     * 状态
     * 0-失效，1-生效
     */
    @Schema(description = "会员状态：0-失效，1-生效", example = "1")
    private Byte status;

    /**
     * 权限信息
     */
    @Schema(description = "会员权限信息")
    private Privileges privileges;

    /**
     * 使用情况
     */
    @Schema(description = "会员使用情况")
    private Usage usage;

    /**
     * 套餐信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "套餐信息", description = "会员套餐基本信息")
    public static class PackageInfo {

        /**
         * 套餐ID
         */
        @Schema(description = "套餐ID", example = "2")
        private Long id;

        /**
         * 套餐名称
         */
        @Schema(description = "套餐名称", example = "年度会员")
        private String packageName;

        /**
         * 套餐代码
         */
        @Schema(description = "套餐代码", example = "yearly")
        private String packageCode;
    }

    /**
     * 权限信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "权限信息", description = "会员权限详情")
    public static class Privileges {

        /**
         * 最大简历数量
         */
        @Schema(description = "最大简历数量，-1表示无限制", example = "-1")
        private Integer maxResumeCount;

        /**
         * 每月导出次数
         */
        @Schema(description = "每月导出次数，-1表示无限制", example = "-1")
        private Integer maxExportCount;

        /**
         * 是否可用付费模板
         */
        @Schema(description = "是否可以使用付费模板", example = "true")
        private Boolean premiumTemplates;

        /**
         * 是否有优先支持
         */
        @Schema(description = "是否有优先客服支持", example = "true")
        private Boolean prioritySupport;
    }

    /**
     * 使用情况内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "使用情况", description = "会员使用统计")
    public static class Usage {

        /**
         * 已使用简历数量
         */
        @Schema(description = "已使用简历数量", example = "5")
        private Integer usedResumeCount;

        /**
         * 已使用导出次数
         */
        @Schema(description = "本月已使用导出次数", example = "12")
        private Integer usedExportCount;

        /**
         * 导出次数最后重置时间
         */
        @Schema(description = "导出次数最后重置时间", example = "2024-06-01 00:00:00")
        private LocalDateTime lastExportResetTime;
    }

    /**
     * 计算剩余天数
     * 
     * @return 剩余天数，如果已过期则返回0
     */
    public Long calculateRemainingDays() {
        if (endTime == null) {
            return 0L;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(endTime)) {
            return 0L;
        }
        
        return ChronoUnit.DAYS.between(now, endTime);
    }

    /**
     * 是否即将过期（7天内）
     * 
     * @return true-即将过期，false-未即将过期
     */
    public Boolean isExpiringSoon() {
        return calculateRemainingDays() <= 7 && calculateRemainingDays() > 0;
    }

    /**
     * 是否已过期
     * 
     * @return true-已过期，false-未过期
     */
    public Boolean isExpired() {
        return calculateRemainingDays() == 0;
    }

    /**
     * 获取会员状态描述
     * 
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (!isMember) {
            return "非会员";
        }
        
        if (isExpired()) {
            return "已过期";
        }
        
        if (isExpiringSoon()) {
            return "即将过期";
        }
        
        return "正常";
    }
} 