/**
 * 简历数据处理组合式函数
 * @description 处理简历数据的合并、格式化、默认值等操作
 * <AUTHOR>
 * @since 1.0.0
 */

import { computed } from 'vue'

/**
 * 简历数据处理组合式函数
 * @param {Object} props - 组件属性
 * @returns {Object} 数据处理相关的方法和计算属性
 */
export function useResumeDataProcessor(props) {
  
  // ================================
  // 默认数据配置
  // ================================
  
  /**
   * 默认简历数据
   * @description 当模块数据为空时使用的默认数据
   */
  const defaultResumeData = {
    basicInfo: {
      name: '张三',
      title: '前端开发工程师',
      phone: '13800138000',
      email: '<EMAIL>',
      address: '北京市朝阳区',
      photo: ''
    },
    skills: [
      { name: 'JavaScript', level: 90 },
      { name: 'Vue.js', level: 85 },
      { name: 'React', level: 80 }
    ],
    workExperiences: [
      {
        company: '示例公司',
        position: '前端开发工程师',
        startDate: '2022-07',
        endDate: '至今',
        description: '负责前端开发工作'
      }
    ],
    educations: [
      {
        school: '示例大学',
        degree: '本科',
        major: '计算机科学',
        startDate: '2018-09',
        endDate: '2022-06'
      }
    ],
    projects: [
      {
        name: '示例项目',
        role: '开发者',
        startDate: '2023-01',
        endDate: '2023-12',
        description: '项目描述'
      }
    ],
    selfEvaluation: '这里是自我评价内容...'
  }
  
  // ================================
  // 数据处理方法
  // ================================
  
  /**
   * 从模块中提取数据
   * @param {string} moduleId - 模块ID
   * @returns {Object} 模块数据
   */
  const getModuleData = (moduleId) => {
    const modules = props.resumeData?.modules || {}
    
    // 如果是数组格式
    if (Array.isArray(modules)) {
      const module = modules.find(m => m.id === moduleId)
      return module?.data || {}
    }
    
    // 如果是对象格式
    return modules[moduleId] || {}
  }
  
  /**
   * 合并模块数据与默认数据
   * @param {string} moduleId - 模块ID
   * @param {string} dataKey - 数据键名
   * @returns {any} 合并后的数据
   */
  const mergeModuleData = (moduleId, dataKey) => {
    const moduleData = getModuleData(moduleId)
    const defaultData = defaultResumeData[dataKey]
    
    // 如果是数组类型，直接使用模块数据或默认数据
    if (Array.isArray(defaultData)) {
      return Array.isArray(moduleData) ? moduleData : defaultData
    }
    
    // 如果是对象类型，进行深度合并
    if (typeof defaultData === 'object' && defaultData !== null) {
      return { ...defaultData, ...moduleData }
    }
    
    // 其他类型，优先使用模块数据，只有在完全没有数据时才使用默认数据
    return moduleData !== undefined ? moduleData : defaultData
  }
  
  /**
   * 处理分页模式下的数据
   * @param {Array} visibleModules - 可见模块列表
   * @returns {Object} 分页模式数据
   */
  const processPaginationData = (visibleModules) => {
    console.log('📄 分页模式：只显示可见模块数据')
    
    const result = {}
    
    // 基本信息处理
    if (visibleModules.includes('basic_info')) {
      result.basicInfo = mergeModuleData('basic_info', 'basicInfo')
      console.log('✅ 包含基本信息')
    } else {
      result.basicInfo = defaultResumeData.basicInfo
    }
    
    // 技能模块处理
    if (visibleModules.includes('skills')) {
      const skillsModule = getModuleData('skills')
      // 只有在完全没有技能数据时才使用默认数据，空数组也是有效的用户数据
      result.skills = skillsModule?.skills !== undefined ? skillsModule.skills : defaultResumeData.skills
      console.log('✅ 包含技能模块，技能数据:', result.skills)
    } else {
      result.skills = []
    }
    
    // 工作经历模块处理
    if (visibleModules.includes('work')) {
      result.workExperiences = mergeModuleData('work', 'workExperiences')
      console.log('✅ 包含工作经历模块')
    } else {
      result.workExperiences = []
    }
    
    // 教育经历模块处理
    if (visibleModules.includes('education')) {
      const educationModule = getModuleData('education')
      // 只有在完全没有教育数据时才使用默认数据，空数组也是有效的用户数据
      result.educations = educationModule?.education !== undefined ? educationModule.education : defaultResumeData.educations
      console.log('✅ 包含教育经历模块')
    } else {
      result.educations = []
    }
    
    // 项目经历模块处理
    if (visibleModules.includes('project')) {
      result.projects = mergeModuleData('project', 'projects')
      console.log('✅ 包含项目经历模块')
    } else {
      result.projects = []
    }
    
    // 自我评价模块处理
    if (visibleModules.includes('self-evaluation')) {
      const selfEvaluationModule = getModuleData('self-evaluation')
      result.selfEvaluation = selfEvaluationModule?.content || defaultResumeData.selfEvaluation
      console.log('✅ 包含自我评价模块')
    } else {
      result.selfEvaluation = ''
    }
    
    console.log('📊 分页模式结果:', result)
    return result
  }
  
  /**
   * 处理普通模式下的数据
   * @returns {Object} 普通模式数据
   */
  const processNormalData = () => {
    console.log('📜 非分页模式：显示所有数据')
    
    return {
      basicInfo: mergeModuleData('basic_info', 'basicInfo'),
      skills: (() => {
        const skillsModule = getModuleData('skills')
        // 只有在完全没有技能数据时才使用默认数据，空数组也是有效的用户数据
        return skillsModule?.skills !== undefined ? skillsModule.skills : defaultResumeData.skills
      })(),
      workExperiences: mergeModuleData('work', 'workExperiences'),
      educations: (() => {
        const educationModule = getModuleData('education')
        // 只有在完全没有教育数据时才使用默认数据，空数组也是有效的用户数据
        return educationModule?.education !== undefined ? educationModule.education : defaultResumeData.educations
      })(),
      projects: (() => {
        const projectModule = getModuleData('project')
        // 只有在完全没有项目数据时才使用默认数据，空数组也是有效的用户数据
        return projectModule?.projects !== undefined ? projectModule.projects : defaultResumeData.projects
      })(),
      selfEvaluation: (() => {
        const selfEvaluationModule = getModuleData('self-evaluation')
        return selfEvaluationModule?.content || defaultResumeData.selfEvaluation
      })()
    }
  }
  
  // ================================
  // 计算属性
  // ================================
  
  /**
   * 合并后的简历数据
   * @description 根据分页模式和可见模块，返回处理后的简历数据
   */
  const mergedResumeData = computed(() => {
    console.log('🔄 计算 mergedResumeData...')
    console.log('📝 分页模式:', props.isPaginationMode)
    console.log('📝 可见模块:', props.visibleModules)
    
    // 分页模式处理
    if (props.isPaginationMode) {
      return processPaginationData(props.visibleModules)
    }
    
    // 普通模式处理
    return processNormalData()
  })
  
  /**
   * 排序后的模块列表
   * @description 根据启用状态、可见性和排序权重返回模块列表
   */
  const sortedModules = computed(() => {
    console.log('🔧 计算排序模块...')
    console.log('📥 props.visibleModules:', props.visibleModules)
    
    // 直接使用 props.visibleModules 的顺序，因为它已经是从 editorStore.enabledModules 排序后的
    // 但是我们需要确保模块按照正确的顺序渲染
    const moduleList = props.visibleModules.map((moduleId, index) => {
      // 从 resumeData 中获取模块数据
      const moduleData = props.resumeData?.modules?.[moduleId]
      return {
        id: moduleId,
        name: moduleData?.name || moduleId,
        enabled: true, // 已经在 visibleModules 中，说明是启用的
        order: index, // 使用在 visibleModules 中的索引作为 order，确保顺序正确
        data: moduleData
      }
    })
    
    console.log('✅ 最终模块列表:', moduleList.map(m => `${m.name || m.id}(${m.order})`))
    return moduleList
  })
  
  /**
   * 左侧栏模块（技能）
   * @description 筛选出应该显示在左侧栏的模块
   */
  const leftColumnModules = computed(() => {
    const leftModules = sortedModules.value.filter(module => module.id === 'skills')
    console.log('👈 左侧栏模块:', leftModules.map(m => m.name || m.id))
    return leftModules
  })
  
  /**
   * 右侧栏模块（其他所有模块）
   * @description 筛选出应该显示在右侧栏的模块
   */
  const rightColumnModules = computed(() => {
    const rightModules = sortedModules.value.filter(module => module.id !== 'skills')
    console.log('👉 右侧栏模块:', rightModules.map(m => m.name || m.id))
    return rightModules
  })
  
  // ================================
  // 工具方法
  // ================================
  
  /**
   * 格式化工作时间段
   * @param {string} startDate - 开始日期
   * @param {string} endDate - 结束日期
   * @returns {string} 格式化后的时间段
   */
  const formatWorkPeriod = (startDate, endDate) => {
    if (!startDate) return ''
    
    const start = startDate.replace('-', '年') + '月'
    const end = endDate ? (endDate.replace('-', '年') + '月') : '至今'
    
    return `${start} - ${end}`
  }
  
  /**
   * 格式化教育时间段
   * @param {string} startDate - 开始日期
   * @param {string} endDate - 结束日期
   * @returns {string} 格式化后的时间段
   */
  const formatEducationPeriod = (startDate, endDate) => {
    return formatWorkPeriod(startDate, endDate)
  }
  
  /**
   * 格式化项目时间段
   * @param {string} startDate - 开始日期
   * @param {string} endDate - 结束日期
   * @returns {string} 格式化后的时间段
   */
  const formatProjectPeriod = (startDate, endDate) => {
    return formatWorkPeriod(startDate, endDate)
  }
  
  // ================================
  // 返回对象
  // ================================
  
  return {
    // 计算属性
    mergedResumeData,
    sortedModules,
    leftColumnModules,
    rightColumnModules,
    
    // 数据处理方法
    getModuleData,
    mergeModuleData,
    
    // 格式化工具
    formatWorkPeriod,
    formatEducationPeriod,
    formatProjectPeriod,
    
    // 默认数据
    defaultResumeData
  }
} 