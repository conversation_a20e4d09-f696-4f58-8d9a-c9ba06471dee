package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 分享记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("share_records")
@Schema(name = "ShareRecords对象", description = "分享记录表")
public class ShareRecords implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分享记录ID")
    private Long id;

    @Schema(description = "分享用户ID")
    private Long userId;

    @Schema(description = "资源类型（resume:简历,template:模板）")
    private String resourceType;

    @Schema(description = "资源ID（简历ID或模板ID）")
    private Long resourceId;

    @Schema(description = "分享类型（1:公开,2:密码保护,3:仅链接访问,4:指定用户）")
    private Byte shareType;

    @Schema(description = "分享范围（1:公开,2:仅链接,3:指定域名）")
    private Byte shareScope;

    @Schema(description = "分享码（用于生成分享链接）")
    private String shareCode;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "查看次数")
    private Integer viewCount;

    @Schema(description = "下载次数")
    private Integer downloadCount;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "访问密码")
    private String password;

    @Schema(description = "是否允许下载（0:不允许,1:允许）")
    private Byte allowDownload;

    @Schema(description = "访问次数限制（-1表示无限制）")
    private Integer viewLimit;

    @Schema(description = "允许访问的域名白名单（JSON数组）")
    private String allowedDomains;

    @Schema(description = "分享描述")
    private String description;

    @Schema(description = "二维码URL")
    private String qrCodeUrl;

    @Schema(description = "是否有效（0:失效,1:有效）")
    private Byte isActive;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
