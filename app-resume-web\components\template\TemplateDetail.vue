<template>
  <div class="bg-white rounded-2xl shadow-card overflow-hidden">
    <div class="flex gap-8 p-6 lg:p-8">
      <!-- 左侧：模板预览图 (60%宽度) -->
      <div class="w-[60%] space-y-4">
        <!-- 主预览图 -->
        <div 
          class="relative group cursor-pointer aspect-[3/4] bg-gradient-to-br from-secondary-50 to-secondary-100 rounded-2xl overflow-hidden border border-secondary-100 transition-all duration-300 hover:shadow-soft hover:-translate-y-1"
          @click="handleImageClick"
        >
          <img
            v-if="template.thumbnail"
            :src="template.thumbnail"
            :alt="template.name"
            class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            loading="lazy"
          />
          <!-- 默认预览内容 -->
          <div v-else class="p-8 h-full flex flex-col justify-center items-center text-center">
            <div class="w-20 h-20 bg-primary-100 rounded-2xl flex items-center justify-center mb-4">
              <svg class="w-10 h-10 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-secondary-900 mb-2">{{ template.name }}</h3>
            <p class="text-sm text-secondary-600">点击查看大图</p>
          </div>

          <!-- 悬浮提示 -->
          <div class="absolute inset-0 bg-primary-600/0 group-hover:bg-primary-600/10 transition-all duration-300 flex items-center justify-center">
            <div class="opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
              <div class="px-4 py-2 bg-white/90 backdrop-blur-sm rounded-lg text-primary-600 font-medium text-sm">
                点击查看大图
              </div>
            </div>
          </div>
        </div>

        <!-- 缩略图列表 -->
        <div v-if="template.images && template.images.length > 1" class="flex space-x-2 overflow-x-auto">
          <div
            v-for="(image, index) in template.images"
            :key="index"
            class="flex-shrink-0 w-16 h-20 bg-secondary-100 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200"
            :class="selectedImageIndex === index ? 'border-primary-500' : 'border-transparent hover:border-secondary-300'"
            @click="selectImage(index)"
          >
            <img
              :src="image"
              :alt="`${template.name} 预览图 ${index + 1}`"
              class="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>

      <!-- 右侧：模板信息 (40%宽度) -->
      <div class="w-[40%] space-y-6">
        <!-- 模板基本信息 -->
        <div>
          <h1 class="text-2xl lg:text-3xl font-bold text-secondary-900 mb-3">{{ template.name }}</h1>
          
          <!-- 标签和统计 -->
          <div class="flex flex-wrap items-center gap-3 mb-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
              {{ template.category || '经典模板' }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary-100 text-secondary-700">
              {{ template.industry || '通用行业' }}
            </span>
            <div class="flex items-center text-sm text-secondary-600">
              <svg class="w-4 h-4 mr-1 text-warning-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              {{ template.rating || 4.8 }}
              <span class="ml-2 text-danger-600">{{ template.usageCount || 4732 }}人使用</span>
            </div>
          </div>

          <!-- 模板描述 -->
          <p class="text-secondary-700 leading-relaxed mb-6">
            {{ template.description || '这是一款专业的简历模板，设计简洁大方，适合各行各业的求职者使用。模板结构清晰，内容布局合理，能够有效突出个人优势和专业技能。' }}
          </p>

          <!-- 特色功能 -->
          <div class="space-y-3 mb-6">
            <h3 class="text-lg font-semibold text-secondary-900">模板特色</h3>
            <div class="grid grid-cols-4 gap-3">
              <div 
                v-for="feature in templateFeatures.slice(0, 8)"
                :key="feature.id"
                class="flex items-center space-x-2 p-2 bg-secondary-50 rounded-lg"
              >
                <div class="w-6 h-6 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <span class="text-xs text-secondary-700">{{ feature.name }}</span>
              </div>
            </div>
          </div>

          <!-- 使用模板按钮 -->
          <div class="flex justify-center">
            <button
              @click="handleUseTemplate"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95"
            >
              立即使用模板
            </button>
          </div>
        </div>

        <!-- 广告位 -->
        <div class="mt-8">
          <div class="relative bg-gradient-to-br from-orange-400 to-red-500 rounded-2xl overflow-hidden">
            <div class="absolute inset-0 bg-black/20"></div>
            <div class="relative p-6 text-white text-center">
              <h3 class="text-xl font-bold mb-2">2025大学生秋招信息共享群联</h3>
              <p class="text-sm opacity-90 mb-4">2025企业最新秋招信息更新共享</p>
              <div class="flex items-center justify-between">
                <div class="text-left">
                  <p class="text-xs opacity-75">免费领取闪光简历会员 立省49.9</p>
                  <p class="text-sm font-medium">大学生求职攻略一本通</p>
                  <p class="text-xs opacity-75">免费简历诊断、求职咨询</p>
                </div>
                <div class="w-20 h-20 bg-white rounded-lg flex items-center justify-center">
                  <div class="w-16 h-16 bg-black rounded-lg flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <p class="text-xs opacity-75 mt-2">添加好友后发送"进群"</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed } from 'vue'

// Props
const props = defineProps({
  template: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value === 'object' && value.id
    }
  }
})

// Emits
const emit = defineEmits(['image-click', 'use-template'])

// 响应式数据
const selectedImageIndex = ref(0)

// 模板特色功能
const templateFeatures = computed(() => [
  { id: 1, name: '产品设计' },
  { id: 2, name: '产品' },
  { id: 3, name: '产品策划' },
  { id: 4, name: '经典' },
  { id: 5, name: '左右结构' },
  { id: 6, name: '自定义数据' },
  { id: 7, name: '自定义颜色' },
  { id: 8, name: '技能条' },
  { id: 9, name: '荣誉栏' },
  { id: 10, name: '一键模板' }
])

// 当前显示的图片URL
const currentImageUrl = computed(() => {
  if (props.template.images && props.template.images.length > 0) {
    return props.template.images[selectedImageIndex.value]
  }
  return props.template.thumbnail || ''
})

// 方法
const handleImageClick = () => {
  emit('image-click', {
    imageUrl: currentImageUrl.value,
    imageAlt: props.template.name
  })
}

const selectImage = (index) => {
  selectedImageIndex.value = index
}

const handleUseTemplate = () => {
  emit('use-template', props.template)
}
</script> 