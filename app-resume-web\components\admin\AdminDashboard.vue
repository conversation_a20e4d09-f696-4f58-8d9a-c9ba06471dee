<template>
  <div class="admin-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          欢迎回来，{{ adminInfo?.nickname || adminInfo?.username || '管理员' }}！
        </h1>
        <p class="welcome-subtitle">
          今天是 {{ formatDate(new Date()) }}，祝您工作愉快！
        </p>
      </div>
      <div class="welcome-actions">
        <button class="action-button primary">
          <Icon name="plus" class="w-4 h-4" />
          <span>新建模板</span>
        </button>
        <button class="action-button secondary">
          <Icon name="chart-bar" class="w-4 h-4" />
          <span>查看报告</span>
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="stat-icon" :class="stat.iconClass">
          <Icon :name="stat.icon" class="w-6 h-6" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stat.value) }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-change" :class="stat.changeClass">
            <Icon :name="stat.changeIcon" class="w-3 h-3" />
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和快捷操作 -->
    <div class="dashboard-grid">
      <!-- 用户增长趋势 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3 class="card-title">用户增长趋势</h3>
          <div class="card-actions">
            <button class="card-action-btn">
              <Icon name="undo" class="w-4 h-4" />
            </button>
          </div>
        </div>
        <div class="card-content">
          <div class="chart-placeholder">
            <div class="chart-mock">
              <div class="chart-bars">
                <div class="bar" style="height: 60%"></div>
                <div class="bar" style="height: 80%"></div>
                <div class="bar" style="height: 45%"></div>
                <div class="bar" style="height: 90%"></div>
                <div class="bar" style="height: 70%"></div>
                <div class="bar" style="height: 95%"></div>
                <div class="bar" style="height: 85%"></div>
              </div>
              <div class="chart-labels">
                <span>周一</span>
                <span>周二</span>
                <span>周三</span>
                <span>周四</span>
                <span>周五</span>
                <span>周六</span>
                <span>周日</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 热门模板 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3 class="card-title">热门模板</h3>
          <NuxtLink to="/admin/template/list" class="card-link">
            查看全部
            <Icon name="arrow-right" class="w-4 h-4" />
          </NuxtLink>
        </div>
        <div class="card-content">
          <div class="template-list">
            <div class="template-item" v-for="template in popularTemplates" :key="template.id">
              <div class="template-preview">
                <img :src="template.preview" :alt="template.name" class="template-image" />
              </div>
              <div class="template-info">
                <h4 class="template-name">{{ template.name }}</h4>
                <p class="template-stats">{{ template.uses }} 次使用</p>
              </div>
              <div class="template-trend" :class="template.trendClass">
                <Icon :name="template.trendIcon" class="w-3 h-3" />
                <span>{{ template.trend }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3 class="card-title">最近活动</h3>
          <button class="card-action-btn">
            <Icon name="menu" class="w-4 h-4" />
          </button>
        </div>
        <div class="card-content">
          <div class="activity-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-icon" :class="activity.iconClass">
                <Icon :name="activity.icon" class="w-4 h-4" />
              </div>
              <div class="activity-content">
                <p class="activity-text">{{ activity.text }}</p>
                <p class="activity-time">{{ activity.time }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3 class="card-title">快捷操作</h3>
        </div>
        <div class="card-content">
          <div class="quick-actions">
            <NuxtLink to="/admin/users" class="quick-action">
              <div class="quick-action-icon users">
                <Icon name="users" class="w-5 h-5" />
              </div>
              <span class="quick-action-text">用户管理</span>
            </NuxtLink>
            
            <NuxtLink to="/admin/template/list" class="quick-action">
              <div class="quick-action-icon templates">
                <Icon name="document" class="w-5 h-5" />
              </div>
              <span class="quick-action-text">模板管理</span>
            </NuxtLink>
            
            <NuxtLink to="/admin/orders" class="quick-action">
              <div class="quick-action-icon orders">
                <Icon name="shopping-cart" class="w-5 h-5" />
              </div>
              <span class="quick-action-text">订单管理</span>
            </NuxtLink>
            
            <NuxtLink to="/admin/settings" class="quick-action" v-if="isSuperAdmin">
              <div class="quick-action-icon settings">
                <Icon name="settings" class="w-5 h-5" />
              </div>
              <span class="quick-action-text">系统设置</span>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAdminStore } from '~/composables/admin/useAdminStore'
import Icon from '~/components/common/Icon.vue'

const { adminInfo, isSuperAdmin } = useAdminStore()

// 模拟统计数据
const stats = ref([
  {
    key: 'users',
    label: '总用户数',
    value: 12580,
    change: '+12.5%',
    changeClass: 'positive',
    changeIcon: 'arrow-up',
    icon: 'users',
    iconClass: 'users'
  },
  {
    key: 'resumes',
    label: '简历总数',
    value: 8460,
    change: '+8.2%',
    changeClass: 'positive',
    changeIcon: 'arrow-up',
    icon: 'document',
    iconClass: 'resumes'
  },
  {
    key: 'templates',
    label: '模板数量',
    value: 156,
    change: '+3.1%',
    changeClass: 'positive',
    changeIcon: 'arrow-up',
    icon: 'document',
    iconClass: 'templates'
  },
  {
    key: 'revenue',
    label: '本月收入',
    value: 45680,
    change: '-2.4%',
    changeClass: 'negative',
    changeIcon: 'arrow-down',
    icon: 'dollar-sign',
    iconClass: 'revenue'
  }
])

// 模拟热门模板数据
const popularTemplates = ref([
  {
    id: 1,
    name: '现代简约',
    preview: '/api/admin/placeholder/120/80',
    uses: 1240,
    trend: '+15%',
    trendClass: 'positive',
    trendIcon: 'arrow-up'
  },
  {
    id: 2,
    name: '商务专业',
    preview: '/api/admin/placeholder/120/80',
    uses: 980,
    trend: '+8%',
    trendClass: 'positive',
    trendIcon: 'arrow-up'
  },
  {
    id: 3,
    name: '创意设计',
    preview: '/api/admin/placeholder/120/80',
    uses: 756,
    trend: '-3%',
    trendClass: 'negative',
    trendIcon: 'arrow-down'
  }
])

// 模拟最近活动数据
const recentActivities = ref([
  {
    id: 1,
    text: '用户 张三 注册了新账号',
    time: '2分钟前',
    icon: 'user-plus',
    iconClass: 'user'
  },
  {
    id: 2,
    text: '新模板 "科技风格" 已上架',
    time: '15分钟前',
    icon: 'plus',
    iconClass: 'template'
  },
  {
    id: 3,
    text: '订单 #12345 支付成功',
    time: '1小时前',
    icon: 'check-circle',
    iconClass: 'order'
  },
  {
    id: 4,
    text: '系统进行了定期备份',
    time: '2小时前',
    icon: 'download',
    iconClass: 'system'
  }
])

/**
 * 格式化日期
 */
const formatDate = (date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }).format(date)
}

/**
 * 格式化数字
 */
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toLocaleString()
}
</script>

<style scoped>
.admin-dashboard {
  padding: 0;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.welcome-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button.primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.action-button.primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.action-button.secondary {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.users { background: linear-gradient(135deg, #667eea, #764ba2); }
.stat-icon.resumes { background: linear-gradient(135deg, #f093fb, #f5576c); }
.stat-icon.templates { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.stat-icon.revenue { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #10b981;
}

.stat-change.negative {
  color: #ef4444;
}

/* 仪表盘网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.card-link {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #3b82f6;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.card-link:hover {
  color: #2563eb;
}

.card-action-btn {
  padding: 6px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.card-action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.card-content {
  padding: 24px;
}

/* 图表占位符 */
.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-mock {
  width: 100%;
  max-width: 300px;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 120px;
  margin-bottom: 12px;
  gap: 8px;
}

.bar {
  flex: 1;
  background: linear-gradient(180deg, #3b82f6, #1d4ed8);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
}

/* 模板列表 */
.template-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.template-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.template-preview {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.template-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.template-stats {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.template-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.template-trend.positive {
  color: #10b981;
}

.template-trend.negative {
  color: #ef4444;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  gap: 12px;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-icon.user { background: #3b82f6; }
.activity-icon.template { background: #10b981; }
.activity-icon.order { background: #f59e0b; }
.activity-icon.system { background: #6b7280; }

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.activity-time {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

/* 快捷操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
}

.quick-action:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.quick-action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.quick-action-icon.users { background: linear-gradient(135deg, #667eea, #764ba2); }
.quick-action-icon.templates { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.quick-action-icon.orders { background: linear-gradient(135deg, #f093fb, #f5576c); }
.quick-action-icon.settings { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.quick-action-text {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
}
</style> 