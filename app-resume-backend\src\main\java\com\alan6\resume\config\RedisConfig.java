package com.alan6.resume.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类
 * 
 * 主要功能：
 * 1. 配置RedisTemplate的序列化方式
 * 2. 设置key为String序列化，value为JSON序列化
 * 3. 确保Redis存储和读取的数据格式一致性
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Configuration
public class RedisConfig {

    /**
     * 配置RedisTemplate
     * 
     * 使用Jackson2JsonRedisSerializer序列化，提高性能并保证数据可读性：
     * - key采用String序列化方式
     * - value采用Jackson JSON序列化方式
     * - hash key采用String序列化方式
     * - hash value采用Jackson JSON序列化方式
     * 
     * @param connectionFactory Redis连接工厂
     * @return 配置好的RedisTemplate实例
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        // 创建RedisTemplate实例
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        
        // 设置连接工厂
        template.setConnectionFactory(connectionFactory);

        // 配置ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE); // 驼峰命名
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // 使用GenericJackson2JsonRedisSerializer，通过构造函数传递ObjectMapper
        GenericJackson2JsonRedisSerializer jackson2JsonRedisSerializer =
                new GenericJackson2JsonRedisSerializer(objectMapper);

        // 创建String序列化器
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        
        // 设置key的序列化方式为String
        template.setKeySerializer(stringRedisSerializer);
        
        // 设置hash key的序列化方式为String
        template.setHashKeySerializer(stringRedisSerializer);
        
        // 设置value的序列化方式为JSON
        template.setValueSerializer(jackson2JsonRedisSerializer);
        
        // 设置hash value的序列化方式为JSON
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        
        // 初始化RedisTemplate
        template.afterPropertiesSet();
        
        log.info("Redis配置初始化完成，采用String+JSON序列化方式");
        
        return template;
    }
} 