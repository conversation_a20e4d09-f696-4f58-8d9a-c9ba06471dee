package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 退款申请请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "RefundRequest", description = "退款申请请求")
public class RefundRequest {

    /**
     * 退款金额
     */
    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0.01", message = "退款金额必须大于0")
    @Schema(description = "退款金额", required = true, example = "199.90")
    private BigDecimal refundAmount;

    /**
     * 退款原因描述
     */
    @NotBlank(message = "退款原因不能为空")
    @Schema(description = "退款原因描述", required = true, example = "用户主动退款")
    private String refundReason;

    /**
     * 退款原因代码
     */
    @NotBlank(message = "退款原因代码不能为空")
    @Schema(description = "退款原因代码", required = true, example = "USER_CANCEL", 
            allowableValues = {"USER_CANCEL", "QUALITY_ISSUE", "SYSTEM_ERROR", "DUPLICATE_PAYMENT", "OTHER"})
    private String refundReasonCode;

    /**
     * 操作员ID（管理员退款时必填）
     */
    @Schema(description = "操作员ID", example = "admin001")
    private String operatorId;

    /**
     * 退款结果通知地址
     */
    @Schema(description = "退款结果通知地址", example = "https://api.resume.com/payment/refund/callback")
    private String notifyUrl;
} 