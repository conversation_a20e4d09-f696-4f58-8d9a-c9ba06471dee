package com.alan6.resume.service;

import com.alan6.resume.dto.category.CategoryRequest;
import com.alan6.resume.dto.category.CategoryResponse;
import com.alan6.resume.entity.ResumeCategories;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 简历分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface IResumeCategoriesService extends IService<ResumeCategories> {

    /**
     * 获取分类树结构
     * @return 分类树列表
     */
    List<CategoryResponse> getCategoryTree();

    /**
     * 获取指定类型的分类列表
     * @param categoryType 分类类型
     * @return 分类列表
     */
    List<CategoryResponse> getCategoriesByType(String categoryType);

    /**
     * 创建分类
     * @param request 分类请求
     * @return 是否成功
     */
    boolean createCategory(CategoryRequest request);

    /**
     * 更新分类
     * @param request 分类请求
     * @return 是否成功
     */
    boolean updateCategory(CategoryRequest request);

    /**
     * 删除分类
     * @param id 分类ID
     * @return 是否成功
     */
    boolean deleteCategory(Long id);

    /**
     * 获取子分类列表
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<CategoryResponse> getChildCategories(Long parentId);

    /**
     * 检查分类代码是否存在
     * @param code 分类代码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByCode(String code, Long excludeId);
} 