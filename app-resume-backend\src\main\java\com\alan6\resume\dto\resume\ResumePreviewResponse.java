package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 简历预览响应DTO
 * 
 * @description 返回简历预览的数据
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "简历预览响应")
public class ResumePreviewResponse {

    /**
     * 状态ID
     */
    @Schema(description = "状态ID")
    private String stateId;

    /**
     * 简历导出数据
     */
    @Schema(description = "简历导出数据")
    private ResumePreviewRequest.ResumeExportData exportData;

    /**
     * 预览URL
     */
    @Schema(description = "预览URL")
    private String previewUrl;

    /**
     * 数据过期时间
     */
    @Schema(description = "数据过期时间（秒）")
    private Long expiresIn;
} 