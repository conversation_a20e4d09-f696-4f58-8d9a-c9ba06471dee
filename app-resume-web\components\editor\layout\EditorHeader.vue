<template>
  <header class="editor-header">
    <div class="header-left">
      <!-- Logo -->
      <div class="header-logo">
        <NuxtLink to="/" class="logo-link">
          <div class="logo-icon">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2c-1.1 0-2 .9-2 2 0 .74.4 1.39 1 1.73v1.77L9 9.5c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2s2 .9 2 2c0 .74-.4 1.39-1 1.73V7.5h1c.55 0 1 .45 1 1s-.45 1-1 1l-2 2v1.77c.6.34 1 .99 1 1.73 0 1.1-.9 2-2 2s-2-.9-2-2c0-.74.4-1.39 1-1.73V11.5l-2-2c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2z"/>
            </svg>
            <div class="logo-spark"></div>
          </div>
          <span class="logo-text">火花简历</span>
        </NuxtLink>
      </div>

      <!-- 可编辑文件名 -->
      <div class="file-name-editor">
        <input
          v-if="isEditingTitle"
          ref="titleInput"
          v-model="editableTitle"
          class="title-input"
          type="text"
          @blur="handleTitleBlur"
          @keydown.enter="handleTitleSave"
          @keydown.esc="handleTitleCancel"
        />
        <div
          v-else
          class="title-display"
          @click="handleTitleEdit"
        >
          <span class="title-text">{{ resumeData.title || '未命名简历' }}</span>
          <Icon name="edit" class="edit-icon" />
        </div>
      </div>
    </div>

    <div class="header-right">
      <!-- 保存状态 -->
      <SaveStatus
        :is-dirty="isDirty"
        :last-saved="resumeData.lastSaved"
        :is-saving="isSaving"
      />

      <!-- 操作按钮组 -->
      <div class="action-buttons">
        <!-- 保存 -->
        <button
          class="action-btn save-btn"
          :disabled="!isDirty || isSaving"
          @click="$emit('save')"
          title="保存 (Ctrl+S)"
        >
          <Icon name="save" />
          <span class="btn-text">保存</span>
        </button>

        <!-- 下载 -->
        <DownloadDropdown
          :is-exporting="isExporting"
          :export-status="exportStatus"
          @download="handleDownloadFormat"
          @send-email="$emit('send-email')"
        />

        <!-- 分割线 -->
        <div class="divider"></div>

        <!-- 用户状态 -->
        <UserStatus @request-login="$emit('request-login')" />
      </div>
    </div>
  </header>
</template>

<script setup>
/**
 * 编辑器顶部导航栏组件
 * @description 包含Logo、文件名编辑、保存状态、操作按钮等功能
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, nextTick } from 'vue'
import Icon from '~/components/common/Icon.vue'
import SaveStatus from '../common/SaveStatus.vue'
import UserStatus from '../common/UserStatus.vue'
import DownloadDropdown from './DownloadDropdown.vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 简历数据
   */
  resumeData: {
    type: Object,
    required: true
  },
  
  /**
   * 是否有未保存的更改
   */
  isDirty: {
    type: Boolean,
    default: false
  },
  
  /**
   * 是否正在保存
   */
  isSaving: {
    type: Boolean,
    default: false
  },
  

})

// ================================
// 组件事件
// ================================
const emit = defineEmits([
  'save',
  'download',
  'send-email',
  'title-change',
  'request-login'
])

// ================================
// 响应式数据
// ================================

// 标题编辑状态
const isEditingTitle = ref(false)
const editableTitle = ref('')
const titleInput = ref(null)

// 导出状态
const isExporting = ref(false)
const exportStatus = ref('正在导出...')

// ================================
// 方法定义
// ================================

/**
 * 开始编辑标题
 */
const handleTitleEdit = () => {
  isEditingTitle.value = true
  editableTitle.value = props.resumeData.title || '未命名简历'
  
  nextTick(() => {
    if (titleInput.value) {
      titleInput.value.focus()
      titleInput.value.select()
    }
  })
}

/**
 * 保存标题
 */
const handleTitleSave = () => {
  const newTitle = editableTitle.value.trim()
  if (newTitle && newTitle !== props.resumeData.title) {
    emit('title-change', newTitle)
  }
  isEditingTitle.value = false
}

/**
 * 取消编辑标题
 */
const handleTitleCancel = () => {
  editableTitle.value = props.resumeData.title || '未命名简历'
  isEditingTitle.value = false
}

/**
 * 失焦时保存标题
 */
const handleTitleBlur = () => {
  handleTitleSave()
}

/**
 * 处理下载格式选择
 * @param {string} format - 下载格式 (pdf/word/image)
 */
const handleDownloadFormat = (format) => {
  // 设置导出状态
  isExporting.value = true
  
  // 根据格式设置不同的状态文本
  const statusTexts = {
    pdf: '正在生成PDF文档...',
    word: '正在生成Word文档...',
    image: '正在生成图片...'
  }
  
  exportStatus.value = statusTexts[format] || '正在导出...'
  
  // 触发下载事件，传递格式参数
  emit('download', { format })
}
</script>

<style scoped>
.editor-header {
  @apply h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.header-left {
  @apply flex items-center space-x-6;
}

.header-right {
  @apply flex items-center space-x-4;
}

/* Logo 样式 */
.header-logo {
  @apply flex-shrink-0;
}

.logo-link {
  @apply flex items-center space-x-3 text-gray-900 hover:text-primary-600 transition-colors duration-200;
}

.logo-icon {
  @apply w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center relative;
}

.logo-spark {
  @apply absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping;
}

.logo-text {
  @apply font-display font-bold text-xl;
}

/* 文件名编辑器样式 */
.file-name-editor {
  @apply relative;
}

.title-input {
  @apply px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-gray-900 font-medium;
  min-width: 200px;
}

.title-display {
  @apply flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors duration-200;
}

.title-text {
  @apply text-gray-900 font-medium;
}

.edit-icon {
  @apply w-4 h-4 text-gray-400;
}

/* 操作按钮样式 */
.action-buttons {
  @apply flex items-center space-x-2;
}

.action-btn {
  @apply inline-flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 font-medium text-sm;
  @apply hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.action-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.save-btn {
  @apply text-primary-600 hover:bg-primary-50 hover:text-primary-700;
}

.save-btn:disabled {
  @apply text-gray-400 hover:bg-gray-100;
}

.download-btn {
  @apply text-success-600 hover:bg-success-50 hover:text-success-700;
}

.btn-text {
  @apply hidden sm:inline;
}

.divider {
  @apply w-px h-6 bg-gray-300;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    @apply px-4;
  }
  
  .header-left {
    @apply space-x-4;
  }
  
  .logo-text {
    @apply hidden;
  }
  
  .action-buttons {
    @apply space-x-1;
  }
  
  .action-btn {
    @apply px-2;
  }
}
</style> 