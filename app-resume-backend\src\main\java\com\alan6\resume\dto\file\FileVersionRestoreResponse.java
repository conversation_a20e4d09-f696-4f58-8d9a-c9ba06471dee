package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文件版本恢复响应DTO
 * 
 * 主要功能：
 * 1. 返回版本恢复操作的结果
 * 2. 包含恢复前后的版本信息
 * 3. 提供恢复操作的详细记录
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "文件版本恢复响应", description = "文件版本恢复操作结果")
public class FileVersionRestoreResponse {

    /**
     * 文件ID
     */
    @Schema(description = "文件ID", example = "F20241222123456789")
    private String fileId;

    /**
     * 恢复的版本ID
     */
    @Schema(description = "恢复的版本ID", example = "v1.0.3")
    private String restoredVersionId;

    /**
     * 新版本ID
     */
    @Schema(description = "新版本ID", example = "v1.0.5")
    private String newVersionId;

    /**
     * 恢复时间
     */
    @Schema(description = "恢复时间", example = "2024-12-22 16:45:00")
    private LocalDateTime restoreTime;

    /**
     * 恢复前的版本ID
     */
    @Schema(description = "恢复前的版本ID", example = "v1.0.4")
    private String previousVersionId;

    /**
     * 恢复操作类型
     */
    @Schema(description = "恢复操作类型", example = "VERSION_RESTORE")
    private String operationType;

    /**
     * 恢复原因
     */
    @Schema(description = "恢复原因", example = "回滚到稳定版本")
    private String restoreReason;

    /**
     * 操作员ID
     */
    @Schema(description = "操作员ID", example = "123")
    private Long operatorId;

    /**
     * 操作员名称
     */
    @Schema(description = "操作员名称", example = "张三")
    private String operatorName;

    /**
     * 恢复状态
     */
    @Schema(description = "恢复状态", example = "SUCCESS")
    private String restoreStatus;

    /**
     * 恢复消息
     */
    @Schema(description = "恢复消息", example = "版本恢复成功")
    private String restoreMessage;

    /**
     * 文件信息
     */
    @Schema(description = "文件信息")
    private FileInfo fileInfo;

    /**
     * 文件信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "文件信息", description = "恢复后的文件信息")
    public static class FileInfo {

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "avatar_20241222123456.jpg")
        private String fileName;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小", example = "102400")
        private Long fileSize;

        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "image/jpeg")
        private String fileType;

        /**
         * 文件URL
         */
        @Schema(description = "文件URL", example = "https://minio.example.com/avatar/2024/12/22/avatar_20241222123456.jpg")
        private String fileUrl;

        /**
         * ETag
         */
        @Schema(description = "ETag", example = "d41d8cd98f00b204e9800998ecf8427e")
        private String etag;

        /**
         * 存储类别
         */
        @Schema(description = "存储类别", example = "STANDARD")
        private String storageClass;

        /**
         * 最后修改时间
         */
        @Schema(description = "最后修改时间", example = "2024-12-22 16:45:00")
        private LocalDateTime lastModified;

        /**
         * 格式化文件大小为可读格式
         * 
         * @return 格式化后的文件大小字符串
         */
        public String getFormattedFileSize() {
            if (fileSize == null) {
                return "未知";
            }
            
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else if (fileSize < 1024 * 1024 * 1024) {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            } else {
                return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
            }
        }

        /**
         * 检查文件是否为图片类型
         * 
         * @return true-图片文件，false-非图片文件
         */
        public boolean isImageFile() {
            return fileType != null && fileType.startsWith("image/");
        }

        /**
         * 检查文件是否为文档类型
         * 
         * @return true-文档文件，false-非文档文件
         */
        public boolean isDocumentFile() {
            return fileType != null && 
                   (fileType.equals("application/pdf") || 
                    fileType.equals("application/msword") || 
                    fileType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                    fileType.equals("text/plain"));
        }
    }

    /**
     * 检查恢复操作是否成功
     * 
     * @return true-成功，false-失败
     */
    public boolean isRestoreSuccessful() {
        return "SUCCESS".equalsIgnoreCase(restoreStatus);
    }

    /**
     * 检查是否为回滚操作
     * 
     * @return true-回滚操作，false-其他操作
     */
    public boolean isRollbackOperation() {
        return "VERSION_ROLLBACK".equalsIgnoreCase(operationType);
    }

    /**
     * 检查是否为恢复操作
     * 
     * @return true-恢复操作，false-其他操作
     */
    public boolean isRestoreOperation() {
        return "VERSION_RESTORE".equalsIgnoreCase(operationType);
    }

    /**
     * 获取操作类型描述
     * 
     * @return 操作类型描述
     */
    public String getOperationTypeDescription() {
        if (operationType == null) {
            return "未知操作";
        }
        
        switch (operationType.toUpperCase()) {
            case "VERSION_RESTORE":
                return "版本恢复";
            case "VERSION_ROLLBACK":
                return "版本回滚";
            case "VERSION_REVERT":
                return "版本还原";
            default:
                return "其他操作";
        }
    }

    /**
     * 获取恢复状态描述
     * 
     * @return 恢复状态描述
     */
    public String getRestoreStatusDescription() {
        if (restoreStatus == null) {
            return "未知状态";
        }
        
        switch (restoreStatus.toUpperCase()) {
            case "SUCCESS":
                return "成功";
            case "FAILED":
                return "失败";
            case "PENDING":
                return "处理中";
            case "CANCELLED":
                return "已取消";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取恢复操作摘要
     * 
     * @return 恢复操作摘要
     */
    public String getRestoreSummary() {
        return String.format("%s：将文件 %s 从版本 %s 恢复到版本 %s，操作时间：%s", 
                getOperationTypeDescription(),
                fileId,
                previousVersionId,
                restoredVersionId,
                restoreTime != null ? restoreTime.toString() : "未知");
    }

    /**
     * 创建成功的恢复响应
     * 
     * @param fileId 文件ID
     * @param restoredVersionId 恢复的版本ID
     * @param newVersionId 新版本ID
     * @param previousVersionId 恢复前的版本ID
     * @param operatorId 操作员ID
     * @param operatorName 操作员名称
     * @return 成功的恢复响应
     */
    public static FileVersionRestoreResponse success(String fileId, String restoredVersionId, 
                                                   String newVersionId, String previousVersionId,
                                                   Long operatorId, String operatorName) {
        return FileVersionRestoreResponse.builder()
                .fileId(fileId)
                .restoredVersionId(restoredVersionId)
                .newVersionId(newVersionId)
                .previousVersionId(previousVersionId)
                .restoreTime(LocalDateTime.now())
                .operationType("VERSION_RESTORE")
                .operatorId(operatorId)
                .operatorName(operatorName)
                .restoreStatus("SUCCESS")
                .restoreMessage("版本恢复成功")
                .build();
    }

    /**
     * 创建失败的恢复响应
     * 
     * @param fileId 文件ID
     * @param restoredVersionId 恢复的版本ID
     * @param errorMessage 错误消息
     * @param operatorId 操作员ID
     * @return 失败的恢复响应
     */
    public static FileVersionRestoreResponse failure(String fileId, String restoredVersionId, 
                                                   String errorMessage, Long operatorId) {
        return FileVersionRestoreResponse.builder()
                .fileId(fileId)
                .restoredVersionId(restoredVersionId)
                .restoreTime(LocalDateTime.now())
                .operationType("VERSION_RESTORE")
                .operatorId(operatorId)
                .restoreStatus("FAILED")
                .restoreMessage(errorMessage)
                .build();
    }
} 