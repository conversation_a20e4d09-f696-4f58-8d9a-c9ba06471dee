<template>
  <div class="users-page">
    <div class="page-header">
      <h1 class="page-title">用户管理</h1>
      <p class="page-description">管理系统中的所有用户</p>
    </div>
    
    <div class="page-content">
      <div class="empty-state">
        <div class="empty-icon">👥</div>
        <h3>用户管理功能</h3>
        <p>此页面将显示和管理系统中的所有用户</p>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  title: '用户管理 - 管理后台',
  layout: 'admin'
})
</script>

<style scoped>
.users-page {
  /* 使用布局提供的padding */
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.page-content {
  background: white;
  border-radius: 8px;
  padding: 48px;
  text-align: center;
}

.empty-state {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: #909399;
  margin: 0 0 24px 0;
}
</style> 