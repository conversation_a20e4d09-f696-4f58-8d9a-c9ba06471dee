import{P as r,r as o}from"./CURHyiUL.js";const a=o({visible:!1,type:"info",message:"",duration:4e3});class s{static show(e,i="info",n=4e3){a.value={visible:!0,type:i,message:e,duration:n}}static success(e,i=4e3){this.show(e,"success",i)}static error(e,i=5e3){this.show(e,"error",i)}static warning(e,i=4e3){this.show(e,"warning",i)}static info(e,i=4e3){this.show(e,"info",i)}static hide(){a.value={...a.value,visible:!1}}static clear(){a.value={visible:!1,type:"info",message:"",duration:4e3}}static getState(){return a.value}}const l=()=>({messageState:r(a),showMessage:s.show.bind(s),showSuccess:s.success.bind(s),showError:s.error.bind(s),showWarning:s.warning.bind(s),showInfo:s.info.bind(s),hideMessage:s.hide.bind(s),clearMessage:s.clear.bind(s)}),d={success:s.success.bind(s),error:s.error.bind(s),warning:s.warning.bind(s),info:s.info.bind(s),show:s.show.bind(s),hide:s.hide.bind(s),clear:s.clear.bind(s)};export{d as $,l as u};
