package com.alan6.resume.mapper;

import com.alan6.resume.entity.Permissions;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Mapper
public interface PermissionsMapper extends BaseMapper<Permissions> {

    /**
     * 根据用户ID获取权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<Permissions> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID获取权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permissions> selectPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据权限代码获取权限
     *
     * @param permissionCode 权限代码
     * @return 权限信息
     */
    Permissions selectByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 获取菜单权限树
     *
     * @return 权限树列表
     */
    List<Permissions> selectMenuPermissions();

    /**
     * 根据用户ID获取菜单权限树
     *
     * @param userId 用户ID
     * @return 菜单权限树
     */
    List<Permissions> selectMenuPermissionsByUserId(@Param("userId") Long userId);
} 