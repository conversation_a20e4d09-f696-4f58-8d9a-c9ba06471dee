package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.dto.resume.ResumeSaveRequest;
import com.alan6.resume.dto.resume.ResumeDetailResponse;
import com.alan6.resume.dto.resume.ResumeListResponse;
import com.alan6.resume.dto.resume.ResumeExportRequest;
import com.alan6.resume.dto.resume.ResumeExportResponse;
import com.alan6.resume.dto.resume.ResumePreviewRequest;
import com.alan6.resume.dto.resume.ResumePreviewResponse;
import com.alan6.resume.service.IResumeService;
import com.alan6.resume.service.IMinioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * 简历管理控制器
 * 
 * @description 提供简历的创建、编辑、查询、导出等功能
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/resume")
@RequiredArgsConstructor
@Tag(name = "简历管理", description = "简历的创建、编辑、查询、导出等接口")
public class ResumesController {

    /**
     * 简历服务接口
     */
    private final IResumeService resumeService;
    
    /**
     * MinIO服务接口
     */
    private final IMinioService minioService;

    /**
     * 创建新简历
     * 
     * @param request 创建简历请求参数
     * @return 创建成功的简历ID
     */
    @PostMapping("/create")
    @Operation(summary = "创建新简历", description = "基于模板创建一份新的简历")
    public R<Long> createResume(@RequestBody @Validated ResumeSaveRequest request) {
        log.info("创建新简历，用户ID：{}，模板ID：{}", request.getUserId(), request.getTemplateId());
        
        try {
            Long resumeId = resumeService.createResume(request);
            log.info("简历创建成功，简历ID：{}", resumeId);
            return R.ok(resumeId);
        } catch (Exception e) {
            log.error("创建简历失败", e);
            return R.fail("创建简历失败：" + e.getMessage());
        }
    }

    /**
     * 更新简历内容
     * 
     * @param id 简历ID
     * @param request 更新请求参数
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新简历", description = "更新简历的内容和设置")
    public R<Void> updateResume(
            @Parameter(description = "简历ID") @PathVariable Long id,
            @RequestBody @Validated ResumeSaveRequest request) {
        log.info("更新简历，简历ID：{}", id);
        
        try {
            resumeService.updateResume(id, request);
            log.info("简历更新成功，简历ID：{}", id);
            return R.ok();
        } catch (Exception e) {
            log.error("更新简历失败，简历ID：{}", id, e);
            return R.fail("更新简历失败：" + e.getMessage());
        }
    }

    /**
     * 获取简历详情
     * 
     * @param id 简历ID
     * @return 简历详细信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取简历详情", description = "获取简历的完整数据，包括所有模块内容和样式设置")
    public R<ResumeDetailResponse> getResumeDetail(@Parameter(description = "简历ID") @PathVariable Long id) {
        log.info("获取简历详情，简历ID：{}", id);
        
        try {
            ResumeDetailResponse detail = resumeService.getResumeDetail(id);
            return R.ok(detail);
        } catch (Exception e) {
            log.error("获取简历详情失败，简历ID：{}", id, e);
            return R.fail("获取简历详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户简历列表
     * 
     * @param userId 用户ID
     * @param status 简历状态（可选）
     * @return 简历列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取简历列表", description = "获取用户的所有简历列表")
    public R<List<ResumeListResponse>> getResumeList(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "简历状态") @RequestParam(required = false) Integer status) {
        log.info("获取用户简历列表，用户ID：{}，状态：{}", userId, status);
        
        try {
            List<ResumeListResponse> list = resumeService.getResumeList(userId, status);
            log.info("获取简历列表成功，数量：{}", list.size());
            return R.ok(list);
        } catch (Exception e) {
            log.error("获取简历列表失败，用户ID：{}", userId, e);
            return R.fail("获取简历列表失败：" + e.getMessage());
        }
    }

    /**
     * 删除简历
     * 
     * @param id 简历ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除简历", description = "软删除简历，可在回收站恢复")
    public R<Void> deleteResume(@Parameter(description = "简历ID") @PathVariable Long id) {
        log.info("删除简历，简历ID：{}", id);
        
        try {
            resumeService.deleteResume(id);
            log.info("简历删除成功，简历ID：{}", id);
            return R.ok();
        } catch (Exception e) {
            log.error("删除简历失败，简历ID：{}", id, e);
            return R.fail("删除简历失败：" + e.getMessage());
        }
    }

    /**
     * 复制简历
     * 
     * @param id 源简历ID
     * @param newName 新简历名称
     * @return 新简历ID
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制简历", description = "基于现有简历创建副本")
    public R<Long> copyResume(
            @Parameter(description = "源简历ID") @PathVariable Long id,
            @Parameter(description = "新简历名称") @RequestParam String newName) {
        log.info("复制简历，源简历ID：{}，新名称：{}", id, newName);
        
        try {
            Long newResumeId = resumeService.copyResume(id, newName);
            log.info("简历复制成功，新简历ID：{}", newResumeId);
            return R.ok(newResumeId);
        } catch (Exception e) {
            log.error("复制简历失败，源简历ID：{}", id, e);
            return R.fail("复制简历失败：" + e.getMessage());
        }
    }

    /**
     * 导出简历
     * 
     * @param request 导出请求参数
     * @return 导出任务信息
     */
    @PostMapping("/export")
    @Operation(summary = "导出简历", description = "导出简历为PDF、Word或图片格式")
    public R<ResumeExportResponse> exportResume(@RequestBody @Validated ResumeExportRequest request) {
        log.info("导出简历，简历ID：{}，格式：{}", request.getResumeId(), request.getFormat());
        
        try {
            ResumeExportResponse response = resumeService.exportResume(request);
            log.info("简历导出任务提交成功，任务ID：{}", response.getJobId());
            return R.ok(response);
        } catch (Exception e) {
            log.error("导出简历失败，简历ID：{}", request.getResumeId(), e);
            return R.fail("导出简历失败：" + e.getMessage());
        }
    }

    /**
     * 获取导出状态
     * 
     * @param jobId 导出任务ID
     * @return 导出状态信息
     */
    @GetMapping("/export/status/{jobId}")
    @Operation(summary = "获取导出状态", description = "查询简历导出任务的执行状态")
    public R<ResumeExportResponse> getExportStatus(
            @Parameter(description = "任务ID") @PathVariable String jobId) {
        log.info("查询导出状态，任务ID：{}", jobId);
        
        try {
            ResumeExportResponse status = resumeService.getExportStatus(jobId);
            return R.ok(status);
        } catch (Exception e) {
            log.error("查询导出状态失败，任务ID：{}", jobId, e);
            return R.fail("查询导出状态失败：" + e.getMessage());
        }
    }

    /**
     * 保存导出状态
     * 
     * @param request 预览请求参数
     * @return 状态ID
     */
    @PostMapping("/save-export-state")
    @Operation(summary = "保存导出状态", description = "保存简历的完整状态用于导出")
    public R<String> saveExportState(@RequestBody @Validated ResumePreviewRequest request) {
        log.info("保存导出状态，简历ID：{}", request.getResumeId());
        
        try {
            String stateId = resumeService.saveExportState(request);
            log.info("导出状态保存成功，状态ID：{}", stateId);
            return R.ok(stateId);
        } catch (Exception e) {
            log.error("保存导出状态失败", e);
            return R.fail("保存导出状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取导出数据
     * 
     * @param jobId 任务ID
     * @return 导出数据
     */
    @GetMapping("/export-data/{jobId}")
    @Operation(summary = "获取导出数据", description = "获取导出任务的完整数据")
    public R<ResumePreviewResponse> getExportData(
            @Parameter(description = "任务ID") @PathVariable String jobId) {
        log.info("获取导出数据，任务ID：{}", jobId);
        
        try {
            ResumePreviewResponse data = resumeService.getExportData(jobId);
            return R.ok(data);
        } catch (Exception e) {
            log.error("获取导出数据失败，任务ID：{}", jobId, e);
            return R.fail("获取导出数据失败：" + e.getMessage());
        }
    }

    /**
     * 自动保存简历
     * 
     * @param id 简历ID
     * @param request 保存请求参数
     * @return 操作结果
     */
    @PostMapping("/{id}/auto-save")
    @Operation(summary = "自动保存", description = "自动保存简历的修改内容")
    public R<Void> autoSaveResume(
            @Parameter(description = "简历ID") @PathVariable Long id,
            @RequestBody ResumeSaveRequest request) {
        log.debug("自动保存简历，简历ID：{}", id);
        
        try {
            resumeService.autoSaveResume(id, request);
            return R.ok();
        } catch (Exception e) {
            log.error("自动保存失败，简历ID：{}", id, e);
            return R.fail("自动保存失败：" + e.getMessage());
        }
    }

    /**
     * 下载导出文件
     * 
     * @param fileName 文件名
     * @return 文件内容
     */
    @GetMapping("/export/download/{fileName}")
    @Operation(summary = "下载导出文件", description = "下载已导出的简历文件")
    public ResponseEntity<byte[]> downloadExportFile(
            @Parameter(description = "文件名") @PathVariable String fileName) {
        log.info("下载导出文件，文件名：{}", fileName);
        
        try {
            byte[] fileData;
            String contentType = "application/pdf";
            
            // 首先尝试从MinIO下载
            try {
                fileData = minioService.downloadFile(fileName);
                log.info("从MinIO下载文件成功，大小：{} bytes", fileData.length);
            } catch (Exception e) {
                log.warn("从MinIO下载失败，尝试从本地文件系统下载：{}", e.getMessage());
                
                // 如果MinIO下载失败，尝试从本地文件系统下载
                String localPath = "tmp/resume-exports/" + fileName;
                File localFile = new File(localPath);
                
                if (localFile.exists()) {
                    fileData = Files.readAllBytes(Paths.get(localPath));
                    log.info("从本地文件系统下载成功，大小：{} bytes", fileData.length);
                } else {
                    log.error("文件不存在：{}", fileName);
                    return ResponseEntity.notFound().build();
                }
            }
            
            // 根据文件扩展名设置Content-Type
            if (fileName.toLowerCase().endsWith(".pdf")) {
                contentType = "application/pdf";
            } else if (fileName.toLowerCase().endsWith(".docx")) {
                contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            } else if (fileName.toLowerCase().endsWith(".png")) {
                contentType = "image/png";
            } else if (fileName.toLowerCase().endsWith(".jpg") || fileName.toLowerCase().endsWith(".jpeg")) {
                contentType = "image/jpeg";
            }
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(fileData.length);
            
            log.info("文件下载成功，文件名：{}，大小：{} bytes", fileName, fileData.length);
            return ResponseEntity.ok().headers(headers).body(fileData);
            
        } catch (Exception e) {
            log.error("下载文件失败，文件名：{}", fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }
} 
