package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.system.FeedbackRequest;
import com.alan6.resume.dto.system.FeedbackResponse;
import com.alan6.resume.entity.SystemFeedback;
import com.alan6.resume.mapper.SystemFeedbackMapper;
import com.alan6.resume.service.ISystemFeedbackService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统反馈表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemFeedbackServiceImpl extends ServiceImpl<SystemFeedbackMapper, SystemFeedback> implements ISystemFeedbackService {

    /**
     * 提交反馈
     *
     * @param request 反馈请求
     * @param userId  用户ID（可为空，表示匿名反馈）
     * @return 反馈提交结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> submitFeedback(FeedbackRequest request, Long userId) {
        log.info("提交反馈, userId: {}, type: {}, title: {}", userId, request.getType(), request.getTitle());
        
        try {
            // 创建反馈实体
            SystemFeedback feedback = new SystemFeedback();
            BeanUtils.copyProperties(request, feedback);
            
            // 设置基本信息
            feedback.setFeedbackId(generateFeedbackId());
            feedback.setUserId(userId);
            feedback.setStatus("pending");
            feedback.setPriority(determinePriority(request.getType()));
            feedback.setCreateTime(LocalDateTime.now());
            feedback.setUpdateTime(LocalDateTime.now());
            
            // 处理客户端信息
            if (request.getClientInfo() != null && !request.getClientInfo().isEmpty()) {
                // 将客户端信息转换为JSON字符串存储
                feedback.setClientInfo(convertClientInfoToJson(request.getClientInfo()));
            }
            
            // 保存反馈
            boolean result = save(feedback);
            if (!result) {
                throw new BusinessException("反馈提交失败");
            }
            
            // 计算预期回复时间（根据优先级）
            LocalDateTime expectedReplyTime = calculateExpectedReplyTime(feedback.getPriority());
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("feedbackId", feedback.getFeedbackId());
            response.put("status", feedback.getStatus());
            response.put("submitTime", feedback.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            response.put("expectedReplyTime", expectedReplyTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            response.put("message", "反馈提交成功，我们将尽快处理");
            
            log.info("反馈提交成功, feedbackId: {}", feedback.getFeedbackId());
            return response;
            
        } catch (Exception e) {
            log.error("反馈提交失败, userId: {}, error: {}", userId, e.getMessage(), e);
            throw new BusinessException("反馈提交失败: " + e.getMessage());
        }
    }

    /**
     * 获取反馈列表（分页）
     *
     * @param type   反馈类型（可选）
     * @param status 处理状态（可选）
     * @param page   页码
     * @param size   每页数量
     * @return 反馈列表
     */
    @Override
    public Page<FeedbackResponse> getFeedbackList(String type, String status, Integer page, Integer size) {
        log.info("获取反馈列表, type: {}, status: {}, page: {}, size: {}", type, status, page, size);
        
        // 构建查询条件
        LambdaQueryWrapper<SystemFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.hasText(type), SystemFeedback::getType, type)
                   .eq(StringUtils.hasText(status), SystemFeedback::getStatus, status)
                   .eq(SystemFeedback::getIsDeleted, 0)
                   .orderByDesc(SystemFeedback::getCreateTime);
        
        // 分页查询
        Page<SystemFeedback> feedbackPage = page(new Page<>(page, size), queryWrapper);
        
        // 转换为响应DTO
        List<FeedbackResponse> responseList = feedbackPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        // 构建分页响应
        Page<FeedbackResponse> responsePage = new Page<>(page, size);
        responsePage.setRecords(responseList);
        responsePage.setTotal(feedbackPage.getTotal());
        responsePage.setPages(feedbackPage.getPages());
        
        log.info("获取反馈列表成功, 总数: {}", feedbackPage.getTotal());
        return responsePage;
    }

    /**
     * 获取反馈详情
     *
     * @param feedbackId 反馈ID
     * @return 反馈详情
     */
    @Override
    public FeedbackResponse getFeedbackDetail(Long feedbackId) {
        log.info("获取反馈详情, feedbackId: {}", feedbackId);
        
        SystemFeedback feedback = getById(feedbackId);
        if (feedback == null) {
            throw new BusinessException("反馈不存在");
        }
        
        FeedbackResponse response = convertToResponse(feedback);
        log.info("获取反馈详情成功, feedbackId: {}", feedbackId);
        return response;
    }

    /**
     * 处理反馈
     *
     * @param feedbackId   反馈ID
     * @param handlerId    处理人ID
     * @param handlerReply 处理回复
     * @param status       新状态
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleFeedback(Long feedbackId, Long handlerId, String handlerReply, String status) {
        log.info("处理反馈, feedbackId: {}, handlerId: {}, status: {}", feedbackId, handlerId, status);
        
        SystemFeedback feedback = getById(feedbackId);
        if (feedback == null) {
            throw new BusinessException("反馈不存在");
        }
        
        // 更新反馈信息
        feedback.setHandlerId(handlerId);
        feedback.setHandlerReply(handlerReply);
        feedback.setStatus(status);
        feedback.setHandleTime(LocalDateTime.now());
        feedback.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(feedback);
        if (result) {
            log.info("反馈处理成功, feedbackId: {}", feedbackId);
        } else {
            log.error("反馈处理失败, feedbackId: {}", feedbackId);
        }
        
        return result;
    }

    /**
     * 生成反馈编号
     *
     * @return 反馈编号
     */
    @Override
    public String generateFeedbackId() {
        // 格式：FB + 年月日 + 时分秒 + 3位随机数
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int randomNum = (int) (Math.random() * 1000);
        return String.format("FB%s%03d", timestamp, randomNum);
    }

    /**
     * 转换为响应DTO
     *
     * @param feedback 反馈实体
     * @return 响应DTO
     */
    private FeedbackResponse convertToResponse(SystemFeedback feedback) {
        FeedbackResponse response = new FeedbackResponse();
        BeanUtils.copyProperties(feedback, response);
        response.setSubmitTime(feedback.getCreateTime());
        return response;
    }

    /**
     * 根据反馈类型确定优先级
     *
     * @param type 反馈类型
     * @return 优先级
     */
    private String determinePriority(String type) {
        switch (type) {
            case "bug":
                return "high";  // 错误报告高优先级
            case "complaint":
                return "medium"; // 投诉中优先级
            case "feature":
            case "suggestion":
            default:
                return "low";   // 功能建议和其他建议低优先级
        }
    }

    /**
     * 计算预期回复时间
     *
     * @param priority 优先级
     * @return 预期回复时间
     */
    private LocalDateTime calculateExpectedReplyTime(String priority) {
        LocalDateTime now = LocalDateTime.now();
        switch (priority) {
            case "urgent":
                return now.plusHours(4);   // 紧急：4小时内
            case "high":
                return now.plusHours(24);  // 高：24小时内
            case "medium":
                return now.plusDays(2);    // 中：2天内
            case "low":
            default:
                return now.plusDays(7);    // 低：7天内
        }
    }

    /**
     * 将客户端信息转换为JSON字符串
     *
     * @param clientInfo 客户端信息Map
     * @return JSON字符串
     */
    private String convertClientInfoToJson(Map<String, Object> clientInfo) {
        try {
            // 这里简单处理，实际项目中可以使用JSON库
            StringBuilder json = new StringBuilder("{");
            clientInfo.forEach((key, value) -> {
                json.append("\"").append(key).append("\":\"").append(value).append("\",");
            });
            if (json.length() > 1) {
                json.setLength(json.length() - 1); // 移除最后的逗号
            }
            json.append("}");
            return json.toString();
        } catch (Exception e) {
            log.warn("客户端信息转换失败: {}", e.getMessage());
            return "{}";
        }
    }
} 