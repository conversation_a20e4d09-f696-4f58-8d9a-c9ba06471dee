<template>
  <div class="portfolio-form">
    <div class="form-header">
      <h4 class="form-title">作品集</h4>
      <p class="form-desc">展示您的个人作品和项目</p>
    </div>
    
    <div v-if="formData.items && formData.items.length > 0" class="portfolio-list">
      <div v-for="(item, index) in formData.items" :key="index" class="portfolio-item">
        <div class="item-header">
          <input
            v-model="item.title"
            type="text"
            class="title-input"
            placeholder="作品标题"
            @input="handleItemChange"
          />
          <select
            v-model="item.type"
            class="type-select"
            @change="handleItemChange"
          >
            <option value="">选择类型</option>
            <option value="网站">网站</option>
            <option value="移动应用">移动应用</option>
            <option value="设计作品">设计作品</option>
            <option value="开源项目">开源项目</option>
            <option value="其他">其他</option>
          </select>
          <button 
            class="delete-btn"
            @click="removeItem(index)"
            title="删除作品"
          >
            ×
          </button>
        </div>
        
        <div class="item-details">
          <input
            v-model="item.url"
            type="url"
            class="url-input"
            placeholder="作品链接"
            @input="handleItemChange"
          />
          <div class="date-picker-wrapper">
            <input
              v-model="item.date"
              type="text"
              class="date-input"
              placeholder="完成时间"
              readonly
              @click="showDatePicker(index)"
            />
            <div 
              v-if="activeDatePicker === index" 
              class="date-picker-dropdown"
            >
              <div class="date-picker-header">
                <button type="button" @click="changeYear(-1)">‹</button>
                <span>{{ currentYear }}</span>
                <button type="button" @click="changeYear(1)">›</button>
              </div>
              <div class="month-grid">
                <button
                  v-for="month in 12"
                  :key="month"
                  type="button"
                  class="month-btn"
                  :class="{ active: isSelectedMonth(index, month) }"
                  @click="selectDate(index, currentYear, month)"
                >
                  {{ month }}月
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <RichTextEditor
          v-model="item.description"
          placeholder="作品描述"
          min-height="120px"
          @update:modelValue="handleItemChange"
        />
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无作品信息</p>
      <p class="empty-hint">点击下方按钮添加作品</p>
    </div>
    
    <div class="form-actions">
      <button class="add-btn" @click="addItem">
        <Icon name="plus" size="sm" />
        <span>添加作品</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, ref, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ items: [] })
  }
})

const emit = defineEmits(['update'])

const formData = reactive({
  items: []
})

let debounceTimer = null

// 日期选择器状态
const activeDatePicker = ref('')
const currentYear = ref(new Date().getFullYear())

watch(() => props.data, (newData) => {
  if (newData && newData.items) {
    formData.items = [...newData.items]
  } else {
    formData.items = []
  }
}, { immediate: true, deep: true })

const handleItemChange = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  debounceTimer = setTimeout(() => {
    emit('update', { items: [...formData.items] })
  }, 300)
}

const addItem = () => {
  formData.items.push({
    title: '',
    type: '',
    url: '',
    date: '',
    description: ''
  })
  handleItemChange()
}

const removeItem = (index) => {
  formData.items.splice(index, 1)
  handleItemChange()
}

/**
 * 显示日期选择器
 */
const showDatePicker = (index) => {
  activeDatePicker.value = index
  // 根据当前值设置年份
  const currentValue = formData.items[index].date
  if (currentValue) {
    const year = parseInt(currentValue.split('-')[0])
    if (year) {
      currentYear.value = year
    }
  }
}

/**
 * 改变年份
 */
const changeYear = (delta) => {
  currentYear.value += delta
}

/**
 * 检查是否为选中的月份
 */
const isSelectedMonth = (index, month) => {
  const currentValue = formData.items[index].date
  if (!currentValue) return false
  
  const [year, selectedMonth] = currentValue.split('-')
  return parseInt(year) === currentYear.value && parseInt(selectedMonth) === month
}

/**
 * 选择日期
 */
const selectDate = (index, year, month) => {
  const dateStr = `${year}-${month.toString().padStart(2, '0')}`
  formData.items[index].date = dateStr
  
  // 关闭日期选择器
  activeDatePicker.value = ''
  
  // 触发更新
  handleItemChange()
}

/**
 * 点击外部关闭日期选择器
 */
const handleClickOutside = (event) => {
  if (!event.target.closest('.date-picker-wrapper')) {
    activeDatePicker.value = ''
  }
}

// 挂载时添加全局点击监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 卸载时移除监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.portfolio-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.portfolio-list {
  @apply space-y-4 mb-6;
}

.portfolio-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-3;
}

.item-header {
  @apply flex items-center gap-2;
}

.title-input {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.type-select {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
  min-width: 120px;
}

.delete-btn {
  @apply w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold;
}

.item-details {
  @apply flex items-center gap-2;
}

.url-input {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.date-picker-wrapper {
  @apply relative;
}

.date-input {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer;
  min-width: 140px;
}

.date-picker-dropdown {
  @apply absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 p-3;
  min-width: 240px;
}

.date-picker-header {
  @apply flex items-center justify-between mb-3;
}

.date-picker-header button {
  @apply w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-all duration-200 text-sm;
}

.date-picker-header span {
  @apply font-medium text-gray-900 text-base;
}

.month-grid {
  @apply grid grid-cols-4 gap-2;
}

.month-btn {
  @apply px-3 py-2 text-sm text-gray-700 bg-gray-50 rounded hover:bg-blue-50 hover:text-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-400 transition-all duration-200 text-center whitespace-nowrap;
  min-width: 50px;
}

.month-btn.active {
  @apply bg-blue-500 text-white;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-xs text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}
</style> 