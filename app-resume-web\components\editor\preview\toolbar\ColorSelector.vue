<template>
  <div class="color-selector">
    <!-- 颜色显示按钮 -->
    <div class="color-trigger" @click="toggleColorPicker">
      <div 
        class="color-preview"
        :style="{ backgroundColor: modelValue }"
        :title="`当前颜色: ${modelValue}`"
      ></div>
      <svg class="dropdown-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="6,9 12,15 18,9"></polyline>
      </svg>
    </div>
    
    <!-- 颜色选择面板 -->
    <Transition name="color-panel">
      <div v-show="isPickerOpen" class="color-panel">
        <div class="color-options">
          <button
            v-for="(color, index) in colorOptions"
            :key="color.value"
            class="color-option"
            :class="{ 'active': modelValue === color.value }"
            :style="{ backgroundColor: color.value }"
            :title="color.label"
            @click="selectColor(color.value)"
          >
            <svg 
              v-if="modelValue === color.value"
              class="check-icon"
              width="12" 
              height="12" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              stroke-width="2" 
              stroke-linecap="round" 
              stroke-linejoin="round"
            >
              <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
          </button>
        </div>
      </div>
    </Transition>
    
    <!-- 点击遮罩层关闭 -->
    <div 
      v-if="isPickerOpen"
      class="color-overlay"
      @click="closeColorPicker"
    ></div>
  </div>
</template>

<script setup>
/**
 * 颜色选择器组件
 * @description 提供颜色选择功能，支持弹出式颜色面板
 * <AUTHOR>
 * @since 2.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, onMounted, onUnmounted } from 'vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 当前选中的颜色值
   */
  modelValue: {
    type: String,
    default: '#333333'
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['update:modelValue', 'change'])

// ================================
// 响应式数据
// ================================

/**
 * 颜色选择器开启状态
 */
const isPickerOpen = ref(false)

/**
 * 颜色选项配置（黑到灰渐变，5个颜色）
 */
const colorOptions = [
  { label: '纯黑', value: '#000000' },
  { label: '深灰', value: '#333333' }, // 默认选择
  { label: '中灰', value: '#666666' },
  { label: '浅灰', value: '#999999' },
  { label: '淡灰', value: '#CCCCCC' }
]

// ================================
// 生命周期钩子
// ================================
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// ================================
// 事件处理方法
// ================================

/**
 * 切换颜色选择器
 */
const toggleColorPicker = () => {
  isPickerOpen.value = !isPickerOpen.value
}

/**
 * 关闭颜色选择器
 */
const closeColorPicker = () => {
  isPickerOpen.value = false
}

/**
 * 选择颜色
 * @param {string} color - 选中的颜色值
 */
const selectColor = (color) => {
  emit('update:modelValue', color)
  emit('change', color)
  closeColorPicker()
}

/**
 * 处理点击外部区域
 * @param {Event} event - 点击事件
 */
const handleClickOutside = (event) => {
  const colorSelector = event.target.closest('.color-selector')
  if (!colorSelector && isPickerOpen.value) {
    closeColorPicker()
  }
}
</script>

<style scoped>
/* ================================
 * 颜色选择器容器
 * ================================ */
.color-selector {
  @apply relative flex items-center;
}

/* ================================
 * 颜色触发按钮
 * ================================ */
.color-trigger {
  @apply flex items-center gap-2;
  @apply px-2 py-1.5;
  @apply bg-white border border-gray-200 rounded-lg;
  @apply cursor-pointer;
  @apply transition-all duration-200;
  @apply hover:border-gray-300 hover:shadow-sm;
}

.color-trigger:hover .dropdown-icon {
  @apply text-gray-600;
}

/* ================================
 * 颜色预览
 * ================================ */
.color-preview {
  @apply w-5 h-5 rounded border border-gray-300;
  @apply shadow-inner;
}

/* ================================
 * 下拉图标
 * ================================ */
.dropdown-icon {
  @apply text-gray-400;
  @apply transition-transform duration-200;
}

.color-trigger:hover .dropdown-icon {
  @apply rotate-180;
}

/* ================================
 * 颜色选择面板
 * ================================ */
.color-panel {
  @apply absolute top-full left-0 mt-2 z-20;
  @apply bg-white border border-gray-200 rounded-lg;
  @apply shadow-lg;
  @apply p-3;
  @apply min-w-48;
}

.color-options {
  @apply flex gap-2;
}

/* ================================
 * 颜色选项
 * ================================ */
.color-option {
  @apply relative w-8 h-8 rounded-lg;
  @apply border-2 border-transparent;
  @apply cursor-pointer;
  @apply transition-all duration-200;
  @apply hover:scale-110 hover:border-gray-300;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.color-option.active {
  @apply border-primary-500 ring-2 ring-primary-500 ring-opacity-30;
  @apply scale-110;
}

/* ================================
 * 选中图标
 * ================================ */
.check-icon {
  @apply absolute inset-0 m-auto;
  @apply text-white;
  @apply drop-shadow-sm;
}

/* ================================
 * 遮罩层
 * ================================ */
.color-overlay {
  @apply fixed inset-0 z-10;
  @apply bg-transparent;
}

/* ================================
 * 颜色面板动画
 * ================================ */
.color-panel-enter-active,
.color-panel-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.color-panel-enter-from {
  @apply opacity-0 -translate-y-2 scale-95;
}

.color-panel-leave-to {
  @apply opacity-0 translate-y-2 scale-95;
}

.color-panel-enter-to,
.color-panel-leave-from {
  @apply opacity-100 translate-y-0 scale-100;
}

/* ================================
 * 响应式设计
 * ================================ */
@media (max-width: 768px) {
  .color-panel {
    @apply left-auto right-0;
  }
  
  .color-options {
    @apply gap-1;
  }
  
  .color-option {
    @apply w-7 h-7;
  }
}
</style> 