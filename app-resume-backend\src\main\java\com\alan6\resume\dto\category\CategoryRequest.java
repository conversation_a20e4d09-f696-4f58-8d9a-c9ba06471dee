package com.alan6.resume.dto.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 分类请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@Schema(description = "分类请求DTO")
public class CategoryRequest {

    @Schema(description = "分类ID（更新时需要）")
    private Long id;

    @Schema(description = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @Schema(description = "分类代码")
    @NotBlank(message = "分类代码不能为空")
    private String code;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "父分类ID（0为顶级分类）")
    @NotNull(message = "父分类ID不能为空")
    private Long parentId;

    @Schema(description = "分类类型")
    @NotBlank(message = "分类类型不能为空")
    private String categoryType;

    @Schema(description = "分类图标URL")
    private String iconUrl;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "状态（0:禁用,1:启用）")
    private Byte status;
} 