package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.template.*;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.entity.TemplateCategories;
import com.alan6.resume.entity.Resumes;
import com.alan6.resume.mapper.ResumeTemplatesMapper;
import com.alan6.resume.mapper.TemplateCategoriesMapper;
import com.alan6.resume.mapper.ResumesMapper;
import com.alan6.resume.service.IUserFavoritesService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ResumeTemplatesServiceImpl测试类
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("简历模板服务测试")
class ResumeTemplatesServiceImplTest {

    @Spy
    @InjectMocks
    private ResumeTemplatesServiceImpl resumeTemplatesService;

    @Mock
    private ResumeTemplatesMapper resumeTemplatesMapper;

    @Mock
    private TemplateCategoriesMapper templateCategoriesMapper;

    @Mock
    private ResumesMapper resumesMapper;

    @Mock
    private IUserFavoritesService userFavoritesService;

    private Long userId;
    private Long templateId;
    private ResumeTemplates mockTemplate;
    private TemplateCategories mockCategory;
    private ResumeTemplates sampleTemplate;
    private TemplatePreviewRequest previewRequest;
    private TemplatePreviewWithDataRequest previewWithDataRequest;

    @BeforeEach
    void setUp() {
        userId = 1001L;
        templateId = 2001L;
        
        // 准备模板分类数据
        mockCategory = new TemplateCategories();
        mockCategory.setId(1L);
        mockCategory.setName("商务简约");
        mockCategory.setDescription("适合商务场合的简约风格模板");
        mockCategory.setIconUrl("business-icon.png");
        mockCategory.setSortOrder(1);
        mockCategory.setStatus((byte) 1);
        mockCategory.setIsDeleted((byte) 0);
        
        // 准备模板数据
        mockTemplate = new ResumeTemplates();
        mockTemplate.setId(templateId);
        mockTemplate.setName("商务简约模板");
        mockTemplate.setDescription("适合商务人士使用的简约风格简历模板");
        mockTemplate.setCategoryId(1L);
        mockTemplate.setIndustry("通用");
        mockTemplate.setStyle("简约");
        mockTemplate.setColorScheme("蓝色");
        mockTemplate.setPrice(new BigDecimal("0.00"));
        mockTemplate.setIsPremium((byte) 0);
        mockTemplate.setPreviewImageUrl("https://example.com/preview/2001.jpg");
        mockTemplate.setUseCount(100);
        mockTemplate.setStatus((byte) 1);
        mockTemplate.setIsDeleted((byte) 0);
        mockTemplate.setCreateTime(LocalDateTime.now());
        mockTemplate.setUpdateTime(LocalDateTime.now());

        // 创建示例模板数据
        sampleTemplate = new ResumeTemplates();
        sampleTemplate.setId(3001L);
        sampleTemplate.setName("简约商务模板");
        sampleTemplate.setDescription("适合商务人士的简约风格模板");
        sampleTemplate.setCategoryId(1001L);
        sampleTemplate.setConfigData("{\"layout\":\"classic\",\"color\":\"blue\"}");
        sampleTemplate.setPreviewImageUrl("https://cdn.example.com/templates/preview1.jpg");
        sampleTemplate.setPrice(BigDecimal.ZERO);
        sampleTemplate.setIsPremium((byte) 0);
        sampleTemplate.setUseCount(150);
        sampleTemplate.setStatus((byte) 1);
        sampleTemplate.setIsDeleted((byte) 0);
        sampleTemplate.setCreateTime(LocalDateTime.now().minusDays(30));
        sampleTemplate.setUpdateTime(LocalDateTime.now());

        // 创建预览请求数据
        previewRequest = new TemplatePreviewRequest();
        previewRequest.setFormat("image");
        previewRequest.setQuality("medium");
        previewRequest.setWidth(800);
        previewRequest.setHeight(0);
        previewRequest.setScale(1.0);

        // 创建带数据的预览请求数据
        previewWithDataRequest = new TemplatePreviewWithDataRequest();
        previewWithDataRequest.setFormat("image");
        previewWithDataRequest.setQuality("high");
        previewWithDataRequest.setWidth(1200);
        previewWithDataRequest.setHeight(0);
        previewWithDataRequest.setScale(1.0);
        previewWithDataRequest.setShowWatermark(false);

        // 设置简历数据
        Map<String, Object> resumeData = Map.of(
                "basicInfo", Map.of("realName", "张三", "phone", "13800138000", "email", "<EMAIL>", "jobIntention", "前端开发工程师"),
                "workExperiences", List.of(Map.of("companyName", "XX科技有限公司", "position", "前端开发工程师", "startDate", "2022-01-01", "endDate", "2024-12-01", "description", "负责前端开发工作，参与多个项目的开发和维护。"))
        );
        previewWithDataRequest.setResumeData(resumeData);
    }

    /**
     * 测试获取模板列表 - 成功场景
     */
    @Test
    void testGetTemplateList_Success() {
        // 准备请求参数
        TemplateListRequest request = new TemplateListRequest();
        request.setCurrent(1);
        request.setSize(10);
        request.setCategoryId(1L);
        request.setSortBy("useCount");
        
        // 准备分页结果
        Page<ResumeTemplates> mockPage = new Page<>(1, 10);
        mockPage.setTotal(1);
        mockPage.setRecords(Collections.singletonList(mockTemplate));
        
        // Mock方法调用
        doReturn(mockPage).when(resumeTemplatesService).page(any(Page.class), any(LambdaQueryWrapper.class));
        when(templateCategoriesMapper.selectById(1L)).thenReturn(mockCategory);
        
        // 执行测试
        TemplateListResponse result = resumeTemplatesService.getTemplateList(request, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        
        TemplateResponse templateResponse = result.getRecords().get(0);
        assertEquals(templateId, templateResponse.getId());
        assertEquals("商务简约模板", templateResponse.getName());
        assertEquals("商务简约", templateResponse.getCategoryName());
    }

    /**
     * 测试获取模板列表 - 空结果
     */
    @Test
    void testGetTemplateList_EmptyResult() {
        // 准备请求参数
        TemplateListRequest request = new TemplateListRequest();
        request.setCurrent(1);
        request.setSize(10);
        
        // 准备空分页结果
        Page<ResumeTemplates> mockPage = new Page<>(1, 10);
        mockPage.setTotal(0);
        mockPage.setRecords(Collections.emptyList());
        
        // Mock方法调用
        doReturn(mockPage).when(resumeTemplatesService).page(any(Page.class), any(LambdaQueryWrapper.class));
        
        // 执行测试
        TemplateListResponse result = resumeTemplatesService.getTemplateList(request, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.getRecords().isEmpty());
    }

    /**
     * 测试获取模板详情 - 成功场景
     */
    @Test
    void testGetTemplateDetail_Success() {
        // Mock方法调用
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        when(templateCategoriesMapper.selectById(1L)).thenReturn(mockCategory);
        // Mock getRelatedTemplates需要的list方法
        doReturn(Collections.emptyList()).when(resumeTemplatesService).list(any(LambdaQueryWrapper.class));
        
        // 执行测试
        TemplateDetailResponse result = resumeTemplatesService.getTemplateDetail(templateId, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        assertEquals("商务简约模板", result.getName());
        assertEquals("商务简约", result.getCategoryName());
        assertNotNull(result.getTags());
        assertNotNull(result.getFeatures());
    }

    /**
     * 测试获取模板详情 - 模板不存在
     */
    @Test
    void testGetTemplateDetail_TemplateNotExists() {
        // Mock返回null
        doReturn(null).when(resumeTemplatesService).getById(templateId);
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> resumeTemplatesService.getTemplateDetail(templateId, userId));
        
        assertEquals("模板不存在或已删除", exception.getMessage());
    }

    /**
     * 测试获取模板详情 - 模板已删除
     */
    @Test
    void testGetTemplateDetail_TemplateDeleted() {
        // 设置模板为已删除状态
        mockTemplate.setIsDeleted((byte) 1);
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> resumeTemplatesService.getTemplateDetail(templateId, userId));
        
        assertEquals("模板不存在或已删除", exception.getMessage());
    }

    /**
     * 测试获取模板详情 - 模板未上架
     */
    @Test
    void testGetTemplateDetail_TemplateNotPublished() {
        // 设置模板为未上架状态
        mockTemplate.setStatus((byte) 0);
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> resumeTemplatesService.getTemplateDetail(templateId, userId));
        
        assertEquals("模板未上架，无法查看", exception.getMessage());
    }

    /**
     * 测试使用模板 - 成功场景
     */
    @Test
    void testUseTemplate_Success() {
        // 准备请求参数
        TemplateUseRequest request = new TemplateUseRequest();
        request.setResumeName("我的简历");
        request.setLanguage("zh-CN");
        
        // Mock方法调用
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        doReturn(true).when(resumeTemplatesService).checkUserPermission(templateId, userId);
        doAnswer(invocation -> {
            Resumes resume = invocation.getArgument(0);
            resume.setId(3001L);
            return 1;
        }).when(resumesMapper).insert(any(Resumes.class));
        doNothing().when(resumeTemplatesService).incrementUseCount(templateId);
        
        // 执行测试
        TemplateUseResponse result = resumeTemplatesService.useTemplate(templateId, request, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3001L, result.getResumeId());
        assertEquals("我的简历", result.getResumeName());
        assertEquals(templateId, result.getTemplateId());
        assertEquals("/resume/edit/3001", result.getRedirectUrl());
        
        // 验证方法调用
        verify(resumesMapper, times(1)).insert(any(Resumes.class));
        verify(resumeTemplatesService, times(1)).incrementUseCount(templateId);
    }

    /**
     * 测试使用模板 - 模板不存在
     */
    @Test
    void testUseTemplate_TemplateNotExists() {
        // 准备请求参数
        TemplateUseRequest request = new TemplateUseRequest();
        request.setResumeName("我的简历");
        
        // Mock返回null
        doReturn(null).when(resumeTemplatesService).getById(templateId);
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> resumeTemplatesService.useTemplate(templateId, request, userId));
        
        assertEquals("模板不存在或已删除", exception.getMessage());
    }

    /**
     * 测试使用模板 - 无权限
     */
    @Test
    void testUseTemplate_NoPermission() {
        // 准备请求参数
        TemplateUseRequest request = new TemplateUseRequest();
        request.setResumeName("我的简历");
        
        // Mock方法调用
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        doReturn(false).when(resumeTemplatesService).checkUserPermission(templateId, userId);
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> resumeTemplatesService.useTemplate(templateId, request, userId));
        
        assertEquals("您没有使用此模板的权限", exception.getMessage());
    }

    /**
     * 测试搜索模板 - 成功场景
     */
    @Test
    void testSearchTemplates_Success() {
        // 准备请求参数
        TemplateSearchRequest request = new TemplateSearchRequest();
        request.setKeyword("商务");
        request.setCurrent(1);
        request.setSize(10);
        
        // 准备分页结果
        Page<ResumeTemplates> mockPage = new Page<>(1, 10);
        mockPage.setTotal(1);
        mockPage.setRecords(Collections.singletonList(mockTemplate));
        
        // Mock方法调用
        doReturn(mockPage).when(resumeTemplatesService).page(any(Page.class), any(LambdaQueryWrapper.class));
        when(templateCategoriesMapper.selectById(1L)).thenReturn(mockCategory);
        
        // 执行测试
        TemplateListResponse result = resumeTemplatesService.searchTemplates(request, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        assertEquals("商务简约模板", result.getRecords().get(0).getName());
    }

    /**
     * 测试推荐模板 - 成功场景
     */
    @Test
    void testRecommendTemplates_Success() {
        // 准备请求参数
        TemplateRecommendRequest request = new TemplateRecommendRequest();
        request.setIndustry("IT");
        request.setLimit(5);
        
        // Mock方法调用
        doReturn(Collections.singletonList(mockTemplate)).when(resumeTemplatesService).list(any(LambdaQueryWrapper.class));
        when(templateCategoriesMapper.selectById(1L)).thenReturn(mockCategory);
        
        // 执行测试
        List<TemplateResponse> result = resumeTemplatesService.recommendTemplates(request, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("商务简约模板", result.get(0).getName());
    }

    /**
     * 测试获取热门模板 - 成功场景
     */
    @Test
    void testGetHotTemplates_Success() {
        // 准备请求参数
        TemplateHotRequest request = new TemplateHotRequest();
        request.setLimit(10);
        
        // Mock方法调用
        doReturn(Collections.singletonList(mockTemplate)).when(resumeTemplatesService).list(any(LambdaQueryWrapper.class));
        when(templateCategoriesMapper.selectById(1L)).thenReturn(mockCategory);
        
        // 执行测试
        List<TemplateResponse> result = resumeTemplatesService.getHotTemplates(request, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("商务简约模板", result.get(0).getName());
    }

    /**
     * 测试增加使用次数 - 成功场景
     */
    @Test
    void testIncrementUseCount_Success() {
        // Mock更新操作
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        doReturn(true).when(resumeTemplatesService).updateById(any(ResumeTemplates.class));
        
        // 执行测试
        assertDoesNotThrow(() -> resumeTemplatesService.incrementUseCount(templateId));
        
        // 验证使用次数增加
        verify(resumeTemplatesService, times(1)).getById(templateId);
        verify(resumeTemplatesService, times(1)).updateById(any(ResumeTemplates.class));
    }

    /**
     * 测试检查用户权限 - 免费模板
     */
    @Test
    void testCheckUserPermission_FreeTemplate() {
        // 设置为免费模板
        mockTemplate.setIsPremium((byte) 0);
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        
        // 执行测试
        boolean result = resumeTemplatesService.checkUserPermission(templateId, userId);
        
        // 验证结果 - 免费模板应该有权限
        assertTrue(result);
    }

    /**
     * 测试检查用户权限 - 付费模板
     */
    @Test
    void testCheckUserPermission_PaidTemplate() {
        // 设置为付费模板
        mockTemplate.setIsPremium((byte) 1);
        mockTemplate.setPrice(new BigDecimal("29.90"));
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        
        // 执行测试
        boolean result = resumeTemplatesService.checkUserPermission(templateId, userId);
        
        // 验证结果 - 付费模板的权限检查逻辑
        assertTrue(result);
    }

    /**
     * 测试参数校验 - 无效参数
     */
    @Test
    void testValidateListRequest_InvalidParameters() {
        // 准备无效请求参数
        TemplateListRequest request = new TemplateListRequest();
        request.setCurrent(0);  // 无效的页码
        request.setSize(0);     // 无效的页面大小
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> resumeTemplatesService.getTemplateList(request, userId));
        
        assertTrue(exception.getMessage().contains("获取模板列表失败"));
    }

    /**
     * 测试数据转换 - 验证DTO转换正确性
     */
    @Test
    void testConvertToTemplateDetailResponse() {
        // Mock相关数据
        when(templateCategoriesMapper.selectById(1L)).thenReturn(mockCategory);
        
        // 通过getTemplateDetail方法来测试转换逻辑
        doReturn(mockTemplate).when(resumeTemplatesService).getById(templateId);
        // Mock getRelatedTemplates需要的list方法
        doReturn(Collections.emptyList()).when(resumeTemplatesService).list(any(LambdaQueryWrapper.class));
        
        TemplateDetailResponse result = resumeTemplatesService.getTemplateDetail(templateId, userId);
        
        // 验证转换结果
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        assertEquals("商务简约模板", result.getName());
        assertEquals("适合商务人士使用的简约风格简历模板", result.getDescription());
        assertEquals("商务简约", result.getCategoryName());
    }

    /**
     * 测试模板预览 - 成功场景
     */
    @Test
    @DisplayName("测试模板预览 - 成功生成预览")
    void testPreviewTemplate_Success() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // When
        TemplatePreviewResponse response = resumeTemplatesService.previewTemplate(templateId, previewRequest, userId);

        // Then
        assertNotNull(response);
        assertEquals(templateId, response.getTemplateId());
        assertEquals(sampleTemplate.getName(), response.getTemplateName());
        assertEquals("image", response.getPreviewType());
        assertNotNull(response.getPreviewUrl());
        assertNotNull(response.getPreviewUrls());
        assertNotNull(response.getDimensions());
        assertEquals(800, response.getDimensions().getWidth());
        assertTrue(response.getDimensions().getHeight() > 0);
        assertNotNull(response.getGenerateTime());
        assertEquals(3600, response.getExpiresIn());
        assertFalse(response.getIsTemporary());
        assertTrue(response.getFileSize() > 0);

        // 验证多尺寸预览URL
        assertTrue(response.getPreviewUrls().containsKey("thumbnail"));
        assertTrue(response.getPreviewUrls().containsKey("medium"));
        assertTrue(response.getPreviewUrls().containsKey("large"));
    }

    /**
     * 测试模板预览 - 模板不存在
     */
    @Test
    @DisplayName("测试模板预览 - 模板不存在")
    void testPreviewTemplate_TemplateNotFound() {
        // Given
        Long templateId = 9999L;
        Long userId = 1001L;

        // Mock模板查询返回null
        doReturn(null).when(resumeTemplatesService).getById(templateId);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            resumeTemplatesService.previewTemplate(templateId, previewRequest, userId);
        });

        assertEquals("模板不存在或已删除", exception.getMessage());
    }

    /**
     * 测试模板预览 - 模板已删除
     */
    @Test
    @DisplayName("测试模板预览 - 模板已删除")
    void testPreviewTemplate_TemplateDeleted() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;
        
        // 创建已删除的模板副本
        ResumeTemplates deletedTemplate = new ResumeTemplates();
        deletedTemplate.setId(templateId);
        deletedTemplate.setName("已删除模板");
        deletedTemplate.setIsDeleted((byte) 1); // 设置为已删除
        deletedTemplate.setStatus((byte) 1);

        // Mock模板查询
        doReturn(deletedTemplate).when(resumeTemplatesService).getById(templateId);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            resumeTemplatesService.previewTemplate(templateId, previewRequest, userId);
        });

        assertEquals("模板不存在或已删除", exception.getMessage());
    }

    /**
     * 测试模板预览 - 模板已下架
     */
    @Test
    @DisplayName("测试模板预览 - 模板已下架")
    void testPreviewTemplate_TemplateInactive() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;
        
        // 创建已下架的模板副本
        ResumeTemplates inactiveTemplate = new ResumeTemplates();
        inactiveTemplate.setId(templateId);
        inactiveTemplate.setName("已下架模板");
        inactiveTemplate.setIsDeleted((byte) 0);
        inactiveTemplate.setStatus((byte) 0); // 设置为已下架

        // Mock模板查询
        doReturn(inactiveTemplate).when(resumeTemplatesService).getById(templateId);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            resumeTemplatesService.previewTemplate(templateId, previewRequest, userId);
        });

        assertEquals("模板已下架，无法预览", exception.getMessage());
    }

    /**
     * 测试模板预览（带数据） - 成功场景
     */
    @Test
    @DisplayName("测试模板预览（带数据） - 成功生成预览")
    void testPreviewTemplateWithData_Success() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // When
        TemplatePreviewResponse response = resumeTemplatesService.previewTemplateWithData(templateId, previewWithDataRequest, userId);

        // Then
        assertNotNull(response);
        assertEquals(templateId, response.getTemplateId());
        assertEquals(sampleTemplate.getName(), response.getTemplateName());
        assertEquals("image", response.getPreviewType());
        assertNotNull(response.getPreviewUrl());
        assertNotNull(response.getDimensions());
        assertEquals(1200, response.getDimensions().getWidth());
        assertTrue(response.getDimensions().getHeight() > 0);
        assertNotNull(response.getGenerateTime());
        assertEquals(1800, response.getExpiresIn()); // 临时文件30分钟
        assertTrue(response.getIsTemporary());
        assertTrue(response.getFileSize() > 0);
    }

    /**
     * 测试模板预览（带数据） - 简历数据为空
     */
    @Test
    @DisplayName("测试模板预览（带数据） - 简历数据为空")
    void testPreviewTemplateWithData_EmptyResumeData() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;
        previewWithDataRequest.setResumeData(null);

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            resumeTemplatesService.previewTemplateWithData(templateId, previewWithDataRequest, userId);
        });

        assertEquals("简历数据不能为空", exception.getMessage());
    }

    /**
     * 测试模板预览（带数据） - 简历数据为空Map
     */
    @Test
    @DisplayName("测试模板预览（带数据） - 简历数据为空Map")
    void testPreviewTemplateWithData_EmptyResumeDataMap() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;
        previewWithDataRequest.setResumeData(Map.of());

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            resumeTemplatesService.previewTemplateWithData(templateId, previewWithDataRequest, userId);
        });

        assertEquals("简历数据不能为空", exception.getMessage());
    }

    /**
     * 测试预览参数设置 - 默认值
     */
    @Test
    @DisplayName("测试预览参数设置 - 使用默认值")
    void testPreviewTemplate_DefaultParameters() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;
        TemplatePreviewRequest requestWithDefaults = new TemplatePreviewRequest();
        // 不设置任何参数，使用默认值

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // When
        TemplatePreviewResponse response = resumeTemplatesService.previewTemplate(templateId, requestWithDefaults, userId);

        // Then
        assertNotNull(response);
        assertEquals("image", response.getPreviewType());
        assertEquals(800, response.getDimensions().getWidth()); // 默认宽度
        assertTrue(response.getDimensions().getHeight() > 0);
    }

    /**
     * 测试HTML格式预览
     */
    @Test
    @DisplayName("测试HTML格式预览")
    void testPreviewTemplate_HtmlFormat() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;
        previewRequest.setFormat("html");

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // When
        TemplatePreviewResponse response = resumeTemplatesService.previewTemplate(templateId, previewRequest, userId);

        // Then
        assertNotNull(response);
        assertEquals("html", response.getPreviewType());
        assertTrue(response.getPreviewUrl().contains(".html"));
    }

    /**
     * 测试不同质量设置
     */
    @Test
    @DisplayName("测试不同质量设置对文件大小的影响")
    void testPreviewTemplate_QualitySettings() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // 测试低质量
        previewRequest.setQuality("low");
        TemplatePreviewResponse lowQualityResponse = resumeTemplatesService.previewTemplate(templateId, previewRequest, userId);

        // 测试高质量
        previewRequest.setQuality("high");
        TemplatePreviewResponse highQualityResponse = resumeTemplatesService.previewTemplate(templateId, previewRequest, userId);

        // Then
        assertNotNull(lowQualityResponse);
        assertNotNull(highQualityResponse);
        assertTrue(highQualityResponse.getFileSize() > lowQualityResponse.getFileSize());
    }

    /**
     * 测试复杂简历数据的处理
     */
    @Test
    @DisplayName("测试复杂简历数据的高度计算")
    void testPreviewTemplateWithData_ComplexData() {
        // Given
        Long templateId = 3001L;
        Long userId = 1001L;

        // 创建复杂的简历数据
        Map<String, Object> complexResumeData = Map.of(
                "basicInfo", Map.of("realName", "李四"),
                "workExperiences", List.of(
                        Map.of("company", "公司1"),
                        Map.of("company", "公司2"),
                        Map.of("company", "公司3")
                ),
                "educations", List.of(
                        Map.of("school", "学校1"),
                        Map.of("school", "学校2")
                ),
                "projects", List.of(
                        Map.of("name", "项目1"),
                        Map.of("name", "项目2"),
                        Map.of("name", "项目3"),
                        Map.of("name", "项目4")
                )
        );

        previewWithDataRequest.setResumeData(complexResumeData);

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // When
        TemplatePreviewResponse response = resumeTemplatesService.previewTemplateWithData(templateId, previewWithDataRequest, userId);

        // Then
        assertNotNull(response);
        // 复杂数据应该产生更高的预览图
        assertTrue(response.getDimensions().getHeight() > 1697); // 基础高度 + 额外高度
    }

    /**
     * 测试用户权限 - 匿名用户
     */
    @Test
    @DisplayName("测试匿名用户预览")
    void testPreviewTemplate_AnonymousUser() {
        // Given
        Long templateId = 3001L;
        Long userId = null; // 匿名用户

        // Mock模板查询
        doReturn(sampleTemplate).when(resumeTemplatesService).getById(templateId);

        // When
        TemplatePreviewResponse response = resumeTemplatesService.previewTemplate(templateId, previewRequest, userId);

        // Then
        assertNotNull(response);
        assertEquals(templateId, response.getTemplateId());
        assertEquals("image", response.getPreviewType());
    }
} 