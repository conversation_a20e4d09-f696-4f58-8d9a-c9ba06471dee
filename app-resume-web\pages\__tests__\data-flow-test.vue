<template>
  <div class="data-flow-test">
    <div class="test-header">
      <h1>数据流测试页面</h1>
      <p>测试简历编辑器的实时预览功能</p>
    </div>
    
    <div class="test-content">
      <div class="test-section">
        <h2>1. 编辑器Store状态</h2>
        <div class="debug-info">
          <p><strong>当前简历数据:</strong></p>
          <pre>{{ JSON.stringify(editorStore.currentResumeData, null, 2) }}</pre>
          
          <p><strong>当前模块ID:</strong> {{ editorStore.currentModuleId }}</p>
          <p><strong>模块配置:</strong></p>
          <pre>{{ JSON.stringify(editorStore.moduleConfigs?.slice(0, 3), null, 2) }}</pre>
        </div>
      </div>
      
      <div class="test-section">
        <h2>2. 基本信息表单测试</h2>
        <div class="form-test">
          <label>姓名:</label>
          <input 
            v-model="testName" 
            @input="updateBasicInfo"
            placeholder="输入姓名测试"
          />
          
          <label>职位:</label>
          <input 
            v-model="testTitle" 
            @input="updateBasicInfo"
            placeholder="输入职位测试"
          />
          
          <button @click="manualUpdate">手动更新数据</button>
        </div>
      </div>
      
      <div class="test-section">
        <h2>3. 预览组件测试</h2>
        <div class="preview-test">
          <ResumePreview
            :resume-data="editorStore.currentResumeData"
            :settings="testSettings"
            @module-order-change="handleModuleOrderChange"
            @module-delete="handleModuleDelete"
          />
        </div>
      </div>
      
      <div class="test-section">
        <h2>4. 数据流日志</h2>
        <div class="log-section">
          <button @click="clearLogs">清除日志</button>
          <div class="logs">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
              <pre v-if="log.data" class="log-data">{{ JSON.stringify(log.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'
import ResumePreview from '~/components/editor/preview/ResumePreview.vue'

// 页面元数据
definePageMeta({
  layout: 'default',
  title: '数据流测试'
})

// 响应式数据
const editorStore = useEditorStore()
const testName = ref('')
const testTitle = ref('')
const logs = ref([])

const testSettings = reactive({
  fontFamily: 'system-ui',
  fontSize: '14px',
  textColor: '#333333',
  margins: {
    top: 60,
    bottom: 60,
    side: 60
  },
  spacings: {
    module: 24,
    modulePadding: 16,
    item: 16,
    itemPadding: 12,
    line: 1.6,
    paragraph: 12
  }
})

// 添加日志
const addLog = (message, data = null) => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    data
  })
  console.log('📝 测试日志:', message, data)
}

// 清除日志
const clearLogs = () => {
  logs.value = []
}

// 更新基本信息
const updateBasicInfo = () => {
  const basicInfo = {
    name: testName.value,
    title: testTitle.value,
    phone: '13800138000',
    email: '<EMAIL>',
    address: '北京市朝阳区',
    website: '',
    linkedin: '',
    github: '',
    photo: ''
  }
  
  addLog('更新基本信息', basicInfo)
  editorStore.updateModuleData('basic_info', basicInfo)
}

// 手动更新数据
const manualUpdate = () => {
  addLog('手动触发数据更新')
  updateBasicInfo()
}

// 处理模块顺序变更
const handleModuleOrderChange = (data) => {
  addLog('模块顺序变更', data)
}

// 处理模块删除
const handleModuleDelete = (moduleId) => {
  addLog('模块删除', { moduleId })
}

// 监听store数据变化
watch(() => editorStore.currentResumeData, (newData, oldData) => {
  addLog('Store数据变化', {
    hasModules: Object.keys(newData?.modules || {}).length,
    basicInfo: newData?.modules?.basic_info
  })
}, { deep: true })

// 监听当前模块变化
watch(() => editorStore.currentModuleId, (newId, oldId) => {
  addLog('当前模块变化', { from: oldId, to: newId })
})

// 初始化
onMounted(async () => {
  addLog('页面初始化开始')
  
  try {
    // 创建新简历
    await editorStore.createNewResume()
    addLog('新简历创建成功')
    
    // 设置当前模块为基本信息
    editorStore.setCurrentModule('basic_info')
    addLog('设置当前模块为基本信息')
    
    // 初始化测试数据
    testName.value = '张三'
    testTitle.value = '前端开发工程师'
    updateBasicInfo()
    
  } catch (error) {
    addLog('初始化失败', error)
  }
})
</script>

<style scoped>
.data-flow-test {
  @apply p-6 max-w-full mx-auto;
}

.test-header {
  @apply mb-8 text-center;
}

.test-header h1 {
  @apply text-3xl font-bold text-gray-900 mb-2;
}

.test-header p {
  @apply text-gray-600;
}

.test-content {
  @apply space-y-8;
}

.test-section {
  @apply border border-gray-200 rounded-lg p-6;
}

.test-section h2 {
  @apply text-xl font-semibold text-gray-900 mb-4;
}

.debug-info {
  @apply space-y-4;
}

.debug-info p {
  @apply font-medium text-gray-700;
}

.debug-info pre {
  @apply bg-gray-100 p-4 rounded text-sm overflow-x-auto;
}

.form-test {
  @apply space-y-4;
}

.form-test label {
  @apply block text-sm font-medium text-gray-700;
}

.form-test input {
  @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-test button {
  @apply bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors;
}

.preview-test {
  @apply border border-gray-300 rounded-lg p-4 max-h-96 overflow-y-auto;
}

.log-section {
  @apply space-y-4;
}

.log-section button {
  @apply bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors;
}

.logs {
  @apply bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto;
}

.log-item {
  @apply mb-4 pb-4 border-b border-gray-200 last:border-b-0;
}

.log-time {
  @apply text-xs text-gray-500 mr-2;
}

.log-message {
  @apply text-sm font-medium text-gray-700;
}

.log-data {
  @apply mt-2 bg-white p-2 rounded text-xs overflow-x-auto;
}
</style> 