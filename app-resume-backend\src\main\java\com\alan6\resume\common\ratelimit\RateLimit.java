package com.alan6.resume.common.ratelimit;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 限流注解
 * 
 * 主要功能：
 * 1. 标记需要限流的方法
 * 2. 配置限流参数（速率、时间窗口等）
 * 3. 支持自定义限流键和错误消息
 * 
 * 使用场景：
 * - API接口限流
 * - 用户行为限流
 * - 系统资源保护
 * 
 * 示例用法：
 * @RateLimit(key = "sms_code:#phone", rate = 1, rateInterval = 60, 
 *           message = "验证码发送过于频繁，请1分钟后重试")
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimit {

    /**
     * 限流键模式
     * 
     * 支持SpEL表达式和占位符：
     * - 固定字符串：如 "login_attempt"
     * - 包含参数：如 "sms_code:#phone"
     * - 复合键：如 "login:#account:#platform"
     * 
     * @return 限流键模式
     */
    String key();

    /**
     * 允许的请求速率（每个时间窗口内的请求数）
     * 
     * @return 请求速率，默认为10
     */
    long rate() default 10L;

    /**
     * 时间窗口长度（秒）
     * 
     * @return 时间窗口，默认为60秒
     */
    long rateInterval() default 60L;

    /**
     * 限流类型
     * 
     * 用于区分不同的限流场景：
     * - USER：用户级别限流
     * - IP：IP级别限流
     * - API：接口级别限流
     * - GLOBAL：全局限流
     * 
     * @return 限流类型，默认为USER
     */
    String limitType() default "USER";

    /**
     * 触发限流时的错误消息
     * 
     * @return 错误消息，默认为通用提示
     */
    String message() default "请求过于频繁，请稍后重试";

    /**
     * 是否启用限流
     * 
     * 可以通过配置动态控制是否启用限流功能
     * 
     * @return 是否启用，默认为true
     */
    boolean enabled() default true;

    /**
     * 限流算法类型
     * 
     * 支持的算法类型：
     * - TOKEN_BUCKET：令牌桶算法
     * - SLIDING_WINDOW：滑动窗口算法
     * - FIXED_WINDOW：固定窗口算法
     * 
     * @return 算法类型，默认为TOKEN_BUCKET
     */
    String algorithm() default "TOKEN_BUCKET";
} 