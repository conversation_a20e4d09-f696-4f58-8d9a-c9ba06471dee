package com.alan6.resume.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 系统状态响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "SystemStatusResponse", description = "系统状态响应")
public class SystemStatusResponse {

    /**
     * 系统信息
     */
    @Schema(description = "系统信息")
    private SystemInfo system;

    /**
     * 应用信息
     */
    @Schema(description = "应用信息")
    private ApplicationInfo application;

    /**
     * 数据库信息
     */
    @Schema(description = "数据库信息")
    private DatabaseInfo database;

    /**
     * Redis信息
     */
    @Schema(description = "Redis信息")
    private RedisInfo redis;

    /**
     * MinIO信息
     */
    @Schema(description = "MinIO信息")
    private MinioInfo minio;

    /**
     * 检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检查时间", example = "2024-12-22 17:00:00")
    private LocalDateTime checkTime;

    /**
     * 系统信息内部类
     */
    @Data
    @Schema(name = "SystemInfo", description = "系统信息")
    public static class SystemInfo {
        @Schema(description = "操作系统", example = "Linux")
        private String osName;
        
        @Schema(description = "系统架构", example = "amd64")
        private String osArch;
        
        @Schema(description = "Java版本", example = "17.0.1")
        private String javaVersion;
        
        @Schema(description = "CPU核心数", example = "8")
        private Integer cpuCores;
        
        @Schema(description = "总内存(MB)", example = "8192")
        private Long totalMemory;
        
        @Schema(description = "可用内存(MB)", example = "4096")
        private Long availableMemory;
        
        @Schema(description = "已用内存(MB)", example = "4096")
        private Long usedMemory;
        
        @Schema(description = "内存使用率(%)", example = "50.0")
        private Double memoryUsage;
    }

    /**
     * 应用信息内部类
     */
    @Data
    @Schema(name = "ApplicationInfo", description = "应用信息")
    public static class ApplicationInfo {
        @Schema(description = "应用名称", example = "app-resume-backend")
        private String name;
        
        @Schema(description = "应用版本", example = "1.0.0")
        private String version;
        
        @Schema(description = "启动时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;
        
        @Schema(description = "运行时长(秒)", example = "86400")
        private Long uptime;
        
        @Schema(description = "运行环境", example = "prod")
        private String profile;
    }

    /**
     * 数据库信息内部类
     */
    @Data
    @Schema(name = "DatabaseInfo", description = "数据库信息")
    public static class DatabaseInfo {
        @Schema(description = "数据库类型", example = "MySQL")
        private String type;
        
        @Schema(description = "数据库版本", example = "8.0.33")
        private String version;
        
        @Schema(description = "连接状态", example = "UP")
        private String status;
        
        @Schema(description = "活动连接数", example = "5")
        private Integer activeConnections;
        
        @Schema(description = "最大连接数", example = "20")
        private Integer maxConnections;
        
        @Schema(description = "响应时间(ms)", example = "10")
        private Long responseTime;
    }

    /**
     * Redis信息内部类
     */
    @Data
    @Schema(name = "RedisInfo", description = "Redis信息")
    public static class RedisInfo {
        @Schema(description = "Redis版本", example = "7.0.5")
        private String version;
        
        @Schema(description = "连接状态", example = "UP")
        private String status;
        
        @Schema(description = "已用内存(MB)", example = "128")
        private Long usedMemory;
        
        @Schema(description = "键总数", example = "1500")
        private Long keyCount;
        
        @Schema(description = "响应时间(ms)", example = "2")
        private Long responseTime;
    }

    /**
     * MinIO信息内部类
     */
    @Data
    @Schema(name = "MinioInfo", description = "MinIO信息")
    public static class MinioInfo {
        @Schema(description = "连接状态", example = "UP")
        private String status;
        
        @Schema(description = "存储桶数量", example = "4")
        private Integer bucketCount;
        
        @Schema(description = "对象总数", example = "2500")
        private Long objectCount;
        
        @Schema(description = "总存储大小(MB)", example = "10240")
        private Long totalSize;
        
        @Schema(description = "响应时间(ms)", example = "25")
        private Long responseTime;
    }
} 