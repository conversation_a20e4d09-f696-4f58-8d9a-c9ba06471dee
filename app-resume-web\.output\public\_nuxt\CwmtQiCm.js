var C=t=>{throw TypeError(t)};var B=(t,n,r)=>n.has(t)||C("Cannot "+r);var h=(t,n,r)=>(B(t,n,"read from private field"),r?r.call(t):n.get(t)),T=(t,n,r)=>n.has(t)?C("Cannot add the same private member more than once"):n instanceof WeakSet?n.add(t):n.set(t,r);import{a9 as P,r as k,aa as j,A as N,ab as U,ac as F,D as J,k as I}from"./CURHyiUL.js";function z(t){return typeof t=="string"?`'${t}'`:new q().serialize(t)}const q=function(){var n;class t{constructor(){T(this,n,new Map)}compare(e,i){const o=typeof e,a=typeof i;return o==="string"&&a==="string"?e.localeCompare(i):o==="number"&&a==="number"?e-i:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(i,!0))}serialize(e,i){if(e===null)return"null";switch(typeof e){case"string":return i?e:`'${e}'`;case"bigint":return`${e}n`;case"object":return this.$object(e);case"function":return this.$function(e)}return String(e)}serializeObject(e){const i=Object.prototype.toString.call(e);if(i!=="[object Object]")return this.serializeBuiltInType(i.length<10?`unknown:${i}`:i.slice(8,-1),e);const o=e.constructor,a=o===Object||o===void 0?"":o.name;if(a!==""&&globalThis[a]===o)return this.serializeBuiltInType(a,e);if(typeof e.toJSON=="function"){const s=e.toJSON();return a+(s!==null&&typeof s=="object"?this.$object(s):`(${this.serialize(s)})`)}return this.serializeObjectEntries(a,Object.entries(e))}serializeBuiltInType(e,i){const o=this["$"+e];if(o)return o.call(this,i);if(typeof(i==null?void 0:i.entries)=="function")return this.serializeObjectEntries(e,i.entries());throw new Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,i){const o=Array.from(i).sort((s,c)=>this.compare(s[0],c[0]));let a=`${e}{`;for(let s=0;s<o.length;s++){const[c,u]=o[s];a+=`${this.serialize(c,!0)}:${this.serialize(u)}`,s<o.length-1&&(a+=",")}return a+"}"}$object(e){let i=h(this,n).get(e);return i===void 0&&(h(this,n).set(e,`#${h(this,n).size}`),i=this.serializeObject(e),h(this,n).set(e,i)),i}$function(e){const i=Function.prototype.toString.call(e);return i.slice(-15)==="[native code] }"?`${e.name||""}()[native]`:`${e.name}(${e.length})${i.replace(/\s*\n\s*/g,"")}`}$Array(e){let i="[";for(let o=0;o<e.length;o++)i+=this.serialize(e[o]),o<e.length-1&&(i+=",");return i+"]"}$Date(e){try{return`Date(${e.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(",")}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort((i,o)=>this.compare(i,o)))}`}$Map(e){return this.serializeObjectEntries("Map",e.entries())}}n=new WeakMap;for(const r of["Error","RegExp","URL"])t.prototype["$"+r]=function(e){return`${r}(${e})`};for(const r of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])t.prototype["$"+r]=function(e){return`${r}[${e.join(",")}]`};for(const r of["BigInt64Array","BigUint64Array"])t.prototype["$"+r]=function(e){return`${r}[${e.join("n,")}${e.length>0?"n":""}]`};return t}();function H(t,n){return t===n||z(t)===z(n)}function V(t,n){if(typeof t!="string")throw new TypeError("argument str must be a string");const r={},e=n||{},i=e.decode||X;let o=0;for(;o<t.length;){const a=t.indexOf("=",o);if(a===-1)break;let s=t.indexOf(";",o);if(s===-1)s=t.length;else if(s<a){o=t.lastIndexOf(";",a-1)+1;continue}const c=t.slice(o,a).trim();if(e!=null&&e.filter&&!(e!=null&&e.filter(c))){o=s+1;continue}if(r[c]===void 0){let u=t.slice(a+1,s).trim();u.codePointAt(0)===34&&(u=u.slice(1,-1)),r[c]=Y(u,i)}o=s+1}return r}function X(t){return t.includes("%")?decodeURIComponent(t):t}function Y(t,n){try{return n(t)}catch{return t}}const S=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function L(t,n,r){const e=r||{},i=e.encode||encodeURIComponent;if(typeof i!="function")throw new TypeError("option encode is invalid");if(!S.test(t))throw new TypeError("argument name is invalid");const o=i(n);if(o&&!S.test(o))throw new TypeError("argument val is invalid");let a=t+"="+o;if(e.maxAge!==void 0&&e.maxAge!==null){const s=e.maxAge-0;if(Number.isNaN(s)||!Number.isFinite(s))throw new TypeError("option maxAge is invalid");a+="; Max-Age="+Math.floor(s)}if(e.domain){if(!S.test(e.domain))throw new TypeError("option domain is invalid");a+="; Domain="+e.domain}if(e.path){if(!S.test(e.path))throw new TypeError("option path is invalid");a+="; Path="+e.path}if(e.expires){if(!G(e.expires)||Number.isNaN(e.expires.valueOf()))throw new TypeError("option expires is invalid");a+="; Expires="+e.expires.toUTCString()}if(e.httpOnly&&(a+="; HttpOnly"),e.secure&&(a+="; Secure"),e.priority)switch(typeof e.priority=="string"?e.priority.toLowerCase():e.priority){case"low":{a+="; Priority=Low";break}case"medium":{a+="; Priority=Medium";break}case"high":{a+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(e.sameSite)switch(typeof e.sameSite=="string"?e.sameSite.toLowerCase():e.sameSite){case!0:{a+="; SameSite=Strict";break}case"lax":{a+="; SameSite=Lax";break}case"strict":{a+="; SameSite=Strict";break}case"none":{a+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return e.partitioned&&(a+="; Partitioned"),a}function G(t){return Object.prototype.toString.call(t)==="[object Date]"||t instanceof Date}function f(t){if(typeof t!="object")return t;var n,r,e=Object.prototype.toString.call(t);if(e==="[object Object]"){if(t.constructor!==Object&&typeof t.constructor=="function"){r=new t.constructor;for(n in t)t.hasOwnProperty(n)&&r[n]!==t[n]&&(r[n]=f(t[n]))}else{r={};for(n in t)n==="__proto__"?Object.defineProperty(r,n,{value:f(t[n]),configurable:!0,enumerable:!0,writable:!0}):r[n]=f(t[n])}return r}if(e==="[object Array]"){for(n=t.length,r=Array(n);n--;)r[n]=f(t[n]);return r}return e==="[object Set]"?(r=new Set,t.forEach(function(i){r.add(f(i))}),r):e==="[object Map]"?(r=new Map,t.forEach(function(i,o){r.set(f(o),f(i))}),r):e==="[object Date]"?new Date(+t):e==="[object RegExp]"?(r=new RegExp(t.source,t.flags),r.lastIndex=t.lastIndex,r):e==="[object DataView]"?new t.constructor(f(t.buffer)):e==="[object ArrayBuffer]"?t.slice(0):e.slice(-6)==="Array]"?new t.constructor(t):t}const K={path:"/",watch:!0,decode:t=>P(decodeURIComponent(t)),encode:t=>encodeURIComponent(typeof t=="string"?t:JSON.stringify(t))},b=window.cookieStore;function R(t,n){var u;const r={...K,...n};r.filter??(r.filter=l=>l===t);const e=D(r)||{};let i;r.maxAge!==void 0?i=r.maxAge*1e3:r.expires&&(i=r.expires.getTime()-Date.now());const o=i!==void 0&&i<=0,a=o||e[t]===void 0||e[t]===null,s=f(o?void 0:e[t]??((u=r.default)==null?void 0:u.call(r))),c=i&&!o?W(s,i,r.watch&&r.watch!=="shallow"):k(s);{let l=null;try{!b&&typeof BroadcastChannel<"u"&&(l=new BroadcastChannel(`nuxt:cookies:${t}`))}catch{}const p=(d=!1)=>{!d&&(r.readonly||H(c.value,e[t]))||(Z(t,c.value,r),e[t]=f(c.value),l==null||l.postMessage({value:r.encode(c.value)}))},$=d=>{var g;const m=d.refresh?(g=D(r))==null?void 0:g[t]:r.decode(d.value);w=!0,c.value=m,e[t]=f(m),J(()=>{w=!1})};let w=!1;const O=!!U();if(O&&j(()=>{w=!0,p(),l==null||l.close()}),b){const d=m=>{const g=m.changed.find(E=>E.name===t),x=m.deleted.find(E=>E.name===t);g&&$({value:g.value}),x&&$({value:null})};b.addEventListener("change",d),O&&j(()=>b.removeEventListener("change",d))}else l&&(l.onmessage=({data:d})=>$(d));r.watch&&N(c,()=>{w||p()},{deep:r.watch!=="shallow"}),a&&p(a)}return c}function D(t={}){return V(document.cookie,t)}function Q(t,n,r={}){return n==null?L(t,n,{...r,maxAge:-1}):L(t,n,r)}function Z(t,n,r={}){document.cookie=Q(t,n,r)}const _=2147483647;function W(t,n,r){let e,i,o=0;const a=r?k(t):{value:t};return U()&&j(()=>{i==null||i(),clearTimeout(e)}),F((s,c)=>{r&&(i=N(a,c));function u(){o=0,clearTimeout(e);const l=n-o,p=l<_?l:_;e=setTimeout(()=>{if(o+=p,o<n)return u();a.value=void 0,c()},p)}return{get(){return s(),a.value},set(l){u(),a.value=l,c()}}})}const v=k(!1),y=k(null);class A{static async login(n){try{v.value=!0,y.value=null,console.log("🔒 开始管理员登录...");const r={phone:n.phone,password:n.password,platform:n.platform||"web"};console.log("🔒 管理员登录请求参数:",r);const e=await $fetch("/api/auth/login-admin",{method:"POST",body:r,headers:{"Content-Type":"application/json",Accept:"application/json"}});if(console.log("🔒 管理员登录响应:",e),e&&e.code===200&&e.data&&e.data.token){localStorage.setItem("admin_token",e.data.token),localStorage.setItem("admin_info",JSON.stringify(e.data)),console.log("💾 管理员信息已存储到localStorage");const i=R("admin_token",{default:()=>"",maxAge:60*60*24*7,httpOnly:!1,secure:!0,sameSite:"lax"});return i.value=e.data.token,{success:!0,data:e.data,message:e.msg||"管理员登录成功"}}else throw new Error((e==null?void 0:e.msg)||(e==null?void 0:e.message)||"管理员登录失败")}catch(r){const e=this.handleLoginError(r);return y.value=e,console.error("❌ 管理员登录失败:",e),{success:!1,error:e,message:e}}finally{v.value=!1}}static handleLoginError(n){const r=n.message||"管理员登录失败";return r.includes("用户不存在")?"账号不存在，请检查手机号":r.includes("密码错误")?"密码错误，请重新输入":r.includes("权限不足")?"权限不足，您不是管理员用户":r.includes("账户已被禁用")?"账户已被禁用，请联系系统管理员":r.includes("登录失败次数过多")?"登录失败次数过多，请稍后再试":r.includes("参数验证失败")?"输入信息有误，请检查后重试":r}static clearAdminAuth(){localStorage.removeItem("admin_token"),localStorage.removeItem("admin_info"),console.log("🗑️ 管理员认证信息已清除");const n=R("admin_token");n.value=null}static isAdminLoggedIn(){return!!localStorage.getItem("admin_token")}static getAdminInfo(){{const n=localStorage.getItem("admin_info");if(n)try{return JSON.parse(n)}catch(r){return console.error("解析管理员信息失败:",r),null}}return null}}const re=()=>{const t=I(()=>v.value),n=I(()=>!!y.value),r=async s=>await A.login(s),e=()=>{y.value=null},i=()=>{A.clearAdminAuth()},o=()=>A.isAdminLoggedIn(),a=()=>A.getAdminInfo();return{loading:M(v),error:M(y),isLoading:t,hasError:n,adminLogin:r,clearError:e,clearAdminAuth:i,isAdminLoggedIn:o,getAdminInfo:a}};function M(t){return I(()=>t.value)}export{re as u};
