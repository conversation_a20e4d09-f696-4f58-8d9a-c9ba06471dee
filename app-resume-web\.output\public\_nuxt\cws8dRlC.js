import{e as n,i as r,n as s,c as a,a as e,o as i}from"./CURHyiUL.js";const l={class:"pt-16 min-h-screen bg-gradient-to-br from-primary-50 to-orange-50 flex items-center justify-center"},u={__name:"login",setup(d){n({title:"登录 - 火花简历",description:"登录火花简历，开始制作您的专业简历",ogTitle:"登录 - 火花简历",ogDescription:"登录火花简历，开始制作您的专业简历"}),r(()=>{setTimeout(()=>{s("/?showLogin=true")},1500)});const o=()=>{s("/?showLogin=true")};return(c,t)=>(i(),a("div",l,[e("div",{class:"max-w-md w-full space-y-8 p-8 text-center"},[e("div",{class:"space-y-4"},[t[0]||(t[0]=e("div",{class:"w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto"},[e("div",{class:"w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"})],-1)),t[1]||(t[1]=e("h2",{class:"text-2xl font-display font-bold text-secondary-900"},"正在跳转到登录...",-1)),t[2]||(t[2]=e("p",{class:"text-secondary-600"},"如果没有自动跳转，请点击下方按钮",-1)),e("button",{onClick:o,class:"btn-primary"}," 打开登录 ")])])]))}};export{u as default};
