import{_ as u}from"./DlAUqK2U.js";import{c,a as d,f as r,b as l,d as n,$ as q,o as m}from"./CURHyiUL.js";const p={class:"html-specification"},g={class:"page-header"},h={class:"header-content"},f={class:"page-title"},x={class:"header-actions"},y={class:"table-of-contents"},k={class:"toc-list"},D={__name:"html-specification",setup(_){const e=o=>{const t=document.getElementById(o);t&&t.scrollIntoView({behavior:"smooth"})},s=()=>{const o=document.querySelector(".full-example pre code");if(!o){console.error("无法找到完整示例模板");return}let t=o.textContent||o.innerText;t=t.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#x27;/g,"'");const i=new Blob([t],{type:"text/html; charset=utf-8"}),a=URL.createObjectURL(i),v=document.createElement("a");v.href=a,v.download="resume-base-template.html",v.click(),URL.revokeObjectURL(a),console.log("完整基础模板已下载：resume-base-template.html")},b=async()=>{try{await navigator.clipboard.writeText(window.location.href),console.log("链接已复制到剪贴板")}catch(o){console.error("复制失败:",o)}};return(o,t)=>{const i=q("Icon");return m(),c("div",p,[d("div",g,[d("div",h,[d("h1",f,[l(i,{name:"document",class:"title-icon"}),t[9]||(t[9]=n(" HTML简历模板规范要求 "))]),t[10]||(t[10]=d("p",{class:"page-description"}," 详细的HTML简历模板开发规范，确保转换为Vue组件时的完美兼容性 ",-1))]),d("div",x,[d("button",{onClick:s,class:"btn btn-primary"},[l(i,{name:"download"}),t[11]||(t[11]=n(" 下载示例模板 "))]),d("button",{onClick:b,class:"btn btn-outline"},[l(i,{name:"copy"}),t[12]||(t[12]=n(" 复制规范链接 "))])])]),d("div",y,[t[13]||(t[13]=d("h3",null,"目录",-1)),d("ul",k,[d("li",null,[d("a",{href:"#basic-structure",onClick:t[0]||(t[0]=a=>e("basic-structure"))},"1. 基本文档结构")]),d("li",null,[d("a",{href:"#module-definition",onClick:t[1]||(t[1]=a=>e("module-definition"))},"2. 模块定义规范")]),d("li",null,[d("a",{href:"#field-binding",onClick:t[2]||(t[2]=a=>e("field-binding"))},"3. 字段绑定规范")]),d("li",null,[d("a",{href:"#css-variables",onClick:t[3]||(t[3]=a=>e("css-variables"))},"4. CSS变量使用规范")]),d("li",null,[d("a",{href:"#data-attributes",onClick:t[4]||(t[4]=a=>e("data-attributes"))},"5. 数据属性详解")]),d("li",null,[d("a",{href:"#module-specifications",onClick:t[5]||(t[5]=a=>e("module-specifications"))},"6. 各模块字段规范")]),d("li",null,[d("a",{href:"#style-requirements",onClick:t[6]||(t[6]=a=>e("style-requirements"))},"7. 样式要求")]),d("li",null,[d("a",{href:"#examples",onClick:t[7]||(t[7]=a=>e("examples"))},"8. 完整示例")]),d("li",null,[d("a",{href:"#validation",onClick:t[8]||(t[8]=a=>e("validation"))},"9. 验证清单")])])]),t[14]||(t[14]=r(`<div class="specification-content" data-v-0e4d416b><section id="basic-structure" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>1. 基本文档结构</h2><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>必需的HTML结构</h3><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;zh-CN&quot;&gt;
&lt;head&gt;
    &lt;meta charset=&quot;UTF-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;
    &lt;title&gt;简历标题&lt;/title&gt;
    &lt;style&gt;
        /* CSS样式 */
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class=&quot;resume-template&quot; data-module-root=&quot;true&quot;&gt;
        &lt;!-- 简历内容 --&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre></div><div class="requirements-list" data-v-0e4d416b><h4 data-v-0e4d416b>关键要求：</h4><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>DOCTYPE声明</strong>：必须使用 <code data-v-0e4d416b>&lt;!DOCTYPE html&gt;</code></li><li data-v-0e4d416b><strong data-v-0e4d416b>字符编码</strong>：必须设置为 <code data-v-0e4d416b>UTF-8</code></li><li data-v-0e4d416b><strong data-v-0e4d416b>根容器</strong>：必须包含 <code data-v-0e4d416b>class=&quot;resume-template&quot;</code> 和 <code data-v-0e4d416b>data-module-root=&quot;true&quot;</code></li><li data-v-0e4d416b><strong data-v-0e4d416b>样式标签</strong>：CSS样式必须包含在 <code data-v-0e4d416b>&lt;style&gt;</code> 标签内</li></ul></div></div></section><section id="module-definition" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>2. 模块定义规范</h2><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>模块标识</h3><p data-v-0e4d416b>每个简历模块必须使用 <code data-v-0e4d416b>data-module</code> 属性进行标识：</p><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;section data-module=&quot;basic_info&quot;&gt;
    &lt;!-- 基本信息内容 --&gt;
&lt;/section&gt;

&lt;section data-module=&quot;work_experience&quot;&gt;
    &lt;!-- 工作经历内容 --&gt;
&lt;/section&gt;</code></pre></div><div class="module-table" data-v-0e4d416b><h4 data-v-0e4d416b>支持的模块名称（共18个）：</h4><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>模块名称</th><th data-v-0e4d416b>data-module值</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>基本信息</td><td data-v-0e4d416b><code data-v-0e4d416b>basic_info</code></td><td data-v-0e4d416b>姓名、联系方式、求职意向等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>工作经历</td><td data-v-0e4d416b><code data-v-0e4d416b>work_experience</code></td><td data-v-0e4d416b>工作履历、职位、公司等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>教育经历</td><td data-v-0e4d416b><code data-v-0e4d416b>education</code></td><td data-v-0e4d416b>学历、学校、专业等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>项目经历</td><td data-v-0e4d416b><code data-v-0e4d416b>project</code></td><td data-v-0e4d416b>项目名称、角色、描述等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>技能特长</td><td data-v-0e4d416b><code data-v-0e4d416b>skills</code></td><td data-v-0e4d416b>技能名称、熟练程度等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>获奖情况</td><td data-v-0e4d416b><code data-v-0e4d416b>award</code></td><td data-v-0e4d416b>奖项名称、获奖时间等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>证书资质</td><td data-v-0e4d416b><code data-v-0e4d416b>certificate</code></td><td data-v-0e4d416b>证书名称、颁发机构等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>语言能力</td><td data-v-0e4d416b><code data-v-0e4d416b>language</code></td><td data-v-0e4d416b>语言种类、熟练程度等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>实习经历</td><td data-v-0e4d416b><code data-v-0e4d416b>internship</code></td><td data-v-0e4d416b>实习公司、职位、时间等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>志愿经历</td><td data-v-0e4d416b><code data-v-0e4d416b>volunteer_experience</code></td><td data-v-0e4d416b>志愿组织、角色、时间等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>论文发表</td><td data-v-0e4d416b><code data-v-0e4d416b>publication</code></td><td data-v-0e4d416b>论文标题、期刊、时间等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>科研经历</td><td data-v-0e4d416b><code data-v-0e4d416b>research_experience</code></td><td data-v-0e4d416b>研究课题、机构、时间等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>培训经历</td><td data-v-0e4d416b><code data-v-0e4d416b>training</code></td><td data-v-0e4d416b>培训课程、机构、时间等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>作品集</td><td data-v-0e4d416b><code data-v-0e4d416b>portfolio</code></td><td data-v-0e4d416b>作品标题、类型、链接等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>兴趣爱好</td><td data-v-0e4d416b><code data-v-0e4d416b>hobbies</code></td><td data-v-0e4d416b>爱好名称、描述等</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自我评价</td><td data-v-0e4d416b><code data-v-0e4d416b>self_evaluation</code></td><td data-v-0e4d416b>自我评价内容</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自荐信</td><td data-v-0e4d416b><code data-v-0e4d416b>cover_letter</code></td><td data-v-0e4d416b>自荐信内容</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自定义模块</td><td data-v-0e4d416b><code data-v-0e4d416b>custom_*</code></td><td data-v-0e4d416b>用户创建的个性化模块</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div></section><section id="field-binding" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>3. 字段绑定规范</h2><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>数据绑定语法</h3><p data-v-0e4d416b>使用 <code data-v-0e4d416b>data-vue-text</code> 属性进行数据绑定：</p><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;!-- 基本信息字段 --&gt;
&lt;span data-field=&quot;name&quot; data-vue-text=&quot;resumeData.basic_info.name&quot;&gt;默认值&lt;/span&gt;

&lt;!-- 循环列表项 --&gt;
&lt;div data-vue-for=&quot;item in resumeData.work_experience&quot; data-vue-key=&quot;item.id&quot;&gt;
    &lt;span data-field=&quot;company&quot; data-vue-text=&quot;item.company&quot;&gt;公司名称&lt;/span&gt;
&lt;/div&gt;</code></pre></div><div class="binding-rules" data-v-0e4d416b><h4 data-v-0e4d416b>绑定规则：</h4><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>data-field</strong>：字段名称标识，用于系统识别</li><li data-v-0e4d416b><strong data-v-0e4d416b>data-vue-text</strong>：Vue数据绑定表达式</li><li data-v-0e4d416b><strong data-v-0e4d416b>data-vue-for</strong>：循环渲染指令</li><li data-v-0e4d416b><strong data-v-0e4d416b>data-vue-key</strong>：循环项唯一标识</li><li data-v-0e4d416b><strong data-v-0e4d416b>data-vue-if</strong>：条件渲染指令（用于控制模块显示/隐藏）</li></ul></div><div class="conditional-rendering" data-v-0e4d416b><h4 data-v-0e4d416b>条件渲染规范：</h4><p data-v-0e4d416b>为了优化用户体验，避免空模块和空字段显示，建议为所有可选模块和字段添加条件渲染：</p><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;!-- 核心模块（始终显示） --&gt;
&lt;section data-module=&quot;basic_info&quot;&gt;
    &lt;!-- 核心字段（始终显示） --&gt;
    &lt;h1 data-field=&quot;name&quot; data-vue-text=&quot;resumeData.basic_info.name&quot;&gt;张三&lt;/h1&gt;
    &lt;div data-field=&quot;phone&quot; data-vue-text=&quot;resumeData.basic_info.phone&quot;&gt;13800138000&lt;/div&gt;
    
    &lt;!-- 可选字段（有数据才显示） --&gt;
    &lt;div data-field=&quot;age&quot; data-vue-text=&quot;resumeData.basic_info.age&quot; 
         data-vue-if=&quot;resumeData.basic_info.age&quot;&gt;25岁&lt;/div&gt;
    &lt;div data-field=&quot;github&quot; data-vue-text=&quot;resumeData.basic_info.github&quot;
         data-vue-if=&quot;resumeData.basic_info.github&quot;&gt;github.com/user&lt;/div&gt;
&lt;/section&gt;

&lt;!-- 可选模块（有数据才显示） --&gt;
&lt;section data-module=&quot;education&quot; data-vue-if=&quot;resumeData.education &amp;&amp; resumeData.education.length &gt; 0&quot;&gt;
    &lt;h2&gt;教育经历&lt;/h2&gt;
    &lt;div data-vue-for=&quot;education in resumeData.education&quot; data-vue-key=&quot;education.id&quot;&gt;
        &lt;!-- 核心字段（始终显示） --&gt;
        &lt;h3 data-field=&quot;school&quot; data-vue-text=&quot;education.school&quot;&gt;北京大学&lt;/h3&gt;
        &lt;div data-field=&quot;major&quot; data-vue-text=&quot;education.major&quot;&gt;计算机科学与技术&lt;/div&gt;
        
        &lt;!-- 可选字段（有数据才显示） --&gt;
        &lt;div data-field=&quot;degree&quot; data-vue-text=&quot;education.degree&quot; 
             data-vue-if=&quot;education.degree&quot;&gt;本科&lt;/div&gt;
        &lt;div data-field=&quot;gpa&quot; data-vue-text=&quot;education.gpa&quot; 
             data-vue-if=&quot;education.gpa&quot;&gt;GPA: 3.8&lt;/div&gt;
        &lt;div data-field=&quot;description&quot; data-vue-text=&quot;education.description&quot; 
             data-vue-if=&quot;education.description&quot;&gt;详细描述...&lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre></div><div class="condition-examples" data-v-0e4d416b><h5 data-v-0e4d416b>条件渲染策略：</h5><h6 data-v-0e4d416b>1. 模块级条件渲染</h6><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>数组型模块</strong>：<code data-v-0e4d416b>resumeData.education &amp;&amp; resumeData.education.length &gt; 0</code></li><li data-v-0e4d416b><strong data-v-0e4d416b>文本型模块</strong>：<code data-v-0e4d416b>resumeData.self_evaluation &amp;&amp; resumeData.self_evaluation.content</code></li><li data-v-0e4d416b><strong data-v-0e4d416b>对象型模块</strong>：<code data-v-0e4d416b>resumeData.basic_info &amp;&amp; resumeData.basic_info.name</code></li></ul><h6 data-v-0e4d416b>2. 字段级条件渲染</h6><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>基本字段</strong>：<code data-v-0e4d416b>resumeData.basic_info.age</code></li><li data-v-0e4d416b><strong data-v-0e4d416b>循环项字段</strong>：<code data-v-0e4d416b>education.degree</code></li><li data-v-0e4d416b><strong data-v-0e4d416b>复杂判断</strong>：<code data-v-0e4d416b>education.startDate &amp;&amp; education.endDate</code></li></ul><h6 data-v-0e4d416b>3. 字段分级策略</h6><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>核心字段</strong>：不加条件，始终显示（如姓名、学校名）</li><li data-v-0e4d416b><strong data-v-0e4d416b>常用字段</strong>：有数据时显示（如年龄、专业、职位）</li><li data-v-0e4d416b><strong data-v-0e4d416b>可选字段</strong>：用户选择性填写（如GPA、部门、详细描述）</li></ul><div class="note-box" data-v-0e4d416b><p data-v-0e4d416b><strong data-v-0e4d416b>实现效果：</strong></p><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>技术完整性</strong>：HTML模板包含所有18个模块和字段（满足技术要求）</li><li data-v-0e4d416b><strong data-v-0e4d416b>界面简洁性</strong>：初始显示只展示有数据的模块和字段（避免空白区域）</li><li data-v-0e4d416b><strong data-v-0e4d416b>动态扩展性</strong>：用户添加数据后，相应模块和字段自动显示（响应式体验）</li><li data-v-0e4d416b><strong data-v-0e4d416b>自动清理性</strong>：删除数据后，对应模块和字段自动隐藏（保持整洁）</li><li data-v-0e4d416b><strong data-v-0e4d416b>零代码修改</strong>：基于现有HTML转Vue转换器，无需修改任何后端代码</li></ul><div class="implementation-summary" data-v-0e4d416b><h6 data-v-0e4d416b>条件渲染实施总结：</h6><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>基本信息模块</strong>：核心字段（姓名、电话、邮箱）始终显示，其他字段按需显示</li><li data-v-0e4d416b><strong data-v-0e4d416b>列表型模块</strong>：工作经历、教育经历、项目经历等，模块和字段都支持条件渲染</li><li data-v-0e4d416b><strong data-v-0e4d416b>技能类模块</strong>：技能名称必显，等级、熟练度、进度条按需显示</li><li data-v-0e4d416b><strong data-v-0e4d416b>文本型模块</strong>：自我评价、自荐信等，有内容才显示整个模块</li><li data-v-0e4d416b><strong data-v-0e4d416b>时间字段</strong>：开始/结束时间及分隔符都支持独立的条件显示</li></ul></div></div></div></div></div></section><section id="css-variables" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>4. CSS变量使用规范</h2><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>标准CSS变量系统</h3><p data-v-0e4d416b>为了确保样式的一致性和可配置性，所有简历模板必须使用以下标准CSS变量：</p><div class="css-variables-table" data-v-0e4d416b><h4 data-v-0e4d416b>📝 变量分类说明</h4><table class="specification-table" data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>分类</th><th data-v-0e4d416b>变量名</th><th data-v-0e4d416b>说明</th><th data-v-0e4d416b>示例值</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td rowspan="3" data-v-0e4d416b><strong data-v-0e4d416b>字体设置</strong></td><td data-v-0e4d416b><code data-v-0e4d416b>--resume-font-family</code></td><td data-v-0e4d416b>全局字体族，影响整个简历的字体显示</td><td data-v-0e4d416b>system-ui, sans-serif</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-font-size</code></td><td data-v-0e4d416b>基础字体大小，其他字体大小基于此计算</td><td data-v-0e4d416b>14px</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-line-height</code></td><td data-v-0e4d416b>行高，影响文本可读性和紧凑度</td><td data-v-0e4d416b>1.6</td></tr><tr data-v-0e4d416b><td rowspan="3" data-v-0e4d416b><strong data-v-0e4d416b>颜色设置</strong></td><td data-v-0e4d416b><code data-v-0e4d416b>--resume-text-color</code></td><td data-v-0e4d416b>主要文本颜色，用于正文内容</td><td data-v-0e4d416b>#333333</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-primary-color</code></td><td data-v-0e4d416b>主色调，用于标题、链接等强调元素</td><td data-v-0e4d416b>#3498db</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-secondary-color</code></td><td data-v-0e4d416b>次要文本颜色，用于日期、地址等辅助信息</td><td data-v-0e4d416b>#666666</td></tr><tr data-v-0e4d416b><td rowspan="4" data-v-0e4d416b><strong data-v-0e4d416b>页面边距</strong></td><td data-v-0e4d416b><code data-v-0e4d416b>--resume-margin-top</code></td><td data-v-0e4d416b>页面上边距，控制简历与页面顶部的距离</td><td data-v-0e4d416b>60px</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-margin-bottom</code></td><td data-v-0e4d416b>页面下边距，控制简历与页面底部的距离</td><td data-v-0e4d416b>60px</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-margin-left</code></td><td data-v-0e4d416b>页面左边距，控制简历与页面左侧的距离</td><td data-v-0e4d416b>60px</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-margin-right</code></td><td data-v-0e4d416b>页面右边距，控制简历与页面右侧的距离</td><td data-v-0e4d416b>60px</td></tr><tr data-v-0e4d416b><td rowspan="4" data-v-0e4d416b><strong data-v-0e4d416b>模块间距</strong></td><td data-v-0e4d416b><code data-v-0e4d416b>--resume-module-spacing</code></td><td data-v-0e4d416b>模块间垂直间距（如教育、工作经历之间）</td><td data-v-0e4d416b>24px</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-item-spacing</code></td><td data-v-0e4d416b>同模块内条目间距（如多个工作经历之间）</td><td data-v-0e4d416b>16px</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-paragraph-spacing</code></td><td data-v-0e4d416b>段落间距，用于小的文本块分隔</td><td data-v-0e4d416b>12px</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-column-gap</code></td><td data-v-0e4d416b>多列布局时的列间距</td><td data-v-0e4d416b>40px</td></tr><tr data-v-0e4d416b><td rowspan="2" data-v-0e4d416b><strong data-v-0e4d416b>内边距</strong></td><td data-v-0e4d416b><code data-v-0e4d416b>--resume-module-padding</code></td><td data-v-0e4d416b>模块内部的填充空间</td><td data-v-0e4d416b>16px</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b><code data-v-0e4d416b>--resume-item-padding</code></td><td data-v-0e4d416b>条目内部的填充空间</td><td data-v-0e4d416b>12px</td></tr></tbody></table></div><div class="css-usage-examples" data-v-0e4d416b><h4 data-v-0e4d416b>🔧 使用示例</h4><h5 data-v-0e4d416b>推荐用法（使用resume-前缀变量）：</h5><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>.resume-template {
    /* 页面布局 */
    padding: var(--resume-margin-top) var(--resume-margin-right) 
             var(--resume-margin-bottom) var(--resume-margin-left);
    
    /* 字体设置 */
    font-family: var(--resume-font-family);
    font-size: var(--resume-font-size);
    line-height: var(--resume-line-height);
    color: var(--resume-text-color);
}

.section-title {
    /* 主色调用于标题 */
    color: var(--resume-primary-color);
    /* 基于基础字体大小计算标题大小 */
    font-size: calc(var(--resume-font-size) + 4px);
    /* 模块间距 */
    margin-bottom: var(--resume-module-spacing);
}

.work-item {
    /* 条目间距 */
    margin-bottom: var(--resume-item-spacing);
    /* 条目内边距 */
    padding: var(--resume-item-padding);
}

.work-date {
    /* 次要颜色用于日期 */
    color: var(--resume-secondary-color);
}</code></pre></div><h5 data-v-0e4d416b>向后兼容（旧版变量仍可使用）：</h5><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>/* 旧版变量（系统会自动映射到新版变量） */
.resume-template {
    padding: var(--margin-top) var(--margin-side) var(--margin-bottom);
    font-family: var(--font-family);
    color: var(--text-color);
}

/* 新版变量（推荐使用） */
.resume-template {
    padding: var(--resume-margin-top) var(--resume-margin-left) var(--resume-margin-bottom);
    font-family: var(--resume-font-family);
    color: var(--resume-text-color);
}</code></pre></div></div><div class="important-notes" data-v-0e4d416b><h4 data-v-0e4d416b>⚠️ 重要说明</h4><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>必须使用</strong>：所有模板必须使用这些CSS变量，不得硬编码样式值</li><li data-v-0e4d416b><strong data-v-0e4d416b>前缀规范</strong>：新模板推荐使用 <code data-v-0e4d416b>--resume-</code> 前缀的变量名</li><li data-v-0e4d416b><strong data-v-0e4d416b>向后兼容</strong>：旧版无前缀变量仍然支持，但新开发建议使用新版</li><li data-v-0e4d416b><strong data-v-0e4d416b>计算值</strong>：可以基于变量进行计算，如 <code data-v-0e4d416b>calc(var(--resume-font-size) + 2px)</code></li><li data-v-0e4d416b><strong data-v-0e4d416b>系统控制</strong>：这些变量由编辑器系统动态设置，模板只负责使用</li></ul></div></div></section><section id="data-attributes" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>5. 数据属性详解</h2><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>数据表达式格式</h3><div class="expression-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>表达式类型</th><th data-v-0e4d416b>格式</th><th data-v-0e4d416b>示例</th><th data-v-0e4d416b>说明</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>基本信息</td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.{字段名}</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.name</code></td><td data-v-0e4d416b>直接访问基本信息字段</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>循环项字段</td><td data-v-0e4d416b><code data-v-0e4d416b>item.{字段名}</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.company</code></td><td data-v-0e4d416b>循环中的单项数据</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>循环集合</td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.{模块名}</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.work_experience</code></td><td data-v-0e4d416b>数据集合</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>技能循环</td><td data-v-0e4d416b><code data-v-0e4d416b>skill.{字段名}</code></td><td data-v-0e4d416b><code data-v-0e4d416b>skill.name</code></td><td data-v-0e4d416b>技能模块专用变量名</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>兴趣爱好循环</td><td data-v-0e4d416b><code data-v-0e4d416b>hobby.{字段名}</code></td><td data-v-0e4d416b><code data-v-0e4d416b>hobby.name</code></td><td data-v-0e4d416b>兴趣爱好模块专用变量名</td></tr></tbody></table></div></div></section><section id="module-specifications" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>6. 各模块字段规范</h2><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.1 基本信息模块 (basic_info)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>照片</td><td data-v-0e4d416b><code data-v-0e4d416b>photo</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.photo</code></td><td data-v-0e4d416b>头像照片URL</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>姓名</td><td data-v-0e4d416b><code data-v-0e4d416b>name</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.name</code></td><td data-v-0e4d416b>完整姓名</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>年龄</td><td data-v-0e4d416b><code data-v-0e4d416b>age</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.age</code></td><td data-v-0e4d416b>年龄</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>电话</td><td data-v-0e4d416b><code data-v-0e4d416b>phone</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.phone</code></td><td data-v-0e4d416b>联系电话</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>邮箱</td><td data-v-0e4d416b><code data-v-0e4d416b>email</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.email</code></td><td data-v-0e4d416b>电子邮箱</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>所在城市</td><td data-v-0e4d416b><code data-v-0e4d416b>currentCity</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.currentCity</code></td><td data-v-0e4d416b>当前居住城市</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>意向城市</td><td data-v-0e4d416b><code data-v-0e4d416b>intendedCity</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.intendedCity</code></td><td data-v-0e4d416b>期望工作城市</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>居住地址</td><td data-v-0e4d416b><code data-v-0e4d416b>address</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.address</code></td><td data-v-0e4d416b>详细地址</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>当前状态</td><td data-v-0e4d416b><code data-v-0e4d416b>currentStatus</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.currentStatus</code></td><td data-v-0e4d416b>当前工作状态</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>期望岗位</td><td data-v-0e4d416b><code data-v-0e4d416b>expectedPosition</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.expectedPosition</code></td><td data-v-0e4d416b>求职意向</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>期望薪资</td><td data-v-0e4d416b><code data-v-0e4d416b>expectedSalary</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.expectedSalary</code></td><td data-v-0e4d416b>薪资期望</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>微信号</td><td data-v-0e4d416b><code data-v-0e4d416b>wechat</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.wechat</code></td><td data-v-0e4d416b>微信联系方式</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>个人网站</td><td data-v-0e4d416b><code data-v-0e4d416b>website</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.website</code></td><td data-v-0e4d416b>个人网站链接</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>GitHub</td><td data-v-0e4d416b><code data-v-0e4d416b>github</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.github</code></td><td data-v-0e4d416b>GitHub链接</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>性别</td><td data-v-0e4d416b><code data-v-0e4d416b>gender</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.gender</code></td><td data-v-0e4d416b>性别信息</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>身高</td><td data-v-0e4d416b><code data-v-0e4d416b>height</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.height</code></td><td data-v-0e4d416b>身高信息</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>体重</td><td data-v-0e4d416b><code data-v-0e4d416b>weight</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.weight</code></td><td data-v-0e4d416b>体重信息</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>政治面貌</td><td data-v-0e4d416b><code data-v-0e4d416b>politicalStatus</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.politicalStatus</code></td><td data-v-0e4d416b>政治面貌</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>婚姻状况</td><td data-v-0e4d416b><code data-v-0e4d416b>maritalStatus</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.maritalStatus</code></td><td data-v-0e4d416b>婚姻状况</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>籍贯</td><td data-v-0e4d416b><code data-v-0e4d416b>hometown</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.hometown</code></td><td data-v-0e4d416b>籍贯信息</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>民族</td><td data-v-0e4d416b><code data-v-0e4d416b>ethnicity</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.ethnicity</code></td><td data-v-0e4d416b>民族信息</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自定义社交信息</td><td data-v-0e4d416b><code data-v-0e4d416b>customSocials</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.customSocials</code></td><td data-v-0e4d416b>自定义社交信息数组</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自定义信息</td><td data-v-0e4d416b><code data-v-0e4d416b>customInfos</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.basic_info.customInfos</code></td><td data-v-0e4d416b>自定义信息数组</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div><div class="code-example" data-v-0e4d416b><h4 data-v-0e4d416b>示例代码：</h4><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;section data-module=&quot;basic_info&quot;&gt;
    &lt;h2&gt;基本信息&lt;/h2&gt;
    &lt;div class=&quot;basic-info-grid&quot;&gt;
        &lt;div class=&quot;info-item&quot;&gt;
            &lt;img data-field=&quot;photo&quot; data-vue-src=&quot;resumeData.basic_info.photo&quot; src=&quot;default-avatar.jpg&quot; alt=&quot;头像&quot; /&gt;
        &lt;/div&gt;
        &lt;div class=&quot;info-item&quot;&gt;
            &lt;label&gt;姓名：&lt;/label&gt;
            &lt;span data-field=&quot;name&quot; data-vue-text=&quot;resumeData.basic_info.name&quot;&gt;张三&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class=&quot;info-item&quot;&gt;
            &lt;label&gt;期望岗位：&lt;/label&gt;
            &lt;span data-field=&quot;expectedPosition&quot; data-vue-text=&quot;resumeData.basic_info.expectedPosition&quot;&gt;前端工程师&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class=&quot;info-item&quot;&gt;
            &lt;label&gt;电话：&lt;/label&gt;
            &lt;span data-field=&quot;phone&quot; data-vue-text=&quot;resumeData.basic_info.phone&quot;&gt;138-0000-0000&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class=&quot;info-item&quot;&gt;
            &lt;label&gt;邮箱：&lt;/label&gt;
            &lt;span data-field=&quot;email&quot; data-vue-text=&quot;resumeData.basic_info.email&quot;&gt;<EMAIL>&lt;/span&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre></div></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.2 工作经历模块 (work_experience)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>公司名称</td><td data-v-0e4d416b><code data-v-0e4d416b>company</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.company</code></td><td data-v-0e4d416b>工作单位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>职位</td><td data-v-0e4d416b><code data-v-0e4d416b>position</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.position</code></td><td data-v-0e4d416b>担任职位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>部门</td><td data-v-0e4d416b><code data-v-0e4d416b>department</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.department</code></td><td data-v-0e4d416b>所在部门</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>开始时间</td><td data-v-0e4d416b><code data-v-0e4d416b>startDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.startDate</code></td><td data-v-0e4d416b>入职时间</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>结束时间</td><td data-v-0e4d416b><code data-v-0e4d416b>endDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.endDate</code></td><td data-v-0e4d416b>离职时间</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>工作描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>工作内容</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div><div class="code-example" data-v-0e4d416b><h4 data-v-0e4d416b>示例代码：</h4><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;section data-module=&quot;work_experience&quot;&gt;
    &lt;h2&gt;工作经历&lt;/h2&gt;
    &lt;div class=&quot;work-list&quot;&gt;
        &lt;div class=&quot;work-item&quot; data-vue-for=&quot;item in resumeData.work_experience&quot; data-vue-key=&quot;item.id&quot;&gt;
            &lt;div class=&quot;work-header&quot;&gt;
                &lt;h3&gt;
                    &lt;span data-field=&quot;company&quot; data-vue-text=&quot;item.company&quot;&gt;ABC科技公司&lt;/span&gt; - 
                    &lt;span data-field=&quot;position&quot; data-vue-text=&quot;item.position&quot;&gt;前端工程师&lt;/span&gt;
                &lt;/h3&gt;
                &lt;div class=&quot;work-period&quot;&gt;
                    &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot;&gt;2020-01&lt;/span&gt; ~ 
                    &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot;&gt;2023-12&lt;/span&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class=&quot;work-content&quot;&gt;
                &lt;p data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot;&gt;
                    负责前端开发工作，使用Vue.js和React开发用户界面。
                &lt;/p&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre></div></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.3 教育经历模块 (education)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>学校名称</td><td data-v-0e4d416b><code data-v-0e4d416b>school</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.school</code></td><td data-v-0e4d416b>毕业院校</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>专业</td><td data-v-0e4d416b><code data-v-0e4d416b>major</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.major</code></td><td data-v-0e4d416b>所学专业</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>学历</td><td data-v-0e4d416b><code data-v-0e4d416b>degree</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.degree</code></td><td data-v-0e4d416b>学位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>开始时间</td><td data-v-0e4d416b><code data-v-0e4d416b>startDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.startDate</code></td><td data-v-0e4d416b>入学时间</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>结束时间</td><td data-v-0e4d416b><code data-v-0e4d416b>endDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.endDate</code></td><td data-v-0e4d416b>毕业时间</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自定义学历</td><td data-v-0e4d416b><code data-v-0e4d416b>customDegree</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.customDegree</code></td><td data-v-0e4d416b>自定义学历名称</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>教育背景描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.4 项目经历模块 (project)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>项目名称</td><td data-v-0e4d416b><code data-v-0e4d416b>name</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.name</code></td><td data-v-0e4d416b>项目标题</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>担任角色</td><td data-v-0e4d416b><code data-v-0e4d416b>role</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.role</code></td><td data-v-0e4d416b>项目角色</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>所在公司</td><td data-v-0e4d416b><code data-v-0e4d416b>company</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.company</code></td><td data-v-0e4d416b>项目所属公司</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>开始时间</td><td data-v-0e4d416b><code data-v-0e4d416b>startDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.startDate</code></td><td data-v-0e4d416b>项目开始</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>结束时间</td><td data-v-0e4d416b><code data-v-0e4d416b>endDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.endDate</code></td><td data-v-0e4d416b>项目结束</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>项目描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.5 技能特长模块 (skills)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>技能名称</td><td data-v-0e4d416b><code data-v-0e4d416b>name</code></td><td data-v-0e4d416b><code data-v-0e4d416b>skill.name</code></td><td data-v-0e4d416b>技能名称</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>熟练程度百分比</td><td data-v-0e4d416b><code data-v-0e4d416b>level</code></td><td data-v-0e4d416b><code data-v-0e4d416b>skill.level</code></td><td data-v-0e4d416b>0-100的数字</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>熟练程度文字</td><td data-v-0e4d416b><code data-v-0e4d416b>proficiency</code></td><td data-v-0e4d416b><code data-v-0e4d416b>skill.proficiency</code></td><td data-v-0e4d416b>精通/擅长/熟练/良好/一般</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>显示模式</td><td data-v-0e4d416b><code data-v-0e4d416b>displayMode</code></td><td data-v-0e4d416b><code data-v-0e4d416b>skill.displayMode</code></td><td data-v-0e4d416b>percentage/proficiency</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div><div class="code-example" data-v-0e4d416b><h4 data-v-0e4d416b>示例代码：</h4><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;section data-module=&quot;skills&quot;&gt;
    &lt;h2&gt;技能特长&lt;/h2&gt;
    &lt;div class=&quot;skills-grid&quot;&gt;
        &lt;div class=&quot;skill-item&quot; data-vue-for=&quot;skill in resumeData.skills&quot; data-vue-key=&quot;skill.id&quot;&gt;
            &lt;div class=&quot;skill-name&quot; data-field=&quot;name&quot; data-vue-text=&quot;skill.name&quot;&gt;JavaScript&lt;/div&gt;
            &lt;div class=&quot;skill-level&quot; data-field=&quot;level&quot; data-vue-text=&quot;skill.level&quot;&gt;90&lt;/div&gt;
            &lt;div class=&quot;skill-proficiency&quot; data-field=&quot;proficiency&quot; data-vue-text=&quot;skill.proficiency&quot;&gt;精通&lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre></div></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.6 获奖情况模块 (award)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>奖项名称</td><td data-v-0e4d416b><code data-v-0e4d416b>name</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.name</code></td><td data-v-0e4d416b>奖项标题</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>获奖时间</td><td data-v-0e4d416b><code data-v-0e4d416b>date</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.date</code></td><td data-v-0e4d416b>获奖日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>颁发机构</td><td data-v-0e4d416b><code data-v-0e4d416b>organization</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.organization</code></td><td data-v-0e4d416b>颁奖单位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>获奖等级</td><td data-v-0e4d416b><code data-v-0e4d416b>level</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.level</code></td><td data-v-0e4d416b>奖项等级</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.7 证书资质模块 (certificate)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>证书名称</td><td data-v-0e4d416b><code data-v-0e4d416b>name</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.name</code></td><td data-v-0e4d416b>证书标题</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>获得时间</td><td data-v-0e4d416b><code data-v-0e4d416b>date</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.date</code></td><td data-v-0e4d416b>取得日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>颁发机构</td><td data-v-0e4d416b><code data-v-0e4d416b>organization</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.organization</code></td><td data-v-0e4d416b>颁发单位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>证书编号</td><td data-v-0e4d416b><code data-v-0e4d416b>number</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.number</code></td><td data-v-0e4d416b>证书序号</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.8 语言能力模块 (language)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>语言名称</td><td data-v-0e4d416b><code data-v-0e4d416b>name</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.name</code></td><td data-v-0e4d416b>语言种类</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>熟练程度</td><td data-v-0e4d416b><code data-v-0e4d416b>level</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.level</code></td><td data-v-0e4d416b>掌握程度</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>相关证书</td><td data-v-0e4d416b><code data-v-0e4d416b>certificate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.certificate</code></td><td data-v-0e4d416b>语言证书</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.9 实习经历模块 (internship)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>公司名称</td><td data-v-0e4d416b><code data-v-0e4d416b>company</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.company</code></td><td data-v-0e4d416b>实习单位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>实习职位</td><td data-v-0e4d416b><code data-v-0e4d416b>position</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.position</code></td><td data-v-0e4d416b>实习岗位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>开始时间</td><td data-v-0e4d416b><code data-v-0e4d416b>startDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.startDate</code></td><td data-v-0e4d416b>开始日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>结束时间</td><td data-v-0e4d416b><code data-v-0e4d416b>endDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.endDate</code></td><td data-v-0e4d416b>结束日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>实习描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>工作内容</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.10 志愿经历模块 (volunteer_experience)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>组织机构</td><td data-v-0e4d416b><code data-v-0e4d416b>organization</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.organization</code></td><td data-v-0e4d416b>志愿组织</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>担任角色</td><td data-v-0e4d416b><code data-v-0e4d416b>role</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.role</code></td><td data-v-0e4d416b>志愿角色</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>服务地点</td><td data-v-0e4d416b><code data-v-0e4d416b>location</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.location</code></td><td data-v-0e4d416b>服务场所</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>开始时间</td><td data-v-0e4d416b><code data-v-0e4d416b>startDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.startDate</code></td><td data-v-0e4d416b>开始日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>结束时间</td><td data-v-0e4d416b><code data-v-0e4d416b>endDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.endDate</code></td><td data-v-0e4d416b>结束日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.11 论文发表模块 (publication)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>论文标题</td><td data-v-0e4d416b><code data-v-0e4d416b>title</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.title</code></td><td data-v-0e4d416b>文章标题</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>论文类型</td><td data-v-0e4d416b><code data-v-0e4d416b>type</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.type</code></td><td data-v-0e4d416b>文章类型</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>发表期刊</td><td data-v-0e4d416b><code data-v-0e4d416b>journal</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.journal</code></td><td data-v-0e4d416b>期刊名称</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>发表时间</td><td data-v-0e4d416b><code data-v-0e4d416b>date</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.date</code></td><td data-v-0e4d416b>发表日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.12 科研经历模块 (research_experience)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>研究课题</td><td data-v-0e4d416b><code data-v-0e4d416b>topic</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.topic</code></td><td data-v-0e4d416b>研究主题</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>担任角色</td><td data-v-0e4d416b><code data-v-0e4d416b>role</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.role</code></td><td data-v-0e4d416b>研究角色</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>研究机构</td><td data-v-0e4d416b><code data-v-0e4d416b>organization</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.organization</code></td><td data-v-0e4d416b>研究单位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>开始时间</td><td data-v-0e4d416b><code data-v-0e4d416b>startDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.startDate</code></td><td data-v-0e4d416b>开始日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>结束时间</td><td data-v-0e4d416b><code data-v-0e4d416b>endDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.endDate</code></td><td data-v-0e4d416b>结束日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.13 培训经历模块 (training)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>培训课程</td><td data-v-0e4d416b><code data-v-0e4d416b>course</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.course</code></td><td data-v-0e4d416b>课程名称</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>培训机构</td><td data-v-0e4d416b><code data-v-0e4d416b>company</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.company</code></td><td data-v-0e4d416b>培训单位</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>开始时间</td><td data-v-0e4d416b><code data-v-0e4d416b>startDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.startDate</code></td><td data-v-0e4d416b>开始日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>结束时间</td><td data-v-0e4d416b><code data-v-0e4d416b>endDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.endDate</code></td><td data-v-0e4d416b>结束日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.14 作品集模块 (portfolio)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>作品标题</td><td data-v-0e4d416b><code data-v-0e4d416b>title</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.title</code></td><td data-v-0e4d416b>作品名称</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>作品类型</td><td data-v-0e4d416b><code data-v-0e4d416b>type</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.type</code></td><td data-v-0e4d416b>作品分类</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>作品链接</td><td data-v-0e4d416b><code data-v-0e4d416b>url</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.url</code></td><td data-v-0e4d416b>在线链接</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>创作时间</td><td data-v-0e4d416b><code data-v-0e4d416b>date</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.date</code></td><td data-v-0e4d416b>创作日期</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.15 兴趣爱好模块 (hobbies)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>爱好名称</td><td data-v-0e4d416b><code data-v-0e4d416b>name</code></td><td data-v-0e4d416b><code data-v-0e4d416b>hobby.name</code></td><td data-v-0e4d416b>兴趣名称</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>熟练程度</td><td data-v-0e4d416b><code data-v-0e4d416b>level</code></td><td data-v-0e4d416b><code data-v-0e4d416b>hobby.level</code></td><td data-v-0e4d416b>掌握程度</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>描述</td><td data-v-0e4d416b><code data-v-0e4d416b>description</code></td><td data-v-0e4d416b><code data-v-0e4d416b>hobby.description</code></td><td data-v-0e4d416b>详细描述</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.16 自我评价模块 (self_evaluation)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>评价内容</td><td data-v-0e4d416b><code data-v-0e4d416b>content</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.self_evaluation.content</code></td><td data-v-0e4d416b>自我评价文本</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.17 自荐信模块 (cover_letter)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>信件内容</td><td data-v-0e4d416b><code data-v-0e4d416b>content</code></td><td data-v-0e4d416b><code data-v-0e4d416b>resumeData.cover_letter.content</code></td><td data-v-0e4d416b>自荐信文本</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.18 自定义模块 (custom_*)</h3><div class="field-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>字段名称</th><th data-v-0e4d416b>data-field值</th><th data-v-0e4d416b>data-vue-text表达式</th><th data-v-0e4d416b>描述</th><th data-v-0e4d416b>是否必需</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>名称</td><td data-v-0e4d416b><code data-v-0e4d416b>name</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.name</code></td><td data-v-0e4d416b>自定义项目名称</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>角色</td><td data-v-0e4d416b><code data-v-0e4d416b>role</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.role</code></td><td data-v-0e4d416b>担任的角色</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>开始时间</td><td data-v-0e4d416b><code data-v-0e4d416b>startDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.startDate</code></td><td data-v-0e4d416b>开始时间</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>结束时间</td><td data-v-0e4d416b><code data-v-0e4d416b>endDate</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.endDate</code></td><td data-v-0e4d416b>结束时间</td><td class="required" data-v-0e4d416b>必需</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>详细内容</td><td data-v-0e4d416b><code data-v-0e4d416b>content</code></td><td data-v-0e4d416b><code data-v-0e4d416b>item.content</code></td><td data-v-0e4d416b>详细描述内容</td><td class="required" data-v-0e4d416b>必需</td></tr></tbody></table></div><div class="code-example" data-v-0e4d416b><h4 data-v-0e4d416b>示例代码：</h4><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;section data-module=&quot;custom_1001&quot;&gt;
    &lt;h2&gt;社团活动&lt;/h2&gt;
    &lt;div data-vue-for=&quot;item in resumeData.custom_1001.items&quot; data-vue-key=&quot;item.id&quot;&gt;
        &lt;div class=&quot;custom-item&quot;&gt;
            &lt;div class=&quot;item-header&quot;&gt;
                &lt;h3 data-field=&quot;name&quot; data-vue-text=&quot;item.name&quot;&gt;学生会&lt;/h3&gt;
                &lt;span data-field=&quot;role&quot; data-vue-text=&quot;item.role&quot;&gt;主席&lt;/span&gt;
            &lt;/div&gt;
            &lt;div class=&quot;item-time&quot;&gt;
                &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot;&gt;2019-09&lt;/span&gt;
                &lt;span&gt; - &lt;/span&gt;
                &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot;&gt;2020-06&lt;/span&gt;
            &lt;/div&gt;
            &lt;div class=&quot;item-content&quot;&gt;
                &lt;p data-field=&quot;content&quot; data-vue-text=&quot;item.content&quot;&gt;组织学校大型活动，管理团队50人...&lt;/p&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre></div></div><div class="requirement-box" data-v-0e4d416b><h4 data-v-0e4d416b>重要说明：</h4><ul data-v-0e4d416b><li data-v-0e4d416b>自定义模块ID格式为：<code data-v-0e4d416b>custom_[时间戳]</code>（如：custom_1700000001）</li><li data-v-0e4d416b>每个用户最多可创建3个自定义模块</li><li data-v-0e4d416b>自定义模块支持类似其他模块的数组数据结构</li><li data-v-0e4d416b>模块名称由用户创建时自定义（如：社团活动、获奖情况等）</li><li data-v-0e4d416b>所有字段都是可选的，可根据实际需要使用</li></ul></div></div><div class="module-spec" data-v-0e4d416b><h3 data-v-0e4d416b>5.19 支持的模块总览</h3><div class="modules-overview" data-v-0e4d416b><p data-v-0e4d416b><strong data-v-0e4d416b>当前系统支持以下18个简历模块：</strong></p><div class="modules-grid" data-v-0e4d416b><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>basic_info</span><span class="module-name" data-v-0e4d416b>基本信息</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>work_experience</span><span class="module-name" data-v-0e4d416b>工作经历</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>education</span><span class="module-name" data-v-0e4d416b>教育经历</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>project</span><span class="module-name" data-v-0e4d416b>项目经历</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>skills</span><span class="module-name" data-v-0e4d416b>技能特长</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>award</span><span class="module-name" data-v-0e4d416b>获奖情况</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>certificate</span><span class="module-name" data-v-0e4d416b>证书资质</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>language</span><span class="module-name" data-v-0e4d416b>语言能力</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>internship</span><span class="module-name" data-v-0e4d416b>实习经历</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>volunteer_experience</span><span class="module-name" data-v-0e4d416b>志愿经历</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>publication</span><span class="module-name" data-v-0e4d416b>论文发表</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>research_experience</span><span class="module-name" data-v-0e4d416b>科研经历</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>training</span><span class="module-name" data-v-0e4d416b>培训经历</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>portfolio</span><span class="module-name" data-v-0e4d416b>作品集</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>hobbies</span><span class="module-name" data-v-0e4d416b>兴趣爱好</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>self_evaluation</span><span class="module-name" data-v-0e4d416b>自我评价</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>cover_letter</span><span class="module-name" data-v-0e4d416b>自荐信</span><span class="module-status" data-v-0e4d416b>必需</span></div><div class="module-item required" data-v-0e4d416b><span class="module-id" data-v-0e4d416b>custom_*</span><span class="module-name" data-v-0e4d416b>自定义模块</span><span class="module-status" data-v-0e4d416b>必需</span></div></div><p class="modules-note" data-v-0e4d416b><strong data-v-0e4d416b>注意：</strong>大部分模块ID使用单数形式，只有 <code data-v-0e4d416b>skills</code> 和 <code data-v-0e4d416b>hobbies</code> 使用复数形式。 在循环渲染中，大部分模块使用 <code data-v-0e4d416b>item</code> 作为循环变量名，技能模块使用 <code data-v-0e4d416b>skill</code>，兴趣爱好模块使用 <code data-v-0e4d416b>hobby</code>。 </p></div></div></section><section id="style-requirements" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>7. 样式要求</h2><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>CSS样式规范</h3><div class="style-rules" data-v-0e4d416b><h4 data-v-0e4d416b>基本要求：</h4><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>根容器样式</strong>：必须定义 <code data-v-0e4d416b>.resume-template</code> 类</li><li data-v-0e4d416b><strong data-v-0e4d416b>单位使用</strong>：建议使用相对单位（rem、em、%）</li><li data-v-0e4d416b><strong data-v-0e4d416b>响应式设计</strong>：支持不同屏幕尺寸</li><li data-v-0e4d416b><strong data-v-0e4d416b>打印友好</strong>：考虑打印时的样式表现</li><li data-v-0e4d416b><strong data-v-0e4d416b>字体设置</strong>：使用系统字体或Web安全字体</li></ul></div><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>/* 必需的根容器样式 */
.resume-template {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, &#39;Segoe UI&#39;, Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 模块通用样式 */
.resume-template section {
    margin-bottom: 2rem;
    padding: 1rem 0;
}

.resume-template h1, h2, h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .resume-template {
        padding: 15px;
        font-size: 14px;
    }
}

/* 打印样式 */
@media print {
    .resume-template {
        margin: 0;
        padding: 0;
        box-shadow: none;
    }
}</code></pre></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>模块样式规范</h3><p data-v-0e4d416b>每个模块都应遵循统一的样式规范，确保视觉一致性：</p><div class="module-style-table" data-v-0e4d416b><table data-v-0e4d416b><thead data-v-0e4d416b><tr data-v-0e4d416b><th data-v-0e4d416b>模块类型</th><th data-v-0e4d416b>推荐CSS类名</th><th data-v-0e4d416b>布局要求</th><th data-v-0e4d416b>特殊样式说明</th></tr></thead><tbody data-v-0e4d416b><tr data-v-0e4d416b><td data-v-0e4d416b>基本信息</td><td data-v-0e4d416b><code data-v-0e4d416b>.basic-info</code></td><td data-v-0e4d416b>居中对齐，头像+信息布局</td><td data-v-0e4d416b>头像圆形显示，联系信息网格布局</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>工作经历</td><td data-v-0e4d416b><code data-v-0e4d416b>.work-experience</code></td><td data-v-0e4d416b>时间轴或卡片布局</td><td data-v-0e4d416b>公司+职位标题，描述内容缩进</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>教育经历</td><td data-v-0e4d416b><code data-v-0e4d416b>.education</code></td><td data-v-0e4d416b>学校+学历+时间布局</td><td data-v-0e4d416b>学历信息突出显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>项目经历</td><td data-v-0e4d416b><code data-v-0e4d416b>.project</code></td><td data-v-0e4d416b>项目名+角色+时间布局</td><td data-v-0e4d416b>技术栈标签化显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>技能特长</td><td data-v-0e4d416b><code data-v-0e4d416b>.skills</code></td><td data-v-0e4d416b>网格或标签布局</td><td data-v-0e4d416b>支持进度条和等级显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>语言能力</td><td data-v-0e4d416b><code data-v-0e4d416b>.language</code></td><td data-v-0e4d416b>语言+等级布局</td><td data-v-0e4d416b>等级可用星级或文字显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>获奖情况</td><td data-v-0e4d416b><code data-v-0e4d416b>.award</code></td><td data-v-0e4d416b>奖项+机构+时间布局</td><td data-v-0e4d416b>奖项名称突出显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>证书资质</td><td data-v-0e4d416b><code data-v-0e4d416b>.certificate</code></td><td data-v-0e4d416b>证书+机构+时间布局</td><td data-v-0e4d416b>证书编号可选显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>实习经历</td><td data-v-0e4d416b><code data-v-0e4d416b>.internship</code></td><td data-v-0e4d416b>公司+职位+时间布局</td><td data-v-0e4d416b>类似工作经历样式</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>志愿经历</td><td data-v-0e4d416b><code data-v-0e4d416b>.volunteer-experience</code></td><td data-v-0e4d416b>组织+角色+时间布局</td><td data-v-0e4d416b>服务地点可额外显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>论文发表</td><td data-v-0e4d416b><code data-v-0e4d416b>.publication</code></td><td data-v-0e4d416b>标题+期刊+时间布局</td><td data-v-0e4d416b>论文标题斜体显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>科研经历</td><td data-v-0e4d416b><code data-v-0e4d416b>.research-experience</code></td><td data-v-0e4d416b>课题+角色+机构布局</td><td data-v-0e4d416b>研究机构单独一行</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>培训经历</td><td data-v-0e4d416b><code data-v-0e4d416b>.training</code></td><td data-v-0e4d416b>课程+机构+时间布局</td><td data-v-0e4d416b>培训机构突出显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>作品集</td><td data-v-0e4d416b><code data-v-0e4d416b>.portfolio</code></td><td data-v-0e4d416b>作品+类型+链接布局</td><td data-v-0e4d416b>链接可点击，图标显示</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>兴趣爱好</td><td data-v-0e4d416b><code data-v-0e4d416b>.hobbies</code></td><td data-v-0e4d416b>标签或列表布局</td><td data-v-0e4d416b>简洁的标签样式</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自我评价</td><td data-v-0e4d416b><code data-v-0e4d416b>.self-evaluation</code></td><td data-v-0e4d416b>段落布局</td><td data-v-0e4d416b>文字行高适中，易读</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自荐信</td><td data-v-0e4d416b><code data-v-0e4d416b>.cover-letter</code></td><td data-v-0e4d416b>正文段落布局</td><td data-v-0e4d416b>类似自我评价样式</td></tr><tr data-v-0e4d416b><td data-v-0e4d416b>自定义模块</td><td data-v-0e4d416b><code data-v-0e4d416b>.custom-module</code></td><td data-v-0e4d416b>通用模块布局</td><td data-v-0e4d416b>根据内容类型灵活调整</td></tr></tbody></table></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>字段样式规范</h3><div class="field-style-rules" data-v-0e4d416b><h4 data-v-0e4d416b>通用字段样式：</h4><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>标题字段</strong>：使用较大字号，加粗显示</li><li data-v-0e4d416b><strong data-v-0e4d416b>时间字段</strong>：使用较小字号，右对齐或单独一行</li><li data-v-0e4d416b><strong data-v-0e4d416b>描述字段</strong>：正常字号，左对齐，适当行间距</li><li data-v-0e4d416b><strong data-v-0e4d416b>链接字段</strong>：蓝色显示，悬停效果</li><li data-v-0e4d416b><strong data-v-0e4d416b>技能等级</strong>：进度条或星级显示</li></ul></div><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>/* 字段通用样式 */
.field-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.field-subtitle {
    font-weight: 500;
    color: #34495e;
    font-size: 0.9em;
}

.field-date {
    font-size: 0.85em;
    color: #7f8c8d;
    font-style: italic;
}

.field-description {
    line-height: 1.6;
    color: #2c3e50;
    margin: 0.5rem 0;
}

.field-link {
    color: #3498db;
    text-decoration: none;
    transition: color 0.2s;
}

.field-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* 技能等级样式 */
.skill-progress {
    width: 100%;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.skill-progress-bar {
    height: 100%;
    background: #3498db;
    transition: width 0.3s ease;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: #ecf0f1;
    color: #2c3e50;
    border-radius: 0.25rem;
    font-size: 0.85em;
    margin: 0.125rem;
}</code></pre></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>布局样式规范</h3><div class="layout-rules" data-v-0e4d416b><h4 data-v-0e4d416b>推荐布局模式：</h4><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>单列布局</strong>：适合简洁风格，所有模块垂直排列</li><li data-v-0e4d416b><strong data-v-0e4d416b>双列布局</strong>：左侧基本信息+技能，右侧其他模块</li><li data-v-0e4d416b><strong data-v-0e4d416b>网格布局</strong>：技能、语言等模块使用网格排列</li><li data-v-0e4d416b><strong data-v-0e4d416b>时间轴布局</strong>：工作、教育、项目经历使用时间轴</li></ul></div><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>/* 双列布局示例 */
.resume-template {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

.left-column {
    order: 1;
}

.right-column {
    order: 2;
}

/* 网格布局示例 */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* 时间轴布局示例 */
.timeline {
    position: relative;
    padding-left: 1.5rem;
}

.timeline::before {
    content: &#39;&#39;;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #3498db;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item::before {
    content: &#39;&#39;;
    position: absolute;
    left: -1.5rem;
    top: 0.5rem;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #3498db;
}</code></pre></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>响应式样式规范</h3><div class="responsive-rules" data-v-0e4d416b><h4 data-v-0e4d416b>断点设置：</h4><ul data-v-0e4d416b><li data-v-0e4d416b><strong data-v-0e4d416b>桌面端</strong>：1024px及以上 - 完整双列布局</li><li data-v-0e4d416b><strong data-v-0e4d416b>平板端</strong>：768px-1023px - 简化双列或单列布局</li><li data-v-0e4d416b><strong data-v-0e4d416b>手机端</strong>：767px及以下 - 单列布局，字号调整</li></ul></div><div class="code-block" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>/* 响应式布局 */
@media (max-width: 1023px) {
    .resume-template {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 767px) {
    .resume-template {
        padding: 1rem;
        font-size: 14px;
    }
    
    .field-title {
        font-size: 1rem;
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
    
    .timeline {
        padding-left: 1rem;
    }
}

/* 打印样式优化 */
@media print {
    .resume-template {
        grid-template-columns: 1fr;
        font-size: 12px;
        line-height: 1.4;
    }
    
    .timeline::before,
    .timeline-item::before {
        display: none;
    }
    
    section {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}</code></pre></div></div></section><section id="examples" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>8. 完整示例</h2><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>完整的HTML简历模板（包含所有18个标准模块）</h3><p class="template-description" data-v-0e4d416b> 这是一个完整的基础HTML简历模板，包含了所有18个标准模块和完整的字段绑定。 其他HTML模板可以基于此模板修改样式和布局。 </p><div class="code-block full-example" data-v-0e4d416b><pre data-v-0e4d416b><code data-v-0e4d416b>&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;zh-CN&quot;&gt;
&lt;head&gt;
    &lt;meta charset=&quot;UTF-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;
    &lt;title&gt;标准简历模板&lt;/title&gt;
    &lt;style&gt;
        /* === CSS变量系统 - 用户可配置的样式参数 === */
        :root {
            /* === 字体设置 === */
            --resume-font-family: system-ui, -apple-system, BlinkMacSystemFont, &#39;Segoe UI&#39;, Roboto, sans-serif; /* 全局字体族 */
            --resume-font-size: 14px;              /* 基础字体大小 */
            --resume-line-height: 1.6;             /* 行高，影响文本可读性 */
            
            /* === 颜色设置 === */
            --resume-text-color: #333333;          /* 主要文本颜色 */
            --resume-primary-color: #3498db;       /* 主色调，用于强调元素（标题、链接等） */
            --resume-secondary-color: #666666;     /* 次要文本颜色，用于辅助信息（日期、地址等） */
            
            /* === 页面边距设置 === */
            --resume-margin-top: 60px;             /* 页面上边距 */
            --resume-margin-bottom: 60px;          /* 页面下边距 */
            --resume-margin-left: 60px;            /* 页面左边距 */
            --resume-margin-right: 60px;           /* 页面右边距 */
            
            /* === 模块间距设置 === */
            --resume-module-spacing: 24px;         /* 模块之间的垂直间距（如教育经历、工作经历之间） */
            --resume-item-spacing: 16px;           /* 同一模块内条目之间的间距（如多个工作经历之间） */
            --resume-paragraph-spacing: 12px;      /* 段落间距，用于小的文本块分隔 */
            --resume-column-gap: 40px;             /* 多列布局时的列间距 */
            
            /* === 内边距设置 === */
            --resume-module-padding: 16px;         /* 模块内部的填充空间 */
            --resume-item-padding: 12px;           /* 条目内部的填充空间 */
            
            /* === 兼容性变量（旧版本支持，新模板请使用上述resume-前缀变量） === */
            --font-family: var(--resume-font-family);
            --font-size: var(--resume-font-size);
            --line-height: var(--resume-line-height);
            --text-color: var(--resume-text-color);
            --primary-color: var(--resume-primary-color);
            --secondary-color: var(--resume-secondary-color);
            --margin-top: var(--resume-margin-top);
            --margin-bottom: var(--resume-margin-bottom);
            --margin-side: var(--resume-margin-left);
            --module-spacing: var(--resume-module-spacing);
            --item-spacing: var(--resume-item-spacing);
            --paragraph-spacing: var(--resume-paragraph-spacing);
            --module-padding: var(--resume-module-padding);
            --item-padding: var(--resume-item-padding);
        }

        /* === 基础布局样式 === */
        .resume-template {
            /* 推荐使用：--resume-margin-top, --resume-margin-left, --resume-margin-bottom */
            padding: var(--margin-top) var(--margin-side) var(--margin-bottom);
            /* 推荐使用：--resume-font-family, --resume-font-size, --resume-line-height, --resume-text-color */
            font-family: var(--font-family);
            font-size: var(--font-size);
            line-height: var(--line-height);
            color: var(--text-color);
        }
        
        /* === 基本信息模块样式 === */
        .basic-info-section {
            text-align: center;
            margin-bottom: var(--module-spacing);
            padding: var(--module-padding);
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto var(--item-spacing);
            object-fit: cover;
        }
        
        .name {
            font-size: calc(var(--font-size) + 6px);
            font-weight: 700;
            margin-bottom: var(--paragraph-spacing);
            color: var(--text-color);
        }

        .expected-position {
            font-size: calc(var(--font-size) + 2px);
            color: var(--primary-color);
            margin-bottom: var(--item-spacing);
            font-weight: 500;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--paragraph-spacing);
            text-align: left;
            margin-bottom: var(--item-spacing);
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: var(--paragraph-spacing);
            padding: var(--item-padding);
        }

        .contact-label {
            font-weight: 600;
            color: var(--secondary-color);
            min-width: 80px;
        }

        .contact-value {
            color: var(--text-color);
        }
        
        /* === 通用模块样式 === */
        /* === 通用模块样式 === */
        .section {
            margin-bottom: var(--module-spacing);
        }
        
        .section-title {
            font-size: calc(var(--font-size) + 4px);
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: var(--item-spacing);
        }

        /* === 列表项通用样式 === */
        .list-item {
            margin-bottom: var(--item-spacing);
            padding: var(--item-padding);
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--paragraph-spacing);
            flex-wrap: wrap;
            gap: var(--paragraph-spacing);
        }
        
        .item-title {
            font-weight: 600;
            color: var(--text-color);
            font-size: calc(var(--font-size) + 1px);
        }

        .item-subtitle {
            color: var(--secondary-color);
            font-weight: 500;
            margin-top: 4px;
        }
        
        .item-date {
            color: var(--secondary-color);
            font-size: var(--font-size);
            font-weight: 500;
        }

        .item-content {
            color: var(--text-color);
            line-height: var(--line-height);
        }
        
        /* === 技能模块样式 === */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--item-spacing);
        }
        
        .skill-item {
            padding: var(--item-padding);
            text-align: center;
        }

        .skill-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--paragraph-spacing);
        }

        .skill-level {
            color: var(--primary-color);
            font-size: var(--font-size);
            font-weight: 500;
        }

        .skill-progress {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin-top: var(--paragraph-spacing);
            overflow: hidden;
        }

        .skill-progress-bar {
            height: 100%;
            background: var(--primary-color);
            border-radius: 4px;
        }

        /* === 简单列表样式 === */
        .simple-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--paragraph-spacing);
        }

        .simple-item {
            padding: var(--item-padding);
        }

        /* === 文本内容样式 === */
        .text-content {
            padding: var(--item-padding);
            line-height: var(--line-height);
        }

        /* === 响应式设计 === */
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
            }

            .item-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }

            .simple-list {
                grid-template-columns: 1fr;
            }
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class=&quot;resume-template&quot; data-module-root=&quot;true&quot;&gt;
        
        &lt;!-- 1. 基本信息模块 (必需) --&gt;
        &lt;section class=&quot;basic-info-section&quot; data-module=&quot;basic_info&quot;&gt;
            &lt;!-- 头像：可选显示 --&gt;
            &lt;img class=&quot;avatar&quot; data-field=&quot;photo&quot; data-vue-src=&quot;resumeData.basic_info.photo&quot; 
                 data-vue-if=&quot;resumeData.basic_info.photo&quot;
                 src=&quot;data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjhGOUZBIi8+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNDAiIHI9IjIwIiBmaWxsPSIjNkM3NTdEIi8+CjxwYXRoIGQ9Ik0yMCA5MEM0MCA3MCA4MCA3MCAxMDAgOTBWMTIwSDIwVjkwWiIgZmlsbD0iIzZDNzU3RCIvPgo8L3N2Zz4=&quot; alt=&quot;头像&quot; /&gt;
            
            &lt;!-- 核心信息：始终显示 --&gt;
            &lt;h1 class=&quot;name&quot; data-field=&quot;name&quot; data-vue-text=&quot;resumeData.basic_info.name&quot;&gt;张三&lt;/h1&gt;
            &lt;div class=&quot;expected-position&quot; data-field=&quot;expectedPosition&quot; data-vue-text=&quot;resumeData.basic_info.expectedPosition&quot;&gt;软件工程师&lt;/div&gt;
            
            &lt;div class=&quot;contact-grid&quot;&gt;
                &lt;!-- 核心联系方式：始终显示 --&gt;
                &lt;div class=&quot;contact-item&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;电话：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;phone&quot; data-vue-text=&quot;resumeData.basic_info.phone&quot;&gt;138-0000-0000&lt;/span&gt;
            &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;邮箱：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;email&quot; data-vue-text=&quot;resumeData.basic_info.email&quot;&gt;<EMAIL>&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 可选基本信息：有数据才显示 --&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.age&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;年龄：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;age&quot; data-vue-text=&quot;resumeData.basic_info.age&quot;&gt;28岁&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.currentCity&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;现居：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;currentCity&quot; data-vue-text=&quot;resumeData.basic_info.currentCity&quot;&gt;北京市&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.intendedCity&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;意向：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;intendedCity&quot; data-vue-text=&quot;resumeData.basic_info.intendedCity&quot;&gt;上海市&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.address&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;地址：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;address&quot; data-vue-text=&quot;resumeData.basic_info.address&quot;&gt;北京市朝阳区&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.currentStatus&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;状态：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;currentStatus&quot; data-vue-text=&quot;resumeData.basic_info.currentStatus&quot;&gt;在职寻新&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.expectedSalary&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;薪资：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;expectedSalary&quot; data-vue-text=&quot;resumeData.basic_info.expectedSalary&quot;&gt;15-20K&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 社交信息：有数据才显示 --&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.wechat&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;微信：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;wechat&quot; data-vue-text=&quot;resumeData.basic_info.wechat&quot;&gt;zhangsan_wx&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.website&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;网站：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;website&quot; data-vue-text=&quot;resumeData.basic_info.website&quot;&gt;https://zhangsan.dev&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.github&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;GitHub：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;github&quot; data-vue-text=&quot;resumeData.basic_info.github&quot;&gt;https://github.com/zhangsan&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 详细个人信息：有数据才显示 --&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.gender&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;性别：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;gender&quot; data-vue-text=&quot;resumeData.basic_info.gender&quot;&gt;男&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.height&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;身高：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;height&quot; data-vue-text=&quot;resumeData.basic_info.height&quot;&gt;175cm&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.weight&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;体重：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;weight&quot; data-vue-text=&quot;resumeData.basic_info.weight&quot;&gt;70kg&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.politicalStatus&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;政治面貌：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;politicalStatus&quot; data-vue-text=&quot;resumeData.basic_info.politicalStatus&quot;&gt;群众&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.maritalStatus&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;婚姻状况：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;maritalStatus&quot; data-vue-text=&quot;resumeData.basic_info.maritalStatus&quot;&gt;未婚&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.hometown&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;籍贯：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;hometown&quot; data-vue-text=&quot;resumeData.basic_info.hometown&quot;&gt;山东济南&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-if=&quot;resumeData.basic_info.ethnicity&quot;&gt;
                    &lt;span class=&quot;contact-label&quot;&gt;民族：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;ethnicity&quot; data-vue-text=&quot;resumeData.basic_info.ethnicity&quot;&gt;汉族&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 自定义社交信息：有数据才显示 --&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-for=&quot;social in resumeData.basic_info.customSocials&quot; data-vue-key=&quot;social.id&quot;
                     data-vue-if=&quot;resumeData.basic_info.customSocials &amp;&amp; resumeData.basic_info.customSocials.length &gt; 0&quot;&gt;
                    &lt;span class=&quot;contact-label&quot; data-field=&quot;label&quot; data-vue-text=&quot;social.label&quot;&gt;LinkedIn：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;value&quot; data-vue-text=&quot;social.value&quot;&gt;linkedin.com/in/zhangsan&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 自定义信息：有数据才显示 --&gt;
                &lt;div class=&quot;contact-item&quot; data-vue-for=&quot;info in resumeData.basic_info.customInfos&quot; data-vue-key=&quot;info.id&quot;
                     data-vue-if=&quot;resumeData.basic_info.customInfos &amp;&amp; resumeData.basic_info.customInfos.length &gt; 0&quot;&gt;
                    &lt;span class=&quot;contact-label&quot; data-field=&quot;label&quot; data-vue-text=&quot;info.label&quot;&gt;驾照：&lt;/span&gt;
                    &lt;span class=&quot;contact-value&quot; data-field=&quot;value&quot; data-vue-text=&quot;info.value&quot;&gt;C1&lt;/span&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 2. 工作经历模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;work_experience&quot; data-vue-if=&quot;resumeData.work_experience &amp;&amp; resumeData.work_experience.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;工作经历&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.work_experience&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;company&quot; data-vue-text=&quot;item.company&quot;&gt;ABC科技有限公司&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot;&gt;
                        &lt;span data-field=&quot;position&quot; data-vue-text=&quot;item.position&quot;&gt;高级前端工程师&lt;/span&gt;
                            &lt;!-- 可选字段：有数据才显示 --&gt;
                            &lt;span data-vue-if=&quot;item.department&quot;&gt; · &lt;/span&gt;
                            &lt;span data-field=&quot;department&quot; data-vue-text=&quot;item.department&quot; data-vue-if=&quot;item.department&quot;&gt;技术部&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot;&gt;
                        &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot; data-vue-if=&quot;item.startDate&quot;&gt;2020-03&lt;/span&gt;
                        &lt;span data-vue-if=&quot;item.startDate &amp;&amp; item.endDate&quot;&gt; ~ &lt;/span&gt;
                        &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot; data-vue-if=&quot;item.endDate&quot;&gt;2024-01&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- 工作描述：可选字段 --&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot; data-vue-if=&quot;item.description&quot;&gt;
                    负责公司核心产品的前端开发工作，使用Vue.js、React等技术栈构建高质量的用户界面。
                    参与产品需求分析、技术方案设计、代码开发、测试和上线等全流程工作。
                    优化页面性能，提升用户体验，并负责团队技术培训和代码评审工作。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 3. 教育经历模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;education&quot; data-vue-if=&quot;resumeData.education &amp;&amp; resumeData.education.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;教育经历&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.education&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;school&quot; data-vue-text=&quot;item.school&quot;&gt;北京理工大学&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot;&gt;
                            &lt;span data-field=&quot;major&quot; data-vue-text=&quot;item.major&quot;&gt;计算机科学与技术&lt;/span&gt;
                            &lt;!-- 可选字段：有数据才显示 --&gt;
                            &lt;span data-vue-if=&quot;item.degree&quot;&gt; · &lt;/span&gt;
                            &lt;span data-field=&quot;degree&quot; data-vue-text=&quot;item.degree&quot; data-vue-if=&quot;item.degree&quot;&gt;本科&lt;/span&gt;
                            &lt;span data-vue-if=&quot;item.customDegree&quot;&gt; · &lt;/span&gt;
                            &lt;span data-field=&quot;customDegree&quot; data-vue-text=&quot;item.customDegree&quot; data-vue-if=&quot;item.customDegree&quot;&gt;工学学士&lt;/span&gt;
                            &lt;span data-vue-if=&quot;item.gpa&quot;&gt; · GPA: &lt;/span&gt;
                            &lt;span data-field=&quot;gpa&quot; data-vue-text=&quot;item.gpa&quot; data-vue-if=&quot;item.gpa&quot;&gt;3.8&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot;&gt;
                        &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot; data-vue-if=&quot;item.startDate&quot;&gt;2016-09&lt;/span&gt;
                        &lt;span data-vue-if=&quot;item.startDate &amp;&amp; item.endDate&quot;&gt; ~ &lt;/span&gt;
                        &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot; data-vue-if=&quot;item.endDate&quot;&gt;2020-06&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- 教育描述：可选字段 --&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot; data-vue-if=&quot;item.description&quot;&gt;
                    主修计算机科学核心课程，包括数据结构与算法、操作系统、计算机网络、数据库系统等。
                    参与多个课程项目和实践活动，培养了扎实的编程基础和系统设计能力。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 4. 项目经历模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;project&quot; data-vue-if=&quot;resumeData.project &amp;&amp; resumeData.project.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;项目经历&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.project&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;name&quot; data-vue-text=&quot;item.name&quot;&gt;电商管理后台系统&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot;&gt;
                            &lt;!-- 可选字段：有数据才显示 --&gt;
                            &lt;span data-field=&quot;role&quot; data-vue-text=&quot;item.role&quot; data-vue-if=&quot;item.role&quot;&gt;技术负责人&lt;/span&gt;
                            &lt;span data-vue-if=&quot;item.role &amp;&amp; item.company&quot;&gt; · &lt;/span&gt;
                            &lt;span data-field=&quot;company&quot; data-vue-text=&quot;item.company&quot; data-vue-if=&quot;item.company&quot;&gt;ABC科技有限公司&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot;&gt;
                        &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot; data-vue-if=&quot;item.startDate&quot;&gt;2022-06&lt;/span&gt;
                        &lt;span data-vue-if=&quot;item.startDate &amp;&amp; item.endDate&quot;&gt; ~ &lt;/span&gt;
                        &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot; data-vue-if=&quot;item.endDate&quot;&gt;2023-12&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- 项目描述：可选字段 --&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot; data-vue-if=&quot;item.description&quot;&gt;
                    负责电商管理后台系统的前端架构设计与开发，采用Vue3 + TypeScript + Element Plus技术栈。
                    实现了商品管理、订单处理、用户管理、数据统计等核心功能模块。
                    通过组件化开发和代码分割优化，显著提升了系统性能和开发效率。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 5. 技能特长模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;skills&quot; data-vue-if=&quot;resumeData.skills &amp;&amp; resumeData.skills.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;技能特长&lt;/h2&gt;
            &lt;div class=&quot;skills-grid&quot;&gt;
                &lt;div class=&quot;skill-item&quot; data-vue-for=&quot;skill in resumeData.skills&quot; data-vue-key=&quot;skill.id&quot;&gt;
                    &lt;!-- 核心字段：始终显示 --&gt;
                    &lt;div class=&quot;skill-name&quot; data-field=&quot;name&quot; data-vue-text=&quot;skill.name&quot;&gt;JavaScript&lt;/div&gt;
                    &lt;!-- 可选字段：有数据才显示 --&gt;
                    &lt;div class=&quot;skill-level&quot; data-field=&quot;level&quot; data-vue-text=&quot;skill.level&quot; data-vue-if=&quot;skill.level&quot;&gt;90%&lt;/div&gt;
                    &lt;div class=&quot;skill-proficiency&quot; data-field=&quot;proficiency&quot; data-vue-text=&quot;skill.proficiency&quot; data-vue-if=&quot;skill.proficiency&quot;&gt;精通&lt;/div&gt;
                    &lt;div class=&quot;skill-progress&quot; data-vue-if=&quot;skill.displayMode &amp;&amp; skill.level&quot;&gt;
                        &lt;div class=&quot;skill-progress-bar&quot; data-field=&quot;displayMode&quot; :style=&quot;{ width: skill.displayMode === &#39;percentage&#39; ? skill.level + &#39;%&#39; : &#39;0%&#39; }&quot;&gt;&lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 6. 获奖情况模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;award&quot; data-vue-if=&quot;resumeData.award &amp;&amp; resumeData.award.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;获奖情况&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.award&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;name&quot; data-vue-text=&quot;item.name&quot;&gt;优秀员工奖&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot;&gt;
                            &lt;span data-field=&quot;organization&quot; data-vue-text=&quot;item.organization&quot;&gt;ABC科技有限公司&lt;/span&gt;
                            &lt;span data-vue-if=&quot;item.level&quot;&gt; · &lt;/span&gt;
                            &lt;span data-field=&quot;level&quot; data-vue-text=&quot;item.level&quot;&gt;公司级&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot; data-field=&quot;date&quot; data-vue-text=&quot;item.date&quot;&gt;2023-12&lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot;&gt;
                    因在项目开发中表现突出，技术能力强，团队协作能力佳，获得公司年度优秀员工奖励。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 7. 证书资质模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;certificate&quot; data-vue-if=&quot;resumeData.certificate &amp;&amp; resumeData.certificate.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;证书资质&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.certificate&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;name&quot; data-vue-text=&quot;item.name&quot;&gt;软件设计师&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot;&gt;
                            &lt;span data-field=&quot;organization&quot; data-vue-text=&quot;item.organization&quot;&gt;中国计算机技术职业资格网&lt;/span&gt;
                            &lt;span data-vue-if=&quot;item.number&quot;&gt; · 证书编号: &lt;/span&gt;
                            &lt;span data-field=&quot;number&quot; data-vue-text=&quot;item.number&quot;&gt;SD202312345&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot; data-field=&quot;date&quot; data-vue-text=&quot;item.date&quot;&gt;2023-05&lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot;&gt;
                    通过国家软件设计师资格考试，具备软件系统分析、设计和开发的专业能力。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 8. 语言能力模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;language&quot; data-vue-if=&quot;resumeData.language &amp;&amp; resumeData.language.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;语言能力&lt;/h2&gt;
            &lt;div class=&quot;simple-list&quot;&gt;
                &lt;div class=&quot;simple-item&quot; data-vue-for=&quot;item in resumeData.language&quot; data-vue-key=&quot;item.id&quot;&gt;
                    &lt;!-- 核心字段：始终显示 --&gt;
                    &lt;div class=&quot;item-title&quot; data-field=&quot;name&quot; data-vue-text=&quot;item.name&quot;&gt;英语&lt;/div&gt;
                    &lt;div class=&quot;item-subtitle&quot;&gt;
                        &lt;!-- 可选字段：有数据才显示 --&gt;
                        &lt;span data-field=&quot;level&quot; data-vue-text=&quot;item.level&quot; data-vue-if=&quot;item.level&quot;&gt;熟练&lt;/span&gt;
                        &lt;span data-vue-if=&quot;item.level &amp;&amp; item.certificate&quot;&gt; · &lt;/span&gt;
                        &lt;span data-field=&quot;certificate&quot; data-vue-text=&quot;item.certificate&quot; data-vue-if=&quot;item.certificate&quot;&gt;CET-6&lt;/span&gt;
                    &lt;/div&gt;
                    &lt;!-- 语言描述：可选字段 --&gt;
                    &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot; data-vue-if=&quot;item.description&quot;&gt;
                        具备良好的英语读写能力，能够阅读英文技术文档，与外国同事进行日常交流。
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 9. 实习经历模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;internship&quot; data-vue-if=&quot;resumeData.internship &amp;&amp; resumeData.internship.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;实习经历&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.internship&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;company&quot; data-vue-text=&quot;item.company&quot;&gt;腾讯科技有限公司&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot; data-field=&quot;position&quot; data-vue-text=&quot;item.position&quot;&gt;前端开发实习生&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot;&gt;
                        &lt;!-- 可选字段：有数据才显示 --&gt;
                        &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot; data-vue-if=&quot;item.startDate&quot;&gt;2019-07&lt;/span&gt;
                        &lt;span data-vue-if=&quot;item.startDate &amp;&amp; item.endDate&quot;&gt; ~ &lt;/span&gt;
                        &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot; data-vue-if=&quot;item.endDate&quot;&gt;2019-09&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- 实习描述：可选字段 --&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot; data-vue-if=&quot;item.description&quot;&gt;
                    在微信事业群实习，参与微信小程序开发工具的前端开发工作。
                    学习并实践了React技术栈，参与了代码重构和性能优化项目。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 10. 志愿经历模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;volunteer_experience&quot; data-vue-if=&quot;resumeData.volunteer_experience &amp;&amp; resumeData.volunteer_experience.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;志愿经历&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.volunteer_experience&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;organization&quot; data-vue-text=&quot;item.organization&quot;&gt;北京市红十字会&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot;&gt;
                            &lt;span data-field=&quot;role&quot; data-vue-text=&quot;item.role&quot;&gt;志愿者&lt;/span&gt;
                            &lt;span data-vue-if=&quot;item.location&quot;&gt; · &lt;/span&gt;
                            &lt;span data-field=&quot;location&quot; data-vue-text=&quot;item.location&quot;&gt;北京市朝阳区&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot;&gt;
                        &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot;&gt;2018-06&lt;/span&gt; ~ 
                        &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot;&gt;2020-06&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot;&gt;
                    参与社区志愿服务活动，为老人提供技术支持，教授智能手机和电脑的基本使用方法。
                    累计志愿服务时长超过100小时，获得优秀志愿者称号。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 11. 论文发表模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;publication&quot; data-vue-if=&quot;resumeData.publication &amp;&amp; resumeData.publication.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;论文发表&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.publication&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;title&quot; data-vue-text=&quot;item.title&quot;&gt;基于深度学习的图像识别算法研究&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot;&gt;
                            &lt;span data-field=&quot;type&quot; data-vue-text=&quot;item.type&quot;&gt;学术论文&lt;/span&gt; · 
                            &lt;span data-field=&quot;journal&quot; data-vue-text=&quot;item.journal&quot;&gt;《计算机工程》&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot; data-field=&quot;date&quot; data-vue-text=&quot;item.date&quot;&gt;2020-03&lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot;&gt;
                    第一作者发表学术论文，研究基于卷积神经网络的图像分类算法，
                    在CIFAR-10数据集上取得了较好的实验结果。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 12. 科研经历模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;research_experience&quot; data-vue-if=&quot;resumeData.research_experience &amp;&amp; resumeData.research_experience.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;科研经历&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.research_experience&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;topic&quot; data-vue-text=&quot;item.topic&quot;&gt;机器学习在图像处理中的应用&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot;&gt;
                            &lt;span data-field=&quot;role&quot; data-vue-text=&quot;item.role&quot;&gt;主要研究员&lt;/span&gt; · 
                            &lt;span data-field=&quot;organization&quot; data-vue-text=&quot;item.organization&quot;&gt;北京理工大学计算机学院&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot;&gt;
                        &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot;&gt;2019-03&lt;/span&gt; ~ 
                        &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot;&gt;2020-06&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot;&gt;
                    参与导师的科研项目，研究深度学习算法在图像识别和处理中的应用。
                    负责算法实现、实验设计和数据分析，取得了较好的研究成果。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 13. 培训经历模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;training&quot; data-vue-if=&quot;resumeData.training &amp;&amp; resumeData.training.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;培训经历&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.training&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;name&quot; data-vue-text=&quot;item.name&quot;&gt;React高级开发培训&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot; data-field=&quot;organization&quot; data-vue-text=&quot;item.organization&quot;&gt;极客时间&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot;&gt;
                        &lt;span data-field=&quot;startDate&quot; data-vue-text=&quot;item.startDate&quot;&gt;2021-06&lt;/span&gt; ~ 
                        &lt;span data-field=&quot;endDate&quot; data-vue-text=&quot;item.endDate&quot;&gt;2021-08&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot;&gt;
                    系统学习React高级特性，包括Hooks、Context、性能优化等内容。
                    完成了多个实战项目，提升了React开发能力。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 14. 作品集模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;portfolio&quot; data-vue-if=&quot;resumeData.portfolio &amp;&amp; resumeData.portfolio.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;作品集&lt;/h2&gt;
            &lt;div class=&quot;list-item&quot; data-vue-for=&quot;item in resumeData.portfolio&quot; data-vue-key=&quot;item.id&quot;&gt;
                &lt;div class=&quot;item-header&quot;&gt;
                    &lt;div&gt;
                        &lt;div class=&quot;item-title&quot; data-field=&quot;title&quot; data-vue-text=&quot;item.title&quot;&gt;个人博客网站&lt;/div&gt;
                        &lt;div class=&quot;item-subtitle&quot; data-field=&quot;type&quot; data-vue-text=&quot;item.type&quot;&gt;网站开发&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class=&quot;item-date&quot; data-field=&quot;date&quot; data-vue-text=&quot;item.date&quot;&gt;2023-01&lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;item-content&quot;&gt;
                    &lt;p data-field=&quot;description&quot; data-vue-text=&quot;item.description&quot;&gt;
                        使用Nuxt.js开发的个人博客网站，支持文章发布、评论、搜索等功能。
                        采用响应式设计，支持多终端访问。
                    &lt;/p&gt;
                    &lt;p data-vue-if=&quot;item.link&quot;&gt;
                        &lt;strong&gt;链接：&lt;/strong&gt;
                        &lt;a href=&quot;#&quot; data-field=&quot;link&quot; data-vue-text=&quot;item.link&quot; data-vue-href=&quot;item.link&quot;&gt;https://zhangsan.blog&lt;/a&gt;
                    &lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 15. 兴趣爱好模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;hobbies&quot; data-vue-if=&quot;resumeData.hobbies &amp;&amp; resumeData.hobbies.length &gt; 0&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;兴趣爱好&lt;/h2&gt;
            &lt;div class=&quot;simple-list&quot;&gt;
                &lt;div class=&quot;simple-item&quot; data-vue-for=&quot;hobby in resumeData.hobbies&quot; data-vue-key=&quot;hobby.id&quot;&gt;
                    &lt;div class=&quot;item-title&quot; data-field=&quot;name&quot; data-vue-text=&quot;hobby.name&quot;&gt;摄影&lt;/div&gt;
                    &lt;div class=&quot;item-content&quot; data-field=&quot;description&quot; data-vue-text=&quot;hobby.description&quot;&gt;
                        热爱摄影，擅长风景和人像摄影，具有较好的审美能力和创意思维。
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 16. 自我评价模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;self_evaluation&quot; data-vue-if=&quot;resumeData.self_evaluation &amp;&amp; resumeData.self_evaluation.content&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;自我评价&lt;/h2&gt;
            &lt;div class=&quot;text-content&quot; data-field=&quot;content&quot; data-vue-text=&quot;resumeData.self_evaluation.content&quot;&gt;
                本人性格开朗，工作认真负责，具有较强的学习能力和团队协作精神。
                在前端开发领域有着扎实的技术基础和丰富的项目经验，
                能够快速适应新技术和新环境，具备良好的问题解决能力。
                希望能够在新的工作岗位上发挥自己的专业技能，与团队共同成长。
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 17. 自荐信模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;cover_letter&quot; data-vue-if=&quot;resumeData.cover_letter &amp;&amp; resumeData.cover_letter.content&quot;&gt;
            &lt;h2 class=&quot;section-title&quot;&gt;自荐信&lt;/h2&gt;
            &lt;div class=&quot;text-content&quot; data-field=&quot;content&quot; data-vue-text=&quot;resumeData.cover_letter.content&quot;&gt;
                尊敬的招聘负责人，您好！我是一名有着4年前端开发经验的工程师，
                在看到贵公司的招聘信息后，我对这个职位非常感兴趣。
                我具备扎实的技术基础和丰富的项目经验，相信能够为贵公司的发展贡献自己的力量。
                期待能有机会与您面谈，详细介绍我的技能和经验。谢谢！
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 18. 自定义模块 --&gt;
        &lt;section class=&quot;section&quot; data-module=&quot;custom_1704067200000&quot; data-vue-if=&quot;customModule &amp;&amp; customModule.content&quot;&gt;
            &lt;h2 class=&quot;section-title&quot; data-field=&quot;title&quot; data-vue-text=&quot;customModule.title&quot;&gt;其他信息&lt;/h2&gt;
            &lt;div class=&quot;text-content&quot; data-field=&quot;content&quot; data-vue-text=&quot;customModule.content&quot;&gt;
                这里可以添加任何自定义的内容，比如特殊经历、补充说明等。
                自定义模块的ID格式为 custom_ + 时间戳，确保唯一性。
            &lt;/div&gt;
        &lt;/section&gt;
        
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre></div></div></section><section id="validation" class="spec-section" data-v-0e4d416b><h2 class="section-title" data-v-0e4d416b>9. 验证清单</h2><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>基础文档结构验证</h3><div class="checklist" data-v-0e4d416b><h4 data-v-0e4d416b>文档结构检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> DOCTYPE声明正确</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 字符编码设置为UTF-8</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含完整的html、head、body标签</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 根容器包含class=&quot;resume-template&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 根容器包含data-module-root=&quot;true&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> title标签包含简历标题</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> meta viewport标签设置正确</li></ul><h4 data-v-0e4d416b>样式检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含CSS样式在style标签内</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 定义了.resume-template根样式</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用相对单位（rem、em、%）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含响应式设计断点</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含打印样式优化</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 字体设置为系统字体或Web安全字体</li></ul></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>模块定义验证</h3><div class="checklist" data-v-0e4d416b><h4 data-v-0e4d416b>模块结构检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 每个模块都有data-module属性</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 模块名称使用规范的值（18个标准模块）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 必须包含basic_info模块</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 模块ID命名正确（大部分单数，skills和hobbies复数）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 自定义模块ID格式为custom_[时间戳]</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 每个模块包含在section标签内</li></ul><h4 data-v-0e4d416b>模块完整性检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含的模块数量合理（建议3-8个主要模块）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 模块顺序符合简历逻辑</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 各模块间有明确的视觉分隔</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 模块标题清晰明确</li></ul></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>字段绑定验证</h3><div class="checklist" data-v-0e4d416b><h4 data-v-0e4d416b>基本绑定检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 每个字段都有data-field属性</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 每个字段都有data-vue-text属性</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 表达式格式正确</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 循环列表使用data-vue-for</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 循环项使用data-vue-key</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 图片字段使用data-vue-src</li></ul><h4 data-v-0e4d416b>表达式正确性检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 基本信息使用resumeData.basic_info.{字段名}</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 循环模块使用item.{字段名}</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 技能模块使用skill.{字段名}</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 兴趣爱好使用hobby.{字段名}</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 自我评价使用resumeData.self_evaluation.content</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 自荐信使用resumeData.cover_letter.content</li></ul><h4 data-v-0e4d416b>条件渲染检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 非必需模块添加了data-vue-if模块级条件</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 可选字段添加了data-vue-if字段级条件</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 核心字段（如姓名、学校名）不加条件</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 数组型模块条件：resumeData.module &amp;&amp; resumeData.module.length &gt; 0</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 文本型模块条件：resumeData.module &amp;&amp; resumeData.module.content</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 基本字段条件：resumeData.basic_info.字段名</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 循环项字段条件：item.字段名</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 时间分隔符条件：item.startDate &amp;&amp; item.endDate</li></ul></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>各模块字段完整性验证</h3><div class="checklist" data-v-0e4d416b><h4 data-v-0e4d416b>基本信息模块（basic_info）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含name字段（必需）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含phone字段（必需）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含email字段（必需）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含photo、age、currentCity、intendedCity等扩展字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 支持自定义社交信息customSocials</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 支持自定义信息customInfos</li></ul><h4 data-v-0e4d416b>工作经历模块（work_experience）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.work_experience&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含company、position、startDate、endDate字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含department字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含description字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 字段绑定正确（item.字段名）</li></ul><h4 data-v-0e4d416b>教育经历模块（education）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.education&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含school、major、degree字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含startDate、endDate字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含customDegree字段（自定义学历）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含description字段</li></ul><h4 data-v-0e4d416b>项目经历模块（project）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.project&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含name、role、company字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含startDate、endDate字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含description字段</li></ul><h4 data-v-0e4d416b>技能特长模块（skills）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;skill in resumeData.skills&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含name字段（skill.name）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含level字段（skill.level）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含proficiency字段（skill.proficiency）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含displayMode字段（skill.displayMode）</li></ul><h4 data-v-0e4d416b>语言能力模块（language）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.language&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含name、level字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含certificate、description字段</li></ul><h4 data-v-0e4d416b>获奖情况模块（award）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.award&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含name、date、organization字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含level、description字段</li></ul><h4 data-v-0e4d416b>证书资质模块（certificate）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.certificate&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含name、date、organization字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含number、description字段</li></ul><h4 data-v-0e4d416b>实习经历模块（internship）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.internship&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含company、position字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含startDate、endDate、description字段</li></ul><h4 data-v-0e4d416b>志愿经历模块（volunteer_experience）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.volunteer_experience&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含organization、role、location字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含startDate、endDate、description字段</li></ul><h4 data-v-0e4d416b>论文发表模块（publication）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.publication&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含title、type、journal字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含date、description字段</li></ul><h4 data-v-0e4d416b>科研经历模块（research_experience）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.research_experience&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含topic、role、organization字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含startDate、endDate、description字段</li></ul><h4 data-v-0e4d416b>培训经历模块（training）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.training&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含course、company字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含startDate、endDate、description字段</li></ul><h4 data-v-0e4d416b>作品集模块（portfolio）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.portfolio&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含title、type、url字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含date、description字段</li></ul><h4 data-v-0e4d416b>兴趣爱好模块（hobbies）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;hobby in resumeData.hobbies&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含name字段（hobby.name）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含level、description字段</li></ul><h4 data-v-0e4d416b>自我评价模块（self_evaluation）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-field=&quot;content&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-text=&quot;resumeData.self_evaluation.content&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 不使用循环结构</li></ul><h4 data-v-0e4d416b>自荐信模块（cover_letter）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-field=&quot;content&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-text=&quot;resumeData.cover_letter.content&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 不使用循环结构</li></ul><h4 data-v-0e4d416b>自定义模块（custom_*）：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 模块ID格式为custom_[时间戳]</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 使用data-vue-for=&quot;item in resumeData.custom_*.items&quot;</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含name、role字段</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 包含startDate、endDate、content字段</li></ul></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>数据绑定一致性验证</h3><div class="checklist" data-v-0e4d416b><h4 data-v-0e4d416b>循环变量检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 技能模块使用&quot;skill&quot;变量名</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 兴趣爱好模块使用&quot;hobby&quot;变量名</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 其他模块使用&quot;item&quot;变量名</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 所有循环都包含data-vue-key=&quot;变量.id&quot;</li></ul><h4 data-v-0e4d416b>字段命名检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 所有字段使用camelCase命名</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 时间字段命名为startDate、endDate</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 描述字段命名为description</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 自定义字段数组命名正确</li></ul></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>CSS变量使用验证</h3><div class="checklist" data-v-0e4d416b><h4 data-v-0e4d416b>基础CSS变量检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 页面布局使用了margin变量（--resume-margin-*或--margin-*）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 字体样式使用了font变量（--resume-font-*或--font-*）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 文本颜色使用了color变量（--resume-text-color或--text-color）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 主色调使用了primary变量（--resume-primary-color或--primary-color）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 次要颜色使用了secondary变量（--resume-secondary-color或--secondary-color）</li></ul><h4 data-v-0e4d416b>间距CSS变量检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 模块间距使用了spacing变量（--resume-module-spacing或--module-spacing）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 条目间距使用了spacing变量（--resume-item-spacing或--item-spacing）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 段落间距使用了spacing变量（--resume-paragraph-spacing或--paragraph-spacing）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 内边距使用了padding变量（--resume-*-padding或--*-padding）</li></ul><h4 data-v-0e4d416b>CSS变量规范性检查：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 没有硬编码的样式值（字体大小、颜色、间距等）</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 推荐使用带resume-前缀的新版变量名</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 计算值正确使用calc()函数</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 变量名拼写正确，无语法错误</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> CSS变量在:root选择器中正确定义</li></ul></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>兼容性和性能验证</h3><div class="checklist" data-v-0e4d416b><h4 data-v-0e4d416b>浏览器兼容性：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> CSS属性兼容主流浏览器</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 避免使用实验性CSS特性</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 提供必要的CSS前缀</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 字体回退方案完整</li></ul><h4 data-v-0e4d416b>性能优化：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> CSS代码简洁高效</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 避免过度嵌套选择器</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 合理使用CSS动画和过渡</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 图片尺寸和格式优化</li></ul><h4 data-v-0e4d416b>可访问性：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 文字颜色对比度符合标准</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 字体大小适合阅读</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 链接有明确的视觉标识</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 结构语义化正确</li></ul></div></div><div class="requirement-box" data-v-0e4d416b><h3 data-v-0e4d416b>最终质量验证</h3><div class="checklist" data-v-0e4d416b><h4 data-v-0e4d416b>转换测试：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 在转换系统中测试成功</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 所有字段正确显示数据</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 循环渲染正常工作</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 样式在预览中正确应用</li></ul><h4 data-v-0e4d416b>多场景测试：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 空数据情况处理正确</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 大量数据情况下布局正常</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 不同模块组合下显示正常</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 导出PDF格式正确</li></ul><h4 data-v-0e4d416b>用户体验：</h4><ul class="checklist-items" data-v-0e4d416b><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 整体布局美观协调</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 信息层次清晰</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 视觉重点突出</li><li data-v-0e4d416b><input type="checkbox" data-v-0e4d416b> 符合简历阅读习惯</li></ul></div></div></section></div>`,1))])}}},C=u(D,[["__scopeId","data-v-0e4d416b"]]);export{C as default};
