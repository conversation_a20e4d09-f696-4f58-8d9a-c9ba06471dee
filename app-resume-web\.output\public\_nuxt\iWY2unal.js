import{_ as v}from"./BXnw39BI.js";import{_ as o}from"./DlAUqK2U.js";import{c as l,a as t,f as n,b as i,w as s,o as p,d}from"./CURHyiUL.js";const c={class:"test-page"},r={class:"test-section"},m={class:"test-section"},_={__name:"html-specification-test",setup(h){return(u,a)=>{const e=v;return p(),l("div",c,[a[4]||(a[4]=t("h1",null,"HTML规范页面测试",-1)),t("div",r,[a[1]||(a[1]=t("h2",null,"页面链接测试",-1)),i(e,{to:"/admin/template/html-specification",class:"test-link"},{default:s(()=>a[0]||(a[0]=[d(" 访问HTML规范页面 ")])),_:1,__:[0]})]),t("div",m,[a[3]||(a[3]=t("h2",null,"转换页面链接测试",-1)),i(e,{to:"/admin/template/converter",class:"test-link"},{default:s(()=>a[2]||(a[2]=[d(" 访问转换页面（包含规范按钮） ")])),_:1,__:[2]})]),a[5]||(a[5]=n('<div class="test-section" data-v-e24909a9><h2 data-v-e24909a9>功能验证清单</h2><ul class="test-checklist" data-v-e24909a9><li data-v-e24909a9>✅ HTML规范页面创建完成</li><li data-v-e24909a9>✅ 转换页面添加规范按钮</li><li data-v-e24909a9>✅ 页面样式和布局正确</li><li data-v-e24909a9>✅ 包含完整的规范文档</li><li data-v-e24909a9>✅ 包含示例代码和验证清单</li></ul></div><div class="test-section" data-v-e24909a9><h2 data-v-e24909a9>规范内容概览</h2><div class="content-overview" data-v-e24909a9><div class="overview-item" data-v-e24909a9><h3 data-v-e24909a9>1. 基本文档结构</h3><p data-v-e24909a9>DOCTYPE、HTML标签、字符编码等基本要求</p></div><div class="overview-item" data-v-e24909a9><h3 data-v-e24909a9>2. 模块定义规范</h3><p data-v-e24909a9>data-module属性、支持的模块类型</p></div><div class="overview-item" data-v-e24909a9><h3 data-v-e24909a9>3. 字段绑定规范</h3><p data-v-e24909a9>data-field、data-vue-text等属性使用</p></div><div class="overview-item" data-v-e24909a9><h3 data-v-e24909a9>4. 各模块字段详解</h3><p data-v-e24909a9>基本信息、工作经历、教育经历等模块字段</p></div><div class="overview-item" data-v-e24909a9><h3 data-v-e24909a9>5. 完整示例代码</h3><p data-v-e24909a9>可直接使用的HTML模板示例</p></div><div class="overview-item" data-v-e24909a9><h3 data-v-e24909a9>6. 验证清单</h3><p data-v-e24909a9>转换前的检查项目</p></div></div></div>',2))])}}},T=o(_,[["__scopeId","data-v-e24909a9"]]);export{T as default};
