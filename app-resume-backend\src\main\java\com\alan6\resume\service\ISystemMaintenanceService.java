package com.alan6.resume.service;

import com.alan6.resume.dto.system.CacheClearRequest;
import com.alan6.resume.dto.system.CacheClearResponse;
import com.alan6.resume.dto.system.MaintenanceRequest;
import com.alan6.resume.dto.system.MaintenanceResponse;

/**
 * 系统维护服务接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ISystemMaintenanceService {

    /**
     * 清理缓存
     *
     * @param request 缓存清理请求
     * @return 清理结果
     */
    CacheClearResponse clearCache(CacheClearRequest request);

    /**
     * 设置维护模式
     *
     * @param request 维护模式请求
     * @return 设置结果
     */
    MaintenanceResponse setMaintenanceMode(MaintenanceRequest request);

    /**
     * 获取当前维护状态
     *
     * @return 维护状态
     */
    MaintenanceResponse getMaintenanceStatus();

    /**
     * 检查系统是否处于维护模式
     *
     * @return 是否维护模式
     */
    boolean isMaintenanceMode();
} 