package com.alan6.resume.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户注册响应DTO
 * 
 * 主要功能：
 * 1. 返回用户注册成功后的响应信息
 * 2. 包含访问Token、刷新Token和用户基本信息
 * 3. 提供给前端进行后续请求认证和用户信息展示
 * 
 * 该类封装了注册成功后需要返回给客户端的所有必要信息，
 * 包括用户身份认证所需的Token以及用户的基本信息。
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "用户注册响应", description = "用户注册成功后的响应数据")
public class RegisterResponse {

    /**
     * 用户ID
     * 新注册用户的唯一标识符
     */
    @Schema(description = "新注册用户的唯一标识符", example = "1001")
    private Long userId;

    /**
     * 访问Token
     * 用于后续API请求的身份认证令牌
     * 
     * 访问Token是用户在系统中的身份凭证，
     * 前端需要在每次请求时在Header中携带此Token
     */
    @Schema(description = "用于API请求认证的访问令牌", example = "eyJhbGciOiJIUzI1NiIs...")
    private String accessToken;

    /**
     * 刷新Token
     * 用于在访问Token过期时获取新的访问Token
     * 
     * 刷新Token的有效期通常比访问Token更长，
     * 当访问Token过期时，可以使用刷新Token来获取新的访问Token
     */
    @Schema(description = "用于刷新访问令牌的刷新令牌", example = "eyJhbGciOiJIUzI1NiIs...")
    private String refreshToken;

    /**
     * Token过期时间（秒）
     * 访问Token的有效期长度
     */
    @Schema(description = "访问令牌的有效期长度（秒）", example = "7200")
    private Long expiresIn;

    /**
     * Token过期时间戳
     * 访问Token的具体过期时间
     */
    @Schema(description = "访问令牌的过期时间戳")
    private LocalDateTime tokenExpireTime;

    /**
     * 用户基本信息
     * 新注册用户的基本信息
     */
    @Schema(description = "新注册用户的基本信息")
    private UserInfo userInfo;

    /**
     * 用户基本信息内部类
     * 
     * 封装了用户的基本信息，包括用户ID、手机号、头像等
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "用户基本信息", description = "用户的基本信息")
    public static class UserInfo {

        /**
         * 用户ID
         * 用户的唯一标识符
         */
        @Schema(description = "用户ID", example = "1001")
        private Long id;

        /**
         * 手机号
         * 用户注册时使用的手机号（脱敏显示）
         */
        @Schema(description = "用户手机号（脱敏显示）", example = "138****8000")
        private String phone;

        /**
         * 用户名
         * 系统为用户生成的用户名
         */
        @Schema(description = "用户名", example = "user_138****8000")
        private String username;

        /**
         * 昵称
         * 用户的显示昵称，初始值为手机号
         */
        @Schema(description = "用户昵称", example = "138****8000")
        private String nickname;

        /**
         * 头像URL
         * 用户头像地址，新用户为默认头像
         */
        @Schema(description = "用户头像地址", example = "https://cdn.example.com/avatar/default.jpg")
        private String avatar;

        /**
         * 邮箱
         * 用户绑定的邮箱地址
         */
        @Schema(description = "用户邮箱", example = "<EMAIL>")
        private String email;

        /**
         * 会员等级
         * 用户的会员等级，新用户为0（普通用户）
         */
        @Schema(description = "会员等级，0-普通用户", example = "0")
        private Integer membershipLevel;

        /**
         * 会员到期时间
         * 会员服务的到期时间
         */
        @Schema(description = "会员到期时间")
        private LocalDateTime membershipExpireTime;

        /**
         * 注册时间
         * 用户的注册时间
         */
        @Schema(description = "注册时间")
        private LocalDateTime registerTime;

        /**
         * 注册平台
         * 用户注册的平台来源
         */
        @Schema(description = "注册平台", example = "web")
        private String registerPlatform;
    }

    /**
     * 创建注册成功响应
     * 
     * 根据用户信息和Token构建注册成功响应对象
     * 
     * @param accessToken 访问Token
     * @param refreshToken 刷新Token
     * @param expiresIn Token过期时间（秒）
     * @param user 用户实体对象
     * @return 注册响应对象
     */
    public static RegisterResponse createSuccessResponse(String accessToken, String refreshToken, 
                                                       Long expiresIn, Object user) {
        // 构建Token过期时间
        LocalDateTime tokenExpireTime = LocalDateTime.now().plusSeconds(expiresIn);
        
        // 构建响应对象
        return RegisterResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .expiresIn(expiresIn)
                .tokenExpireTime(tokenExpireTime)
                .build();
    }

    /**
     * 手机号脱敏处理
     * 
     * 将手机号中间4位替换为*号
     * 
     * @param phone 原始手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
} 