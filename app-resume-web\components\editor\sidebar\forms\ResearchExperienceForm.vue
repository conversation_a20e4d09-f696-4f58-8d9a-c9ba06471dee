<template>
  <div class="research-experience-form">
    <div class="form-header">
      <h4 class="form-title">研究经历</h4>
      <p class="form-desc">添加您参与的研究项目和学术活动</p>
    </div>
    
    <div v-if="formData.research && formData.research.length > 0" class="research-list">
      <div v-for="(research, index) in formData.research" :key="index" class="research-item">
        <div class="research-header">
          <div class="form-group">
            <label class="form-label">研究课题/方向</label>
            <input
              v-model="research.topic"
              type="text"
              class="form-input"
              placeholder="如：绿色社区创建方案体系研究"
              @input="handleResearchChange"
            />
          </div>
          <div class="form-group">
            <label class="form-label">担任角色</label>
            <input
              v-model="research.role"
              type="text"
              class="form-input"
              placeholder="如：实验室成员"
              @input="handleResearchChange"
            />
          </div>
          <button 
            class="delete-btn"
            @click="removeResearch(index)"
            title="删除研究经历"
          >
            ×
          </button>
        </div>
        
        <div class="research-details">
          <div class="form-group">
            <label class="form-label">所在公司/组织/实验室</label>
            <input
              v-model="research.organization"
              type="text"
              class="form-input"
              placeholder="如：***实验室"
              @input="handleResearchChange"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">研究时间</label>
            <div class="date-range-group">
              <div class="date-picker-wrapper">
                <input
                  v-model="research.startDate"
                  type="text"
                  class="date-input"
                  placeholder="2023-01"
                  readonly
                  @click="showDatePicker(`${index}_start`)"
                />
                <div 
                  v-if="activeDatePicker === `${index}_start`" 
                  class="date-picker-dropdown"
                >
                  <div class="date-picker-header">
                    <button type="button" @click="changeYear(-1)">‹</button>
                    <span>{{ currentYear }}</span>
                    <button type="button" @click="changeYear(1)">›</button>
                  </div>
                  <div class="month-grid">
                    <button
                      v-for="month in 12"
                      :key="month"
                      type="button"
                      class="month-btn"
                      :class="{ active: isSelectedMonth(`${index}_start`, month) }"
                      @click="selectDate(`${index}_start`, currentYear, month)"
                    >
                      {{ month }}月
                    </button>
                  </div>
                </div>
              </div>
              <span class="date-separator">至</span>
              <div class="date-picker-wrapper">
                <input
                  v-model="research.endDate"
                  type="text"
                  class="date-input"
                  placeholder="2024-06 或 至今"
                  readonly
                  @click="showDatePicker(`${index}_end`)"
                />
                <div 
                  v-if="activeDatePicker === `${index}_end`" 
                  class="date-picker-dropdown"
                >
                  <div class="date-picker-header">
                    <button type="button" @click="changeYear(-1)">‹</button>
                    <span>{{ currentYear }}</span>
                    <button type="button" @click="changeYear(1)">›</button>
                  </div>
                  <div class="month-grid">
                    <button
                      v-for="month in 12"
                      :key="month"
                      type="button"
                      class="month-btn"
                      :class="{ active: isSelectedMonth(`${index}_end`, month) }"
                      @click="selectDate(`${index}_end`, currentYear, month)"
                    >
                      {{ month }}月
                    </button>
                    <button
                      type="button"
                      class="month-btn present-btn"
                      @click="selectPresent(`${index}_end`)"
                    >
                      至今
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">研究描述</label>
            <RichTextEditor
              v-model="research.description"
              placeholder="请输入研究内容、主要成果、发表论文、获得奖项等..."
              min-height="120px"
              @update:modelValue="handleResearchChange"
            />
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无研究经历信息</p>
      <p class="empty-hint">点击下方按钮添加研究经历</p>
    </div>
    
    <div class="form-actions">
      <button 
        class="add-btn" 
        @click="addResearch"
        :disabled="formData.research.length >= 10"
      >
        <Icon name="plus" size="sm" />
        <span>添加研究经历</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, ref, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ research: [] })
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = reactive({
  research: []
})

// 防抖定时器
let debounceTimer = null

// 日期选择器状态
const activeDatePicker = ref('')
const currentYear = ref(new Date().getFullYear())

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.research) {
    formData.research = [...newData.research]
  } else {
    formData.research = []
  }
}, { immediate: true, deep: true })

/**
 * 处理研究经历变化 - 实时更新
 */
const handleResearchChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的定时器，300ms后触发更新
  debounceTimer = setTimeout(() => {
    emit('update', { research: [...formData.research] })
  }, 300)
}



/**
 * 添加研究经历
 */
const addResearch = () => {
  if (formData.research.length >= 10) {
    return
  }
  
  formData.research.push({
    topic: '',
    role: '',
    organization: '',
    startDate: '',
    endDate: '',
    isOngoing: false,
    description: ''
  })
  handleResearchChange()
}

/**
 * 显示日期选择器
 */
const showDatePicker = (pickerId) => {
  activeDatePicker.value = pickerId
  // 根据当前值设置年份
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.research[index].startDate 
    : formData.research[index].endDate
  
  if (currentValue && currentValue !== '至今') {
    const year = parseInt(currentValue.split('-')[0])
    if (year) {
      currentYear.value = year
    }
  }
}

/**
 * 改变年份
 */
const changeYear = (delta) => {
  currentYear.value += delta
}

/**
 * 选择日期
 */
const selectDate = (pickerId, year, month) => {
  const dateStr = `${year}-${String(month).padStart(2, '0')}`
  const [index, type] = pickerId.split('_')
  
  if (type === 'start') {
    formData.research[index].startDate = dateStr
  } else {
    formData.research[index].endDate = dateStr
    formData.research[index].isOngoing = false
  }
  
  activeDatePicker.value = ''
  handleResearchChange()
}

/**
 * 选择"至今"
 */
const selectPresent = (pickerId) => {
  const [index] = pickerId.split('_')
  formData.research[index].endDate = '至今'
  formData.research[index].isOngoing = true
  activeDatePicker.value = ''
  handleResearchChange()
}

/**
 * 判断是否为选中的月份
 */
const isSelectedMonth = (pickerId, month) => {
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.research[index].startDate 
    : formData.research[index].endDate
    
  if (!currentValue || currentValue === '至今') return false
  
  const [year, selectedMonth] = currentValue.split('-')
  return parseInt(year) === currentYear.value && parseInt(selectedMonth) === month
}

/**
 * 点击外部关闭日期选择器
 */
const handleClickOutside = (event) => {
  if (!event.target.closest('.date-picker-wrapper')) {
    activeDatePicker.value = ''
  }
}

// 挂载时添加全局点击监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 卸载时移除监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

/**
 * 删除研究经历
 */
const removeResearch = (index) => {
  formData.research.splice(index, 1)
  handleResearchChange()
}
</script>

<style scoped>
.research-experience-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.research-list {
  @apply space-y-4 mb-6;
}

.research-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-4;
}

.research-header {
  @apply flex items-start gap-4;
}

.form-group {
  @apply flex-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.delete-btn {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold mt-6;
}

.research-details {
  @apply space-y-4;
}

.date-range-group {
  @apply flex items-center gap-3;
}

.date-picker-wrapper {
  @apply relative flex-1;
}

.date-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer;
}

.date-picker-dropdown {
  @apply absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10;
  min-width: 240px;
}

.date-picker-header {
  @apply flex items-center justify-between px-4 py-3 border-b border-gray-200;
}

.date-picker-header button {
  @apply w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors duration-200;
}

.month-grid {
  @apply grid grid-cols-4 gap-2 p-3;
}

.month-btn {
  @apply px-3 py-2 text-sm text-gray-700 hover:bg-blue-100 hover:text-blue-800 rounded transition-colors duration-200;
  white-space: nowrap;
  min-width: 48px;
}

.month-btn.active {
  @apply bg-blue-500 text-white;
}

.month-btn.present-btn {
  @apply col-span-4 bg-green-100 text-green-800 hover:bg-green-200;
}

.date-separator {
  @apply text-sm text-gray-500 flex-shrink-0;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-sm text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}

.limit-hint {
  @apply text-xs text-gray-500;
}
</style> 