<template>
  <div class="template-upload-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="breadcrumb">
        <span class="breadcrumb-item">管理后台</span>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item">模板管理</span>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">模板上传</span>
      </div>
      <h1 class="page-title">简历模板上传</h1>
      <p class="page-description">支持批量上传Vue组件、CSS样式、预览图等文件，自动创建模板记录</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="upload-container">
      <!-- 基本信息表单 -->
      <div class="form-section">
        <h2 class="section-title">基本信息</h2>
        <form @submit.prevent="handleSubmit" class="upload-form">
          <!-- 第一行：模板名称和代码 -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label required">模板名称</label>
              <div class="input-wrapper">
                <input
                  v-model="formData.name"
                  type="text"
                  class="form-input"
                  placeholder="请输入模板名称，如：商务简约模板"
                  :class="{ 'error': errors.name }"
                  @blur="validateName"
                  @input="onTemplateNameInput"
                />
                <div class="input-status">
                  <span v-if="nameCheckStatus === 'checking'" class="status-checking">检查中...</span>
                  <span v-else-if="nameCheckStatus === 'available'" class="status-success">✓ 可用</span>
                  <span v-else-if="nameCheckStatus === 'unavailable'" class="status-error">✗ 已存在</span>
                </div>
              </div>
              <span v-if="errors.name" class="error-message">{{ errors.name }}</span>
            </div>

            <div class="form-group">
              <label class="form-label required">模板代码</label>
              <input
                v-model="formData.templateCode"
                type="text"
                class="form-input"
                placeholder="请输入模板代码，如：business-simple"
                :class="{ 'error': errors.templateCode }"
                @blur="validateTemplateCode"
                @input="onTemplateCodeInput"
              />
              <span v-if="errors.templateCode" class="error-message">{{ errors.templateCode }}</span>
              <span v-if="codeCheckStatus === 'checking'" class="info-message">正在检查可用性...</span>
              <span v-if="codeCheckStatus === 'available'" class="success-message">✓ 代码可用</span>
              <span v-if="codeCheckStatus === 'unavailable'" class="error-message">✗ 代码已存在</span>
            </div>
          </div>

          <!-- 模板分类选择 -->
          <div class="form-row">
            <div class="form-group full-width">
              <label class="form-label">模板分类</label>
              <div class="category-header">
                <span>请选择适用的分类（可多选）</span>
                <button 
                  type="button" 
                  @click="refreshCategories" 
                  class="refresh-categories-btn"
                  title="刷新分类列表"
                >
                  🔄 刷新分类
                </button>
              </div>
              <div class="category-selection">
                <div v-if="categoryGroups.length === 0" class="no-categories">
                  <span>暂无分类数据，请先在分类管理中创建分类</span>
                </div>
                <div v-for="categoryGroup in categoryGroups" :key="categoryGroup.type" class="category-group">
                  <h4 class="category-group-title">{{ categoryGroup.name }}</h4>
                  <div class="category-options">
                    <label 
                      v-for="option in categoryGroup.options" 
                      :key="option.id" 
                      class="category-option"
                      :class="{ 'selected': formData.categoryIds.includes(option.id) }"
                    >
                      <input 
                        type="checkbox" 
                        :value="option.id" 
                        v-model="formData.categoryIds"
                        class="category-checkbox"
                      />
                      <span class="category-label">{{ option.name }}</span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="selected-categories">
                <span class="selected-label">已选择分类：</span>
                <span v-if="selectedCategoryNames.length === 0" class="no-selection">未选择</span>
                <span v-else class="selection-summary">{{ selectedCategoryNames.join(', ') }}</span>
              </div>
            </div>
          </div>

          <!-- 移除适用行业、模板风格、配色方案三个字段 -->
          <!-- 这些字段已废弃，请使用上方的分类选择功能 -->

          <!-- 模板类型和价格 -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">模板类型</label>
              <select v-model="formData.isPremium" class="form-select">
                <option :value="0">免费模板</option>
                <option :value="1">付费模板</option>
              </select>
            </div>
          </div>

          <!-- 价格输入（仅付费模板显示） -->
          <div v-if="formData.isPremium === 1" class="form-row">
            <div class="form-group">
              <label class="form-label required">模板价格</label>
              <input
                v-model="formData.price"
                type="number"
                step="0.01"
                min="0"
                class="form-input"
                placeholder="请输入价格（元）"
                :class="{ 'error': errors.price }"
              />
              <span v-if="errors.price" class="error-message">{{ errors.price }}</span>
            </div>

            <div class="form-group">
              <label class="form-label">排序权重</label>
              <input
                v-model="formData.sortOrder"
                type="number"
                min="0"
                class="form-input"
                placeholder="数值越大排序越靠前"
              />
            </div>
          </div>

          <!-- 模板预览图上传 -->
          <div class="form-row">
            <div class="form-group full-width">
              <label class="form-label">模板预览图</label>
              <div class="preview-upload-section">
                <div class="upload-area" :class="{ 'has-image': previewImageUrl }">
                  <div v-if="!previewImageUrl" class="upload-placeholder" @click="triggerPreviewUpload">
                    <div class="upload-icon">🖼️</div>
                    <p>点击上传预览图</p>
                    <p class="upload-tips">支持 JPG、PNG 格式，建议尺寸 400x600px</p>
                  </div>
                  <div v-else class="preview-image-container">
                    <img :src="previewImageUrl" alt="预览图" class="preview-image" />
                    <div class="image-actions">
                      <button type="button" @click="triggerPreviewUpload" class="btn-change">更换图片</button>
                      <button type="button" @click="removePreviewImage" class="btn-remove">删除图片</button>
                    </div>
                  </div>
                </div>
                <input 
                  ref="previewFileInput" 
                  type="file" 
                  accept="image/jpeg,image/png,image/jpg"
                  @change="handlePreviewImageSelect"
                  style="display: none;"
                />
                <div class="upload-info">
                  <span class="info-text">预览图将显示在模板列表中，帮助用户快速了解模板样式</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 模板描述 -->
          <div class="form-group full-width">
            <label class="form-label">模板描述</label>
            <textarea
              v-model="formData.description"
              class="form-textarea"
              rows="3"
              placeholder="请描述模板的特点和适用场景..."
            ></textarea>
          </div>
        </form>
      </div>

      <!-- 存储配置 -->
      <div class="form-section">
        <h2 class="section-title">存储配置</h2>
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">MinIO桶名称</label>
            <input
              v-model="formData.bucketName"
              type="text"
              class="form-input"
              placeholder="默认：resume-templates"
              @blur="validateBucket"
            />
            <span v-if="bucketCheckStatus === 'checking'" class="info-message">正在检查桶状态...</span>
            <span v-if="bucketCheckStatus === 'available'" class="success-message">✓ 桶可用</span>
            <span v-if="bucketCheckStatus === 'unavailable'" class="error-message">✗ 桶不可用</span>
          </div>
          
          <div class="form-group">
            <label class="form-label">存储说明</label>
            <div class="storage-info">
              <p>文件将存储在：<code>{{ formData.bucketName }}/{{ formData.templateCode || '{模板代码}' }}/</code></p>
              <p class="info-text">系统会自动根据模板代码创建存储目录</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件上传区域 -->
      <div class="form-section">
        <h2 class="section-title">文件上传</h2>
        
        <!-- 文件拖拽上传区域 -->
        <div 
          class="upload-area"
          :class="{ 
            'drag-over': isDragOver, 
            'has-files': selectedFiles.length > 0 
          }"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragleave="handleDragLeave"
          @click="triggerFileInput"
        >
          <input
            ref="fileInput"
            type="file"
            multiple
            accept=".vue,.js,.ts,.html,.css,.json,.txt,.jpg,.jpeg,.png,.gif,.svg"
            style="display: none"
            @change="handleFileSelect"
          />
          
          <div v-if="selectedFiles.length === 0" class="upload-placeholder">
            <div class="upload-icon">📁</div>
            <p class="upload-text">点击选择文件或拖拽文件到此区域</p>
            <p class="upload-hint">支持 .vue .js .html .css .json .png .jpg 等格式</p>
          </div>

          <!-- 已选择的文件列表 -->
          <div v-else class="file-list">
            <div class="file-list-header">
              <span>已选择 {{ selectedFiles.length }} 个文件</span>
              <button type="button" @click.stop="clearFiles" class="clear-btn">清空</button>
            </div>
            
            <div class="file-items">
              <div
                v-for="(file, index) in selectedFiles"
                :key="index"
                class="file-item"
                :class="{ 
                  'invalid': !isFileValid(file),
                  'vue-file': getFileType(file) === 'vue',
                  'json-file': getFileType(file) === 'json',
                  'important-file': isImportantFile(file)
                }"
              >
                <div class="file-info">
                  <div class="file-name-section">
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-type-badge" :class="getFileType(file)">
                      {{ getFileTypeLabel(file) }}
                    </span>
                  </div>
                  <div class="file-details">
                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                    <span v-if="!isFileValid(file)" class="file-status error">不支持</span>
                    <span v-else-if="isImportantFile(file)" class="file-status important">核心文件</span>
                    <span v-else class="file-status success">支持</span>
                  </div>
                </div>
                <button
                  type="button"
                  @click.stop="removeFile(index)"
                  class="remove-btn"
                >
                  ×
                </button>
              </div>
            </div>

            <div class="file-summary">
              <span>总大小: {{ formatFileSize(totalFileSize) }}</span>
              <span>有效文件: {{ validFileCount }}</span>
              <span class="vue-count">Vue文件: {{ vueFileCount }}</span>
              <span class="json-count">JSON文件: {{ jsonFileCount }}</span>
            </div>
          </div>
        </div>

        <!-- 支持的文件类型提示 -->
        <div class="file-types-info">
          <h4>支持的文件类型：</h4>
          <div class="file-types-list">
            <span class="file-type-tag priority">Vue组件 (.vue)</span>
            <span class="file-type-tag priority">JSON内容 (.json)</span>
            <span class="file-type-tag">CSS样式 (.css)</span>
            <span class="file-type-tag">JavaScript (.js)</span>
            <span class="file-type-tag">TypeScript (.ts)</span>
            <span class="file-type-tag">图片 (.jpg .png .gif .svg)</span>
            <span class="file-type-tag">其他 (.html .txt)</span>
          </div>
          <div class="file-types-note">
            <div class="note-section">
              <strong>📋 新架构说明：</strong>
              <ul>
                <li><strong>Vue文件</strong>：模板的UI组件，必须包含（存储在MinIO）</li>
                <li><strong>JSON文件</strong>：默认简历内容，推荐包含（存储在数据库）</li>
                <li><strong>其他文件</strong>：图片、样式等辅助文件（存储在MinIO）</li>
              </ul>
            </div>
            <div class="note-section">
              <strong>✨ 优势：</strong>
              <span>内容与样式分离，支持内容复用，便于维护管理</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件验证提示 -->
      <div v-if="selectedFiles.length > 0" class="file-validation-info">
        <div v-if="vueFileCount === 0" class="validation-warning">
          <span class="warning-icon">⚠️</span>
          <span>请至少上传一个Vue文件作为模板组件</span>
        </div>
        <div v-else-if="jsonFileCount === 0" class="validation-info">
          <span class="info-icon">💡</span>
          <span>建议上传JSON文件作为默认内容，如果没有将使用系统推荐内容</span>
        </div>
        <div v-else class="validation-success">
          <span class="success-icon">✅</span>
          <span>文件配置完整，包含Vue组件和JSON内容</span>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <button
          type="button"
          @click="handleSubmit"
          class="submit-btn"
          :disabled="isSubmitting || !canSubmit"
          :class="{ 'loading': isSubmitting }"
        >
          <span v-if="isSubmitting" class="loading-icon">⏳</span>
          {{ isSubmitting ? '上传中...' : '开始上传' }}
        </button>
        
        <button type="button" @click="resetForm" class="reset-btn">
          重置表单
        </button>
      </div>
    </div>

    <!-- 上传结果弹窗 -->
    <div v-if="showResult" class="result-modal" @click="closeResult">
      <div class="result-content" @click.stop>
        <div class="result-header">
          <h3>上传结果</h3>
          <button @click="closeResult" class="close-btn">×</button>
        </div>
        
        <div class="result-body">
          <div v-if="uploadResult" class="result-summary">
            <div class="summary-item">
              <span class="label">模板名称:</span>
              <span class="value">{{ uploadResult.templateName }}</span>
            </div>
            <div class="summary-item">
              <span class="label">模板代码:</span>
              <span class="value">{{ uploadResult.templateCode }}</span>
            </div>
            <div class="summary-item">
              <span class="label">总文件数:</span>
              <span class="value">{{ uploadResult.totalFiles }}</span>
            </div>
            <div class="summary-item">
              <span class="label">成功上传:</span>
              <span class="value success">{{ uploadResult.successCount }}</span>
            </div>
            <div class="summary-item">
              <span class="label">上传失败:</span>
              <span class="value error">{{ uploadResult.failureCount }}</span>
            </div>
            <div v-if="uploadResult.contentId" class="summary-item">
              <span class="label">内容ID:</span>
              <span class="value">{{ uploadResult.contentId }}</span>
            </div>
            <div v-if="uploadResult.mainTemplateFileId" class="summary-item">
              <span class="label">主模板文件ID:</span>
              <span class="value">{{ uploadResult.mainTemplateFileId }}</span>
            </div>
          </div>

          <!-- 成功文件列表 -->
          <div v-if="uploadResult?.uploadedFiles?.length" class="result-section">
            <h4>成功上传的文件:</h4>
            <div class="file-results">
              <div
                v-for="file in uploadResult.uploadedFiles"
                :key="file.fileId"
                class="file-result success"
              >
                <span class="file-name">{{ file.originalName }}</span>
                <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
              </div>
            </div>
          </div>

          <!-- 失败文件列表 -->
          <div v-if="uploadResult?.failedFiles?.length" class="result-section">
            <h4>上传失败的文件:</h4>
            <div class="file-results">
              <div
                v-for="file in uploadResult.failedFiles"
                :key="file.originalName"
                class="file-result error"
              >
                <span class="file-name">{{ file.originalName }}</span>
                <span class="file-reason">{{ file.failureReason }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="result-actions">
          <button @click="handleConfirm" class="btn-primary">
            确定
          </button>
          <button @click="handleContinueUpload" class="btn-secondary">
            清空并继续上传
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTemplateUploadService } from '~/composables/admin/useTemplateUploadService'

// 页面标题
definePageMeta({
  title: '模板上传 - 管理后台',
  layout: 'admin'
})

// 使用模板上传服务
const templateUploadService = useTemplateUploadService()

// 响应式数据
const formData = ref({
  name: '',
  templateCode: '',
  description: '',
  bucketName: 'resume-templates',
  categoryId: null, // 保留兼容性
  categoryIds: [], // 新增：分类ID数组
  industry: '',
  style: '',
  colorScheme: '',
  isPremium: 0,
  price: null,
  sortOrder: 0,
  tags: '',
  features: '',
  configData: ''
})

const selectedFiles = ref([])
const isDragOver = ref(false)
const isSubmitting = ref(false)
const showResult = ref(false)
const uploadResult = ref(null)

// 验证相关
const errors = ref({})
const codeCheckStatus = ref('') // '', 'checking', 'available', 'unavailable'
const nameCheckStatus = ref('') // '', 'checking', 'available', 'unavailable'
const bucketCheckStatus = ref('')

// DOM引用
const fileInput = ref(null)
const previewFileInput = ref(null)

// 分类相关数据
const categoryGroups = ref([])
const allCategories = ref([])

// 预览图相关数据
const previewImageUrl = ref('')
const previewImageFile = ref(null)

// 计算属性
const totalFileSize = computed(() => {
  return selectedFiles.value.reduce((total, file) => total + file.size, 0)
})

const validFileCount = computed(() => {
  return selectedFiles.value.filter(file => isFileValid(file)).length
})

const vueFileCount = computed(() => {
  return selectedFiles.value.filter(file => getFileType(file) === 'vue').length
})

const jsonFileCount = computed(() => {
  return selectedFiles.value.filter(file => getFileType(file) === 'json').length
})

const canSubmit = computed(() => {
  return formData.value.name &&
         formData.value.templateCode &&
         selectedFiles.value.length > 0 &&
         validFileCount.value > 0 &&
         vueFileCount.value > 0 && // 至少需要一个Vue文件
         codeCheckStatus.value === 'available' &&
         nameCheckStatus.value === 'available' &&
         !Object.keys(errors.value).length
})

const selectedCategoryNames = computed(() => {
  return formData.value.categoryIds
    .map(id => {
      const category = allCategories.value.find(cat => cat.id === id)
      return category?.name
    })
    .filter(Boolean)
})

// 生命周期
onMounted(() => {
  // 初始化时检查默认桶状态
  validateBucket()
  // 获取分类数据
  fetchCategories()
})

// 分类相关方法
const fetchCategories = async () => {
  try {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      console.warn('未找到管理员token，跳过获取分类数据')
      return
    }

    const response = await $fetch('/api/admin/template/category/tree', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.code === 200) {
      allCategories.value = []
      categoryGroups.value = []
      
      // 遍历分类树，组织数据
      const data = response.data || []
      data.forEach(parentCategory => {
        if (parentCategory.children && parentCategory.children.length > 0) {
          // 收集所有分类
          allCategories.value.push(parentCategory)
          parentCategory.children.forEach(child => {
            allCategories.value.push(child)
          })
          
          // 组织分类组
          categoryGroups.value.push({
            type: parentCategory.categoryType,
            name: parentCategory.name,
            options: parentCategory.children
          })
        }
      })
    }
  } catch (error) {
    console.error('获取分类数据失败:', error)
  }
}

// 刷新分类列表
const refreshCategories = async () => {
  try {
    await fetchCategories()
    // 显示刷新成功消息
    showMessage('分类列表已刷新', 'success')
  } catch (error) {
    console.error('刷新分类失败:', error)
    showMessage('刷新分类失败', 'error')
  }
}

// 显示消息提示
const showMessage = (message, type = 'info') => {
  // 这里可以使用element-plus的message组件或自定义消息提示
  console.log(`[${type.toUpperCase()}] ${message}`)
  // 如果项目中有全局消息组件，可以在这里调用
}

// 方法定义
const validateName = async () => {
  const name = formData.value.name.trim()
  
  if (!name) {
    errors.value.name = '模板名称不能为空'
    nameCheckStatus.value = ''
    return
  }

  if (name.length > 100) {
    errors.value.name = '模板名称长度不能超过100个字符'
    nameCheckStatus.value = ''
    return
  }

  // 检查名称可用性
  nameCheckStatus.value = 'checking'
  try {
    const available = await templateUploadService.checkTemplateName(name)
    if (available) {
      nameCheckStatus.value = 'available'
      delete errors.value.name
    } else {
      nameCheckStatus.value = 'unavailable'
      errors.value.name = '模板名称已存在'
    }
  } catch (error) {
    nameCheckStatus.value = ''
    errors.value.name = '检查名称可用性失败'
  }
}

const onTemplateNameInput = () => {
  // 重置检查状态
  nameCheckStatus.value = ''
  delete errors.value.name
}

const validateTemplateCode = async () => {
  const code = formData.value.templateCode.trim()
  
  if (!code) {
    errors.value.templateCode = '模板代码不能为空'
    codeCheckStatus.value = ''
    return
  }

  if (!/^[a-zA-Z0-9_-]+$/.test(code)) {
    errors.value.templateCode = '模板代码只能包含字母、数字、下划线和连字符'
    codeCheckStatus.value = ''
    return
  }

  if (code.length > 50) {
    errors.value.templateCode = '模板代码长度不能超过50个字符'
    codeCheckStatus.value = ''
    return
  }

  // 检查代码可用性
  codeCheckStatus.value = 'checking'
  try {
    const available = await templateUploadService.checkTemplateCode(code)
    if (available) {
      codeCheckStatus.value = 'available'
      delete errors.value.templateCode
    } else {
      codeCheckStatus.value = 'unavailable'
      errors.value.templateCode = '模板代码已存在'
    }
  } catch (error) {
    codeCheckStatus.value = ''
    errors.value.templateCode = '检查代码可用性失败'
  }
}

const onTemplateCodeInput = () => {
  // 重置检查状态
  codeCheckStatus.value = ''
  delete errors.value.templateCode
}

const validateBucket = async () => {
  const bucket = formData.value.bucketName.trim()
  
  if (!bucket) {
    bucketCheckStatus.value = ''
    return
  }

  bucketCheckStatus.value = 'checking'
  try {
    const available = await templateUploadService.checkBucketStatus(bucket)
    bucketCheckStatus.value = available ? 'available' : 'unavailable'
  } catch (error) {
    bucketCheckStatus.value = 'unavailable'
  }
}

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  addFiles(files)
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = Array.from(event.dataTransfer.files)
  addFiles(files)
}

const handleDragOver = (event) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const addFiles = (files) => {
  // 过滤重复文件
  const newFiles = files.filter(file => 
    !selectedFiles.value.some(existing => 
      existing.name === file.name && existing.size === file.size
    )
  )
  
  selectedFiles.value.push(...newFiles)
}

const removeFile = (index) => {
  selectedFiles.value.splice(index, 1)
}

const clearFiles = () => {
  selectedFiles.value = []
}

const isFileValid = (file) => {
  const validExtensions = ['.vue', '.js', '.ts', '.html', '.css', '.json', '.txt', '.jpg', '.jpeg', '.png', '.gif', '.svg']
  const fileName = file.name.toLowerCase()
  return validExtensions.some(ext => fileName.endsWith(ext))
}

const getFileType = (file) => {
  const fileName = file.name.toLowerCase()
  if (fileName.endsWith('.vue')) return 'vue'
  if (fileName.endsWith('.json')) return 'json'
  if (fileName.endsWith('.css')) return 'css'
  if (fileName.endsWith('.js') || fileName.endsWith('.ts')) return 'script'
  if (fileName.endsWith('.html')) return 'html'
  if (fileName.match(/\.(jpg|jpeg|png|gif|svg)$/)) return 'image'
  return 'other'
}

const getFileTypeLabel = (file) => {
  const type = getFileType(file)
  const labels = {
    'vue': 'Vue组件',
    'json': 'JSON内容',
    'css': 'CSS样式',
    'script': 'JavaScript',
    'html': 'HTML文件',
    'image': '图片文件',
    'other': '其他文件'
  }
  return labels[type] || '未知类型'
}

const isImportantFile = (file) => {
  const type = getFileType(file)
  return type === 'vue' || type === 'json'
}

// 预览图处理方法
const triggerPreviewUpload = () => {
  previewFileInput.value?.click()
}

const handlePreviewImageSelect = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
  if (!allowedTypes.includes(file.type)) {
    showMessage('请选择 JPG 或 PNG 格式的图片', 'error')
    return
  }

  // 验证文件大小（限制5MB）
  const maxSize = 5 * 1024 * 1024
  if (file.size > maxSize) {
    showMessage('图片大小不能超过 5MB', 'error')
    return
  }

  // 创建预览URL
  const reader = new FileReader()
  reader.onload = (e) => {
    previewImageUrl.value = e.target.result
    previewImageFile.value = file
  }
  reader.readAsDataURL(file)

  // 清空input值，确保可以重复选择同一文件
  event.target.value = ''
}

const removePreviewImage = () => {
  previewImageUrl.value = ''
  previewImageFile.value = null
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const handleSubmit = async () => {
  // 最终验证
  validateName()
  await validateTemplateCode()
  
  if (formData.value.isPremium === 1 && (!formData.value.price || formData.value.price <= 0)) {
    errors.value.price = '付费模板价格必须大于0'
    return
  }

  if (!canSubmit.value) {
    return
  }

  isSubmitting.value = true
  
  try {
    console.log('🚀 开始上传模板...')
    
    // 如果有预览图，添加到上传数据中
    let uploadData = { ...formData.value }
    if (previewImageFile.value) {
      uploadData.previewImage = previewImageFile.value
    }
    
    const result = await templateUploadService.uploadTemplate(selectedFiles.value, uploadData, previewImageFile.value)
    
    console.log('📋 上传结果:', result)
    
    if (result.success && result.data) {
      // 成功：使用result.data作为上传结果
      uploadResult.value = result.data
      showResult.value = true
      console.log('✅ 上传成功，显示结果弹窗')
    } else {
      // 失败：显示错误信息
      console.error('❌ 上传失败:', result.error || result.message)
      // 可以在这里显示错误提示，或者设置一个错误状态
      uploadResult.value = {
        templateName: formData.value.name,
        templateCode: formData.value.templateCode,
        totalFiles: selectedFiles.value.length,
        successCount: 0,
        failureCount: selectedFiles.value.length,
        uploadedFiles: [],
        failedFiles: selectedFiles.value.map(file => ({
          originalName: file.name,
          failureReason: result.error || result.message || '上传失败'
        }))
      }
      showResult.value = true
    }
  } catch (error) {
    console.error('❌ 上传异常:', error)
    // 异常处理：构造错误结果
    uploadResult.value = {
      templateName: formData.value.name,
      templateCode: formData.value.templateCode,
      totalFiles: selectedFiles.value.length,
      successCount: 0,
      failureCount: selectedFiles.value.length,
      uploadedFiles: [],
      failedFiles: selectedFiles.value.map(file => ({
        originalName: file.name,
        failureReason: error.message || '网络错误，请重试'
      }))
    }
    showResult.value = true
  } finally {
    isSubmitting.value = false
  }
}

const resetForm = () => {
  formData.value = {
    name: '',
    templateCode: '',
    description: '',
    bucketName: 'resume-templates',
    categoryId: null,
    categoryIds: [], // 重置分类选择
    industry: '',
    style: '',
    colorScheme: '',
    isPremium: 0,
    price: null,
    sortOrder: 0,
    tags: '',
    features: '',
    configData: ''
  }
  
  selectedFiles.value = []
  errors.value = {}
  codeCheckStatus.value = ''
  nameCheckStatus.value = ''
  bucketCheckStatus.value = ''
  
  // 重置文件输入框
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  
  validateBucket()
}

const closeResult = () => {
  showResult.value = false
  uploadResult.value = null
}

const handleConfirm = () => {
  // 确定按钮：关闭结果对话框
  // 如果是成功上传，保留表单内容让用户可以查看或修改
  // 如果是失败，也保留表单内容让用户可以重试
  closeResult()
  
  // 如果上传成功，可以显示一个简单的成功提示
  const hasSuccess = uploadResult.value?.successCount > 0
  if (hasSuccess) {
    // 可以在这里添加成功提示，比如显示一个toast消息
    console.log(`✅ 模板 "${uploadResult.value.templateName}" 上传成功！`)
  }
}

const handleContinueUpload = () => {
  // 继续上传按钮：关闭结果对话框并清空表单，准备下一次上传
  closeResult()
  resetForm()
}
</script>

<style scoped>
/* 页面整体样式 */
.template-upload-page {
  /* 移除padding和背景色，使用布局提供的样式 */
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.breadcrumb {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.breadcrumb-item.active {
  color: #409eff;
}

.breadcrumb-separator {
  margin: 0 8px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 主要容器 */
.upload-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 表单区块 */
.form-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 24px 0;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

/* 表单样式 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.form-label.required::after {
  content: ' *';
  color: #f56c6c;
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

/* 输入框包装器 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper .form-input {
  flex: 1;
  padding-right: 80px; /* 为状态提示留出空间 */
}

.input-status {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  white-space: nowrap;
}

.status-checking {
  color: #909399;
}

.status-success {
  color: #67c23a;
  font-weight: 500;
}

.status-error {
  color: #f56c6c;
  font-weight: 500;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #f56c6c;
}

.error-message {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 4px;
}

.success-message {
  font-size: 12px;
  color: #67c23a;
  margin-top: 4px;
}

.info-message {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 文件上传区域 */
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area.drag-over {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-area.has-files {
  padding: 20px;
  text-align: left;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 文件列表 */
.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  color: #606266;
}

.clear-btn {
  background: none;
  border: none;
  color: #f56c6c;
  cursor: pointer;
  font-size: 14px;
}

.file-items {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  background: white;
}

.file-item.invalid {
  background: #fef0f0;
  border-color: #fbc4c4;
}

.file-item.vue-file {
  border-left: 4px solid #4fc08d;
  background: #f0f9ff;
}

.file-item.json-file {
  border-left: 4px solid #f59e0b;
  background: #fffbeb;
}

.file-item.important-file {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.file-name-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-details {
  display: flex;
  gap: 12px;
  align-items: center;
}

.file-name {
  font-weight: 500;
  color: #303133;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-status.success {
  font-size: 12px;
  color: #67c23a;
}

.file-status.error {
  font-size: 12px;
  color: #f56c6c;
}

.file-status.important {
  font-size: 12px;
  color: #e6a23c;
  font-weight: 600;
}

.file-type-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.file-type-badge.vue {
  background: #4fc08d;
  color: white;
}

.file-type-badge.json {
  background: #f59e0b;
  color: white;
}

.file-type-badge.css {
  background: #1572b6;
  color: white;
}

.file-type-badge.script {
  background: #f7df1e;
  color: #333;
}

.file-type-badge.html {
  background: #e34c26;
  color: white;
}

.file-type-badge.image {
  background: #9333ea;
  color: white;
}

.file-type-badge.other {
  background: #6b7280;
  color: white;
}

.remove-btn {
  background: #f56c6c;
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
}

.file-summary {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #606266;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

/* 文件类型信息 */
.file-types-info {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.file-types-info h4 {
  font-size: 14px;
  margin: 0 0 12px 0;
  color: #606266;
}

.file-types-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-type-tag {
  background: #f0f2f5;
  color: #606266;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.file-type-tag.priority {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
  font-weight: 500;
}

.file-types-note {
  margin-top: 12px;
  font-size: 14px;
  color: #606266;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.file-types-note .note-section {
  margin-bottom: 12px;
}

.file-types-note .note-section:last-child {
  margin-bottom: 0;
}

.file-types-note ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.file-types-note li {
  margin-bottom: 4px;
  line-height: 1.5;
}

.file-summary .vue-count {
  color: #4fc08d;
  font-weight: 600;
}

.file-summary .json-count {
  color: #f59e0b;
  font-weight: 600;
}

/* 存储说明样式 */
.storage-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 16px;
}

.storage-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #495057;
}

.storage-info p:last-child {
  margin-bottom: 0;
}

.storage-info code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  color: #495057;
}

.storage-info .info-text {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

/* 文件验证提示 */
.file-validation-info {
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.validation-warning {
  background: #fef3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.validation-info {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.validation-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.warning-icon, .info-icon, .success-icon {
  font-size: 16px;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

.submit-btn,
.reset-btn {
  padding: 12px 32px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.submit-btn {
  background: #409eff;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #66b1ff;
}

.submit-btn:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.submit-btn.loading {
  background: #c0c4cc;
}

.reset-btn {
  background: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.reset-btn:hover {
  background: #ecf5ff;
  color: #409eff;
  border-color: #409eff;
}

.loading-icon {
  margin-right: 8px;
}

/* 结果弹窗 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.result-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #ebeef5;
}

.result-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #909399;
}

.result-body {
  padding: 24px;
}

.result-summary {
  margin-bottom: 24px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-item .label {
  color: #606266;
}

.summary-item .value {
  font-weight: 500;
}

.summary-item .value.success {
  color: #67c23a;
}

.summary-item .value.error {
  color: #f56c6c;
}

.result-section {
  margin-bottom: 24px;
}

.result-section h4 {
  font-size: 16px;
  margin: 0 0 12px 0;
  color: #303133;
}

.file-results {
  max-height: 200px;
  overflow-y: auto;
}

.file-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 14px;
}

.file-result.success {
  background: #f0f9ff;
  color: #1890ff;
}

.file-result.error {
  background: #fef0f0;
  color: #f56c6c;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 24px;
  border-top: 1px solid #ebeef5;
}

.btn-primary,
.btn-secondary {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: #409eff;
  color: white;
}

.btn-secondary {
  background: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

/* 分类选择样式 */
.form-group.full-width {
  grid-column: 1 / -1;
}

/* 分类选择相关样式 */
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.refresh-categories-btn {
  padding: 6px 12px;
  background: #67c23a;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.refresh-categories-btn:hover {
  background: #85ce61;
}

.no-categories {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  font-size: 14px;
  background: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
}

.category-selection {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.category-group {
  margin-bottom: 20px;
}

.category-group:last-child {
  margin-bottom: 0;
}

.category-group-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.category-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.category-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
}

.category-option:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.category-option.selected {
  border-color: #409eff;
  background: #409eff;
  color: white;
}

.category-checkbox {
  display: none;
}

.category-label {
  user-select: none;
}

.selected-categories {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e4e7ed;
  font-size: 12px;
}

.selected-label {
  color: #909399;
  margin-right: 8px;
}

.no-selection {
  color: #c0c4cc;
}

.selection-summary {
  color: #409eff;
}

/* 预览图上传相关样式 */
.preview-upload-section {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

.upload-area {
  position: relative;
  background: #fafafa;
  transition: all 0.2s;
}

.upload-area.has-image {
  background: white;
}

.upload-placeholder {
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.upload-placeholder:hover {
  background: #f0f0f0;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-placeholder p {
  margin: 8px 0;
  color: #606266;
}

.upload-tips {
  font-size: 12px;
  color: #909399;
}

.preview-image-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.preview-image {
  max-width: 200px;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.btn-change {
  padding: 6px 12px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-change:hover {
  background: #66b1ff;
}

.btn-remove {
  padding: 6px 12px;
  background: #f56c6c;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-remove:hover {
  background: #f78989;
}

.upload-info {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.info-text {
  font-size: 12px;
  color: #666;
}

.info-message {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-upload-page {
    padding: 16px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .submit-btn,
  .reset-btn {
    width: 100%;
  }
}
</style> 