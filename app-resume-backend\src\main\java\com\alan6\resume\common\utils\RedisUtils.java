package com.alan6.resume.common.utils;

import com.alan6.resume.common.constants.AuthConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis操作工具类
 * 
 * 主要功能：
 * 1. 提供Redis基础操作方法
 * 2. 封装用户Token相关的Redis操作
 * 3. 提供缓存操作的统一接口
 * 4. 处理Redis操作异常情况
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Component
public class RedisUtils {

    /**
     * Redis模板操作类
     */
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 设置缓存数据
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间（秒）
     * @return true-设置成功，false-设置失败
     */
    public boolean set(String key, Object value, long timeout) {
        try {
            // 设置缓存值并指定过期时间
            redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
            log.debug("设置Redis缓存成功，key: {}, timeout: {}秒", key, timeout);
            return true;
        } catch (Exception e) {
            log.error("设置Redis缓存失败，key: {}", key, e);
            return false;
        }
    }

    /**
     * 设置缓存数据（不过期）
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @return true-设置成功，false-设置失败
     */
    public boolean set(String key, Object value) {
        try {
            // 设置缓存值，不设置过期时间
            redisTemplate.opsForValue().set(key, value);
            log.debug("设置Redis缓存成功，key: {}", key);
            return true;
        } catch (Exception e) {
            log.error("设置Redis缓存失败，key: {}", key, e);
            return false;
        }
    }

    /**
     * 获取缓存数据
     * 
     * @param key 缓存键
     * @return 缓存值，如果不存在或获取失败则返回null
     */
    public Object get(String key) {
        try {
            // 获取缓存值
            Object value = redisTemplate.opsForValue().get(key);
            log.debug("获取Redis缓存，key: {}, 存在: {}", key, value != null);
            return value;
        } catch (Exception e) {
            log.error("获取Redis缓存失败，key: {}", key, e);
            return null;
        }
    }

    /**
     * 删除缓存数据
     * 
     * @param key 缓存键
     * @return true-删除成功，false-删除失败
     */
    public boolean delete(String key) {
        try {
            // 删除缓存
            Boolean result = redisTemplate.delete(key);
            boolean success = result != null && result;
            log.debug("删除Redis缓存，key: {}, 成功: {}", key, success);
            return success;
        } catch (Exception e) {
            log.error("删除Redis缓存失败，key: {}", key, e);
            return false;
        }
    }

    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存键
     * @return true-存在，false-不存在
     */
    public boolean exists(String key) {
        try {
            // 检查键是否存在
            Boolean result = redisTemplate.hasKey(key);
            boolean exists = result != null && result;
            log.debug("检查Redis缓存存在性，key: {}, 存在: {}", key, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查Redis缓存存在性失败，key: {}", key, e);
            return false;
        }
    }

    /**
     * 设置缓存过期时间
     * 
     * @param key 缓存键
     * @param timeout 过期时间（秒）
     * @return true-设置成功，false-设置失败
     */
    public boolean expire(String key, long timeout) {
        try {
            // 设置过期时间
            Boolean result = redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
            boolean success = result != null && result;
            log.debug("设置Redis缓存过期时间，key: {}, timeout: {}秒, 成功: {}", key, timeout, success);
            return success;
        } catch (Exception e) {
            log.error("设置Redis缓存过期时间失败，key: {}", key, e);
            return false;
        }
    }

    /**
     * 获取缓存剩余存活时间
     * 
     * @param key 缓存键
     * @return 剩余存活时间（秒），-1表示永不过期，-2表示键不存在
     */
    public long getExpire(String key) {
        try {
            // 获取剩余存活时间
            Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            long result = expire != null ? expire : -2;
            log.debug("获取Redis缓存剩余存活时间，key: {}, 剩余时间: {}秒", key, result);
            return result;
        } catch (Exception e) {
            log.error("获取Redis缓存剩余存活时间失败，key: {}", key, e);
            return -2;
        }
    }

    /**
     * 递增操作
     * 
     * @param key 缓存键
     * @return 递增后的值
     */
    public long incr(String key) {
        try {
            Long result = redisTemplate.opsForValue().increment(key);
            long value = result != null ? result : 0;
            log.debug("Redis递增操作，key: {}, 结果: {}", key, value);
            return value;
        } catch (Exception e) {
            log.error("Redis递增操作失败，key: {}", key, e);
            return 0;
        }
    }

    /**
     * 存储用户Token信息
     * 
     * 使用专用的Token存储方法，确保Token数据的一致性和安全性
     * 
     * @param token Token字符串
     * @param userInfo 用户信息对象
     * @return true-存储成功，false-存储失败
     */
    public boolean storeUserToken(String token, Object userInfo) {
        // 构建Token存储的Redis Key
        String tokenKey = AuthConstants.RedisKey.USER_TOKEN_KEY + token;
        
        // 存储用户信息到Redis，设置过期时间
        boolean success = set(tokenKey, userInfo, AuthConstants.Token.TOKEN_EXPIRE_TIME);
        
        if (success) {
            log.info("用户Token信息存储成功，token: {}", token);
        } else {
            log.error("用户Token信息存储失败，token: {}", token);
        }
        
        return success;
    }

    /**
     * 获取用户Token信息
     * 
     * @param token Token字符串
     * @return 用户信息对象，如果Token无效或过期则返回null
     */
    public Object getUserByToken(String token) {
        // 构建Token存储的Redis Key
        String tokenKey = AuthConstants.RedisKey.USER_TOKEN_KEY + token;
        
        // 从Redis获取用户信息
        Object userInfo = get(tokenKey);
        
        if (userInfo != null) {
            log.debug("根据Token获取用户信息成功，token: {}", token);
        } else {
            log.debug("根据Token获取用户信息失败，token可能已过期或无效，token: {}", token);
        }
        
        return userInfo;
    }

    /**
     * 删除用户Token
     * 
     * 用于用户退出登录时清除Token缓存
     * 
     * @param token Token字符串
     * @return true-删除成功，false-删除失败
     */
    public boolean removeUserToken(String token) {
        // 构建Token存储的Redis Key
        String tokenKey = AuthConstants.RedisKey.USER_TOKEN_KEY + token;
        
        // 删除Token缓存
        boolean success = delete(tokenKey);
        
        if (success) {
            log.info("用户Token删除成功，token: {}", token);
        } else {
            log.error("用户Token删除失败，token: {}", token);
        }
        
        return success;
    }

    /**
     * 检查Token是否有效
     * 
     * @param token Token字符串
     * @return true-有效，false-无效或已过期
     */
    public boolean isTokenValid(String token) {
        // 构建Token存储的Redis Key
        String tokenKey = AuthConstants.RedisKey.USER_TOKEN_KEY + token;
        
        // 检查Token是否存在且未过期
        boolean valid = exists(tokenKey);
        
        log.debug("检查Token有效性，token: {}, 有效: {}", token, valid);
        
        return valid;
    }

    /**
     * 刷新Token过期时间
     * 
     * 用于延长Token的有效期，提升用户体验
     * 
     * @param token Token字符串
     * @return true-刷新成功，false-刷新失败
     */
    public boolean refreshTokenExpire(String token) {
        // 构建Token存储的Redis Key
        String tokenKey = AuthConstants.RedisKey.USER_TOKEN_KEY + token;
        
        // 检查Token是否存在
        if (!exists(tokenKey)) {
            log.warn("尝试刷新不存在的Token，token: {}", token);
            return false;
        }
        
        // 重新设置过期时间
        boolean success = expire(tokenKey, AuthConstants.Token.TOKEN_EXPIRE_TIME);
        
        if (success) {
            log.info("Token过期时间刷新成功，token: {}", token);
        } else {
            log.error("Token过期时间刷新失败，token: {}", token);
        }
        
        return success;
    }

    /**
     * 获取用户Token数量
     * 
     * @param userId 用户ID
     * @return Token数量
     */
    public long getUserTokenCount(String userId) {
        try {
            String userTokensKey = AuthConstants.RedisKey.USER_TOKENS_SET + userId;
            Long count = redisTemplate.opsForSet().size(userTokensKey);
            long result = count != null ? count : 0;
            log.debug("获取用户Token数量，用户ID: {}, 数量: {}", userId, result);
            return result;
        } catch (Exception e) {
            log.error("获取用户Token数量失败，用户ID: {}", userId, e);
            return 0;
        }
    }

    /**
     * 添加Token到用户Token集合
     * 
     * @param userId 用户ID
     * @param token Token字符串
     * @return true-添加成功，false-添加失败
     */
    public boolean addUserToken(String userId, String token) {
        try {
            String userTokensKey = AuthConstants.RedisKey.USER_TOKENS_SET + userId;
            redisTemplate.opsForSet().add(userTokensKey, token);
            expire(userTokensKey, AuthConstants.Token.TOKEN_EXPIRE_TIME);
            log.debug("添加用户Token成功，用户ID: {}, Token: {}", userId, token);
            return true;
        } catch (Exception e) {
            log.error("添加用户Token失败，用户ID: {}, Token: {}", userId, token, e);
            return false;
        }
    }

    /**
     * 从用户Token集合中移除Token
     * 
     * @param userId 用户ID
     * @param token Token字符串
     * @return true-移除成功，false-移除失败
     */
    public boolean removeUserTokenFromSet(String userId, String token) {
        try {
            String userTokensKey = AuthConstants.RedisKey.USER_TOKENS_SET + userId;
            redisTemplate.opsForSet().remove(userTokensKey, token);
            log.debug("从用户Token集合移除Token成功，用户ID: {}, Token: {}", userId, token);
            return true;
        } catch (Exception e) {
            log.error("从用户Token集合移除Token失败，用户ID: {}, Token: {}", userId, token, e);
            return false;
        }
    }

    /**
     * 清理用户的所有Token
     * 
     * @param userId 用户ID
     * @return true-清理成功，false-清理失败
     */
    public boolean cleanAllUserTokens(String userId) {
        try {
            String userTokensKey = AuthConstants.RedisKey.USER_TOKENS_SET + userId;
            
            // 获取用户所有Token
            Set<Object> tokens = redisTemplate.opsForSet().members(userTokensKey);
            
            if (tokens != null && !tokens.isEmpty()) {
                // 批量删除所有Token
                for (Object token : tokens) {
                    String tokenKey = AuthConstants.RedisKey.USER_TOKEN_KEY + token;
                    delete(tokenKey);
                }
                
                // 清空用户Token集合
                delete(userTokensKey);
                
                log.info("清理用户所有Token成功，用户ID: {}, Token数量: {}", userId, tokens.size());
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("清理用户所有Token失败，用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 移除用户最旧的Token
     * 
     * @param userId 用户ID
     * @return true-移除成功，false-移除失败
     */
    public boolean removeOldestUserToken(String userId) {
        try {
            String userTokensKey = AuthConstants.RedisKey.USER_TOKENS_SET + userId;
            
            // 获取一个Token（Set是无序的，这里假设移除任意一个）
            Object oldestToken = redisTemplate.opsForSet().pop(userTokensKey);
            
            if (oldestToken != null) {
                // 删除对应的Token数据
                String tokenKey = AuthConstants.RedisKey.USER_TOKEN_KEY + oldestToken;
                delete(tokenKey);
                
                log.info("移除用户最旧Token成功，用户ID: {}, Token: {}", userId, oldestToken);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("移除用户最旧Token失败，用户ID: {}", userId, e);
            return false;
        }
    }
} 