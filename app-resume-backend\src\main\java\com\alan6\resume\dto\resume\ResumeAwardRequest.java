package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;

/**
 * 简历奖项请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(name = "ResumeAwardRequest", description = "简历奖项请求")
public class ResumeAwardRequest {

    /**
     * 奖项名称（必填，1-100字符）
     */
    @NotBlank(message = "奖项名称不能为空")
    @Size(min = 1, max = 100, message = "奖项名称长度必须在1-100个字符之间")
    @Schema(description = "奖项名称", example = "优秀员工奖")
    private String awardName;

    /**
     * 奖项级别（可选，如：国家级、省级、市级、公司级等）
     */
    @Size(max = 50, message = "奖项级别长度不能超过50个字符")
    @Schema(description = "奖项级别", example = "公司级")
    private String awardLevel;

    /**
     * 颁发机构（必填，1-100字符）
     */
    @NotBlank(message = "颁发机构不能为空")
    @Size(min = 1, max = 100, message = "颁发机构长度必须在1-100个字符之间")
    @Schema(description = "颁发机构", example = "阿里巴巴集团")
    private String issuingOrganization;

    /**
     * 获奖时间（必填）
     */
    @NotNull(message = "获奖时间不能为空")
    @Schema(description = "获奖时间", example = "2023-12-01")
    private LocalDate awardDate;

    /**
     * 获奖描述（可选）
     */
    @Size(max = 500, message = "获奖描述长度不能超过500个字符")
    @Schema(description = "获奖描述", example = "因在前端开发项目中表现突出，获得年度优秀员工奖")
    private String description;

    /**
     * 排序权重（必填）
     */
    @NotNull(message = "排序权重不能为空")
    @Min(value = 0, message = "排序权重不能为负数")
    @Schema(description = "排序权重", example = "1")
    private Integer sortOrder;
} 