import{_ as a}from"./DlAUqK2U.js";import{c as d,f as s,o as t}from"./CURHyiUL.js";const c={class:"users-page"},p={__name:"users",setup(o){return(i,e)=>(t(),d("div",c,e[0]||(e[0]=[s('<div class="page-header" data-v-1ede0eda><h1 class="page-title" data-v-1ede0eda>用户管理</h1><p class="page-description" data-v-1ede0eda>管理系统中的所有用户</p></div><div class="page-content" data-v-1ede0eda><div class="empty-state" data-v-1ede0eda><div class="empty-icon" data-v-1ede0eda>👥</div><h3 data-v-1ede0eda>用户管理功能</h3><p data-v-1ede0eda>此页面将显示和管理系统中的所有用户</p></div></div>',2)])))}},n=a(p,[["__scopeId","data-v-1ede0eda"]]);export{n as default};
