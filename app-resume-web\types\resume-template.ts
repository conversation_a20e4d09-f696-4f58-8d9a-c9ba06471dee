/**
 * 简历模板类型定义
 * @description 定义简历模板的标准接口和数据结构
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 基础数据类型
// ================================

/**
 * 基本信息接口
 */
export interface BasicInfo {
  /** 姓名 */
  name: string
  /** 职位标题 */
  title: string
  /** 电话号码 */
  phone: string
  /** 邮箱地址 */
  email: string
  /** 地址 */
  address: string
  /** 头像URL */
  photo?: string
  /** 个人网站 */
  website?: string
  /** LinkedIn */
  linkedin?: string
  /** GitHub */
  github?: string
}

/**
 * 技能接口
 */
export interface Skill {
  /** 技能名称 */
  name: string
  /** 技能等级 (0-100) */
  level: number
  /** 技能分类 */
  category?: string
  /** 技能描述 */
  description?: string
}

/**
 * 工作经历接口
 */
export interface WorkExperience {
  /** 公司名称 */
  company: string
  /** 职位名称 */
  position: string
  /** 开始日期 */
  startDate: string
  /** 结束日期 */
  endDate: string
  /** 工作描述 */
  description: string
  /** 工作地点 */
  location?: string
  /** 工作成就 */
  achievements?: string[]
}

/**
 * 教育经历接口
 */
export interface Education {
  /** 学校名称 */
  school: string
  /** 学位 */
  degree: string
  /** 专业 */
  major: string
  /** 开始日期 */
  startDate: string
  /** 结束日期 */
  endDate: string
  /** GPA */
  gpa?: string
  /** 荣誉 */
  honors?: string[]
}

/**
 * 项目经历接口
 */
export interface Project {
  /** 项目名称 */
  name: string
  /** 项目角色 */
  role: string
  /** 开始日期 */
  startDate: string
  /** 结束日期 */
  endDate: string
  /** 项目描述 */
  description: string
  /** 项目链接 */
  url?: string
  /** 使用技术 */
  technologies?: string[]
}

/**
 * 模块数据接口
 */
export interface ModuleData {
  /** 模块ID */
  id: string
  /** 模块名称 */
  name: string
  /** 模块标题 */
  title: string
  /** 是否启用 */
  enabled: boolean
  /** 排序权重 */
  order: number
  /** 模块数据 */
  data: any
}

/**
 * 简历数据接口
 */
export interface ResumeData {
  /** 基本信息 */
  basicInfo: BasicInfo
  /** 技能列表 */
  skills: Skill[]
  /** 工作经历 */
  workExperiences: WorkExperience[]
  /** 教育经历 */
  educations: Education[]
  /** 项目经历 */
  projects: Project[]
  /** 自我评价 */
  selfEvaluation: string
  /** 模块列表 */
  modules?: ModuleData[]
}

// ================================
// 样式配置类型
// ================================

/**
 * 文本样式接口
 */
export interface TextStyle {
  /** 字体大小 */
  fontSize: string
  /** 行高 */
  lineHeight: string
  /** 文本颜色 */
  textColor: string
  /** 主要颜色 */
  primaryColor: string
  /** 次要颜色 */
  secondaryColor: string
  /** 字体家族 */
  fontFamily: string
  /** 章节标题字体大小 */
  sectionTitleFontSize: string
  /** 项目标题字体大小 */
  itemTitleFontSize: string
  /** 正文字体大小 */
  bodyFontSize: string
}

/**
 * 间距配置接口
 */
export interface SpacingConfig {
  /** 模块间距 */
  moduleSpacing: string
  /** 项目间距 */
  itemSpacing: string
  /** 行间距 */
  lineSpacing: string
  /** 段落间距 */
  paragraphSpacing: string
  /** 章节内边距 */
  sectionPadding: string
  /** 项目内边距 */
  itemPadding: string
}

/**
 * 边距配置接口
 */
export interface MarginConfig {
  /** 上边距 */
  top: string
  /** 下边距 */
  bottom: string
  /** 左边距 */
  left: string
  /** 右边距 */
  right: string
}

/**
 * 主题配置接口
 */
export interface ThemeConfig {
  /** 主题名称 */
  name: string
  /** 主要颜色 */
  primaryColor: string
  /** 次要颜色 */
  secondaryColor: string
  /** 文本颜色 */
  textColor: string
  /** 背景颜色 */
  backgroundColor: string
}

// ================================
// 模板属性类型
// ================================

/**
 * 模板属性接口
 */
export interface TemplateProps {
  /** 简历数据 */
  resumeData: ResumeData
  /** 文本样式 */
  textStyle: TextStyle
  /** 是否可拖拽 */
  isDraggable: boolean
  /** 可见模块列表 */
  visibleModules: string[]
  /** 页面索引 */
  pageIndex: number
  /** 是否分页模式 */
  isPaginationMode: boolean
  /** 是否自然流动模式 */
  isNaturalFlow: boolean
}

// ================================
// 事件类型
// ================================

/**
 * 模板事件接口
 */
export interface TemplateEvents {
  /** 更新简历数据 */
  'update:resume-data': (data: ResumeData) => void
  /** 基本信息更新 */
  'basic-info-update': (basicInfo: BasicInfo) => void
  /** 模块上移 */
  'module-move-up': (moduleType: string) => void
  /** 模块下移 */
  'module-move-down': (moduleType: string) => void
  /** 模块删除 */
  'module-delete': (moduleType: string) => void
  /** 技能更新 */
  'skill-update': (skills: Skill[]) => void
  /** 工作经历更新 */
  'experience-update': (experiences: WorkExperience[]) => void
  /** 教育经历更新 */
  'education-update': (educations: Education[]) => void
  /** 项目经历更新 */
  'project-update': (projects: Project[]) => void
  /** 内容高度变化 */
  'content-height-change': (height: number) => void
}

// ================================
// 操作类型
// ================================

/**
 * 操作类型枚举
 */
export enum OperationType {
  /** 上移 */
  MOVE_UP = 'move-up',
  /** 下移 */
  MOVE_DOWN = 'move-down',
  /** 删除 */
  DELETE = 'delete',
  /** 编辑 */
  EDIT = 'edit',
  /** 添加 */
  ADD = 'add'
}

/**
 * 操作按钮配置接口
 */
export interface OperationButton {
  /** 操作类型 */
  type: OperationType
  /** 按钮文本 */
  text: string
  /** 按钮图标 */
  icon: string
  /** 是否禁用 */
  disabled: boolean
  /** 点击处理函数 */
  onClick: () => void
}

// ================================
// 模板接口
// ================================

/**
 * 模板基础接口
 * @description 所有简历模板都必须实现的基础接口
 */
export interface ResumeTemplateBase {
  /** 模板名称 */
  name: string
  /** 模板版本 */
  version: string
  /** 模板描述 */
  description: string
  /** 模板作者 */
  author: string
  /** 模板预览图 */
  preview: string
  /** 支持的功能 */
  features: string[]
  /** 模板标签 */
  tags: string[]
}

/**
 * 模板实例接口
 * @description 模板组件实例必须暴露的方法和属性
 */
export interface ResumeTemplateInstance {
  /** 模板DOM引用 */
  templateRef: HTMLElement | null
  /** 调试信息 */
  debugInfo: {
    printAllData: () => void
    printPerformanceInfo: () => void
    validateDataIntegrity: () => boolean
  }
  /** 验证模板属性 */
  validateTemplateProps: () => boolean
  /** 验证模板事件 */
  validateTemplateEvents: () => boolean
  /** 初始化模板 */
  initializeTemplate: () => void
}

// ================================
// 工具类型
// ================================

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * 模板配置类型
 */
export type TemplateConfig = DeepPartial<{
  textStyle: TextStyle
  spacing: SpacingConfig
  margins: MarginConfig
  theme: ThemeConfig
}>

/**
 * 模块类型联合
 */
export type ModuleType = 'basic_info' | 'education' | 'work_experience' | 'project' | 'skills' | 'language' | 'award' | 'hobbies' | 'self_evaluation' | 'internship' | 'training' | 'research_experience' | 'publication' | 'certificate' | 'volunteer_experience' | 'portfolio' | 'cover_letter' | 'custom'

// ================================
// 常量定义
// ================================

/**
 * 默认启用模块列表
 */
export const DEFAULT_ENABLED_MODULES: ModuleType[] = [
  'basic_info',
  'education',
  'work_experience',
  'project',
  'skills',
  'language',
  'award',
  'hobbies',
  'self_evaluation'
]

/**
 * 默认可选模块列表
 */
export const DEFAULT_OPTIONAL_MODULES: ModuleType[] = [
  'internship',
  'training',
  'research_experience',
  'publication',
  'certificate',
  'volunteer_experience',
  'portfolio',
  'cover_letter',
  'custom'
]

/**
 * 默认模块列表（所有模块）
 */
export const DEFAULT_MODULES: ModuleType[] = [
  ...DEFAULT_ENABLED_MODULES,
  ...DEFAULT_OPTIONAL_MODULES
]

/**
 * 模块标题映射
 */
export const MODULE_TITLES: Record<ModuleType, string> = {
  basic_info: '基本信息',
  education: '教育经历',
  work_experience: '工作经历',
  project: '项目经验',
  skills: '技能标签',
  language: '语言能力',
  award: '获奖荣誉',
  hobbies: '兴趣爱好',
  self_evaluation: '自我评价',
  internship: '实习经历',
  training: '培训经历',
  research_experience: '研究经历',
  publication: '论文专利',
  certificate: '证书资质',
  volunteer_experience: '志愿服务',
  portfolio: '作品集',
  cover_letter: '自荐信',
  custom: '自定义'
}

/**
 * 默认文本样式
 */
export const DEFAULT_TEXT_STYLE: TextStyle = {
  fontSize: '14px',
  lineHeight: '1.6',
  textColor: '#1f2937',
  primaryColor: '#3b82f6',
  secondaryColor: '#6366f1',
  fontFamily: 'system-ui, -apple-system, sans-serif',
  sectionTitleFontSize: '1.3em',
  itemTitleFontSize: '1.1em',
  bodyFontSize: '1em'
}

/**
 * 默认间距配置
 */
export const DEFAULT_SPACING: SpacingConfig = {
  moduleSpacing: '24px',
  itemSpacing: '16px',
  lineSpacing: '1.6',
  paragraphSpacing: '12px',
  sectionPadding: '20px',
  itemPadding: '12px'
}

/**
 * 默认边距配置
 */
export const DEFAULT_MARGINS: MarginConfig = {
  top: '60px',
  bottom: '60px',
  left: '60px',
  right: '60px'
} 