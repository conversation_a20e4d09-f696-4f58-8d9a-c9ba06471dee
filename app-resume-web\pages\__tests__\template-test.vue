<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">Vue模板动态加载测试</h1>
      
      <!-- 控制面板 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">控制面板</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              模板代码
            </label>
            <select v-model="selectedTemplateCode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">请选择模板</option>
              <option value="modern-simple">现代简约</option>
              <option value="creative-design">创意设计</option>
              <option value="business-classic">商务经典</option>
              <option value="tech-geek">技术极客</option>
              <option value="fresh-literature">清新文艺</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              强制刷新
            </label>
            <label class="inline-flex items-center">
              <input type="checkbox" v-model="forceRefresh" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
              <span class="ml-2 text-sm text-gray-600">强制从服务器重新加载</span>
            </label>
          </div>
        </div>
        
        <div class="flex flex-wrap gap-3">
          <button 
            @click="loadTemplate" 
            :disabled="loading || !selectedTemplateCode"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {{ loading ? '加载中...' : '加载模板' }}
          </button>
          
          <button 
            @click="clearTemplate" 
            :disabled="!loadedTemplate"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            清除模板
          </button>
          
          <button 
            @click="checkTemplate" 
            :disabled="!selectedTemplateCode"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            检查模板
          </button>
        </div>
      </div>
      
      <!-- 状态信息 -->
      <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <h3 class="text-lg font-medium mb-3">状态信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span class="font-medium">加载状态:</span>
            <span :class="getStatusClass(loadingState)">{{ loadingState }}</span>
          </div>
          <div>
            <span class="font-medium">进度:</span>
            <span class="text-blue-600">{{ loadingProgress }}%</span>
          </div>
          <div>
            <span class="font-medium">缓存状态:</span>
            <span :class="isCached ? 'text-green-600' : 'text-gray-600'">
              {{ isCached ? '已缓存' : '未缓存' }}
            </span>
          </div>
        </div>
        
        <div class="mt-3" v-if="loadingStep">
          <span class="font-medium">当前步骤:</span>
          <span class="text-gray-600">{{ loadingStep }}</span>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <h3 class="text-lg font-medium text-red-800 mb-2">错误信息</h3>
        <p class="text-red-700">{{ error }}</p>
      </div>
      
      <!-- 模板元信息 -->
      <div v-if="templateMeta" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h3 class="text-lg font-medium text-blue-800 mb-3">模板信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span class="font-medium">模板名称:</span>
            <span class="text-gray-700">{{ templateMeta.name }}</span>
          </div>
          <div>
            <span class="font-medium">版本:</span>
            <span class="text-gray-700">{{ templateMeta.version }}</span>
          </div>
          <div>
            <span class="font-medium">作者:</span>
            <span class="text-gray-700">{{ templateMeta.author }}</span>
          </div>
          <div>
            <span class="font-medium">描述:</span>
            <span class="text-gray-700">{{ templateMeta.description }}</span>
          </div>
        </div>
        
        <div class="mt-3" v-if="templateMeta.features && templateMeta.features.length > 0">
          <span class="font-medium">功能特性:</span>
          <div class="flex flex-wrap gap-2 mt-1">
            <span 
              v-for="feature in templateMeta.features" 
              :key="feature"
              class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
            >
              {{ feature }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 模板预览 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-xl font-semibold mb-4">模板预览</h3>
        
        <div v-if="loadedTemplate" class="border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-[400px]">
          <component :is="loadedTemplate" :resume-data="sampleResumeData" />
        </div>
        
        <div v-else class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500">
          <p class="text-lg">请选择并加载一个模板</p>
          <p class="text-sm mt-2">选择上方的模板代码，然后点击"加载模板"按钮</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 使用模板加载器
const {
  loadTemplate: loadTemplateFromService,
  hasTemplate,
  templateMeta,
  loadingState,
  loadingProgress,
  loadingStep,
  error,
  clearCache
} = useTemplateLoader()

// 响应式数据
const selectedTemplateCode = ref('')
const forceRefresh = ref(false)
const loading = ref(false)
const loadedTemplate = ref(null)

// 示例简历数据
const sampleResumeData = ref({
  basicInfo: {
    name: '张三',
    title: '前端开发工程师',
    email: '<EMAIL>',
    phone: '13800138000',
    location: '北京市朝阳区',
    avatar: '/images/avatar-sample.jpg'
  },
  summary: '具有5年前端开发经验，熟练掌握Vue.js、React等主流框架，擅长移动端开发和性能优化。',
  experience: [
    {
      company: '某科技公司',
      position: '高级前端开发工程师',
      duration: '2021.06 - 至今',
      description: '负责公司核心产品的前端开发，参与架构设计和技术选型。'
    },
    {
      company: '某互联网公司',
      position: '前端开发工程师',
      duration: '2019.03 - 2021.05',
      description: '负责移动端H5页面开发，优化页面性能，提升用户体验。'
    }
  ],
  education: [
    {
      school: '某大学',
      degree: '计算机科学与技术 学士',
      duration: '2015.09 - 2019.06',
      description: '主修计算机科学与技术，GPA 3.8/4.0'
    }
  ],
  skills: ['Vue.js', 'React', 'JavaScript', 'TypeScript', 'Node.js', 'Webpack']
})

// 计算属性
const isCached = computed(() => {
  return selectedTemplateCode.value && hasTemplate(selectedTemplateCode.value)
})

// 方法
const loadTemplate = async () => {
  if (!selectedTemplateCode.value) return
  
  loading.value = true
  loadedTemplate.value = null
  
  try {
    const template = await loadTemplateFromService({
      templateCode: selectedTemplateCode.value,
      forceRefresh: forceRefresh.value
    })
    
    if (template) {
      loadedTemplate.value = template
      console.log('模板加载成功:', template)
    }
  } catch (err) {
    console.error('模板加载失败:', err)
  } finally {
    loading.value = false
  }
}

const clearTemplate = () => {
  loadedTemplate.value = null
  if (selectedTemplateCode.value) {
    clearCache(selectedTemplateCode.value)
  }
}

const checkTemplate = () => {
  if (!selectedTemplateCode.value) return
  
  const cached = hasTemplate(selectedTemplateCode.value)
  const meta = templateMeta.value
  
  console.log('模板检查结果:', {
    templateCode: selectedTemplateCode.value,
    cached,
    meta
  })
}

const getStatusClass = (status) => {
  switch (status) {
    case 'loading':
      return 'text-blue-600'
    case 'success':
      return 'text-green-600'
    case 'error':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

// 页面标题
useHead({
  title: 'Vue模板动态加载测试'
})
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style> 