<template>
  <div class="resume-template" :class="{ 'preview-mode': !isDraggable }">
          <!-- 简历头部 -->
          <div class="resume-header">
              <div class="header-left">
                  <div class="title-section">
                      <h1 class="resume-title">个人简历</h1>
                      <h1 class="resume-title-en">RESUME</h1>
                  </div>
                  <div class="divider-line"></div>
              </div>
              <div class="header-right">
                  <div class="profile-photo" >{{ mergedResumeData.basicInfo.avatar }}</div>
              </div>
          </div>

          <!-- 基本信息区域 -->
          <section class="basic-info-section">
              <div class="basic-info-grid">
                  <div class="info-row">
                      <div class="info-item">
                          <span class="info-label">姓名</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.name }}</span>
                      </div>
                      <div class="info-item">
                          <span class="info-label">求职意向:</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.title }}</span>
                      </div>
                  </div>

                  <div class="info-row">
                      <div class="info-item">
                          <span class="info-label">电话:</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.phone }}</span>
                      </div>
                      <div class="info-item">
                          <span class="info-label">邮箱:</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.email }}</span>
                      </div>
                  </div>

                  <div class="info-row">
                      <div class="info-item">
                          <span class="info-label">工作地点:</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.address }}</span>
                      </div>
                      <div class="info-item">
                          <span class="info-label">个人网站:</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.url }}</span>
                      </div>
                  </div>

                  <div class="info-row">
                      <div class="info-item">
                          <span class="info-label">LinkedIn:</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.linkedin }}</span>
                      </div>
                      <div class="info-item">
                          <span class="info-label">GitHub:</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.url }}</span>
                      </div>
                  </div>

                  <div class="info-row">
                      <div class="info-item">
                          <span class="info-label">个人简介:</span>
                          <span class="info-value" >{{ mergedResumeData.basicInfo.summary }}</span>
                      </div>
                  </div>
              </div>
          </section>

          <!-- 工作经历模块 -->
          <section class="section-block work-section">
              <div class="section-header">
                  <div class="section-icon">💼</div>
                  <h2 class="section-title">工作经历</h2>
              </div>

              <div class="section-content">
                  <div class="experience-item" v-for="item in mergedResumeData.workExperiences" :key="item.id">
                      <div class="experience-header">
                          <h3 class="company-position">
                              <span >{{ item.company }}</span> - 
                              <span >{{ item.position }}</span>
                          </h3>
                          <div class="date-range">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>

                      <div class="experience-content">
                          <div class="achievements-list">
                              <div class="achievement-item" >{{ item.description }}</div>
                          </div>
                      </div>
                  </div>
              </div>
          </section>

          <!-- 教育经历模块 -->
          <section class="section-block education-section">
              <div class="section-header">
                  <div class="section-icon">🎓</div>
                  <h2 class="section-title">教育经历</h2>
              </div>

              <div class="section-content">
                  <div class="education-item" v-for="item in mergedResumeData.educations" :key="item.id">
                      <div class="education-header">
                          <h3 class="school-degree">
                              <span >{{ item.school }}</span> - 
                              <span >{{ item.degree }}</span>
                              （<span >{{ item.major }}</span>）
                          </h3>
                          <div class="date-range">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>

                      <div class="courses">
                          <div class="course-item" >{{ item.description }}</div>
                          <div class="course-item">
                              GPA: <span >{{ item.gpa }}</span>
                          </div>
                      </div>
                  </div>
              </div>
          </section>

          <!-- 项目经历模块 -->
          <section class="section-block project-section">
              <div class="section-header">
                  <div class="section-icon">📊</div>
                  <h2 class="section-title">项目经历</h2>
              </div>

              <div class="section-content">
                  <div class="project-item" v-for="item in mergedResumeData.projects" :key="item.id">
                      <div class="project-header">
                          <h3 class="project-name">
                              <span >{{ item.name }}</span>
                          </h3>
                          <div class="date-range">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>

                      <div class="project-content">
                          <div class="responsibilities">
                              <div class="responsibility-item">
                                  <strong>使用技术：</strong>
                                  <span >{{ item.technology }}</span>
                              </div>
                              <div class="responsibility-item" >
                                  <strong>项目描述：</strong>
                                  某电商平台为提高用户体验与转化率，需对界面进行优化。分析用户行为数据，找出界面痛点，重新规划导航栏，优化核心功能模块。
                                  优化后平台用户停留时间延长20%，转化率提升15%。
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </section>

          <!-- 技能特长模块 -->
          <section class="section-block skills-section">
              <div class="section-header">
                  <div class="section-icon">🛠️</div>
                  <h2 class="section-title">技能特长</h2>
              </div>

              <div class="section-content">
                  <div class="skills-grid">
                      <div class="skill-item" v-for="skill in mergedResumeData.skills" :key="skill.id">
                          <div class="skill-name" >{{ skill.name }}</div>
                          <div class="skill-level">
                              <div class="skill-bar">
                                  <div class="skill-progress" style="width: 90%"></div>
                              </div>
                              <div class="skill-percentage" >{{ skill.level }}</div>
                          </div>
                      </div>
                  </div>
              </div>
          </section>
      </div></template>

<script setup>
import { useResumeTemplateBase } from '~/composables/template/useResumeTemplateBase'

// 定义组件属性
const props = defineProps({
  resumeData: {
    type: Object,
    default: () => ({})
  },
  textStyle: {
    type: Object,
    default: () => ({})
  },
  isDraggable: {
    type: Boolean,
    default: false
  },
  visibleModules: {
    type: Array,
    default: () => []
  }
})

// 定义事件
const emit = defineEmits([
  'update-resume-data',
  'update-basic-info',
  'module-operation'
])

// 使用基础组合式函数
const {
  // 数据相关
  mergedResumeData,
  sortedModules,
  leftColumnModules,
  rightColumnModules,
  
  // 样式相关
  mergedTextStyle,
  titleStyle,
  subtitleStyle,
  bodyStyle,
  
  // 操作相关
  hoveredSkill,
  hoveredExperience,
  hoveredEducation,
  hoveredProject,
  moveModuleUp,
  moveModuleDown,
  deleteModule,
  handleBasicInfoUpdate,
  
  // 工具方法
  formatWorkPeriod,
  formatEducationPeriod,
  formatProjectPeriod,
  getSkillProgressStyle
} = useResumeTemplateBase(props, emit)
</script>

<style scoped>
/* 重置样式 */
        .resume-template * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 主容器样式 */

        .resume-template {
            width: 100%;
            max-width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 20mm 15mm;
            background: white;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        /* 简历头部 */
        .resume-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .header-left {
            flex: 1;
        }

        .title-section {
            display: flex;
            align-items: baseline;
            gap: 40px;
            margin-bottom: 10px;
        }

        .resume-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .resume-title-en {
            font-size: 36px;
            font-weight: bold;
            color: #666;
            margin: 0;
            letter-spacing: 8px;
        }

        .divider-line {
            height: 3px;
            background: linear-gradient(to right, #333 0%, #333 30%, #666 100%);
            margin-top: 5px;
        }

        .header-right {
            width: 120px;
            height: 160px;
        }

        .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 2px solid #ddd;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        /* 基本信息区域 */
        .basic-info-section {
            margin-bottom: 25px;
        }

        .basic-info-grid {
            display: grid;
            gap: 8px;
        }

        .info-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .info-item {
            display: flex;
            align-items: center;
        }

        .info-label {
            font-weight: bold;
            color: #333;
            min-width: 80px;
        }

        .info-value {
            color: #555;
            margin-left: 8px;
        }

        /* 模块通用样式 */
        .section-block {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #4a5568;
        }

        .section-icon {
            font-size: 18px;
            margin-right: 8px;
            background: #4a5568;
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            min-width: 36px;
            text-align: center;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: #4a5568;
            padding: 6px 15px;
            margin: 0;
            margin-left: -2px;
        }

        .section-content {
            padding-left: 0;
        }

        /* 工作经历样式 */
        .experience-item {
            margin-bottom: 20px;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .company-position {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .date-range {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
        }

        .achievements-list {
            margin-left: 0;
        }

        .achievement-item {
            margin-bottom: 4px;
            color: #555;
            line-height: 1.6;
        }

        /* 教育经历样式 */
        .education-item {
            margin-bottom: 15px;
        }

        .education-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .school-degree {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .courses {
            display: grid;
            gap: 4px;
        }

        .course-item {
            color: #555;
            line-height: 1.6;
        }

        /* 项目经历样式 */
        .project-item {
            margin-bottom: 20px;
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .project-name {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .responsibilities, .achievements {
            margin-bottom: 8px;
        }

        .responsibility-item, .achievement-item {
            margin-bottom: 4px;
            color: #555;
            line-height: 1.6;
        }

        /* 技能样式 */
        .skills-grid {
            display: grid;
            gap: 10px;
        }

        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .skill-name {
            font-weight: 500;
            color: #333;
            min-width: 100px;
        }

        .skill-level {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .skill-bar {
            flex: 1;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(to right, #4a5568, #718096);
            transition: width 0.3s ease;
        }

        .skill-percentage {
            font-size: 11px;
            color: #666;
            min-width: 35px;
            text-align: right;
        }

        /* 打印样式 */
        @media print {
            
            .resume-template {
                margin: 0;
                box-shadow: none;
                page-break-inside: avoid;
            }
            
            .section-block {
                page-break-inside: avoid;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .resume-template {
                padding: 15mm 10mm;
                font-size: 11px;
            }
            
            .resume-header {
                flex-direction: column;
                gap: 20px;
            }
            
            .title-section {
                flex-direction: column;
                gap: 10px;
            }
            
            .resume-title-en {
                font-size: 24px;
                letter-spacing: 4px;
            }
            
            .info-row {
                grid-template-columns: 1fr;
                gap: 8px;
            }
            
            .header-right {
                width: 100px;
                height: 130px;
            }
        }
</style>
