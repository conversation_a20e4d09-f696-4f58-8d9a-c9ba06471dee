<template>
  <div class="cover-letter-form">
    <div class="form-header">
      <h4 class="form-title">自荐信</h4>
      <p class="form-desc">撰写针对性的求职自荐信</p>
    </div>
    
    <div class="form-content">
      <RichTextEditor
        v-model="formData.content"
        placeholder="在这里撰写您的自荐信内容..."
        min-height="300px"
        @update:modelValue="handleContentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ content: '' })
  }
})

const emit = defineEmits(['update'])

const formData = reactive({
  content: ''
})

let debounceTimer = null

watch(() => props.data, (newData) => {
  if (newData && newData.content) {
    formData.content = newData.content
  } else {
    formData.content = ''
  }
}, { immediate: true, deep: true })

const handleContentChange = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  debounceTimer = setTimeout(() => {
    emit('update', { content: formData.content })
  }, 300)
}
</script>

<style scoped>
.cover-letter-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.form-content {
  @apply space-y-6;
}

.content-textarea {
  @apply w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none;
  min-height: 300px;
}

.tips-section {
  @apply bg-blue-50 rounded-lg p-4 border border-blue-200;
}

.tips-title {
  @apply text-sm font-medium text-blue-900 mb-3;
}

.tips-list {
  @apply space-y-2;
}

.tips-list li {
  @apply text-sm text-blue-800 flex items-start;
}

.tips-list li::before {
  @apply text-blue-600 mr-2 mt-0.5;
  content: '•';
}
</style> 