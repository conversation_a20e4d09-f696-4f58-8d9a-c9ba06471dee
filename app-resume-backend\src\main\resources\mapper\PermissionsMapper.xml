<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alan6.resume.mapper.PermissionsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.alan6.resume.entity.Permissions">
        <id column="id" property="id" />
        <result column="permission_name" property="permissionName" />
        <result column="permission_code" property="permissionCode" />
        <result column="resource_type" property="resourceType" />
        <result column="resource_url" property="resourceUrl" />
        <result column="parent_id" property="parentId" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, permission_name, permission_code, resource_type, resource_url, parent_id, sort_order, status, is_deleted, create_time, update_time
    </sql>

    <!-- 根据用户ID获取权限列表 -->
    <select id="selectPermissionsByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT p.id, p.permission_name, p.permission_code, p.resource_type, p.resource_url, p.parent_id, p.sort_order, p.status, p.is_deleted, p.create_time, p.update_time
        FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.is_deleted = 0
          AND p.status = 1
          AND rp.is_deleted = 0
          AND ur.is_deleted = 0
        ORDER BY p.sort_order ASC
    </select>

    <!-- 根据角色ID获取权限列表 -->
    <select id="selectPermissionsByRoleId" resultMap="BaseResultMap">
        SELECT p.id, p.permission_name, p.permission_code, p.resource_type, p.resource_url, p.parent_id, p.sort_order, p.status, p.is_deleted, p.create_time, p.update_time
        FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND p.is_deleted = 0
          AND p.status = 1
          AND rp.is_deleted = 0
        ORDER BY p.sort_order ASC
    </select>

    <!-- 根据权限代码获取权限 -->
    <select id="selectByPermissionCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM permissions
        WHERE permission_code = #{permissionCode}
          AND is_deleted = 0
    </select>

    <!-- 获取菜单权限树 -->
    <select id="selectMenuPermissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM permissions
        WHERE resource_type = 'menu'
          AND status = 1
          AND is_deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 根据用户ID获取菜单权限树 -->
    <select id="selectMenuPermissionsByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT p.id, p.permission_name, p.permission_code, p.resource_type, p.resource_url, p.parent_id, p.sort_order, p.status, p.is_deleted, p.create_time, p.update_time
        FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.resource_type = 'menu'
          AND p.is_deleted = 0
          AND p.status = 1
          AND rp.is_deleted = 0
          AND ur.is_deleted = 0
        ORDER BY p.sort_order ASC
    </select>

</mapper> 