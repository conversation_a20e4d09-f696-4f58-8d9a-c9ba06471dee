package com.alan6.resume.security.filter;

import cn.hutool.core.util.StrUtil;
import com.alan6.resume.common.constants.AuthConstants;
import com.alan6.resume.common.utils.RedisUtils;
import com.alan6.resume.common.utils.TokenUtils;
import com.alan6.resume.security.UserPrincipal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;

/**
 * Token认证过滤器
 * 
 * 主要功能：
 * 1. 从HTTP请求头中提取Token
 * 2. 验证Token的有效性
 * 3. 从Redis获取用户信息
 * 4. 设置Spring Security认证上下文
 * 5. 处理Token刷新逻辑
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Component
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    /**
     * Token工具类
     */
    @Autowired
    private TokenUtils tokenUtils;

    /**
     * Redis工具类
     */
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 过滤器核心处理方法
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                   HttpServletResponse response, 
                                   FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // 获取请求URI，用于日志记录
            String requestUri = request.getRequestURI();
            log.debug("处理请求认证过滤，URI: {}", requestUri);
            
            // 检查是否为公开访问路径，如果是则跳过认证
            if (isPublicPath(requestUri)) {
                log.debug("公开访问路径，跳过Token认证，URI: {}", requestUri);
                filterChain.doFilter(request, response);
                return;
            }
            
            // 从请求头中提取Token
            String authorizationHeader = request.getHeader(AuthConstants.Token.HEADER_NAME);
            String token = tokenUtils.extractTokenFromHeader(authorizationHeader);
            
            // 如果Token为空，则继续执行过滤器链（由Security处理未认证情况）
            if (StrUtil.isBlank(token)) {
                log.debug("请求头中未找到有效Token，URI: {}", requestUri);
                filterChain.doFilter(request, response);
                return;
            }
            
            // 从Redis获取用户信息
            Object userInfo = redisUtils.getUserByToken(token);
            if (userInfo == null) {
                log.debug("Token已过期或无效，URI: {}, Token: {}", requestUri, token);
                filterChain.doFilter(request, response);
                return;
            }
            
            // 创建用户主体对象
            UserPrincipal userPrincipal = UserPrincipal.create(userInfo);
            
            // 调试：输出用户权限信息
            log.info("Token认证调试 - 用户ID: {}, 角色: {}, 权限: {}, getAuthorities: {}", 
                    userPrincipal.getUserId(), 
                    userPrincipal.getRoles(), 
                    userPrincipal.getPermissions(),
                    userPrincipal.getAuthorities());
            
            // 创建认证对象
            UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(
                    userPrincipal, 
                    null, 
                    userPrincipal.getAuthorities() // 使用用户的实际权限
                );
            
            // 设置认证信息到Spring Security上下文
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // 检查是否需要刷新Token过期时间
            checkAndRefreshToken(token);
            
            log.debug("Token认证成功，用户ID: {}, URI: {}", userPrincipal.getUserId(), requestUri);
            
        } catch (Exception e) {
            log.error("Token认证过滤器处理异常，URI: {}", request.getRequestURI(), e);
            // 异常情况下清除认证上下文，让后续处理器处理
            SecurityContextHolder.clearContext();
        }
        
        // 继续执行过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 判断是否为公开访问路径
     */
    private boolean isPublicPath(String requestUri) {
        // 认证相关公开接口（精确匹配）
        if (requestUri.equals("/auth/send-code") ||
                requestUri.equals("/auth/login") ||
                requestUri.equals("/auth/register")) {
            return true;
        }

        // 系统接口
        if (requestUri.equals("/health") ||
                requestUri.equals("/actuator/health") ||
                requestUri.equals("/error")) {
            return true;
        }

        // 静态资源
        if (requestUri.startsWith("/static/") ||
                requestUri.equals("/favicon.ico")) {
            return true;
        }

        // Swagger文档相关接口
        if (requestUri.startsWith("/swagger-ui/") ||
                requestUri.startsWith("/v3/api-docs") ||
                requestUri.startsWith("/webjars/") ||
                requestUri.startsWith("/swagger-resources/") ||
                requestUri.equals("/doc.html")) {
            return true;
        }

        // 分享相关公开接口
        if (requestUri.startsWith("/share/") ||
                requestUri.startsWith("/public/")) {
            return true;
        }
        
        // Druid监控接口
        if (requestUri.startsWith("/druid/")) {
            return true;
        }
        
        // 管理员模板预览图片（公开访问）
        if (requestUri.startsWith("/admin/template/preview-image/")) {
            return true;
        }
        
        return false;
    }

    /**
     * 检查并刷新Token过期时间
     */
    private void checkAndRefreshToken(String token) {
        try {
            // 构建Token的Redis Key
            String tokenKey = AuthConstants.RedisKey.USER_TOKEN_KEY + token;
            
            // 获取Token剩余有效时间
            long remainTime = redisUtils.getExpire(tokenKey);
            
            // 如果剩余时间小于刷新阈值，则刷新Token过期时间
            if (remainTime > 0 && remainTime < AuthConstants.Token.TOKEN_REFRESH_THRESHOLD) {
                boolean refreshSuccess = redisUtils.refreshTokenExpire(token);
                if (refreshSuccess) {
                    log.info("Token过期时间已刷新，Token: {}, 原剩余时间: {}秒", token, remainTime);
                }
            }
            
        } catch (Exception e) {
            log.error("检查Token刷新时发生异常，Token: {}", token, e);
        }
    }
}