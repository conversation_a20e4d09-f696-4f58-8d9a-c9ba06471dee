package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员记录查询响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "MembershipHistoryResponse", description = "会员记录查询响应")
public class MembershipHistoryResponse {

    /**
     * 会员记录列表
     */
    @Schema(description = "会员记录列表")
    private List<MembershipRecord> records;

    /**
     * 总记录数
     */
    @Schema(description = "总记录数", example = "10")
    private Long total;

    /**
     * 会员记录内部类
     */
    @Data
    @Schema(name = "MembershipRecord", description = "会员记录")
    public static class MembershipRecord {
        /**
         * 会员记录ID
         */
        @Schema(description = "会员记录ID", example = "123")
        private Long id;

        /**
         * 订单号
         */
        @Schema(description = "订单号", example = "ORD20241201123456")
        private String orderNo;

        /**
         * 套餐信息
         */
        @Schema(description = "套餐信息")
        private PackageInfo packageInfo;

        /**
         * 会员开始时间
         */
        @Schema(description = "会员开始时间", example = "2024-12-01 10:00:00")
        private LocalDateTime startTime;

        /**
         * 会员结束时间
         */
        @Schema(description = "会员结束时间", example = "2024-11-30 23:59:59")
        private LocalDateTime endTime;

        /**
         * 支付金额
         */
        @Schema(description = "支付金额", example = "199.90")
        private BigDecimal amount;

        /**
         * 会员状态（0:待激活,1:有效,2:已过期,3:已取消）
         */
        @Schema(description = "会员状态", example = "1", allowableValues = {"0", "1", "2", "3"})
        private Byte status;

        /**
         * 状态描述
         */
        @Schema(description = "状态描述", example = "有效")
        private String statusDesc;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间", example = "2024-12-01 10:00:00")
        private LocalDateTime createTime;
    }

    /**
     * 套餐信息内部类
     */
    @Data
    @Schema(name = "PackageInfo", description = "套餐信息")
    public static class PackageInfo {
        /**
         * 套餐ID
         */
        @Schema(description = "套餐ID", example = "2")
        private Long id;

        /**
         * 套餐名称
         */
        @Schema(description = "套餐名称", example = "年度会员")
        private String packageName;

        /**
         * 套餐代码
         */
        @Schema(description = "套餐代码", example = "yearly")
        private String packageCode;

        /**
         * 时长描述
         */
        @Schema(description = "时长描述", example = "1年")
        private String durationDesc;
    }
} 