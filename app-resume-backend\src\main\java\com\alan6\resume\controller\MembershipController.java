package com.alan6.resume.controller;

import com.alan6.resume.common.utils.BusinessLogUtils;
import com.alan6.resume.core.R;
import com.alan6.resume.entity.Orders;
import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.membership.MembershipBenefitsResponse;
import com.alan6.resume.dto.membership.MembershipCheckResponse;
import com.alan6.resume.dto.membership.MembershipCurrentResponse;
import com.alan6.resume.dto.membership.MembershipHistoryResponse;
import com.alan6.resume.dto.membership.MembershipPackageResponse;
import com.alan6.resume.dto.membership.MembershipPurchaseRequest;
import com.alan6.resume.dto.membership.MembershipPurchaseResponse;
import com.alan6.resume.dto.membership.OrderQueryResponse;
import com.alan6.resume.dto.membership.OrderStatusUpdateRequest;
import com.alan6.resume.security.UserPrincipal;
import com.alan6.resume.service.IMembershipPackagesService;
import com.alan6.resume.service.IUserMembershipsService;
import com.alan6.resume.service.IOrdersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <p>
 * 会员管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/membership")
@Tag(name = "会员管理", description = "会员体系相关接口")
public class MembershipController {

    /**
     * 会员套餐服务
     */
    @Autowired
    private IMembershipPackagesService membershipPackagesService;

    /**
     * 用户会员服务
     */
    @Autowired
    private IUserMembershipsService userMembershipsService;

    /**
     * 订单服务
     */
    @Autowired
    private IOrdersService ordersService;

    /**
     * 获取会员套餐列表
     */
    @GetMapping("/packages")
    @Operation(summary = "获取会员套餐列表", description = "获取所有可用的会员套餐")
    public R<List<MembershipPackageResponse>> getPackages(
            @Parameter(description = "套餐状态（可选）") @RequestParam(required = false) Byte status,
            @Parameter(description = "是否只返回推荐套餐") @RequestParam(required = false) Boolean recommended) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "anonymous", "获取会员套餐列表");
        
        try {
            List<MembershipPackageResponse> packages = membershipPackagesService.getPackageList(status, recommended);
            BusinessLogUtils.logSuccess("获取会员套餐列表", "count", packages.size());
            return R.ok(packages);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 获取单个套餐详情
     */
    @GetMapping("/packages/{id}")
    @Operation(summary = "获取套餐详情", description = "根据ID获取会员套餐详细信息")
    public R<MembershipPackageResponse> getPackageDetail(
            @Parameter(description = "套餐ID", required = true) @PathVariable Long id) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "anonymous", "获取套餐详情");
        
        try {
            MembershipPackageResponse packageDetail = membershipPackagesService.getPackageDetail(id);
            BusinessLogUtils.logSuccess("获取套餐详情", "packageId", id);
            return R.ok(packageDetail);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 获取推荐套餐
     */
    @GetMapping("/packages/recommended")
    @Operation(summary = "获取推荐套餐", description = "获取系统推荐的会员套餐")
    public R<MembershipPackageResponse> getRecommendedPackage() {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "anonymous", "获取推荐套餐");
        
        try {
            MembershipPackageResponse recommendedPackage = membershipPackagesService.getRecommendedPackage();
            BusinessLogUtils.logSuccess("获取推荐套餐", "packageId", recommendedPackage.getId());
            return R.ok(recommendedPackage);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 获取当前会员信息
     */
    @GetMapping("/current")
    @Operation(summary = "获取当前会员信息", description = "获取用户当前会员状态和详细信息")
    public R<MembershipCurrentResponse> getCurrentMembership(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "获取当前会员信息");
        
        try {
            MembershipCurrentResponse currentMembership = userMembershipsService.getCurrentMembership(userId);
            BusinessLogUtils.logSuccess("获取当前会员信息", "isMember", currentMembership.getIsMember());
            return R.ok(currentMembership);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 获取会员权益
     */
    @GetMapping("/benefits")
    @Operation(summary = "获取会员权益", description = "获取用户当前会员的所有权益信息")
    public R<MembershipBenefitsResponse> getMembershipBenefits(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "获取会员权益");
        
        try {
            MembershipBenefitsResponse benefits = userMembershipsService.getMembershipBenefits(userId);
            BusinessLogUtils.logSuccess("获取会员权益", "isMember", benefits.getCurrentMember().getIsMember());
            return R.ok(benefits);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 检查简历创建权限
     */
    @GetMapping("/check/resume-limit")
    @Operation(summary = "检查简历创建权限", description = "检查用户是否可以创建新简历")
    public R<MembershipCheckResponse> checkResumeLimit(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "检查简历创建权限");
        
        try {
            MembershipCheckResponse checkResult = userMembershipsService.checkResumeLimit(userId);
            BusinessLogUtils.logSuccess("检查简历创建权限", "allowed", checkResult.getAllowed());
            return R.ok(checkResult);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 检查导出权限
     */
    @GetMapping("/check/export-limit")
    @Operation(summary = "检查导出权限", description = "检查用户是否可以导出简历")
    public R<MembershipCheckResponse> checkExportLimit(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "检查导出权限");
        
        try {
            MembershipCheckResponse checkResult = userMembershipsService.checkExportLimit(userId);
            BusinessLogUtils.logSuccess("检查导出权限", "allowed", checkResult.getAllowed());
            return R.ok(checkResult);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 检查模板使用权限
     */
    @GetMapping("/check/template/{templateId}")
    @Operation(summary = "检查模板使用权限", description = "检查用户是否可以使用指定模板")
    public R<MembershipCheckResponse> checkTemplatePermission(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "模板ID", required = true) @PathVariable Long templateId) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "检查模板使用权限");
        
        try {
            MembershipCheckResponse checkResult = userMembershipsService.checkTemplatePermission(userId, templateId);
            BusinessLogUtils.logSuccess("检查模板使用权限", "templateId", templateId, "allowed", checkResult.getAllowed());
            return R.ok(checkResult);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 购买会员
     */
    @PostMapping("/purchase")
    @Operation(summary = "购买会员", description = "用户购买会员套餐")
    public R<MembershipPurchaseResponse> purchaseMembership(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "购买请求", required = true) @Valid @RequestBody MembershipPurchaseRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "购买会员");
        
        try {
            MembershipPurchaseResponse response = userMembershipsService.purchaseMembership(userId, request);
            BusinessLogUtils.logSuccess("购买会员", "packageId", request.getPackageId(), "orderId", response.getOrderId());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 续费会员
     */
    @PutMapping("/renew")
    @Operation(summary = "续费会员", description = "用户续费会员套餐")
    public R<MembershipPurchaseResponse> renewMembership(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "续费请求", required = true) @Valid @RequestBody MembershipPurchaseRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "续费会员");
        
        try {
            MembershipPurchaseResponse response = userMembershipsService.renewMembership(userId, request);
            BusinessLogUtils.logSuccess("续费会员", "packageId", request.getPackageId(), "orderId", response.getOrderId());
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 查询会员记录
     */
    @GetMapping("/history")
    @Operation(summary = "查询会员记录", description = "分页查询用户的会员购买和使用记录")
    public R<MembershipHistoryResponse> getMembershipHistory(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "查询会员记录");
        
        try {
            MembershipHistoryResponse history = userMembershipsService.getMembershipHistory(userId, page, size);
            BusinessLogUtils.logSuccess("查询会员记录", "page", page, "size", size, "total", history.getTotal());
            return R.ok(history);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 取消会员
     */
    @DeleteMapping("/{membershipId}")
    @Operation(summary = "取消会员", description = "取消指定的会员记录")
    public R<Boolean> cancelMembership(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "会员记录ID", required = true) @PathVariable Long membershipId) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "取消会员");
        
        try {
            Boolean result = userMembershipsService.cancelMembership(userId, membershipId);
            BusinessLogUtils.logSuccess("取消会员", "membershipId", membershipId, "result", result);
            return R.ok(result);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 激活会员（内部接口，支付成功后调用）
     */
    @PutMapping("/{membershipId}/activate")
    @Operation(summary = "激活会员", description = "激活指定的会员记录（内部接口）")
    public R<Boolean> activateMembership(
            @Parameter(description = "会员记录ID", required = true) @PathVariable Long membershipId) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "system", "激活会员");
        
        try {
            Boolean result = userMembershipsService.activateMembership(membershipId);
            BusinessLogUtils.logSuccess("激活会员", "membershipId", membershipId, "result", result);
            return R.ok(result);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 查询订单详情
     */
    @GetMapping("/orders/{orderNo}")
    @Operation(summary = "查询订单详情", description = "根据订单号查询订单详细信息")
    public R<OrderQueryResponse> getOrderDetail(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Parameter(description = "订单号", required = true) @PathVariable String orderNo) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), userId.toString(), "查询订单详情");
        
        try {
            // 查询订单
            Orders order = ordersService.getByOrderNo(orderNo);
            
            // 验证订单所有者
            if (!order.getUserId().equals(userId)) {
                throw new BusinessException("无权查看此订单");
            }
            
            // 转换为响应DTO
            OrderQueryResponse response = convertToOrderQueryResponse(order);
            
            BusinessLogUtils.logSuccess("查询订单详情", "orderNo", orderNo);
            return R.ok(response);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 更新订单支付状态（内部接口，支付回调使用）
     */
    @PutMapping("/orders/{orderNo}/status")
    @Operation(summary = "更新订单支付状态", description = "更新订单的支付状态（内部接口）")
    public R<Boolean> updateOrderStatus(
            @Parameter(description = "订单号", required = true) @PathVariable String orderNo,
            @Parameter(description = "状态更新请求", required = true) @Valid @RequestBody OrderStatusUpdateRequest request) {
        
        BusinessLogUtils.setContext(BusinessLogUtils.generateRequestId(), "system", "更新订单支付状态");
        
        try {
            Boolean result = ordersService.updateOrderStatusByOrderNo(orderNo, request.getStatus(), 
                                                                     request.getTradeNo(), request.getRemark());
            
            // 如果是支付成功，需要激活对应的会员
            if (result && request.getStatus() == 2) {
                Orders order = ordersService.getByOrderNo(orderNo);
                if (order.getOrderType() == 1) { // 会员充值订单
                    // 这里应该调用会员激活逻辑，但由于复杂性，暂时只记录日志
                    log.info("订单支付成功，需要激活会员，订单号：{}，用户ID：{}", orderNo, order.getUserId());
                }
            }
            
            BusinessLogUtils.logSuccess("更新订单支付状态", "orderNo", orderNo, "status", request.getStatus(), "result", result);
            return R.ok(result);
        } finally {
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 转换订单实体为查询响应DTO
     */
    private OrderQueryResponse convertToOrderQueryResponse(Orders order) {
        OrderQueryResponse response = new OrderQueryResponse();
        response.setId(order.getId());
        response.setOrderNo(order.getOrderNo());
        response.setUserId(order.getUserId());
        response.setOrderType(order.getOrderType());
        response.setOrderTypeDesc(getOrderTypeDesc(order.getOrderType()));
        response.setProductId(order.getProductId());
        response.setProductName(order.getProductName());
        response.setOriginalAmount(order.getOriginalAmount());
        response.setDiscountAmount(order.getDiscountAmount());
        response.setFinalAmount(order.getFinalAmount());
        response.setPaymentMethod(order.getPaymentMethod());
        response.setPaymentMethodDesc(getPaymentMethodDesc(order.getPaymentMethod()));
        response.setPaymentPlatform(order.getPaymentPlatform());
        response.setTradeNo(order.getTradeNo());
        response.setOrderStatus(order.getOrderStatus());
        response.setOrderStatusDesc(getOrderStatusDesc(order.getOrderStatus()));
        response.setPayTime(order.getPayTime());
        response.setCompleteTime(order.getCompleteTime());
        response.setCreateTime(order.getCreateTime());
        response.setUpdateTime(order.getUpdateTime());
        return response;
    }

    /**
     * 获取订单类型描述
     */
    private String getOrderTypeDesc(Byte orderType) {
        switch (orderType) {
            case 1: return "会员充值";
            case 2: return "模板购买";
            default: return "未知";
        }
    }

    /**
     * 获取支付方式描述
     */
    private String getPaymentMethodDesc(Byte paymentMethod) {
        switch (paymentMethod) {
            case 1: return "微信支付";
            case 2: return "支付宝";
            default: return "未知";
        }
    }

    /**
     * 获取订单状态描述
     */
    private String getOrderStatusDesc(Byte orderStatus) {
        switch (orderStatus) {
            case 1: return "待支付";
            case 2: return "已支付";
            case 3: return "已完成";
            case 4: return "已取消";
            case 5: return "已退款";
            default: return "未知";
        }
    }

}
