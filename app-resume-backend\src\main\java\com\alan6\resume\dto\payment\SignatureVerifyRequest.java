package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 签名验证请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "SignatureVerifyRequest", description = "签名验证请求")
public class SignatureVerifyRequest {

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    @Schema(description = "支付方式", required = true, example = "wechat", allowableValues = {"wechat", "alipay"})
    private String paymentMethod;

    /**
     * 待验证的签名
     */
    @NotBlank(message = "签名不能为空")
    @Schema(description = "待验证的签名", required = true, example = "abc123def456ghi789")
    private String signature;

    /**
     * 时间戳
     */
    @NotBlank(message = "时间戳不能为空")
    @Schema(description = "时间戳", required = true, example = "1703001234567")
    private String timestamp;

    /**
     * 随机字符串
     */
    @NotBlank(message = "随机字符串不能为空")
    @Schema(description = "随机字符串", required = true, example = "random123456")
    private String nonce;

    /**
     * 原始数据体
     */
    @NotBlank(message = "原始数据体不能为空")
    @Schema(description = "原始数据体", required = true, example = "{\"orderNo\":\"ORD20241222123456789\"}")
    private String body;

    /**
     * 证书序列号（微信支付需要）
     */
    @Schema(description = "证书序列号", example = "certificate_serial_number")
    private String serialNo;
} 