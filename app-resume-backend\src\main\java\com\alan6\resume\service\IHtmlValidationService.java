package com.alan6.resume.service;

import com.alan6.resume.dto.template.HtmlValidationResult;

import java.io.File;
import java.util.List;

/**
 * HTML模板验证服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IHtmlValidationService {
    
    /**
     * 验证HTML文件是否符合转换规范
     * 
     * @param htmlFile HTML文件
     * @return 验证结果
     */
    HtmlValidationResult validateHtmlFile(File htmlFile);
    
    /**
     * 验证HTML内容是否符合转换规范
     * 
     * @param htmlContent HTML内容
     * @param fileName 文件名（用于错误提示）
     * @return 验证结果
     */
    HtmlValidationResult validateHtmlContent(String htmlContent, String fileName);
    
    /**
     * 批量验证HTML文件
     * 
     * @param htmlFiles HTML文件列表
     * @return 验证结果列表
     */
    List<HtmlValidationResult> validateHtmlFiles(List<File> htmlFiles);
    
    /**
     * 验证HTML文档结构
     * 
     * @param htmlContent HTML内容
     * @param fileName 文件名
     * @return 验证结果
     */
    HtmlValidationResult validateDocumentStructure(String htmlContent, String fileName);
    
    /**
     * 验证模块标识
     * 
     * @param htmlContent HTML内容
     * @param fileName 文件名
     * @return 验证结果
     */
    HtmlValidationResult validateModuleIdentifiers(String htmlContent, String fileName);
    
    /**
     * 验证字段绑定
     * 
     * @param htmlContent HTML内容
     * @param fileName 文件名
     * @return 验证结果
     */
    HtmlValidationResult validateFieldBindings(String htmlContent, String fileName);
    
    /**
     * 验证CSS样式
     * 
     * @param htmlContent HTML内容
     * @param fileName 文件名
     * @return 验证结果
     */
    HtmlValidationResult validateCssStyles(String htmlContent, String fileName);
    
    /**
     * 生成验证报告
     * 
     * @param validationResults 验证结果列表
     * @return 验证报告内容
     */
    String generateValidationReport(List<HtmlValidationResult> validationResults);
    
    /**
     * 获取支持的模块ID列表
     * 
     * @return 模块ID列表
     */
    List<String> getSupportedModuleIds();
    
    /**
     * 获取支持的字段名称列表
     * 
     * @return 字段名称列表
     */
    List<String> getSupportedFieldNames();
    
    /**
     * 检查模块ID是否支持
     * 
     * @param moduleId 模块ID
     * @return 是否支持
     */
    boolean isSupportedModuleId(String moduleId);
    
    /**
     * 检查字段名称是否支持
     * 
     * @param fieldName 字段名称
     * @return 是否支持
     */
    boolean isSupportedFieldName(String fieldName);
} 