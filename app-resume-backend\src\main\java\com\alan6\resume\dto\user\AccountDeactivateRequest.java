package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 账号注销请求DTO
 * 
 * 主要功能：
 * 1. 接收用户注销账号的请求参数
 * 2. 支持两种验证方式：密码验证和短信验证码验证
 * 3. 收集注销原因用于产品改进
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Schema(name = "账号注销请求", description = "用户注销账号的请求参数")
public class AccountDeactivateRequest {

    /**
     * 验证方式
     * 1-密码验证，2-短信验证码验证
     */
    @Schema(description = "验证方式 1-密码验证，2-短信验证码验证")
    @NotNull(message = "验证方式不能为空")
    private Integer verificationType;

    /**
     * 账号密码
     * 当验证方式为1时必填
     */
    @Schema(description = "账号密码")
    private String password;

    /**
     * 短信验证码
     * 当验证方式为2时必填
     */
    @Schema(description = "短信验证码")
    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确，应为6位数字")
    private String smsCode;

    /**
     * 注销原因
     * 用于产品改进和用户反馈收集
     */
    @Schema(description = "注销原因，用于产品改进，可选填写不再使用")
    @Size(max = 200, message = "注销原因长度不能超过200个字符")
    private String reason;

    /**
     * 验证是否为密码验证方式
     * 
     * @return true-密码验证，false-不是
     */
    public boolean isPasswordVerification() {
        return Integer.valueOf(1).equals(verificationType);
    }

    /**
     * 验证是否为短信验证码验证方式
     * 
     * @return true-短信验证码验证，false-不是
     */
    public boolean isSmsCodeVerification() {
        return Integer.valueOf(2).equals(verificationType);
    }

    /**
     * 验证密码验证方式的参数是否完整
     * 
     * @return true-参数完整，false-参数不完整
     */
    public boolean isPasswordVerificationValid() {
        return isPasswordVerification() && password != null && !password.trim().isEmpty();
    }

    /**
     * 验证短信验证码验证方式的参数是否完整
     * 
     * @return true-参数完整，false-参数不完整
     */
    public boolean isSmsCodeVerificationValid() {
        return isSmsCodeVerification() && smsCode != null && !smsCode.trim().isEmpty();
    }

    /**
     * 验证请求参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        // 检查验证方式是否有效
        if (verificationType == null || (verificationType != 1 && verificationType != 2)) {
            return false;
        }

        // 根据验证方式检查对应参数
        if (isPasswordVerification()) {
            return isPasswordVerificationValid();
        } else if (isSmsCodeVerification()) {
            return isSmsCodeVerificationValid();
        }

        return false;
    }
} 