package com.alan6.resume.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 管理员登录响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@Schema(description = "管理员登录响应数据")
public class AdminLoginResponse {
    
    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;
    
    @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String refreshToken;
    
    @Schema(description = "令牌过期时间")
    private LocalDateTime tokenExpireTime;
    
    @Schema(description = "用户ID", example = "1001")
    private Long userId;
    
    @Schema(description = "用户名", example = "admin")
    private String username;
    
    @Schema(description = "昵称", example = "管理员")
    private String nickname;
    
    @Schema(description = "手机号", example = "13800138000")
    private String phone;
    
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;
    
    @Schema(description = "性别", example = "1")
    private Integer gender;
    
    @Schema(description = "账户状态", example = "1")
    private Integer status;
    
    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;
    
    @Schema(description = "用户角色列表")
    private List<String> roles;
    
    @Schema(description = "用户权限列表")
    private List<String> permissions;
    
    @Schema(description = "管理员菜单")
    private List<AdminMenuDTO> menus;
    
    /**
     * 管理员菜单DTO
     */
    @Data
    @Schema(description = "管理员菜单项")
    public static class AdminMenuDTO {
        
        @Schema(description = "菜单ID", example = "1")
        private Long id;
        
        @Schema(description = "菜单名称", example = "用户管理")
        private String name;
        
        @Schema(description = "菜单路径", example = "/admin/users")
        private String path;
        
        @Schema(description = "菜单图标", example = "users")
        private String icon;
        
        @Schema(description = "排序", example = "1")
        private Integer sort;
        
        @Schema(description = "父菜单ID", example = "0")
        private Long parentId;
        
        @Schema(description = "子菜单列表")
        private List<AdminMenuDTO> children;
        
        @Schema(description = "菜单元数据")
        private Map<String, Object> meta;
    }
} 