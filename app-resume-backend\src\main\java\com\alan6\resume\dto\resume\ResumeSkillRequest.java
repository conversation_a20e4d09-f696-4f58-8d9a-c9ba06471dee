package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 技能请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "技能请求")
public class ResumeSkillRequest {

    /**
     * 技能名称
     */
    @Schema(description = "技能名称", example = "Java", required = true)
    @NotBlank(message = "技能名称不能为空")
    @Size(min = 1, max = 100, message = "技能名称长度必须在1-100字符之间")
    private String skillName;

    /**
     * 技能水平
     */
    @Schema(description = "技能水平", example = "3", required = true)
    @NotNull(message = "技能水平不能为空")
    private Byte skillLevel;

    /**
     * 技能分类
     */
    @Schema(description = "技能分类", example = "编程语言")
    @Size(max = 50, message = "技能分类长度不能超过50字符")
    private String skillCategory;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重", example = "1", required = true)
    @NotNull(message = "排序权重不能为空")
    private Integer sortOrder;
} 