package com.alan6.resume.service;

import java.time.Duration;

/**
 * 模板缓存服务接口
 * @description 管理Redis中的模板缓存，提供模板内容的读写操作
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ITemplateCacheService {

    /**
     * 从Redis缓存中获取模板内容
     * @description 根据模板ID从Redis中获取缓存的Vue模板内容
     * @param templateId 模板ID
     * @return 模板内容，如果不存在则返回null
     */
    String getTemplateFromCache(Long templateId);

    /**
     * 从Redis缓存中获取模板内容（通过模板代码）
     * @description 根据模板代码从Redis中获取缓存的Vue模板内容
     * @param templateCode 模板代码
     * @return 模板内容，如果不存在则返回null
     */
    String getTemplateFromCache(String templateCode);

    /**
     * 将模板内容存储到Redis缓存
     * @description 将Vue模板内容存储到Redis中，设置过期时间
     * @param templateId 模板ID
     * @param templateContent 模板内容
     * @param expireTime 过期时间
     * @return 是否存储成功
     */
    boolean setTemplateToCache(Long templateId, String templateContent, Duration expireTime);

    /**
     * 将模板内容存储到Redis缓存（通过模板代码）
     * @description 将Vue模板内容存储到Redis中，设置过期时间
     * @param templateCode 模板代码
     * @param templateContent 模板内容
     * @param expireTime 过期时间
     * @return 是否存储成功
     */
    boolean setTemplateToCache(String templateCode, String templateContent, Duration expireTime);

    /**
     * 检查模板是否存在于缓存中
     * @description 检查指定模板是否已经缓存在Redis中
     * @param templateId 模板ID
     * @return 是否存在
     */
    boolean isTemplateExistsInCache(Long templateId);

    /**
     * 检查模板是否存在于缓存中（通过模板代码）
     * @description 检查指定模板是否已经缓存在Redis中
     * @param templateCode 模板代码
     * @return 是否存在
     */
    boolean isTemplateExistsInCache(String templateCode);

    /**
     * 删除模板缓存
     * @description 从Redis中删除指定模板的缓存
     * @param templateId 模板ID
     * @return 是否删除成功
     */
    boolean deleteTemplateFromCache(Long templateId);

    /**
     * 删除模板缓存（通过模板代码）
     * @description 从Redis中删除指定模板的缓存
     * @param templateCode 模板代码
     * @return 是否删除成功
     */
    boolean deleteTemplateFromCache(String templateCode);

    /**
     * 获取模板缓存的过期时间
     * @description 获取指定模板在Redis中的剩余过期时间
     * @param templateId 模板ID
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示不存在
     */
    long getTemplateExpireTime(Long templateId);

    /**
     * 刷新模板缓存过期时间
     * @description 重新设置模板缓存的过期时间
     * @param templateId 模板ID
     * @param expireTime 新的过期时间
     * @return 是否设置成功
     */
    boolean refreshTemplateExpireTime(Long templateId, Duration expireTime);

    /**
     * 获取缓存键名
     * @description 根据模板ID生成Redis缓存键名
     * @param templateId 模板ID
     * @return 缓存键名
     */
    String getCacheKey(Long templateId);

    /**
     * 获取缓存键名（通过模板代码）
     * @description 根据模板代码生成Redis缓存键名
     * @param templateCode 模板代码
     * @return 缓存键名
     */
    String getCacheKey(String templateCode);

    /**
     * 批量预热模板缓存
     * @description 批量加载热门模板到Redis缓存中
     * @param templateIds 模板ID列表
     * @return 成功加载的模板数量
     */
    int preloadTemplates(Long[] templateIds);

    /**
     * 清理过期的模板缓存
     * @description 清理Redis中过期的模板缓存
     * @return 清理的缓存数量
     */
    int cleanExpiredTemplates();
} 