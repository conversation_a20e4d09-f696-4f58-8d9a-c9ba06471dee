package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 模板批量删除请求DTO
 * 
 * @description 管理员批量删除模板的请求参数
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "模板批量删除请求")
public class TemplateBatchDeleteRequest {

    /**
     * 要删除的模板ID列表
     */
    @Schema(description = "要删除的模板ID列表", required = true, example = "[1, 2, 3]")
    @NotEmpty(message = "模板ID列表不能为空")
    @Size(min = 1, max = 50, message = "单次最多删除50个模板")
    private List<Long> templateIds;

    /**
     * 删除原因（可选）
     */
    @Schema(description = "删除原因", example = "模板过时，需要下架")
    private String reason;

    /**
     * 是否强制删除
     */
    @Schema(description = "是否强制删除（忽略关联检查）", example = "false")
    private Boolean forceDelete = false;
} 