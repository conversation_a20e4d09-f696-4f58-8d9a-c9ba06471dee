package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.system.SystemConfigRequest;
import com.alan6.resume.dto.system.SystemConfigResponse;
import com.alan6.resume.entity.SystemConfigs;
import com.alan6.resume.mapper.SystemConfigsMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SystemConfigsServiceImpl 测试类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SystemConfigsServiceImplTest {

    @Mock
    private SystemConfigsMapper systemConfigsMapper;

    private SystemConfigsServiceImpl systemConfigsService;

    private SystemConfigs mockConfig;
    private SystemConfigRequest mockRequest;

    @BeforeEach
    void setUp() {
        // 初始化service实例
        systemConfigsService = new SystemConfigsServiceImpl();
        
        // 使用反射注入mock的mapper到ServiceImpl的baseMapper字段
        ReflectionTestUtils.setField(systemConfigsService, "baseMapper", systemConfigsMapper);
        
        // 初始化测试数据
        mockConfig = new SystemConfigs();
        mockConfig.setId(1L);
        mockConfig.setConfigKey("test.config.key");
        mockConfig.setConfigValue("test_value");
        mockConfig.setConfigType("string");
        mockConfig.setConfigGroup("test");
        mockConfig.setDescription("测试配置");
        mockConfig.setIsDeleted((byte) 0);
        mockConfig.setCreateTime(LocalDateTime.now());
        mockConfig.setUpdateTime(LocalDateTime.now());

        mockRequest = new SystemConfigRequest();
        mockRequest.setConfigKey("test.config.key");
        mockRequest.setConfigValue("test_value");
        mockRequest.setConfigType("string");
        mockRequest.setConfigGroup("test");
        mockRequest.setDescription("测试配置");
    }

    /**
     * 测试获取系统配置列表 - 成功场景
     */
    @Test
    void testGetConfigList_Success() {
        // 准备测试数据
        List<SystemConfigs> mockConfigs = Arrays.asList(mockConfig);
        Page<SystemConfigs> mockPage = new Page<>(1, 10);
        mockPage.setRecords(mockConfigs);
        mockPage.setTotal(1);

        // 模拟mapper行为
        when(systemConfigsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        Page<SystemConfigResponse> result = systemConfigsService.getConfigList("test", 1, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        
        SystemConfigResponse response = result.getRecords().get(0);
        assertEquals(mockConfig.getConfigKey(), response.getConfigKey());
        assertEquals(mockConfig.getConfigValue(), response.getConfigValue());
        assertEquals(mockConfig.getConfigType(), response.getConfigType());
        assertEquals(mockConfig.getConfigGroup(), response.getConfigGroup());

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }

    /**
     * 测试根据配置键获取配置 - 成功场景
     */
    @Test
    void testGetConfigByKey_Success() {
        // 模拟mapper行为 - 需要匹配MyBatis-Plus ServiceImpl.getOne()的调用方式
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(mockConfig);

        // 执行测试
        SystemConfigResponse result = systemConfigsService.getConfigByKey("test.config.key");

        // 验证结果
        assertNotNull(result);
        assertEquals(mockConfig.getConfigKey(), result.getConfigKey());
        assertEquals(mockConfig.getConfigValue(), result.getConfigValue());
        assertEquals(mockConfig.getConfigType(), result.getConfigType());

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
    }

    /**
     * 测试根据配置键获取配置 - 配置不存在
     */
    @Test
    void testGetConfigByKey_NotFound() {
        // 模拟mapper行为
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            systemConfigsService.getConfigByKey("non.existent.key");
        });

        assertEquals("系统配置不存在", exception.getMessage());
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
    }

    /**
     * 测试更新配置项 - 成功场景
     */
    @Test
    void testUpdateConfig_Success() {
        // 模拟mapper行为
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(mockConfig);
        when(systemConfigsMapper.updateById(any(SystemConfigs.class)))
                .thenReturn(1);

        // 执行测试
        boolean result = systemConfigsService.updateConfig("test.config.key", mockRequest);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
        verify(systemConfigsMapper, times(1))
                .updateById(any(SystemConfigs.class));
    }

    /**
     * 测试更新配置项 - 配置不存在
     */
    @Test
    void testUpdateConfig_NotFound() {
        // 模拟mapper行为
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            systemConfigsService.updateConfig("non.existent.key", mockRequest);
        });

        assertEquals("系统配置不存在", exception.getMessage());
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
        verify(systemConfigsMapper, never())
                .updateById(any(SystemConfigs.class));
    }

    /**
     * 测试创建配置项 - 成功场景
     */
    @Test
    void testCreateConfig_Success() {
        // 模拟mapper行为
        when(systemConfigsMapper.selectCount(any(LambdaQueryWrapper.class)))
                .thenReturn(0L);
        when(systemConfigsMapper.insert(any(SystemConfigs.class)))
                .thenReturn(1);

        // 执行测试
        SystemConfigResponse result = systemConfigsService.createConfig(mockRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockRequest.getConfigKey(), result.getConfigKey());
        assertEquals(mockRequest.getConfigValue(), result.getConfigValue());

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectCount(any(LambdaQueryWrapper.class));
        verify(systemConfigsMapper, times(1))
                .insert(any(SystemConfigs.class));
    }

    /**
     * 测试创建配置项 - 配置键已存在
     */
    @Test
    void testCreateConfig_KeyExists() {
        // 模拟mapper行为
        when(systemConfigsMapper.selectCount(any(LambdaQueryWrapper.class)))
                .thenReturn(1L);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            systemConfigsService.createConfig(mockRequest);
        });

        assertEquals("配置键名已存在", exception.getMessage());
        verify(systemConfigsMapper, times(1))
                .selectCount(any(LambdaQueryWrapper.class));
        verify(systemConfigsMapper, never())
                .insert(any(SystemConfigs.class));
    }

    /**
     * 测试获取配置分组列表
     */
    @Test
    void testGetConfigGroups_Success() {
        // 准备测试数据
        SystemConfigs config1 = new SystemConfigs();
        config1.setConfigGroup("group1");
        SystemConfigs config2 = new SystemConfigs();
        config2.setConfigGroup("group2");
        List<SystemConfigs> mockConfigs = Arrays.asList(config1, config2);

        // 模拟mapper行为 - 使用更宽松的匹配来避免Lambda缓存问题
        when(systemConfigsMapper.selectList(any()))
                .thenReturn(mockConfigs);

        // 执行测试
        List<String> result = systemConfigsService.getConfigGroups();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("group1"));
        assertTrue(result.contains("group2"));

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectList(any());
    }

    /**
     * 测试获取前端配置
     */
    @Test
    void testGetFrontendConfig_Success() {
        // 准备测试数据
        SystemConfigs frontendConfig = new SystemConfigs();
        frontendConfig.setConfigKey("frontend.title");
        frontendConfig.setConfigValue("简历系统");
        frontendConfig.setConfigType("string");
        frontendConfig.setConfigGroup("frontend");

        List<SystemConfigs> mockConfigs = Arrays.asList(frontendConfig);

        // 模拟mapper行为
        when(systemConfigsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockConfigs);

        // 执行测试
        Map<String, Object> result = systemConfigsService.getFrontendConfig();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("简历系统", result.get("frontend.title"));

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectList(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试根据配置键获取配置值 - 成功场景
     */
    @Test
    void testGetConfigValue_Success() {
        // 模拟mapper行为
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(mockConfig);

        // 执行测试
        String result = systemConfigsService.getConfigValue("test.config.key", "default_value");

        // 验证结果
        assertEquals("test_value", result);

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
    }

    /**
     * 测试根据配置键获取配置值 - 使用默认值
     */
    @Test
    void testGetConfigValue_UseDefault() {
        // 模拟mapper行为
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(null);

        // 执行测试
        String result = systemConfigsService.getConfigValue("non.existent.key", "default_value");

        // 验证结果
        assertEquals("default_value", result);

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
    }

    /**
     * 测试根据配置键获取配置值并转换类型 - Integer类型
     */
    @Test
    void testGetConfigValueWithType_Integer() {
        // 准备测试数据
        SystemConfigs numberConfig = new SystemConfigs();
        numberConfig.setConfigKey("test.number");
        numberConfig.setConfigValue("123");
        numberConfig.setConfigType("number");

        // 模拟mapper行为
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(numberConfig);

        // 执行测试
        Integer result = systemConfigsService.getConfigValue("test.number", 0, Integer.class);

        // 验证结果
        assertEquals(123, result);

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
    }

    /**
     * 测试根据配置键获取配置值并转换类型 - Boolean类型
     */
    @Test
    void testGetConfigValueWithType_Boolean() {
        // 准备测试数据
        SystemConfigs booleanConfig = new SystemConfigs();
        booleanConfig.setConfigKey("test.boolean");
        booleanConfig.setConfigValue("true");
        booleanConfig.setConfigType("boolean");

        // 模拟mapper行为
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(booleanConfig);

        // 执行测试
        Boolean result = systemConfigsService.getConfigValue("test.boolean", false, Boolean.class);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
    }

    /**
     * 测试根据配置键获取配置值并转换类型 - 转换异常使用默认值
     */
    @Test
    void testGetConfigValueWithType_ConversionError() {
        // 准备测试数据
        SystemConfigs invalidConfig = new SystemConfigs();
        invalidConfig.setConfigKey("test.invalid");
        invalidConfig.setConfigValue("invalid_number");
        invalidConfig.setConfigType("number");

        // 模拟mapper行为
        when(systemConfigsMapper.selectOne(any(LambdaQueryWrapper.class), eq(true)))
                .thenReturn(invalidConfig);

        // 执行测试
        Integer result = systemConfigsService.getConfigValue("test.invalid", 999, Integer.class);

        // 验证结果（应该返回默认值）
        assertEquals(999, result);

        // 验证方法调用
        verify(systemConfigsMapper, times(1))
                .selectOne(any(LambdaQueryWrapper.class), eq(true));
    }

    /**
     * 测试配置值解析 - 数字类型
     */
    @Test
    void testParseConfigValue_Number() {
        // 通过反射测试私有方法的逻辑，这里通过公共方法间接测试
        SystemConfigs numberConfig = new SystemConfigs();
        numberConfig.setConfigKey("test.number");
        numberConfig.setConfigValue("123.45");
        numberConfig.setConfigType("number");
        numberConfig.setConfigGroup("frontend");

        List<SystemConfigs> mockConfigs = Arrays.asList(numberConfig);
        when(systemConfigsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockConfigs);

        Map<String, Object> result = systemConfigsService.getFrontendConfig();

        assertNotNull(result);
        assertEquals(123.45, result.get("test.number"));
    }

    /**
     * 测试配置值解析 - 布尔类型
     */
    @Test
    void testParseConfigValue_Boolean() {
        SystemConfigs booleanConfig = new SystemConfigs();
        booleanConfig.setConfigKey("test.boolean");
        booleanConfig.setConfigValue("true");
        booleanConfig.setConfigType("boolean");
        booleanConfig.setConfigGroup("frontend");

        List<SystemConfigs> mockConfigs = Arrays.asList(booleanConfig);
        when(systemConfigsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockConfigs);

        Map<String, Object> result = systemConfigsService.getFrontendConfig();

        assertNotNull(result);
        assertEquals(true, result.get("test.boolean"));
    }

    /**
     * 测试配置值解析 - JSON类型
     */
    @Test
    void testParseConfigValue_Json() {
        SystemConfigs jsonConfig = new SystemConfigs();
        jsonConfig.setConfigKey("test.json");
        jsonConfig.setConfigValue("{\"key\":\"value\"}");
        jsonConfig.setConfigType("json");
        jsonConfig.setConfigGroup("frontend");

        List<SystemConfigs> mockConfigs = Arrays.asList(jsonConfig);
        when(systemConfigsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockConfigs);

        Map<String, Object> result = systemConfigsService.getFrontendConfig();

        assertNotNull(result);
        assertEquals("{\"key\":\"value\"}", result.get("test.json"));
    }
} 