package com.alan6.resume.service.impl;

import com.alan6.resume.dto.system.SystemHealthResponse;
import com.alan6.resume.dto.system.SystemMetricsResponse;
import com.alan6.resume.dto.system.SystemStatusResponse;
import com.alan6.resume.service.ISystemMonitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统监控服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemMonitorServiceImpl implements ISystemMonitorService {

    private final DataSource dataSource;

    /**
     * 系统健康检查
     *
     * @return 健康检查结果
     */
    @Override
    public SystemHealthResponse healthCheck() {
        log.info("执行系统健康检查");
        
        SystemHealthResponse response = new SystemHealthResponse();
        response.setCheckTime(LocalDateTime.now());
        
        Map<String, SystemHealthResponse.ComponentStatus> components = new HashMap<>();
        
        // 检查数据库连接
        SystemHealthResponse.ComponentStatus dbStatus = checkDatabaseHealth();
        components.put("database", dbStatus);
        
        // 检查Redis连接（模拟）
        SystemHealthResponse.ComponentStatus redisStatus = checkRedisHealth();
        components.put("redis", redisStatus);
        
        // 检查MinIO连接（模拟）
        SystemHealthResponse.ComponentStatus minioStatus = checkMinioHealth();
        components.put("minio", minioStatus);
        
        // 检查磁盘空间
        SystemHealthResponse.ComponentStatus diskStatus = checkDiskHealth();
        components.put("disk", diskStatus);
        
        response.setComponents(components);
        
        // 判断整体状态
        boolean allUp = components.values().stream()
                .allMatch(status -> "UP".equals(status.getStatus()));
        response.setStatus(allUp ? "UP" : "DOWN");
        
        log.info("系统健康检查完成, 整体状态: {}", response.getStatus());
        return response;
    }

    /**
     * 获取系统状态
     *
     * @return 系统状态信息
     */
    @Override
    public SystemStatusResponse getSystemStatus() {
        log.info("获取系统状态");
        
        SystemStatusResponse response = new SystemStatusResponse();
        response.setCheckTime(LocalDateTime.now());
        
        // 获取系统信息
        response.setSystem(getSystemInfo());
        
        // 获取应用信息
        response.setApplication(getApplicationInfo());
        
        // 获取数据库信息
        response.setDatabase(getDatabaseInfo());
        
        // 获取Redis信息（模拟）
        response.setRedis(getRedisInfo());
        
        // 获取MinIO信息（模拟）
        response.setMinio(getMinioInfo());
        
        log.info("系统状态获取完成");
        return response;
    }

    /**
     * 获取系统指标
     *
     * @return 系统指标信息
     */
    @Override
    public SystemMetricsResponse getSystemMetrics() {
        log.info("获取系统指标");
        
        SystemMetricsResponse response = new SystemMetricsResponse();
        response.setCheckTime(LocalDateTime.now());
        
        // 获取性能指标
        response.setPerformance(getPerformanceMetrics());
        
        // 获取业务指标
        response.setBusiness(getBusinessMetrics());
        
        // 获取错误指标
        response.setErrors(getErrorMetrics());
        
        log.info("系统指标获取完成");
        return response;
    }

    /**
     * 检查数据库健康状态
     */
    private SystemHealthResponse.ComponentStatus checkDatabaseHealth() {
        SystemHealthResponse.ComponentStatus status = new SystemHealthResponse.ComponentStatus();
        long startTime = System.currentTimeMillis();
        
        try (Connection connection = dataSource.getConnection()) {
            // 执行简单查询测试连接
            connection.createStatement().executeQuery("SELECT 1").close();
            
            long responseTime = System.currentTimeMillis() - startTime;
            status.setStatus("UP");
            status.setResponseTime(responseTime);
            
            Map<String, Object> details = new HashMap<>();
            details.put("database", connection.getMetaData().getDatabaseProductName());
            details.put("version", connection.getMetaData().getDatabaseProductVersion());
            status.setDetails(details);
            
        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            status.setStatus("DOWN");
            status.setResponseTime(System.currentTimeMillis() - startTime);
            
            Map<String, Object> details = new HashMap<>();
            details.put("error", e.getMessage());
            status.setDetails(details);
        }
        
        return status;
    }

    /**
     * 检查Redis健康状态（模拟）
     */
    private SystemHealthResponse.ComponentStatus checkRedisHealth() {
        SystemHealthResponse.ComponentStatus status = new SystemHealthResponse.ComponentStatus();
        
        // 模拟Redis检查
        status.setStatus("UP");
        status.setResponseTime(2L);
        
        Map<String, Object> details = new HashMap<>();
        details.put("version", "7.0.5");
        details.put("mode", "standalone");
        status.setDetails(details);
        
        return status;
    }

    /**
     * 检查MinIO健康状态（模拟）
     */
    private SystemHealthResponse.ComponentStatus checkMinioHealth() {
        SystemHealthResponse.ComponentStatus status = new SystemHealthResponse.ComponentStatus();
        
        // 模拟MinIO检查
        status.setStatus("UP");
        status.setResponseTime(25L);
        
        Map<String, Object> details = new HashMap<>();
        details.put("endpoint", "192.168.16.130:9000");
        details.put("buckets", 4);
        status.setDetails(details);
        
        return status;
    }

    /**
     * 检查磁盘健康状态
     */
    private SystemHealthResponse.ComponentStatus checkDiskHealth() {
        SystemHealthResponse.ComponentStatus status = new SystemHealthResponse.ComponentStatus();
        
        try {
            java.io.File root = new java.io.File("/");
            long totalSpace = root.getTotalSpace();
            long freeSpace = root.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            double usagePercent = (double) usedSpace / totalSpace * 100;
            
            status.setStatus(usagePercent > 90 ? "WARN" : "UP");
            status.setResponseTime(1L);
            
            Map<String, Object> details = new HashMap<>();
            details.put("total", totalSpace / (1024 * 1024 * 1024) + "GB");
            details.put("free", freeSpace / (1024 * 1024 * 1024) + "GB");
            details.put("usage", String.format("%.1f%%", usagePercent));
            status.setDetails(details);
            
        } catch (Exception e) {
            log.error("磁盘检查失败", e);
            status.setStatus("DOWN");
            status.setResponseTime(0L);
        }
        
        return status;
    }

    /**
     * 获取系统信息
     */
    private SystemStatusResponse.SystemInfo getSystemInfo() {
        SystemStatusResponse.SystemInfo systemInfo = new SystemStatusResponse.SystemInfo();
        
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        
        systemInfo.setOsName(osBean.getName());
        systemInfo.setOsArch(osBean.getArch());
        systemInfo.setJavaVersion(System.getProperty("java.version"));
        systemInfo.setCpuCores(osBean.getAvailableProcessors());
        
        long totalMemory = memoryBean.getHeapMemoryUsage().getMax() / (1024 * 1024);
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed() / (1024 * 1024);
        long availableMemory = totalMemory - usedMemory;
        
        systemInfo.setTotalMemory(totalMemory);
        systemInfo.setUsedMemory(usedMemory);
        systemInfo.setAvailableMemory(availableMemory);
        systemInfo.setMemoryUsage((double) usedMemory / totalMemory * 100);
        
        return systemInfo;
    }

    /**
     * 获取应用信息
     */
    private SystemStatusResponse.ApplicationInfo getApplicationInfo() {
        SystemStatusResponse.ApplicationInfo appInfo = new SystemStatusResponse.ApplicationInfo();
        
        appInfo.setName("app-resume-backend");
        appInfo.setVersion("1.0.0");
        appInfo.setProfile(System.getProperty("spring.profiles.active", "default"));
        
        // 获取JVM启动时间
        long startTime = ManagementFactory.getRuntimeMXBean().getStartTime();
        appInfo.setStartTime(LocalDateTime.now().minusSeconds(
                (System.currentTimeMillis() - startTime) / 1000));
        appInfo.setUptime(ManagementFactory.getRuntimeMXBean().getUptime() / 1000);
        
        return appInfo;
    }

    /**
     * 获取数据库信息
     */
    private SystemStatusResponse.DatabaseInfo getDatabaseInfo() {
        SystemStatusResponse.DatabaseInfo dbInfo = new SystemStatusResponse.DatabaseInfo();
        
        try (Connection connection = dataSource.getConnection()) {
            long startTime = System.currentTimeMillis();
            connection.createStatement().executeQuery("SELECT 1").close();
            long responseTime = System.currentTimeMillis() - startTime;
            
            dbInfo.setType(connection.getMetaData().getDatabaseProductName());
            dbInfo.setVersion(connection.getMetaData().getDatabaseProductVersion());
            dbInfo.setStatus("UP");
            dbInfo.setResponseTime(responseTime);
            dbInfo.setActiveConnections(5); // 模拟值
            dbInfo.setMaxConnections(20);   // 模拟值
            
        } catch (Exception e) {
            log.error("获取数据库信息失败", e);
            dbInfo.setStatus("DOWN");
            dbInfo.setResponseTime(0L);
        }
        
        return dbInfo;
    }

    /**
     * 获取Redis信息（模拟）
     */
    private SystemStatusResponse.RedisInfo getRedisInfo() {
        SystemStatusResponse.RedisInfo redisInfo = new SystemStatusResponse.RedisInfo();
        
        redisInfo.setVersion("7.0.5");
        redisInfo.setStatus("UP");
        redisInfo.setUsedMemory(128L);
        redisInfo.setKeyCount(1500L);
        redisInfo.setResponseTime(2L);
        
        return redisInfo;
    }

    /**
     * 获取MinIO信息（模拟）
     */
    private SystemStatusResponse.MinioInfo getMinioInfo() {
        SystemStatusResponse.MinioInfo minioInfo = new SystemStatusResponse.MinioInfo();
        
        minioInfo.setStatus("UP");
        minioInfo.setBucketCount(4);
        minioInfo.setObjectCount(2500L);
        minioInfo.setTotalSize(10240L);
        minioInfo.setResponseTime(25L);
        
        return minioInfo;
    }

    /**
     * 获取性能指标
     */
    private SystemMetricsResponse.PerformanceMetrics getPerformanceMetrics() {
        SystemMetricsResponse.PerformanceMetrics performance = new SystemMetricsResponse.PerformanceMetrics();
        
        // 模拟性能指标数据
        performance.setAvgResponseTime(120.0);
        performance.setQps(150.0);
        performance.setCpuUsage(45.5);
        performance.setMemoryUsage(68.2);
        performance.setDiskUsage(35.8);
        performance.setNetworkIo(1024.0);
        performance.setDbPoolUsage(25.0);
        performance.setRedisConnections(10);
        
        return performance;
    }

    /**
     * 获取业务指标
     */
    private SystemMetricsResponse.BusinessMetrics getBusinessMetrics() {
        SystemMetricsResponse.BusinessMetrics business = new SystemMetricsResponse.BusinessMetrics();
        
        // 模拟业务指标数据
        business.setOnlineUsers(156);
        business.setTodayNewUsers(45);
        business.setTodayActiveUsers(520);
        business.setTodayResumeCreated(156);
        business.setTodayResumeExported(89);
        business.setTodayOrders(15);
        business.setTodayRevenue(1500.00);
        business.setPendingOrders(8);
        
        return business;
    }

    /**
     * 获取错误指标
     */
    private SystemMetricsResponse.ErrorMetrics getErrorMetrics() {
        SystemMetricsResponse.ErrorMetrics errors = new SystemMetricsResponse.ErrorMetrics();
        
        // 模拟错误指标数据
        errors.setTodayErrorCount(12);
        errors.setErrorRate(0.8);
        errors.setRecentErrors(java.util.List.of(
                createRecentError("BusinessException", "文件上传失败", 3, LocalDateTime.now().minusMinutes(5)),
                createRecentError("ValidationException", "参数验证失败", 2, LocalDateTime.now().minusMinutes(10))
        ));
        
        return errors;
    }

    /**
     * 创建最近错误对象
     */
    private SystemMetricsResponse.RecentError createRecentError(String errorType, String message, 
                                                               Integer count, LocalDateTime lastOccurred) {
        SystemMetricsResponse.RecentError error = new SystemMetricsResponse.RecentError();
        error.setErrorType(errorType);
        error.setMessage(message);
        error.setCount(count);
        error.setLastOccurred(lastOccurred);
        return error;
    }
} 