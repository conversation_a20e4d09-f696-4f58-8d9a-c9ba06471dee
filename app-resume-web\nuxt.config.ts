export default defineNuxtConfig({
  devtools: { enabled: true },
  
  // CSS 框架
  css: ['~/assets/css/main.css'],
  
  // 模块配置
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/google-fonts',
    '@pinia/nuxt',
    '@nuxtjs/sitemap',
    '@nuxtjs/robots'
  ],

  // Google Fonts
  googleFonts: {
    families: {
      Inter: [300, 400, 500, 600, 700],
      'Plus Jakarta Sans': [300, 400, 500, 600, 700]
    }
  },

  // App 配置
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: '简历制作工具 - 专业在线简历生成器',
      meta: [
        { name: 'description', content: '提供100+专业简历模板，支持在线编辑、一键导出。帮助求职者制作出色的简历，提升面试成功率。' },
        { name: 'keywords', content: '在线简历制作,免费简历模板,简历生成器,求职简历,简历设计' },
        { property: 'og:title', content: '简历制作工具 - 专业在线简历生成器' },
        { property: 'og:description', content: '提供100+专业简历模板，支持在线编辑、一键导出。' },
        { property: 'og:type', content: 'website' },
        { name: 'twitter:card', content: 'summary_large_image' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },

  // 路由渲染策略
  routeRules: {
    // 首页预渲染（构建时生成）
    '/': { prerender: true },
    
    // 模板页面预渲染
    '/templates/**': { prerender: true },
    
    // 应用功能页面使用 SPA 模式
    '/editor/**': { ssr: false },
    '/my-resumes': { ssr: false },
    '/profile': { ssr: false },
    
    // 认证页面使用 SPA 模式
    '/login': { ssr: false },
    '/register': { ssr: false },
    
    // API 路由
    '/api/**': { cors: true }
  },

  // 站点配置
  site: {
    url: process.env.NUXT_PUBLIC_SITE_URL || 'https://resume.example.com',
    name: '火花简历'
  },

  // 运行时配置
  runtimeConfig: {
    // 服务端环境变量
    apiSecret: '',
    
    // 公开环境变量
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:9311',
      appName: '火花简历',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'https://resume.example.com'
    }
  },

  // Nitro 配置
  nitro: {
    prerender: {
      routes: [
        '/',
        '/templates'
      ]
    },
    // 开发服务器代理配置
    devProxy: {
      '/api': {
        target: 'http://localhost:9311',
        changeOrigin: true
        // 移除 pathRewrite，保留 /api 前缀
      }
    }
  }
}) 