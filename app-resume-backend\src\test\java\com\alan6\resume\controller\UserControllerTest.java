package com.alan6.resume.controller;

import com.alan6.resume.dto.user.*;
import com.alan6.resume.service.IUsersService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 用户控制器集成测试
 * 
 * 测试内容：
 * 1. 用户信息管理接口
 * 2. 用户设置管理接口
 * 3. 头像上传接口
 * 4. 密码修改接口
 * 5. 手机号/邮箱绑定接口
 * 6. 账号注销接口
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@WebMvcTest(controllers = UserController.class)
@Import(TestSecurityConfig.class)
@DisplayName("用户控制器集成测试")
class UserControllerTest {

    /**
     * MockMvc测试工具
     */
    @Autowired
    private MockMvc mockMvc;

    /**
     * Mock的用户服务
     */
    @MockBean
    private IUsersService usersService;

    /**
     * Mock RateLimit相关组件
     */
    @MockBean
    private org.springframework.data.redis.core.RedisTemplate<String, Object> redisTemplate;

    /**
     * JSON序列化工具
     */
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试用户信息响应
     */
    private UserProfileResponse userProfileResponse;

    /**
     * 测试用户设置响应
     */
    private UserSettingsResponse userSettingsResponse;

    /**
     * 每个测试方法执行前的初始化
     */
    @BeforeEach
    void setUp() {
        // 初始化用户信息响应数据
        userProfileResponse = new UserProfileResponse();
        userProfileResponse.setId(1001L);
        userProfileResponse.setUsername("testuser");
        userProfileResponse.setEmail("test***@example.com");
        userProfileResponse.setPhone("138****8000");
        userProfileResponse.setNickname("测试用户");
        userProfileResponse.setGender((byte) 1);
        userProfileResponse.setBirthday(LocalDate.of(1990, 1, 1));
        userProfileResponse.setRegisterType((byte) 2);
        userProfileResponse.setRegisterPlatform("web");
        userProfileResponse.setPreferredLanguage("zh-CN");
        userProfileResponse.setIsPhoneVerified((byte) 1);

        // 初始化会员信息
        UserProfileResponse.MembershipInfo membershipInfo = new UserProfileResponse.MembershipInfo();
        membershipInfo.setLevel(0);
        userProfileResponse.setMembership(membershipInfo);

        // 初始化用户设置响应数据
        userSettingsResponse = new UserSettingsResponse();
        userSettingsResponse.setPreferredLanguage("zh-CN");
    }

    /**
     * 测试获取用户信息 - 成功场景
     */
    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    @DisplayName("获取用户信息 - 成功")
    void testGetUserProfile_Success() throws Exception {
        // Given: Mock服务返回用户信息
        when(usersService.getUserProfile(any(Long.class)))
                .thenReturn(userProfileResponse);

        // When & Then: 执行GET请求并验证响应
        mockMvc.perform(get("/user/profile")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1001))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.email").value("test***@example.com"))
                .andExpect(jsonPath("$.data.nickname").value("测试用户"))
                .andExpect(jsonPath("$.data.membership.level").value(0));
    }

    /**
     * 测试更新用户信息 - 成功场景
     */
    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    @DisplayName("更新用户信息 - 成功")
    void testUpdateUserProfile_Success() throws Exception {
        // Given: 准备更新请求数据
        UserProfileRequest request = new UserProfileRequest();
        request.setNickname("新昵称");
        request.setGender((byte) 2);
        request.setBirthday(LocalDate.of(1995, 6, 15));
        request.setPreferredLanguage("en");

        // Mock服务返回成功
        when(usersService.updateUserProfile(any(Long.class), any(UserProfileRequest.class)))
                .thenReturn(true);

        // When & Then: 执行PUT请求并验证响应
        mockMvc.perform(put("/user/profile")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }

    /**
     * 测试上传头像 - 成功场景
     */
    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    @DisplayName("上传头像 - 成功")
    void testUploadAvatar_Success() throws Exception {
        // Given: 准备文件上传数据
        MockMultipartFile file = new MockMultipartFile(
                "file", "avatar.jpg", "image/jpeg", "test image content".getBytes());

        AvatarUploadResponse response = new AvatarUploadResponse();
        response.setAvatarUrl("https://example.com/avatar/new-avatar.jpg");

        // Mock服务返回上传结果
        when(usersService.uploadAvatar(any(Long.class), any()))
                .thenReturn(response);

        // When & Then: 执行文件上传请求并验证响应
        mockMvc.perform(multipart("/user/avatar")
                        .file(file)
                        .with(csrf()))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.avatarUrl").value("https://example.com/avatar/new-avatar.jpg"));
    }

    /**
     * 测试修改密码 - 成功场景
     */
    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    @DisplayName("修改密码 - 成功")
    void testChangePassword_Success() throws Exception {
        // Given: 准备密码修改请求数据
        PasswordChangeRequest request = new PasswordChangeRequest();
        request.setVerificationType(1); // 旧密码验证
        request.setOldPassword("oldpassword123");
        request.setNewPassword("newpassword123");
        request.setConfirmPassword("newpassword123");

        // Mock服务返回成功
        when(usersService.changePassword(any(Long.class), any(PasswordChangeRequest.class)))
                .thenReturn(true);

        // When & Then: 执行PUT请求并验证响应
        mockMvc.perform(put("/user/password")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试获取用户设置 - 成功场景
     */
    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    @DisplayName("获取用户设置 - 成功")
    void testGetUserSettings_Success() throws Exception {
        // Given: Mock服务返回用户设置
        when(usersService.getUserSettings(any(Long.class)))
                .thenReturn(userSettingsResponse);

        // When & Then: 执行GET请求并验证响应
        mockMvc.perform(get("/user/settings")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.preferredLanguage").value("zh-CN"));
    }

    /**
     * 测试更新用户设置 - 成功场景
     */
    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    @DisplayName("更新用户设置 - 成功")
    void testUpdateUserSettings_Success() throws Exception {
        // Given: 准备设置更新请求数据
        UserSettingsRequest request = new UserSettingsRequest();
        request.setPreferredLanguage("en");

        // Mock服务返回成功
        when(usersService.updateUserSettings(any(Long.class), any(UserSettingsRequest.class)))
                .thenReturn(true);

        // When & Then: 执行PUT请求并验证响应
        mockMvc.perform(put("/user/settings")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试绑定手机号 - 成功场景
     */
    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    @DisplayName("绑定手机号 - 成功")
    void testBindPhone_Success() throws Exception {
        // Given: 准备手机号绑定请求数据
        PhoneBindRequest request = new PhoneBindRequest();
        request.setPhone("13900139000");
        request.setSmsCode("123456");

        // Mock服务返回成功
        when(usersService.bindPhone(any(Long.class), any(PhoneBindRequest.class)))
                .thenReturn(true);

        // When & Then: 执行POST请求并验证响应
        mockMvc.perform(post("/user/phone/bind")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试未认证访问 - 应返回401
     */
    @Test
    @DisplayName("未认证访问 - 应返回401")
    void testUnauthenticatedAccess() throws Exception {
        // When & Then: 未认证状态访问用户信息接口
        mockMvc.perform(get("/user/profile")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isUnauthorized());
    }
} 