package com.alan6.resume.dto.template;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * HTML模板转换请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HtmlConversionRequest {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 转换类型（single: 单个转换, batch: 批量转换）
     */
    @NotBlank(message = "转换类型不能为空")
    private String conversionType;
    
    /**
     * HTML文件名列表
     */
    @NotEmpty(message = "HTML文件列表不能为空")
    private List<String> htmlFiles;
    
    /**
     * 转换选项
     */
    @Builder.Default
    private ConversionOptions options = new ConversionOptions();
    
    /**
     * 转换选项配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ConversionOptions {
        
        /**
         * 是否验证HTML规范
         */
        @Builder.Default
        private Boolean validateHtml = true;
        
        /**
         * 是否生成完整的Vue组件
         */
        @Builder.Default
        private Boolean generateFullComponent = true;
        
        /**
         * 是否保留原始HTML注释
         */
        @Builder.Default
        private Boolean preserveComments = false;
        
        /**
         * 是否格式化输出代码
         */
        @Builder.Default
        private Boolean formatOutput = true;
        
        /**
         * 是否生成TypeScript类型定义
         */
        @Builder.Default
        private Boolean generateTypes = false;
        
        /**
         * 目标Vue版本（vue2, vue3）
         */
        @Builder.Default
        private String targetVueVersion = "vue3";
        
        /**
         * CSS处理方式（scoped, module, global）
         */
        @Builder.Default
        private String cssProcessing = "scoped";
        
        /**
         * 是否启用严格模式
         */
        @Builder.Default
        private Boolean strictMode = false;
        
        /**
         * 自定义转换规则
         */
        private List<CustomRule> customRules;
    }
    
    /**
     * 自定义转换规则
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CustomRule {
        
        /**
         * 规则名称
         */
        private String name;
        
        /**
         * 规则类型（replace, transform, validate）
         */
        private String type;
        
        /**
         * 源模式（正则表达式或选择器）
         */
        private String sourcePattern;
        
        /**
         * 目标模式
         */
        private String targetPattern;
        
        /**
         * 规则描述
         */
        private String description;
        
        /**
         * 是否启用
         */
        @Builder.Default
        private Boolean enabled = true;
    }
    
    /**
     * 验证转换类型
     * 
     * @return 是否为有效的转换类型
     */
    public boolean isValidConversionType() {
        return "single".equals(conversionType) || "batch".equals(conversionType);
    }
    
    /**
     * 判断是否为单个转换
     * 
     * @return 是否为单个转换
     */
    public boolean isSingleConversion() {
        return "single".equals(conversionType);
    }
    
    /**
     * 判断是否为批量转换
     * 
     * @return 是否为批量转换
     */
    public boolean isBatchConversion() {
        return "batch".equals(conversionType);
    }
    
    /**
     * 获取HTML文件数量
     * 
     * @return 文件数量
     */
    public int getFileCount() {
        return htmlFiles != null ? htmlFiles.size() : 0;
    }
    
    /**
     * 创建默认转换选项
     * 
     * @return 默认转换选项
     */
    public static ConversionOptions createDefaultOptions() {
        return ConversionOptions.builder()
                .validateHtml(true)
                .generateFullComponent(true)
                .preserveComments(false)
                .formatOutput(true)
                .generateTypes(false)
                .targetVueVersion("vue3")
                .cssProcessing("scoped")
                .strictMode(false)
                .build();
    }
} 