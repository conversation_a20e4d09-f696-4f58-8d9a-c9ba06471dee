package com.alan6.resume.service;

import com.alan6.resume.entity.RolePermissions;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 角色权限关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface IRolePermissionsService extends IService<RolePermissions> {

    /**
     * 为角色分配权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean assignPermissionsToRole(Long roleId, List<Long> permissionIds);

    /**
     * 移除角色的所有权限
     *
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean removeAllPermissionsFromRole(Long roleId);

    /**
     * 根据角色ID获取权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getPermissionIdsByRoleId(Long roleId);
} 