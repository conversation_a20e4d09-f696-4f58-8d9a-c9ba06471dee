<?xml version="1.0" encoding="UTF-8"?>
<!--
    结构化日志配置文件
    
    主要功能：
    1. 支持结构化JSON日志输出
    2. 区分开发环境和生产环境配置
    3. 为后期链路追踪预留字段
    4. 配置日志轮转和清理策略
    
    <AUTHOR>
    @since 2024-12-17
-->
<configuration>
    
    <!-- 日志存储路径 -->
    <property name="LOG_HOME" value="logs"/>
    
    <!-- 应用名称 -->
    <property name="APP_NAME" value="resume-backend"/>
    
    <!-- 开发环境配置 -->
    <springProfile name="dev,test">
        <!-- 控制台输出：开发环境使用可读格式 -->
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        
        <!-- 文件输出：开发环境也使用结构化格式，便于调试 -->
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_HOME}/${APP_NAME}-dev.log</file>
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <includeContext>true</includeContext>
                <includeMdc>true</includeMdc>
                <customFields>{"service":"${APP_NAME}","env":"dev"}</customFields>
                <!-- 为链路追踪预留字段 -->
                <fieldNames class="net.logstash.logback.fieldnames.ShortenedFieldNames"/>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${LOG_HOME}/${APP_NAME}-dev.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>7</maxHistory>
                <totalSizeCap>1GB</totalSizeCap>
            </rollingPolicy>
        </appender>
        
        <!-- 根日志级别：开发环境使用DEBUG -->
        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
    
    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <!-- 应用日志文件：生产环境主要日志输出 -->
        <appender name="APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_HOME}/${APP_NAME}.log</file>
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <includeContext>true</includeContext>
                <includeMdc>true</includeMdc>
                <customFields>{"service":"${APP_NAME}","env":"prod"}</customFields>
                <!-- 优化字段名，减少日志体积 -->
                <fieldNames>
                    <timestamp>@timestamp</timestamp>
                    <level>level</level>
                    <logger>logger</logger>
                    <message>message</message>
                    <thread>thread</thread>
                </fieldNames>
                <!-- 为链路追踪预留字段映射 -->
                <includePattern>
                    <pattern>
                        {
                            "traceId": "%X{traceId:-}",
                            "spanId": "%X{spanId:-}",
                            "requestId": "%X{requestId:-}",
                            "userId": "%X{userId:-}",
                            "operation": "%X{operation:-}",
                            "clientIp": "%X{clientIp:-}",
                            "platform": "%X{platform:-}",
                            "duration": "%X{duration:-}"
                        }
                    </pattern>
                </includePattern>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${LOG_HOME}/${APP_NAME}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>500MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>20GB</totalSizeCap>
            </rollingPolicy>
        </appender>
        
        <!-- 错误日志文件：单独记录ERROR级别日志 -->
        <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_HOME}/${APP_NAME}-error.log</file>
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <includeContext>true</includeContext>
                <includeMdc>true</includeMdc>
                <customFields>{"service":"${APP_NAME}","env":"prod","logType":"error"}</customFields>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${LOG_HOME}/${APP_NAME}-error.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>90</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <!-- 只记录ERROR级别日志 -->
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>
        
        <!-- 业务日志文件：记录包含[BUSINESS]标记的日志 -->
        <appender name="BUSINESS" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_HOME}/${APP_NAME}-business.log</file>
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <includeContext>true</includeContext>
                <includeMdc>true</includeMdc>
                <customFields>{"service":"${APP_NAME}","env":"prod","logType":"business"}</customFields>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${LOG_HOME}/${APP_NAME}-business.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>60</maxHistory>
                <totalSizeCap>15GB</totalSizeCap>
            </rollingPolicy>
            <!-- 只记录包含[BUSINESS]的日志 -->
            <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
                <evaluator>
                    <expression>message.contains("[BUSINESS]")</expression>
                </evaluator>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>
        
        <!-- 根日志级别：生产环境使用INFO -->
        <root level="INFO">
            <appender-ref ref="APPLICATION"/>
            <appender-ref ref="ERROR"/>
            <appender-ref ref="BUSINESS"/>
        </root>
    </springProfile>
    
    <!-- 特定包的日志级别配置 -->
    
    <!-- Spring框架日志级别 -->
    <logger name="org.springframework" level="WARN" additivity="false">
        <appender-ref ref="APPLICATION"/>
    </logger>
    
    <!-- MyBatis日志级别 -->
    <logger name="com.baomidou.mybatisplus" level="INFO" additivity="false">
        <appender-ref ref="APPLICATION"/>
    </logger>
    
    <!-- Druid数据源日志级别 -->
    <logger name="com.alibaba.druid" level="INFO" additivity="false">
        <appender-ref ref="APPLICATION"/>
    </logger>
    
    <!-- Redis客户端日志级别 -->
    <logger name="org.redisson" level="INFO" additivity="false">
        <appender-ref ref="APPLICATION"/>
    </logger>
    
    <!-- 应用业务日志：保持DEBUG级别便于问题排查 -->
    <logger name="com.alan6.resume" level="DEBUG" additivity="false">
        <appender-ref ref="APPLICATION"/>
        <appender-ref ref="ERROR"/>
        <appender-ref ref="BUSINESS"/>
    </logger>
    
    <!-- 安全相关日志：保持详细记录 -->
    <logger name="com.alan6.resume.security" level="INFO" additivity="false">
        <appender-ref ref="APPLICATION"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <!-- MongoDB相关日志 -->
    <logger name="org.mongodb.driver" level="INFO"/>

</configuration> 