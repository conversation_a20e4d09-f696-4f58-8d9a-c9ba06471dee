package com.alan6.resume.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 帮助文章详情响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(description = "帮助文章详情响应DTO")
public class HelpArticleResponse {

    /**
     * 文章ID
     */
    @Schema(description = "文章ID")
    private Long id;

    /**
     * 文章标题
     */
    @Schema(description = "文章标题", example = "如何注册账号")
    private String title;

    /**
     * 文章内容
     */
    @Schema(description = "文章内容")
    private String content;

    /**
     * 分类代码
     */
    @Schema(description = "分类代码", example = "getting-started")
    private String category;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"注册\", \"账号\", \"新手\"]")
    private List<String> tags;

    /**
     * 浏览次数
     */
    @Schema(description = "浏览次数", example = "1250")
    private Integer viewCount;

    /**
     * 有用评价次数
     */
    @Schema(description = "有用评价次数", example = "89")
    private Integer helpful;

    /**
     * 无用评价次数
     */
    @Schema(description = "无用评价次数", example = "5")
    private Integer unhelpful;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-12-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 相关文章列表
     */
    @Schema(description = "相关文章列表")
    private List<RelatedArticle> relatedArticles;

    /**
     * 相关文章信息
     */
    @Data
    @Schema(description = "相关文章信息")
    public static class RelatedArticle {
        
        /**
         * 文章ID
         */
        @Schema(description = "文章ID")
        private Long id;

        /**
         * 文章标题
         */
        @Schema(description = "文章标题", example = "创建第一份简历")
        private String title;

        /**
         * 访问URL
         */
        @Schema(description = "访问URL", example = "/help/getting-started/first-resume")
        private String url;
    }
} 