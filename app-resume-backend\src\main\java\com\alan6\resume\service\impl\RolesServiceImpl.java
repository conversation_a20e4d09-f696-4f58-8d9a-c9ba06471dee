package com.alan6.resume.service.impl;

import com.alan6.resume.entity.Roles;
import com.alan6.resume.mapper.RolesMapper;
import com.alan6.resume.service.IRolesService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RolesServiceImpl extends ServiceImpl<RolesMapper, Roles> implements IRolesService {

    private final RolesMapper rolesMapper;

    @Override
    public List<Roles> getRolesByUserId(Long userId) {
        if (userId == null) {
            return List.of();
        }
        return rolesMapper.selectRolesByUserId(userId);
    }

    @Override
    public Roles getRoleByCode(String roleCode) {
        if (roleCode == null || roleCode.trim().isEmpty()) {
            return null;
        }
        return rolesMapper.selectByRoleCode(roleCode);
    }

    @Override
    public List<Roles> getEnabledRoles() {
        return rolesMapper.selectEnabledRoles();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(Roles role) {
        try {
            // 检查角色代码是否已存在
            if (existsRoleCode(role.getRoleCode(), null)) {
                log.warn("角色代码已存在: {}", role.getRoleCode());
                return false;
            }

            // 设置默认值
            if (role.getStatus() == null) {
                role.setStatus((byte) 1);
            }
            if (role.getSortOrder() == null) {
                role.setSortOrder(0);
            }

            return this.save(role);
        } catch (Exception e) {
            log.error("创建角色失败", e);
            throw new RuntimeException("创建角色失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(Roles role) {
        try {
            // 检查角色代码是否已存在
            if (existsRoleCode(role.getRoleCode(), role.getId())) {
                log.warn("角色代码已存在: {}", role.getRoleCode());
                return false;
            }

            return this.updateById(role);
        } catch (Exception e) {
            log.error("更新角色失败", e);
            throw new RuntimeException("更新角色失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long roleId) {
        try {
            // 检查是否为系统内置角色
            Roles role = this.getById(roleId);
            if (role != null && isSystemRole(role.getRoleCode())) {
                log.warn("不能删除系统内置角色: {}", role.getRoleCode());
                return false;
            }

            return this.removeById(roleId);
        } catch (Exception e) {
            log.error("删除角色失败", e);
            throw new RuntimeException("删除角色失败", e);
        }
    }

    @Override
    public boolean existsRoleCode(String roleCode, Long excludeId) {
        if (roleCode == null || roleCode.trim().isEmpty()) {
            return false;
        }

        LambdaQueryWrapper<Roles> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Roles::getRoleCode, roleCode);
        if (excludeId != null) {
            queryWrapper.ne(Roles::getId, excludeId);
        }

        return this.count(queryWrapper) > 0;
    }

    /**
     * 检查是否为系统内置角色
     *
     * @param roleCode 角色代码
     * @return 是否为系统角色
     */
    private boolean isSystemRole(String roleCode) {
        return "SUPER_ADMIN".equals(roleCode) || "ADMIN".equals(roleCode) || "USER".equals(roleCode);
    }
} 