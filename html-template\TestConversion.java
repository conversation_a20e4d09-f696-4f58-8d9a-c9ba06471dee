import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class TestConversion {
    public static void main(String[] args) {
        try {
            String htmlContent = Files.readString(Paths.get("1.html"));
            
            String result = htmlContent;
            
            // Basic data binding fixes
            result = result.replaceAll("data-vue-text=\"([^\"]+)\"", ">{{ $1 }}<");
            result = result.replaceAll("data-vue-if=\"([^\"]+)\"", "v-if=\"$1\""); 
            result = result.replaceAll("data-vue-for=\"([^\"]+)\"", "v-for=\"$1\"");
            result = result.replaceAll("data-vue-key=\"([^\"]+)\"", ":key=\"$1\"");
            result = result.replaceAll("data-vue-src=\"([^\"]+)\"", ":src=\"$1\"");
            
            // Fix Vue text interpolation patterns
            result = result.replaceAll(">\\{\\{ ([^}]+) \\}\\}<([^<]*)</span>", ">{{ $1 }}</span>");
            result = result.replaceAll(">\\{\\{ ([^}]+) \\}\\}<([^<]*)</div>", ">{{ $1 }}</div>");
            result = result.replaceAll(">\\{\\{ ([^}]+) \\}\\}<", ">{{ $1 }}</span>");
            
            // Fix data paths
            result = result.replaceAll("resumeData\\.basic_info", "mergedResumeData.basicInfo");
            result = result.replaceAll("resumeData\\.education", "mergedResumeData.educations");  
            result = result.replaceAll("resumeData\\.work_experience", "mergedResumeData.workExperiences");
            result = result.replaceAll("resumeData\\.project", "mergedResumeData.projects");
            result = result.replaceAll("resumeData\\.skills", "mergedResumeData.skills");
            result = result.replaceAll("resumeData\\.award", "mergedResumeData.awards");
            result = result.replaceAll("resumeData\\.self_evaluation", "mergedResumeData.selfEvaluation");
            
            // Clean up data attributes
            result = result.replaceAll("\\s*data-module-root=\"true\"", "");
            result = result.replaceAll("\\s*data-module=\"[^\"]*\"", "");
            
            Files.writeString(Paths.get("1_final.vue"), result);
            System.out.println("Final conversion completed, saved to 1_final.vue");
            
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
} 