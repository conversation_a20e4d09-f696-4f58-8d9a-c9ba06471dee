-- 手动创建测试用户脚本
-- 确保测试账户能够正常登录

-- 删除现有测试用户（如果存在）
DELETE FROM user_roles WHERE user_id IN (1001, 1002);
DELETE FROM users WHERE id IN (1001, 1002);

-- 创建测试用户（明文密码，方便测试）
INSERT INTO users (id, phone, username, password, nickname, status, register_platform, create_time, update_time) VALUES
(1001, '13800138001', 'admin', '123456', '系统管理员', 1, 'web', NOW(), NOW()),
(1002, '13800138002', 'superadmin', '123456', '超级管理员', 1, 'web', NOW(), NOW());

-- 确保角色存在
INSERT IGNORE INTO roles (id, role_name, role_code, description, status, sort_order, create_time, update_time) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, 1, NOW(), NOW()),
(2, '管理员', 'ADMIN', '系统管理员，拥有大部分管理权限', 1, 2, NOW(), NOW()),
(3, '普通用户', 'USER', '普通用户，只能使用基础功能', 1, 3, NOW(), NOW());

-- 分配角色
INSERT IGNORE INTO user_roles (user_id, role_id, create_time) VALUES
(1001, 2, NOW()),  -- admin用户分配管理员角色
(1002, 1, NOW());  -- superadmin用户分配超级管理员角色

-- 确保权限存在（使用正确的字段名）
INSERT IGNORE INTO permissions (id, permission_name, permission_code, resource_type, resource_url, parent_id, sort_order, status, create_time, update_time) VALUES
(1, '后台管理', 'ADMIN_ACCESS', 'menu', '/admin', 0, 1, 1, NOW(), NOW()),
(2, '用户管理', 'USER_MANAGE', 'menu', '/admin/users', 1, 2, 1, NOW(), NOW()),
(3, '模板管理', 'TEMPLATE_MANAGE', 'menu', '/admin/templates', 1, 3, 1, NOW(), NOW()),
(4, '订单管理', 'ORDER_MANAGE', 'menu', '/admin/orders', 1, 4, 1, NOW(), NOW()),
(5, '系统设置', 'SYSTEM_SETTING', 'menu', '/admin/system', 1, 5, 1, NOW(), NOW());

-- 分配权限给角色
INSERT IGNORE INTO role_permissions (role_id, permission_id, create_time) VALUES
-- 超级管理员拥有所有权限
(1, 1, NOW()), (1, 2, NOW()), (1, 3, NOW()), (1, 4, NOW()), (1, 5, NOW()),
-- 普通管理员拥有基础权限
(2, 1, NOW()), (2, 2, NOW()), (2, 3, NOW()), (2, 4, NOW());

-- 验证数据
SELECT 
    u.id, u.phone, u.username, u.nickname,
    r.role_name, r.role_code
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
WHERE u.phone IN ('13800138001', '13800138002'); 