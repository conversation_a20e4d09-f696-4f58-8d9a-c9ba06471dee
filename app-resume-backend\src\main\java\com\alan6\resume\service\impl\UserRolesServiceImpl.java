package com.alan6.resume.service.impl;

import com.alan6.resume.entity.Roles;
import com.alan6.resume.entity.UserRoles;
import com.alan6.resume.mapper.UserRolesMapper;
import com.alan6.resume.service.IRolesService;
import com.alan6.resume.service.IUserRolesService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户角色关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserRolesServiceImpl extends ServiceImpl<UserRolesMapper, UserRoles> implements IUserRolesService {

    private final UserRolesMapper userRolesMapper;
    private final IRolesService rolesService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRolesToUser(Long userId, List<Long> roleIds) {
        try {
            // 先删除用户现有的角色
            removeUserRoles(userId);

            // 如果角色列表为空，直接返回成功
            if (roleIds == null || roleIds.isEmpty()) {
                return true;
            }

            // 批量插入新的角色关联
            List<UserRoles> userRoles = new ArrayList<>();
            for (Long roleId : roleIds) {
                UserRoles userRole = new UserRoles();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRoles.add(userRole);
            }

            return this.saveBatch(userRoles);
        } catch (Exception e) {
            log.error("为用户分配角色失败, userId: {}, roleIds: {}", userId, roleIds, e);
            throw new RuntimeException("为用户分配角色失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserRoles(Long userId) {
        try {
            LambdaQueryWrapper<UserRoles> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserRoles::getUserId, userId);
            return this.remove(queryWrapper);
        } catch (Exception e) {
            log.error("移除用户角色失败, userId: {}", userId, e);
            throw new RuntimeException("移除用户角色失败", e);
        }
    }

    @Override
    public List<Long> getUserRoleIds(Long userId) {
        if (userId == null) {
            return List.of();
        }
        return userRolesMapper.selectRoleIdsByUserId(userId);
    }

    @Override
    public boolean hasRole(Long userId, String roleCode) {
        if (userId == null || roleCode == null || roleCode.trim().isEmpty()) {
            return false;
        }

        List<Roles> roles = rolesService.getRolesByUserId(userId);
        return roles.stream().anyMatch(role -> roleCode.equals(role.getRoleCode()));
    }

    @Override
    public boolean isAdmin(Long userId) {
        return hasRole(userId, "SUPER_ADMIN") || hasRole(userId, "ADMIN");
    }
} 