<template>
  <div class="resume-template" :class="{ 'preview-mode': !isDraggable }">
      <div class="header">
        <div class="header-left basic-info">
          <h1><span>{{ mergedResumeData.basicInfo.name }}</span></h1>
          <p>
            <span v-if="mergedResumeData.basicInfo.gender">{{ mergedResumeData.basicInfo.gender }}</span>
            <span v-if="mergedResumeData.basicInfo.gender">｜</span>
            <span v-if="mergedResumeData.basicInfo.age">{{ mergedResumeData.basicInfo.age }}</span>
            <span v-if="mergedResumeData.basicInfo.age">｜</span>
            <span>{{ mergedResumeData.basicInfo.currentCity }}</span>｜📞
            <span>{{ mergedResumeData.basicInfo.phone }}</span>｜✉️
            <span>{{ mergedResumeData.basicInfo.email }}</span>
          </p>
          <p>
            当前状态：<span>{{ mergedResumeData.basicInfo.currentStatus }}</span>｜
            期望岗位：<span>{{ mergedResumeData.basicInfo.expectedPosition }}</span>｜
            期望薪资：<span>{{ mergedResumeData.basicInfo.expectedSalary }}</span>
          </p>
        </div>
        <div class="header-right" v-if="mergedResumeData.basicInfo.avatar">
          <img width="96" height="96" style="border-radius: 10px; object-fit: cover;"  />
        </div>
        <div class="header-right" v-if="!mergedResumeData.basicInfo.avatar">
          头像
        </div>
      </div>

      <section class="resume-section" v-if="mergedResumeData.basicInfo.education && mergedResumeData.educations.length > 0">
        <div class="section-title">教育经历</div>
        <div v-for="(education, index) in mergedResumeData.educations" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredEducation = index" @mouseleave="hoveredEducation = -1" :key="education.id" class="resume-item">
                <!-- educations项操作按钮 -->
                <div v-if="isDraggable && hoveredEducation === index" class="item-actions">
                  <button 
                    @click="moveEducationUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveEducationDown(index)" 
                    :disabled="index === mergedResumeData.educations.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteEducation(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

          <div class="item-title">
            <span>
              <span>{{ education.school }}</span>｜
              <span>{{ education.major }}</span>
              <span>{{ education.degree }}</span>
            </span>
            <span>
              <span>{{ education.startDate }}</span> - <span>{{ education.endDate }}</span>
            </span>
          </div>
          <div class="item-desc" v-if="education.description">{{ education.description }}</div>
        </div>
      </section>

      <section class="resume-section" v-if="mergedResumeData.basicInfo.workExperience && mergedResumeData.workExperiences.length > 0">
        <div class="section-title">工作经历</div>
        <div v-for="(work, index) in mergedResumeData.workExperiences" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredExperience = index" @mouseleave="hoveredExperience = -1" :key="work.id" class="resume-item">
                <!-- workExperiences项操作按钮 -->
                <div v-if="isDraggable && hoveredExperience === index" class="item-actions">
                  <button 
                    @click="moveExperienceUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveExperienceDown(index)" 
                    :disabled="index === mergedResumeData.workExperiences.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteExperience(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

          <div class="item-title">
            <span>
              <span>{{ work.company }}</span>｜<span>{{ work.position }}</span>
            </span>
            <span>
              <span>{{ work.startDate }}</span> - <span>{{ work.endDate }}</span>
            </span>
          </div>
          <div class="item-desc" v-if="work.description">{{ work.description }}</div>
        </div>
      </section>

      <section class="resume-section" v-if="mergedResumeData.basicInfo.project && mergedResumeData.projects.length > 0">
        <div class="section-title">项目经历</div>
        <div v-for="(project, index) in mergedResumeData.projects" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredProject = index" @mouseleave="hoveredProject = -1" :key="project.id" class="resume-item">
                <!-- projects项操作按钮 -->
                <div v-if="isDraggable && hoveredProject === index" class="item-actions">
                  <button 
                    @click="moveProjectUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveProjectDown(index)" 
                    :disabled="index === mergedResumeData.projects.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteProject(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

          <div class="item-title">
            <span>{{ project.name }}</span>｜<span>{{ project.role }}</span>
          </div>
          <div class="item-desc">
            <div v-if="project.technologies">技术栈：<span>{{ project.technologies }}</span></div>
            <div v-if="project.description">{{ project.description }}</div>
          </div>
        </div>
      </section>

      <section class="resume-section" v-if="mergedResumeData.basicInfo.skills && mergedResumeData.skills.length > 0">
        <div class="section-title">技能特长</div>
        <div v-for="(skill, index) in mergedResumeData.skills" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredSkill = index" @mouseleave="hoveredSkill = -1" :key="skill.id" class="resume-item">
                <!-- skills项操作按钮 -->
                <div v-if="isDraggable && hoveredSkill === index" class="item-actions">
                  <button 
                    @click="moveSkillUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveSkillDown(index)" 
                    :disabled="index === mergedResumeData.skills.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteSkill(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

          <div class="item-title">
            <span>{{ skill.name }}</span>
            <span v-if="skill.level">{{ skill.level }}</span>
          </div>
        </div>
      </section>

      <section class="resume-section" v-if="mergedResumeData.basicInfo.awards && mergedResumeData.awards.length > 0">
        <div class="section-title">获奖荣誉</div>
        <div v-for="(award, index) in mergedResumeData.awards" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredAward = index" @mouseleave="hoveredAward = -1" :key="award.id" class="resume-item">
                <!-- awards项操作按钮 -->
                <div v-if="isDraggable && hoveredAward === index" class="item-actions">
                  <button 
                    @click="moveAwardUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveAwardDown(index)" 
                    :disabled="index === mergedResumeData.awards.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteAward(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

          <div class="item-title">
            <span>{{ award.name }}</span>
            <span v-if="award.organization">｜<span>{{ award.organization }}</span></span>
            <span v-if="award.level">｜<span>{{ award.level }}</span></span>
            <span v-if="award.date">
              <span>{{ award.date }}</span>
            </span>
          </div>
          <div class="item-desc" v-if="award.description">{{ award.description }}</div>
        </div>
      </section>

      <section class="resume-section" v-if="mergedResumeData.basicInfo.selfEvaluation && mergedResumeData.selfEvaluation.content">
        <div class="section-title">自我评价</div>
        <div class="item-desc">{{ mergedResumeData.selfEvaluation.content }}</div>
      </section>
    </div></template>

<script setup>
import { useResumeTemplateBase } from '~/composables/template/useResumeTemplateBase'

// 定义组件属性
const props = defineProps({
  resumeData: {
    type: Object,
    default: () => ({})
  },
  textStyle: {
    type: Object,
    default: () => ({})
  },
  isDraggable: {
    type: Boolean,
    default: false
  },
  visibleModules: {
    type: Array,
    default: () => []
  }
})

// 定义事件
const emit = defineEmits([
  'update-resume-data',
  'update-basic-info',
  'module-operation'
])

// 使用基础组合式函数
const {
  // 数据相关
  mergedResumeData,
  sortedModules,
  leftColumnModules,
  rightColumnModules,
  
  // 样式相关
  mergedTextStyle,
  titleStyle,
  subtitleStyle,
  bodyStyle,
  
  // 操作相关
  hoveredSkill,
  hoveredExperience,
  hoveredEducation,
  hoveredProject,
  hoveredAward,
  hoveredCertificate,
  hoveredLanguage,
  hoveredHobby,
  hoveredInternship,
  hoveredTraining,
  hoveredVolunteer,
  hoveredResearch,
  hoveredPublication,
  hoveredPortfolio,
  moveModuleUp,
  moveModuleDown,
  deleteModule,
  handleBasicInfoUpdate,
  
  // 工具方法
  formatWorkPeriod,
  formatEducationPeriod,
  formatProjectPeriod,
  getSkillProgressStyle
} = useResumeTemplateBase(props, emit)
</script>

<style scoped>
:root {
      --resume-font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      --resume-font-size: 14px;
      --resume-line-height: 1.6;
      --resume-primary-color: #007b81;
      --resume-text-color: #333;
      --resume-secondary-color: #666;
      --resume-margin-top: 60px;
      --resume-margin-bottom: 60px;
      --resume-margin-left: 60px;
      --resume-margin-right: 60px;
      --resume-module-spacing: 24px;
      --resume-item-spacing: 16px;
      --resume-paragraph-spacing: 12px;
      --resume-column-gap: 40px;
      --resume-module-padding: 16px;
      --resume-item-padding: 12px;
    }
    .resume-template {
      width: 794px;
      margin: 0 auto;
      border: 1px solid #eee;
      box-sizing: border-box;
    }
    .header {
      background: var(--resume-primary-color);
      color: #fff;
      padding: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .header-left h1 {
      font-size: 28px;
      margin: 0;
      font-weight: bold;
    }
    .header-left p {
      margin: 10px 0 0;
      font-size: 14px;
    }
    .header-right {
      width: 96px;
      height: 96px;
      border-radius: 10px;
      background: #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666;
    }

    .resume-section {
      padding: 20px 40px;
      border-bottom: 1px solid #ddd;
    }

    .section-title {
      display: inline-block;
      background-color: var(--resume-primary-color);
      color: #fff;
      padding: 5px 15px;
      font-size: 16px;
      font-weight: bold;
      position: relative;
      margin-bottom: 15px;
    }
    .section-title::after {
      content: "";
      position: absolute;
      right: -12px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-top: 12px solid transparent;
      border-bottom: 12px solid transparent;
      border-left: 12px solid var(--resume-primary-color);
    }

    .resume-item {
      margin-bottom: var(--resume-item-spacing);
    }
    .item-title {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
    }
    .item-desc {
      margin-top: 5px;
      font-size: 14px;
    }
</style>
