# PDF转换功能说明

## 功能概述

简历编辑器现在支持真正的HTML到PDF转换功能，不再使用模拟的PDF生成器。新的实现包括：

1. **真正的HTML模板生成**：根据前端传来的JSON简历数据生成完整的HTML模板
2. **Playwright PDF转换**：使用Playwright进行高质量的HTML到PDF转换
3. **备用方案**：当Playwright不可用时，自动降级到增强的模拟PDF生成器

## 实现架构

### 1. 数据流程
```
前端JSON数据 → 后端HTML模板生成 → Playwright转换 → PDF文件 → 下载
```

### 2. 核心组件

#### PdfExportServiceImpl
- `generateResumeHtml()`: 生成真正的简历HTML内容
- `convertWithPlaywright()`: 使用Playwright进行PDF转换
- `convertWithFallback()`: 备用PDF生成方案

#### HTML模板生成器
- `generateBasicInfoSection()`: 生成基本信息部分
- `generateSkillsSection()`: 生成技能部分
- `generateEducationSection()`: 生成教育经历部分
- `generateWorkExperienceSection()`: 生成工作经历部分
- `generateProjectsSection()`: 生成项目经历部分

## 安装和配置

### 1. 安装Playwright

#### Linux/macOS
```bash
cd app-resume-backend
chmod +x install-playwright.sh
./install-playwright.sh
```

#### Windows
```cmd
cd app-resume-backend
install-playwright.bat
```

### 2. 手动安装
```bash
# 安装Node.js和npm（如果尚未安装）
# 然后运行：
npm install playwright
npx playwright install chromium
```

## 支持的简历数据格式

系统支持两种JSON数据格式：

### 格式1：直接字段格式
```json
{
  "basicInfo": {
    "name": "张三",
    "title": "高级前端开发工程师",
    "phone": "138-0000-0000",
    "email": "<EMAIL>",
    "address": "北京市朝阳区"
  },
  "skills": [
    {
      "name": "JavaScript",
      "level": 90
    }
  ],
  "workExperiences": [
    {
      "company": "字节跳动",
      "position": "高级前端开发工程师",
      "startDate": "2022-07",
      "endDate": "至今",
      "description": "负责公司核心产品的前端开发工作..."
    }
  ],
  "educations": [
    {
      "school": "北京理工大学",
      "major": "计算机科学与技术",
      "degree": "本科",
      "startDate": "2018-09",
      "endDate": "2022-06"
    }
  ],
  "projects": [
    {
      "name": "在线简历编辑器",
      "role": "技术负责人",
      "startDate": "2023-01",
      "endDate": "2023-06",
      "description": "使用Vue.js和Nuxt.js构建的在线简历编辑器..."
    }
  ]
}
```

### 格式2：模块化格式
```json
{
  "modules": {
    "basic": {
      "data": {
        "name": "张三",
        "title": "高级前端开发工程师",
        "phone": "138-0000-0000",
        "email": "<EMAIL>",
        "address": "北京市朝阳区"
      }
    },
    "skills": {
      "data": [
        {
          "name": "JavaScript",
          "level": 90
        }
      ]
    },
    "work": {
      "data": [
        {
          "company": "字节跳动",
          "position": "高级前端开发工程师",
          "startDate": "2022-07",
          "endDate": "至今",
          "description": "负责公司核心产品的前端开发工作..."
        }
      ]
    },
    "education": {
      "data": [
        {
          "school": "北京理工大学",
          "major": "计算机科学与技术",
          "degree": "本科",
          "startDate": "2018-09",
          "endDate": "2022-06"
        }
      ]
    },
    "projects": {
      "data": [
        {
          "name": "在线简历编辑器",
          "role": "技术负责人",
          "startDate": "2023-01",
          "endDate": "2023-06",
          "description": "使用Vue.js和Nuxt.js构建的在线简历编辑器..."
        }
      ]
    }
  }
}
```

## 生成的HTML特性

### 1. 响应式设计
- 适配A4纸张尺寸（210mm宽度）
- 支持打印媒体查询
- 自动调整字体大小和间距

### 2. 现代样式
- 使用系统字体栈
- 蓝色主题色彩方案
- 卡片式布局
- 技能进度条可视化

### 3. 双栏布局
- 左栏：技能、教育经历
- 右栏：工作经历、项目经历
- 响应式间距和对齐

## 故障排除

### 1. Playwright安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 重新安装
npm install playwright
npx playwright install chromium
```

### 2. PDF转换失败
- 检查Node.js是否已安装
- 确保Playwright已正确安装
- 查看后端日志获取详细错误信息
- 系统会自动降级到备用PDF生成器

### 3. 中文字符显示问题
- 确保HTML模板使用UTF-8编码
- 检查系统字体是否支持中文
- 备用PDF生成器使用英文内容避免编码问题

## 性能优化

### 1. 缓存机制
- 简历数据缓存到Redis
- 避免重复生成相同内容

### 2. 异步处理
- PDF生成任务异步执行
- 支持任务状态查询

### 3. 资源管理
- 自动清理临时HTML文件
- 浏览器实例复用

## 扩展功能

### 1. 模板系统
- 支持多种简历模板
- 可自定义样式主题
- 模板热插拔

### 2. 高级选项
- 自定义页边距
- 背景图片支持
- 水印添加

### 3. 批量处理
- 支持批量简历导出
- 压缩包下载

## API接口

### 导出PDF
```
POST /resume/export
Content-Type: application/json

{
  "resumeId": 1001,
  "exportData": {
    // 简历JSON数据
  }
}
```

### 下载PDF
```
GET /resume/export/download/{fileName}
```

## 配置选项

### application.yml
```yaml
app:
  export:
    storage-path: tmp/resume-exports  # PDF存储路径
    url-prefix: http://localhost:9311/resume/export/download  # 下载URL前缀
  frontend:
    domain: http://localhost:3004  # 前端域名
```

## 日志和监控

### 关键日志
- PDF转换开始/完成
- Playwright命令执行
- 备用方案启用
- 文件清理操作

### 监控指标
- PDF转换成功率
- 平均转换时间
- 错误率统计
- 存储空间使用量

## 更新历史

### v2.0.0 (当前版本)
- ✅ 实现真正的HTML到PDF转换
- ✅ 支持Playwright高质量转换
- ✅ 完整的简历模板生成器
- ✅ 自动备用方案
- ✅ 支持多种数据格式
- ✅ 响应式设计和打印优化

### v1.0.0 (之前版本)
- ❌ 仅支持模拟PDF生成
- ❌ 使用iframe方式
- ❌ 有限的样式支持 