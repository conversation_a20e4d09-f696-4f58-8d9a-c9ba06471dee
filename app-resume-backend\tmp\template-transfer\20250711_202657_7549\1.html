<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经典商务简历模板</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 主容器样式 */
        body {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .resume-template {
            width: 100%;
            max-width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 20mm 15mm;
            background: white;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        /* 简历头部 */
        .resume-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .header-left {
            flex: 1;
        }

        .title-section {
            display: flex;
            align-items: baseline;
            gap: 40px;
            margin-bottom: 10px;
        }

        .resume-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .resume-title-en {
            font-size: 36px;
            font-weight: bold;
            color: #666;
            margin: 0;
            letter-spacing: 8px;
        }

        .divider-line {
            height: 3px;
            background: linear-gradient(to right, #333 0%, #333 30%, #666 100%);
            margin-top: 5px;
        }

        .header-right {
            width: 120px;
            height: 160px;
        }

        .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 2px solid #ddd;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        /* 基本信息区域 */
        .basic-info-section {
            margin-bottom: 25px;
        }

        .basic-info-grid {
            display: grid;
            gap: 8px;
        }

        .info-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .info-item {
            display: flex;
            align-items: center;
        }

        .info-label {
            font-weight: bold;
            color: #333;
            min-width: 80px;
        }

        .info-value {
            color: #555;
            margin-left: 8px;
        }

        /* 模块通用样式 */
        .section-block {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #4a5568;
        }

        .section-icon {
            font-size: 18px;
            margin-right: 8px;
            background: #4a5568;
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            min-width: 36px;
            text-align: center;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: #4a5568;
            padding: 6px 15px;
            margin: 0;
            margin-left: -2px;
        }

        .section-content {
            padding-left: 0;
        }

        /* 工作经历样式 */
        .experience-item {
            margin-bottom: 20px;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .company-position {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .date-range {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
        }

        .achievements-list {
            margin-left: 0;
        }

        .achievement-item {
            margin-bottom: 4px;
            color: #555;
            line-height: 1.6;
        }

        /* 教育经历样式 */
        .education-item {
            margin-bottom: 15px;
        }

        .school-degree {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0 0 5px 0;
        }

        .courses {
            margin-left: 0;
        }

        .course-item {
            margin-bottom: 3px;
            color: #555;
        }

        /* 项目经历样式 */
        .project-item {
            margin-bottom: 20px;
        }

        .project-header {
            margin-bottom: 8px;
        }

        .project-name {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0 0 5px 0;
        }

        .responsibilities, .achievements {
            margin-bottom: 8px;
        }

        .responsibility-item, .achievement-item {
            margin-bottom: 4px;
            color: #555;
            line-height: 1.6;
        }

        /* 获奖荣誉样式 */
        .awards-list {
            display: grid;
            gap: 6px;
        }

        .award-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .award-bullet {
            color: #4a5568;
            font-weight: bold;
            margin-top: 2px;
        }

        .award-text {
            flex: 1;
            color: #555;
        }

        /* 技能样式 */
        .skills-grid {
            display: grid;
            gap: 10px;
        }

        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .skill-name {
            font-weight: 500;
            color: #333;
            min-width: 100px;
        }

        .skill-level {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .skill-bar {
            flex: 1;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(to right, #4a5568, #718096);
            transition: width 0.3s ease;
        }

        .skill-percentage {
            font-size: 11px;
            color: #666;
            min-width: 35px;
            text-align: right;
        }

        /* 证书资质样式 */
        .certificates-list {
            display: grid;
            gap: 6px;
        }

        .certificate-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .cert-bullet {
            color: #4a5568;
            font-weight: bold;
        }

        .cert-name {
            font-weight: 500;
            color: #333;
        }

        .cert-level {
            color: #555;
        }

        /* 语言能力样式 */
        .languages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .language-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f7fafc;
            border-radius: 4px;
        }

        .language-name {
            font-weight: 500;
            color: #333;
        }

        .language-level {
            color: #666;
            font-size: 11px;
        }

        /* 其他模块样式 */
        .internship-item, .training-item, .volunteer-item, .research-item {
            margin-bottom: 15px;
        }

        .internship-header, .training-header, .volunteer-header, .research-header {
            margin-bottom: 6px;
        }

        .publication-item {
            margin-bottom: 12px;
        }

        .publication-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .publication-details {
            color: #666;
            font-size: 11px;
            margin-bottom: 2px;
        }

        .publication-authors {
            color: #555;
            font-size: 11px;
        }

        .hobbies-text, .self-evaluation-text {
            color: #555;
            line-height: 1.7;
            text-align: justify;
        }

        .portfolio-item, .cover-letter-item, .custom-item {
            margin-bottom: 12px;
        }

        .portfolio-title, .cover-letter-title, .custom-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .portfolio-description, .cover-letter-content, .custom-content {
            color: #555;
            line-height: 1.6;
        }

        .portfolio-url {
            color: #4a5568;
            text-decoration: none;
            font-size: 11px;
        }

        .portfolio-url:hover {
            text-decoration: underline;
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .resume-template {
                margin: 0;
                box-shadow: none;
                page-break-inside: avoid;
            }
            
            .section-block {
                page-break-inside: avoid;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .resume-template {
                padding: 15mm 10mm;
                font-size: 11px;
            }
            
            .resume-header {
                flex-direction: column;
                gap: 20px;
            }
            
            .title-section {
                flex-direction: column;
                gap: 10px;
            }
            
            .resume-title-en {
                font-size: 24px;
                letter-spacing: 4px;
            }
            
            .info-row {
                grid-template-columns: 1fr;
                gap: 8px;
            }
            
            .languages-grid {
                grid-template-columns: 1fr;
            }
            
            .header-right {
                width: 100px;
                height: 130px;
            }
        }
    </style>
</head>
<body>
    <div class="resume-template">
        <!-- 简历头部 -->
        <div class="resume-header">
            <div class="header-left">
                <div class="title-section">
                    <h1 class="resume-title">个人简历</h1>
                    <h1 class="resume-title-en">RESUME</h1>
                </div>
                <div class="divider-line"></div>
            </div>
            <div class="header-right">
                <div class="profile-photo">照片位置</div>
            </div>
        </div>

        <!-- 基本信息区域 -->
        <section class="basic-info-section">
            <div class="basic-info-grid">
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">姓名</span>
                        <span class="info-value">姚亮</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">求职意向:</span>
                        <span class="info-value">CAD设计师</span>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">期望薪资:</span>
                        <span class="info-value">10K-11K</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">工作地点:</span>
                        <span class="info-value">深圳</span>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">求职状态:</span>
                        <span class="info-value">离职</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">性别:</span>
                        <span class="info-value">女</span>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">出生年月:</span>
                        <span class="info-value">2002-01</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">身高:</span>
                        <span class="info-value">163</span>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">体重:</span>
                        <span class="info-value">50</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">电话:</span>
                        <span class="info-value">13333333333</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 工作经历模块 -->
        <section class="section-block work-section">
            <div class="section-header">
                <div class="section-icon">💼</div>
                <h2 class="section-title">工作经历</h2>
            </div>
            
            <div class="section-content">
                <div class="experience-item">
                    <div class="experience-header">
                        <h3 class="company-position">深圳湖田设计院 - 平面设计师</h3>
                        <div class="date-range">2014.05~至今</div>
                    </div>
                    
                    <div class="experience-content">
                        <div class="achievements-list">
                            <div class="achievement-item">1. 精准绘制机械零件，建筑平面图等各类CAD图纸，图纸一次通过率超85%，有效减少设计修改时间与成本。</div>
                            <div class="achievement-item">2. 与跨部门团队紧密协作，负责项目设计的沟通与协调，提出的20余条设计优化建议，多数被采用，有效提升产品质量与用户体验。</div>
                            <div class="achievement-item">3. 主导设计了多个项目，完成50余套CAD图纸，其中3个项目获公司年度优秀设计奖</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 教育经历模块 -->
        <section class="section-block education-section">
            <div class="section-header">
                <div class="section-icon">🎓</div>
                <h2 class="section-title">教育经历</h2>
            </div>
            
            <div class="section-content">
                <div class="education-item">
                    <div class="education-header">
                        <h3 class="school-degree">武汉大学 - 本科（土木工程）</h3>
                        <div class="date-range">2019.12~至今</div>
                    </div>
                    
                    <div class="courses">
                        <div class="course-item">1. 工程设计</div>
                        <div class="course-item">2. 施工安全</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 项目经历模块 -->
        <section class="section-block project-section">
            <div class="section-header">
                <div class="section-icon">📊</div>
                <h2 class="section-title">项目经历</h2>
            </div>
            
            <div class="section-content">
                <div class="project-item">
                    <div class="project-header">
                        <h3 class="project-name">电商平台优化 - 设计师</h3>
                        <div class="date-range">2022.12~至今</div>
                    </div>
                    
                    <div class="project-content">
                        <div class="responsibilities">
                            <div class="responsibility-item">1. 项目背景：某电商平台为提高用户体验与转化率，需对界面进行优化。</div>
                        </div>
                        <div class="achievements">
                            <div class="achievement-item">2. 项目执行：分析用户行为数据，找出界面痛点。重新规划导航栏，优化核心功能模块，采用高清图片与直观类型；设计促销活动页面，运用时尚插画与独特排版，突出视觉冲击力；更新产品包装，采用时尚插画与流行色彩，吸引目标客群。</div>
                            <div class="achievement-item">3. 项目成果：优化后平台用户停留时间延长20%，转化率提升15%。</div>
                        </div>
                    </div>
                </div>

                <div class="project-item">
                    <div class="project-header">
                        <h3 class="project-name">美妆品牌视觉升级 - 设计师</h3>
                        <div class="date-range">2022.12~2024.12</div>
                    </div>
                    
                    <div class="project-content">
                        <div class="responsibilities">
                            <div class="responsibility-item">1. 项目背景：某知名美妆品牌为契合新消费趋势，提升品牌辨识度与市场竞争力，委托进行全面视觉策划。</div>
                        </div>
                        <div class="achievements">
                            <div class="achievement-item">2. 项目执行：深入调研品牌历史，理念及目标受众喜好，提炼核心元素融入新设计，设计全新品牌标志，简化线条与色彩，增强视觉冲击力；更新产品包装，采用时尚插画与流行色彩，突出品牌特色，打造系列宣传海报，运用创意构图与流行色彩，吸引目标客群。</div>
                            <div class="achievement-item">3. 项目成果：新视觉形象获市场高度认可，产品销量提升30%，品牌社交媒体关注度增长50%。</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 获奖荣誉模块 -->
        <section class="section-block awards-section">
            <div class="section-header">
                <div class="section-icon">🏆</div>
                <h2 class="section-title">奖项证书</h2>
            </div>
            
            <div class="section-content">
                <div class="awards-list">
                    <div class="award-item">
                        <span class="award-bullet">•</span>
                        <span class="award-text">英语证书：英语三级证书</span>
                    </div>
                    <div class="award-item">
                        <span class="award-bullet">•</span>
                        <span class="award-text">计算机证书：计算机二级</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 专业技能模块 -->
        <section class="section-block skills-section">
            <div class="section-header">
                <div class="section-icon">⭐</div>
                <h2 class="section-title">专业技能</h2>
            </div>
            
            <div class="section-content">
                <div class="skills-grid">
                    <div class="skill-item">
                        <span class="skill-name">AutoCAD</span>
                        <div class="skill-level">
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 95%"></div>
                            </div>
                            <span class="skill-percentage">95%</span>
                        </div>
                    </div>
                    <div class="skill-item">
                        <span class="skill-name">SolidWorks</span>
                        <div class="skill-level">
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 88%"></div>
                            </div>
                            <span class="skill-percentage">88%</span>
                        </div>
                    </div>
                    <div class="skill-item">
                        <span class="skill-name">3D建模</span>
                        <div class="skill-level">
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 85%"></div>
                            </div>
                            <span class="skill-percentage">85%</span>
                        </div>
                    </div>
                    <div class="skill-item">
                        <span class="skill-name">工程制图</span>
                        <div class="skill-level">
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 92%"></div>
                            </div>
                            <span class="skill-percentage">92%</span>
                        </div>
                    </div>
                    <div class="skill-item">
                        <span class="skill-name">Photoshop</span>
                        <div class="skill-level">
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 80%"></div>
                            </div>
                            <span class="skill-percentage">80%</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 实习经历模块 -->
        <section class="section-block internships-section">
            <div class="section-header">
                <div class="section-icon">👨‍💼</div>
                <h2 class="section-title">实习经历</h2>
            </div>
            
            <div class="section-content">
                <div class="internship-item">
                    <div class="internship-header">
                        <h3 class="company-position">深圳建筑设计有限公司 - CAD实习生</h3>
                        <div class="date-range">2019.07~2019.09</div>
                    </div>
                    <div class="internship-description">负责建筑图纸的绘制和修改，协助资深设计师完成项目设计，学习了标准的CAD制图规范。</div>
                </div>
            </div>
        </section>

        <!-- 培训经历模块 -->
        <section class="section-block trainings-section">
            <div class="section-header">
                <div class="section-icon">📚</div>
                <h2 class="section-title">培训经历</h2>
            </div>
            
            <div class="section-content">
                <div class="training-item">
                    <div class="training-header">
                        <h3 class="training-name">高级CAD设计培训</h3>
                        <div class="training-institution">深圳职业技术学院</div>
                        <div class="date-range">2020.03~2020.06</div>
                    </div>
                    <div class="training-certificate">证书：高级CAD设计师证书</div>
                </div>
            </div>
        </section>

        <!-- 志愿服务模块 -->
        <section class="section-block volunteer-section">
            <div class="section-header">
                <div class="section-icon">🤝</div>
                <h2 class="section-title">志愿服务</h2>
            </div>
            
            <div class="section-content">
                <div class="volunteer-item">
                    <div class="volunteer-header">
                        <h3 class="volunteer-organization">深圳市青年志愿者协会 - 设计志愿者</h3>
                        <div class="date-range">2020.01~2021.12</div>
                    </div>
                    <div class="volunteer-description">为公益组织设计宣传海报和活动物料，累计服务时长120小时。</div>
                </div>
            </div>
        </section>

        <!-- 研究经历模块 -->
        <section class="section-block research-section">
            <div class="section-header">
                <div class="section-icon">🔬</div>
                <h2 class="section-title">研究经历</h2>
            </div>
            
            <div class="section-content">
                <div class="research-item">
                    <div class="research-header">
                        <h3 class="research-title">建筑节能设计研究</h3>
                        <div class="research-institution">武汉大学土木工程学院</div>
                        <div class="date-range">2021.03~2022.03</div>
                    </div>
                    <div class="research-description">参与导师的建筑节能设计研究项目，负责CAD建模和数据分析工作。</div>
                </div>
            </div>
        </section>

        <!-- 论文专利模块 -->
        <section class="section-block publications-section">
            <div class="section-header">
                <div class="section-icon">📝</div>
                <h2 class="section-title">论文专利</h2>
            </div>
            
            <div class="section-content">
                <div class="publication-item">
                    <div class="publication-title">基于CAD的建筑节能设计优化研究</div>
                    <div class="publication-details">建筑设计学报 • 2022</div>
                    <div class="publication-authors">作者：姚亮, 李教授</div>
                </div>
            </div>
        </section>

        <!-- 证书资质模块 -->
        <section class="section-block certificates-section">
            <div class="section-header">
                <div class="section-icon">📜</div>
                <h2 class="section-title">证书资质</h2>
            </div>
            
            <div class="section-content">
                <div class="certificates-list">
                    <div class="certificate-item">
                        <span class="cert-bullet">•</span>
                        <span class="cert-name">英语证书</span>
                        <span class="cert-level">：英语三级证书</span>
                    </div>
                    <div class="certificate-item">
                        <span class="cert-bullet">•</span>
                        <span class="cert-name">计算机证书</span>
                        <span class="cert-level">：计算机二级</span>
                    </div>
                    <div class="certificate-item">
                        <span class="cert-bullet">•</span>
                        <span class="cert-name">CAD工程师认证</span>
                        <span class="cert-level">：高级</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 语言能力模块 -->
        <section class="section-block languages-section">
            <div class="section-header">
                <div class="section-icon">🌐</div>
                <h2 class="section-title">语言能力</h2>
            </div>
            
            <div class="section-content">
                <div class="languages-grid">
                    <div class="language-item">
                        <span class="language-name">英语</span>
                        <span class="language-level">熟练</span>
                    </div>
                    <div class="language-item">
                        <span class="language-name">普通话</span>
                        <span class="language-level">母语</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 兴趣爱好模块 -->
        <section class="section-block hobbies-section">
            <div class="section-header">
                <div class="section-icon">🎯</div>
                <h2 class="section-title">兴趣爱好</h2>
            </div>
            
            <div class="section-content">
                <div class="hobbies-text">喜欢摄影、旅行和阅读设计类书籍，经常参加设计展览和行业交流活动，保持对新技术和设计趋势的敏感度。</div>
            </div>
        </section>

        <!-- 自我评价模块 -->
        <section class="section-block self-evaluation-section">
            <div class="section-header">
                <div class="section-icon">💭</div>
                <h2 class="section-title">自我评价</h2>
            </div>
            
            <div class="section-content">
                <div class="self-evaluation-text">本人具有扎实的CAD设计基础和丰富的项目经验，熟练掌握多种设计软件，具备良好的空间想象能力和细致的工作态度。在团队协作中表现出色，能够有效沟通并推进项目进展。持续学习新技术，追求设计品质的提升，希望在CAD设计领域继续深耕发展。</div>
            </div>
        </section>

        <!-- 作品集模块 -->
        <section class="section-block portfolios-section">
            <div class="section-header">
                <div class="section-icon">🎨</div>
                <h2 class="section-title">作品集</h2>
            </div>
            
            <div class="section-content">
                <div class="portfolio-item">
                    <div class="portfolio-title">建筑设计作品集</div>
                    <div class="portfolio-description">包含住宅、商业建筑等多种类型的CAD设计作品</div>
                    <a href="https://portfolio.example.com/architecture" class="portfolio-url">查看作品集</a>
                </div>
            </div>
        </section>

        <!-- 自荐信模块 -->
        <section class="section-block cover-letters-section">
            <div class="section-header">
                <div class="section-icon">✉️</div>
                <h2 class="section-title">自荐信</h2>
            </div>
            
            <div class="section-content">
                <div class="cover-letter-item">
                    <div class="cover-letter-title">CAD设计师求职信</div>
                    <div class="cover-letter-content">尊敬的招聘负责人，我对贵公司的CAD设计师职位非常感兴趣。凭借我在CAD设计方面的专业技能和项目经验，我相信能够为贵公司带来价值...</div>
                </div>
            </div>
        </section>

        <!-- 自定义模块 -->
        <section class="section-block custom-section">
            <div class="section-header">
                <div class="section-icon">⚙️</div>
                <h2 class="section-title">设计理念</h2>
            </div>
            
            <div class="section-content">
                <div class="custom-item">
                    <div class="custom-content">追求简约而不简单的设计风格，注重功能性与美观性的完美结合。</div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>