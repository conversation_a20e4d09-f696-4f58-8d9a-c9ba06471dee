<template>
  <div class="pt-16 min-h-screen bg-gradient-to-br from-primary-50 to-orange-50 flex items-center justify-center">
    
    <div class="max-w-md w-full space-y-8 p-8">
      <!-- 头部信息 -->
      <div class="text-center">
        <div class="w-16 h-16 bg-gradient-to-br from-primary-600 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.19 0 2.34-.21 3.41-.6.3-.11.49-.4.49-.72 0-.43-.35-.78-.78-.78-.17 0-.33.06-.46.14-.82.29-1.69.44-2.58.44-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6c0 .89-.15 1.76-.44 2.58-.08.13-.14.29-.14.46 0 .43.35.78.78.78.32 0 .61-.19.72-.49.39-1.07.6-2.22.6-3.41C22 6.48 17.52 2 12 2z"/>
            <path d="M12 8l-2 6h4l-2-6z"/>
          </svg>
        </div>
        <h2 class="text-3xl font-display font-bold text-secondary-900">创建账户</h2>
        <p class="mt-2 text-secondary-600">加入火花简历，开始制作专业简历</p>
      </div>
      
      <!-- 注册表单 -->
      <form @submit.prevent="handleRegister" class="space-y-6">
        <!-- 手机号输入 -->
        <div class="form-group">
          <label class="block text-sm font-medium text-secondary-700 mb-2">手机号</label>
          <input 
            v-model="formData.phone"
            type="tel" 
            :class="getInputClass('phone')"
            placeholder="请输入手机号"
            @blur="validatePhone"
            @input="clearPhoneError"
            :disabled="isLoading"
            maxlength="11"
            autocomplete="tel"
          >
          <div v-if="formErrors.phone" class="mt-1 text-sm text-red-600">
            {{ formErrors.phone }}
          </div>
        </div>
        
        <!-- 密码输入 -->
        <div class="form-group">
          <label class="block text-sm font-medium text-secondary-700 mb-2">密码</label>
          <div class="relative">
            <input 
              v-model="formData.password"
              :type="showPassword ? 'text' : 'password'"
              :class="getInputClass('password')"
              placeholder="请设置密码（6-20位，包含字母和数字）"
              @blur="validatePassword"
              @input="clearPasswordError"
              :disabled="isLoading"
              maxlength="20"
              autocomplete="new-password"
            >
            <button 
              type="button"
              @click="togglePasswordVisibility"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
              :disabled="isLoading"
            >
              <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.5 8.5M14.12 14.12L15.5 15.5"/>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
              </svg>
            </button>
          </div>
          <div v-if="formErrors.password" class="mt-1 text-sm text-red-600">
            {{ formErrors.password }}
          </div>
        </div>

        <!-- 确认密码输入 -->
        <div class="form-group">
          <label class="block text-sm font-medium text-secondary-700 mb-2">确认密码</label>
          <div class="relative">
            <input 
              v-model="formData.confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              :class="getInputClass('confirmPassword')"
              placeholder="请再次输入密码"
              @blur="validateConfirmPassword"
              @input="clearConfirmPasswordError"
              :disabled="isLoading"
              maxlength="20"
              autocomplete="new-password"
            >
            <button 
              type="button"
              @click="toggleConfirmPasswordVisibility"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
              :disabled="isLoading"
            >
              <svg v-if="showConfirmPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.5 8.5M14.12 14.12L15.5 15.5"/>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
              </svg>
            </button>
          </div>
          <div v-if="formErrors.confirmPassword" class="mt-1 text-sm text-red-600">
            {{ formErrors.confirmPassword }}
          </div>
        </div>
        
        <!-- 验证码输入 -->
        <div class="form-group">
          <label class="block text-sm font-medium text-secondary-700 mb-2">短信验证码</label>
          <div class="flex space-x-3">
            <input 
              v-model="formData.verificationCode"
              type="text" 
              :class="getInputClass('verificationCode')"
              placeholder="请输入6位验证码"
              @blur="validateVerificationCode"
              @input="clearVerificationCodeError"
              :disabled="isLoading"
              maxlength="6"
              autocomplete="one-time-code"
              class="flex-1"
            >
            <button 
              type="button"
              @click="handleSendSmsCode"
              :disabled="!canSendSms || !formData.phone || smsLoading"
              :class="getSmsButtonClass()"
            >
              <span v-if="smsLoading" class="flex items-center">
                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                发送中
              </span>
              <span v-else-if="!canSendSms && smsCountdown > 0 && !smsJustSent">
                {{ smsCountdown }}s后重发
              </span>
              <span v-else-if="smsJustSent && smsCountdown > 0">
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  发送成功({{ smsCountdown }}s)
                </div>
              </span>
              <span v-else>
                发送验证码
              </span>
            </button>
          </div>
          <div v-if="formErrors.verificationCode" class="mt-1 text-sm text-red-600">
            {{ formErrors.verificationCode }}
          </div>
          <div v-if="smsCodeState?.error" class="mt-1 text-sm text-red-600">
            {{ smsCodeState.error }}
          </div>
        </div>

        <!-- 用户协议 -->
        <div class="form-group">
          <label class="flex items-start space-x-3 cursor-pointer">
            <input 
              v-model="formData.agreeTerms"
              type="checkbox" 
              :class="getCheckboxClass()"
              @change="validateAgreement"
              :disabled="isLoading"
              class="mt-0.5"
            >
            <span class="text-sm text-secondary-600 leading-relaxed">
              我已阅读并同意
              <button type="button" @click="showUserAgreement" class="text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200">
                《用户协议》
              </button>
              和
              <button type="button" @click="showPrivacyPolicy" class="text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200">
                《隐私政策》
              </button>
            </span>
          </label>
          <div v-if="formErrors.agreeTerms" class="mt-1 text-sm text-red-600">
            {{ formErrors.agreeTerms }}
          </div>
        </div>
        
        <!-- 提交按钮 -->
        <button 
          type="submit"
          :disabled="!canSubmit"
          :class="getSubmitButtonClass()"
        >
          <div v-if="isLoading" class="flex items-center justify-center">
            <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            <span>注册中...</span>
          </div>
          <span v-else>注册账户</span>
        </button>


      </form>
      
      <!-- 登录链接 -->
      <div class="text-center">
        <p class="text-secondary-600">
          已有账户？
          <NuxtLink to="/login" class="text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200">
            立即登录
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue'
import { useRegisterService } from '~/composables/auth/useRegisterService'
import { $message } from '~/composables/shared/useGlobalMessage'
import { useAuthService } from '~/composables/auth/useAuthService'

// ================================
// 页面元数据
// ================================
useSeoMeta({
  title: '注册 - 火花简历',
  description: '注册火花简历账户，开始制作专业简历，享受专业的简历制作服务'
})

// ================================
// 服务和工具
// ================================
const registerService = useRegisterService()
const authService = useAuthService()

// 注册页面使用默认布局中的全局消息服务，无需重复导入

// ================================
// 响应式数据
// ================================
const formData = reactive({
  phone: '',
  password: '',
  confirmPassword: '',
  verificationCode: '',
  agreeTerms: false
})

const formErrors = reactive({
  phone: '',
  password: '',
  confirmPassword: '',
  verificationCode: '',
  agreeTerms: ''
})

const showPassword = ref(false)
const showConfirmPassword = ref(false)
const smsJustSent = ref(false) // 跟踪验证码刚发送成功的状态

// ================================
// 计算属性
// ================================
const isLoading = computed(() => registerService.isLoading.value)
const smsLoading = computed(() => registerService.smsLoading.value)
const canSendSms = computed(() => registerService.canSendSms.value)
const smsCountdown = computed(() => registerService.smsCountdown.value)
const smsCodeState = computed(() => registerService.smsCodeState || { error: '' })

const canSubmit = computed(() => {
  return formData.phone.trim() !== '' && 
         formData.password.trim() !== '' && 
         formData.confirmPassword.trim() !== '' &&
         formData.verificationCode.trim() !== '' &&
         formData.agreeTerms &&
         !isLoading.value
})

// ================================
// 表单验证方法
// ================================
const validatePhone = () => {
  const result = registerService.validateField('phone', formData.phone)
  formErrors.phone = result.message
  return result.isValid
}

const validatePassword = () => {
  const result = registerService.validateField('password', formData.password)
  formErrors.password = result.message
  return result.isValid
}

const validateConfirmPassword = () => {
  const result = registerService.validateField('confirmPassword', formData.confirmPassword, formData)
  formErrors.confirmPassword = result.message
  return result.isValid
}

const validateVerificationCode = () => {
  const result = registerService.validateField('verificationCode', formData.verificationCode)
  formErrors.verificationCode = result.message
  return result.isValid
}

const validateAgreement = () => {
  const result = registerService.validateField('agreeTerms', formData.agreeTerms)
  formErrors.agreeTerms = result.message
  return result.isValid
}

// ================================
// 错误处理方法
// ================================
const clearPhoneError = () => {
  formErrors.phone = ''
}

const clearPasswordError = () => {
  formErrors.password = ''
}

const clearConfirmPasswordError = () => {
  formErrors.confirmPassword = ''
}

const clearVerificationCodeError = () => {
  formErrors.verificationCode = ''
}

// ================================
// UI交互方法
// ================================
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// ================================
// 样式方法
// ================================
const getInputClass = (field) => {
  const baseClass = 'w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 disabled:bg-secondary-50 disabled:cursor-not-allowed'
  const errorClass = 'border-red-300 focus:ring-red-500'
  const normalClass = 'border-secondary-300'
  
  return formErrors[field] ? `${baseClass} ${errorClass}` : `${baseClass} ${normalClass}`
}

const getCheckboxClass = () => {
  const baseClass = 'w-4 h-4 text-orange-600 border-secondary-300 rounded focus:ring-orange-500 disabled:cursor-not-allowed'
  const errorClass = 'border-red-300'
  
  return formErrors.agreeTerms ? `${baseClass} ${errorClass}` : baseClass
}

const getSubmitButtonClass = () => {
  const baseClass = 'w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2'
  const enabledClass = 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500'
  const disabledClass = 'bg-secondary-300 text-secondary-500 cursor-not-allowed'
  
  return canSubmit.value ? `${baseClass} ${enabledClass}` : `${baseClass} ${disabledClass}`
}

const getSmsButtonClass = () => {
  const baseClass = 'px-4 py-3 text-sm font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 whitespace-nowrap'
  
  if (smsJustSent.value && smsCountdown.value > 0) {
    // 发送成功状态 - 绿色
    return `${baseClass} bg-green-600 text-white border-green-600 focus:ring-green-500 cursor-default`
  } else if (!canSendSms.value || !formData.phone || smsLoading.value) {
    // 禁用状态 - 灰色
    return `${baseClass} bg-secondary-300 text-secondary-500 cursor-not-allowed focus:ring-secondary-500`
  } else {
    // 正常状态 - 橙色
    return `${baseClass} bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500`
  }
}

// 消息处理由默认布局中的全局消息服务处理

// ================================
// 业务方法
// ================================
const handleSendSmsCode = async () => {
  // 先验证手机号
  if (!validatePhone()) {
    return
  }

  console.log('🚀 开始发送验证码...')
  const result = await registerService.sendSmsCode(formData.phone)
  console.log('📱 发送验证码结果:', result)
  
  if (result.success) {
    // 设置发送成功状态，按钮会变成绿色并显示"发送成功"
    smsJustSent.value = true
    console.log('✅ 验证码发送成功，按钮状态已更新为绿色')
    
    // 监听倒计时结束，重置状态
    const resetTimer = setInterval(() => {
      if (smsCountdown.value <= 0) {
        smsJustSent.value = false
        clearInterval(resetTimer)
        console.log('⏰ 倒计时结束，按钮状态重置')
      }
    }, 1000)
  } else {
    console.log('❌ 发送失败:', result.error)
    // 错误信息通过 smsCodeState.error 显示在输入框下方
  }
}

const handleRegister = async () => {
  console.log('🚀 开始注册...')
  const result = await registerService.register(formData)
  console.log('📝 注册结果:', result)
  
  if (result.success) {
    console.log('✅ 注册成功，开始处理自动登录')
    
    // 从注册结果中获取用户信息和Token
    const response = result.data
    console.log('📋 注册响应数据:', response)
    
    // registerService已经处理了Token存储，现在调用authService重新初始化认证状态
    if (response.accessToken) {
      console.log('🔑 Token已由registerService存储，现在重新初始化认证状态...')
      console.log('🔑 accessToken长度:', response.accessToken ? response.accessToken.length : 0)
      
      // 验证Token确实已经存储
      setTimeout(async () => {
        const storedToken = localStorage.getItem('auth_token')
        console.log('🔍 验证存储的Token:', storedToken ? `${storedToken.substring(0, 20)}...` : 'null')
        console.log('🔍 存储Token长度:', storedToken ? storedToken.length : 0)
        
        if (storedToken) {
          // 调用authService初始化来恢复登录状态
          console.log('🔄 重新初始化认证状态')
          await authService.initAuth()
          console.log('✅ 认证状态初始化完成')
        }
      }, 100)
      
      // 显示成功消息
      $message.success('注册成功！欢迎加入火花简历')
      
      // 稍等片刻让用户看到成功消息，然后跳转到首页
      setTimeout(() => {
        console.log('🔄 跳转到首页')
        navigateTo('/', { replace: true })
      }, 1500)
    } else {
      console.error('❌ 注册响应缺少accessToken:', response)
      $message.error('注册成功但登录状态更新失败，请手动登录')
    }
  } else {
    // 显示错误消息
    console.log('❌ 注册失败:', result.error)
    $message.error(result.error || '注册失败，请重试')
  }
}

const showUserAgreement = () => {
  // 显示用户协议弹窗或跳转到协议页面
  console.log('显示用户协议')
}

const showPrivacyPolicy = () => {
  // 显示隐私政策弹窗或跳转到政策页面
  console.log('显示隐私政策')
}

// ================================
// 生命周期钩子
// ================================
onMounted(() => {
  // 重置注册服务状态
  registerService.resetState()
})
</script>

<style scoped>
.form-group {
  @apply relative;
}

.form-label {
  @apply block text-sm font-medium text-secondary-700 mb-2;
}

/* 强化橙色输入框聚焦效果 */
input:focus {
  --tw-ring-color: rgb(249 115 22) !important;
  border-color: rgb(249 115 22) !important;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
}

/* 错误状态下的红色焦点 */
input.border-red-300:focus {
  --tw-ring-color: rgb(239 68 68) !important;
  border-color: rgb(239 68 68) !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* 按钮加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 强化复选框橙色样式 */
input[type="checkbox"] {
  accent-color: rgb(234 88 12) !important;
}

input[type="checkbox"]:checked {
  background-color: rgb(234 88 12) !important;
  border-color: rgb(234 88 12) !important;
}

input[type="checkbox"]:focus {
  --tw-ring-color: rgb(249 115 22) !important;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
}

/* 强化按钮橙色样式 */
button.bg-orange-600 {
  background-color: rgb(234 88 12) !important;
}

button.bg-orange-600:hover:not(:disabled) {
  background-color: rgb(194 65 12) !important;
}

button.bg-orange-600:focus:not(:disabled) {
  --tw-ring-color: rgb(249 115 22) !important;
  box-shadow: 0 0 0 2px white, 0 0 0 4px rgb(249 115 22) !important;
}
</style> 