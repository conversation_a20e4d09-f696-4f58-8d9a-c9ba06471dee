package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;

/**
 * 简历证书请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(name = "ResumeCertificateRequest", description = "简历证书请求")
public class ResumeCertificateRequest {

    /**
     * 证书名称（必填，1-100字符）
     */
    @NotBlank(message = "证书名称不能为空")
    @Size(min = 1, max = 100, message = "证书名称长度必须在1-100个字符之间")
    @Schema(description = "证书名称", example = "Java程序员认证")
    private String certificateName;

    /**
     * 颁发机构（必填，1-100字符）
     */
    @NotBlank(message = "颁发机构不能为空")
    @Size(min = 1, max = 100, message = "颁发机构长度必须在1-100个字符之间")
    @Schema(description = "颁发机构", example = "Oracle")
    private String issuingOrganization;

    /**
     * 证书编号（可选）
     */
    @Size(max = 100, message = "证书编号长度不能超过100个字符")
    @Schema(description = "证书编号", example = "OCP-2023-001234")
    private String certificateNumber;

    /**
     * 颁发时间（必填）
     */
    @NotNull(message = "颁发时间不能为空")
    @Schema(description = "颁发时间", example = "2023-06-01")
    private LocalDate issueDate;

    /**
     * 到期时间（可选）
     */
    @Schema(description = "到期时间", example = "2026-06-01")
    private LocalDate expiryDate;

    /**
     * 是否永久有效（必填，0:否，1:是）
     */
    @NotNull(message = "是否永久有效不能为空")
    @Schema(description = "是否永久有效", example = "0", allowableValues = {"0", "1"})
    private Integer isPermanent;

    /**
     * 证书描述（可选）
     */
    @Size(max = 500, message = "证书描述长度不能超过500个字符")
    @Schema(description = "证书描述", example = "通过Oracle官方Java SE 11程序员认证考试")
    private String description;

    /**
     * 排序权重（必填）
     */
    @NotNull(message = "排序权重不能为空")
    @Min(value = 0, message = "排序权重不能为负数")
    @Schema(description = "排序权重", example = "1")
    private Integer sortOrder;
} 