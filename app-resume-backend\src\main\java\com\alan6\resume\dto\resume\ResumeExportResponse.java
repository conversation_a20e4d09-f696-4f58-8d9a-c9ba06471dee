package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 简历导出响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(name = "ResumeExportResponse", description = "简历导出响应")
public class ResumeExportResponse {

    /**
     * 任务ID
     */
    @Schema(description = "任务ID", example = "export_1234567890_abcd1234")
    private String jobId;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态（queued/processing/completed/failed）", example = "completed")
    private String status;

    /**
     * 进度百分比
     */
    @Schema(description = "进度百分比（0-100）", example = "100")
    private Integer progress;

    /**
     * 状态消息
     */
    @Schema(description = "状态消息", example = "导出完成")
    private String message;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息（失败时返回）")
    private String errorMessage;

    /**
     * 导出记录ID
     */
    @Schema(description = "导出记录ID", example = "5001")
    private Long id;

    /**
     * 简历ID
     */
    @Schema(description = "简历ID", example = "2001")
    private Long resumeId;

    /**
     * 导出格式
     */
    @Schema(description = "导出格式", example = "pdf")
    private String format;

    /**
     * 下载链接
     */
    @Schema(description = "下载链接", example = "https://cdn.example.com/export/resume_2001_20241201.pdf")
    private String downloadUrl;

    /**
     * 文件名
     */
    @Schema(description = "文件名", example = "张三_前端开发工程师.pdf")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小", example = "1024576")
    private Long fileSize;

    /**
     * 是否有水印
     */
    @Schema(description = "是否有水印", example = "false")
    private Boolean watermark;

    /**
     * 导出质量
     */
    @Schema(description = "导出质量", example = "high")
    private String quality;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间", example = "2024-12-02T10:00:00")
    private LocalDateTime expiresAt;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-01T10:00:00")
    private LocalDateTime createTime;
} 