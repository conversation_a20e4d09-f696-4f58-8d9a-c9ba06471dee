package com.alan6.resume.mapper;

import com.alan6.resume.entity.Roles;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Mapper
public interface RolesMapper extends BaseMapper<Roles> {

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Roles> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据角色代码获取角色
     *
     * @param roleCode 角色代码
     * @return 角色信息
     */
    Roles selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 获取所有启用的角色
     *
     * @return 角色列表
     */
    List<Roles> selectEnabledRoles();
} 