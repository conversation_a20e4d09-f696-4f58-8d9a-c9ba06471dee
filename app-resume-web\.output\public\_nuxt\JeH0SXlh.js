import{k as $,r as W,D as Z,c as S,o as v,a as e,z as Y,M as te,t as E,f as oe,G as H,q as _,A as le,i as se,F as U,h as c,g as N,s as q}from"./CURHyiUL.js";import{_ as K}from"./DlAUqK2U.js";function ne(p){const h={basicInfo:{name:"张三",title:"前端开发工程师",phone:"13800138000",email:"<EMAIL>",address:"北京市朝阳区",photo:""},skills:[{name:"JavaScript",level:90},{name:"Vue.js",level:85},{name:"React",level:80}],workExperiences:[{company:"示例公司",position:"前端开发工程师",startDate:"2022-07",endDate:"至今",description:"负责前端开发工作"}],educations:[{school:"示例大学",degree:"本科",major:"计算机科学",startDate:"2018-09",endDate:"2022-06"}],projects:[{name:"示例项目",role:"开发者",startDate:"2023-01",endDate:"2023-12",description:"项目描述"}],selfEvaluation:"这里是自我评价内容..."},u=n=>{var y;const a=((y=p.resumeData)==null?void 0:y.modules)||{};if(Array.isArray(a)){const r=a.find(M=>M.id===n);return(r==null?void 0:r.data)||{}}return a[n]||{}},d=(n,a)=>{const y=u(n),r=h[a];return Array.isArray(r)?Array.isArray(y)?y:r:typeof r=="object"&&r!==null?{...r,...y}:y!==void 0?y:r},g=n=>{console.log("📄 分页模式：只显示可见模块数据");const a={};if(n.includes("basic_info")?(a.basicInfo=d("basic_info","basicInfo"),console.log("✅ 包含基本信息")):a.basicInfo=h.basicInfo,n.includes("skills")){const y=u("skills");a.skills=(y==null?void 0:y.skills)!==void 0?y.skills:h.skills,console.log("✅ 包含技能模块，技能数据:",a.skills)}else a.skills=[];if(n.includes("work")?(a.workExperiences=d("work","workExperiences"),console.log("✅ 包含工作经历模块")):a.workExperiences=[],n.includes("education")){const y=u("education");a.educations=(y==null?void 0:y.education)!==void 0?y.education:h.educations,console.log("✅ 包含教育经历模块")}else a.educations=[];if(n.includes("project")?(a.projects=d("project","projects"),console.log("✅ 包含项目经历模块")):a.projects=[],n.includes("self-evaluation")){const y=u("self-evaluation");a.selfEvaluation=(y==null?void 0:y.content)||h.selfEvaluation,console.log("✅ 包含自我评价模块")}else a.selfEvaluation="";return console.log("📊 分页模式结果:",a),a},k=()=>(console.log("📜 非分页模式：显示所有数据"),{basicInfo:d("basic_info","basicInfo"),skills:(()=>{const n=u("skills");return(n==null?void 0:n.skills)!==void 0?n.skills:h.skills})(),workExperiences:d("work","workExperiences"),educations:(()=>{const n=u("education");return(n==null?void 0:n.education)!==void 0?n.education:h.educations})(),projects:(()=>{const n=u("project");return(n==null?void 0:n.projects)!==void 0?n.projects:h.projects})(),selfEvaluation:(()=>{const n=u("self-evaluation");return(n==null?void 0:n.content)||h.selfEvaluation})()}),C=$(()=>(console.log("🔄 计算 mergedResumeData..."),console.log("📝 分页模式:",p.isPaginationMode),console.log("📝 可见模块:",p.visibleModules),p.isPaginationMode?g(p.visibleModules):k())),o=$(()=>{console.log("🔧 计算排序模块..."),console.log("📥 props.visibleModules:",p.visibleModules);const n=p.visibleModules.map((a,y)=>{var M,A;const r=(A=(M=p.resumeData)==null?void 0:M.modules)==null?void 0:A[a];return{id:a,name:(r==null?void 0:r.name)||a,enabled:!0,order:y,data:r}});return console.log("✅ 最终模块列表:",n.map(a=>`${a.name||a.id}(${a.order})`)),n}),b=$(()=>{const n=o.value.filter(a=>a.id==="skills");return console.log("👈 左侧栏模块:",n.map(a=>a.name||a.id)),n}),j=$(()=>{const n=o.value.filter(a=>a.id!=="skills");return console.log("👉 右侧栏模块:",n.map(a=>a.name||a.id)),n}),z=(n,a)=>{if(!n)return"";const y=n.replace("-","年")+"月",r=a?a.replace("-","年")+"月":"至今";return`${y} - ${r}`};return{mergedResumeData:C,sortedModules:o,leftColumnModules:b,rightColumnModules:j,getModuleData:u,mergeModuleData:d,formatWorkPeriod:z,formatEducationPeriod:(n,a)=>z(n,a),formatProjectPeriod:(n,a)=>z(n,a),defaultResumeData:h}}function ae(p,h){const u=W(-1),d=W(-1),g=W(-1),k=W(-1),C=t=>({basic:"基本信息",skills:"专业技能",work:"工作经历",education:"教育经历",project:"项目经历","self-evaluation":"自我评价"})[t]||"未知模块";return{hoveredSkill:u,hoveredExperience:d,hoveredEducation:g,hoveredProject:k,moveModuleUp:t=>{console.log(`🔼 模块上移: ${t}`),h("module-move-up",t)},moveModuleDown:t=>{console.log(`🔽 模块下移: ${t}`),h("module-move-down",t)},deleteModule:t=>{const f=C(t);confirm(`确定要删除${f}模块吗？`)&&(console.log(`🗑️ 删除模块: ${t}`),h("module-delete",t))},getModuleTitle:C,moveSkillUp:(t,f)=>{if(t>0){console.log(`🔼 技能上移: 索引 ${t}`);const s=[...f];[s[t],s[t-1]]=[s[t-1],s[t]],h("skill-update",s)}},moveSkillDown:(t,f)=>{if(t<f.length-1){console.log(`🔽 技能下移: 索引 ${t}`);const s=[...f];[s[t],s[t+1]]=[s[t+1],s[t]],h("skill-update",s)}},deleteSkill:(t,f)=>{if(confirm("确定要删除这个技能吗？")){console.log(`🗑️ 删除技能: 索引 ${t}`);const s=f.filter((R,F)=>F!==t);h("skill-update",s)}},moveExperienceUp:(t,f)=>{if(t>0){console.log(`🔼 工作经历上移: 索引 ${t}`);const s=[...f];[s[t],s[t-1]]=[s[t-1],s[t]],h("experience-update",s)}},moveExperienceDown:(t,f)=>{if(t<f.length-1){console.log(`🔽 工作经历下移: 索引 ${t}`);const s=[...f];[s[t],s[t+1]]=[s[t+1],s[t]],h("experience-update",s)}},deleteExperience:(t,f)=>{if(confirm("确定要删除这段工作经历吗？")){console.log(`🗑️ 删除工作经历: 索引 ${t}`);const s=f.filter((R,F)=>F!==t);h("experience-update",s)}},moveEducationUp:(t,f)=>{if(t>0){console.log(`🔼 教育经历上移: 索引 ${t}`);const s=[...f];[s[t],s[t-1]]=[s[t-1],s[t]],h("education-update",s)}},moveEducationDown:(t,f)=>{if(t<f.length-1){console.log(`🔽 教育经历下移: 索引 ${t}`);const s=[...f];[s[t],s[t+1]]=[s[t+1],s[t]],h("education-update",s)}},deleteEducation:(t,f)=>{if(confirm("确定要删除这段教育经历吗？")){console.log(`🗑️ 删除教育经历: 索引 ${t}`);const s=f.filter((R,F)=>F!==t);h("education-update",s)}},moveProjectUp:(t,f)=>{if(t>0){console.log(`🔼 项目经历上移: 索引 ${t}`);const s=[...f];[s[t],s[t-1]]=[s[t-1],s[t]],h("project-update",s)}},moveProjectDown:(t,f)=>{if(t<f.length-1){console.log(`🔽 项目经历下移: 索引 ${t}`);const s=[...f];[s[t],s[t+1]]=[s[t+1],s[t]],h("project-update",s)}},deleteProject:(t,f)=>{if(confirm("确定要删除这个项目经历吗？")){console.log(`🗑️ 删除项目经历: 索引 ${t}`);const s=f.filter((R,F)=>F!==t);h("project-update",s)}},handleBasicInfoUpdate:t=>{console.log("📝 基本信息更新:",t),h("basic-info-update",t)},confirmBatchOperation:(t,f)=>confirm(`确定要${t} ${f} 个项目吗？`),canMove:(t,f,s)=>s==="up"?t>0:s==="down"?t<f-1:!1,getOperationButtonStyle:(t,f)=>{const s={opacity:t?.5:1,cursor:t?"not-allowed":"pointer",transition:"all 0.2s ease"};return f==="delete"?{...s,backgroundColor:t?"#fca5a5":"#ef4444",color:"white"}:{...s,backgroundColor:t?"#d1d5db":"#3b82f6",color:"white"}}}}function ie(p){const h={fontSize:"14px",lineHeight:"1.6",textColor:"#1f2937",primaryColor:"#3b82f6",secondaryColor:"#6366f1",fontFamily:"system-ui, -apple-system, sans-serif",sectionTitleFontSize:"1.3em",itemTitleFontSize:"1.1em",bodyFontSize:"1em"},u={moduleSpacing:"24px",itemSpacing:"16px",lineSpacing:"1.6",paragraphSpacing:"12px",sectionPadding:"20px",itemPadding:"12px"},d={top:"60px",bottom:"60px",left:"60px",right:"60px"},g=(i,P)=>({...P,...i}),k=i=>i?i.startsWith("#")||i.startsWith("rgb")?i:{red:"#ef4444",blue:"#3b82f6",green:"#10b981",yellow:"#f59e0b",purple:"#8b5cf6",pink:"#ec4899",indigo:"#6366f1",gray:"#6b7280",black:"#000000",white:"#ffffff"}[i.toLowerCase()]||i:"#000000",C=i=>{const T=k(i).replace("#",""),t=parseInt(T.substr(0,2),16),f=parseInt(T.substr(2,2),16),s=parseInt(T.substr(4,2),16);return(t*299+f*587+s*114)/1e3>128?"#000000":"#ffffff"},o=(i,P,T="90deg")=>{const t=k(i),f=k(P);return`linear-gradient(${T}, ${t}, ${f})`},b=$(()=>{const i=p.textStyle||{};return g(i,h)}),j=$(()=>{const i=b.value;return{color:k(i.primaryColor),fontSize:i.sectionTitleFontSize,fontFamily:i.fontFamily,fontWeight:"600",lineHeight:"1.4",marginBottom:"16px"}}),z=$(()=>{const i=b.value;return{color:k(i.textColor),fontSize:i.itemTitleFontSize,fontFamily:i.fontFamily,fontWeight:"500",lineHeight:"1.4",marginBottom:"8px"}}),x=$(()=>{const i=b.value;return{color:k(i.textColor),fontSize:i.bodyFontSize,fontFamily:i.fontFamily,fontWeight:"400",lineHeight:i.lineHeight,marginBottom:u.paragraphSpacing}}),I=$(()=>{const i=b.value,P=k(i.primaryColor),T=k(i.secondaryColor);return{background:o(P,T,"90deg"),borderRadius:"4px",height:"8px",transition:"width 0.3s ease-out"}}),n=i=>({...I.value,width:`${i}%`}),a=$(()=>({marginBottom:u.moduleSpacing,padding:u.sectionPadding,borderRadius:"8px",backgroundColor:"#ffffff",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",transition:"all 0.2s ease"})),y=$(()=>({marginBottom:u.itemSpacing,padding:u.itemPadding,borderRadius:"6px",backgroundColor:"#f9fafb",border:"1px solid #e5e7eb",transition:"all 0.2s ease"})),r=$(()=>{const i=b.value;return{backgroundColor:"#f3f4f6",borderColor:k(i.primaryColor),boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)",transform:"translateY(-2px)"}}),M=$(()=>{const i=b.value;return{backgroundColor:k(i.primaryColor),color:C(i.primaryColor),border:"none",borderRadius:"4px",padding:"4px 8px",fontSize:"12px",cursor:"pointer",transition:"all 0.2s ease",opacity:.8}}),A=$(()=>{const i=b.value;return{fontFamily:i.fontFamily,fontSize:i.fontSize,lineHeight:i.lineHeight,color:k(i.textColor),backgroundColor:"#ffffff",padding:`${d.top} ${d.right} ${d.bottom} ${d.left}`,minHeight:"100vh","--primary-color":k(i.primaryColor),"--secondary-color":k(i.secondaryColor),"--text-color":k(i.textColor),"--font-family":i.fontFamily,"--font-size":i.fontSize,"--line-height":i.lineHeight,"--module-spacing":u.moduleSpacing,"--item-spacing":u.itemSpacing,"--section-padding":u.sectionPadding,"--item-padding":u.itemPadding}});return{mergedTextStyle:b,titleStyle:j,subtitleStyle:z,bodyStyle:x,skillProgressStyle:I,moduleContainerStyle:a,itemContainerStyle:y,hoverStyle:r,operationButtonStyle:M,pageStyle:A,mergeStyles:g,parseColor:k,getContrastColor:C,generateGradient:o,getSkillProgressStyle:n,getResponsiveFontSize:(i,P="md")=>({sm:"0.875rem",md:"1rem",lg:"1.125rem"})[P]||i,getResponsiveSpacing:(i,P="md")=>({sm:"12px",md:"16px",lg:"24px"})[P]||i,applyTheme:i=>{const P=document.documentElement;Object.entries(i).forEach(([T,t])=>{P.style.setProperty(`--${T}`,t)})},defaultTextStyle:h,defaultSpacing:u,defaultMargins:d,presetThemes:{default:{"primary-color":"#3b82f6","secondary-color":"#6366f1","text-color":"#1f2937","background-color":"#ffffff"},dark:{"primary-color":"#60a5fa","secondary-color":"#a78bfa","text-color":"#f9fafb","background-color":"#111827"},warm:{"primary-color":"#f59e0b","secondary-color":"#f97316","text-color":"#1f2937","background-color":"#fffbeb"},cool:{"primary-color":"#06b6d4","secondary-color":"#0891b2","text-color":"#1f2937","background-color":"#f0f9ff"}}}}function re(p){return{shouldShowModule:g=>$(()=>{var o;if(!p.visibleModules.includes(g))return!1;const C=(((o=p.resumeData)==null?void 0:o.modules)||[]).find(b=>b.id===g);return!(!C||!C.enabled)}).value,getModuleData:g=>{var o;const C=(((o=p.resumeData)==null?void 0:o.modules)||[]).find(b=>b.id===g);return(C==null?void 0:C.data)||null},getModuleTitle:g=>({basic_info:"基本信息",education:"教育经历",work_experience:"工作经历",project:"项目经验",skills:"技能标签",language:"语言能力",award:"获奖荣誉",hobbies:"兴趣爱好",self_evaluation:"自我评价",internship:"实习经历",training:"培训经历",research_experience:"研究经历",publication:"论文专利",certificate:"证书资质",volunteer_experience:"志愿服务",portfolio:"作品集",cover_letter:"自荐信",custom:"自定义"})[g]||"未知模块"}}function ce(){return{addFadeInAnimation:(o,b=0)=>{o&&(o.style.opacity="0",o.style.transform="translateY(20px)",o.style.transition="all 0.6s ease-out",setTimeout(()=>{o.style.opacity="1",o.style.transform="translateY(0)"},b))},addScaleAnimation:(o,b=1.05,j=200)=>{if(!o)return;const z=o.style.transform;o.style.transition=`transform ${j}ms ease-out`,o.style.transform=`${z} scale(${b})`,setTimeout(()=>{o.style.transform=z},j)},addShakeAnimation:o=>{o&&(o.classList.add("animate-shake"),setTimeout(()=>{o.classList.remove("animate-shake")},500))},addSlideInAnimation:(o,b="up")=>{if(!o)return;const j={left:"translateX(-100%)",right:"translateX(100%)",up:"translateY(-100%)",down:"translateY(100%)"};o.style.transform=j[b]||j.up,o.style.transition="transform 0.4s ease-out",Z(()=>{o.style.transform="translate(0, 0)"})},addHighlightAnimation:(o,b="#3b82f6")=>{if(!o)return;const j=o.style.backgroundColor;o.style.transition="background-color 0.3s ease-out",o.style.backgroundColor=`${b}20`,setTimeout(()=>{o.style.backgroundColor=j},300)},addDeleteAnimation:(o,b)=>{o&&(o.style.transition="all 0.3s ease-out",o.style.opacity="0",o.style.transform="scale(0.8) translateX(-20px)",o.style.height="0",o.style.marginTop="0",o.style.marginBottom="0",o.style.paddingTop="0",o.style.paddingBottom="0",setTimeout(()=>{b&&b()},300))},addDragFeedback:(o,b)=>{o&&(b?(o.style.transform="rotate(2deg) scale(1.02)",o.style.boxShadow="0 8px 25px rgba(0, 0, 0, 0.15)",o.style.zIndex="1000"):(o.style.transform="",o.style.boxShadow="",o.style.zIndex=""))}}}function de(p,h){const u=ne(p),d=ae(p,h),g=ie(p),k=re(p),C=ce(),o={mergedResumeData:u.mergedResumeData,sortedModules:u.sortedModules,leftColumnModules:u.leftColumnModules,rightColumnModules:u.rightColumnModules,getModuleData:u.getModuleData,mergeModuleData:u.mergeModuleData,formatWorkPeriod:u.formatWorkPeriod,formatEducationPeriod:u.formatEducationPeriod,formatProjectPeriod:u.formatProjectPeriod,defaultResumeData:u.defaultResumeData},b={hoveredSkill:d.hoveredSkill,hoveredExperience:d.hoveredExperience,hoveredEducation:d.hoveredEducation,hoveredProject:d.hoveredProject,moveModuleUp:d.moveModuleUp,moveModuleDown:d.moveModuleDown,deleteModule:d.deleteModule,getModuleTitle:d.getModuleTitle,moveSkillUp:r=>d.moveSkillUp(r,o.mergedResumeData.value.skills),moveSkillDown:r=>d.moveSkillDown(r,o.mergedResumeData.value.skills),deleteSkill:r=>d.deleteSkill(r,o.mergedResumeData.value.skills),moveExperienceUp:r=>d.moveExperienceUp(r,o.mergedResumeData.value.workExperiences),moveExperienceDown:r=>d.moveExperienceDown(r,o.mergedResumeData.value.workExperiences),deleteExperience:r=>d.deleteExperience(r,o.mergedResumeData.value.workExperiences),moveEducationUp:r=>d.moveEducationUp(r,o.mergedResumeData.value.educations),moveEducationDown:r=>d.moveEducationDown(r,o.mergedResumeData.value.educations),deleteEducation:r=>d.deleteEducation(r,o.mergedResumeData.value.educations),moveProjectUp:r=>d.moveProjectUp(r,o.mergedResumeData.value.projects),moveProjectDown:r=>d.moveProjectDown(r,o.mergedResumeData.value.projects),deleteProject:r=>d.deleteProject(r,o.mergedResumeData.value.projects),handleBasicInfoUpdate:d.handleBasicInfoUpdate,confirmBatchOperation:d.confirmBatchOperation,canMove:d.canMove,getOperationButtonStyle:d.getOperationButtonStyle},j={mergedTextStyle:g.mergedTextStyle,titleStyle:g.titleStyle,subtitleStyle:g.subtitleStyle,bodyStyle:g.bodyStyle,pageStyle:g.pageStyle,moduleContainerStyle:g.moduleContainerStyle,itemContainerStyle:g.itemContainerStyle,hoverStyle:g.hoverStyle,operationButtonStyle:g.operationButtonStyle,skillProgressStyle:g.skillProgressStyle,getSkillProgressStyle:g.getSkillProgressStyle,mergeStyles:g.mergeStyles,parseColor:g.parseColor,getContrastColor:g.getContrastColor,generateGradient:g.generateGradient,getResponsiveFontSize:g.getResponsiveFontSize,getResponsiveSpacing:g.getResponsiveSpacing,applyTheme:g.applyTheme,defaultTextStyle:g.defaultTextStyle,defaultSpacing:g.defaultSpacing,defaultMargins:g.defaultMargins,presetThemes:g.presetThemes},z={shouldShowModule:k.shouldShowModule,getModuleData:k.getModuleData,getModuleTitle:k.getModuleTitle},x={addFadeInAnimation:C.addFadeInAnimation,addHighlightAnimation:C.addHighlightAnimation},I=()=>{const M=["resumeData","textStyle","isDraggable","visibleModules"].filter(A=>!(A in p));return M.length>0?(console.warn("⚠️ 模板缺少必需属性:",M),!1):!0},n=()=>(console.log("📋 模板应该支持的事件:",["update:resume-data","basic-info-update","module-move-up","module-move-down","module-delete","skill-update","experience-update","education-update","project-update"]),!0),a=()=>{console.log("🚀 初始化简历模板...");const r=I(),M=n();r&&M?console.log("✅ 模板初始化成功"):console.error("❌ 模板初始化失败"),console.log("📊 模板信息:",{modules:o.sortedModules.value.length,leftModules:o.leftColumnModules.value.length,rightModules:o.rightColumnModules.value.length,isDraggable:p.isDraggable,isPaginationMode:p.isPaginationMode})},y={printAllData:()=>{console.log("🔍 模板调试信息:"),console.log("📝 简历数据:",o.mergedResumeData.value),console.log("🎨 样式配置:",j.mergedTextStyle.value),console.log("📋 模块列表:",o.sortedModules.value)},printPerformanceInfo:()=>{var r,M;console.log("⚡ 性能信息:"),console.log("模块数量:",o.sortedModules.value.length),console.log("技能数量:",((r=o.mergedResumeData.value.skills)==null?void 0:r.length)||0),console.log("工作经历数量:",((M=o.mergedResumeData.value.workExperiences)==null?void 0:M.length)||0)},validateDataIntegrity:()=>{var A,V,O;const r=o.mergedResumeData.value,M=[];return(A=r.basicInfo)!=null&&A.name||M.push("缺少姓名"),(V=r.skills)!=null&&V.length||M.push("缺少技能"),(O=r.workExperiences)!=null&&O.length||M.push("缺少工作经历"),M.length>0?console.warn("⚠️ 数据完整性问题:",M):console.log("✅ 数据完整性检查通过"),M.length===0}};return a(),{...o,...b,...j,...z,...x,validateTemplateProps:I,validateTemplateEvents:n,initializeTemplate:a,debugInfo:y}}const ue={resumeData:{type:Object,required:!0,validator:p=>p&&typeof p=="object"},textStyle:{type:Object,default:()=>({fontSize:"14px",lineHeight:"1.6",textColor:"#1f2937",primaryColor:"#3b82f6",secondaryColor:"#6366f1"})},isDraggable:{type:Boolean,default:!1},visibleModules:{type:Array,default:()=>["basic_info","education","work_experience","project","skills","language","award","hobbies","self_evaluation","internship","training","research_experience","publication","certificate","volunteer_experience","portfolio","cover_letter","custom"]},pageIndex:{type:Number,default:0},isPaginationMode:{type:Boolean,default:!1},isNaturalFlow:{type:Boolean,default:!1}},pe=["update:resume-data","basic-info-update","module-move-up","module-move-down","module-delete","skill-update","experience-update","education-update","project-update","content-height-change"],me={class:"contact-item group"},ge={class:"contact-icon"},fe={class:"contact-value"},ve={__name:"ContactItem",props:{icon:{type:String,required:!0,validator:p=>["phone","email","location"].includes(p)},value:{type:String,required:!0},isEditable:{type:Boolean,default:!1}},emits:["update"],setup(p,{emit:h}){const u=p,d=$(()=>({phone:g,email:k,location:C})[u.icon]),g={template:`
    <svg viewBox="0 0 20 20" fill="currentColor">
      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
    </svg>
  `},k={template:`
    <svg viewBox="0 0 20 20" fill="currentColor">
      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
    </svg>
  `},C={template:`
    <svg viewBox="0 0 20 20" fill="currentColor">
      <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
    </svg>
  `};return(o,b)=>(v(),S("div",me,[e("div",ge,[(v(),Y(te(d.value),{class:"icon"}))]),e("span",fe,E(p.value),1)]))}},J=K(ve,[["__scopeId","data-v-f80e2aca"]]),he={class:"resume-header"},be={class:"header-content"},ye={class:"avatar-section"},ke={class:"avatar-container group"},Se={class:"avatar"},we=["src","alt"],Ce={key:1,class:"avatar-placeholder"},Me={class:"info-section"},De={class:"contact-info"},_e={__name:"ResumeHeader",props:{basicInfo:{type:Object,required:!0,default:()=>({name:"",title:"",phone:"",email:"",address:"",photo:""})},textStyle:{type:Object,default:()=>({})},isDraggable:{type:Boolean,default:!1}},emits:["update-basic-info"],setup(p,{emit:h}){const u=p,d=$(()=>u.basicInfo.name||"张三"),g=$(()=>u.basicInfo.title||"前端开发工程师"),k=$(()=>({fontSize:u.textStyle.nameFontSize||"2.5rem",color:u.textStyle.nameColor||"#ffffff"})),C=$(()=>({fontSize:u.textStyle.jobTitleFontSize||"1.2rem",color:u.textStyle.jobTitleColor||"#f1f5f9"}));return(o,b)=>(v(),S("header",he,[b[1]||(b[1]=oe('<div class="header-background" data-v-5903252a><div class="geometric-decoration" data-v-5903252a><div class="circle circle-1" data-v-5903252a></div><div class="circle circle-2" data-v-5903252a></div><div class="triangle" data-v-5903252a></div></div></div>',1)),e("div",be,[e("div",ye,[e("div",ke,[e("div",Se,[p.basicInfo.photo?(v(),S("img",{key:0,src:p.basicInfo.photo,alt:p.basicInfo.name+"的头像",class:"avatar-image"},null,8,we)):(v(),S("div",Ce,b[0]||(b[0]=[e("svg",{class:"avatar-icon",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"})],-1)])))])])]),e("div",Me,[e("h1",{class:"name",style:H(k.value)},E(d.value),5),e("div",{class:"job-title",style:H(C.value)},E(g.value),5),e("div",De,[p.basicInfo.phone?(v(),Y(J,{key:0,icon:"phone",value:p.basicInfo.phone},null,8,["value"])):_("",!0),p.basicInfo.email?(v(),Y(J,{key:1,icon:"email",value:p.basicInfo.email},null,8,["value"])):_("",!0),p.basicInfo.address?(v(),Y(J,{key:2,icon:"location",value:p.basicInfo.address},null,8,["value"])):_("",!0)])])])]))}},$e=K(_e,[["__scopeId","data-v-5903252a"]]),Ee={key:0,class:"placeholder-page"},je={class:"placeholder-content"},Pe={class:"main-content"},ze={class:"left-column"},Be={key:0,class:"skills-section module-container"},Te={key:0,class:"module-actions"},Ae={class:"skills-list"},Ie=["onMouseenter"],Re={key:0,class:"skill-actions"},xe=["onClick","disabled"],Fe=["onClick","disabled"],Ue=["onClick"],Le={class:"skill-name"},He={class:"skill-progress-container"},Ve={class:"skill-progress-track"},Oe={class:"skill-level-text"},Ne={class:"right-column"},qe={key:0,class:"work-section module-container"},We={key:0,class:"module-actions"},Ye={class:"work-timeline"},Ge=["onMouseenter"],Xe={key:0,class:"work-actions"},Je=["onClick","disabled"],Ke=["onClick","disabled"],Qe=["onClick"],Ze={class:"work-content"},et={class:"work-header"},tt={class:"job-title"},ot={class:"company-name"},lt={class:"work-period"},st={class:"work-description"},nt={key:1,class:"education-section module-container"},at={key:0,class:"module-actions"},it={class:"education-list"},rt={key:0,class:"education-actions"},ct=["onClick","disabled"],dt=["onClick","disabled"],ut=["onClick"],pt={class:"education-content"},mt={class:"education-header"},gt={class:"school-name"},ft={class:"degree-info"},vt={class:"degree"},ht={class:"major"},bt={class:"education-period"},yt=["innerHTML"],kt={key:2,class:"project-section module-container"},St={key:0,class:"module-actions"},wt={class:"project-list"},Ct={key:0,class:"project-actions"},Mt=["onClick","disabled"],Dt=["onClick","disabled"],_t=["onClick"],$t={class:"project-content"},Et={class:"project-header"},jt={class:"project-name"},Pt={class:"project-role"},zt={class:"project-period"},Bt={key:0,class:"project-tech"},Tt=["innerHTML"],At={key:3,class:"self-evaluation-section module-container"},It={key:0,class:"module-actions"},Rt={class:"self-evaluation-content"},xt={__name:"ResumeTemplate",props:ue,emits:pe,setup(p,{expose:h,emit:u}){const d=p,g=u,k=W(null),C=de(d,g),{mergedResumeData:o,leftColumnModules:b,rightColumnModules:j,formatWorkPeriod:z,hoveredSkill:x,hoveredExperience:I,moveModuleUp:n,moveModuleDown:a,deleteModule:y,moveSkillUp:r,moveSkillDown:M,deleteSkill:A,moveExperienceUp:V,moveExperienceDown:O,deleteExperience:G,moveEducationUp:X,moveEducationDown:i,deleteEducation:P,moveProjectUp:T,moveProjectDown:t,deleteProject:f,handleBasicInfoUpdate:s,titleStyle:R,getSkillProgressStyle:F,debugInfo:ee}=C;h({templateRef:k,debugInfo:ee});const Q=()=>{d.isNaturalFlow&&k.value&&Z(()=>{const D=k.value.scrollHeight;console.log("📐 ResumeTemplate 内容高度变化:",D),g("content-height-change",D)})};return le(()=>o.value,Q,{deep:!0,immediate:!0}),se(()=>{d.isNaturalFlow&&setTimeout(Q,100)}),(D,l)=>(v(),S("div",{ref_key:"templateRef",ref:k,class:q(["resume-template",{"preview-mode":!D.isDraggable}])},[D.resumeData.isPlaceholderPage?(v(),S("div",Ee,[e("div",je,[l[17]||(l[17]=e("div",{class:"placeholder-icon"},"📄",-1)),e("h3",null,"第 "+E(D.resumeData.pageNumber)+" 页",1),l[18]||(l[18]=e("p",{class:"placeholder-text"},"此页将显示超出第一页的内容",-1)),l[19]||(l[19]=e("p",{class:"placeholder-note"},"（内容分页功能开发中）",-1))])])):(v(),S(U,{key:1},[!D.isPaginationMode||D.visibleModules.includes("basic_info")?(v(),Y($e,{key:0,"basic-info":c(o).basicInfo,"text-style":D.textStyle,"is-draggable":D.isDraggable,onUpdateBasicInfo:c(s)},null,8,["basic-info","text-style","is-draggable","onUpdateBasicInfo"])):_("",!0),e("div",Pe,[e("aside",ze,[(v(!0),S(U,null,N(c(b),L=>(v(),S(U,{key:L.id},[L.id==="skills"?(v(),S("section",Be,[D.isDraggable?(v(),S("div",Te,[e("button",{onClick:l[0]||(l[0]=m=>c(n)("skills")),class:"module-action-btn",title:"模块上移"},l[20]||(l[20]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[1]||(l[1]=m=>c(a)("skills")),class:"module-action-btn",title:"模块下移"},l[21]||(l[21]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[2]||(l[2]=m=>c(y)("skills")),class:"module-action-btn delete",title:"删除模块"},l[22]||(l[22]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z","clip-rule":"evenodd"})],-1)]))])):_("",!0),e("h2",{class:"section-title",style:H(c(R))},"专业技能",4),e("div",Ae,[(v(!0),S(U,null,N(c(o).skills,(m,w)=>(v(),S("div",{key:m.id||w,class:q(["skill-item",{draggable:D.isDraggable}]),onMouseenter:B=>x.value=w,onMouseleave:l[3]||(l[3]=B=>x.value=-1)},[D.isDraggable&&c(x)===w?(v(),S("div",Re,[e("button",{onClick:B=>c(r)(w),disabled:w===0,class:"skill-action-btn",title:"上移"}," ↑ ",8,xe),e("button",{onClick:B=>c(M)(w),disabled:w===c(o).skills.length-1,class:"skill-action-btn",title:"下移"}," ↓ ",8,Fe),e("button",{onClick:B=>c(A)(w),class:"skill-action-btn delete",title:"删除"}," × ",8,Ue)])):_("",!0),e("div",Le,E(m.name),1),e("div",He,[e("div",Ve,[e("div",{class:"skill-progress-bar",style:H(c(F)(m.level))},null,4)]),e("div",Oe,E(m.level)+"%",1)])],42,Ie))),128))])])):_("",!0)],64))),128))]),e("main",Ne,[(v(!0),S(U,null,N(c(j),L=>(v(),S(U,{key:L.id},[L.id==="work"?(v(),S("section",qe,[D.isDraggable?(v(),S("div",We,[e("button",{onClick:l[4]||(l[4]=m=>c(n)("work")),class:"module-action-btn",title:"模块上移"},l[23]||(l[23]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[5]||(l[5]=m=>c(a)("work")),class:"module-action-btn",title:"模块下移"},l[24]||(l[24]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[6]||(l[6]=m=>c(y)("work")),class:"module-action-btn delete",title:"删除模块"},l[25]||(l[25]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z","clip-rule":"evenodd"})],-1)]))])):_("",!0),e("h2",{class:"section-title",style:H(c(R))},"工作经历",4),e("div",Ye,[(v(!0),S(U,null,N(c(o).workExperiences,(m,w)=>(v(),S("div",{key:m.id||w,class:q(["work-item",{draggable:D.isDraggable}]),onMouseenter:B=>I.value=w,onMouseleave:l[7]||(l[7]=B=>I.value=-1)},[l[26]||(l[26]=e("div",{class:"timeline-node"},null,-1)),D.isDraggable&&c(I)===w?(v(),S("div",Xe,[e("button",{onClick:B=>c(V)(w),disabled:w===0,class:"work-action-btn",title:"上移"}," ↑ ",8,Je),e("button",{onClick:B=>c(O)(w),disabled:w===c(o).workExperiences.length-1,class:"work-action-btn",title:"下移"}," ↓ ",8,Ke),e("button",{onClick:B=>c(G)(w),class:"work-action-btn delete",title:"删除"}," × ",8,Qe)])):_("",!0),e("div",Ze,[e("div",et,[e("h3",tt,E(m.position),1),e("div",ot,E(m.company),1),e("div",lt,E(c(z)(m.startDate,m.endDate)),1)]),e("div",st,E(m.description),1)])],42,Ge))),128))])])):_("",!0),L.id==="education"?(v(),S("section",nt,[D.isDraggable?(v(),S("div",at,[e("button",{onClick:l[8]||(l[8]=m=>c(n)("education")),class:"module-action-btn",title:"模块上移"},l[27]||(l[27]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[9]||(l[9]=m=>c(a)("education")),class:"module-action-btn",title:"模块下移"},l[28]||(l[28]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[10]||(l[10]=m=>c(y)("education")),class:"module-action-btn delete",title:"删除模块"},l[29]||(l[29]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z","clip-rule":"evenodd"})],-1)]))])):_("",!0),e("h2",{class:"section-title",style:H(c(R))},"教育经历",4),e("div",it,[(v(!0),S(U,null,N(c(o).educations,(m,w)=>(v(),S("div",{key:m.id||w,class:q(["education-item",{draggable:D.isDraggable}])},[D.isDraggable?(v(),S("div",rt,[e("button",{onClick:B=>c(X)(w),disabled:w===0,class:"education-action-btn",title:"上移"}," ↑ ",8,ct),e("button",{onClick:B=>c(i)(w),disabled:w===c(o).educations.length-1,class:"education-action-btn",title:"下移"}," ↓ ",8,dt),e("button",{onClick:B=>c(P)(w),class:"education-action-btn delete",title:"删除"}," × ",8,ut)])):_("",!0),e("div",pt,[e("div",mt,[e("h3",gt,E(m.school),1),e("div",ft,[e("span",vt,E(m.degree),1),e("span",ht,E(m.major),1)]),e("div",bt,E(c(z)(m.startDate,m.endDate)),1)]),m.description?(v(),S("div",{key:0,class:"education-description",innerHTML:m.description},null,8,yt)):_("",!0)])],2))),128))])])):_("",!0),L.id==="project"?(v(),S("section",kt,[D.isDraggable?(v(),S("div",St,[e("button",{onClick:l[11]||(l[11]=m=>c(n)("project")),class:"module-action-btn",title:"模块上移"},l[30]||(l[30]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[12]||(l[12]=m=>c(a)("project")),class:"module-action-btn",title:"模块下移"},l[31]||(l[31]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[13]||(l[13]=m=>c(y)("project")),class:"module-action-btn delete",title:"删除模块"},l[32]||(l[32]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z","clip-rule":"evenodd"})],-1)]))])):_("",!0),e("h2",{class:"section-title",style:H(c(R))},"项目经历",4),e("div",wt,[(v(!0),S(U,null,N(c(o).projects,(m,w)=>(v(),S("div",{key:m.id||w,class:q(["project-item",{draggable:D.isDraggable}])},[D.isDraggable?(v(),S("div",Ct,[e("button",{onClick:B=>c(T)(w),disabled:w===0,class:"project-action-btn",title:"上移"}," ↑ ",8,Mt),e("button",{onClick:B=>c(t)(w),disabled:w===c(o).projects.length-1,class:"project-action-btn",title:"下移"}," ↓ ",8,Dt),e("button",{onClick:B=>c(f)(w),class:"project-action-btn delete",title:"删除"}," × ",8,_t)])):_("",!0),e("div",$t,[e("div",Et,[e("h3",jt,E(m.name),1),e("div",Pt,E(m.role),1),e("div",zt,E(c(z)(m.startDate,m.endDate)),1),m.technology?(v(),S("div",Bt," 技术栈："+E(m.technology),1)):_("",!0)]),m.description?(v(),S("div",{key:0,class:"project-description",innerHTML:m.description},null,8,Tt)):_("",!0)])],2))),128))])])):_("",!0),L.id==="self-evaluation"?(v(),S("section",At,[D.isDraggable?(v(),S("div",It,[e("button",{onClick:l[14]||(l[14]=m=>c(n)("self-evaluation")),class:"module-action-btn",title:"模块上移"},l[33]||(l[33]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[15]||(l[15]=m=>c(a)("self-evaluation")),class:"module-action-btn",title:"模块下移"},l[34]||(l[34]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z","clip-rule":"evenodd"})],-1)])),e("button",{onClick:l[16]||(l[16]=m=>c(y)("self-evaluation")),class:"module-action-btn delete",title:"删除模块"},l[35]||(l[35]=[e("svg",{viewBox:"0 0 20 20",fill:"currentColor",class:"w-4 h-4"},[e("path",{"fill-rule":"evenodd",d:"M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z","clip-rule":"evenodd"})],-1)]))])):_("",!0),e("h2",{class:"section-title",style:H(c(R))},"自我评价",4),e("div",Rt,[e("p",null,E(c(o).selfEvaluation),1)])])):_("",!0)],64))),128))])])],64))],2))}},Lt=K(xt,[["__scopeId","data-v-d1469c88"]]);export{Lt as R};
