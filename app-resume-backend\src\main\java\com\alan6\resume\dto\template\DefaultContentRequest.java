package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 默认内容请求DTO
 * @description 用于请求获取默认简历内容的参数
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DefaultContentRequest", description = "默认内容请求")
public class DefaultContentRequest {

    /**
     * 模板类别
     * @description 模板的分类，用于获取对应的默认内容
     */
    @Schema(description = "模板类别", example = "modern", required = true)
    @NotBlank(message = "模板类别不能为空")
    @Size(max = 50, message = "模板类别长度不能超过50个字符")
    private String templateCategory;

    /**
     * 行业类型
     * @description 目标行业类型，用于获取行业相关的默认内容
     */
    @Schema(description = "行业类型", example = "tech")
    @Size(max = 50, message = "行业类型长度不能超过50个字符")
    private String industry;

    /**
     * 职位类型
     * @description 目标职位类型，用于获取职位相关的默认内容
     */
    @Schema(description = "职位类型", example = "frontend")
    @Size(max = 50, message = "职位类型长度不能超过50个字符")
    private String position;

    /**
     * 语言
     * @description 简历语言，默认为中文
     */
    @Schema(description = "简历语言", example = "zh-CN")
    @Size(max = 10, message = "语言代码长度不能超过10个字符")
    private String language;

    /**
     * 请求来源
     * @description 请求的来源标识，用于统计和分析
     */
    @Schema(description = "请求来源", example = "web")
    @Size(max = 20, message = "请求来源长度不能超过20个字符")
    private String source;
} 