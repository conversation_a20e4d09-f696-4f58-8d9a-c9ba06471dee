package com.alan6.resume.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 缓存清理响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "CacheClearResponse", description = "缓存清理响应")
public class CacheClearResponse {

    /**
     * 清理的键数量
     */
    @Schema(description = "清理的键数量", example = "1250")
    private Integer clearedKeys;

    /**
     * 释放的内存
     */
    @Schema(description = "释放的内存", example = "25MB")
    private String freedMemory;

    /**
     * 操作耗时
     */
    @Schema(description = "操作耗时", example = "150ms")
    private String operationTime;
} 