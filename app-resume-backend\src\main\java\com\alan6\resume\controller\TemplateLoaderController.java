package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.dto.template.DefaultContentRequest;
import com.alan6.resume.dto.template.DefaultContentResponse;
import com.alan6.resume.dto.template.TemplateLoadRequest;
import com.alan6.resume.dto.template.TemplateLoadResponse;
import com.alan6.resume.service.IDefaultContentService;
import com.alan6.resume.service.ITemplateFileService;
import com.alan6.resume.service.ITemplateLoaderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模板加载控制器
 * @description 提供Vue模板动态加载和默认内容获取的API接口
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/template-loader")
@RequiredArgsConstructor
@Validated
@Tag(name = "模板加载", description = "Vue模板动态加载和默认内容相关接口")
public class TemplateLoaderController {

    /**
     * 模板加载服务
     * @description 核心业务逻辑服务
     */
    private final ITemplateLoaderService templateLoaderService;

    /**
     * 默认内容服务
     * @description 默认简历内容管理服务
     */
    private final IDefaultContentService defaultContentService;

    /**
     * 模板文件服务
     * @description 模板文件存储管理服务
     */
    private final ITemplateFileService templateFileService;

    /**
     * 加载Vue模板
     * @description 根据模板ID或代码加载Vue模板内容，支持三层存储架构，现在支持Vue+JSON文件组合
     * @param request 模板加载请求参数
     * @return 模板内容和相关信息，包含Vue文件和JSON默认内容
     */
    @PostMapping("/load")
    @Operation(
        summary = "加载Vue模板", 
        description = "根据模板ID加载Vue模板内容和JSON默认内容，优先从Redis缓存获取，没有则从MinIO获取，最后从本地获取"
    )
    public R<TemplateLoadResponse> loadTemplate(
        @Parameter(description = "模板加载请求参数", required = true)
        @Valid @RequestBody TemplateLoadRequest request
    ) {
        try {
            log.info("开始加载模板，templateId: {}, templateCode: {}", 
                request.getTemplateId(), request.getTemplateCode());
            
            // 调用服务层加载模板
            TemplateLoadResponse response = templateLoaderService.loadTemplate(request);
            
            if (response != null && response.getTemplateContent() != null) {
                log.info("模板加载成功，templateId: {}, dataSource: {}, hasDefaultContent: {}, loadTime: {}ms", 
                    response.getTemplateId(), response.getDataSource(), 
                    response.getHasDefaultContent(), response.getLoadTime());
                return R.ok(response);
            } else {
                log.warn("模板加载失败，未找到模板内容，templateId: {}", request.getTemplateId());
                return R.fail("模板不存在或加载失败");
            }
        } catch (Exception e) {
            log.error("加载模板时发生异常，templateId: {}", request.getTemplateId(), e);
            return R.fail("模板加载异常：" + e.getMessage());
        }
    }

    /**
     * 通过模板代码加载Vue模板
     * @description 根据模板代码快速加载Vue模板内容
     * @param templateCode 模板代码
     * @param forceRefresh 是否强制刷新缓存
     * @return 模板内容和相关信息
     */
    @GetMapping("/load/{templateCode}")
    @Operation(
        summary = "通过代码加载Vue模板", 
        description = "根据模板代码快速加载Vue模板内容"
    )
    public R<TemplateLoadResponse> loadTemplateByCode(
        @Parameter(description = "模板代码", required = true, example = "modern-simple")
        @PathVariable String templateCode,
        
        @Parameter(description = "是否强制刷新缓存", example = "false")
        @RequestParam(value = "forceRefresh", defaultValue = "false") Boolean forceRefresh
    ) {
        try {
            log.info("开始通过代码加载模板，templateCode: {}, forceRefresh: {}", 
                templateCode, forceRefresh);
            
            // 构建请求对象
            TemplateLoadRequest request = new TemplateLoadRequest();
            request.setTemplateCode(templateCode);
            request.setForceRefresh(forceRefresh);
            request.setSource("api-direct");
            
            // 调用服务层加载模板
            TemplateLoadResponse response = templateLoaderService.loadTemplate(request);
            
            if (response != null && response.getTemplateContent() != null) {
                log.info("模板加载成功，templateCode: {}, dataSource: {}, loadTime: {}ms", 
                    templateCode, response.getDataSource(), response.getLoadTime());
                return R.ok(response);
            } else {
                log.warn("模板加载失败，未找到模板内容，templateCode: {}", templateCode);
                return R.fail("模板不存在或加载失败");
            }
        } catch (Exception e) {
            log.error("加载模板时发生异常，templateCode: {}", templateCode, e);
            return R.fail("模板加载异常：" + e.getMessage());
        }
    }

    /**
     * 获取默认简历内容
     * @description 当模板没有自带JSON内容时，根据模板类别、行业、职位获取默认简历内容
     * @param request 默认内容请求参数
     * @return 默认简历内容JSON数据
     */
    @PostMapping("/default-content")
    @Operation(
        summary = "获取默认简历内容", 
        description = "根据模板类别、行业、职位获取对应的默认简历内容，支持多级优先级匹配"
    )
    public R<DefaultContentResponse> getDefaultContent(
        @Parameter(description = "默认内容请求参数", required = true)
        @Valid @RequestBody DefaultContentRequest request
    ) {
        try {
            log.info("开始获取默认内容，templateCategory: {}, industry: {}, position: {}", 
                request.getTemplateCategory(), request.getIndustry(), request.getPosition());
            
            // 调用服务层获取默认内容
            DefaultContentResponse response = defaultContentService.getDefaultContent(request);
            
            if (response != null && response.getDefaultContent() != null) {
                log.info("默认内容获取成功，contentSource: {}, templateCategory: {}", 
                    response.getContentSource(), response.getTemplateCategory());
                return R.ok(response);
            } else {
                log.warn("默认内容获取失败，templateCategory: {}", request.getTemplateCategory());
                return R.fail("默认内容不存在或获取失败");
            }
        } catch (Exception e) {
            log.error("获取默认内容时发生异常，templateCategory: {}", request.getTemplateCategory(), e);
            return R.fail("获取默认内容异常：" + e.getMessage());
        }
    }

    /**
     * 获取支持的模板类别
     * @description 获取系统支持的所有模板类别列表
     * @return 模板类别列表
     */
    @GetMapping("/categories")
    @Operation(
        summary = "获取支持的模板类别", 
        description = "获取系统支持的所有模板类别列表"
    )
    public R<List<String>> getSupportedCategories() {
        try {
            log.info("开始获取支持的模板类别");
            
            List<String> categories = defaultContentService.getSupportedCategories();
            
            log.info("获取支持的模板类别成功，数量: {}", categories.size());
            return R.ok(categories);
        } catch (Exception e) {
            log.error("获取支持的模板类别时发生异常", e);
            return R.fail("获取类别列表异常：" + e.getMessage());
        }
    }

    /**
     * 获取支持的行业类型
     * @description 获取系统支持的所有行业类型列表
     * @return 行业类型列表
     */
    @GetMapping("/industries")
    @Operation(
        summary = "获取支持的行业类型", 
        description = "获取系统支持的所有行业类型列表"
    )
    public R<List<String>> getSupportedIndustries() {
        try {
            log.info("开始获取支持的行业类型");
            
            List<String> industries = defaultContentService.getSupportedIndustries();
            
            log.info("获取支持的行业类型成功，数量: {}", industries.size());
            return R.ok(industries);
        } catch (Exception e) {
            log.error("获取支持的行业类型时发生异常", e);
            return R.fail("获取行业列表异常：" + e.getMessage());
        }
    }

    /**
     * 获取支持的职位类型
     * @description 获取系统支持的职位类型列表，可以按行业筛选
     * @param industry 行业类型（可选）
     * @return 职位类型列表
     */
    @GetMapping("/positions")
    @Operation(
        summary = "获取支持的职位类型", 
        description = "获取系统支持的职位类型列表，可以按行业筛选"
    )
    public R<List<String>> getSupportedPositions(
        @Parameter(description = "行业类型（可选）", example = "tech")
        @RequestParam(value = "industry", required = false) String industry
    ) {
        try {
            log.info("开始获取支持的职位类型，industry: {}", industry);
            
            List<String> positions = defaultContentService.getSupportedPositions(industry);
            
            log.info("获取支持的职位类型成功，数量: {}", positions.size());
            return R.ok(positions);
        } catch (Exception e) {
            log.error("获取支持的职位类型时发生异常，industry: {}", industry, e);
            return R.fail("获取职位列表异常：" + e.getMessage());
        }
    }

    /**
     * 预热模板缓存
     * @description 批量预加载热门模板到Redis缓存中
     * @param templateIds 模板ID数组
     * @return 预热结果
     */
    @PostMapping("/preload")
    @Operation(
        summary = "预热模板缓存", 
        description = "批量预加载热门模板到Redis缓存中，提高访问速度"
    )
    public R<String> preloadTemplates(
        @Parameter(description = "模板ID数组", required = true)
        @RequestBody Long[] templateIds
    ) {
        try {
            log.info("开始预热模板缓存，模板数量: {}", templateIds != null ? templateIds.length : 0);
            
            int successCount = templateLoaderService.preloadTemplates(templateIds);
            
            String message = String.format("预热完成，成功: %d, 总数: %d", 
                successCount, templateIds != null ? templateIds.length : 0);
            
            log.info("模板缓存预热完成，{}", message);
            return R.ok(message);
        } catch (Exception e) {
            log.error("预热模板缓存时发生异常", e);
            return R.fail("预热失败：" + e.getMessage());
        }
    }

    /**
     * 刷新模板缓存
     * @description 强制刷新指定模板的缓存
     * @param templateId 模板ID
     * @return 刷新结果
     */
    @PostMapping("/refresh/{templateId}")
    @Operation(
        summary = "刷新模板缓存", 
        description = "强制刷新指定模板的缓存，重新从MinIO或本地加载"
    )
    public R<String> refreshTemplateCache(
        @Parameter(description = "模板ID", required = true)
        @PathVariable Long templateId
    ) {
        try {
            log.info("开始刷新模板缓存，templateId: {}", templateId);
            
            boolean success = templateLoaderService.refreshTemplateCache(templateId);
            
            if (success) {
                log.info("模板缓存刷新成功，templateId: {}", templateId);
                return R.ok("缓存刷新成功");
            } else {
                log.warn("模板缓存刷新失败，templateId: {}", templateId);
                return R.fail("缓存刷新失败");
            }
        } catch (Exception e) {
            log.error("刷新模板缓存时发生异常，templateId: {}", templateId, e);
            return R.fail("刷新失败：" + e.getMessage());
        }
    }

    /**
     * 刷新默认内容缓存
     * @description 刷新默认内容的缓存
     * @return 刷新结果
     */
    @PostMapping("/refresh-content-cache")
    @Operation(
        summary = "刷新默认内容缓存", 
        description = "清除并重新加载默认内容的缓存"
    )
    public R<String> refreshContentCache() {
        try {
            log.info("开始刷新默认内容缓存");
            
            boolean success = defaultContentService.refreshContentCache();
            
            if (success) {
                log.info("默认内容缓存刷新成功");
                return R.ok("默认内容缓存刷新成功");
            } else {
                log.warn("默认内容缓存刷新失败");
                return R.fail("默认内容缓存刷新失败");
            }
        } catch (Exception e) {
            log.error("刷新默认内容缓存时发生异常", e);
            return R.fail("刷新失败：" + e.getMessage());
        }
    }

    /**
     * 检查模板可用性
     * @description 检查模板在各个存储层的可用性状态
     * @param templateId 模板ID
     * @return 可用性报告
     */
    @GetMapping("/check/{templateId}")
    @Operation(
        summary = "检查模板可用性", 
        description = "检查模板在Redis、MinIO、Local各层的可用性状态"
    )
    public R<String> checkTemplateAvailability(
        @Parameter(description = "模板ID", required = true)
        @PathVariable Long templateId
    ) {
        try {
            log.info("开始检查模板可用性，templateId: {}", templateId);
            
            String report = templateLoaderService.checkTemplateAvailability(templateId);
            
            log.info("模板可用性检查完成，templateId: {}", templateId);
            return R.ok(report);
        } catch (Exception e) {
            log.error("检查模板可用性时发生异常，templateId: {}", templateId, e);
            return R.fail("检查失败：" + e.getMessage());
        }
    }

    /**
     * 检查默认内容是否存在
     * @description 检查指定条件的默认内容是否存在
     * @param templateCategory 模板类别
     * @param industry 行业类型（可选）
     * @param position 职位类型（可选）
     * @return 是否存在
     */
    @GetMapping("/check-content")
    @Operation(
        summary = "检查默认内容是否存在", 
        description = "检查指定条件的默认内容是否存在"
    )
    public R<Boolean> checkDefaultContentExists(
        @Parameter(description = "模板类别", required = true, example = "modern")
        @RequestParam("templateCategory") String templateCategory,
        
        @Parameter(description = "行业类型", example = "tech")
        @RequestParam(value = "industry", required = false) String industry,
        
        @Parameter(description = "职位类型", example = "frontend")
        @RequestParam(value = "position", required = false) String position
    ) {
        try {
            log.info("开始检查默认内容是否存在，templateCategory: {}, industry: {}, position: {}", 
                templateCategory, industry, position);
            
            boolean exists = defaultContentService.hasDefaultContent(templateCategory, industry, position);
            
            log.info("默认内容存在性检查完成，结果: {}", exists);
            return R.ok(exists);
        } catch (Exception e) {
            log.error("检查默认内容存在性时发生异常，templateCategory: {}", templateCategory, e);
            return R.fail("检查失败：" + e.getMessage());
        }
    }



    /**
     * 上传模板文件
     * @description 上传Vue模板文件和JSON默认内容文件，支持按模板分组的目录结构
     * @param templateCode 模板代码
     * @param templateName 模板名称
     * @param templateCategory 模板类别
     * @param description 模板描述
     * @param vueFile Vue模板文件
     * @param jsonFile JSON默认内容文件
     * @param htmlFile HTML预览文件（可选）
     * @return 上传结果
     */
    @PostMapping("/upload")
    @Operation(
        summary = "上传模板文件", 
        description = "上传Vue模板文件和JSON默认内容文件，支持按模板分组的目录结构：resume-templates/{templateCode}/"
    )
    public R<Map<String, Object>> uploadTemplate(
        @Parameter(description = "模板代码", required = true, example = "modern-001")
        @RequestParam("templateCode") String templateCode,
        
        @Parameter(description = "模板名称", required = true, example = "现代简约模板")
        @RequestParam("templateName") String templateName,
        
        @Parameter(description = "模板类别", required = true, example = "modern")
        @RequestParam("templateCategory") String templateCategory,
        
        @Parameter(description = "模板描述", example = "适合IT行业的现代简约风格简历模板")
        @RequestParam(value = "description", required = false) String description,
        
        @Parameter(description = "Vue模板文件", required = true)
        @RequestParam("vueFile") MultipartFile vueFile,
        
        @Parameter(description = "JSON默认内容文件", required = true)
        @RequestParam("jsonFile") MultipartFile jsonFile,
        
        @Parameter(description = "HTML预览文件（可选）")
        @RequestParam(value = "htmlFile", required = false) MultipartFile htmlFile
    ) {
        try {
            log.info("开始上传模板文件，templateCode: {}, templateName: {}, templateCategory: {}", 
                templateCode, templateName, templateCategory);
            
            // 参数验证
            if (!StringUtils.hasText(templateCode) || !StringUtils.hasText(templateName) || 
                !StringUtils.hasText(templateCategory)) {
                return R.fail("模板代码、名称和类别不能为空");
            }
            
            if (vueFile == null || vueFile.isEmpty()) {
                return R.fail("Vue模板文件不能为空");
            }
            
            if (jsonFile == null || jsonFile.isEmpty()) {
                return R.fail("JSON默认内容文件不能为空");
            }
            
            // 验证文件类型
            if (!isValidFileType(vueFile, "vue")) {
                return R.fail("Vue文件格式不正确");
            }
            
            if (!isValidFileType(jsonFile, "json")) {
                return R.fail("JSON文件格式不正确");
            }
            
            if (htmlFile != null && !htmlFile.isEmpty() && !isValidFileType(htmlFile, "html")) {
                return R.fail("HTML文件格式不正确");
            }
            
            // 读取文件内容
            String vueContent = new String(vueFile.getBytes(), StandardCharsets.UTF_8);
            String jsonContent = new String(jsonFile.getBytes(), StandardCharsets.UTF_8);
            String htmlContent = null;
            
            if (htmlFile != null && !htmlFile.isEmpty()) {
                htmlContent = new String(htmlFile.getBytes(), StandardCharsets.UTF_8);
            }
            
            // 保存文件到MinIO
            boolean vueSuccess = templateFileService.saveVueFile(templateCode, vueContent);
            boolean jsonSuccess = templateFileService.saveJsonFile(templateCode, jsonContent);
            boolean htmlSuccess = true; // HTML文件是可选的
            
            if (htmlContent != null) {
                htmlSuccess = templateFileService.saveHtmlFile(templateCode, htmlContent);
            }
            
            // 构建响应结果
            Map<String, Object> result = new HashMap<>();
            result.put("templateCode", templateCode);
            result.put("templateName", templateName);
            result.put("templateCategory", templateCategory);
            result.put("description", description);
            result.put("vueFileUploaded", vueSuccess);
            result.put("jsonFileUploaded", jsonSuccess);
            result.put("htmlFileUploaded", htmlSuccess);
            result.put("vueFileSize", vueFile.getSize());
            result.put("jsonFileSize", jsonFile.getSize());
            result.put("htmlFileSize", htmlFile != null ? htmlFile.getSize() : 0);
            
            // 存储路径信息
            result.put("vueFilePath", String.format("resume-templates/%s/%s.vue", templateCode, templateCode));
            result.put("jsonFilePath", String.format("resume-templates/%s/%s.json", templateCode, templateCode));
            
            if (htmlContent != null) {
                result.put("htmlFilePath", String.format("resume-templates/%s/%s.html", templateCode, templateCode));
            }
            
            if (vueSuccess && jsonSuccess && htmlSuccess) {
                log.info("模板文件上传成功，templateCode: {}, vueSize: {}, jsonSize: {}, htmlSize: {}", 
                    templateCode, vueFile.getSize(), jsonFile.getSize(), 
                    htmlFile != null ? htmlFile.getSize() : 0);
                
                result.put("success", true);
                result.put("message", "模板文件上传成功");
                return R.ok(result);
            } else {
                log.warn("模板文件上传部分失败，templateCode: {}, vueSuccess: {}, jsonSuccess: {}, htmlSuccess: {}", 
                    templateCode, vueSuccess, jsonSuccess, htmlSuccess);
                
                result.put("success", false);
                result.put("message", "模板文件上传部分失败");
                return R.fail(result, "上传失败");
            }
            
        } catch (Exception e) {
            log.error("上传模板文件时发生异常，templateCode: {}", templateCode, e);
            return R.fail("上传失败：" + e.getMessage());
        }
    }

    /**
     * 验证文件类型
     * @param file 文件
     * @param expectedType 期望的文件类型
     * @return 是否有效
     */
    private boolean isValidFileType(MultipartFile file, String expectedType) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            return false;
        }
        
        String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        return expectedType.equals(extension);
    }
} 