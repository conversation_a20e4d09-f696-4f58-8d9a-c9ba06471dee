# SpecStory explanation file
.specstory/.what-is-this.md

# === Java / Spring Boot ===
*.class
*.log
*.war
*.jar
*.ear

# Compiled class files
target/
build/

# Maven
.mvn/
!**/src/main/**/maven-wrapper.jar

# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar

# IntelliJ IDEA
.idea/
*.iml
*.iws
out/

# Eclipse
.project
.classpath
.settings/
bin/

# VS Code
.vscode/

# === Node.js / Vue ===
node_modules/
dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
vite.config.*
vite.*
.env.local
.env.*.local
.env.development.local
.env.production.local
app-resume-web/.nuxt/

# === OS 文件 ===
.DS_Store
Thumbs.db
desktop.ini

# === 日志与临时文件 ===
*.log
*.tmp
*.bak
*.swp
*.swo

# === 测试报告或覆盖率 ===
coverage/
.nyc_output/

# === 输出或缓存 ===
.cache/
.cache-loader/
.eslintcache
.idea_modules/
logs/
*.pid

# === 本地配置或密钥 ===
*.key
*.crt
*.pem
*.env
*.secret

