package com.alan6.resume.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * 登录请求DTO
 * 
 * 主要功能：
 * 1. 接收前端登录请求参数
 * 2. 参数校验和格式化
 * 3. 支持多种登录方式的统一入口
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@Schema(name = "登录请求", description = "用户登录请求参数")
public class LoginRequest {

    /**
     * 登录类型
     * 1-手机号登录，2-微信授权登录，3-小程序登录
     */
    @Schema(description = "登录类型：1-手机号登录，2-微信授权登录，3-小程序登录", 
            requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "登录类型不能为空")
    private Integer loginType;

    /**
     * 登录平台
     * web、wechat_mp、douyin_mp、baidu_mp、alipay_mp、app_ios、app_android
     */
    @Schema(description = "登录平台标识", 
            requiredMode = Schema.RequiredMode.REQUIRED, example = "web")
    @NotBlank(message = "登录平台不能为空")
    @Pattern(regexp = "^(web|wechat_mp|douyin_mp|baidu_mp|alipay_mp|app_ios|app_android)$", 
             message = "登录平台格式不正确")
    private String platform;

    /**
     * 手机号
     * 当登录类型为手机号登录时必填
     */
    @Schema(description = "手机号，手机号登录时必填", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 短信验证码
     * 当登录类型为手机号登录时必填
     */
    @Schema(description = "短信验证码，手机号登录时必填", example = "123456")
    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确，应为6位数字")
    private String smsCode;

    /**
     * 微信授权码
     * 当登录类型为微信授权登录时必填
     */
    @Schema(description = "微信授权码")
    private String wechatCode;

    /**
     * 小程序登录凭证
     * 当登录类型为小程序登录时必填
     */
    @Schema(description = "小程序登录凭证")
    private String jsCode;

    /**
     * 小程序用户信息
     * 小程序登录时可选，用于获取用户昵称、头像等信息
     */
    @Schema(description = "小程序用户信息")
    private String userInfo;

    /**
     * 设备标识
     * 用于设备绑定和安全控制
     */
    @Schema(description = "设备标识")
    private String deviceId;

    /**
     * 用户代理信息
     * 记录用户的浏览器或应用信息
     */
    @Schema(description = "用户代理信息")
    private String userAgent;

    /**
     * 客户端IP地址
     * 系统自动获取，前端无需传递
     */
    @Schema(description = "客户端IP地址")
    private String clientIp;

    /**
     * 验证手机号登录参数是否完整
     * 
     * @return true-参数完整，false-参数不完整
     */
    public boolean isPhoneLoginValid() {
        return phone != null && !phone.trim().isEmpty() 
               && smsCode != null && !smsCode.trim().isEmpty();
    }

    /**
     * 验证微信授权登录参数是否完整
     * 
     * @return true-参数完整，false-参数不完整
     */
    public boolean isWechatLoginValid() {
        return wechatCode != null && !wechatCode.trim().isEmpty();
    }

    /**
     * 验证小程序登录参数是否完整
     * 
     * @return true-参数完整，false-参数不完整
     */
    public boolean isMiniProgramLoginValid() {
        return jsCode != null && !jsCode.trim().isEmpty();
    }
} 