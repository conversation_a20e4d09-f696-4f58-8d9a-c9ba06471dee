-- ================================
-- 简历模板表结构更新脚本
-- 用于支持Vue模板动态加载功能
-- 作者：火花简历团队
-- 版本：1.0.0
-- ================================

-- 删除旧表（如果需要完全重建）
-- DROP TABLE IF EXISTS `resume_templates`;

-- 创建新的简历模板表
CREATE TABLE `resume_templates` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `template_code` VARCHAR(50) NOT NULL COMMENT '模板代码（对应文件夹名或组件名）',
  `description` TEXT DEFAULT NULL COMMENT '模板描述',
  `preview_image_url` VARCHAR(500) DEFAULT NULL COMMENT '预览图URL',
  
  -- 新增字段：文件关联和配置
  `template_file_id` BIGINT DEFAULT NULL COMMENT '关联的模板文件ID（file_upload_records表）',
  `local_file_path` VARCHAR(500) DEFAULT NULL COMMENT '本地文件路径',
  `config_data` JSON DEFAULT NULL COMMENT '模板配置数据（主题、样式等）',
  `features` JSON DEFAULT NULL COMMENT '支持的功能特性列表',
  `tags` JSON DEFAULT NULL COMMENT '模板标签',
  
  -- 原有字段
  `category_id` BIGINT DEFAULT NULL COMMENT '分类ID',
  `industry` VARCHAR(100) DEFAULT NULL COMMENT '适用行业',
  `style` VARCHAR(50) DEFAULT NULL COMMENT '模板风格',
  `color_scheme` VARCHAR(50) DEFAULT NULL COMMENT '配色方案',
  `is_premium` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否付费模板（0:免费,1:付费）',
  `price` DECIMAL(10,2) DEFAULT NULL COMMENT '模板价格',
  `use_count` INT NOT NULL DEFAULT 0 COMMENT '使用次数',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态（0:下架,1:上架）',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_template_file_id` (`template_file_id`),
  KEY `idx_status_deleted` (`status`, `is_deleted`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历模板表';

-- ================================
-- 插入示例数据
-- ================================

-- 插入现代简约模板
INSERT INTO `resume_templates` (
  `id`, `name`, `template_code`, `description`, `preview_image_url`,
  `template_file_id`, `local_file_path`, `config_data`, `features`, `tags`,
  `category_id`, `industry`, `style`, `color_scheme`, `is_premium`, `price`,
  `use_count`, `sort_order`, `status`, `is_deleted`
) VALUES (
  1, 
  '现代简约模板', 
  'modern-simple', 
  '适合IT、金融等行业的现代简约风格模板，突出专业性和简洁性',
  'https://example.com/preview/modern-simple.jpg',
  NULL, -- 暂时为空，等待上传到MinIO后更新
  '/templates/vue/modern-simple.vue', -- 本地文件路径
  JSON_OBJECT(
    'primaryColor', '#2563eb',
    'secondaryColor', '#64748b', 
    'fontFamily', 'Inter, sans-serif',
    'fontSize', '14px',
    'lineHeight', '1.6',
    'spacing', 'normal',
    'borderRadius', '8px',
    'supportsDarkMode', true,
    'supportsCustomColors', true
  ),
  JSON_ARRAY('拖拽排序', '实时预览', '多主题', '响应式', '打印优化', '数据导出'),
  JSON_ARRAY('简约', '现代', '商务', '专业', 'IT', '推荐'),
  1, -- 通用分类
  'IT,金融,咨询,互联网',
  '现代简约',
  '蓝色系',
  0, -- 免费
  NULL,
  156, -- 使用次数
  100, -- 高优先级
  1, -- 上架
  0  -- 未删除
);

-- 插入创意设计模板
INSERT INTO `resume_templates` (
  `id`, `name`, `template_code`, `description`, `preview_image_url`,
  `template_file_id`, `local_file_path`, `config_data`, `features`, `tags`,
  `category_id`, `industry`, `style`, `color_scheme`, `is_premium`, `price`,
  `use_count`, `sort_order`, `status`, `is_deleted`
) VALUES (
  2,
  '创意设计模板',
  'creative-design',
  '专为设计师、创意工作者打造的个性化模板，支持作品展示和创意表达',
  'https://example.com/preview/creative-design.jpg',
  NULL,
  '/templates/vue/creative-design.vue',
  JSON_OBJECT(
    'primaryColor', '#8b5cf6',
    'secondaryColor', '#a78bfa',
    'fontFamily', 'Poppins, sans-serif',
    'fontSize', '15px',
    'lineHeight', '1.7',
    'spacing', 'relaxed',
    'borderRadius', '12px',
    'supportsDarkMode', true,
    'supportsCustomColors', true,
    'supportsImageGallery', true
  ),
  JSON_ARRAY('作品展示', '创意布局', '图片画廊', '动画效果', '个性配色', '移动适配'),
  JSON_ARRAY('创意', '设计', '艺术', '个性', '作品集'),
  2, -- 创意分类
  '设计,广告,艺术,媒体,游戏',
  '创意个性',
  '紫色系',
  1, -- 付费
  29.99,
  89,
  90,
  1,
  0
);

-- 插入商务经典模板
INSERT INTO `resume_templates` (
  `id`, `name`, `template_code`, `description`, `preview_image_url`,
  `template_file_id`, `local_file_path`, `config_data`, `features`, `tags`,
  `category_id`, `industry`, `style`, `color_scheme`, `is_premium`, `price`,
  `use_count`, `sort_order`, `status`, `is_deleted`
) VALUES (
  3,
  '商务经典模板',
  'business-classic',
  '传统商务风格，适合金融、法律、管理等传统行业，强调稳重和专业',
  'https://example.com/preview/business-classic.jpg',
  NULL,
  '/templates/vue/business-classic.vue',
  JSON_OBJECT(
    'primaryColor', '#1f2937',
    'secondaryColor', '#6b7280',
    'fontFamily', 'Georgia, serif',
    'fontSize', '13px',
    'lineHeight', '1.5',
    'spacing', 'compact',
    'borderRadius', '4px',
    'supportsDarkMode', false,
    'supportsCustomColors', false
  ),
  JSON_ARRAY('经典布局', '正式风格', '打印友好', 'ATS兼容', '传统格式'),
  JSON_ARRAY('商务', '经典', '传统', '正式', '金融', '法律'),
  3, -- 商务分类
  '金融,法律,管理,政府,传统企业',
  '商务经典',
  '黑白灰',
  0, -- 免费
  NULL,
  234,
  80,
  1,
  0
);

-- 插入技术极客模板
INSERT INTO `resume_templates` (
  `id`, `name`, `template_code`, `description`, `preview_image_url`,
  `template_file_id`, `local_file_path`, `config_data`, `features`, `tags`,
  `category_id`, `industry`, `style`, `color_scheme`, `is_premium`, `price`,
  `use_count`, `sort_order`, `status`, `is_deleted`
) VALUES (
  4,
  '技术极客模板',
  'tech-geek',
  '专为程序员和技术人员设计，支持代码展示、技术栈可视化和项目详情',
  'https://example.com/preview/tech-geek.jpg',
  NULL,
  '/templates/vue/tech-geek.vue',
  JSON_OBJECT(
    'primaryColor', '#10b981',
    'secondaryColor', '#34d399',
    'fontFamily', 'JetBrains Mono, monospace',
    'fontSize', '14px',
    'lineHeight', '1.6',
    'spacing', 'normal',
    'borderRadius', '6px',
    'supportsDarkMode', true,
    'supportsCustomColors', true,
    'supportsCodeHighlight', true,
    'supportsTechStack', true
  ),
  JSON_ARRAY('代码高亮', '技术栈图表', '项目展示', 'GitHub集成', '暗色主题', '终端风格'),
  JSON_ARRAY('技术', '程序员', '极客', '代码', '开发', 'GitHub'),
  4, -- 技术分类
  '软件开发,互联网,AI,区块链,游戏开发',
  '技术极客',
  '绿色系',
  1, -- 付费
  39.99,
  67,
  85,
  1,
  0
);

-- 插入清新文艺模板
INSERT INTO `resume_templates` (
  `id`, `name`, `template_code`, `description`, `preview_image_url`,
  `template_file_id`, `local_file_path`, `config_data`, `features`, `tags`,
  `category_id`, `industry`, `style`, `color_scheme`, `is_premium`, `price`,
  `use_count`, `sort_order`, `status`, `is_deleted`
) VALUES (
  5,
  '清新文艺模板',
  'fresh-literary',
  '清新文艺风格，适合教育、文化、媒体等行业，注重内容表达和美感',
  'https://example.com/preview/fresh-literary.jpg',
  NULL,
  '/templates/vue/fresh-literary.vue',
  JSON_OBJECT(
    'primaryColor', '#f59e0b',
    'secondaryColor', '#fbbf24',
    'fontFamily', 'Merriweather, serif',
    'fontSize', '15px',
    'lineHeight', '1.8',
    'spacing', 'relaxed',
    'borderRadius', '16px',
    'supportsDarkMode', true,
    'supportsCustomColors', true,
    'supportsWatermark', true
  ),
  JSON_ARRAY('文艺布局', '温馨配色', '内容重点', '阅读友好', '个性水印', '优雅动效'),
  JSON_ARRAY('文艺', '清新', '教育', '文化', '温馨', '优雅'),
  5, -- 文艺分类
  '教育,文化,媒体,出版,非营利',
  '清新文艺',
  '橙黄色系',
  0, -- 免费
  NULL,
  123,
  70,
  1,
  0
);

-- ================================
-- 创建相关索引和约束
-- ================================

-- 添加外键约束（如果file_upload_records表存在）
-- ALTER TABLE `resume_templates` 
-- ADD CONSTRAINT `fk_template_file` 
-- FOREIGN KEY (`template_file_id`) REFERENCES `file_upload_records`(`id`) 
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- 创建全文索引用于搜索
-- ALTER TABLE `resume_templates` 
-- ADD FULLTEXT KEY `ft_search` (`name`, `description`, `industry`);

-- ================================
-- 数据库配置说明
-- ================================

/*
字段说明：

1. template_file_id: 关联file_upload_records表，指向MinIO中存储的Vue文件
2. local_file_path: 本地文件路径，作为备用存储
3. config_data: JSON格式的模板配置，包括：
   - primaryColor: 主色调
   - secondaryColor: 辅助色
   - fontFamily: 字体族
   - fontSize: 字体大小
   - lineHeight: 行高
   - spacing: 间距风格
   - borderRadius: 圆角大小
   - supportsDarkMode: 是否支持暗色模式
   - supportsCustomColors: 是否支持自定义颜色
   - 其他特殊功能配置

4. features: JSON数组，包含模板支持的功能特性
5. tags: JSON数组，包含模板的标签，用于分类和搜索

使用场景：
- 前端通过template_code请求模板
- 后端优先从Redis缓存获取
- 缓存未命中时从MinIO加载（通过template_file_id）
- MinIO失败时从本地文件加载（通过local_file_path）
- 加载成功后更新Redis缓存

数据流向：
Redis缓存 -> MinIO对象存储 -> 本地文件系统
*/ 