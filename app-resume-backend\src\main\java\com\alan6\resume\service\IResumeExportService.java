package com.alan6.resume.service;

import com.alan6.resume.dto.resume.ResumeExportRequest;
import com.alan6.resume.dto.resume.ResumeExportResponse;

/**
 * 简历导出服务接口
 * 
 * @description 定义简历导出相关的业务方法
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IResumeExportService {

    /**
     * 提交导出任务
     * 
     * @param request 导出请求
     * @return 导出响应
     */
    ResumeExportResponse submitExportJob(ResumeExportRequest request);

    /**
     * 获取导出状态
     * 
     * @param jobId 任务ID
     * @return 导出状态
     */
    ResumeExportResponse getExportStatus(String jobId);

    /**
     * 处理导出任务
     * 
     * @param jobId 任务ID
     */
    void processExportJob(String jobId);

    /**
     * 取消导出任务
     * 
     * @param jobId 任务ID
     * @return 是否取消成功
     */
    boolean cancelExportJob(String jobId);

    /**
     * 清理过期的导出文件
     */
    void cleanupExpiredExports();

    /**
     * 获取用户的导出历史
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 导出历史列表
     */
    Object getExportHistory(Long userId, Integer limit);
} 