package com.alan6.resume.security;

import com.alan6.resume.entity.Users;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * 用户主体类
 * 
 * 主要功能：
 * 1. 实现Spring Security的UserDetails接口
 * 2. 封装用户认证信息
 * 3. 提供用户状态检查方法
 * 4. 作为Security上下文中的用户信息载体
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal implements UserDetails {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户状态（0:禁用,1:正常）
     */
    private Byte status;

    /**
     * 注册平台
     */
    private String registerPlatform;

    /**
     * 用户角色列表
     */
    private List<String> roles;

    /**
     * 用户权限列表
     */
    private List<String> permissions;

    /**
     * 从用户实体创建用户主体对象
     * 
     * @param user 用户实体对象
     * @return 用户主体对象
     */
    public static UserPrincipal create(Users user) {
        return new UserPrincipal(
                user.getId(),
                user.getUsername(),
                user.getNickname(),
                user.getAvatarUrl(),
                user.getPhone(),
                user.getEmail(),
                user.getStatus(),
                user.getRegisterPlatform(),
                null,
                null
        );
    }

    /**
     * 从缓存对象创建用户主体对象
     *
     * @param userInfo 缓存中的用户信息对象
     * @return 用户主体对象
     */
    public static UserPrincipal create(Object userInfo) {
        if (userInfo instanceof Users) {
            return create((Users) userInfo);
        }

        // 处理Redis反序列化后的LinkedHashMap类型
        if (userInfo instanceof Map) {
            Map<?, ?> userMap = (Map<?, ?>) userInfo;
            return createFromMap(userMap);
        }

        // 如果是其他类型，返回空对象
        log.warn("无法识别的用户信息类型: {}", userInfo.getClass().getName());
        return new UserPrincipal();
    }

    /**
     * 从Map对象创建用户主体对象
     *
     * @param userMap 用户信息Map
     * @return 用户主体对象
     */
    private static UserPrincipal createFromMap(Map<?, ?> userMap) {
        try {
            UserPrincipal userPrincipal = new UserPrincipal();

            // 提取用户ID（支持多种可能的字段名）
            Object id = userMap.get("userId");  // 首先查找userId字段
            if (id == null) {
                id = userMap.get("id");  // 如果没有userId，则查找id字段
            }
            if (id != null) {
                if (id instanceof Number) {
                    userPrincipal.setUserId(((Number) id).longValue());
                } else {
                    userPrincipal.setUserId(Long.parseLong(id.toString()));
                }
            }

            // 提取其他字段
            userPrincipal.setUsername(getStringValue(userMap, "username"));
            userPrincipal.setNickname(getStringValue(userMap, "nickname"));
            userPrincipal.setAvatarUrl(getStringValue(userMap, "avatarUrl", "avatar_url"));
            userPrincipal.setPhone(getStringValue(userMap, "phone"));
            userPrincipal.setEmail(getStringValue(userMap, "email"));
            userPrincipal.setRegisterPlatform(getStringValue(userMap, "registerPlatform", "register_platform"));

            // 提取状态字段
            Object status = userMap.get("status");
            if (status != null) {
                if (status instanceof Number) {
                    userPrincipal.setStatus(((Number) status).byteValue());
                } else {
                    userPrincipal.setStatus(Byte.parseByte(status.toString()));
                }
            }

            // 提取角色信息
            Object rolesObj = userMap.get("roles");
            if (rolesObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> rolesList = (List<String>) rolesObj;
                userPrincipal.setRoles(rolesList);
            }

            // 提取权限信息
            Object permissionsObj = userMap.get("permissions");
            if (permissionsObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> permissionsList = (List<String>) permissionsObj;
                userPrincipal.setPermissions(permissionsList);
            }

            return userPrincipal;

        } catch (Exception e) {
            log.error("从Map创建UserPrincipal失败", e);
            return new UserPrincipal();
        }
    }

    /**
     * 从Map中获取String值的辅助方法
     * 支持多个可能的字段名（snake_case和camelCase）
     */
    private static String getStringValue(Map<?, ?> map, String... keys) {
        for (String key : keys) {
            Object value = map.get(key);
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }

    /**
     * 获取用户权限集合
     * 
     * 根据用户角色和权限返回Spring Security权限集合
     * 
     * @return 权限集合
     */
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        // 添加角色权限
        if (roles != null && !roles.isEmpty()) {
            for (String role : roles) {
                authorities.add(new SimpleGrantedAuthority("ROLE_" + role));
            }
        }
        
        // 添加具体权限
        if (permissions != null && !permissions.isEmpty()) {
            for (String permission : permissions) {
                authorities.add(new SimpleGrantedAuthority(permission));
            }
        }
        
        return authorities;
    }

    /**
     * 获取密码
     * 
     * 由于使用Token认证，此方法返回null
     * 
     * @return 密码（此处返回null）
     */
    @Override
    public String getPassword() {
        // Token认证模式下不需要密码
        return null;
    }

    /**
     * 获取用户名
     * 
     * @return 用户名
     */
    @Override
    public String getUsername() {
        return this.username;
    }

    /**
     * 账户是否未过期
     * 
     * @return true-未过期，false-已过期
     */
    @Override
    public boolean isAccountNonExpired() {
        // 暂时返回true，后续可根据业务需要实现账户过期逻辑
        return true;
    }

    /**
     * 账户是否未锁定
     * 
     * @return true-未锁定，false-已锁定
     */
    @Override
    public boolean isAccountNonLocked() {
        // 根据用户状态判断账户是否被锁定
        // 这里简单判断状态不为禁用即为未锁定
        return status != null && status == 1;
    }

    /**
     * 凭证是否未过期
     * 
     * @return true-未过期，false-已过期
     */
    @Override
    public boolean isCredentialsNonExpired() {
        // Token认证模式下，凭证过期由Token管理
        return true;
    }

    /**
     * 账户是否启用
     * 
     * @return true-启用，false-禁用
     */
    @Override
    public boolean isEnabled() {
        // 根据用户状态判断账户是否启用
        return status != null && status == 1;
    }

    /**
     * 获取用户显示名称
     * 
     * 优先返回昵称，如果昵称为空则返回用户名
     * 
     * @return 用户显示名称
     */
    public String getDisplayName() {
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname;
        }
        return username;
    }

    /**
     * 检查用户是否为管理员
     * 
     * @return true-管理员，false-普通用户
     */
    public boolean isAdmin() {
        return roles != null && (roles.contains("SUPER_ADMIN") || roles.contains("ADMIN"));
    }

    /**
     * 检查用户是否为会员
     * 
     * @return true-会员，false-普通用户
     */
    public boolean isVip() {
        // 暂时返回false，后续可根据会员信息判断
        // 需要查询用户会员表来确定会员状态
        return false;
    }

    /**
     * 获取用户信息摘要（用于日志记录）
     * 
     * @return 用户信息摘要字符串
     */
    public String getSummary() {
        return String.format("User{id=%d, username='%s', nickname='%s', status=%d}", 
                            userId, username, nickname, status);
    }

    /**
     * 重写toString方法，避免敏感信息泄露
     * 
     * @return 安全的字符串表示
     */
    @Override
    public String toString() {
        return String.format("UserPrincipal{userId=%d, username='%s', nickname='%s'}", 
                            userId, username, nickname);
    }
} 