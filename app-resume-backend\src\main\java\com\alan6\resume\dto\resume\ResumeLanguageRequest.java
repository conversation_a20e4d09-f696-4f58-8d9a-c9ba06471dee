package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 简历语言能力请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(name = "ResumeLanguageRequest", description = "简历语言能力请求")
public class ResumeLanguageRequest {

    /**
     * 语言名称（必填，1-50字符）
     */
    @NotBlank(message = "语言名称不能为空")
    @Size(min = 1, max = 50, message = "语言名称长度必须在1-50个字符之间")
    @Schema(description = "语言名称", example = "英语")
    private String languageName;

    /**
     * 熟练程度（必填，1:入门，2:初级，3:中级，4:高级，5:母语）
     */
    @NotNull(message = "熟练程度不能为空")
    @Min(value = 1, message = "熟练程度最小值为1")
    @Max(value = 5, message = "熟练程度最大值为5")
    @Schema(description = "熟练程度", example = "4", allowableValues = {"1", "2", "3", "4", "5"})
    private Integer proficiencyLevel;

    /**
     * 证书类型（可选，如：CET-4、CET-6、TOEFL、IELTS等）
     */
    @Size(max = 50, message = "证书类型长度不能超过50个字符")
    @Schema(description = "证书类型", example = "CET-6")
    private String certificateType;

    /**
     * 证书成绩（可选）
     */
    @Size(max = 20, message = "证书成绩长度不能超过20个字符")
    @Schema(description = "证书成绩", example = "580")
    private String certificateScore;

    /**
     * 排序权重（必填）
     */
    @NotNull(message = "排序权重不能为空")
    @Min(value = 0, message = "排序权重不能为负数")
    @Schema(description = "排序权重", example = "1")
    private Integer sortOrder;
} 