<template>
  <div class="skills-form">
    <div class="form-header">
      <h4 class="form-title">技能特长</h4>
      <p class="form-desc">展示您的专业技能和水平</p>
    </div>
    
    <div v-if="formData.skills && formData.skills.length > 0" class="skills-list">
      <div v-for="(skill, index) in formData.skills" :key="index" class="skill-item">
        <div class="skill-header">
          <input
            v-model="skill.name"
            type="text"
            class="skill-name-input"
            placeholder="技能名称"
            @input="handleSkillChange"
          />
          
          <button 
            class="delete-btn"
            @click="removeSkill(index)"
            title="删除技能"
          >
            ×
          </button>
        </div>
        
        <!-- 百分比模式 -->
        <div v-if="skill.displayMode === 'percentage'" class="skill-level-control">
          <div class="level-input-row">
            <!-- 显示方式选择 -->
            <div class="item-display-mode">
              <label class="mode-option">
                <input
                  v-model="skill.displayMode"
                  type="radio"
                  value="percentage"
                  @change="handleSkillDisplayModeChange(index)"
                />
                <span>百分比</span>
              </label>
              <label class="mode-option">
                <input
                  v-model="skill.displayMode"
                  type="radio"
                  value="proficiency"
                  @change="handleSkillDisplayModeChange(index)"
                />
                <span>熟练程度</span>
              </label>
            </div>
            
            <label class="level-label">熟练度: {{ skill.level }}%</label>
          </div>
          <input
            v-model.number="skill.level"
            type="range"
            min="0"
            max="100"
            step="5"
            class="level-slider"
            @input="handleSkillLevelChange(index)"
          />
          <div class="skill-progress">
            <div 
              class="progress-bar" 
              :style="{ width: skill.level + '%' }"
            ></div>
          </div>
        </div>
        
        <!-- 熟练程度模式 -->
        <div v-else class="skill-proficiency-control">
          <div class="proficiency-input-row">
            <!-- 显示方式选择 -->
            <div class="item-display-mode">
              <label class="mode-option">
                <input
                  v-model="skill.displayMode"
                  type="radio"
                  value="percentage"
                  @change="handleSkillDisplayModeChange(index)"
                />
                <span>百分比</span>
              </label>
              <label class="mode-option">
                <input
                  v-model="skill.displayMode"
                  type="radio"
                  value="proficiency"
                  @change="handleSkillDisplayModeChange(index)"
                />
                <span>熟练程度</span>
              </label>
            </div>
            
            <label class="proficiency-label">熟练程度：</label>
            <select
              v-model="skill.proficiency"
              class="proficiency-select"
              @change="handleSkillProficiencyChange(index)"
            >
              <option value="">请选择熟练程度</option>
              <option value="精通">精通</option>
              <option value="擅长">擅长</option>
              <option value="熟练">熟练</option>
              <option value="良好">良好</option>
              <option value="一般">一般</option>
            </select>
          </div>
          <div class="proficiency-display">
            <span class="proficiency-text">{{ skill.proficiency || '未设置' }}</span>
            <div class="proficiency-bar">
              <div 
                class="proficiency-fill" 
                :style="{ width: skill.level + '%' }"
                :class="getProficiencyClass(skill.proficiency)"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无技能信息</p>
      <p class="empty-hint">点击下方按钮添加您的技能</p>
    </div>
    
    <div class="form-actions">
      <button class="add-btn" @click="addSkill">
        <Icon name="plus" size="sm" />
        <span>添加技能</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, watch } from 'vue'
import Icon from '~/components/common/Icon.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ skills: [] })
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = reactive({
  skills: []
})

// 防抖定时器
let debounceTimer = null

// 熟练程度和百分比的映射关系
const proficiencyMapping = {
  '精通': 95,
  '擅长': 82,
  '熟练': 67,
  '良好': 50,
  '一般': 30
}

// 百分比转熟练程度
const getPercentageToProficiency = (level) => {
  if (level >= 90) return '精通'
  if (level >= 75) return '擅长'
  if (level >= 60) return '熟练'
  if (level >= 40) return '良好'
  if (level >= 20) return '一般'
  return '一般'
}

// 获取熟练程度的样式类
const getProficiencyClass = (proficiency) => {
  const classMap = {
    '精通': 'expert',
    '擅长': 'skilled',
    '熟练': 'proficient',
    '良好': 'good',
    '一般': 'basic'
  }
  return classMap[proficiency] || 'basic'
}

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.skills) {
    formData.skills = newData.skills.map(skill => ({
      ...skill,
      displayMode: skill.displayMode || 'percentage',
      proficiency: skill.proficiency || getPercentageToProficiency(skill.level || 50)
    }))
  } else {
    formData.skills = []
  }
}, { immediate: true, deep: true })

/**
 * 处理单个技能的显示模式变化
 */
const handleSkillDisplayModeChange = (index) => {
  const skill = formData.skills[index]
  
  // 如果切换到熟练程度模式且没有设置proficiency，则根据level设置
  if (skill.displayMode === 'proficiency' && !skill.proficiency) {
    skill.proficiency = getPercentageToProficiency(skill.level || 50)
  }
  
  handleSkillChange()
}

/**
 * 处理技能变化 - 实时更新
 */
const handleSkillChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的定时器，300ms后触发更新
  debounceTimer = setTimeout(() => {
    emit('update', { 
      skills: [...formData.skills]
    })
  }, 300)
}

/**
 * 处理百分比变化
 */
const handleSkillLevelChange = (index) => {
  const skill = formData.skills[index]
  // 同步更新熟练程度
  skill.proficiency = getPercentageToProficiency(skill.level)
  handleSkillChange()
}

/**
 * 处理熟练程度变化
 */
const handleSkillProficiencyChange = (index) => {
  const skill = formData.skills[index]
  // 同步更新百分比
  skill.level = proficiencyMapping[skill.proficiency] || 50
  handleSkillChange()
}

/**
 * 添加技能
 */
const addSkill = () => {
  const newSkill = {
    name: '',
    level: 50,
    proficiency: '良好',
    displayMode: 'percentage'
  }
  formData.skills.push(newSkill)
  handleSkillChange()
}

/**
 * 删除技能
 */
const removeSkill = (index) => {
  formData.skills.splice(index, 1)
  handleSkillChange()
}
</script>

<style scoped>
.skills-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.skills-list {
  @apply space-y-4 mb-6;
}

.skill-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-3;
}

.skill-header {
  @apply flex items-center gap-3;
}

/* 每个技能项的显示模式选择器 */
.item-display-mode {
  @apply flex gap-3 bg-white rounded-md px-2 py-1 border border-gray-200 mr-4;
}

.mode-option {
  @apply flex items-center gap-1 cursor-pointer;
}

.mode-option input[type="radio"] {
  @apply w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500;
}

.mode-option span {
  @apply text-sm text-gray-600;
}

.skill-name-input {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.delete-btn {
  @apply w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold;
}

/* 百分比模式样式 */
.skill-level-control {
  @apply space-y-2;
}

/* 百分比输入行 - 单选框和标签在同一行 */
.level-input-row {
  @apply flex items-center gap-4;
}

.level-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.level-slider {
  @apply w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer;
}

.level-slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-blue-500 rounded-full cursor-pointer;
}

.level-slider::-moz-range-thumb {
  @apply w-4 h-4 bg-blue-500 rounded-full cursor-pointer border-0;
}

.skill-progress {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar {
  @apply h-2 bg-blue-500 rounded-full transition-all duration-300;
}

/* 熟练程度模式样式 */
.skill-proficiency-control {
  @apply space-y-3;
}

/* 熟练程度输入行 - 标签和下拉框在同一行 */
.proficiency-input-row {
  @apply flex items-center gap-4;
}

.proficiency-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.proficiency-select {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.proficiency-display {
  @apply flex items-center gap-3;
}

.proficiency-text {
  @apply text-sm font-medium text-gray-700 min-w-[60px];
}

.proficiency-bar {
  @apply flex-1 bg-gray-200 rounded-full h-2;
}

.proficiency-fill {
  @apply h-2 rounded-full transition-all duration-300;
}

/* 熟练程度颜色 */
.proficiency-fill.expert {
  @apply bg-purple-500;
}

.proficiency-fill.skilled {
  @apply bg-green-500;
}

.proficiency-fill.proficient {
  @apply bg-blue-500;
}

.proficiency-fill.good {
  @apply bg-yellow-500;
}

.proficiency-fill.basic {
  @apply bg-gray-500;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-xs text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}
</style> 