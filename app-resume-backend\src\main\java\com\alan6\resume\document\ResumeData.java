package com.alan6.resume.document;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 简历数据MongoDB文档实体
 * 
 * @description 存储完整的简历JSON数据，包括模块内容、样式设置等
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "resume_data")
@CompoundIndexes({
    @CompoundIndex(name = "idx_user_lastsaved", def = "{'userId': 1, 'lastSaved': -1}"),
    @CompoundIndex(name = "idx_template_status", def = "{'templateId': 1, 'status': 1}")
})
public class ResumeData {

    /**
     * MongoDB文档ID
     */
    @Id
    private String id;

    /**
     * 简历ID（对应MySQL中的简历ID）
     */
    @Indexed(unique = true)
    private String resumeId;

    /**
     * 用户ID
     */
    @Indexed
    private Long userId;

    /**
     * 模板ID
     */
    @Indexed
    private String templateId;

    /**
     * 简历标题
     */
    private String title;

    /**
     * 简历语言
     */
    private String language;

    /**
     * 简历模块列表
     * 包含所有模块的数据和配置
     */
    private List<ResumeModule> modules;

    /**
     * 全局样式设置
     * 包含主题、字体、间距等全局配置
     */
    private GlobalSettings globalSettings;

    /**
     * 简历状态
     * 0-草稿，1-完成
     */
    private Integer status;

    /**
     * 最后保存时间
     */
    private LocalDateTime lastSaved;

    /**
     * 数据版本号
     * 用于乐观锁控制
     */
    private Long version;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 简历模块定义
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ResumeModule {
        /**
         * 模块ID
         */
        private String id;

        /**
         * 模块类型
         * basic-info, education, skills, work-experience等
         */
        private String type;

        /**
         * 模块名称
         */
        private String name;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 排序顺序
         */
        private Integer order;

        /**
         * 是否必需
         */
        private Boolean required;

        /**
         * 自定义标题
         */
        private String customTitle;

        /**
         * 模块样式
         * 包含该模块特定的样式设置
         */
        private Map<String, Object> styles;

        /**
         * 模块数据
         * 根据不同模块类型存储不同的数据结构
         */
        private Map<String, Object> data;
    }

    /**
     * 全局设置定义
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class GlobalSettings {
        /**
         * 主题设置
         */
        private ThemeSettings theme;

        /**
         * 布局设置
         */
        private LayoutSettings layout;

        /**
         * 排版设置
         */
        private TypographySettings typography;

        /**
         * 页面设置
         */
        private PageSettings page;
    }

    /**
     * 主题设置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ThemeSettings {
        /**
         * 主色调
         */
        private String primaryColor;

        /**
         * 次要色调
         */
        private String secondaryColor;

        /**
         * 背景色
         */
        private String backgroundColor;

        /**
         * 文本颜色
         */
        private String textColor;

        /**
         * 字体族
         */
        private String fontFamily;

        /**
         * 基础字体大小
         */
        private String fontSize;

        /**
         * 行高
         */
        private String lineHeight;
    }

    /**
     * 布局设置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LayoutSettings {
        /**
         * 上边距
         */
        private String marginTop;

        /**
         * 下边距
         */
        private String marginBottom;

        /**
         * 左边距
         */
        private String marginLeft;

        /**
         * 右边距
         */
        private String marginRight;

        /**
         * 模块间距
         */
        private String moduleSpacing;

        /**
         * 页面宽度
         */
        private String pageWidth;

        /**
         * 页面高度
         */
        private String pageHeight;
    }

    /**
     * 排版设置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TypographySettings {
        /**
         * 一级标题大小
         */
        private String h1Size;

        /**
         * 一级标题字重
         */
        private String h1Weight;

        /**
         * 一级标题颜色
         */
        private String h1Color;

        /**
         * 二级标题大小
         */
        private String h2Size;

        /**
         * 二级标题字重
         */
        private String h2Weight;

        /**
         * 二级标题颜色
         */
        private String h2Color;

        /**
         * 正文字体大小
         */
        private String bodySize;

        /**
         * 正文字重
         */
        private String bodyWeight;

        /**
         * 正文颜色
         */
        private String bodyColor;
    }

    /**
     * 页面设置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PageSettings {
        /**
         * 纸张大小
         * A4, Letter等
         */
        private String size;

        /**
         * 页面方向
         * portrait（纵向）, landscape（横向）
         */
        private String orientation;

        /**
         * 缩放比例
         */
        private Double scale;
    }
} 