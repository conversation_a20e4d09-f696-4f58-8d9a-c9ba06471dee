package com.alan6.resume.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI文档配置类
 * 
 * 主要功能：
 * 1. 配置API文档基本信息
 * 2. 配置JWT Token认证方式
 * 3. 设置全局安全要求
 * 4. 优化Swagger UI展示
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Configuration
public class OpenApiConfig {

    /**
     * 配置OpenAPI文档信息
     * 
     * @return OpenAPI配置对象
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("简历制作应用API")
                        .description("简历制作应用后端接口文档\n\n" +
                                "## 认证说明\n" +
                                "大部分接口需要JWT Token认证，请先调用登录接口获取Token，然后点击右上角的🔒按钮输入Token。\n\n" +
                                "## Token格式\n" +
                                "Bearer {your-jwt-token}\n\n" +
                                "## 公开接口\n" +
                                "- POST /auth/login - 用户登录\n" +
                                "- POST /auth/send-code - 发送验证码")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Alan6")
                                .email("<EMAIL>")))
                .components(new Components()
                        .addSecuritySchemes("Bearer Token", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("请输入JWT Token，格式：Bearer {token}")))
                .addSecurityItem(new SecurityRequirement().addList("Bearer Token"));
    }
} 