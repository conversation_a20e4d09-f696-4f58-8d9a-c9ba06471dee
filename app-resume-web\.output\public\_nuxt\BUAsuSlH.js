import{r as u,i as C,c as v,a as s,d as h,t as c,p as b,v as w,F as I,g as N,o as k,s as L}from"./CURHyiUL.js";import{_ as U}from"./DlAUqK2U.js";const z={class:"auth-debug-page"},B={class:"debug-section"},P={class:"debug-info"},R={class:"debug-section"},j={class:"test-controls"},E=["disabled"],J=["disabled"],O={class:"debug-section"},D={class:"direct-test"},G=["disabled"],V={class:"debug-section"},F={class:"log-container"},M={class:"log-time"},q={class:"log-message"},H={__name:"admin-auth-debug",setup(K){const r=u(""),d=u("test-code-123"),p=u("resume-template"),l=u(!1),g=u([]),t=(o,e="info")=>{g.value.push({time:new Date().toLocaleTimeString(),message:o,type:e})},$=()=>{r.value=localStorage.getItem("admin_token")||"",t(`认证令牌获取: ${r.value?"有令牌":"无令牌"}`)},A=()=>{localStorage.removeItem("admin_token"),r.value="",t("认证信息已清除","warning")},T=async()=>{l.value=!0,t(`开始测试模板代码检查: ${d.value}`);try{const o=localStorage.getItem("admin_token");t(`使用令牌: ${o?o.substring(0,20)+"...":"无令牌"}`);const e=new URL("/api/admin/template/check-code",window.location.origin);e.searchParams.append("templateCode",d.value);const n={Accept:"application/json"};o?(n.Authorization=`Bearer ${o}`,t("已添加Authorization头")):t("未添加Authorization头","warning"),t(`请求URL: ${e.toString()}`),t(`请求头: ${JSON.stringify(n)}`);const a=await fetch(e,{method:"GET",headers:n});if(t(`响应状态: ${a.status} ${a.statusText}`),a.ok){const i=await a.json();t(`响应结果: ${JSON.stringify(i)}`,"success")}else{const i=await a.text();t(`错误响应: ${i}`,"error")}}catch(o){t(`请求失败: ${o.message}`,"error")}finally{l.value=!1}},_=async()=>{l.value=!0,t(`开始测试桶状态检查: ${p.value}`);try{const o=localStorage.getItem("admin_token");t(`使用令牌: ${o?o.substring(0,20)+"...":"无令牌"}`);const e=new URL("/api/admin/template/check-bucket",window.location.origin);e.searchParams.append("bucketName",p.value);const n={Accept:"application/json"};o?(n.Authorization=`Bearer ${o}`,t("已添加Authorization头")):t("未添加Authorization头","warning"),t(`请求URL: ${e.toString()}`),t(`请求头: ${JSON.stringify(n)}`);const a=await fetch(e,{method:"GET",headers:n});if(t(`响应状态: ${a.status} ${a.statusText}`),a.ok){const i=await a.json();t(`响应结果: ${JSON.stringify(i)}`,"success")}else{const i=await a.text();t(`错误响应: ${i}`,"error")}}catch(o){t(`请求失败: ${o.message}`,"error")}finally{l.value=!1}},y=async()=>{l.value=!0,t("开始直接API测试");try{const o=localStorage.getItem("admin_token");t(`当前域名: ${window.location.origin}`),t(`当前路径: ${window.location.pathname}`);const e="/api/admin/template/check-code?templateCode=test";t(`测试URL: ${e}`);const n={Accept:"application/json","Content-Type":"application/json"};o?(n.Authorization=`Bearer ${o}`,t(`Authorization头: Bearer ${o.substring(0,20)}...`)):t("警告: 没有找到认证令牌","warning"),t("请求方法: GET"),t(`请求头: ${JSON.stringify(n,null,2)}`);const a=await fetch(e,{method:"GET",headers:n});t(`响应状态: ${a.status} ${a.statusText}`),t(`响应头: ${JSON.stringify([...a.headers.entries()],null,2)}`);const i=await a.text();if(t(`响应内容: ${i}`),a.status===401&&(t("检测到401错误，可能需要重新登录","error"),t("建议: 1. 检查localStorage中的token是否有效","warning"),t("建议: 2. 重新登录管理后台","warning"),t("建议: 3. 检查后端服务是否正常运行","warning")),a.status===200){t("第一个接口测试成功，继续测试桶检查接口");const f="/api/admin/template/check-bucket?bucketName=resume-template";t(`桶测试URL: ${f}`);const m=await fetch(f,{method:"GET",headers:n});t(`桶检查响应状态: ${m.status} ${m.statusText}`);const S=await m.text();t(`桶检查响应内容: ${S}`)}}catch(o){t(`直接API测试失败: ${o.message}`,"error"),t(`错误堆栈: ${o.stack}`,"error")}finally{l.value=!1}},x=()=>{g.value=[]};return C(()=>{$(),t("页面加载完成")}),(o,e)=>(k(),v("div",z,[e[12]||(e[12]=s("h1",null,"认证调试页面",-1)),s("div",B,[e[5]||(e[5]=s("h2",null,"1. 认证状态检查",-1)),s("div",P,[s("p",null,[e[2]||(e[2]=s("strong",null,"Auth Token:",-1)),h(" "+c(r.value||"未设置"),1)]),s("p",null,[e[3]||(e[3]=s("strong",null,"Token长度:",-1)),h(" "+c(r.value?r.value.length:0),1)]),s("p",null,[e[4]||(e[4]=s("strong",null,"Token前缀:",-1)),h(" "+c(r.value?r.value.substring(0,20)+"...":"N/A"),1)]),s("button",{onClick:$},"刷新认证信息"),s("button",{onClick:A},"清除认证信息")])]),s("div",R,[e[8]||(e[8]=s("h2",null,"2. 手动测试API调用",-1)),s("div",j,[s("div",null,[e[6]||(e[6]=s("label",null,"模板代码:",-1)),b(s("input",{"onUpdate:modelValue":e[0]||(e[0]=n=>d.value=n),placeholder:"输入测试代码"},null,512),[[w,d.value]]),s("button",{onClick:T,disabled:l.value},"测试代码检查",8,E)]),s("div",null,[e[7]||(e[7]=s("label",null,"桶名称:",-1)),b(s("input",{"onUpdate:modelValue":e[1]||(e[1]=n=>p.value=n),placeholder:"输入桶名称"},null,512),[[w,p.value]]),s("button",{onClick:_,disabled:l.value},"测试桶检查",8,J)])])]),s("div",O,[e[10]||(e[10]=s("h2",null,"3. 直接API测试",-1)),s("div",D,[s("button",{onClick:y,disabled:l.value},"直接测试API",8,G),e[9]||(e[9]=s("p",null,"这将直接使用fetch调用API，绕过Service层",-1))])]),s("div",V,[e[11]||(e[11]=s("h2",null,"4. 调试日志",-1)),s("div",F,[(k(!0),v(I,null,N(g.value,(n,a)=>(k(),v("div",{key:a,class:L(["log-item",n.type])},[s("span",M,c(n.time),1),s("span",q,c(n.message),1)],2))),128))]),s("button",{onClick:x},"清除日志")])]))}},X=U(H,[["__scopeId","data-v-f51153cd"]]);export{X as default};
