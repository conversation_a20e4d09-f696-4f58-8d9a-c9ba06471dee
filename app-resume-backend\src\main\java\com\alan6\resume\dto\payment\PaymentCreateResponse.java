package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 创建支付订单响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "PaymentCreateResponse", description = "创建支付订单响应")
public class PaymentCreateResponse {

    /**
     * 支付ID
     */
    @Schema(description = "支付ID", example = "PAY20241222123456789")
    private String paymentId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "ORD20241222123456789")
    private String orderNo;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式", example = "wechat")
    private String paymentMethod;

    /**
     * 支付类型
     */
    @Schema(description = "支付类型", example = "qrcode")
    private String paymentType;

    /**
     * 支付信息
     */
    @Schema(description = "支付信息")
    private PaymentInfo paymentInfo;

    /**
     * 超时时间（分钟）
     */
    @Schema(description = "超时时间（分钟）", example = "30")
    private Integer timeoutMinutes;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-22 15:00:00")
    private LocalDateTime createTime;

    /**
     * 支付信息内部类
     */
    @Data
    @Schema(name = "PaymentInfo", description = "支付信息")
    public static class PaymentInfo {
        
        /**
         * 二维码URL
         */
        @Schema(description = "二维码URL", example = "weixin://wxpay/bizpayurl?pr=abc123def")
        private String qrcodeUrl;

        /**
         * 二维码图片（Base64）
         */
        @Schema(description = "二维码图片", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhE...")
        private String qrcodeImage;

        /**
         * 过期时间
         */
        @Schema(description = "过期时间", example = "2024-12-22 15:30:00")
        private LocalDateTime expireTime;

        /**
         * 支付金额
         */
        @Schema(description = "支付金额", example = "199.90")
        private BigDecimal amount;

        /**
         * 货币类型
         */
        @Schema(description = "货币类型", example = "CNY")
        private String currency;
    }
} 