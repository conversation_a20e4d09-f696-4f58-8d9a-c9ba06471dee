# 简历模板 Composables 使用指南

## 概述

为了解决简历模板代码重复和维护困难的问题，我们将公共逻辑抽取到了可复用的 composables 中。现在创建新的简历模板变得非常简单，只需要专注于模板的视觉设计，所有的数据处理、操作逻辑、样式计算都由 composables 自动处理。

## 核心 Composables

### 1. useResumeTemplateBase (主入口)

这是所有简历模板的基础 composable，整合了所有功能：

```javascript
import { useResumeTemplateBase } from '@/composables/useResumeTemplateBase'

export default {
  setup(props, { emit }) {
    // 获取所有模板功能
    const templateBase = useResumeTemplateBase(props, emit)
    
    // 解构需要的功能
    const {
      // 数据
      mergedResumeData,
      sortedModules,
      
      // 操作
      moveSkillUp,
      deleteSkill,
      
      // 样式
      titleStyle,
      getSkillProgressStyle
    } = templateBase
    
    return {
      mergedResumeData,
      sortedModules,
      moveSkillUp,
      deleteSkill,
      titleStyle,
      getSkillProgressStyle
    }
  }
}
```

### 2. useResumeDataProcessor

专门处理数据的 composable：

```javascript
import { useResumeDataProcessor } from '@/composables/useResumeDataProcessor'

// 提供的功能：
// - mergedResumeData: 合并后的简历数据
// - sortedModules: 排序后的模块列表
// - formatWorkPeriod: 格式化工作时间段
// - defaultResumeData: 默认数据
```

### 3. useModuleOperations

处理模块和项目操作的 composable：

```javascript
import { useModuleOperations } from '@/composables/useModuleOperations'

// 提供的功能：
// - 悬停状态管理
// - 模块操作（上移、下移、删除）
// - 技能操作
// - 工作经历操作
// - 教育经历操作
// - 项目经历操作
```

### 4. useTemplateStyles

处理样式计算的 composable：

```javascript
import { useTemplateStyles } from '@/composables/useTemplateStyles'

// 提供的功能：
// - 各种样式计算
// - 主题色彩处理
// - 响应式样式
// - 技能进度条样式
```

## 创建新模板的步骤

### 1. 创建模板文件

```vue
<template>
  <div ref="templateRef" class="my-resume-template">
    <!-- 基本信息 -->
    <header>
      <h1>{{ mergedResumeData.basicInfo.name }}</h1>
      <p>{{ mergedResumeData.basicInfo.title }}</p>
    </header>
    
    <!-- 技能列表 -->
    <section v-if="mergedResumeData.skills.length > 0">
      <h2 :style="titleStyle">专业技能</h2>
      <div 
        v-for="(skill, index) in mergedResumeData.skills" 
        :key="index"
        @mouseenter="hoveredSkill = index"
        @mouseleave="hoveredSkill = -1"
      >
        <span>{{ skill.name }}</span>
        <div class="progress-bar">
          <div :style="getSkillProgressStyle(skill.level)"></div>
        </div>
        
        <!-- 操作按钮 -->
        <div v-if="isDraggable && hoveredSkill === index">
          <button @click="moveSkillUp(index)">上移</button>
          <button @click="moveSkillDown(index)">下移</button>
          <button @click="deleteSkill(index)">删除</button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  useResumeTemplateBase, 
  TEMPLATE_STANDARD_PROPS, 
  TEMPLATE_STANDARD_EVENTS 
} from '@/composables/useResumeTemplateBase'

// 标准属性和事件
const props = defineProps(TEMPLATE_STANDARD_PROPS)
const emit = defineEmits(TEMPLATE_STANDARD_EVENTS)

// 模板引用
const templateRef = ref(null)

// 获取所有基础功能
const templateBase = useResumeTemplateBase(props, emit)

// 解构需要的功能
const {
  mergedResumeData,
  hoveredSkill,
  titleStyle,
  getSkillProgressStyle,
  moveSkillUp,
  moveSkillDown,
  deleteSkill
} = templateBase

// 暴露给父组件
defineExpose({
  templateRef,
  debugInfo: templateBase.debugInfo
})
</script>
```

### 2. 模板必须遵循的规范

#### 属性规范
- 使用 `TEMPLATE_STANDARD_PROPS` 定义属性
- 所有模板都有相同的属性接口

#### 事件规范
- 使用 `TEMPLATE_STANDARD_EVENTS` 定义事件
- 必须支持所有标准事件

#### 暴露接口规范
- 必须暴露 `templateRef` 和 `debugInfo`
- 用于父组件调试和操作

## 可用的数据和方法

### 数据相关
- `mergedResumeData`: 处理后的简历数据
- `sortedModules`: 排序后的模块列表
- `leftColumnModules`: 左侧栏模块
- `rightColumnModules`: 右侧栏模块

### 操作相关
- `hoveredSkill/Experience/Education/Project`: 悬停状态
- `moveSkillUp/Down`: 技能排序
- `deleteSkill`: 删除技能
- `moveExperienceUp/Down`: 工作经历排序
- `deleteExperience`: 删除工作经历
- （其他模块的操作方法类似）

### 样式相关
- `titleStyle`: 标题样式
- `getSkillProgressStyle(level)`: 技能进度条样式
- `mergedTextStyle`: 合并后的文本样式
- `pageStyle`: 页面样式

### 工具方法
- `formatWorkPeriod(start, end)`: 格式化工作时间段
- `formatEducationPeriod(start, end)`: 格式化教育时间段
- `formatProjectPeriod(start, end)`: 格式化项目时间段

## 调试功能

每个模板都提供了调试功能：

```javascript
// 在模板中调用
debugInfo.printAllData() // 打印所有数据
debugInfo.printPerformanceInfo() // 打印性能信息
debugInfo.validateDataIntegrity() // 验证数据完整性
```

## 模板开发最佳实践

### 1. 专注于视觉设计
- 不需要处理数据逻辑，专注于HTML结构和CSS样式
- 使用提供的数据和样式计算结果

### 2. 遵循命名规范
- 使用统一的CSS类名前缀
- 遵循BEM命名规范

### 3. 响应式设计
- 使用提供的响应式样式方法
- 考虑不同屏幕尺寸的适配

### 4. 性能优化
- 避免在模板中进行复杂计算
- 使用计算属性缓存结果

## 类型支持

我们提供了完整的 TypeScript 类型定义：

```typescript
import type { 
  ResumeData, 
  TextStyle, 
  TemplateProps 
} from '@/types/resume-template'
```

## 示例模板

参考 `ResumeTemplate.vue` 文件，它已经使用了新的 composables 架构，代码从1700+行减少到了500+行，同时保持了所有原有功能。

## 迁移现有模板

如果你有现有的模板需要迁移：

1. 替换属性定义为 `TEMPLATE_STANDARD_PROPS`
2. 替换事件定义为 `TEMPLATE_STANDARD_EVENTS`  
3. 使用 `useResumeTemplateBase` 替换所有数据处理逻辑
4. 删除重复的操作方法
5. 使用提供的样式计算方法

## 常见问题

### Q: 如何添加模板特有的功能？
A: 在使用 `useResumeTemplateBase` 后，可以添加模板特有的计算属性和方法。

### Q: 如何自定义样式？
A: 使用 `useTemplateStyles` 提供的样式方法，或者在模板中添加特有的样式计算。

### Q: 如何处理特殊的数据格式？
A: 可以在模板中对 `mergedResumeData` 进行二次处理，但建议扩展 composables。

### Q: 如何确保模板兼容性？
A: 遵循标准属性和事件接口，使用类型检查，运行调试验证。 