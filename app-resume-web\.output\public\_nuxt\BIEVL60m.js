import{r as v,i as $,B as j,c as a,a as t,f as R,F as B,g as D,o as r,t as p,b as V,w as z,q as T,T as H,n as f}from"./CURHyiUL.js";import{$ as i}from"./D1FrdRFX.js";import{_ as L}from"./DlAUqK2U.js";const A={key:0,class:"flex items-center justify-center py-12"},N={key:1,class:"text-center py-12"},E={key:2,class:"grid gap-4 md:grid-cols-2 lg:grid-cols-3"},I={class:"aspect-[3/4] bg-secondary-100 relative overflow-hidden"},S=["src","alt"],F={key:1,class:"w-full h-full flex items-center justify-center"},P={class:"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3"},U=["onClick"],q=["onClick"],O={class:"p-4"},G={class:"font-medium text-secondary-900 mb-1 truncate"},J={class:"text-sm text-secondary-500"},K={class:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"},Q={class:"relative"},W=["onClick"],X={key:0,class:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-100 overflow-hidden z-10"},Y=["onClick"],Z=["onClick"],tt=["onClick"],et={__name:"MyResumes",setup(ot){const d=v(!0),c=v([]),l=v(null),y=[{id:"1",title:"前端开发工程师简历",thumbnail:null,updatedAt:new Date("2024-01-15T10:30:00"),templateId:"template-1"},{id:"2",title:"UI设计师简历",thumbnail:null,updatedAt:new Date("2024-01-10T14:20:00"),templateId:"template-2"},{id:"3",title:"产品经理简历",thumbnail:null,updatedAt:new Date("2024-01-05T09:15:00"),templateId:"template-3"}],b=o=>{if(!o)return"-";const e=new Date(o),n=new Date-e;return n<36e5?`${Math.floor(n/6e4)}分钟前`:n<864e5?`${Math.floor(n/36e5)}小时前`:n<6048e5?`${Math.floor(n/864e5)}天前`:e.toLocaleDateString("zh-CN")},x=o=>{l.value=l.value===o?null:o},m=o=>{o.target.closest(".relative")||(l.value=null)},g=()=>{f("/templates")},w=o=>{f(`/editor/${o.id}`)},k=o=>{i.info("预览功能开发中...")},_=async o=>{l.value=null;try{i.success("简历复制成功"),await u()}catch{i.error("复制失败，请重试")}},C=o=>{l.value=null,i.info("重命名功能开发中...")},M=async o=>{if(l.value=null,confirm(`确定要删除"${o.title}"吗？此操作不可恢复。`))try{i.success("简历已删除"),await u()}catch{i.error("删除失败，请重试")}},u=async()=>{d.value=!0;try{await new Promise(o=>setTimeout(o,500)),c.value=y}catch{i.error("加载简历列表失败")}finally{d.value=!1}};return $(()=>{u(),document.addEventListener("click",m)}),j(()=>{document.removeEventListener("click",m)}),(o,e)=>(r(),a("div",null,[e[7]||(e[7]=t("div",{class:"mb-6"},[t("h2",{class:"text-2xl font-bold text-secondary-900"},"我的简历"),t("p",{class:"mt-1 text-sm text-secondary-600"},"管理您创建的所有简历")],-1)),d.value?(r(),a("div",A,e[0]||(e[0]=[t("div",{class:"text-center"},[t("div",{class:"w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),t("p",{class:"text-secondary-600"},"加载中...")],-1)]))):c.value.length===0?(r(),a("div",N,[e[1]||(e[1]=R('<div class="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-b6a8b85d><svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b6a8b85d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-b6a8b85d></path></svg></div><h3 class="text-lg font-medium text-secondary-900 mb-2" data-v-b6a8b85d>还没有创建简历</h3><p class="text-secondary-600 mb-6" data-v-b6a8b85d>开始创建您的第一份专业简历吧</p>',3)),t("button",{onClick:g,class:"px-6 py-3 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200"}," 创建简历 ")])):(r(),a("div",E,[(r(!0),a(B,null,D(c.value,s=>(r(),a("div",{key:s.id,class:"group relative bg-white border border-secondary-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300"},[t("div",I,[s.thumbnail?(r(),a("img",{key:0,src:s.thumbnail,alt:s.title,class:"w-full h-full object-cover"},null,8,S)):(r(),a("div",F,e[2]||(e[2]=[t("svg",{class:"w-16 h-16 text-secondary-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)]))),t("div",P,[t("button",{onClick:n=>w(s),class:"px-4 py-2 bg-white text-secondary-900 rounded-lg font-medium hover:bg-secondary-100 transition-colors duration-200"}," 编辑 ",8,U),t("button",{onClick:n=>k(),class:"px-4 py-2 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200"}," 预览 ",8,q)])]),t("div",O,[t("h3",G,p(s.title||"未命名简历"),1),t("p",J," 更新于 "+p(b(s.updatedAt)),1)]),t("div",K,[t("div",Q,[t("button",{onClick:n=>x(s.id),class:"w-8 h-8 bg-white/90 backdrop-blur rounded-lg flex items-center justify-center hover:bg-white transition-colors duration-200"},e[3]||(e[3]=[t("svg",{class:"w-5 h-5 text-secondary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,W),V(H,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:z(()=>[l.value===s.id?(r(),a("div",X,[t("button",{onClick:n=>_(s),class:"w-full px-4 py-2 text-left text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-150"},e[4]||(e[4]=[t("span",{class:"flex items-center space-x-2"},[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})]),t("span",null,"复制简历")],-1)]),8,Y),t("button",{onClick:n=>C(s),class:"w-full px-4 py-2 text-left text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-150"},e[5]||(e[5]=[t("span",{class:"flex items-center space-x-2"},[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),t("span",null,"重命名")],-1)]),8,Z),t("button",{onClick:n=>M(s),class:"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"},e[6]||(e[6]=[t("span",{class:"flex items-center space-x-2"},[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})]),t("span",null,"删除")],-1)]),8,tt)])):T("",!0)]),_:2},1024)])])]))),128))]))]))}},rt=L(et,[["__scopeId","data-v-b6a8b85d"]]);export{rt as default};
