<template>
  <div class="font-selector w-36">
    <select 
      :value="modelValue"
      @change="handleChange"
      class="font-selector-dropdown"
    >
      <option 
        v-for="font in fontOptions" 
        :key="font.value" 
        :value="font.value"
        :style="{ fontFamily: font.value }"
      >
        {{ font.label }}
      </option>
    </select>
    
    <svg class="dropdown-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <polyline points="6,9 12,15 18,9"></polyline>
    </svg>
  </div>
</template>

<script setup>
/**
 * 字体选择器组件
 * @description 提供字体选择功能，支持预览
 * <AUTHOR>
 * @since 2.0.0
 */

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 当前选中的字体
   */
  modelValue: {
    type: String,
    default: 'system-ui'
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['update:modelValue', 'change'])

// ================================
// 字体选项配置
// ================================
const fontOptions = [
  { label: '系统默认', value: 'system-ui' },
  { label: '微软雅黑', value: "'Microsoft YaHei', sans-serif" },
  { label: '黑体', value: "'SimHei', sans-serif" },
  { label: '宋体', value: "'SimSun', serif" },
  { label: '楷体', value: "'KaiTi', serif" },
  { label: 'Arial', value: "'Arial', sans-serif" },
  { label: 'Times New Roman', value: "'Times New Roman', serif" },
  { label: 'Helvetica', value: "'Helvetica', sans-serif" },
  { label: 'Georgia', value: "'Georgia', serif" }
]

// ================================
// 事件处理方法
// ================================

/**
 * 处理字体变化
 * @param {Event} event - 选择事件
 */
const handleChange = (event) => {
  const value = event.target.value
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped>
/* ================================
 * 字体选择器容器
 * ================================ */
.font-selector {
  @apply relative;
}

/* ================================
 * 下拉选择框
 * ================================ */
.font-selector-dropdown {
  @apply w-full px-3 py-2 pr-8;
  @apply text-sm text-gray-900;
  @apply bg-white border border-gray-200 rounded-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  @apply transition-all duration-200;
  @apply cursor-pointer;
  @apply appearance-none;
}

.font-selector-dropdown:hover {
  @apply border-gray-300 shadow-sm;
}

.font-selector-dropdown:focus {
  @apply shadow-md;
}

/* ================================
 * 下拉图标
 * ================================ */
.dropdown-icon {
  @apply absolute right-2 top-1/2 transform -translate-y-1/2;
  @apply text-gray-400;
  @apply pointer-events-none;
  @apply transition-transform duration-200;
}

.font-selector:hover .dropdown-icon {
  @apply text-gray-600;
}

/* ================================
 * 选项样式
 * ================================ */
.font-selector-dropdown option {
  @apply py-2 px-4;
}

/* ================================
 * 禁用状态
 * ================================ */
.font-selector-dropdown:disabled {
  @apply bg-gray-50 text-gray-400 cursor-not-allowed;
}

.font-selector-dropdown:disabled:hover {
  @apply border-gray-200 shadow-none;
}

/* ================================
 * 响应式设计
 * ================================ */
@media (max-width: 768px) {
  .font-selector-dropdown {
    @apply px-2 py-1.5 text-sm;
  }
}
</style> 