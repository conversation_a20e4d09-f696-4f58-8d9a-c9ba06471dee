package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 支付方式响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "PaymentMethodResponse", description = "支付方式响应")
public class PaymentMethodResponse {

    /**
     * 支付方式列表
     */
    @Schema(description = "支付方式列表")
    private List<PaymentMethod> methods;

    /**
     * 支付方式详情
     */
    @Data
    @Schema(name = "PaymentMethod", description = "支付方式详情")
    public static class PaymentMethod {
        
        /**
         * 支付方式代码
         */
        @Schema(description = "支付方式代码", example = "wechat")
        private String code;

        /**
         * 支付方式名称
         */
        @Schema(description = "支付方式名称", example = "微信支付")
        private String name;

        /**
         * 支付方式图标URL
         */
        @Schema(description = "支付方式图标URL", example = "https://cdn.example.com/icons/wechat.png")
        private String icon;

        /**
         * 是否启用
         */
        @Schema(description = "是否启用", example = "true")
        private Boolean enabled;

        /**
         * 支付方式描述
         */
        @Schema(description = "支付方式描述", example = "使用微信扫码支付")
        private String description;

        /**
         * 支持的支付类型
         */
        @Schema(description = "支持的支付类型", example = "[\"qrcode\", \"h5\", \"app\"]")
        private List<String> supportedTypes;
    }
} 