package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 简历导出队列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Getter
@Setter
@TableName("resume_export_queue")
@Schema(name = "ResumeExportQueue对象", description = "简历导出队列表")
public class ResumeExportQueue implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "队列ID")
    private Long id;

    @Schema(description = "任务ID")
    private String jobId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "简历ID")
    private Long resumeId;

    @Schema(description = "导出格式（pdf/word/png/jpg）")
    private String exportFormat;

    @Schema(description = "优先级1-10")
    private Integer priority;

    @Schema(description = "状态（queued/processing/completed/failed）")
    private String status;

    @Schema(description = "下载链接")
    private String downloadUrl;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "尝试次数")
    private Integer attempts;

    @Schema(description = "最大尝试次数")
    private Integer maxAttempts;

    @Schema(description = "开始处理时间")
    private LocalDateTime startedAt;

    @Schema(description = "完成时间")
    private LocalDateTime completedAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 