package com.alan6.resume.dto.system;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 语言列表响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
public class LanguageListResponse {

    /**
     * 语言列表
     */
    private List<LanguageInfo> languages;

    /**
     * 语言信息内部类
     */
    @Data
    public static class LanguageInfo {
        /**
         * 语言ID
         */
        private Long id;

        /**
         * 语言代码（如：zh-CN, en-US）
         */
        private String code;

        /**
         * 语言名称
         */
        private String name;

        /**
         * 本地化名称
         */
        private String nativeName;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 是否为默认语言
         */
        private Boolean isDefault;

        /**
         * 排序顺序
         */
        private Integer sortOrder;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
} 