/**
 * 编辑器状态管理
 * @description 管理简历编辑器的全局状态，包括简历数据、编辑状态、历史记录等
 *              简化版本：移除默认内容数据，内容数据由后端提供
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, nextTick } from 'vue'
import { useResumeData } from './useResumeData'
import { useUndoRedo } from '../shared/useUndoRedo'

// ================================
// 常量定义
// ================================

/**
 * 模块类型枚举
 */
export const MODULE_TYPES = {
  // 默认启用模块
  BASIC_INFO: 'basic_info',
  EDUCATION: 'education',
  WORK_EXPERIENCE: 'work_experience',
  PROJECT: 'project',
  SKILLS: 'skills',
  LANGUAGE: 'language',
  AWARD: 'award',
  HOBBIES: 'hobbies',
  SELF_EVALUATION: 'self_evaluation',
  
  // 默认可选模块
  INTERNSHIP: 'internship',
  TRAINING: 'training',
  RESEARCH_EXPERIENCE: 'research_experience',
  PUBLICATION: 'publication',
  CERTIFICATE: 'certificate',
  VOLUNTEER_EXPERIENCE: 'volunteer_experience',
  PORTFOLIO: 'portfolio',
  COVER_LETTER: 'cover_letter',
  CUSTOM: 'custom'
}

/**
 * 默认模块配置（不包含数据）
 * @description 只包含模块的配置信息，不包含具体的数据内容
 */
const DEFAULT_MODULE_CONFIGS = [
  // 默认启用模块
  {
    id: 'basic_info',
    type: 'basic_info',
    name: '基本信息',
    icon: 'user',
    order: 1,
    enabled: true,
    required: true,
    description: '个人基本信息，包括姓名、联系方式等',
    tips: '填写准确的个人信息，确保HR能够联系到您',
    examples: [
      '姓名应使用真实姓名',
      '手机号保持畅通',
      '邮箱地址要准确'
    ]
  },
  {
    id: 'education',
    type: 'education',
    name: '教育经历',
    icon: 'graduation-cap',
    order: 2,
    enabled: true,
    required: false,
    description: '教育背景和学历信息',
    tips: '按时间倒序排列，突出相关专业和优秀成绩',
    examples: [
      '2018-2022 北京大学 计算机科学与技术 本科',
      '主修课程：数据结构、算法、数据库等'
    ]
  },
  {
    id: 'work_experience',
    type: 'work_experience',
    name: '工作经历',
    icon: 'briefcase',
    order: 3,
    enabled: true,
    required: false,
    description: '工作经验和职业发展',
    tips: '用数据说话，突出工作成果和贡献',
    examples: [
      '负责用户增长项目，使活跃用户提升30%',
      '优化系统性能，响应时间减少50%'
    ]
  },
  {
    id: 'project',
    type: 'project',
    name: '项目经验',
    icon: 'folder',
    order: 4,
    enabled: true,
    required: false,
    description: '参与的重要项目经验',
    tips: '重点介绍技术栈、个人职责和项目成果',
    examples: [
      '使用Vue3+TypeScript开发管理系统',
      '负责前端架构设计和核心功能开发'
    ]
  },
  {
    id: 'skills',
    type: 'skills',
    name: '技能标签',
    icon: 'star',
    order: 5,
    enabled: true,
    required: false,
    description: '专业技能和特长',
    tips: '列出与目标职位相关的技能，可分类展示',
    examples: [
      '编程语言：JavaScript、Python、Java',
      '框架技术：Vue.js、React、Node.js'
    ]
  },
  {
    id: 'language',
    type: 'language',
    name: '语言能力',
    icon: 'globe',
    order: 6,
    enabled: true,
    required: false,
    description: '掌握的语言及熟练程度',
    tips: '诚实填写语言水平，可标注相关证书',
    examples: [
      '英语：CET-6，流利的听说读写',
      '日语：N2水平，商务交流无障碍'
    ]
  },
  {
    id: 'award',
    type: 'award',
    name: '获奖荣誉',
    icon: 'trophy',
    order: 7,
    enabled: true,
    required: false,
    description: '获得的奖项和荣誉',
    tips: '按重要性和时间排序，突出与职位相关的奖项',
    examples: [
      '2023年度优秀员工',
      '全国大学生数学建模竞赛一等奖'
    ]
  },
  {
    id: 'hobbies',
    type: 'hobbies',
    name: '兴趣爱好',
    icon: 'heart',
    order: 8,
    enabled: true,
    required: false,
    description: '个人兴趣爱好和特长',
    tips: '展示个人特色，可体现团队协作或领导能力',
    examples: [
      '摄影、旅行、阅读',
      '篮球队队长，组织团队活动'
    ]
  },
  {
    id: 'self_evaluation',
    type: 'self_evaluation',
    name: '自我评价',
    icon: 'message-circle',
    order: 9,
    enabled: true,
    required: false,
    description: '个人特点和优势总结',
    tips: '简洁地描述您的个人特点和职业优势',
    examples: [
      '突出与目标职位相关的优势',
      '展示学习能力和适应性',
      '体现团队协作和沟通能力',
      '保持真实，避免过度夸大',
      '控制在100-200字左右'
    ]
  },
  
  // 默认可选模块
  {
    id: 'internship',
    type: 'internship',
    name: '实习经历',
    icon: 'briefcase',
    order: 10,
    enabled: false,
    required: false,
    description: '实习经验和学习成果',
    tips: '重点描述实习期间的工作内容和收获',
    examples: [
      '在阿里巴巴实习3个月，参与电商平台开发',
      '负责前端页面优化，提升用户体验'
    ]
  },
  {
    id: 'training',
    type: 'training',
    name: '培训经历',
    icon: 'book',
    order: 11,
    enabled: false,
    required: false,
    description: '参加的培训课程和学习经历',
    tips: '列出与职位相关的培训经历和技能提升',
    examples: [
      'AWS云计算架构师培训',
      '敏捷开发Scrum Master认证'
    ]
  },
  {
    id: 'research_experience',
    type: 'research_experience',
    name: '研究经历',
    icon: 'search',
    order: 12,
    enabled: false,
    required: false,
    description: '参与的研究项目和学术活动',
    tips: '突出研究成果和学术贡献',
    examples: [
      '参与导师的机器学习项目研究',
      '发表SCI论文2篇'
    ]
  },
  {
    id: 'publication',
    type: 'publication',
    name: '论文专利',
    icon: 'file-text',
    order: 13,
    enabled: false,
    required: false,
    description: '发表的论文和申请的专利',
    tips: '按影响因子和重要性排序',
    examples: [
      'IEEE论文：基于深度学习的图像识别算法',
      '发明专利：一种新型数据压缩方法'
    ]
  },
  {
    id: 'certificate',
    type: 'certificate',
    name: '证书资质',
    icon: 'award',
    order: 14,
    enabled: false,
    required: false,
    description: '获得的专业证书和资质',
    tips: '列出与目标职位相关的证书',
    examples: [
      'PMP项目管理专业人士认证',
      'AWS解决方案架构师认证'
    ]
  },
  {
    id: 'volunteer_experience',
    type: 'volunteer_experience',
    name: '志愿服务',
    icon: 'users',
    order: 15,
    enabled: false,
    required: false,
    description: '参与的志愿服务活动',
    tips: '体现社会责任感和奉献精神',
    examples: [
      '支教志愿者，为山区儿童提供编程教育',
      '社区志愿服务，组织老年人智能手机培训'
    ]
  },
  {
    id: 'portfolio',
    type: 'portfolio',
    name: '作品集',
    icon: 'images',
    order: 16,
    enabled: false,
    required: false,
    description: '个人作品和项目展示',
    tips: '展示最佳作品，提供在线链接',
    examples: [
      '个人博客：https://myblog.com',
      'GitHub项目：https://github.com/username'
    ]
  },
  {
    id: 'cover_letter',
    type: 'cover_letter',
    name: '自荐信',
    icon: 'mail',
    order: 17,
    enabled: false,
    required: false,
    description: '求职自荐信和求职意向',
    tips: '撰写针对性的求职自荐信',
    examples: [
      '开头表达对公司和职位的兴趣',
      '突出与职位匹配的经验和技能',
      '展示对公司的了解和认同',
      '表达加入团队的热情和期待',
      '保持简洁，控制在300-500字'
    ]
  }
]

/**
 * 默认简历设置
 */
const DEFAULT_SETTINGS = {
  theme: {
    primaryColor: '#3B82F6',
    fontFamily: 'system-ui',
    fontSize: '14px',
    lineHeight: '1.6'
  },
  layout: {
    marginTop: '20px',
    marginBottom: '20px',
    marginLeft: '20px',
    marginRight: '20px',
    spacing: '16px'
  },
  typography: {
    headingSize: '18px',
    headingWeight: '600',
    bodySize: '14px',
    bodyWeight: '400'
  }
}

// ================================
// 全局状态
// ================================
let globalStore = null

/**
 * 编辑器状态管理Hook
 * @returns {Object} 编辑器状态和方法
 */
export const useEditorStore = () => {
  // 无论服务端还是客户端都使用单例
  if (!globalStore) {
    console.log('创建新的全局 store 实例')
    globalStore = createEditorStore()
  } else {
    console.log('使用已存在的全局 store 实例')
  }
  
  return globalStore
}

/**
 * 创建编辑器Store实例
 * @description 创建完整的编辑器状态管理实例
 * @returns {Object} 编辑器状态和方法
 */
function createEditorStore() {
  // ================================
  // 响应式状态
  // ================================
  
  /**
   * 当前简历数据
   * @description 用户正在编辑的简历数据，初始为空对象，由模板加载时填充
   */
  const currentResumeData = ref({
    modules: {},
    settings: { ...DEFAULT_SETTINGS },
    title: '我的简历'
  })
  
  /**
   * 当前模板信息
   * @description 当前使用的模板信息
   */
  const currentTemplate = ref(null)
  
  /**
   * 模块配置
   * @description 模块的配置信息（不包含数据）
   */
  const moduleConfigs = ref([...DEFAULT_MODULE_CONFIGS])
  
  /**
   * 自定义模块列表
   * @description 用户创建的自定义模块
   */
  const customModules = ref([])
  
  /**
   * 当前选中的模块ID
   */
  const currentModuleId = ref('basic_info')
  
  /**
   * 编辑器设置
   */
  const settings = ref({ ...DEFAULT_SETTINGS })
  
  /**
   * 简历标题
   */
  const title = ref('我的简历')
  
  /**
   * 编辑状态
   */
  const isDirty = ref(false)
  const isLoading = ref(false)
  const error = ref(null)
  
  /**
   * 历史记录管理
   */
  const { canUndo, canRedo, saveState, undo: undoAction, redo: redoAction } = useUndoRedo()
  
  /**
   * 简历数据服务
   */
  const { saveResume: saveResumeData, fetchResume, loading: dataLoading } = useResumeData()

  // ================================
  // 计算属性
  // ================================
  
  /**
   * 是否有简历数据
   */
  const hasResumeData = computed(() => {
    return currentResumeData.value && Object.keys(currentResumeData.value.modules || {}).length > 0
  })
  
  /**
   * 启用的模块列表
   */
  const enabledModules = computed(() => {
    const allEnabledModules = [
      ...moduleConfigs.value.filter(module => module.enabled),
      ...customModules.value.filter(module => module.enabled)
    ]
    const sortedModules = allEnabledModules.sort((a, b) => (a.order || 0) - (b.order || 0))
    console.log('🔄 enabledModules 计算属性更新:', sortedModules.map(m => `${m.id}(${m.order})`))
    return sortedModules
  })
  
  /**
   * 当前模块信息
   */
  const currentModule = computed(() => {
    return moduleConfigs.value.find(module => module.id === currentModuleId.value)
  })
  
  /**
   * 整体加载状态
   */
  const overallLoading = computed(() => {
    return isLoading.value || dataLoading.value
  })

  // ================================
  // 核心方法
  // ================================

  /**
   * 加载简历数据
   * @description 从后端加载简历数据，同时加载模板和内容
   * @param {string} resumeId 简历ID
   * @param {string} templateId 模板ID（可选）
   * @returns {Promise<void>}
   */
  const loadResumeData = async (resumeId, templateId = null) => {
    try {
      isLoading.value = true
      error.value = null
      
      // 加载简历数据
      const resumeData = await fetchResume(resumeId)
      
      if (resumeData) {
        currentResumeData.value = resumeData
        title.value = resumeData.title || '我的简历'
        
        // 如果有模板信息，更新当前模板
        if (resumeData.templateId) {
          currentTemplate.value = {
            id: resumeData.templateId,
            // 其他模板信息可以从resumeData中获取
          }
        }
        
        // 保存状态到历史记录
        saveState({
          resumeData: resumeData,
          title: title.value,
          settings: settings.value
        })
        
        console.log('简历数据加载成功:', resumeId)
      }
      
    } catch (err) {
      console.error('加载简历数据失败:', err)
      error.value = err.message || '加载简历数据失败'
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 设置简历数据
   * @description 直接设置简历数据（用于模板加载后的数据填充）
   * @param {Object} resumeData 简历数据
   * @param {Object} templateInfo 模板信息
   */
  const setResumeData = (resumeData, templateInfo = null) => {
    try {
      currentResumeData.value = resumeData
      
      if (templateInfo) {
        currentTemplate.value = templateInfo
      }
      
      // 如果有标题，更新标题
      if (resumeData.title) {
        title.value = resumeData.title
      }
      
      // 保存状态到历史记录
      saveState({
        resumeData: resumeData,
        title: title.value,
        settings: settings.value
      })
      
      console.log('简历数据设置成功')
      
    } catch (err) {
      console.error('设置简历数据失败:', err)
      error.value = err.message || '设置简历数据失败'
    }
  }

  /**
   * 创建新简历
   * @description 创建一个新的简历，数据为空，等待用户填写或模板加载
   * @param {Object} options 创建选项
   * @returns {Promise<void>}
   */
  const createNewResume = async (options = {}) => {
    try {
      isLoading.value = true
      error.value = null
      
      // 创建基础的简历数据结构（空数据）
      const newResumeData = {
        id: generateId(),
        title: options.title || '我的简历',
        templateId: options.templateId || null,
        modules: {},
        settings: { ...DEFAULT_SETTINGS },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      currentResumeData.value = newResumeData
      title.value = newResumeData.title
      
      // 如果指定了模板，设置模板信息
      if (options.templateId) {
        currentTemplate.value = {
          id: options.templateId,
          ...options.templateInfo
        }
      }
      
      // 保存状态到历史记录
      saveState({
        resumeData: newResumeData,
        title: title.value,
        settings: settings.value
      })
      
      console.log('新简历创建成功:', newResumeData.id)
      
    } catch (err) {
      console.error('创建新简历失败:', err)
      error.value = err.message || '创建新简历失败'
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 保存简历
   * @description 保存当前简历数据到后端
   * @returns {Promise<boolean>} 是否保存成功
   */
  const saveResume = async () => {
    if (!currentResumeData.value || !currentResumeData.value.id) {
      console.warn('没有可保存的简历数据或简历ID')
      return false
    }
    
    try {
      isLoading.value = true
      error.value = null
      
      // 更新最后修改时间
      currentResumeData.value.updatedAt = new Date().toISOString()
      
      // 调用数据服务保存
      const result = await saveResumeData(currentResumeData.value)
      
      if (result.success) {
        isDirty.value = false
        console.log('简历保存成功:', result.id)
        return true
      } else {
        throw new Error(result.message || '保存失败')
      }
      
    } catch (err) {
      console.error('保存简历失败:', err)
      error.value = err.message || '保存简历失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  // ================================
  // 模块操作方法
  // ================================

  /**
   * 设置当前模块
   * @param {string} moduleId 模块ID
   */
  const setCurrentModule = (moduleId) => {
    // 检查普通模块
    if (moduleConfigs.value.find(m => m.id === moduleId)) {
      currentModuleId.value = moduleId
      return
    }
    
    // 检查自定义模块
    if (customModules.value.find(m => m.id === moduleId)) {
      currentModuleId.value = moduleId
      return
    }
    
    console.warn('setCurrentModule - 未找到模块:', moduleId)
  }

  /**
   * 获取模块数据
   * @param {string} moduleId 模块ID
   * @returns {any} 模块数据
   */
  const getModuleData = (moduleId) => {
    if (!currentResumeData.value?.modules) {
      return null
    }
    return currentResumeData.value.modules[moduleId]
  }

  /**
   * 更新模块数据
   * @param {string} moduleId 模块ID
   * @param {any} data 新的数据
   */
  const updateModuleData = (moduleId, data) => {
    console.log('🔄 updateModuleData 被调用:', moduleId, data)
    
    if (!currentResumeData.value?.modules) {
      currentResumeData.value.modules = {}
    }
    
    // 确保触发响应式更新 - 使用对象替换而不是直接修改
    const oldData = currentResumeData.value.modules[moduleId]
    
    // 创建新的modules对象以确保响应式更新
    const newModules = { ...currentResumeData.value.modules }
    newModules[moduleId] = data
    
    // 替换整个currentResumeData以确保响应式更新
    currentResumeData.value = {
      ...currentResumeData.value,
      modules: newModules
    }
    
    console.log('📊 模块数据更新:', {
      moduleId,
      oldData,
      newData: data,
      allModules: Object.keys(currentResumeData.value.modules)
    })
    
    markAsDirty()
    
    // 保存状态到历史记录
    saveState({
      resumeData: currentResumeData.value,
      title: title.value,
      settings: settings.value
    })
  }

  /**
   * 删除模块
   * @param {string} moduleId 模块ID
   */
  const deleteModule = (moduleId) => {
    // 不能删除必需的模块
    const moduleConfig = moduleConfigs.value.find(m => m.id === moduleId)
    if (moduleConfig && moduleConfig.required) {
      console.warn('不能删除必需的模块:', moduleId)
      return
    }
    
    // 从配置中禁用模块
    const module = moduleConfigs.value.find(m => m.id === moduleId)
    if (module) {
      module.enabled = false
    }
    
    // 从数据中删除模块数据
    if (currentResumeData.value?.modules) {
      delete currentResumeData.value.modules[moduleId]
    }
    
    // 如果删除的是当前模块，切换到基本信息
    if (currentModuleId.value === moduleId) {
      currentModuleId.value = 'basic_info'
    }
    
    markAsDirty()
  }

  /**
   * 移动模块
   * @param {string} moduleType 模块类型
   * @param {number} direction 移动方向 (-1: 上移, 1: 下移)
   */
  const moveModule = (moduleType, direction) => {
    console.log(`🔄 开始移动模块: ${moduleType}, 方向: ${direction > 0 ? '下移' : '上移'}`)
    
    // 基本信息模块标识
    const BASIC_MODULE_IDS = ['basic', 'basic_info', 'basicInfo']
    
    // 检查是否尝试移动基本信息模块
    if (BASIC_MODULE_IDS.includes(moduleType)) {
      console.warn('⚠️ 基本信息模块不允许移动，始终保持在第一位')
      return
    }
    
    // 获取所有启用的模块（包括自定义模块）
    const allEnabledModules = [
      ...moduleConfigs.value.filter(m => m.enabled),
      ...customModules.value.filter(m => m.enabled)
    ]
    
    // 按order排序
    allEnabledModules.sort((a, b) => (a.order || 0) - (b.order || 0))
    
    console.log('📋 当前模块顺序:', allEnabledModules.map(m => `${m.id}(${m.order})`))
    
    // 找到当前模块的索引
    const currentIndex = allEnabledModules.findIndex(m => m.type === moduleType || m.id === moduleType)
    
    if (currentIndex === -1) {
      console.warn('未找到模块:', moduleType)
      return
    }
    
    // 计算目标索引
    const targetIndex = currentIndex + direction
    
    // 检查边界
    if (targetIndex < 0 || targetIndex >= allEnabledModules.length) {
      console.warn('模块已到达边界，无法移动')
      return
    }
    
    // 获取目标模块
    const targetModule = allEnabledModules[targetIndex]
    
    // 检查目标模块是否是基本信息模块
    if (BASIC_MODULE_IDS.includes(targetModule.id) || BASIC_MODULE_IDS.includes(targetModule.type)) {
      console.warn('⚠️ 不能移动到基本信息模块的位置，基本信息模块必须保持在第一位')
      return
    }
    
    // 获取当前模块
    const currentModule = allEnabledModules[currentIndex]
    
    console.log(`🔄 交换模块: ${currentModule.id}(${currentModule.order}) ↔ ${targetModule.id}(${targetModule.order})`)
    
    // 交换order值
    const tempOrder = currentModule.order
    currentModule.order = targetModule.order
    targetModule.order = tempOrder
    
    // 强制触发响应式更新
    // 直接更新原始数组中的对象
    const configIndex = moduleConfigs.value.findIndex(m => m.id === currentModule.id)
    if (configIndex !== -1) {
      moduleConfigs.value[configIndex].order = currentModule.order
    }
    const configIndex2 = moduleConfigs.value.findIndex(m => m.id === targetModule.id)
    if (configIndex2 !== -1) {
      moduleConfigs.value[configIndex2].order = targetModule.order
    }
    
    const customIndex = customModules.value.findIndex(m => m.id === currentModule.id)
    if (customIndex !== -1) {
      customModules.value[customIndex].order = currentModule.order
    }
    const customIndex2 = customModules.value.findIndex(m => m.id === targetModule.id)
    if (customIndex2 !== -1) {
      customModules.value[customIndex2].order = targetModule.order
    }
    
    // 强制触发响应式更新
    moduleConfigs.value = [...moduleConfigs.value]
    customModules.value = [...customModules.value]
    
    // 标记为已修改
    markAsDirty()
    
    console.log(`✅ 模块 ${moduleType} ${direction > 0 ? '下移' : '上移'} 成功`)
    console.log('📋 移动后模块顺序:', [
      ...moduleConfigs.value.filter(m => m.enabled),
      ...customModules.value.filter(m => m.enabled)
    ].sort((a, b) => (a.order || 0) - (b.order || 0)).map(m => `${m.id}(${m.order})`))
  }

  /**
   * 创建自定义模块
   * @param {string} name 模块名称
   * @returns {string} 模块ID
   */
  const createCustomModule = (name) => {
    if (customModules.value.length >= 3) {
      throw new Error('最多只能创建3个自定义模块')
    }
    
    const customId = `custom_${Date.now()}`
    const newModule = {
      id: customId,
      type: 'custom',
      name: name,
      icon: 'folder',
      order: 50 + customModules.value.length,
      enabled: false,
      required: false,
      description: '自定义模块',
      tips: '记录您的特殊经历或成就',
      examples: [
        '填写名称和担任的角色',
        '记录起始时间和详细内容'
      ]
    }
    
    customModules.value.push(newModule)
    markAsDirty()
    
    return customId
  }

  /**
   * 切换自定义模块启用状态
   * @param {string} moduleId 模块ID
   */
  const toggleCustomModule = (moduleId) => {
    const module = customModules.value.find(m => m.id === moduleId)
    if (module) {
      module.enabled = !module.enabled
      
      // 如果是启用模块，设置到最后位置
      if (module.enabled) {
        // 获取所有已启用模块的最大order值
        const allEnabledModules = [
          ...moduleConfigs.value.filter(m => m.enabled),
          ...customModules.value.filter(m => m.enabled)
        ]
        const maxOrder = allEnabledModules.length > 0 
          ? Math.max(...allEnabledModules.map(m => m.order || 0))
          : 0
        module.order = maxOrder + 1
      }
      
      markAsDirty()
    }
  }

  /**
   * 删除自定义模块
   * @param {string} moduleId 模块ID
   */
  const deleteCustomModule = (moduleId) => {
    const index = customModules.value.findIndex(m => m.id === moduleId)
    if (index !== -1) {
      customModules.value.splice(index, 1)
      
      // 从数据中删除模块数据
      if (currentResumeData.value?.modules) {
        delete currentResumeData.value.modules[moduleId]
      }
      
      // 如果删除的是当前模块，切换到基本信息
      if (currentModuleId.value === moduleId) {
        currentModuleId.value = 'basic_info'
      }
      
      markAsDirty()
    }
  }

  /**
   * 更新设置
   * @param {Object} newSettings 新的设置
   */
  const updateSettings = (newSettings) => {
    settings.value = { ...settings.value, ...newSettings }
    
    if (currentResumeData.value) {
      currentResumeData.value.settings = settings.value
    }
    
    markAsDirty()
  }

  /**
   * 更新标题
   * @param {string} newTitle 新标题
   */
  const updateTitle = (newTitle) => {
    title.value = newTitle
    
    if (currentResumeData.value) {
      currentResumeData.value.title = newTitle
    }
    
    markAsDirty()
  }

  // ================================
  // 历史记录方法
  // ================================

  /**
   * 撤销操作
   */
  const undo = () => {
    const state = undoAction()
    if (state) {
      currentResumeData.value = state.resumeData
      title.value = state.title
      settings.value = state.settings
    }
  }

  /**
   * 重做操作
   */
  const redo = () => {
    const state = redoAction()
    if (state) {
      currentResumeData.value = state.resumeData
      title.value = state.title
      settings.value = state.settings
    }
  }

  // ================================
  // 工具方法
  // ================================

  /**
   * 标记为已修改
   */
  const markAsDirty = () => {
    isDirty.value = true
  }

  /**
   * 清除错误
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 重置状态
   */
  const reset = () => {
    currentResumeData.value = {
      modules: {},
      settings: { ...DEFAULT_SETTINGS },
      title: '我的简历'
    }
    currentTemplate.value = null
    currentModuleId.value = 'basic_info'
    title.value = '我的简历'
    isDirty.value = false
    isLoading.value = false
    error.value = null
  }

  // ================================
  // 返回状态和方法
  // ================================
  return {
    // 状态 - 兼容性别名
    currentResumeData,
    resumeData: currentResumeData,  // 添加别名以保持向后兼容
    currentTemplate,
    moduleConfigs,
    customModules,
    currentModuleId,
    settings,
    title,
    isDirty,
    isLoading: overallLoading,
    error,
    
    // 计算属性
    hasResumeData,
    enabledModules,
    currentModule,
    
    // 历史记录
    canUndo,
    canRedo,
    
    // 核心方法
    loadResumeData,
    setResumeData,
    createNewResume,
    saveResume,
    
    // 模块操作
    setCurrentModule,
    getModuleData,
    updateModuleData,
    deleteModule,
    moveModule,
    createCustomModule,
    toggleCustomModule,
    deleteCustomModule,
    
    // 设置和标题
    updateSettings,
    updateTitle,
    
    // 历史记录
    undo,
    redo,
    
    // 工具方法
    markAsDirty,
    clearError,
    reset
  }
}

// ================================
// 工具函数
// ================================

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
} 