package com.alan6.resume.dto.template;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.ArrayList;

/**
 * HTML模板验证结果DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HtmlValidationResult {
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 验证是否通过
     */
    private Boolean valid;
    
    /**
     * 验证错误信息列表
     */
    @Builder.Default
    private List<ValidationError> errors = new ArrayList<>();
    
    /**
     * 验证警告信息列表
     */
    @Builder.Default
    private List<ValidationWarning> warnings = new ArrayList<>();
    
    /**
     * 验证统计信息
     */
    private ValidationStatistics statistics;
    
    /**
     * 验证错误信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ValidationError {
        
        /**
         * 错误类型
         */
        private String type;
        
        /**
         * 错误代码
         */
        private String code;
        
        /**
         * 错误消息
         */
        private String message;
        
        /**
         * 错误位置（行号）
         */
        private Integer line;
        
        /**
         * 错误元素
         */
        private String element;
        
        /**
         * 修复建议
         */
        private String suggestion;
        
        /**
         * 错误级别（ERROR, WARNING, INFO）
         */
        private String level;
    }
    
    /**
     * 验证警告信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ValidationWarning {
        
        /**
         * 警告类型
         */
        private String type;
        
        /**
         * 警告代码
         */
        private String code;
        
        /**
         * 警告消息
         */
        private String message;
        
        /**
         * 警告位置（行号）
         */
        private Integer line;
        
        /**
         * 警告元素
         */
        private String element;
        
        /**
         * 优化建议
         */
        private String suggestion;
    }
    
    /**
     * 验证统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ValidationStatistics {
        
        /**
         * 总模块数量
         */
        private Integer totalModules;
        
        /**
         * 有效模块数量
         */
        private Integer validModules;
        
        /**
         * 总字段数量
         */
        private Integer totalFields;
        
        /**
         * 有效字段数量
         */
        private Integer validFields;
        
        /**
         * 错误数量
         */
        private Integer errorCount;
        
        /**
         * 警告数量
         */
        private Integer warningCount;
        
        /**
         * 检测到的模块列表
         */
        private List<String> detectedModules;
        
        /**
         * 检测到的字段列表
         */
        private List<String> detectedFields;
    }
    
    /**
     * 添加错误信息
     * 
     * @param error 错误信息
     */
    public void addError(ValidationError error) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        this.errors.add(error);
    }
    
    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(ValidationWarning warning) {
        if (this.warnings == null) {
            this.warnings = new ArrayList<>();
        }
        this.warnings.add(warning);
    }
    
    /**
     * 创建错误信息
     * 
     * @param type 错误类型
     * @param code 错误代码
     * @param message 错误消息
     * @param line 错误行号
     * @param element 错误元素
     * @param suggestion 修复建议
     * @return 错误信息对象
     */
    public static ValidationError createError(String type, String code, String message, 
                                            Integer line, String element, String suggestion) {
        return ValidationError.builder()
                .type(type)
                .code(code)
                .message(message)
                .line(line)
                .element(element)
                .suggestion(suggestion)
                .level("ERROR")
                .build();
    }
    
    /**
     * 创建警告信息
     * 
     * @param type 警告类型
     * @param code 警告代码
     * @param message 警告消息
     * @param line 警告行号
     * @param element 警告元素
     * @param suggestion 优化建议
     * @return 警告信息对象
     */
    public static ValidationWarning createWarning(String type, String code, String message, 
                                                Integer line, String element, String suggestion) {
        return ValidationWarning.builder()
                .type(type)
                .code(code)
                .message(message)
                .line(line)
                .element(element)
                .suggestion(suggestion)
                .build();
    }
    
    /**
     * 判断是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * 判断是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
    
    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
    
    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warnings != null ? warnings.size() : 0;
    }
} 