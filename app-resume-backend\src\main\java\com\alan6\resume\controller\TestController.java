package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.entity.Roles;
import com.alan6.resume.entity.Permissions;
import com.alan6.resume.entity.Users;
import com.alan6.resume.entity.UserRoles;
import com.alan6.resume.entity.RolePermissions;
import com.alan6.resume.service.IRolesService;
import com.alan6.resume.service.IPermissionsService;
import com.alan6.resume.service.IUsersService;
import com.alan6.resume.service.IUserRolesService;
import com.alan6.resume.service.IRolePermissionsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于开发和测试环境的数据初始化
 */
@Slf4j
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
@Tag(name = "测试接口", description = "开发测试用接口")
public class TestController {

    private final IRolesService rolesService;
    private final IPermissionsService permissionsService;
    private final IUsersService usersService;
    private final IUserRolesService userRolesService;
    private final IRolePermissionsService rolePermissionsService;

    /**
     * 初始化测试数据
     */
    @PostMapping("/init-data")
    @Operation(summary = "初始化测试数据", description = "创建测试用户和角色权限数据")
    public R<Map<String, Object>> initTestData() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 1. 创建角色
            createRoles();
            result.put("roles", "角色数据创建完成");
            
            // 2. 创建权限
            createPermissions();
            result.put("permissions", "权限数据创建完成");
            
            // 3. 创建测试用户
            createTestUsers();
            result.put("users", "测试用户创建完成");
            
            // 4. 分配角色
            assignRolesToUsers();
            result.put("userRoles", "用户角色分配完成");
            
            // 5. 分配权限给角色
            assignPermissionsToRoles();
            result.put("rolePermissions", "角色权限分配完成");
            
            log.info("测试数据初始化完成");
            return R.ok(result, "测试数据初始化成功");
            
        } catch (Exception e) {
            log.error("测试数据初始化失败", e);
            return R.fail("测试数据初始化失败: " + e.getMessage());
        }
    }

    /**
     * 创建角色
     */
    private void createRoles() {
        // 超级管理员
        createRoleIfNotExists(1L, "超级管理员", "SUPER_ADMIN", "系统超级管理员，拥有所有权限", 1);
        // 管理员
        createRoleIfNotExists(2L, "管理员", "ADMIN", "系统管理员，拥有大部分管理权限", 2);
        // 普通用户
        createRoleIfNotExists(3L, "普通用户", "USER", "普通用户，只能使用基础功能", 3);
    }

    /**
     * 创建权限
     */
    private void createPermissions() {
        createPermissionIfNotExists(1L, "后台管理", "admin", "menu", "/admin", 0L, 1);
        createPermissionIfNotExists(2L, "用户管理", "admin:user", "menu", "/admin/users", 1L, 2);
        createPermissionIfNotExists(3L, "模板管理", "admin:template", "menu", "/admin/templates", 1L, 3);
        createPermissionIfNotExists(4L, "订单管理", "admin:order", "menu", "/admin/orders", 1L, 4);
        createPermissionIfNotExists(5L, "系统设置", "admin:system", "menu", "/admin/system", 1L, 5);
    }

    /**
     * 创建测试用户
     */
    private void createTestUsers() {
        // 管理员用户
        createUserIfNotExists(1001L, "admin", "13800138001", "123456", "系统管理员");
        // 超级管理员用户
        createUserIfNotExists(1002L, "superadmin", "13800138002", "123456", "超级管理员");
    }

    /**
     * 分配角色给用户
     */
    private void assignRolesToUsers() {
        try {
            // 根据手机号查找用户并分配角色
            LambdaQueryWrapper<Users> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(Users::getPhone, "13800138001");
            Users adminUser = usersService.getOne(queryWrapper1);
            if (adminUser != null) {
                log.info("找到管理员用户: ID={}, 手机号={}", adminUser.getId(), adminUser.getPhone());
                boolean result1 = userRolesService.assignRolesToUser(adminUser.getId(), Arrays.asList(2L));
                log.info("给用户 {} 分配管理员角色: {}", adminUser.getNickname(), result1 ? "成功" : "失败");
            } else {
                log.warn("未找到手机号为 13800138001 的用户");
            }
            
            LambdaQueryWrapper<Users> queryWrapper2 = new LambdaQueryWrapper<>();
            queryWrapper2.eq(Users::getPhone, "13800138002");
            Users superAdminUser = usersService.getOne(queryWrapper2);
            if (superAdminUser != null) {
                log.info("找到超级管理员用户: ID={}, 手机号={}", superAdminUser.getId(), superAdminUser.getPhone());
                boolean result2 = userRolesService.assignRolesToUser(superAdminUser.getId(), Arrays.asList(1L));
                log.info("给用户 {} 分配超级管理员角色: {}", superAdminUser.getNickname(), result2 ? "成功" : "失败");
            } else {
                log.warn("未找到手机号为 13800138002 的用户");
            }
        } catch (Exception e) {
            log.error("分配角色给用户时发生异常", e);
            throw e;
        }
    }

    /**
     * 分配权限给角色
     */
    private void assignPermissionsToRoles() {
        // 超级管理员拥有所有权限
        rolePermissionsService.assignPermissionsToRole(1L, Arrays.asList(1L, 2L, 3L, 4L, 5L));
        
        // 管理员拥有除系统设置外的权限
        rolePermissionsService.assignPermissionsToRole(2L, Arrays.asList(1L, 2L, 3L, 4L));
        
        log.info("角色权限分配完成");
    }

    /**
     * 创建角色（如果不存在）
     */
    private void createRoleIfNotExists(Long id, String roleName, String roleCode, String description, Integer sortOrder) {
        LambdaQueryWrapper<Roles> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Roles::getRoleCode, roleCode);
        
        Roles existingRole = rolesService.getOne(queryWrapper);
        if (existingRole == null) {
            Roles role = new Roles();
            role.setId(id);
            role.setRoleName(roleName);
            role.setRoleCode(roleCode);
            role.setDescription(description);
            role.setSortOrder(sortOrder);
            role.setStatus((byte) 1);
            role.setCreateTime(LocalDateTime.now());
            role.setUpdateTime(LocalDateTime.now());
            
            rolesService.save(role);
            log.info("创建角色: {}", roleName);
        } else {
            log.info("角色已存在: {}", roleName);
        }
    }

    /**
     * 创建权限（如果不存在）
     */
    private void createPermissionIfNotExists(Long id, String permissionName, String permissionCode, 
                                           String resourceType, String resourceUrl, Long parentId, Integer sortOrder) {
        LambdaQueryWrapper<Permissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Permissions::getPermissionCode, permissionCode);
        
        Permissions existingPermission = permissionsService.getOne(queryWrapper);
        if (existingPermission == null) {
            Permissions permission = new Permissions();
            permission.setId(id);
            permission.setPermissionName(permissionName);
            permission.setPermissionCode(permissionCode);
            permission.setResourceType(resourceType);
            permission.setResourceUrl(resourceUrl);
            permission.setParentId(parentId);
            permission.setSortOrder(sortOrder);
            permission.setStatus((byte) 1);
            permission.setCreateTime(LocalDateTime.now());
            permission.setUpdateTime(LocalDateTime.now());
            
            permissionsService.save(permission);
            log.info("创建权限: {}", permissionName);
        } else {
            log.info("权限已存在: {}", permissionName);
        }
    }

    /**
     * 创建用户（如果不存在）
     */
    private void createUserIfNotExists(Long id, String username, String phone, String password, String nickname) {
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getPhone, phone);
        
        Users existingUser = usersService.getOne(queryWrapper);
        if (existingUser == null) {
            Users user = new Users();
            user.setId(id);
            user.setUsername(username);
            user.setPhone(phone);
            user.setPassword(password); // 注意：实际应用中需要加密
            user.setNickname(nickname);
            user.setStatus((byte) 1);
            user.setRegisterPlatform("web");
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            usersService.save(user);
            log.info("创建用户: {} ({})", nickname, phone);
        } else {
            log.info("用户已存在: {} ({})", nickname, phone);
        }
    }

    /**
     * 测试密码登录
     */
    @PostMapping("/test-password-login")
    public R<String> testPasswordLogin(@RequestBody Map<String, String> request) {
        try {
            String phone = request.get("phone");
            String password = request.get("password");
            
            log.info("测试密码登录: phone={}", phone);
            
            // 查找用户
            LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Users::getPhone, phone);
            Users user = usersService.getOne(queryWrapper);
            
            if (user == null) {
                return R.fail("用户不存在");
            }
            
            log.info("找到用户: ID={}, 手机号={}, 密码长度={}", user.getId(), user.getPhone(), 
                    user.getPassword() != null ? user.getPassword().length() : 0);
            
            // 检查密码
            if (user.getPassword() == null) {
                return R.fail("用户密码为空");
            }
            
            // 简单测试：检查明文密码匹配
            if (password.equals(user.getPassword())) {
                return R.ok("明文密码匹配成功");
            }
            
            // 测试BCrypt密码匹配
            try {
                PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
                if (passwordEncoder.matches(password, user.getPassword())) {
                    return R.ok("BCrypt密码匹配成功");
                } else {
                    return R.fail("BCrypt密码不匹配");
                }
            } catch (Exception e) {
                return R.fail("BCrypt验证异常: " + e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("测试密码登录异常", e);
            return R.fail("测试异常: " + e.getMessage());
        }
    }
} 