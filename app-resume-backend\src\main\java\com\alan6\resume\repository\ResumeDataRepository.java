package com.alan6.resume.repository;

import com.alan6.resume.document.ResumeData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.List;

/**
 * 简历数据MongoDB仓库接口
 * 
 * @description 定义简历数据在MongoDB中的数据访问方法
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface ResumeDataRepository extends MongoRepository<ResumeData, String> {

    /**
     * 根据简历ID查询
     * 
     * @param resumeId 简历ID
     * @return 简历数据
     */
    Optional<ResumeData> findByResumeId(String resumeId);

    /**
     * 根据用户ID查询简历列表
     * 
     * @param userId 用户ID
     * @return 简历数据列表
     */
    List<ResumeData> findByUserId(Long userId);

    /**
     * 根据用户ID和状态查询
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 简历数据列表
     */
    List<ResumeData> findByUserIdAndStatus(Long userId, Integer status);

    /**
     * 根据模板ID查询使用该模板的简历数量
     * 
     * @param templateId 模板ID
     * @return 使用数量
     */
    long countByTemplateId(String templateId);

    /**
     * 删除指定简历ID的数据
     * 
     * @param resumeId 简历ID
     * @return 删除的数量
     */
    long deleteByResumeId(String resumeId);

    /**
     * 查询指定用户最近保存的简历
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 简历数据列表
     */
    @Query(value = "{'userId': ?0}", sort = "{'lastSaved': -1}")
    List<ResumeData> findRecentByUserId(Long userId, int limit);
} 