package com.alan6.resume.mapper;

import com.alan6.resume.entity.ResumeTemplateContent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 简历模板内容数据访问层
 * 
 * @description 提供简历模板内容的数据库操作接口
 *              支持按行业、职位、语言等维度查询内容
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface ResumeTemplateContentMapper extends BaseMapper<ResumeTemplateContent> {

    /**
     * 根据内容代码查询内容
     * 
     * @description 通过唯一的内容代码查询特定的模板内容
     * @param code 内容代码
     * @return 匹配的模板内容，如果不存在则返回null
     */
    @Select("SELECT * FROM resume_template_content WHERE code = #{code} AND status = 1")
    ResumeTemplateContent selectByCode(@Param("code") String code);

    /**
     * 根据行业查询可用内容列表
     * 
     * @description 查询指定行业的所有启用状态的模板内容
     * @param industry 行业名称
     * @return 匹配的内容列表
     */
    @Select("SELECT * FROM resume_template_content WHERE industry = #{industry} AND status = 1 ORDER BY create_time DESC")
    List<ResumeTemplateContent> selectByIndustry(@Param("industry") String industry);

    /**
     * 根据职位查询可用内容列表
     * 
     * @description 查询指定职位的所有启用状态的模板内容
     * @param position 职位名称
     * @return 匹配的内容列表
     */
    @Select("SELECT * FROM resume_template_content WHERE position = #{position} AND status = 1 ORDER BY create_time DESC")
    List<ResumeTemplateContent> selectByPosition(@Param("position") String position);

    /**
     * 根据行业和职位查询最匹配的内容
     * 
     * @description 优先查询同时匹配行业和职位的内容，如果没有则查询只匹配行业的内容
     * @param industry 行业名称
     * @param position 职位名称
     * @return 最匹配的内容列表
     */
    @Select("SELECT * FROM resume_template_content " +
            "WHERE status = 1 " +
            "AND (industry = #{industry} OR position = #{position}) " +
            "ORDER BY " +
            "CASE WHEN industry = #{industry} AND position = #{position} THEN 1 " +
            "     WHEN industry = #{industry} THEN 2 " +
            "     WHEN position = #{position} THEN 3 " +
            "     ELSE 4 END, " +
            "create_time DESC")
    List<ResumeTemplateContent> selectByIndustryAndPosition(@Param("industry") String industry, 
                                                           @Param("position") String position);

    /**
     * 根据语言查询内容列表
     * 
     * @description 查询指定语言的所有启用状态的模板内容
     * @param language 语言代码
     * @return 匹配的内容列表
     */
    @Select("SELECT * FROM resume_template_content WHERE language = #{language} AND status = 1 ORDER BY create_time DESC")
    List<ResumeTemplateContent> selectByLanguage(@Param("language") String language);

    /**
     * 查询所有启用状态的内容
     * 
     * @description 获取所有可用的模板内容，按创建时间倒序排列
     * @return 所有启用的内容列表
     */
    @Select("SELECT * FROM resume_template_content WHERE status = 1 ORDER BY create_time DESC")
    List<ResumeTemplateContent> selectAllEnabled();

    /**
     * 检查内容代码是否已存在
     * 
     * @description 用于验证内容代码的唯一性
     * @param code 内容代码
     * @return 如果存在返回true，否则返回false
     */
    @Select("SELECT COUNT(*) > 0 FROM resume_template_content WHERE code = #{code}")
    boolean existsByCode(@Param("code") String code);

    /**
     * 统计指定行业的内容数量
     * 
     * @description 统计某个行业下的内容总数
     * @param industry 行业名称
     * @return 内容数量
     */
    @Select("SELECT COUNT(*) FROM resume_template_content WHERE industry = #{industry} AND status = 1")
    int countByIndustry(@Param("industry") String industry);

    /**
     * 统计指定职位的内容数量
     * 
     * @description 统计某个职位下的内容总数
     * @param position 职位名称
     * @return 内容数量
     */
    @Select("SELECT COUNT(*) FROM resume_template_content WHERE position = #{position} AND status = 1")
    int countByPosition(@Param("position") String position);

    /**
     * 获取所有不同的行业列表
     * 
     * @description 获取系统中所有已配置的行业类型
     * @return 行业列表
     */
    @Select("SELECT DISTINCT industry FROM resume_template_content WHERE status = 1 AND industry IS NOT NULL ORDER BY industry")
    List<String> selectDistinctIndustries();

    /**
     * 获取所有不同的职位列表
     * 
     * @description 获取系统中所有已配置的职位类型
     * @return 职位列表
     */
    @Select("SELECT DISTINCT position FROM resume_template_content WHERE status = 1 AND position IS NOT NULL ORDER BY position")
    List<String> selectDistinctPositions();
} 