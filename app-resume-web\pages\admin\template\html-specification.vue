<template>
  <div class="html-specification">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <Icon name="document" class="title-icon" />
          HTML简历模板规范要求
        </h1>
        <p class="page-description">
          详细的HTML简历模板开发规范，确保转换为Vue组件时的完美兼容性
        </p>
      </div>
      
      <div class="header-actions">
        <button @click="downloadTemplate" class="btn btn-primary">
          <Icon name="download" />
          下载示例模板
        </button>
        <button @click="copyToClipboard" class="btn btn-outline">
          <Icon name="copy" />
          复制规范链接
        </button>
      </div>
    </div>

    <!-- 目录导航 -->
    <div class="table-of-contents">
      <h3>目录</h3>
      <ul class="toc-list">
        <li><a href="#basic-structure" @click="scrollTo('basic-structure')">1. 基本文档结构</a></li>
        <li><a href="#module-definition" @click="scrollTo('module-definition')">2. 模块定义规范</a></li>
        <li><a href="#field-binding" @click="scrollTo('field-binding')">3. 字段绑定规范</a></li>
        <li><a href="#css-variables" @click="scrollTo('css-variables')">4. CSS变量使用规范</a></li>
        <li><a href="#data-attributes" @click="scrollTo('data-attributes')">5. 数据属性详解</a></li>
        <li><a href="#module-specifications" @click="scrollTo('module-specifications')">6. 各模块字段规范</a></li>
        <li><a href="#style-requirements" @click="scrollTo('style-requirements')">7. 样式要求</a></li>
        <li><a href="#examples" @click="scrollTo('examples')">8. 完整示例</a></li>
        <li><a href="#validation" @click="scrollTo('validation')">9. 验证清单</a></li>
      </ul>
    </div>

    <!-- 规范内容 -->
    <div class="specification-content">
      
      <!-- 1. 基本文档结构 -->
      <section id="basic-structure" class="spec-section">
        <h2 class="section-title">1. 基本文档结构</h2>
        
        <div class="requirement-box">
          <h3>必需的HTML结构</h3>
          <div class="code-block">
            <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;简历标题&lt;/title&gt;
    &lt;style&gt;
        /* CSS样式 */
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="resume-template" data-module-root="true"&gt;
        &lt;!-- 简历内容 --&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
          </div>
          
          <div class="requirements-list">
            <h4>关键要求：</h4>
            <ul>
              <li><strong>DOCTYPE声明</strong>：必须使用 <code>&lt;!DOCTYPE html&gt;</code></li>
              <li><strong>字符编码</strong>：必须设置为 <code>UTF-8</code></li>
              <li><strong>根容器</strong>：必须包含 <code>class="resume-template"</code> 和 <code>data-module-root="true"</code></li>
              <li><strong>样式标签</strong>：CSS样式必须包含在 <code>&lt;style&gt;</code> 标签内</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 2. 模块定义规范 -->
      <section id="module-definition" class="spec-section">
        <h2 class="section-title">2. 模块定义规范</h2>
        
        <div class="requirement-box">
          <h3>模块标识</h3>
          <p>每个简历模块必须使用 <code>data-module</code> 属性进行标识：</p>
          
          <div class="code-block">
            <pre><code>&lt;section data-module="basic_info"&gt;
    &lt;!-- 基本信息内容 --&gt;
&lt;/section&gt;

&lt;section data-module="work_experience"&gt;
    &lt;!-- 工作经历内容 --&gt;
&lt;/section&gt;</code></pre>
          </div>
          
          <div class="module-table">
            <h4>支持的模块名称（共18个）：</h4>
            <table>
              <thead>
                <tr>
                  <th>模块名称</th>
                  <th>data-module值</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>基本信息</td>
                  <td><code>basic_info</code></td>
                  <td>姓名、联系方式、求职意向等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>工作经历</td>
                  <td><code>work_experience</code></td>
                  <td>工作履历、职位、公司等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>教育经历</td>
                  <td><code>education</code></td>
                  <td>学历、学校、专业等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>项目经历</td>
                  <td><code>project</code></td>
                  <td>项目名称、角色、描述等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>技能特长</td>
                  <td><code>skills</code></td>
                  <td>技能名称、熟练程度等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>获奖情况</td>
                  <td><code>award</code></td>
                  <td>奖项名称、获奖时间等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>证书资质</td>
                  <td><code>certificate</code></td>
                  <td>证书名称、颁发机构等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>语言能力</td>
                  <td><code>language</code></td>
                  <td>语言种类、熟练程度等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>实习经历</td>
                  <td><code>internship</code></td>
                  <td>实习公司、职位、时间等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>志愿经历</td>
                  <td><code>volunteer_experience</code></td>
                  <td>志愿组织、角色、时间等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>论文发表</td>
                  <td><code>publication</code></td>
                  <td>论文标题、期刊、时间等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>科研经历</td>
                  <td><code>research_experience</code></td>
                  <td>研究课题、机构、时间等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>培训经历</td>
                  <td><code>training</code></td>
                  <td>培训课程、机构、时间等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>作品集</td>
                  <td><code>portfolio</code></td>
                  <td>作品标题、类型、链接等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>兴趣爱好</td>
                  <td><code>hobbies</code></td>
                  <td>爱好名称、描述等</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>自我评价</td>
                  <td><code>self_evaluation</code></td>
                  <td>自我评价内容</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>自荐信</td>
                  <td><code>cover_letter</code></td>
                  <td>自荐信内容</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>自定义模块</td>
                  <td><code>custom_*</code></td>
                  <td>用户创建的个性化模块</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- 3. 字段绑定规范 -->
      <section id="field-binding" class="spec-section">
        <h2 class="section-title">3. 字段绑定规范</h2>
        
        <div class="requirement-box">
          <h3>数据绑定语法</h3>
          <p>使用 <code>data-vue-text</code> 属性进行数据绑定：</p>
          
          <div class="code-block">
            <pre><code>&lt;!-- 基本信息字段 --&gt;
&lt;span data-field="name" data-vue-text="resumeData.basic_info.name"&gt;默认值&lt;/span&gt;

&lt;!-- 循环列表项 --&gt;
&lt;div data-vue-for="item in resumeData.work_experience" data-vue-key="item.id"&gt;
    &lt;span data-field="company" data-vue-text="item.company"&gt;公司名称&lt;/span&gt;
&lt;/div&gt;</code></pre>
          </div>
          
          <div class="binding-rules">
            <h4>绑定规则：</h4>
            <ul>
              <li><strong>data-field</strong>：字段名称标识，用于系统识别</li>
              <li><strong>data-vue-text</strong>：Vue数据绑定表达式</li>
              <li><strong>data-vue-for</strong>：循环渲染指令</li>
              <li><strong>data-vue-key</strong>：循环项唯一标识</li>
              <li><strong>data-vue-if</strong>：条件渲染指令（用于控制模块显示/隐藏）</li>
            </ul>
          </div>
          
          <div class="conditional-rendering">
            <h4>条件渲染规范：</h4>
            <p>为了优化用户体验，避免空模块和空字段显示，建议为所有可选模块和字段添加条件渲染：</p>
            
            <div class="code-block">
              <pre><code>&lt;!-- 核心模块（始终显示） --&gt;
&lt;section data-module="basic_info"&gt;
    &lt;!-- 核心字段（始终显示） --&gt;
    &lt;h1 data-field="name" data-vue-text="resumeData.basic_info.name"&gt;张三&lt;/h1&gt;
    &lt;div data-field="phone" data-vue-text="resumeData.basic_info.phone"&gt;13800138000&lt;/div&gt;
    
    &lt;!-- 可选字段（有数据才显示） --&gt;
    &lt;div data-field="age" data-vue-text="resumeData.basic_info.age" 
         data-vue-if="resumeData.basic_info.age"&gt;25岁&lt;/div&gt;
    &lt;div data-field="github" data-vue-text="resumeData.basic_info.github"
         data-vue-if="resumeData.basic_info.github"&gt;github.com/user&lt;/div&gt;
&lt;/section&gt;

&lt;!-- 可选模块（有数据才显示） --&gt;
&lt;section data-module="education" data-vue-if="resumeData.education && resumeData.education.length > 0"&gt;
    &lt;h2&gt;教育经历&lt;/h2&gt;
    &lt;div data-vue-for="education in resumeData.education" data-vue-key="education.id"&gt;
        &lt;!-- 核心字段（始终显示） --&gt;
        &lt;h3 data-field="school" data-vue-text="education.school"&gt;北京大学&lt;/h3&gt;
        &lt;div data-field="major" data-vue-text="education.major"&gt;计算机科学与技术&lt;/div&gt;
        
        &lt;!-- 可选字段（有数据才显示） --&gt;
        &lt;div data-field="degree" data-vue-text="education.degree" 
             data-vue-if="education.degree"&gt;本科&lt;/div&gt;
        &lt;div data-field="gpa" data-vue-text="education.gpa" 
             data-vue-if="education.gpa"&gt;GPA: 3.8&lt;/div&gt;
        &lt;div data-field="description" data-vue-text="education.description" 
             data-vue-if="education.description"&gt;详细描述...&lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre>
            </div>
            
            <div class="condition-examples">
              <h5>条件渲染策略：</h5>
              
              <h6>1. 模块级条件渲染</h6>
              <ul>
                <li><strong>数组型模块</strong>：<code>resumeData.education && resumeData.education.length > 0</code></li>
                <li><strong>文本型模块</strong>：<code>resumeData.self_evaluation && resumeData.self_evaluation.content</code></li>
                <li><strong>对象型模块</strong>：<code>resumeData.basic_info && resumeData.basic_info.name</code></li>
              </ul>
              
              <h6>2. 字段级条件渲染</h6>
              <ul>
                <li><strong>基本字段</strong>：<code>resumeData.basic_info.age</code></li>
                <li><strong>循环项字段</strong>：<code>education.degree</code></li>
                <li><strong>复杂判断</strong>：<code>education.startDate && education.endDate</code></li>
              </ul>
              
              <h6>3. 字段分级策略</h6>
              <ul>
                <li><strong>核心字段</strong>：不加条件，始终显示（如姓名、学校名）</li>
                <li><strong>常用字段</strong>：有数据时显示（如年龄、专业、职位）</li>
                <li><strong>可选字段</strong>：用户选择性填写（如GPA、部门、详细描述）</li>
              </ul>
              
              <div class="note-box">
                <p><strong>实现效果：</strong></p>
                <ul>
                  <li><strong>技术完整性</strong>：HTML模板包含所有18个模块和字段（满足技术要求）</li>
                  <li><strong>界面简洁性</strong>：初始显示只展示有数据的模块和字段（避免空白区域）</li>
                  <li><strong>动态扩展性</strong>：用户添加数据后，相应模块和字段自动显示（响应式体验）</li>
                  <li><strong>自动清理性</strong>：删除数据后，对应模块和字段自动隐藏（保持整洁）</li>
                  <li><strong>零代码修改</strong>：基于现有HTML转Vue转换器，无需修改任何后端代码</li>
                </ul>
                
                <div class="implementation-summary">
                  <h6>条件渲染实施总结：</h6>
                  <ul>
                    <li><strong>基本信息模块</strong>：核心字段（姓名、电话、邮箱）始终显示，其他字段按需显示</li>
                    <li><strong>列表型模块</strong>：工作经历、教育经历、项目经历等，模块和字段都支持条件渲染</li>
                    <li><strong>技能类模块</strong>：技能名称必显，等级、熟练度、进度条按需显示</li>
                    <li><strong>文本型模块</strong>：自我评价、自荐信等，有内容才显示整个模块</li>
                    <li><strong>时间字段</strong>：开始/结束时间及分隔符都支持独立的条件显示</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 4. CSS变量使用规范 -->
      <section id="css-variables" class="spec-section">
        <h2 class="section-title">4. CSS变量使用规范</h2>
        
        <div class="requirement-box">
          <h3>标准CSS变量系统</h3>
          <p>为了确保样式的一致性和可配置性，所有简历模板必须使用以下标准CSS变量：</p>
          
          <div class="css-variables-table">
            <h4>📝 变量分类说明</h4>
            <table class="specification-table">
              <thead>
                <tr>
                  <th>分类</th>
                  <th>变量名</th>
                  <th>说明</th>
                  <th>示例值</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td rowspan="3"><strong>字体设置</strong></td>
                  <td><code>--resume-font-family</code></td>
                  <td>全局字体族，影响整个简历的字体显示</td>
                  <td>system-ui, sans-serif</td>
                </tr>
                <tr>
                  <td><code>--resume-font-size</code></td>
                  <td>基础字体大小，其他字体大小基于此计算</td>
                  <td>14px</td>
                </tr>
                <tr>
                  <td><code>--resume-line-height</code></td>
                  <td>行高，影响文本可读性和紧凑度</td>
                  <td>1.6</td>
                </tr>
                <tr>
                  <td rowspan="3"><strong>颜色设置</strong></td>
                  <td><code>--resume-text-color</code></td>
                  <td>主要文本颜色，用于正文内容</td>
                  <td>#333333</td>
                </tr>
                <tr>
                  <td><code>--resume-primary-color</code></td>
                  <td>主色调，用于标题、链接等强调元素</td>
                  <td>#3498db</td>
                </tr>
                <tr>
                  <td><code>--resume-secondary-color</code></td>
                  <td>次要文本颜色，用于日期、地址等辅助信息</td>
                  <td>#666666</td>
                </tr>
                <tr>
                  <td rowspan="4"><strong>页面边距</strong></td>
                  <td><code>--resume-margin-top</code></td>
                  <td>页面上边距，控制简历与页面顶部的距离</td>
                  <td>60px</td>
                </tr>
                <tr>
                  <td><code>--resume-margin-bottom</code></td>
                  <td>页面下边距，控制简历与页面底部的距离</td>
                  <td>60px</td>
                </tr>
                <tr>
                  <td><code>--resume-margin-left</code></td>
                  <td>页面左边距，控制简历与页面左侧的距离</td>
                  <td>60px</td>
                </tr>
                <tr>
                  <td><code>--resume-margin-right</code></td>
                  <td>页面右边距，控制简历与页面右侧的距离</td>
                  <td>60px</td>
                </tr>
                <tr>
                  <td rowspan="4"><strong>模块间距</strong></td>
                  <td><code>--resume-module-spacing</code></td>
                  <td>模块间垂直间距（如教育、工作经历之间）</td>
                  <td>24px</td>
                </tr>
                <tr>
                  <td><code>--resume-item-spacing</code></td>
                  <td>同模块内条目间距（如多个工作经历之间）</td>
                  <td>16px</td>
                </tr>
                <tr>
                  <td><code>--resume-paragraph-spacing</code></td>
                  <td>段落间距，用于小的文本块分隔</td>
                  <td>12px</td>
                </tr>
                <tr>
                  <td><code>--resume-column-gap</code></td>
                  <td>多列布局时的列间距</td>
                  <td>40px</td>
                </tr>
                <tr>
                  <td rowspan="2"><strong>内边距</strong></td>
                  <td><code>--resume-module-padding</code></td>
                  <td>模块内部的填充空间</td>
                  <td>16px</td>
                </tr>
                <tr>
                  <td><code>--resume-item-padding</code></td>
                  <td>条目内部的填充空间</td>
                  <td>12px</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="css-usage-examples">
            <h4>🔧 使用示例</h4>
            
            <h5>推荐用法（使用resume-前缀变量）：</h5>
            <div class="code-block">
              <pre><code>.resume-template {
    /* 页面布局 */
    padding: var(--resume-margin-top) var(--resume-margin-right) 
             var(--resume-margin-bottom) var(--resume-margin-left);
    
    /* 字体设置 */
    font-family: var(--resume-font-family);
    font-size: var(--resume-font-size);
    line-height: var(--resume-line-height);
    color: var(--resume-text-color);
}

.section-title {
    /* 主色调用于标题 */
    color: var(--resume-primary-color);
    /* 基于基础字体大小计算标题大小 */
    font-size: calc(var(--resume-font-size) + 4px);
    /* 模块间距 */
    margin-bottom: var(--resume-module-spacing);
}

.work-item {
    /* 条目间距 */
    margin-bottom: var(--resume-item-spacing);
    /* 条目内边距 */
    padding: var(--resume-item-padding);
}

.work-date {
    /* 次要颜色用于日期 */
    color: var(--resume-secondary-color);
}</code></pre>
            </div>
            
            <h5>向后兼容（旧版变量仍可使用）：</h5>
            <div class="code-block">
              <pre><code>/* 旧版变量（系统会自动映射到新版变量） */
.resume-template {
    padding: var(--margin-top) var(--margin-side) var(--margin-bottom);
    font-family: var(--font-family);
    color: var(--text-color);
}

/* 新版变量（推荐使用） */
.resume-template {
    padding: var(--resume-margin-top) var(--resume-margin-left) var(--resume-margin-bottom);
    font-family: var(--resume-font-family);
    color: var(--resume-text-color);
}</code></pre>
            </div>
          </div>
          
          <div class="important-notes">
            <h4>⚠️ 重要说明</h4>
            <ul>
              <li><strong>必须使用</strong>：所有模板必须使用这些CSS变量，不得硬编码样式值</li>
              <li><strong>前缀规范</strong>：新模板推荐使用 <code>--resume-</code> 前缀的变量名</li>
              <li><strong>向后兼容</strong>：旧版无前缀变量仍然支持，但新开发建议使用新版</li>
              <li><strong>计算值</strong>：可以基于变量进行计算，如 <code>calc(var(--resume-font-size) + 2px)</code></li>
              <li><strong>系统控制</strong>：这些变量由编辑器系统动态设置，模板只负责使用</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 5. 数据属性详解 -->
      <section id="data-attributes" class="spec-section">
        <h2 class="section-title">5. 数据属性详解</h2>
        
        <div class="requirement-box">
          <h3>数据表达式格式</h3>
          
          <div class="expression-table">
            <table>
              <thead>
                <tr>
                  <th>表达式类型</th>
                  <th>格式</th>
                  <th>示例</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>基本信息</td>
                  <td><code>resumeData.basic_info.{字段名}</code></td>
                  <td><code>resumeData.basic_info.name</code></td>
                  <td>直接访问基本信息字段</td>
                </tr>
                <tr>
                  <td>循环项字段</td>
                  <td><code>item.{字段名}</code></td>
                  <td><code>item.company</code></td>
                  <td>循环中的单项数据</td>
                </tr>
                <tr>
                  <td>循环集合</td>
                  <td><code>resumeData.{模块名}</code></td>
                  <td><code>resumeData.work_experience</code></td>
                  <td>数据集合</td>
                </tr>
                <tr>
                  <td>技能循环</td>
                  <td><code>skill.{字段名}</code></td>
                  <td><code>skill.name</code></td>
                  <td>技能模块专用变量名</td>
                </tr>
                <tr>
                  <td>兴趣爱好循环</td>
                  <td><code>hobby.{字段名}</code></td>
                  <td><code>hobby.name</code></td>
                  <td>兴趣爱好模块专用变量名</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- 5. 各模块字段规范 -->
      <section id="module-specifications" class="spec-section">
        <h2 class="section-title">6. 各模块字段规范</h2>
        
        <!-- 基本信息模块 -->
        <div class="module-spec">
          <h3>5.1 基本信息模块 (basic_info)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>照片</td>
                  <td><code>photo</code></td>
                  <td><code>resumeData.basic_info.photo</code></td>
                  <td>头像照片URL</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>姓名</td>
                  <td><code>name</code></td>
                  <td><code>resumeData.basic_info.name</code></td>
                  <td>完整姓名</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>年龄</td>
                  <td><code>age</code></td>
                  <td><code>resumeData.basic_info.age</code></td>
                  <td>年龄</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>电话</td>
                  <td><code>phone</code></td>
                  <td><code>resumeData.basic_info.phone</code></td>
                  <td>联系电话</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>邮箱</td>
                  <td><code>email</code></td>
                  <td><code>resumeData.basic_info.email</code></td>
                  <td>电子邮箱</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>所在城市</td>
                  <td><code>currentCity</code></td>
                  <td><code>resumeData.basic_info.currentCity</code></td>
                  <td>当前居住城市</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>意向城市</td>
                  <td><code>intendedCity</code></td>
                  <td><code>resumeData.basic_info.intendedCity</code></td>
                  <td>期望工作城市</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>居住地址</td>
                  <td><code>address</code></td>
                  <td><code>resumeData.basic_info.address</code></td>
                  <td>详细地址</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>当前状态</td>
                  <td><code>currentStatus</code></td>
                  <td><code>resumeData.basic_info.currentStatus</code></td>
                  <td>当前工作状态</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>期望岗位</td>
                  <td><code>expectedPosition</code></td>
                  <td><code>resumeData.basic_info.expectedPosition</code></td>
                  <td>求职意向</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>期望薪资</td>
                  <td><code>expectedSalary</code></td>
                  <td><code>resumeData.basic_info.expectedSalary</code></td>
                  <td>薪资期望</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>微信号</td>
                  <td><code>wechat</code></td>
                  <td><code>resumeData.basic_info.wechat</code></td>
                  <td>微信联系方式</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>个人网站</td>
                  <td><code>website</code></td>
                  <td><code>resumeData.basic_info.website</code></td>
                  <td>个人网站链接</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>GitHub</td>
                  <td><code>github</code></td>
                  <td><code>resumeData.basic_info.github</code></td>
                  <td>GitHub链接</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>性别</td>
                  <td><code>gender</code></td>
                  <td><code>resumeData.basic_info.gender</code></td>
                  <td>性别信息</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>身高</td>
                  <td><code>height</code></td>
                  <td><code>resumeData.basic_info.height</code></td>
                  <td>身高信息</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>体重</td>
                  <td><code>weight</code></td>
                  <td><code>resumeData.basic_info.weight</code></td>
                  <td>体重信息</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>政治面貌</td>
                  <td><code>politicalStatus</code></td>
                  <td><code>resumeData.basic_info.politicalStatus</code></td>
                  <td>政治面貌</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>婚姻状况</td>
                  <td><code>maritalStatus</code></td>
                  <td><code>resumeData.basic_info.maritalStatus</code></td>
                  <td>婚姻状况</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>籍贯</td>
                  <td><code>hometown</code></td>
                  <td><code>resumeData.basic_info.hometown</code></td>
                  <td>籍贯信息</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>民族</td>
                  <td><code>ethnicity</code></td>
                  <td><code>resumeData.basic_info.ethnicity</code></td>
                  <td>民族信息</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>自定义社交信息</td>
                  <td><code>customSocials</code></td>
                  <td><code>resumeData.basic_info.customSocials</code></td>
                  <td>自定义社交信息数组</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>自定义信息</td>
                  <td><code>customInfos</code></td>
                  <td><code>resumeData.basic_info.customInfos</code></td>
                  <td>自定义信息数组</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="code-example">
            <h4>示例代码：</h4>
            <div class="code-block">
              <pre><code>&lt;section data-module="basic_info"&gt;
    &lt;h2&gt;基本信息&lt;/h2&gt;
    &lt;div class="basic-info-grid"&gt;
        &lt;div class="info-item"&gt;
            &lt;img data-field="photo" data-vue-src="resumeData.basic_info.photo" src="default-avatar.jpg" alt="头像" /&gt;
        &lt;/div&gt;
        &lt;div class="info-item"&gt;
            &lt;label&gt;姓名：&lt;/label&gt;
            &lt;span data-field="name" data-vue-text="resumeData.basic_info.name"&gt;张三&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="info-item"&gt;
            &lt;label&gt;期望岗位：&lt;/label&gt;
            &lt;span data-field="expectedPosition" data-vue-text="resumeData.basic_info.expectedPosition"&gt;前端工程师&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="info-item"&gt;
            &lt;label&gt;电话：&lt;/label&gt;
            &lt;span data-field="phone" data-vue-text="resumeData.basic_info.phone"&gt;138-0000-0000&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="info-item"&gt;
            &lt;label&gt;邮箱：&lt;/label&gt;
            &lt;span data-field="email" data-vue-text="resumeData.basic_info.email"&gt;<EMAIL>&lt;/span&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre>
            </div>
          </div>
        </div>

        <!-- 工作经历模块 -->
        <div class="module-spec">
          <h3>5.2 工作经历模块 (work_experience)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>公司名称</td>
                  <td><code>company</code></td>
                  <td><code>item.company</code></td>
                  <td>工作单位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>职位</td>
                  <td><code>position</code></td>
                  <td><code>item.position</code></td>
                  <td>担任职位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>部门</td>
                  <td><code>department</code></td>
                  <td><code>item.department</code></td>
                  <td>所在部门</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>开始时间</td>
                  <td><code>startDate</code></td>
                  <td><code>item.startDate</code></td>
                  <td>入职时间</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>结束时间</td>
                  <td><code>endDate</code></td>
                  <td><code>item.endDate</code></td>
                  <td>离职时间</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>工作描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>工作内容</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="code-example">
            <h4>示例代码：</h4>
            <div class="code-block">
              <pre><code>&lt;section data-module="work_experience"&gt;
    &lt;h2&gt;工作经历&lt;/h2&gt;
    &lt;div class="work-list"&gt;
        &lt;div class="work-item" data-vue-for="item in resumeData.work_experience" data-vue-key="item.id"&gt;
            &lt;div class="work-header"&gt;
                &lt;h3&gt;
                    &lt;span data-field="company" data-vue-text="item.company"&gt;ABC科技公司&lt;/span&gt; - 
                    &lt;span data-field="position" data-vue-text="item.position"&gt;前端工程师&lt;/span&gt;
                &lt;/h3&gt;
                &lt;div class="work-period"&gt;
                    &lt;span data-field="startDate" data-vue-text="item.startDate"&gt;2020-01&lt;/span&gt; ~ 
                    &lt;span data-field="endDate" data-vue-text="item.endDate"&gt;2023-12&lt;/span&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="work-content"&gt;
                &lt;p data-field="description" data-vue-text="item.description"&gt;
                    负责前端开发工作，使用Vue.js和React开发用户界面。
                &lt;/p&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre>
            </div>
          </div>
        </div>

        <!-- 教育经历模块 -->
        <div class="module-spec">
          <h3>5.3 教育经历模块 (education)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>学校名称</td>
                  <td><code>school</code></td>
                  <td><code>item.school</code></td>
                  <td>毕业院校</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>专业</td>
                  <td><code>major</code></td>
                  <td><code>item.major</code></td>
                  <td>所学专业</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>学历</td>
                  <td><code>degree</code></td>
                  <td><code>item.degree</code></td>
                  <td>学位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>开始时间</td>
                  <td><code>startDate</code></td>
                  <td><code>item.startDate</code></td>
                  <td>入学时间</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>结束时间</td>
                  <td><code>endDate</code></td>
                  <td><code>item.endDate</code></td>
                  <td>毕业时间</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>自定义学历</td>
                  <td><code>customDegree</code></td>
                  <td><code>item.customDegree</code></td>
                  <td>自定义学历名称</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>教育背景描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 项目经历模块 -->
        <div class="module-spec">
          <h3>5.4 项目经历模块 (project)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>项目名称</td>
                  <td><code>name</code></td>
                  <td><code>item.name</code></td>
                  <td>项目标题</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>担任角色</td>
                  <td><code>role</code></td>
                  <td><code>item.role</code></td>
                  <td>项目角色</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>所在公司</td>
                  <td><code>company</code></td>
                  <td><code>item.company</code></td>
                  <td>项目所属公司</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>开始时间</td>
                  <td><code>startDate</code></td>
                  <td><code>item.startDate</code></td>
                  <td>项目开始</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>结束时间</td>
                  <td><code>endDate</code></td>
                  <td><code>item.endDate</code></td>
                  <td>项目结束</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>项目描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 技能特长模块 -->
        <div class="module-spec">
          <h3>5.5 技能特长模块 (skills)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>技能名称</td>
                  <td><code>name</code></td>
                  <td><code>skill.name</code></td>
                  <td>技能名称</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>熟练程度百分比</td>
                  <td><code>level</code></td>
                  <td><code>skill.level</code></td>
                  <td>0-100的数字</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>熟练程度文字</td>
                  <td><code>proficiency</code></td>
                  <td><code>skill.proficiency</code></td>
                  <td>精通/擅长/熟练/良好/一般</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>显示模式</td>
                  <td><code>displayMode</code></td>
                  <td><code>skill.displayMode</code></td>
                  <td>percentage/proficiency</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="code-example">
            <h4>示例代码：</h4>
            <div class="code-block">
              <pre><code>&lt;section data-module="skills"&gt;
    &lt;h2&gt;技能特长&lt;/h2&gt;
    &lt;div class="skills-grid"&gt;
        &lt;div class="skill-item" data-vue-for="skill in resumeData.skills" data-vue-key="skill.id"&gt;
            &lt;div class="skill-name" data-field="name" data-vue-text="skill.name"&gt;JavaScript&lt;/div&gt;
            &lt;div class="skill-level" data-field="level" data-vue-text="skill.level"&gt;90&lt;/div&gt;
            &lt;div class="skill-proficiency" data-field="proficiency" data-vue-text="skill.proficiency"&gt;精通&lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre>
            </div>
          </div>
        </div>

        <!-- 获奖情况模块 -->
        <div class="module-spec">
          <h3>5.6 获奖情况模块 (award)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>奖项名称</td>
                  <td><code>name</code></td>
                  <td><code>item.name</code></td>
                  <td>奖项标题</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>获奖时间</td>
                  <td><code>date</code></td>
                  <td><code>item.date</code></td>
                  <td>获奖日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>颁发机构</td>
                  <td><code>organization</code></td>
                  <td><code>item.organization</code></td>
                  <td>颁奖单位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>获奖等级</td>
                  <td><code>level</code></td>
                  <td><code>item.level</code></td>
                  <td>奖项等级</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 证书资质模块 -->
        <div class="module-spec">
          <h3>5.7 证书资质模块 (certificate)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>证书名称</td>
                  <td><code>name</code></td>
                  <td><code>item.name</code></td>
                  <td>证书标题</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>获得时间</td>
                  <td><code>date</code></td>
                  <td><code>item.date</code></td>
                  <td>取得日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>颁发机构</td>
                  <td><code>organization</code></td>
                  <td><code>item.organization</code></td>
                  <td>颁发单位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>证书编号</td>
                  <td><code>number</code></td>
                  <td><code>item.number</code></td>
                  <td>证书序号</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 语言能力模块 -->
        <div class="module-spec">
          <h3>5.8 语言能力模块 (language)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>语言名称</td>
                  <td><code>name</code></td>
                  <td><code>item.name</code></td>
                  <td>语言种类</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>熟练程度</td>
                  <td><code>level</code></td>
                  <td><code>item.level</code></td>
                  <td>掌握程度</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>相关证书</td>
                  <td><code>certificate</code></td>
                  <td><code>item.certificate</code></td>
                  <td>语言证书</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 实习经历模块 -->
        <div class="module-spec">
          <h3>5.9 实习经历模块 (internship)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>公司名称</td>
                  <td><code>company</code></td>
                  <td><code>item.company</code></td>
                  <td>实习单位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>实习职位</td>
                  <td><code>position</code></td>
                  <td><code>item.position</code></td>
                  <td>实习岗位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>开始时间</td>
                  <td><code>startDate</code></td>
                  <td><code>item.startDate</code></td>
                  <td>开始日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>结束时间</td>
                  <td><code>endDate</code></td>
                  <td><code>item.endDate</code></td>
                  <td>结束日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>实习描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>工作内容</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 志愿经历模块 -->
        <div class="module-spec">
          <h3>5.10 志愿经历模块 (volunteer_experience)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>组织机构</td>
                  <td><code>organization</code></td>
                  <td><code>item.organization</code></td>
                  <td>志愿组织</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>担任角色</td>
                  <td><code>role</code></td>
                  <td><code>item.role</code></td>
                  <td>志愿角色</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>服务地点</td>
                  <td><code>location</code></td>
                  <td><code>item.location</code></td>
                  <td>服务场所</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>开始时间</td>
                  <td><code>startDate</code></td>
                  <td><code>item.startDate</code></td>
                  <td>开始日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>结束时间</td>
                  <td><code>endDate</code></td>
                  <td><code>item.endDate</code></td>
                  <td>结束日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 论文发表模块 -->
        <div class="module-spec">
          <h3>5.11 论文发表模块 (publication)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>论文标题</td>
                  <td><code>title</code></td>
                  <td><code>item.title</code></td>
                  <td>文章标题</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>论文类型</td>
                  <td><code>type</code></td>
                  <td><code>item.type</code></td>
                  <td>文章类型</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>发表期刊</td>
                  <td><code>journal</code></td>
                  <td><code>item.journal</code></td>
                  <td>期刊名称</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>发表时间</td>
                  <td><code>date</code></td>
                  <td><code>item.date</code></td>
                  <td>发表日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 科研经历模块 -->
        <div class="module-spec">
          <h3>5.12 科研经历模块 (research_experience)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>研究课题</td>
                  <td><code>topic</code></td>
                  <td><code>item.topic</code></td>
                  <td>研究主题</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>担任角色</td>
                  <td><code>role</code></td>
                  <td><code>item.role</code></td>
                  <td>研究角色</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>研究机构</td>
                  <td><code>organization</code></td>
                  <td><code>item.organization</code></td>
                  <td>研究单位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>开始时间</td>
                  <td><code>startDate</code></td>
                  <td><code>item.startDate</code></td>
                  <td>开始日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>结束时间</td>
                  <td><code>endDate</code></td>
                  <td><code>item.endDate</code></td>
                  <td>结束日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 培训经历模块 -->
        <div class="module-spec">
          <h3>5.13 培训经历模块 (training)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>培训课程</td>
                  <td><code>course</code></td>
                  <td><code>item.course</code></td>
                  <td>课程名称</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>培训机构</td>
                  <td><code>company</code></td>
                  <td><code>item.company</code></td>
                  <td>培训单位</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>开始时间</td>
                  <td><code>startDate</code></td>
                  <td><code>item.startDate</code></td>
                  <td>开始日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>结束时间</td>
                  <td><code>endDate</code></td>
                  <td><code>item.endDate</code></td>
                  <td>结束日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 作品集模块 -->
        <div class="module-spec">
          <h3>5.14 作品集模块 (portfolio)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>作品标题</td>
                  <td><code>title</code></td>
                  <td><code>item.title</code></td>
                  <td>作品名称</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>作品类型</td>
                  <td><code>type</code></td>
                  <td><code>item.type</code></td>
                  <td>作品分类</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>作品链接</td>
                  <td><code>url</code></td>
                  <td><code>item.url</code></td>
                  <td>在线链接</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>创作时间</td>
                  <td><code>date</code></td>
                  <td><code>item.date</code></td>
                  <td>创作日期</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>item.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 兴趣爱好模块 -->
        <div class="module-spec">
          <h3>5.15 兴趣爱好模块 (hobbies)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>爱好名称</td>
                  <td><code>name</code></td>
                  <td><code>hobby.name</code></td>
                  <td>兴趣名称</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>熟练程度</td>
                  <td><code>level</code></td>
                  <td><code>hobby.level</code></td>
                  <td>掌握程度</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>描述</td>
                  <td><code>description</code></td>
                  <td><code>hobby.description</code></td>
                  <td>详细描述</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 自我评价模块 -->
        <div class="module-spec">
          <h3>5.16 自我评价模块 (self_evaluation)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>评价内容</td>
                  <td><code>content</code></td>
                  <td><code>resumeData.self_evaluation.content</code></td>
                  <td>自我评价文本</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 自荐信模块 -->
        <div class="module-spec">
          <h3>5.17 自荐信模块 (cover_letter)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>信件内容</td>
                  <td><code>content</code></td>
                  <td><code>resumeData.cover_letter.content</code></td>
                  <td>自荐信文本</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 自定义模块 -->
        <div class="module-spec">
          <h3>5.18 自定义模块 (custom_*)</h3>
          <div class="field-table">
            <table>
              <thead>
                <tr>
                  <th>字段名称</th>
                  <th>data-field值</th>
                  <th>data-vue-text表达式</th>
                  <th>描述</th>
                  <th>是否必需</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>名称</td>
                  <td><code>name</code></td>
                  <td><code>item.name</code></td>
                  <td>自定义项目名称</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>角色</td>
                  <td><code>role</code></td>
                  <td><code>item.role</code></td>
                  <td>担任的角色</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>开始时间</td>
                  <td><code>startDate</code></td>
                  <td><code>item.startDate</code></td>
                  <td>开始时间</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>结束时间</td>
                  <td><code>endDate</code></td>
                  <td><code>item.endDate</code></td>
                  <td>结束时间</td>
                  <td class="required">必需</td>
                </tr>
                <tr>
                  <td>详细内容</td>
                  <td><code>content</code></td>
                  <td><code>item.content</code></td>
                  <td>详细描述内容</td>
                  <td class="required">必需</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="code-example">
            <h4>示例代码：</h4>
            <div class="code-block">
              <pre><code>&lt;section data-module="custom_1001"&gt;
    &lt;h2&gt;社团活动&lt;/h2&gt;
    &lt;div data-vue-for="item in resumeData.custom_1001.items" data-vue-key="item.id"&gt;
        &lt;div class="custom-item"&gt;
            &lt;div class="item-header"&gt;
                &lt;h3 data-field="name" data-vue-text="item.name"&gt;学生会&lt;/h3&gt;
                &lt;span data-field="role" data-vue-text="item.role"&gt;主席&lt;/span&gt;
            &lt;/div&gt;
            &lt;div class="item-time"&gt;
                &lt;span data-field="startDate" data-vue-text="item.startDate"&gt;2019-09&lt;/span&gt;
                &lt;span&gt; - &lt;/span&gt;
                &lt;span data-field="endDate" data-vue-text="item.endDate"&gt;2020-06&lt;/span&gt;
            &lt;/div&gt;
            &lt;div class="item-content"&gt;
                &lt;p data-field="content" data-vue-text="item.content"&gt;组织学校大型活动，管理团队50人...&lt;/p&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/section&gt;</code></pre>
            </div>
          </div>
          
          <div class="requirement-box">
            <h4>重要说明：</h4>
            <ul>
              <li>自定义模块ID格式为：<code>custom_[时间戳]</code>（如：custom_1700000001）</li>
              <li>每个用户最多可创建3个自定义模块</li>
              <li>自定义模块支持类似其他模块的数组数据结构</li>
              <li>模块名称由用户创建时自定义（如：社团活动、获奖情况等）</li>
              <li>所有字段都是可选的，可根据实际需要使用</li>
            </ul>
          </div>
        </div>

        <!-- 支持的模块总览 -->
        <div class="module-spec">
          <h3>5.19 支持的模块总览</h3>
          <div class="modules-overview">
            <p><strong>当前系统支持以下18个简历模块：</strong></p>
            <div class="modules-grid">
              <div class="module-item required">
                <span class="module-id">basic_info</span>
                <span class="module-name">基本信息</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required ">
                <span class="module-id">work_experience</span>
                <span class="module-name">工作经历</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">education</span>
                <span class="module-name">教育经历</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">project</span>
                <span class="module-name">项目经历</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">skills</span>
                <span class="module-name">技能特长</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">award</span>
                <span class="module-name">获奖情况</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">certificate</span>
                <span class="module-name">证书资质</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">language</span>
                <span class="module-name">语言能力</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">internship</span>
                <span class="module-name">实习经历</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">volunteer_experience</span>
                <span class="module-name">志愿经历</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">publication</span>
                <span class="module-name">论文发表</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">research_experience</span>
                <span class="module-name">科研经历</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">training</span>
                <span class="module-name">培训经历</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">portfolio</span>
                <span class="module-name">作品集</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">hobbies</span>
                <span class="module-name">兴趣爱好</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">self_evaluation</span>
                <span class="module-name">自我评价</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">cover_letter</span>
                <span class="module-name">自荐信</span>
                <span class="module-status">必需</span>
              </div>
              <div class="module-item required">
                <span class="module-id">custom_*</span>
                <span class="module-name">自定义模块</span>
                <span class="module-status">必需</span>
              </div>
            </div>
            <p class="modules-note">
              <strong>注意：</strong>大部分模块ID使用单数形式，只有 <code>skills</code> 和 <code>hobbies</code> 使用复数形式。
              在循环渲染中，大部分模块使用 <code>item</code> 作为循环变量名，技能模块使用 <code>skill</code>，兴趣爱好模块使用 <code>hobby</code>。
            </p>
          </div>
        </div>
      </section>

      <!-- 6. 样式要求 -->
      <section id="style-requirements" class="spec-section">
        <h2 class="section-title">7. 样式要求</h2>
        
        <!-- 基本样式规范 -->
        <div class="requirement-box">
          <h3>CSS样式规范</h3>
          
          <div class="style-rules">
            <h4>基本要求：</h4>
            <ul>
              <li><strong>根容器样式</strong>：必须定义 <code>.resume-template</code> 类</li>
              <li><strong>单位使用</strong>：建议使用相对单位（rem、em、%）</li>
              <li><strong>响应式设计</strong>：支持不同屏幕尺寸</li>
              <li><strong>打印友好</strong>：考虑打印时的样式表现</li>
              <li><strong>字体设置</strong>：使用系统字体或Web安全字体</li>
            </ul>
          </div>
          
          <div class="code-block">
            <pre><code>/* 必需的根容器样式 */
.resume-template {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 模块通用样式 */
.resume-template section {
    margin-bottom: 2rem;
    padding: 1rem 0;
}

.resume-template h1, h2, h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .resume-template {
        padding: 15px;
        font-size: 14px;
    }
}

/* 打印样式 */
@media print {
    .resume-template {
        margin: 0;
        padding: 0;
        box-shadow: none;
    }
}</code></pre>
          </div>
        </div>

        <!-- 模块样式规范 -->
        <div class="requirement-box">
          <h3>模块样式规范</h3>
          <p>每个模块都应遵循统一的样式规范，确保视觉一致性：</p>
          
          <div class="module-style-table">
            <table>
              <thead>
                <tr>
                  <th>模块类型</th>
                  <th>推荐CSS类名</th>
                  <th>布局要求</th>
                  <th>特殊样式说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>基本信息</td>
                  <td><code>.basic-info</code></td>
                  <td>居中对齐，头像+信息布局</td>
                  <td>头像圆形显示，联系信息网格布局</td>
                </tr>
                <tr>
                  <td>工作经历</td>
                  <td><code>.work-experience</code></td>
                  <td>时间轴或卡片布局</td>
                  <td>公司+职位标题，描述内容缩进</td>
                </tr>
                <tr>
                  <td>教育经历</td>
                  <td><code>.education</code></td>
                  <td>学校+学历+时间布局</td>
                  <td>学历信息突出显示</td>
                </tr>
                <tr>
                  <td>项目经历</td>
                  <td><code>.project</code></td>
                  <td>项目名+角色+时间布局</td>
                  <td>技术栈标签化显示</td>
                </tr>
                <tr>
                  <td>技能特长</td>
                  <td><code>.skills</code></td>
                  <td>网格或标签布局</td>
                  <td>支持进度条和等级显示</td>
                </tr>
                <tr>
                  <td>语言能力</td>
                  <td><code>.language</code></td>
                  <td>语言+等级布局</td>
                  <td>等级可用星级或文字显示</td>
                </tr>
                <tr>
                  <td>获奖情况</td>
                  <td><code>.award</code></td>
                  <td>奖项+机构+时间布局</td>
                  <td>奖项名称突出显示</td>
                </tr>
                <tr>
                  <td>证书资质</td>
                  <td><code>.certificate</code></td>
                  <td>证书+机构+时间布局</td>
                  <td>证书编号可选显示</td>
                </tr>
                <tr>
                  <td>实习经历</td>
                  <td><code>.internship</code></td>
                  <td>公司+职位+时间布局</td>
                  <td>类似工作经历样式</td>
                </tr>
                <tr>
                  <td>志愿经历</td>
                  <td><code>.volunteer-experience</code></td>
                  <td>组织+角色+时间布局</td>
                  <td>服务地点可额外显示</td>
                </tr>
                <tr>
                  <td>论文发表</td>
                  <td><code>.publication</code></td>
                  <td>标题+期刊+时间布局</td>
                  <td>论文标题斜体显示</td>
                </tr>
                <tr>
                  <td>科研经历</td>
                  <td><code>.research-experience</code></td>
                  <td>课题+角色+机构布局</td>
                  <td>研究机构单独一行</td>
                </tr>
                <tr>
                  <td>培训经历</td>
                  <td><code>.training</code></td>
                  <td>课程+机构+时间布局</td>
                  <td>培训机构突出显示</td>
                </tr>
                <tr>
                  <td>作品集</td>
                  <td><code>.portfolio</code></td>
                  <td>作品+类型+链接布局</td>
                  <td>链接可点击，图标显示</td>
                </tr>
                <tr>
                  <td>兴趣爱好</td>
                  <td><code>.hobbies</code></td>
                  <td>标签或列表布局</td>
                  <td>简洁的标签样式</td>
                </tr>
                <tr>
                  <td>自我评价</td>
                  <td><code>.self-evaluation</code></td>
                  <td>段落布局</td>
                  <td>文字行高适中，易读</td>
                </tr>
                <tr>
                  <td>自荐信</td>
                  <td><code>.cover-letter</code></td>
                  <td>正文段落布局</td>
                  <td>类似自我评价样式</td>
                </tr>
                <tr>
                  <td>自定义模块</td>
                  <td><code>.custom-module</code></td>
                  <td>通用模块布局</td>
                  <td>根据内容类型灵活调整</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 字段样式规范 -->
        <div class="requirement-box">
          <h3>字段样式规范</h3>
          
          <div class="field-style-rules">
            <h4>通用字段样式：</h4>
            <ul>
              <li><strong>标题字段</strong>：使用较大字号，加粗显示</li>
              <li><strong>时间字段</strong>：使用较小字号，右对齐或单独一行</li>
              <li><strong>描述字段</strong>：正常字号，左对齐，适当行间距</li>
              <li><strong>链接字段</strong>：蓝色显示，悬停效果</li>
              <li><strong>技能等级</strong>：进度条或星级显示</li>
            </ul>
          </div>
          
          <div class="code-block">
            <pre><code>/* 字段通用样式 */
.field-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.field-subtitle {
    font-weight: 500;
    color: #34495e;
    font-size: 0.9em;
}

.field-date {
    font-size: 0.85em;
    color: #7f8c8d;
    font-style: italic;
}

.field-description {
    line-height: 1.6;
    color: #2c3e50;
    margin: 0.5rem 0;
}

.field-link {
    color: #3498db;
    text-decoration: none;
    transition: color 0.2s;
}

.field-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* 技能等级样式 */
.skill-progress {
    width: 100%;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.skill-progress-bar {
    height: 100%;
    background: #3498db;
    transition: width 0.3s ease;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: #ecf0f1;
    color: #2c3e50;
    border-radius: 0.25rem;
    font-size: 0.85em;
    margin: 0.125rem;
}</code></pre>
          </div>
        </div>

        <!-- 布局样式规范 -->
        <div class="requirement-box">
          <h3>布局样式规范</h3>
          
          <div class="layout-rules">
            <h4>推荐布局模式：</h4>
            <ul>
              <li><strong>单列布局</strong>：适合简洁风格，所有模块垂直排列</li>
              <li><strong>双列布局</strong>：左侧基本信息+技能，右侧其他模块</li>
              <li><strong>网格布局</strong>：技能、语言等模块使用网格排列</li>
              <li><strong>时间轴布局</strong>：工作、教育、项目经历使用时间轴</li>
            </ul>
          </div>
          
          <div class="code-block">
            <pre><code>/* 双列布局示例 */
.resume-template {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

.left-column {
    order: 1;
}

.right-column {
    order: 2;
}

/* 网格布局示例 */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* 时间轴布局示例 */
.timeline {
    position: relative;
    padding-left: 1.5rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #3498db;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 0.5rem;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #3498db;
}</code></pre>
          </div>
        </div>

        <!-- 响应式样式规范 -->
        <div class="requirement-box">
          <h3>响应式样式规范</h3>
          
          <div class="responsive-rules">
            <h4>断点设置：</h4>
            <ul>
              <li><strong>桌面端</strong>：1024px及以上 - 完整双列布局</li>
              <li><strong>平板端</strong>：768px-1023px - 简化双列或单列布局</li>
              <li><strong>手机端</strong>：767px及以下 - 单列布局，字号调整</li>
            </ul>
          </div>
          
          <div class="code-block">
            <pre><code>/* 响应式布局 */
@media (max-width: 1023px) {
    .resume-template {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 767px) {
    .resume-template {
        padding: 1rem;
        font-size: 14px;
    }
    
    .field-title {
        font-size: 1rem;
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
    
    .timeline {
        padding-left: 1rem;
    }
}

/* 打印样式优化 */
@media print {
    .resume-template {
        grid-template-columns: 1fr;
        font-size: 12px;
        line-height: 1.4;
    }
    
    .timeline::before,
    .timeline-item::before {
        display: none;
    }
    
    section {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}</code></pre>
          </div>
        </div>
      </section>

      <!-- 7. 完整示例 -->
      <section id="examples" class="spec-section">
        <h2 class="section-title">8. 完整示例</h2>
        
        <div class="requirement-box">
          <h3>完整的HTML简历模板（包含所有18个标准模块）</h3>
          <p class="template-description">
            这是一个完整的基础HTML简历模板，包含了所有18个标准模块和完整的字段绑定。
            其他HTML模板可以基于此模板修改样式和布局。
          </p>
          <div class="code-block full-example">
            <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;标准简历模板&lt;/title&gt;
    &lt;style&gt;
        /* === CSS变量系统 - 用户可配置的样式参数 === */
        :root {
            /* === 字体设置 === */
            --resume-font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; /* 全局字体族 */
            --resume-font-size: 14px;              /* 基础字体大小 */
            --resume-line-height: 1.6;             /* 行高，影响文本可读性 */
            
            /* === 颜色设置 === */
            --resume-text-color: #333333;          /* 主要文本颜色 */
            --resume-primary-color: #3498db;       /* 主色调，用于强调元素（标题、链接等） */
            --resume-secondary-color: #666666;     /* 次要文本颜色，用于辅助信息（日期、地址等） */
            
            /* === 页面边距设置 === */
            --resume-margin-top: 60px;             /* 页面上边距 */
            --resume-margin-bottom: 60px;          /* 页面下边距 */
            --resume-margin-left: 60px;            /* 页面左边距 */
            --resume-margin-right: 60px;           /* 页面右边距 */
            
            /* === 模块间距设置 === */
            --resume-module-spacing: 24px;         /* 模块之间的垂直间距（如教育经历、工作经历之间） */
            --resume-item-spacing: 16px;           /* 同一模块内条目之间的间距（如多个工作经历之间） */
            --resume-paragraph-spacing: 12px;      /* 段落间距，用于小的文本块分隔 */
            --resume-column-gap: 40px;             /* 多列布局时的列间距 */
            
            /* === 内边距设置 === */
            --resume-module-padding: 16px;         /* 模块内部的填充空间 */
            --resume-item-padding: 12px;           /* 条目内部的填充空间 */
            
            /* === 兼容性变量（旧版本支持，新模板请使用上述resume-前缀变量） === */
            --font-family: var(--resume-font-family);
            --font-size: var(--resume-font-size);
            --line-height: var(--resume-line-height);
            --text-color: var(--resume-text-color);
            --primary-color: var(--resume-primary-color);
            --secondary-color: var(--resume-secondary-color);
            --margin-top: var(--resume-margin-top);
            --margin-bottom: var(--resume-margin-bottom);
            --margin-side: var(--resume-margin-left);
            --module-spacing: var(--resume-module-spacing);
            --item-spacing: var(--resume-item-spacing);
            --paragraph-spacing: var(--resume-paragraph-spacing);
            --module-padding: var(--resume-module-padding);
            --item-padding: var(--resume-item-padding);
        }

        /* === 基础布局样式 === */
        .resume-template {
            /* 推荐使用：--resume-margin-top, --resume-margin-left, --resume-margin-bottom */
            padding: var(--margin-top) var(--margin-side) var(--margin-bottom);
            /* 推荐使用：--resume-font-family, --resume-font-size, --resume-line-height, --resume-text-color */
            font-family: var(--font-family);
            font-size: var(--font-size);
            line-height: var(--line-height);
            color: var(--text-color);
        }
        
        /* === 基本信息模块样式 === */
        .basic-info-section {
            text-align: center;
            margin-bottom: var(--module-spacing);
            padding: var(--module-padding);
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto var(--item-spacing);
            object-fit: cover;
        }
        
        .name {
            font-size: calc(var(--font-size) + 6px);
            font-weight: 700;
            margin-bottom: var(--paragraph-spacing);
            color: var(--text-color);
        }

        .expected-position {
            font-size: calc(var(--font-size) + 2px);
            color: var(--primary-color);
            margin-bottom: var(--item-spacing);
            font-weight: 500;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--paragraph-spacing);
            text-align: left;
            margin-bottom: var(--item-spacing);
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: var(--paragraph-spacing);
            padding: var(--item-padding);
        }

        .contact-label {
            font-weight: 600;
            color: var(--secondary-color);
            min-width: 80px;
        }

        .contact-value {
            color: var(--text-color);
        }
        
        /* === 通用模块样式 === */
        /* === 通用模块样式 === */
        .section {
            margin-bottom: var(--module-spacing);
        }
        
        .section-title {
            font-size: calc(var(--font-size) + 4px);
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: var(--item-spacing);
        }

        /* === 列表项通用样式 === */
        .list-item {
            margin-bottom: var(--item-spacing);
            padding: var(--item-padding);
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--paragraph-spacing);
            flex-wrap: wrap;
            gap: var(--paragraph-spacing);
        }
        
        .item-title {
            font-weight: 600;
            color: var(--text-color);
            font-size: calc(var(--font-size) + 1px);
        }

        .item-subtitle {
            color: var(--secondary-color);
            font-weight: 500;
            margin-top: 4px;
        }
        
        .item-date {
            color: var(--secondary-color);
            font-size: var(--font-size);
            font-weight: 500;
        }

        .item-content {
            color: var(--text-color);
            line-height: var(--line-height);
        }
        
        /* === 技能模块样式 === */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--item-spacing);
        }
        
        .skill-item {
            padding: var(--item-padding);
            text-align: center;
        }

        .skill-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--paragraph-spacing);
        }

        .skill-level {
            color: var(--primary-color);
            font-size: var(--font-size);
            font-weight: 500;
        }

        .skill-progress {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin-top: var(--paragraph-spacing);
            overflow: hidden;
        }

        .skill-progress-bar {
            height: 100%;
            background: var(--primary-color);
            border-radius: 4px;
        }

        /* === 简单列表样式 === */
        .simple-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--paragraph-spacing);
        }

        .simple-item {
            padding: var(--item-padding);
        }

        /* === 文本内容样式 === */
        .text-content {
            padding: var(--item-padding);
            line-height: var(--line-height);
        }

        /* === 响应式设计 === */
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
            }

            .item-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }

            .simple-list {
                grid-template-columns: 1fr;
            }
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="resume-template" data-module-root="true"&gt;
        
        &lt;!-- 1. 基本信息模块 (必需) --&gt;
        &lt;section class="basic-info-section" data-module="basic_info"&gt;
            &lt;!-- 头像：可选显示 --&gt;
            &lt;img class="avatar" data-field="photo" data-vue-src="resumeData.basic_info.photo" 
                 data-vue-if="resumeData.basic_info.photo"
                 src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjhGOUZBIi8+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNDAiIHI9IjIwIiBmaWxsPSIjNkM3NTdEIi8+CjxwYXRoIGQ9Ik0yMCA5MEM0MCA3MCA4MCA3MCAxMDAgOTBWMTIwSDIwVjkwWiIgZmlsbD0iIzZDNzU3RCIvPgo8L3N2Zz4=" alt="头像" /&gt;
            
            &lt;!-- 核心信息：始终显示 --&gt;
            &lt;h1 class="name" data-field="name" data-vue-text="resumeData.basic_info.name"&gt;张三&lt;/h1&gt;
            &lt;div class="expected-position" data-field="expectedPosition" data-vue-text="resumeData.basic_info.expectedPosition"&gt;软件工程师&lt;/div&gt;
            
            &lt;div class="contact-grid"&gt;
                &lt;!-- 核心联系方式：始终显示 --&gt;
                &lt;div class="contact-item"&gt;
                    &lt;span class="contact-label"&gt;电话：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="phone" data-vue-text="resumeData.basic_info.phone"&gt;138-0000-0000&lt;/span&gt;
            &lt;/div&gt;
                &lt;div class="contact-item"&gt;
                    &lt;span class="contact-label"&gt;邮箱：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="email" data-vue-text="resumeData.basic_info.email"&gt;<EMAIL>&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 可选基本信息：有数据才显示 --&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.age"&gt;
                    &lt;span class="contact-label"&gt;年龄：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="age" data-vue-text="resumeData.basic_info.age"&gt;28岁&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.currentCity"&gt;
                    &lt;span class="contact-label"&gt;现居：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="currentCity" data-vue-text="resumeData.basic_info.currentCity"&gt;北京市&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.intendedCity"&gt;
                    &lt;span class="contact-label"&gt;意向：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="intendedCity" data-vue-text="resumeData.basic_info.intendedCity"&gt;上海市&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.address"&gt;
                    &lt;span class="contact-label"&gt;地址：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="address" data-vue-text="resumeData.basic_info.address"&gt;北京市朝阳区&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.currentStatus"&gt;
                    &lt;span class="contact-label"&gt;状态：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="currentStatus" data-vue-text="resumeData.basic_info.currentStatus"&gt;在职寻新&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.expectedSalary"&gt;
                    &lt;span class="contact-label"&gt;薪资：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="expectedSalary" data-vue-text="resumeData.basic_info.expectedSalary"&gt;15-20K&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 社交信息：有数据才显示 --&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.wechat"&gt;
                    &lt;span class="contact-label"&gt;微信：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="wechat" data-vue-text="resumeData.basic_info.wechat"&gt;zhangsan_wx&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.website"&gt;
                    &lt;span class="contact-label"&gt;网站：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="website" data-vue-text="resumeData.basic_info.website"&gt;https://zhangsan.dev&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.github"&gt;
                    &lt;span class="contact-label"&gt;GitHub：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="github" data-vue-text="resumeData.basic_info.github"&gt;https://github.com/zhangsan&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 详细个人信息：有数据才显示 --&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.gender"&gt;
                    &lt;span class="contact-label"&gt;性别：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="gender" data-vue-text="resumeData.basic_info.gender"&gt;男&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.height"&gt;
                    &lt;span class="contact-label"&gt;身高：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="height" data-vue-text="resumeData.basic_info.height"&gt;175cm&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.weight"&gt;
                    &lt;span class="contact-label"&gt;体重：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="weight" data-vue-text="resumeData.basic_info.weight"&gt;70kg&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.politicalStatus"&gt;
                    &lt;span class="contact-label"&gt;政治面貌：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="politicalStatus" data-vue-text="resumeData.basic_info.politicalStatus"&gt;群众&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.maritalStatus"&gt;
                    &lt;span class="contact-label"&gt;婚姻状况：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="maritalStatus" data-vue-text="resumeData.basic_info.maritalStatus"&gt;未婚&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.hometown"&gt;
                    &lt;span class="contact-label"&gt;籍贯：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="hometown" data-vue-text="resumeData.basic_info.hometown"&gt;山东济南&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="contact-item" data-vue-if="resumeData.basic_info.ethnicity"&gt;
                    &lt;span class="contact-label"&gt;民族：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="ethnicity" data-vue-text="resumeData.basic_info.ethnicity"&gt;汉族&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 自定义社交信息：有数据才显示 --&gt;
                &lt;div class="contact-item" data-vue-for="social in resumeData.basic_info.customSocials" data-vue-key="social.id"
                     data-vue-if="resumeData.basic_info.customSocials && resumeData.basic_info.customSocials.length > 0"&gt;
                    &lt;span class="contact-label" data-field="label" data-vue-text="social.label"&gt;LinkedIn：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="value" data-vue-text="social.value"&gt;linkedin.com/in/zhangsan&lt;/span&gt;
                &lt;/div&gt;
                
                &lt;!-- 自定义信息：有数据才显示 --&gt;
                &lt;div class="contact-item" data-vue-for="info in resumeData.basic_info.customInfos" data-vue-key="info.id"
                     data-vue-if="resumeData.basic_info.customInfos && resumeData.basic_info.customInfos.length > 0"&gt;
                    &lt;span class="contact-label" data-field="label" data-vue-text="info.label"&gt;驾照：&lt;/span&gt;
                    &lt;span class="contact-value" data-field="value" data-vue-text="info.value"&gt;C1&lt;/span&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 2. 工作经历模块 --&gt;
        &lt;section class="section" data-module="work_experience" data-vue-if="resumeData.work_experience && resumeData.work_experience.length > 0"&gt;
            &lt;h2 class="section-title"&gt;工作经历&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.work_experience" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class="item-title" data-field="company" data-vue-text="item.company"&gt;ABC科技有限公司&lt;/div&gt;
                        &lt;div class="item-subtitle"&gt;
                        &lt;span data-field="position" data-vue-text="item.position"&gt;高级前端工程师&lt;/span&gt;
                            &lt;!-- 可选字段：有数据才显示 --&gt;
                            &lt;span data-vue-if="item.department"&gt; · &lt;/span&gt;
                            &lt;span data-field="department" data-vue-text="item.department" data-vue-if="item.department"&gt;技术部&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date"&gt;
                        &lt;span data-field="startDate" data-vue-text="item.startDate" data-vue-if="item.startDate"&gt;2020-03&lt;/span&gt;
                        &lt;span data-vue-if="item.startDate && item.endDate"&gt; ~ &lt;/span&gt;
                        &lt;span data-field="endDate" data-vue-text="item.endDate" data-vue-if="item.endDate"&gt;2024-01&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- 工作描述：可选字段 --&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description"&gt;
                    负责公司核心产品的前端开发工作，使用Vue.js、React等技术栈构建高质量的用户界面。
                    参与产品需求分析、技术方案设计、代码开发、测试和上线等全流程工作。
                    优化页面性能，提升用户体验，并负责团队技术培训和代码评审工作。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 3. 教育经历模块 --&gt;
        &lt;section class="section" data-module="education" data-vue-if="resumeData.education && resumeData.education.length > 0"&gt;
            &lt;h2 class="section-title"&gt;教育经历&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.education" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class="item-title" data-field="school" data-vue-text="item.school"&gt;北京理工大学&lt;/div&gt;
                        &lt;div class="item-subtitle"&gt;
                            &lt;span data-field="major" data-vue-text="item.major"&gt;计算机科学与技术&lt;/span&gt;
                            &lt;!-- 可选字段：有数据才显示 --&gt;
                            &lt;span data-vue-if="item.degree"&gt; · &lt;/span&gt;
                            &lt;span data-field="degree" data-vue-text="item.degree" data-vue-if="item.degree"&gt;本科&lt;/span&gt;
                            &lt;span data-vue-if="item.customDegree"&gt; · &lt;/span&gt;
                            &lt;span data-field="customDegree" data-vue-text="item.customDegree" data-vue-if="item.customDegree"&gt;工学学士&lt;/span&gt;
                            &lt;span data-vue-if="item.gpa"&gt; · GPA: &lt;/span&gt;
                            &lt;span data-field="gpa" data-vue-text="item.gpa" data-vue-if="item.gpa"&gt;3.8&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date"&gt;
                        &lt;span data-field="startDate" data-vue-text="item.startDate" data-vue-if="item.startDate"&gt;2016-09&lt;/span&gt;
                        &lt;span data-vue-if="item.startDate && item.endDate"&gt; ~ &lt;/span&gt;
                        &lt;span data-field="endDate" data-vue-text="item.endDate" data-vue-if="item.endDate"&gt;2020-06&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- 教育描述：可选字段 --&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description"&gt;
                    主修计算机科学核心课程，包括数据结构与算法、操作系统、计算机网络、数据库系统等。
                    参与多个课程项目和实践活动，培养了扎实的编程基础和系统设计能力。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 4. 项目经历模块 --&gt;
        &lt;section class="section" data-module="project" data-vue-if="resumeData.project && resumeData.project.length > 0"&gt;
            &lt;h2 class="section-title"&gt;项目经历&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.project" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class="item-title" data-field="name" data-vue-text="item.name"&gt;电商管理后台系统&lt;/div&gt;
                        &lt;div class="item-subtitle"&gt;
                            &lt;!-- 可选字段：有数据才显示 --&gt;
                            &lt;span data-field="role" data-vue-text="item.role" data-vue-if="item.role"&gt;技术负责人&lt;/span&gt;
                            &lt;span data-vue-if="item.role && item.company"&gt; · &lt;/span&gt;
                            &lt;span data-field="company" data-vue-text="item.company" data-vue-if="item.company"&gt;ABC科技有限公司&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date"&gt;
                        &lt;span data-field="startDate" data-vue-text="item.startDate" data-vue-if="item.startDate"&gt;2022-06&lt;/span&gt;
                        &lt;span data-vue-if="item.startDate && item.endDate"&gt; ~ &lt;/span&gt;
                        &lt;span data-field="endDate" data-vue-text="item.endDate" data-vue-if="item.endDate"&gt;2023-12&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- 项目描述：可选字段 --&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description"&gt;
                    负责电商管理后台系统的前端架构设计与开发，采用Vue3 + TypeScript + Element Plus技术栈。
                    实现了商品管理、订单处理、用户管理、数据统计等核心功能模块。
                    通过组件化开发和代码分割优化，显著提升了系统性能和开发效率。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 5. 技能特长模块 --&gt;
        &lt;section class="section" data-module="skills" data-vue-if="resumeData.skills && resumeData.skills.length > 0"&gt;
            &lt;h2 class="section-title"&gt;技能特长&lt;/h2&gt;
            &lt;div class="skills-grid"&gt;
                &lt;div class="skill-item" data-vue-for="skill in resumeData.skills" data-vue-key="skill.id"&gt;
                    &lt;!-- 核心字段：始终显示 --&gt;
                    &lt;div class="skill-name" data-field="name" data-vue-text="skill.name"&gt;JavaScript&lt;/div&gt;
                    &lt;!-- 可选字段：有数据才显示 --&gt;
                    &lt;div class="skill-level" data-field="level" data-vue-text="skill.level" data-vue-if="skill.level"&gt;90%&lt;/div&gt;
                    &lt;div class="skill-proficiency" data-field="proficiency" data-vue-text="skill.proficiency" data-vue-if="skill.proficiency"&gt;精通&lt;/div&gt;
                    &lt;div class="skill-progress" data-vue-if="skill.displayMode && skill.level"&gt;
                        &lt;div class="skill-progress-bar" data-field="displayMode" :style="{ width: skill.displayMode === 'percentage' ? skill.level + '%' : '0%' }"&gt;&lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;
        
        &lt;!-- 6. 获奖情况模块 --&gt;
        &lt;section class="section" data-module="award" data-vue-if="resumeData.award && resumeData.award.length > 0"&gt;
            &lt;h2 class="section-title"&gt;获奖情况&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.award" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class="item-title" data-field="name" data-vue-text="item.name"&gt;优秀员工奖&lt;/div&gt;
                        &lt;div class="item-subtitle"&gt;
                            &lt;span data-field="organization" data-vue-text="item.organization"&gt;ABC科技有限公司&lt;/span&gt;
                            &lt;span data-vue-if="item.level"&gt; · &lt;/span&gt;
                            &lt;span data-field="level" data-vue-text="item.level"&gt;公司级&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date" data-field="date" data-vue-text="item.date"&gt;2023-12&lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description"&gt;
                    因在项目开发中表现突出，技术能力强，团队协作能力佳，获得公司年度优秀员工奖励。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 7. 证书资质模块 --&gt;
        &lt;section class="section" data-module="certificate" data-vue-if="resumeData.certificate && resumeData.certificate.length > 0"&gt;
            &lt;h2 class="section-title"&gt;证书资质&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.certificate" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;div class="item-title" data-field="name" data-vue-text="item.name"&gt;软件设计师&lt;/div&gt;
                        &lt;div class="item-subtitle"&gt;
                            &lt;span data-field="organization" data-vue-text="item.organization"&gt;中国计算机技术职业资格网&lt;/span&gt;
                            &lt;span data-vue-if="item.number"&gt; · 证书编号: &lt;/span&gt;
                            &lt;span data-field="number" data-vue-text="item.number"&gt;SD202312345&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date" data-field="date" data-vue-text="item.date"&gt;2023-05&lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description"&gt;
                    通过国家软件设计师资格考试，具备软件系统分析、设计和开发的专业能力。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 8. 语言能力模块 --&gt;
        &lt;section class="section" data-module="language" data-vue-if="resumeData.language && resumeData.language.length > 0"&gt;
            &lt;h2 class="section-title"&gt;语言能力&lt;/h2&gt;
            &lt;div class="simple-list"&gt;
                &lt;div class="simple-item" data-vue-for="item in resumeData.language" data-vue-key="item.id"&gt;
                    &lt;!-- 核心字段：始终显示 --&gt;
                    &lt;div class="item-title" data-field="name" data-vue-text="item.name"&gt;英语&lt;/div&gt;
                    &lt;div class="item-subtitle"&gt;
                        &lt;!-- 可选字段：有数据才显示 --&gt;
                        &lt;span data-field="level" data-vue-text="item.level" data-vue-if="item.level"&gt;熟练&lt;/span&gt;
                        &lt;span data-vue-if="item.level && item.certificate"&gt; · &lt;/span&gt;
                        &lt;span data-field="certificate" data-vue-text="item.certificate" data-vue-if="item.certificate"&gt;CET-6&lt;/span&gt;
                    &lt;/div&gt;
                    &lt;!-- 语言描述：可选字段 --&gt;
                    &lt;div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description"&gt;
                        具备良好的英语读写能力，能够阅读英文技术文档，与外国同事进行日常交流。
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 9. 实习经历模块 --&gt;
        &lt;section class="section" data-module="internship" data-vue-if="resumeData.internship && resumeData.internship.length > 0"&gt;
            &lt;h2 class="section-title"&gt;实习经历&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.internship" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;!-- 核心字段：始终显示 --&gt;
                        &lt;div class="item-title" data-field="company" data-vue-text="item.company"&gt;腾讯科技有限公司&lt;/div&gt;
                        &lt;div class="item-subtitle" data-field="position" data-vue-text="item.position"&gt;前端开发实习生&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date"&gt;
                        &lt;!-- 可选字段：有数据才显示 --&gt;
                        &lt;span data-field="startDate" data-vue-text="item.startDate" data-vue-if="item.startDate"&gt;2019-07&lt;/span&gt;
                        &lt;span data-vue-if="item.startDate && item.endDate"&gt; ~ &lt;/span&gt;
                        &lt;span data-field="endDate" data-vue-text="item.endDate" data-vue-if="item.endDate"&gt;2019-09&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- 实习描述：可选字段 --&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description" data-vue-if="item.description"&gt;
                    在微信事业群实习，参与微信小程序开发工具的前端开发工作。
                    学习并实践了React技术栈，参与了代码重构和性能优化项目。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 10. 志愿经历模块 --&gt;
        &lt;section class="section" data-module="volunteer_experience" data-vue-if="resumeData.volunteer_experience && resumeData.volunteer_experience.length > 0"&gt;
            &lt;h2 class="section-title"&gt;志愿经历&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.volunteer_experience" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;div class="item-title" data-field="organization" data-vue-text="item.organization"&gt;北京市红十字会&lt;/div&gt;
                        &lt;div class="item-subtitle"&gt;
                            &lt;span data-field="role" data-vue-text="item.role"&gt;志愿者&lt;/span&gt;
                            &lt;span data-vue-if="item.location"&gt; · &lt;/span&gt;
                            &lt;span data-field="location" data-vue-text="item.location"&gt;北京市朝阳区&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date"&gt;
                        &lt;span data-field="startDate" data-vue-text="item.startDate"&gt;2018-06&lt;/span&gt; ~ 
                        &lt;span data-field="endDate" data-vue-text="item.endDate"&gt;2020-06&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description"&gt;
                    参与社区志愿服务活动，为老人提供技术支持，教授智能手机和电脑的基本使用方法。
                    累计志愿服务时长超过100小时，获得优秀志愿者称号。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 11. 论文发表模块 --&gt;
        &lt;section class="section" data-module="publication" data-vue-if="resumeData.publication && resumeData.publication.length > 0"&gt;
            &lt;h2 class="section-title"&gt;论文发表&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.publication" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;div class="item-title" data-field="title" data-vue-text="item.title"&gt;基于深度学习的图像识别算法研究&lt;/div&gt;
                        &lt;div class="item-subtitle"&gt;
                            &lt;span data-field="type" data-vue-text="item.type"&gt;学术论文&lt;/span&gt; · 
                            &lt;span data-field="journal" data-vue-text="item.journal"&gt;《计算机工程》&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date" data-field="date" data-vue-text="item.date"&gt;2020-03&lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description"&gt;
                    第一作者发表学术论文，研究基于卷积神经网络的图像分类算法，
                    在CIFAR-10数据集上取得了较好的实验结果。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 12. 科研经历模块 --&gt;
        &lt;section class="section" data-module="research_experience" data-vue-if="resumeData.research_experience && resumeData.research_experience.length > 0"&gt;
            &lt;h2 class="section-title"&gt;科研经历&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.research_experience" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;div class="item-title" data-field="topic" data-vue-text="item.topic"&gt;机器学习在图像处理中的应用&lt;/div&gt;
                        &lt;div class="item-subtitle"&gt;
                            &lt;span data-field="role" data-vue-text="item.role"&gt;主要研究员&lt;/span&gt; · 
                            &lt;span data-field="organization" data-vue-text="item.organization"&gt;北京理工大学计算机学院&lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date"&gt;
                        &lt;span data-field="startDate" data-vue-text="item.startDate"&gt;2019-03&lt;/span&gt; ~ 
                        &lt;span data-field="endDate" data-vue-text="item.endDate"&gt;2020-06&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description"&gt;
                    参与导师的科研项目，研究深度学习算法在图像识别和处理中的应用。
                    负责算法实现、实验设计和数据分析，取得了较好的研究成果。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 13. 培训经历模块 --&gt;
        &lt;section class="section" data-module="training" data-vue-if="resumeData.training && resumeData.training.length > 0"&gt;
            &lt;h2 class="section-title"&gt;培训经历&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.training" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;div class="item-title" data-field="name" data-vue-text="item.name"&gt;React高级开发培训&lt;/div&gt;
                        &lt;div class="item-subtitle" data-field="organization" data-vue-text="item.organization"&gt;极客时间&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date"&gt;
                        &lt;span data-field="startDate" data-vue-text="item.startDate"&gt;2021-06&lt;/span&gt; ~ 
                        &lt;span data-field="endDate" data-vue-text="item.endDate"&gt;2021-08&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="item-content" data-field="description" data-vue-text="item.description"&gt;
                    系统学习React高级特性，包括Hooks、Context、性能优化等内容。
                    完成了多个实战项目，提升了React开发能力。
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 14. 作品集模块 --&gt;
        &lt;section class="section" data-module="portfolio" data-vue-if="resumeData.portfolio && resumeData.portfolio.length > 0"&gt;
            &lt;h2 class="section-title"&gt;作品集&lt;/h2&gt;
            &lt;div class="list-item" data-vue-for="item in resumeData.portfolio" data-vue-key="item.id"&gt;
                &lt;div class="item-header"&gt;
                    &lt;div&gt;
                        &lt;div class="item-title" data-field="title" data-vue-text="item.title"&gt;个人博客网站&lt;/div&gt;
                        &lt;div class="item-subtitle" data-field="type" data-vue-text="item.type"&gt;网站开发&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="item-date" data-field="date" data-vue-text="item.date"&gt;2023-01&lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="item-content"&gt;
                    &lt;p data-field="description" data-vue-text="item.description"&gt;
                        使用Nuxt.js开发的个人博客网站，支持文章发布、评论、搜索等功能。
                        采用响应式设计，支持多终端访问。
                    &lt;/p&gt;
                    &lt;p data-vue-if="item.link"&gt;
                        &lt;strong&gt;链接：&lt;/strong&gt;
                        &lt;a href="#" data-field="link" data-vue-text="item.link" data-vue-href="item.link"&gt;https://zhangsan.blog&lt;/a&gt;
                    &lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 15. 兴趣爱好模块 --&gt;
        &lt;section class="section" data-module="hobbies" data-vue-if="resumeData.hobbies && resumeData.hobbies.length > 0"&gt;
            &lt;h2 class="section-title"&gt;兴趣爱好&lt;/h2&gt;
            &lt;div class="simple-list"&gt;
                &lt;div class="simple-item" data-vue-for="hobby in resumeData.hobbies" data-vue-key="hobby.id"&gt;
                    &lt;div class="item-title" data-field="name" data-vue-text="hobby.name"&gt;摄影&lt;/div&gt;
                    &lt;div class="item-content" data-field="description" data-vue-text="hobby.description"&gt;
                        热爱摄影，擅长风景和人像摄影，具有较好的审美能力和创意思维。
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 16. 自我评价模块 --&gt;
        &lt;section class="section" data-module="self_evaluation" data-vue-if="resumeData.self_evaluation && resumeData.self_evaluation.content"&gt;
            &lt;h2 class="section-title"&gt;自我评价&lt;/h2&gt;
            &lt;div class="text-content" data-field="content" data-vue-text="resumeData.self_evaluation.content"&gt;
                本人性格开朗，工作认真负责，具有较强的学习能力和团队协作精神。
                在前端开发领域有着扎实的技术基础和丰富的项目经验，
                能够快速适应新技术和新环境，具备良好的问题解决能力。
                希望能够在新的工作岗位上发挥自己的专业技能，与团队共同成长。
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 17. 自荐信模块 --&gt;
        &lt;section class="section" data-module="cover_letter" data-vue-if="resumeData.cover_letter && resumeData.cover_letter.content"&gt;
            &lt;h2 class="section-title"&gt;自荐信&lt;/h2&gt;
            &lt;div class="text-content" data-field="content" data-vue-text="resumeData.cover_letter.content"&gt;
                尊敬的招聘负责人，您好！我是一名有着4年前端开发经验的工程师，
                在看到贵公司的招聘信息后，我对这个职位非常感兴趣。
                我具备扎实的技术基础和丰富的项目经验，相信能够为贵公司的发展贡献自己的力量。
                期待能有机会与您面谈，详细介绍我的技能和经验。谢谢！
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- 18. 自定义模块 --&gt;
        &lt;section class="section" data-module="custom_1704067200000" data-vue-if="customModule && customModule.content"&gt;
            &lt;h2 class="section-title" data-field="title" data-vue-text="customModule.title"&gt;其他信息&lt;/h2&gt;
            &lt;div class="text-content" data-field="content" data-vue-text="customModule.content"&gt;
                这里可以添加任何自定义的内容，比如特殊经历、补充说明等。
                自定义模块的ID格式为 custom_ + 时间戳，确保唯一性。
            &lt;/div&gt;
        &lt;/section&gt;
        
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
          </div>
        </div>
      </section>

      <!-- 8. 验证清单 -->
      <section id="validation" class="spec-section">
        <h2 class="section-title">9. 验证清单</h2>
        
        <!-- 基础文档结构验证 -->
        <div class="requirement-box">
          <h3>基础文档结构验证</h3>
          
          <div class="checklist">
            <h4>文档结构检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> DOCTYPE声明正确</li>
              <li><input type="checkbox"> 字符编码设置为UTF-8</li>
              <li><input type="checkbox"> 包含完整的html、head、body标签</li>
              <li><input type="checkbox"> 根容器包含class="resume-template"</li>
              <li><input type="checkbox"> 根容器包含data-module-root="true"</li>
              <li><input type="checkbox"> title标签包含简历标题</li>
              <li><input type="checkbox"> meta viewport标签设置正确</li>
            </ul>
            
            <h4>样式检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 包含CSS样式在style标签内</li>
              <li><input type="checkbox"> 定义了.resume-template根样式</li>
              <li><input type="checkbox"> 使用相对单位（rem、em、%）</li>
              <li><input type="checkbox"> 包含响应式设计断点</li>
              <li><input type="checkbox"> 包含打印样式优化</li>
              <li><input type="checkbox"> 字体设置为系统字体或Web安全字体</li>
            </ul>
          </div>
        </div>

        <!-- 模块定义验证 -->
        <div class="requirement-box">
          <h3>模块定义验证</h3>
          
          <div class="checklist">
            <h4>模块结构检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 每个模块都有data-module属性</li>
              <li><input type="checkbox"> 模块名称使用规范的值（18个标准模块）</li>
              <li><input type="checkbox"> 必须包含basic_info模块</li>
              <li><input type="checkbox"> 模块ID命名正确（大部分单数，skills和hobbies复数）</li>
              <li><input type="checkbox"> 自定义模块ID格式为custom_[时间戳]</li>
              <li><input type="checkbox"> 每个模块包含在section标签内</li>
            </ul>
            
            <h4>模块完整性检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 包含的模块数量合理（建议3-8个主要模块）</li>
              <li><input type="checkbox"> 模块顺序符合简历逻辑</li>
              <li><input type="checkbox"> 各模块间有明确的视觉分隔</li>
              <li><input type="checkbox"> 模块标题清晰明确</li>
            </ul>
          </div>
        </div>

        <!-- 字段绑定验证 -->
        <div class="requirement-box">
          <h3>字段绑定验证</h3>
          
          <div class="checklist">
            <h4>基本绑定检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 每个字段都有data-field属性</li>
              <li><input type="checkbox"> 每个字段都有data-vue-text属性</li>
              <li><input type="checkbox"> 表达式格式正确</li>
              <li><input type="checkbox"> 循环列表使用data-vue-for</li>
              <li><input type="checkbox"> 循环项使用data-vue-key</li>
              <li><input type="checkbox"> 图片字段使用data-vue-src</li>
            </ul>
            
            <h4>表达式正确性检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 基本信息使用resumeData.basic_info.{字段名}</li>
              <li><input type="checkbox"> 循环模块使用item.{字段名}</li>
              <li><input type="checkbox"> 技能模块使用skill.{字段名}</li>
              <li><input type="checkbox"> 兴趣爱好使用hobby.{字段名}</li>
              <li><input type="checkbox"> 自我评价使用resumeData.self_evaluation.content</li>
              <li><input type="checkbox"> 自荐信使用resumeData.cover_letter.content</li>
            </ul>
            
            <h4>条件渲染检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 非必需模块添加了data-vue-if模块级条件</li>
              <li><input type="checkbox"> 可选字段添加了data-vue-if字段级条件</li>
              <li><input type="checkbox"> 核心字段（如姓名、学校名）不加条件</li>
              <li><input type="checkbox"> 数组型模块条件：resumeData.module && resumeData.module.length > 0</li>
              <li><input type="checkbox"> 文本型模块条件：resumeData.module && resumeData.module.content</li>
              <li><input type="checkbox"> 基本字段条件：resumeData.basic_info.字段名</li>
              <li><input type="checkbox"> 循环项字段条件：item.字段名</li>
              <li><input type="checkbox"> 时间分隔符条件：item.startDate && item.endDate</li>
            </ul>
          </div>
        </div>

        <!-- 各模块字段验证 -->
        <div class="requirement-box">
          <h3>各模块字段完整性验证</h3>
          
          <div class="checklist">
            <h4>基本信息模块（basic_info）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 包含name字段（必需）</li>
              <li><input type="checkbox"> 包含phone字段（必需）</li>
              <li><input type="checkbox"> 包含email字段（必需）</li>
              <li><input type="checkbox"> 包含photo、age、currentCity、intendedCity等扩展字段</li>
              <li><input type="checkbox"> 支持自定义社交信息customSocials</li>
              <li><input type="checkbox"> 支持自定义信息customInfos</li>
            </ul>
            
            <h4>工作经历模块（work_experience）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.work_experience"</li>
              <li><input type="checkbox"> 包含company、position、startDate、endDate字段</li>
              <li><input type="checkbox"> 包含department字段</li>
              <li><input type="checkbox"> 包含description字段</li>
              <li><input type="checkbox"> 字段绑定正确（item.字段名）</li>
            </ul>
            
            <h4>教育经历模块（education）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.education"</li>
              <li><input type="checkbox"> 包含school、major、degree字段</li>
              <li><input type="checkbox"> 包含startDate、endDate字段</li>
              <li><input type="checkbox"> 包含customDegree字段（自定义学历）</li>
              <li><input type="checkbox"> 包含description字段</li>
            </ul>
            
            <h4>项目经历模块（project）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.project"</li>
              <li><input type="checkbox"> 包含name、role、company字段</li>
              <li><input type="checkbox"> 包含startDate、endDate字段</li>
              <li><input type="checkbox"> 包含description字段</li>
            </ul>
            
            <h4>技能特长模块（skills）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="skill in resumeData.skills"</li>
              <li><input type="checkbox"> 包含name字段（skill.name）</li>
              <li><input type="checkbox"> 包含level字段（skill.level）</li>
              <li><input type="checkbox"> 包含proficiency字段（skill.proficiency）</li>
              <li><input type="checkbox"> 包含displayMode字段（skill.displayMode）</li>
            </ul>
            
            <h4>语言能力模块（language）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.language"</li>
              <li><input type="checkbox"> 包含name、level字段</li>
              <li><input type="checkbox"> 包含certificate、description字段</li>
            </ul>
            
            <h4>获奖情况模块（award）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.award"</li>
              <li><input type="checkbox"> 包含name、date、organization字段</li>
              <li><input type="checkbox"> 包含level、description字段</li>
            </ul>
            
            <h4>证书资质模块（certificate）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.certificate"</li>
              <li><input type="checkbox"> 包含name、date、organization字段</li>
              <li><input type="checkbox"> 包含number、description字段</li>
            </ul>
            
            <h4>实习经历模块（internship）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.internship"</li>
              <li><input type="checkbox"> 包含company、position字段</li>
              <li><input type="checkbox"> 包含startDate、endDate、description字段</li>
            </ul>
            
            <h4>志愿经历模块（volunteer_experience）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.volunteer_experience"</li>
              <li><input type="checkbox"> 包含organization、role、location字段</li>
              <li><input type="checkbox"> 包含startDate、endDate、description字段</li>
            </ul>
            
            <h4>论文发表模块（publication）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.publication"</li>
              <li><input type="checkbox"> 包含title、type、journal字段</li>
              <li><input type="checkbox"> 包含date、description字段</li>
            </ul>
            
            <h4>科研经历模块（research_experience）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.research_experience"</li>
              <li><input type="checkbox"> 包含topic、role、organization字段</li>
              <li><input type="checkbox"> 包含startDate、endDate、description字段</li>
            </ul>
            
            <h4>培训经历模块（training）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.training"</li>
              <li><input type="checkbox"> 包含course、company字段</li>
              <li><input type="checkbox"> 包含startDate、endDate、description字段</li>
            </ul>
            
            <h4>作品集模块（portfolio）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.portfolio"</li>
              <li><input type="checkbox"> 包含title、type、url字段</li>
              <li><input type="checkbox"> 包含date、description字段</li>
            </ul>
            
            <h4>兴趣爱好模块（hobbies）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-vue-for="hobby in resumeData.hobbies"</li>
              <li><input type="checkbox"> 包含name字段（hobby.name）</li>
              <li><input type="checkbox"> 包含level、description字段</li>
            </ul>
            
            <h4>自我评价模块（self_evaluation）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-field="content"</li>
              <li><input type="checkbox"> 使用data-vue-text="resumeData.self_evaluation.content"</li>
              <li><input type="checkbox"> 不使用循环结构</li>
            </ul>
            
            <h4>自荐信模块（cover_letter）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 使用data-field="content"</li>
              <li><input type="checkbox"> 使用data-vue-text="resumeData.cover_letter.content"</li>
              <li><input type="checkbox"> 不使用循环结构</li>
            </ul>
            
            <h4>自定义模块（custom_*）：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 模块ID格式为custom_[时间戳]</li>
              <li><input type="checkbox"> 使用data-vue-for="item in resumeData.custom_*.items"</li>
              <li><input type="checkbox"> 包含name、role字段</li>
              <li><input type="checkbox"> 包含startDate、endDate、content字段</li>
            </ul>
          </div>
        </div>

        <!-- 数据绑定一致性验证 -->
        <div class="requirement-box">
          <h3>数据绑定一致性验证</h3>
          
          <div class="checklist">
            <h4>循环变量检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 技能模块使用"skill"变量名</li>
              <li><input type="checkbox"> 兴趣爱好模块使用"hobby"变量名</li>
              <li><input type="checkbox"> 其他模块使用"item"变量名</li>
              <li><input type="checkbox"> 所有循环都包含data-vue-key="变量.id"</li>
            </ul>
            
            <h4>字段命名检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 所有字段使用camelCase命名</li>
              <li><input type="checkbox"> 时间字段命名为startDate、endDate</li>
              <li><input type="checkbox"> 描述字段命名为description</li>
              <li><input type="checkbox"> 自定义字段数组命名正确</li>
            </ul>
          </div>
        </div>

        <!-- CSS变量使用验证 -->
        <div class="requirement-box">
          <h3>CSS变量使用验证</h3>
          
          <div class="checklist">
            <h4>基础CSS变量检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 页面布局使用了margin变量（--resume-margin-*或--margin-*）</li>
              <li><input type="checkbox"> 字体样式使用了font变量（--resume-font-*或--font-*）</li>
              <li><input type="checkbox"> 文本颜色使用了color变量（--resume-text-color或--text-color）</li>
              <li><input type="checkbox"> 主色调使用了primary变量（--resume-primary-color或--primary-color）</li>
              <li><input type="checkbox"> 次要颜色使用了secondary变量（--resume-secondary-color或--secondary-color）</li>
            </ul>
            
            <h4>间距CSS变量检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 模块间距使用了spacing变量（--resume-module-spacing或--module-spacing）</li>
              <li><input type="checkbox"> 条目间距使用了spacing变量（--resume-item-spacing或--item-spacing）</li>
              <li><input type="checkbox"> 段落间距使用了spacing变量（--resume-paragraph-spacing或--paragraph-spacing）</li>
              <li><input type="checkbox"> 内边距使用了padding变量（--resume-*-padding或--*-padding）</li>
            </ul>
            
            <h4>CSS变量规范性检查：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 没有硬编码的样式值（字体大小、颜色、间距等）</li>
              <li><input type="checkbox"> 推荐使用带resume-前缀的新版变量名</li>
              <li><input type="checkbox"> 计算值正确使用calc()函数</li>
              <li><input type="checkbox"> 变量名拼写正确，无语法错误</li>
              <li><input type="checkbox"> CSS变量在:root选择器中正确定义</li>
            </ul>
          </div>
        </div>

        <!-- 兼容性和性能验证 -->
        <div class="requirement-box">
          <h3>兼容性和性能验证</h3>
          
          <div class="checklist">
            <h4>浏览器兼容性：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> CSS属性兼容主流浏览器</li>
              <li><input type="checkbox"> 避免使用实验性CSS特性</li>
              <li><input type="checkbox"> 提供必要的CSS前缀</li>
              <li><input type="checkbox"> 字体回退方案完整</li>
            </ul>
            
            <h4>性能优化：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> CSS代码简洁高效</li>
              <li><input type="checkbox"> 避免过度嵌套选择器</li>
              <li><input type="checkbox"> 合理使用CSS动画和过渡</li>
              <li><input type="checkbox"> 图片尺寸和格式优化</li>
            </ul>
            
            <h4>可访问性：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 文字颜色对比度符合标准</li>
              <li><input type="checkbox"> 字体大小适合阅读</li>
              <li><input type="checkbox"> 链接有明确的视觉标识</li>
              <li><input type="checkbox"> 结构语义化正确</li>
            </ul>
          </div>
        </div>

        <!-- 最终质量验证 -->
        <div class="requirement-box">
          <h3>最终质量验证</h3>
          
          <div class="checklist">
            <h4>转换测试：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 在转换系统中测试成功</li>
              <li><input type="checkbox"> 所有字段正确显示数据</li>
              <li><input type="checkbox"> 循环渲染正常工作</li>
              <li><input type="checkbox"> 样式在预览中正确应用</li>
            </ul>
            
            <h4>多场景测试：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 空数据情况处理正确</li>
              <li><input type="checkbox"> 大量数据情况下布局正常</li>
              <li><input type="checkbox"> 不同模块组合下显示正常</li>
              <li><input type="checkbox"> 导出PDF格式正确</li>
            </ul>
            
            <h4>用户体验：</h4>
            <ul class="checklist-items">
              <li><input type="checkbox"> 整体布局美观协调</li>
              <li><input type="checkbox"> 信息层次清晰</li>
              <li><input type="checkbox"> 视觉重点突出</li>
              <li><input type="checkbox"> 符合简历阅读习惯</li>
            </ul>
          </div>
        </div>
      </section>

    </div>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  middleware: 'admin-simple',
  layout: 'admin'
})

// 滚动到指定位置
const scrollTo = (elementId) => {
  const element = document.getElementById(elementId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 下载完整基础模板
const downloadTemplate = () => {
  // 从页面的完整示例中提取HTML内容
  const fullExampleElement = document.querySelector('.full-example pre code')
  if (!fullExampleElement) {
    console.error('无法找到完整示例模板')
    return
  }
  
  // 获取完整的HTML模板内容
  let templateContent = fullExampleElement.textContent || fullExampleElement.innerText
  
  // 解码HTML实体
  templateContent = templateContent
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'")

  const blob = new Blob([templateContent], { type: 'text/html; charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'resume-base-template.html'
  a.click()
  URL.revokeObjectURL(url)
  
  // 提示下载成功
  console.log('完整基础模板已下载：resume-base-template.html')
}

// 复制规范链接
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(window.location.href)
    // 这里可以添加成功提示
    console.log('链接已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
  }
}
</script>

<style scoped>
.html-specification {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.header-content h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 28px;
  color: #3498db;
}

.page-description {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.template-description {
  margin: 0 0 15px 0;
  padding: 12px 16px;
  background: #e8f4fd;
  border-left: 4px solid #3498db;
  border-radius: 4px;
  color: #2c3e50;
  font-size: 14px;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  transition: all 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.table-of-contents {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.table-of-contents h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.toc-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.toc-list li {
  margin-bottom: 8px;
}

.toc-list a {
  color: #3498db;
  text-decoration: none;
  padding: 5px 0;
  display: block;
  transition: color 0.2s;
}

.toc-list a:hover {
  color: #2980b9;
  text-decoration: underline;
}

.specification-content {
  line-height: 1.6;
}

.spec-section {
  margin-bottom: 50px;
}

.section-title {
  color: #2c3e50;
  font-size: 24px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #3498db;
}

.requirement-box {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.requirement-box h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.requirement-box h4 {
  margin: 20px 0 10px 0;
  color: #34495e;
  font-size: 16px;
}

.code-block {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin: 15px 0;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
}

.full-example .code-block {
  max-height: 600px;
  overflow-y: auto;
}

.requirements-list ul,
.binding-rules ul,
.style-rules ul {
  margin: 15px 0;
  padding-left: 20px;
}

.requirements-list li,
.binding-rules li,
.style-rules li {
  margin-bottom: 8px;
}

.requirements-list strong,
.binding-rules strong,
.style-rules strong {
  color: #e74c3c;
}

.module-table table,
.expression-table table,
.field-table table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  font-size: 14px;
}

.module-table th,
.expression-table th,
.field-table th,
.module-table td,
.expression-table td,
.field-table td {
  padding: 12px;
  text-align: left;
  border: 1px solid #dee2e6;
}

.module-table th,
.expression-table th,
.field-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.module-table tbody tr:nth-child(even),
.expression-table tbody tr:nth-child(even),
.field-table tbody tr:nth-child(even) {
  background: #f8f9fa;
}

.required {
  color: #e74c3c;
  font-weight: 600;
}

.optional {
  color: #27ae60;
  font-weight: 600;
}

.module-spec {
  margin-bottom: 40px;
}

.module-spec h3 {
  color: #2c3e50;
  font-size: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: #ecf0f1;
  border-radius: 6px;
}

.code-example {
  margin-top: 20px;
}

.code-example h4 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.checklist {
  margin-top: 20px;
}

.checklist h4 {
  margin: 25px 0 10px 0;
  color: #2c3e50;
}

.checklist-items {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.checklist-items li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.checklist-items input[type="checkbox"] {
  margin-right: 10px;
}

code {
  background: #f8f9fa;
  color: #e74c3c;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

/* 模块总览样式 */
.modules-overview {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
  margin: 20px 0;
}

.module-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.module-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.module-item.required {
  border-color: #dc3545;
  background: #fff5f5;
}

.module-item.required:hover {
  border-color: #dc3545;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
}

.module-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #495057;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  min-width: 140px;
  text-align: center;
}

.module-name {
  flex: 1;
  text-align: center;
  font-weight: 500;
  color: #343a40;
}

.module-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.module-item.required .module-status {
  background: #dc3545;
  color: white;
}

.module-item:not(.required) .module-status {
  background: #28a745;
  color: white;
}

.modules-note {
  margin-top: 16px;
  padding: 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  font-size: 14px;
  color: #856404;
}

.modules-note code {
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #e83e8c;
}

.note-box {
  background: #F3F4F6;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.note-box p {
  margin: 0;
  color: #374151;
  font-size: 14px;
}
</style> 