package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模板上传响应DTO
 * 
 * @description 返回模板上传的结果信息
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "TemplateUploadResponse", description = "模板上传响应结果")
public class TemplateUploadResponse {

    /**
     * 模板ID
     * 新创建的模板记录ID
     */
    @Schema(description = "模板ID", example = "1001")
    private Long templateId;

    /**
     * 模板名称
     * 创建的模板名称
     */
    @Schema(description = "模板名称", example = "商务简约模板")
    private String templateName;

    /**
     * 模板代码
     * 系统分配的模板代码
     */
    @Schema(description = "模板代码", example = "business-simple")
    private String templateCode;

    /**
     * 上传成功的文件列表
     * 包含所有成功上传的文件信息
     */
    @Schema(description = "上传成功的文件列表")
    private List<UploadedFileInfo> uploadedFiles;

    /**
     * 上传失败的文件列表
     * 包含上传失败的文件及失败原因
     */
    @Schema(description = "上传失败的文件列表")
    private List<FailedFileInfo> failedFiles;

    /**
     * 总文件数量
     * 本次上传尝试的文件总数
     */
    @Schema(description = "总文件数量", example = "5")
    private Integer totalFiles;

    /**
     * 成功上传数量
     * 成功上传的文件数量
     */
    @Schema(description = "成功上传数量", example = "4")
    private Integer successCount;

    /**
     * 失败上传数量
     * 上传失败的文件数量
     */
    @Schema(description = "失败上传数量", example = "1")
    private Integer failureCount;

    /**
     * 主模板文件ID
     * 指向主要模板文件的file_upload_records记录ID
     */
    @Schema(description = "主模板文件ID", example = "2001")
    private Long mainTemplateFileId;

    /**
     * 模板内容ID
     * 指向模板默认内容的resume_template_content记录ID
     */
    @Schema(description = "模板内容ID", example = "3001")
    private Long contentId;

    /**
     * 其他文件URL列表
     * 其他辅助文件的访问URL列表
     */
    @Schema(description = "其他文件URL列表")
    private List<String> otherFileUrls;

    /**
     * 预览图URL
     * 模板预览图的访问URL
     */
    @Schema(description = "预览图URL", example = "https://minio.example.com/resume-template/preview/template-001.jpg")
    private String previewImageUrl;

    /**
     * 上传完成时间
     * 整个上传过程完成的时间
     */
    @Schema(description = "上传完成时间")
    private LocalDateTime uploadTime;

    /**
     * 上传用户ID
     * 执行上传操作的管理员用户ID
     */
    @Schema(description = "上传用户ID", example = "1")
    private Long uploadUserId;

    /**
     * 存储桶名称
     * 文件存储的MinIO桶名称
     */
    @Schema(description = "存储桶名称", example = "resume-template")
    private String bucketName;

    /**
     * 存储路径前缀
     * 文件在桶中的路径前缀
     */
    @Schema(description = "存储路径前缀", example = "/templates/business-simple")
    private String storagePath;

    /**
     * 上传成功的文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "UploadedFileInfo", description = "上传成功的文件信息")
    public static class UploadedFileInfo {

        /**
         * 文件记录ID
         * file_upload_records表中的记录ID
         */
        @Schema(description = "文件记录ID", example = "2001")
        private Long fileId;

        /**
         * 原始文件名
         * 用户上传时的文件名
         */
        @Schema(description = "原始文件名", example = "template.vue")
        private String originalName;

        /**
         * 存储文件名
         * 在MinIO中的实际文件名
         */
        @Schema(description = "存储文件名", example = "business-simple-template.vue")
        private String fileName;

        /**
         * 文件类型
         * MIME类型
         */
        @Schema(description = "文件类型", example = "text/plain")
        private String fileType;

        /**
         * 文件大小
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）", example = "15360")
        private Long fileSize;

        /**
         * 文件URL
         * 可访问的文件URL
         */
        @Schema(description = "文件URL", example = "https://minio.example.com/resume-template/templates/business-simple/template.vue")
        private String fileUrl;

        /**
         * 对象键
         * MinIO中的完整对象路径
         */
        @Schema(description = "对象键", example = "templates/business-simple/template.vue")
        private String objectKey;
    }

    /**
     * 上传失败的文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "FailedFileInfo", description = "上传失败的文件信息")
    public static class FailedFileInfo {

        /**
         * 原始文件名
         * 用户上传时的文件名
         */
        @Schema(description = "原始文件名", example = "invalid-file.txt")
        private String originalName;

        /**
         * 失败原因
         * 具体的失败错误信息
         */
        @Schema(description = "失败原因", example = "文件类型不支持")
        private String failureReason;

        /**
         * 错误代码
         * 标准化的错误代码
         */
        @Schema(description = "错误代码", example = "UNSUPPORTED_FILE_TYPE")
        private String errorCode;

        /**
         * 文件大小
         * 失败文件的大小（如果能获取到）
         */
        @Schema(description = "文件大小（字节）", example = "0")
        private Long fileSize;
    }
} 