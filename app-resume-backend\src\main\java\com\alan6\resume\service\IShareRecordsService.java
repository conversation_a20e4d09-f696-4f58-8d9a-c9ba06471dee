package com.alan6.resume.service;

import com.alan6.resume.dto.share.ShareAccessRequest;
import com.alan6.resume.dto.share.ShareCreateRequest;
import com.alan6.resume.dto.share.ShareResponse;
import com.alan6.resume.entity.ShareRecords;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 分享记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IShareRecordsService extends IService<ShareRecords> {

    /**
     * 创建简历分享链接
     *
     * @param resumeId 简历ID
     * @param request  创建请求
     * @param userId   用户ID
     * @return 分享响应
     */
    ShareResponse createResumeShare(Long resumeId, ShareCreateRequest request, Long userId);

    /**
     * 创建模板分享链接
     *
     * @param templateId 模板ID
     * @param request    创建请求
     * @param userId     用户ID
     * @return 分享响应
     */
    ShareResponse createTemplateShare(Long templateId, ShareCreateRequest request, Long userId);

    /**
     * 访问分享内容
     *
     * @param shareCode 分享码
     * @param request   访问请求
     * @return 分享内容
     */
    Map<String, Object> accessShare(String shareCode, ShareAccessRequest request);

    /**
     * 获取分享记录列表
     *
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     * @param current      当前页
     * @param size         每页大小
     * @param userId       用户ID
     * @return 分享记录分页
     */
    Page<ShareRecords> getShareRecords(String resourceType, Long resourceId, Integer current, Integer size, Long userId);

    /**
     * 更新分享配置
     *
     * @param shareId 分享ID
     * @param request 更新请求
     * @param userId  用户ID
     * @return 是否成功
     */
    Boolean updateShareConfig(Long shareId, ShareCreateRequest request, Long userId);

    /**
     * 删除分享链接
     *
     * @param shareId 分享ID
     * @param userId  用户ID
     * @return 是否成功
     */
    Boolean deleteShare(Long shareId, Long userId);

    /**
     * 批量删除分享链接
     *
     * @param shareIds 分享ID列表
     * @param userId   用户ID
     * @return 删除结果
     */
    Map<String, Object> batchDeleteShares(List<Long> shareIds, Long userId);

    /**
     * 获取分享统计信息
     *
     * @param resourceType 资源类型
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param granularity  时间粒度
     * @param userId       用户ID
     * @return 统计信息
     */
    Map<String, Object> getShareStatistics(String resourceType, LocalDateTime startDate, 
                                          LocalDateTime endDate, String granularity, Long userId);

    /**
     * 获取分享详情
     *
     * @param shareId 分享ID
     * @param userId  用户ID
     * @return 分享详情
     */
    ShareRecords getShareDetail(Long shareId, Long userId);

    /**
     * 切换分享状态
     *
     * @param shareId  分享ID
     * @param isActive 是否激活
     * @param userId   用户ID
     * @return 是否成功
     */
    Boolean toggleShareStatus(Long shareId, Boolean isActive, Long userId);

    /**
     * 预览分享内容（不计入访问次数）
     *
     * @param shareCode 分享码
     * @param password  访问密码（可选）
     * @return 预览信息
     */
    Map<String, Object> previewShare(String shareCode, String password);

    /**
     * 验证分享访问权限
     *
     * @param shareCode 分享码
     * @param password  访问密码（可选）
     * @return 验证结果
     */
    Map<String, Object> verifyShareAccess(String shareCode, String password);

    /**
     * 获取分享访问日志
     *
     * @param shareId   分享ID
     * @param current   当前页
     * @param size      每页大小
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param userId    用户ID
     * @return 访问日志分页
     */
    Page<Map<String, Object>> getShareAccessLogs(Long shareId, Integer current, Integer size, 
                                                  LocalDateTime startDate, LocalDateTime endDate, Long userId);

    /**
     * 批量操作分享状态
     *
     * @param shareIds  分享ID列表
     * @param isActive  目标状态
     * @param userId    用户ID
     * @return 操作结果
     */
    Map<String, Object> batchToggleShareStatus(List<Long> shareIds, Boolean isActive, Long userId);

    /**
     * 批量设置过期时间
     *
     * @param shareIds   分享ID列表
     * @param expireTime 过期时间
     * @param userId     用户ID
     * @return 操作结果
     */
    Map<String, Object> batchSetExpireTime(List<Long> shareIds, LocalDateTime expireTime, Long userId);

    /**
     * 获取热门分享
     *
     * @param resourceType 资源类型
     * @param period       统计周期
     * @param limit        返回数量限制
     * @param userId       用户ID
     * @return 热门分享列表
     */
    Map<String, Object> getTrendingShares(String resourceType, String period, Integer limit, Long userId);
}
