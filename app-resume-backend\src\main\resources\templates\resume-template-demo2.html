<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{姓名}}的简历</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            background-color: #f5f5f5;
        }

        .resume-container {
            max-width: 210mm;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 297mm;
            display: flex;
        }

        /* 左侧个人信息区域 */
        .left-column {
            width: 35%;
            background: #2dd4bf;
            color: white;
            padding: 40px 30px;
            position: relative;
        }

        .avatar-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border: 3px solid white;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .avatar-icon {
            width: 80px;
            height: 80px;
            fill: white;
        }

        .name {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .section {
            margin-bottom: 35px;
        }

        .section-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 8px 12px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            position: relative;
        }

        .section-title::before {
            content: '■';
            margin-right: 8px;
            color: #fbbf24;
        }

        .section-content {
            font-size: 0.9em;
            line-height: 1.6;
        }

        .info-item {
            margin-bottom: 8px;
        }

        .info-label {
            font-weight: bold;
            margin-right: 5px;
        }

        /* 技能进度条 */
        .skill-item {
            margin-bottom: 15px;
        }

        .skill-name {
            margin-bottom: 5px;
            font-size: 0.9em;
        }

        .skill-bar {
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: #fbbf24;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 右侧内容区域 */
        .right-column {
            width: 65%;
            padding: 40px 35px;
            background: white;
        }

        .right-section {
            margin-bottom: 35px;
        }

        .right-section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2dd4bf;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2dd4bf;
            position: relative;
        }

        .right-section-title::before {
            content: '■';
            margin-right: 10px;
            color: #2dd4bf;
        }

        /* 项目经历样式 */
        .experience-item {
            margin-bottom: 25px;
            padding-left: 20px;
            position: relative;
            border-left: 2px solid #e5e7eb;
        }

        .experience-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 5px;
            width: 10px;
            height: 10px;
            background: #2dd4bf;
            border-radius: 50%;
        }

        .project-header {
            margin-bottom: 10px;
        }

        .project-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .project-meta {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 10px;
        }

        .project-tech {
            display: inline-block;
            background: #e0f2fe;
            color: #0891b2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-right: 8px;
            margin-bottom: 5px;
        }

        .project-description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        /* 公司经历样式 */
        .company-item {
            margin-bottom: 25px;
            padding-left: 20px;
            position: relative;
            border-left: 2px solid #e5e7eb;
        }

        .company-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 5px;
            width: 10px;
            height: 10px;
            background: #2dd4bf;
            border-radius: 50%;
        }

        .company-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .company-description {
            color: #555;
            line-height: 1.6;
        }

        /* 项目截图 */
        .project-image {
            width: 120px;
            height: 80px;
            background: #f3f4f6;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #e5e7eb;
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
            }
            
            .resume-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
                width: 100%;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .resume-container {
                flex-direction: column;
                margin: 0;
            }
            
            .left-column,
            .right-column {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 左侧个人信息区域 -->
        <aside class="left-column">
            <!-- 头像和姓名 -->
            <div class="avatar-section">
                <div class="avatar">
                    <svg class="avatar-icon" viewBox="0 0 24 24">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21ZM14 19H16L18.5 21.5L19.92 20.08L18 18.16V15H16V18.16L14.08 20.08L15.5 21.5L16 21H14V19Z"/>
                    </svg>
                </div>
                <div class="name">{{姓名}}</div>
            </div>

            <!-- 基本信息 -->
            <div class="section">
                <div class="section-title">Basic info. 基本信息</div>
                <div class="section-content">
                    <div class="info-item">
                        <span class="info-label">个人信息:</span>{{姓名}} / {{性别}} / {{年龄}}岁 / {{学历}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">学校:</span>{{毕业学校}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">专业:</span>{{专业}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">英语水平:</span>{{英语水平}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">手机:</span>{{手机号码}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">Blog:</span>{{博客地址}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">GitHub:</span>{{GitHub地址}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">在线简历:</span>{{在线简历地址}}
                    </div>
                </div>
            </div>

            <!-- 联系方式 -->
            <div class="section">
                <div class="section-title">Contact. 联系方式</div>
                <div class="section-content">
                    <div class="info-item">
                        <span class="info-label">📧Email:</span>{{邮箱}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">📱QQ:</span>{{QQ号}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">📞PhoneNumber:</span>{{电话号码}}
                    </div>
                </div>
            </div>

            <!-- 应聘岗位 -->
            <div class="section">
                <div class="section-title">Application. 应聘岗位</div>
                <div class="section-content">
                    {{应聘岗位}}
                </div>
            </div>

            <!-- 主要技能点 -->
            <div class="section">
                <div class="section-title">Tech. 主要技能点</div>
                <div class="section-content">
                    <div class="skill-item">
                        <div class="skill-name">HTML/CSS</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 90%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">JavaScript</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 85%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">PHP</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 75%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Git</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 80%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Node.js</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 70%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 右侧内容区域 -->
        <main class="right-column">
            <!-- 技能清单 -->
            <section class="right-section">
                <h2 class="right-section-title">Skill. 技能清单</h2>
                <div class="section-content">
                    <p>• 熟练使用 HTML + CSS 编写语义化的代码，完成数据的布局，熟练Stylus，PostCss 等css预处理，后处理工具</p>
                    <p>• 在Blog项目中熟练了 Vue，写过Vue的双向数据绑定Demo，使用了 ES6 的新语法，了解 Node.js, jQuery.</p>
                    <p>• 熟悉 Git Webpack Gulp 等开发工具</p>
                    <p>• 熟悉 Koa, Express, MongoDB, Mysql 能搭建简单的后端项目，租了Digital Ocean 的VPS，搭建了云端网站</p>
                </div>
            </section>

            <!-- 项目经验 -->
            <section class="right-section">
                <h2 class="right-section-title">Experience. 项目经验</h2>
                
                <div class="project-header">
                    <h3 style="font-size: 1.2em; margin-bottom: 20px;">个人项目</h3>
                </div>

                <div class="experience-item">
                    <div class="project-name">nunjucks-extend-loader 2017.2 
                        <span class="project-tech">SourceCode</span>
                    </div>
                    <div class="project-description">
                        开发了一个多页应用脚手架Webjucks，并发布到npm上，自己写了个loader，已发布在npm上。
                        用 Nodejs保持通过的辅助不存在的目录，最后通过回调函数来完成最后一步copy文件的动作。
                    </div>
                </div>

                <div class="experience-item">
                    <div class="project-name">Resume-it 2017.2 
                        <span class="project-tech">SourceCode</span>
                    </div>
                    <div class="project-description">
                        如字面上看大家的简历，感觉都很复杂，但个人简历应该不复杂的，所以就做了一个数据模板分离的简历
                        制作网站，这样制作简历就能够完全数据化，各个人员就能够对应自己的简历对象。
                        最初是用webpack打包的，后来换成了 Gulp，简单易用，不必上webpack，后面打算改造成页面可
                        以直接修改的。
                    </div>
                </div>

                <div class="experience-item">
                    <div class="project-name">Blog 2017.4-至今 
                        <span class="project-tech">SourceCode</span>
                    </div>
                    <div class="project-description">
                        还是参考vue-hacknews2.0对前端部分做了一次重构，新增了的webpack打包体积好小，实现了
                        深圳加班css，服务器部分也上了各备Docker，正在写dockerfile...
                        <br>
                        这是一个全栈应用 Node2 + Vue2 + MongoDB + Redis 搭建的 前后端分离 + RESTful API +
                        SSR 的简易系统，并且进行了性能优化，如热点数据浏览器缓存。
                    </div>
                </div>
            </section>

            <!-- 实习经历 -->
            <section class="right-section">
                <h2 class="right-section-title">实习经历</h2>

                <div class="company-item">
                    <div class="company-name">腾讯</div>
                    <div class="company-description">
                        在TEG部发展管理部TAPD中心实习，主要做前端方面的实际需求，解决bug等工作。
                        技术需求上，改进过内部的一个手机号验证组件，实现了多实例，状态条件等功能，在代码质量也
                        做了优化，代码更加规范化应用。
                        <br>
                        业务需求上，在看到现有图标需求对应时，回归代代码对装备的照顾件，并且可以对更多个实例，
                        实现代码的复用。
                        <br>
                        在解决bug的过程中，也积累了不少经验，比如有些下的background：rgba来实现透明，通过
                        google的filter解决的。
                        <br>
                        在开发的过程中，我会留意各种实现方案的好的，因为的产品也包含了我以及其他做故障的一个细节，在略
                        试的人生第一次的比工流，我很喜欢ok。
                    </div>
                </div>

                <div class="company-item">
                    <div class="company-name">WPNinja</div>
                    <div class="company-description">
                        这是一个面向国外客户的远程开发团队，基于wordpress搭建网站，前五个月我负责前端的开
                        发，最大程度的还原设计图稿，完成页面交互，后面开始做后端PHP开发。
                    </div>
                </div>

                <div class="company-item">
                    <div class="project-name">SSRGA 2016.7 
                        <span class="project-tech">Demo</span>
                    </div>
                    <div class="project-image"></div>
                    <div class="company-description">
                        这是我接触的第一个项目，为了做应用的很复杂的页面快速学习了模板
                        引擎Nunjucks，CSS预处理Stylus，数据层YAML语法等等。
                        <br>
                        然后在实践中发现，最终一个网左右实现了这个项目的前端部分。
                    </div>
                </div>

                <div class="company-item">
                    <div class="project-name">Clubfit 2016.8 
                        <span class="project-tech">Demo</span>
                    </div>
                    <div class="project-image"></div>
                    <div class="company-description">
                        接手这个项目时已经有了设计图的框架比较完善，这个项目也包括了很多页
                        面组件，整体实现比较复杂，各式的代码leader来review，我用相似的做出修
                        改，整个过程下来收获很多，算一次破冰。
                    </div>
                </div>

                <div class="company-item">
                    <div class="project-name">taldumande 2016.9 
                        <span class="project-tech">Demo</span>
                    </div>
                    <div class="project-image"></div>
                    <div class="company-description">
                        这是目前自己已经能够全栈立完成的，设计也有很多有趣的地方，像
                        header，footer部分两种颜色，为了不影响其他页面也比较方便网页完善
                        法，没有复杂的地方，细节很多需要的能力。
                    </div>
                </div>

                <div class="company-item">
                    <div class="project-name">workforceprofessionals 2016.10 
                        <span class="project-tech">Demo</span>
                    </div>
                    <div class="project-image"></div>
                    <div class="company-description">
                        这个项目是教育培训，前面leader需要交流了一下需要做的用法，研究了两
                        三天大体了解了基于wordpress的后端的工作方式，于是用边边改改的方式完
                        成了这个项目，我做的最是项目实际支付的详细，数据输入人员数等等。
                        <br>
                        另外，首页的教育培训，提供与的，之前我试过tweet的方法需要各种key
                        审阅过有问题，我查了好久找到一个插件叫Twitter-Post-Fetcher，使用简单。
                    </div>
                </div>
            </section>
        </main>
    </div>
</body>
</html>