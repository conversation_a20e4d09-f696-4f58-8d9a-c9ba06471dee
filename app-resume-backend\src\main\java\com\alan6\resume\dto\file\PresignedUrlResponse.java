package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 预签名URL响应DTO
 * 
 * 主要功能：
 * 1. 返回生成的预签名URL信息
 * 2. 包含URL有效期和HTTP方法信息
 * 3. 提供URL状态检查功能
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "预签名URL响应", description = "预签名URL生成结果")
public class PresignedUrlResponse {

    /**
     * 预签名URL
     * 生成的临时访问URL
     */
    @Schema(description = "预签名URL", example = "https://minio.example.com/avatar-bucket/avatar/2024/12/22/avatar_20241222123456.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...")
    private String presignedUrl;

    /**
     * HTTP方法
     * 预签名URL支持的HTTP方法
     */
    @Schema(description = "HTTP方法", example = "GET")
    private String method;

    /**
     * 过期时间
     * 预签名URL的过期时间
     */
    @Schema(description = "过期时间", example = "2024-12-22 16:30:00")
    private LocalDateTime expiresAt;

    /**
     * 有效期（秒）
     * 从当前时间开始的有效期秒数
     */
    @Schema(description = "有效期（秒）", example = "3600")
    private Integer expiresIn;

    /**
     * 文件ID
     * 关联的文件ID
     */
    @Schema(description = "文件ID", example = "F20241222123456789")
    private String fileId;

    /**
     * 文件名
     * 关联的文件名
     */
    @Schema(description = "文件名", example = "avatar_20241222123456.jpg")
    private String fileName;

    /**
     * 检查预签名URL是否已过期
     * 
     * @return true-已过期，false-未过期
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    /**
     * 检查预签名URL是否即将过期（5分钟内）
     * 
     * @return true-即将过期，false-未即将过期
     */
    public boolean isExpiringSoon() {
        return expiresAt != null && 
               expiresAt.isBefore(LocalDateTime.now().plusMinutes(5));
    }

    /**
     * 获取剩余有效时间（秒）
     * 
     * @return 剩余有效时间，如果已过期返回0
     */
    public long getRemainingSeconds() {
        if (expiresAt == null) {
            return 0;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (expiresAt.isBefore(now)) {
            return 0;
        }
        
        return java.time.Duration.between(now, expiresAt).getSeconds();
    }

    /**
     * 获取剩余有效时间的可读格式
     * 
     * @return 剩余有效时间字符串
     */
    public String getRemainingTimeFormatted() {
        long seconds = getRemainingSeconds();
        
        if (seconds <= 0) {
            return "已过期";
        }
        
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分钟";
        } else {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            return hours + "小时" + (minutes > 0 ? minutes + "分钟" : "");
        }
    }

    /**
     * 检查是否为GET方法的预签名URL
     * 
     * @return true-GET方法，false-其他方法
     */
    public boolean isGetMethod() {
        return "GET".equalsIgnoreCase(method);
    }

    /**
     * 检查是否为PUT方法的预签名URL
     * 
     * @return true-PUT方法，false-其他方法
     */
    public boolean isPutMethod() {
        return "PUT".equalsIgnoreCase(method);
    }

    /**
     * 检查是否为POST方法的预签名URL
     * 
     * @return true-POST方法，false-其他方法
     */
    public boolean isPostMethod() {
        return "POST".equalsIgnoreCase(method);
    }

    /**
     * 检查是否为DELETE方法的预签名URL
     * 
     * @return true-DELETE方法，false-其他方法
     */
    public boolean isDeleteMethod() {
        return "DELETE".equalsIgnoreCase(method);
    }

    /**
     * 创建预签名URL响应对象
     * 
     * @param presignedUrl 预签名URL
     * @param method HTTP方法
     * @param expiresAt 过期时间
     * @param expiresIn 有效期（秒）
     * @param fileId 文件ID
     * @param fileName 文件名
     * @return 预签名URL响应对象
     */
    public static PresignedUrlResponse create(String presignedUrl, String method, 
                                            LocalDateTime expiresAt, Integer expiresIn, 
                                            String fileId, String fileName) {
        return PresignedUrlResponse.builder()
                .presignedUrl(presignedUrl)
                .method(method)
                .expiresAt(expiresAt)
                .expiresIn(expiresIn)
                .fileId(fileId)
                .fileName(fileName)
                .build();
    }
} 