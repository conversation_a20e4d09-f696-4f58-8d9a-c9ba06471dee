package com.alan6.resume.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 缓存清理请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "CacheClearRequest", description = "缓存清理请求")
public class CacheClearRequest {

    /**
     * 缓存类型
     */
    @Schema(description = "缓存类型（all, user, resume, template, system）", example = "all")
    private String cacheType = "all";

    /**
     * 具体的缓存键
     */
    @Schema(description = "具体的缓存键（可选，支持通配符）", example = "[\"user:*\", \"resume:*\"]")
    private List<String> keys;
} 