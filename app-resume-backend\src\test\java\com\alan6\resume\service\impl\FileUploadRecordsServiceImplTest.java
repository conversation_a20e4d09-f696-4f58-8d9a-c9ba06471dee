package com.alan6.resume.service.impl;

import com.alan6.resume.config.MinioConfig;
import com.alan6.resume.dto.file.BucketCreateRequest;
import com.alan6.resume.dto.file.FileUploadRequest;
import com.alan6.resume.dto.file.PresignedUrlRequest;
import com.alan6.resume.entity.FileUploadRecords;
import com.alan6.resume.mapper.FileUploadRecordsMapper;
import io.minio.MinioClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FileUploadRecordsServiceImpl 测试类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class FileUploadRecordsServiceImplTest {

    @Mock
    private FileUploadRecordsMapper fileUploadRecordsMapper;

    @Mock
    private MinioClient minioClient;

    @Mock
    private MinioConfig.MinioProperties minioProperties;

    @Spy
    @InjectMocks
    private FileUploadRecordsServiceImpl fileUploadRecordsService;

    private FileUploadRecords testRecord;
    private FileUploadRequest uploadRequest;
    private MultipartFile testFile;
    private Long testUserId;

    @BeforeEach
    void setUp() throws Exception {
        testUserId = 1939974833202876418L;
        
        // 准备测试数据
        testRecord = new FileUploadRecords();
        testRecord.setId(1L);
        testRecord.setFileName("avatar_20241222123456.jpg");
        testRecord.setOriginalName("我的头像.jpg");
        testRecord.setFilePath("avatar/2024/12/22/avatar_20241222123456.jpg");
        testRecord.setBucketName("avatar-bucket");
        testRecord.setEtag("d41d8cd98f00b204e9800998ecf8427e");
        testRecord.setVersionId("v1.0.0");
        testRecord.setFileSize(102400L);
        testRecord.setFileType("image/jpeg");
        testRecord.setStorageClass("STANDARD");
        testRecord.setAccessPolicy("private");
        testRecord.setEncryptionType("SSE-S3");
        testRecord.setUserId(testUserId);
        testRecord.setIsDeleted((byte) 0);
        testRecord.setCreateTime(LocalDateTime.now());
        testRecord.setUpdateTime(LocalDateTime.now());

        // 准备上传请求
        uploadRequest = new FileUploadRequest();
        uploadRequest.setType("avatar");
        uploadRequest.setBucketName("avatar-bucket");

        // 准备测试文件
        testFile = new MockMultipartFile(
                "file",
                "test-avatar.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );

        // Mock MinioProperties基础配置
        when(minioProperties.getEndpoint()).thenReturn("http://**************:9000");
        when(minioProperties.getAccessKey()).thenReturn("admin");
        when(minioProperties.getSecretKey()).thenReturn("admin123");
        when(minioProperties.getBucketName()).thenReturn("default-bucket");
    }

    /**
     * 测试文件验证 - 头像文件成功
     */
    @Test
    void testValidateFile_AvatarSuccess() {
        // 创建测试文件（小于5MB）
        MockMultipartFile avatarFile = new MockMultipartFile(
                "file",
                "avatar.jpg",
                "image/jpeg",
                new byte[1024 * 1024] // 1MB
        );

        // 执行测试
        boolean result = fileUploadRecordsService.validateFile(avatarFile, "avatar");

        // 验证结果
        assertTrue(result);
    }

    /**
     * 测试文件验证 - 文件过大
     */
    @Test
    void testValidateFile_FileTooLarge() {
        // 创建测试文件（大于5MB）
        MockMultipartFile largeFile = new MockMultipartFile(
                "file",
                "large-avatar.jpg",
                "image/jpeg",
                new byte[6 * 1024 * 1024] // 6MB
        );

        // 执行测试
        boolean result = fileUploadRecordsService.validateFile(largeFile, "avatar");

        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试文件验证 - 不支持的文件类型
     */
    @Test
    void testValidateFile_UnsupportedType() {
        // 创建不支持的文件类型
        MockMultipartFile unsupportedFile = new MockMultipartFile(
                "file",
                "test.exe",
                "application/x-msdownload",
                new byte[1024]
        );

        // 执行测试
        boolean result = fileUploadRecordsService.validateFile(unsupportedFile, "avatar");

        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试生成唯一文件名
     */
    @Test
    void testGenerateUniqueFileName() {
        // 执行测试
        String result = fileUploadRecordsService.generateUniqueFileName("test.jpg", "avatar");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.startsWith("avatar_"));
        assertTrue(result.endsWith(".jpg"));
        assertTrue(result.length() > "avatar_.jpg".length());
    }

    /**
     * 测试根据文件类型获取存储桶名称
     */
    @Test
    void testGetBucketNameByFileType() {
        // 执行测试
        String avatarBucket = fileUploadRecordsService.getBucketNameByFileType("avatar");
        String resumeBucket = fileUploadRecordsService.getBucketNameByFileType("resume");
        String templateBucket = fileUploadRecordsService.getBucketNameByFileType("template");
        String documentBucket = fileUploadRecordsService.getBucketNameByFileType("document");
        String unknownBucket = fileUploadRecordsService.getBucketNameByFileType("unknown");

        // 验证结果
        assertEquals("avatar-bucket", avatarBucket);
        assertEquals("resume-bucket", resumeBucket);
        assertEquals("template-bucket", templateBucket);
        assertEquals("document-bucket", documentBucket);
        assertEquals("resume-exports", unknownBucket);
    }

    /**
     * 测试构建文件路径
     */
    @Test
    void testBuildFilePath() {
        // 执行测试
        String result = fileUploadRecordsService.buildFilePath("avatar", "test.jpg");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.startsWith("avatar/"));
        assertTrue(result.endsWith("/test.jpg"));
        // 验证包含日期路径
        assertTrue(result.matches("avatar/\\d{4}/\\d{2}/\\d{2}/test\\.jpg"));
    }

    /**
     * 测试文件上传请求验证 - 成功场景
     */
    @Test
    void testFileUploadRequest_Valid() {
        // 验证有效请求
        assertTrue(uploadRequest.isValid());
    }

    /**
     * 测试文件上传请求验证 - 无效场景
     */
    @Test
    void testFileUploadRequest_Invalid() {
        // 创建无效请求（缺少type）
        FileUploadRequest invalidRequest = new FileUploadRequest();
        invalidRequest.setBucketName("test-bucket");

        // 验证无效请求
        assertFalse(invalidRequest.isValid());
    }

    /**
     * 测试存储桶创建请求验证 - 成功场景
     */
    @Test
    void testBucketCreateRequest_Valid() {
        BucketCreateRequest request = new BucketCreateRequest();
        request.setName("test-bucket");
        request.setDescription("测试存储桶");

        // 验证有效请求
        assertTrue(request.isValid());
    }

    /**
     * 测试存储桶创建请求验证 - 无效场景
     */
    @Test
    void testBucketCreateRequest_Invalid() {
        BucketCreateRequest request = new BucketCreateRequest();
        request.setDescription("测试存储桶"); // 缺少name

        // 验证无效请求
        assertFalse(request.isValid());
    }

    /**
     * 测试预签名URL请求验证 - 成功场景
     */
    @Test
    void testPresignedUrlRequest_Valid() {
        PresignedUrlRequest request = new PresignedUrlRequest();
        request.setFileId("F20241222123456789");
        request.setMethod("GET");
        request.setExpiresIn(3600);

        // 验证有效请求
        assertTrue(request.isValid());
    }

    /**
     * 测试预签名URL请求验证 - 无效场景
     */
    @Test
    void testPresignedUrlRequest_Invalid() {
        PresignedUrlRequest request = new PresignedUrlRequest();
        request.setMethod("GET"); // 缺少fileId

        // 验证无效请求
        assertFalse(request.isValid());
    }

    /**
     * 测试不同文件类型的大小限制
     */
    @Test
    void testFileSizeLimits() {
        // 测试头像文件（5MB限制）
        MockMultipartFile avatarFile = new MockMultipartFile(
                "file", "avatar.jpg", "image/jpeg", new byte[4 * 1024 * 1024]); // 4MB
        assertTrue(fileUploadRecordsService.validateFile(avatarFile, "avatar"));

        // 测试简历文件（10MB限制）
        MockMultipartFile resumeFile = new MockMultipartFile(
                "file", "resume.pdf", "application/pdf", new byte[8 * 1024 * 1024]); // 8MB
        assertTrue(fileUploadRecordsService.validateFile(resumeFile, "resume"));

        // 测试模板文件（20MB限制）
        MockMultipartFile templateFile = new MockMultipartFile(
                "file", "template.html", "text/html", 
                new byte[15 * 1024 * 1024]); // 15MB
        assertTrue(fileUploadRecordsService.validateFile(templateFile, "template"));

        // 测试文档文件（50MB限制）
        MockMultipartFile documentFile = new MockMultipartFile(
                "file", "document.pdf", "application/pdf", new byte[40 * 1024 * 1024]); // 40MB
        assertTrue(fileUploadRecordsService.validateFile(documentFile, "document"));
    }

    /**
     * 测试文件类型验证
     */
    @Test
    void testFileTypeValidation() {
        // 测试支持的图片格式
        MockMultipartFile jpgFile = new MockMultipartFile(
                "file", "test.jpg", "image/jpeg", new byte[1024]);
        assertTrue(fileUploadRecordsService.validateFile(jpgFile, "avatar"));

        MockMultipartFile pngFile = new MockMultipartFile(
                "file", "test.png", "image/png", new byte[1024]);
        assertTrue(fileUploadRecordsService.validateFile(pngFile, "avatar"));

        // 测试支持的文档格式
        MockMultipartFile pdfFile = new MockMultipartFile(
                "file", "test.pdf", "application/pdf", new byte[1024]);
        assertTrue(fileUploadRecordsService.validateFile(pdfFile, "resume"));

        MockMultipartFile docxFile = new MockMultipartFile(
                "file", "test.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", 
                new byte[1024]);
        assertTrue(fileUploadRecordsService.validateFile(docxFile, "document"));

        // 测试不支持的格式
        MockMultipartFile exeFile = new MockMultipartFile(
                "file", "test.exe", "application/x-msdownload", new byte[1024]);
        assertFalse(fileUploadRecordsService.validateFile(exeFile, "avatar"));
    }

    /**
     * 测试MinioProperties配置验证
     */
    @Test
    void testMinioPropertiesValidation() {
        // 验证MinioProperties的Mock配置
        assertEquals("http://**************:9000", minioProperties.getEndpoint());
        assertEquals("admin", minioProperties.getAccessKey());
        assertEquals("admin123", minioProperties.getSecretKey());
        assertEquals("default-bucket", minioProperties.getBucketName());
    }

    /**
     * 测试文件上传记录实体类
     */
    @Test
    void testFileUploadRecordsEntity() {
        // 验证实体类字段设置
        assertNotNull(testRecord);
        assertEquals(1L, testRecord.getId());
        assertEquals("avatar_20241222123456.jpg", testRecord.getFileName());
        assertEquals("我的头像.jpg", testRecord.getOriginalName());
        assertEquals("avatar/2024/12/22/avatar_20241222123456.jpg", testRecord.getFilePath());
        assertEquals("avatar-bucket", testRecord.getBucketName());
        assertEquals("d41d8cd98f00b204e9800998ecf8427e", testRecord.getEtag());
        assertEquals("v1.0.0", testRecord.getVersionId());
        assertEquals(102400L, testRecord.getFileSize());
        assertEquals("image/jpeg", testRecord.getFileType());
        assertEquals("STANDARD", testRecord.getStorageClass());
        assertEquals("private", testRecord.getAccessPolicy());
        assertEquals("SSE-S3", testRecord.getEncryptionType());
        assertEquals(testUserId, testRecord.getUserId());
        assertEquals((byte) 0, testRecord.getIsDeleted());
    }
} 