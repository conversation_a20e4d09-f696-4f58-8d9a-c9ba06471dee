import{O as T,l as V,u as B,i as E,k as M,A as N,c as p,o as m,a as t,p as c,v as g,d as v,t as u,q as R,F,g as O,b as U}from"./CURHyiUL.js";import{u as $}from"./CEpU8TOc.js";import{R as h}from"./JeH0SXlh.js";import{_ as j}from"./DlAUqK2U.js";import"./x_rD_Ya3.js";const A={class:"test-page"},J={class:"test-section"},L={class:"form-group"},q={class:"form-group"},z={class:"form-group"},H={class:"form-group"},P={class:"test-section"},_={class:"preview-content"},G={class:"test-section"},K={class:"test-section"},Q={key:0,class:"modules-info"},W={class:"test-section"},X={class:"template-container"},Y=T({__name:"test",setup(Z){const s=V({name:"",title:"",phone:"",email:"",address:"",website:"",linkedin:"",github:"",other:"",photo:""});B({title:"组件测试 - 火花简历"});const l=$(),k=async()=>{console.log("开始初始化数据...");try{await l.createNewResume(),console.log("数据初始化完成")}catch(o){console.error("初始化失败:",o)}},y=()=>{const o={name:"测试用户",title:"高级前端工程师",phone:"13999999999",email:"<EMAIL>",address:"上海市浦东新区"};l.updateModuleData("basic",o),console.log("基本信息已更新")},w=()=>{const o=[{name:"Vue.js",level:95},{name:"React",level:88},{name:"TypeScript",level:82}];l.updateModuleData("skills",o),console.log("技能信息已更新")},f=()=>{const o=[];for(let e=1;e<=8;e++)o.push({id:`work-${e}`,position:`高级前端工程师 ${e}`,company:`科技公司 ${e}`,startDate:"2020-01",endDate:e===8?"":"2021-12",description:`负责公司${e}的前端开发工作，包括但不限于：
1. 使用Vue.js、React等现代前端框架开发用户界面
2. 与后端团队协作，完成API接口对接和数据交互
3. 优化前端性能，提升用户体验
4. 参与产品需求讨论，提供技术方案
5. 指导初级开发人员，进行代码审查
6. 维护和优化现有项目代码`});l.updateModuleData("work",o),console.log("已添加大量工作经历，测试分页功能")};E(async()=>{console.log("测试页面加载，开始初始化..."),await k()});const i=M(()=>{var e,n;return((n=(e=l.resumeData.value)==null?void 0:e.modules)==null?void 0:n.basic)||{}}),r=M(()=>l.resumeData.value);let D=null;const d=()=>{D&&clearTimeout(D),D=setTimeout(()=>{console.log("更新基本信息:",s),l.updateModuleData("basic",{...s})},300)},x=(o,e)=>{console.log("模块移动:",o,e)},C=o=>{console.log("模块删除:",o)};N(()=>l.resumeData.value,o=>{console.log("EditorStore数据变化:",o)},{deep:!0});const S=()=>{const o=l.moduleConfigs.value||[],e=o.map(n=>({...n}));if(e.length>=2){const n=e[0].order;e[0].order=e[1].order,e[1].order=n,console.log("测试模块顺序变更，更新前:",o),console.log("测试模块顺序变更，更新后:",e),l.moduleConfigs.value=e}},I=()=>{console.log("=== 测试数据流 ==="),console.log("EditorStore resumeData:",l.resumeData.value),console.log("EditorStore currentModuleId:",l.currentModuleId.value);const o={name:"测试姓名 - "+Date.now(),title:"测试职位",phone:"13800138000",email:"<EMAIL>"};console.log("正在更新基本信息:",o),l.updateModuleData("basic_info",o),setTimeout(()=>{console.log("更新后的数据:",l.getModuleData("basic_info")),console.log("完整的resumeData:",l.resumeData.value)},100)};return(o,e)=>{var n;return m(),p("div",A,[e[19]||(e[19]=t("h1",null,"数据同步测试页面",-1)),t("div",J,[e[8]||(e[8]=t("h2",null,"左侧编辑区（模拟BasicInfoForm）",-1)),t("div",L,[e[4]||(e[4]=t("label",null,"姓名：",-1)),c(t("input",{"onUpdate:modelValue":e[0]||(e[0]=a=>s.name=a),type:"text",onInput:d,placeholder:"输入姓名"},null,544),[[g,s.name]])]),t("div",q,[e[5]||(e[5]=t("label",null,"职位：",-1)),c(t("input",{"onUpdate:modelValue":e[1]||(e[1]=a=>s.title=a),type:"text",onInput:d,placeholder:"输入职位"},null,544),[[g,s.title]])]),t("div",z,[e[6]||(e[6]=t("label",null,"手机：",-1)),c(t("input",{"onUpdate:modelValue":e[2]||(e[2]=a=>s.phone=a),type:"text",onInput:d,placeholder:"输入手机号"},null,544),[[g,s.phone]])]),t("div",H,[e[7]||(e[7]=t("label",null,"邮箱：",-1)),c(t("input",{"onUpdate:modelValue":e[3]||(e[3]=a=>s.email=a),type:"text",onInput:d,placeholder:"输入邮箱"},null,544),[[g,s.email]])])]),t("div",P,[e[13]||(e[13]=t("h2",null,"右侧预览区（实时显示）",-1)),t("div",_,[t("p",null,[e[9]||(e[9]=t("strong",null,"姓名：",-1)),v(u(i.value.name||"未填写"),1)]),t("p",null,[e[10]||(e[10]=t("strong",null,"职位：",-1)),v(u(i.value.title||"未填写"),1)]),t("p",null,[e[11]||(e[11]=t("strong",null,"手机：",-1)),v(u(i.value.phone||"未填写"),1)]),t("p",null,[e[12]||(e[12]=t("strong",null,"邮箱：",-1)),v(u(i.value.email||"未填写"),1)])])]),t("div",G,[e[14]||(e[14]=t("h2",null,"EditorStore状态",-1)),t("pre",null,u(JSON.stringify(r.value,null,2)),1)]),t("div",K,[e[16]||(e[16]=t("h2",null,"模块数据详情",-1)),t("button",{onClick:S,class:"test-btn"},"测试模块顺序变更"),t("button",{onClick:f,class:"test-btn"},"添加大量内容测试分页"),(n=r.value)!=null&&n.modules?(m(),p("div",Q,[(m(!0),p(F,null,O(r.value.modules,(a,b)=>(m(),p("div",{key:b,class:"module-item"},[t("h3",null,u(b),1),e[15]||(e[15]=t("p",null,[t("strong",null,"数据：")],-1)),t("pre",null,u(JSON.stringify(a,null,2)),1)]))),128))])):R("",!0)]),t("div",W,[e[17]||(e[17]=t("h2",null,"实际ResumeTemplate组件",-1)),t("div",X,[U(h,{resumeData:r.value,isDraggable:!1,onModuleMove:x,onModuleDelete:C},null,8,["resumeData"])])]),t("div",{class:"test-section"},[e[18]||(e[18]=t("h2",null,"测试数据更新",-1)),t("div",{class:"button-group"},[t("button",{onClick:y,class:"test-btn"},"更新基本信息"),t("button",{onClick:w,class:"test-btn"},"更新技能"),t("button",{onClick:f,class:"test-btn"},"添加大量内容"),t("button",{onClick:I,class:"test-btn"},"测试数据流")])])])}}}),ne=j(Y,[["__scopeId","data-v-5c53ffcf"]]);export{ne as default};
