package com.alan6.resume.controller;

import com.alan6.resume.dto.file.*;
import com.alan6.resume.service.IFileUploadRecordsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * FileController测试类
 * 
 * 主要测试内容：
 * 1. 文件上传接口测试
 * 2. 文件下载接口测试
 * 3. 文件信息查询接口测试
 * 4. 文件删除接口测试
 * 5. 文件列表查询接口测试
 * 6. 预签名URL生成接口测试
 * 7. 文件版本管理接口测试
 * 8. 存储桶管理接口测试
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@WebMvcTest(FileController.class)
public class FileControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IFileUploadRecordsService fileUploadRecordsService;

    @Autowired
    private ObjectMapper objectMapper;

    private FileUploadResponse mockUploadResponse;
    private FileInfoResponse mockFileInfoResponse;
    private FileListResponse mockFileListResponse;
    private PresignedUrlResponse mockPresignedUrlResponse;
    private FileVersionListResponse mockVersionListResponse;
    private FileVersionRestoreResponse mockRestoreResponse;
    private BucketListResponse mockBucketListResponse;
    private BucketCreateResponse mockBucketCreateResponse;

    @BeforeEach
    void setUp() {
        // 准备文件上传响应
        mockUploadResponse = FileUploadResponse.builder()
                .fileId("F20241222123456789")
                .fileName("avatar_20241222123456.jpg")
                .originalName("我的头像.jpg")
                .filePath("avatar/2024/12/22/avatar_20241222123456.jpg")
                .fileUrl("https://minio.example.com/avatar-bucket/avatar/2024/12/22/avatar_20241222123456.jpg")
                .bucketName("avatar-bucket")
                .objectKey("avatar/2024/12/22/avatar_20241222123456.jpg")
                .etag("d41d8cd98f00b204e9800998ecf8427e")
                .versionId("v1.0.0")
                .fileSize(102400L)
                .fileType("image/jpeg")
                .storageClass("STANDARD")
                .accessPolicy("private")
                .encryptionType("SSE-S3")
                .uploadTime(LocalDateTime.now())
                .build();

        // 准备文件信息响应
        mockFileInfoResponse = FileInfoResponse.builder()
                .fileId("F20241222123456789")
                .fileName("avatar_20241222123456.jpg")
                .originalName("我的头像.jpg")
                .filePath("avatar/2024/12/22/avatar_20241222123456.jpg")
                .fileUrl("https://minio.example.com/avatar-bucket/avatar/2024/12/22/avatar_20241222123456.jpg")
                .bucketName("avatar-bucket")
                .objectKey("avatar/2024/12/22/avatar_20241222123456.jpg")
                .etag("d41d8cd98f00b204e9800998ecf8427e")
                .versionId("v1.0.0")
                .fileSize(102400L)
                .fileType("image/jpeg")
                .storageClass("STANDARD")
                .accessPolicy("private")
                .encryptionType("SSE-S3")
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 准备文件列表响应
        FileListResponse.FileItem fileItem = FileListResponse.FileItem.builder()
                .fileId("F20241222123456789")
                .fileName("avatar_20241222123456.jpg")
                .originalName("我的头像.jpg")
                .fileUrl("https://minio.example.com/avatar-bucket/avatar/2024/12/22/avatar_20241222123456.jpg")
                .fileSize(102400L)
                .fileType("image/jpeg")
                .bucketName("avatar-bucket")
                .accessPolicy("private")
                .storageClass("STANDARD")
                .createTime(LocalDateTime.now())
                .build();

        mockFileListResponse = FileListResponse.builder()
                .total(1L)
                .page(1)
                .size(10)
                .pages(1)
                .files(Arrays.asList(fileItem))
                .build();

        // 准备预签名URL响应
        mockPresignedUrlResponse = PresignedUrlResponse.builder()
                .fileId("F20241222123456789")
                .presignedUrl("https://minio.example.com/presigned-url")
                .method("GET")
                .expiresIn(3600)
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        // 准备文件版本列表响应
        FileVersionListResponse.FileVersion version = FileVersionListResponse.FileVersion.builder()
                .versionId("v1.0.0")
                .etag("d41d8cd98f00b204e9800998ecf8427e")
                .fileSize(102400L)
                .storageClass("STANDARD")
                .isLatest(true)
                .createTime(LocalDateTime.now())
                .build();

        mockVersionListResponse = FileVersionListResponse.builder()
                .total(1L)
                .page(1)
                .size(10)
                .pages(1)
                .fileId("F20241222123456789")
                .versions(Arrays.asList(version))
                .build();

        // 准备版本恢复响应
        mockRestoreResponse = FileVersionRestoreResponse.builder()
                .fileId("F20241222123456789")
                .restoredVersionId("v1.0.0")
                .newVersionId("v1.1.0")
                .previousVersionId("v1.0.1")
                .restoreTime(LocalDateTime.now())
                .operationType("VERSION_RESTORE")
                .operatorId(1001L)
                .operatorName("testuser")
                .restoreStatus("SUCCESS")
                .restoreMessage("版本恢复成功")
                .build();

        // 准备存储桶列表响应
        BucketListResponse.BucketInfo bucketInfo = BucketListResponse.BucketInfo.builder()
                .name("avatar-bucket")
                .region("us-east-1")
                .versioning(true)
                .encryption("SSE-S3")
                .storageClass("STANDARD")
                .accessPolicy("private")
                .createTime(LocalDateTime.now())
                .objectCount(10L)
                .totalSize(1024000L)
                .build();

        mockBucketListResponse = BucketListResponse.builder()
                .buckets(Arrays.asList(bucketInfo))
                .build();

        // 准备存储桶创建响应
        mockBucketCreateResponse = BucketCreateResponse.builder()
                .name("new-bucket")
                .description("新建存储桶")
                .region("us-east-1")
                .versioning(true)
                .encryption("SSE-S3")
                .storageClass("STANDARD")
                .accessPolicy("private")
                .createTime(LocalDateTime.now())
                .creatorId(1001L)
                .bucketUrl("https://minio.example.com/new-bucket")
                .createStatus("SUCCESS")
                .createMessage("存储桶创建成功")
                .build();
    }

    /**
     * 测试文件上传接口 - 成功场景
     */
    @Test
    void testUploadFile_Success() throws Exception {
        // 准备测试文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test-avatar.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );

        // Mock服务调用
        when(fileUploadRecordsService.uploadFile(any(), any(), any()))
                .thenReturn(mockUploadResponse);

        // 执行测试
        mockMvc.perform(MockMvcRequestBuilders.multipart("/file/upload")
                        .file(file)
                        .param("type", "avatar")
                        .param("bucketName", "avatar-bucket")
                        .param("accessPolicy", "private")
                        .param("description", "用户头像")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("文件上传成功")))
                .andExpect(jsonPath("$.data.fileId", is("F20241222123456789")))
                .andExpect(jsonPath("$.data.fileName", is("avatar_20241222123456.jpg")))
                .andExpect(jsonPath("$.data.originalName", is("我的头像.jpg")))
                .andExpect(jsonPath("$.data.bucketName", is("avatar-bucket")))
                .andExpect(jsonPath("$.data.fileType", is("image/jpeg")))
                .andExpect(jsonPath("$.data.fileSize", is(102400)))
                .andExpect(jsonPath("$.data.storageClass", is("STANDARD")))
                .andExpect(jsonPath("$.data.accessPolicy", is("private")));
    }

    /**
     * 测试文件下载接口 - 成功场景
     */
    @Test
    void testDownloadFile_Success() throws Exception {
        // 准备测试数据
        byte[] fileData = "test file content".getBytes();
        
        // Mock服务调用
        when(fileUploadRecordsService.getFileInfo("F20241222123456789", 1001L))
                .thenReturn(mockFileInfoResponse);
        when(fileUploadRecordsService.downloadFile("F20241222123456789", false, null, 1001L))
                .thenReturn(fileData);

        // 执行测试
        mockMvc.perform(get("/file/download/F20241222123456789")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "image/jpeg"))
                .andExpect(header().string("Content-Length", String.valueOf(fileData.length)))
                .andExpect(content().bytes(fileData));
    }

    /**
     * 测试获取文件信息接口 - 成功场景
     */
    @Test
    void testGetFileInfo_Success() throws Exception {
        // Mock服务调用
        when(fileUploadRecordsService.getFileInfo("F20241222123456789", 1001L))
                .thenReturn(mockFileInfoResponse);

        // 执行测试
        mockMvc.perform(get("/file/F20241222123456789")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("获取文件信息成功")))
                .andExpect(jsonPath("$.data.fileId", is("F20241222123456789")))
                .andExpect(jsonPath("$.data.fileName", is("avatar_20241222123456.jpg")))
                .andExpect(jsonPath("$.data.originalName", is("我的头像.jpg")))
                .andExpect(jsonPath("$.data.bucketName", is("avatar-bucket")))
                .andExpect(jsonPath("$.data.etag", is("d41d8cd98f00b204e9800998ecf8427e")))
                .andExpect(jsonPath("$.data.versionId", is("v1.0.0")))
                .andExpect(jsonPath("$.data.fileSize", is(102400)))
                .andExpect(jsonPath("$.data.fileType", is("image/jpeg")));
    }

    /**
     * 测试删除文件接口 - 软删除成功
     */
    @Test
    void testDeleteFile_SoftDeleteSuccess() throws Exception {
        // Mock服务调用
        when(fileUploadRecordsService.deleteFile("F20241222123456789", false, 1001L))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/file/F20241222123456789")
                        .param("permanent", "false")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("文件删除成功")))
                .andExpect(jsonPath("$.data", is(true)));
    }

    /**
     * 测试删除文件接口 - 永久删除成功
     */
    @Test
    void testDeleteFile_PermanentDeleteSuccess() throws Exception {
        // Mock服务调用
        when(fileUploadRecordsService.deleteFile("F20241222123456789", true, 1001L))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/file/F20241222123456789")
                        .param("permanent", "true")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("文件永久删除成功")))
                .andExpect(jsonPath("$.data", is(true)));
    }

    /**
     * 测试获取文件列表接口 - 成功场景
     */
    @Test
    void testGetFileList_Success() throws Exception {
        // Mock服务调用
        when(fileUploadRecordsService.getFileList(
                eq("avatar"), isNull(), isNull(), isNull(), isNull(), isNull(), 
                eq(1), eq(10), eq(1001L)))
                .thenReturn(mockFileListResponse);

        // 执行测试
        mockMvc.perform(get("/file/list")
                        .param("page", "1")
                        .param("size", "10")
                        .param("type", "avatar")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("获取文件列表成功")))
                .andExpect(jsonPath("$.data.total", is(1)))
                .andExpect(jsonPath("$.data.page", is(1)))
                .andExpect(jsonPath("$.data.size", is(10)))
                .andExpect(jsonPath("$.data.pages", is(1)))
                .andExpect(jsonPath("$.data.files", hasSize(1)))
                .andExpect(jsonPath("$.data.files[0].fileId", is("F20241222123456789")))
                .andExpect(jsonPath("$.data.files[0].fileName", is("avatar_20241222123456.jpg")))
                .andExpect(jsonPath("$.data.files[0].originalName", is("我的头像.jpg")))
                .andExpect(jsonPath("$.data.files[0].bucketName", is("avatar-bucket")))
                .andExpect(jsonPath("$.data.files[0].fileSize", is(102400)));
    }

    /**
     * 测试生成预签名URL接口 - 成功场景
     */
    @Test
    void testGeneratePresignedUrl_Success() throws Exception {
        // 准备请求数据
        PresignedUrlRequest request = new PresignedUrlRequest();
        request.setFileId("F20241222123456789");
        request.setMethod("GET");
        request.setExpiresIn(3600);

        // Mock服务调用
        when(fileUploadRecordsService.generatePresignedUrl(any(), eq(1001L)))
                .thenReturn(mockPresignedUrlResponse);

        // 执行测试
        mockMvc.perform(post("/file/presigned-url")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("预签名URL生成成功")))
                .andExpect(jsonPath("$.data.fileId", is("F20241222123456789")))
                .andExpect(jsonPath("$.data.presignedUrl", is("https://minio.example.com/presigned-url")))
                .andExpect(jsonPath("$.data.method", is("GET")))
                .andExpect(jsonPath("$.data.expiresIn", is(3600)));
    }

    /**
     * 测试获取文件版本列表接口 - 成功场景
     */
    @Test
    void testGetFileVersions_Success() throws Exception {
        // Mock服务调用
        when(fileUploadRecordsService.getFileVersions("F20241222123456789", 1, 10, 1001L))
                .thenReturn(mockVersionListResponse);

        // 执行测试
        mockMvc.perform(get("/file/F20241222123456789/versions")
                        .param("page", "1")
                        .param("size", "10")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("获取文件版本列表成功")))
                .andExpect(jsonPath("$.data.fileId", is("F20241222123456789")))
                .andExpect(jsonPath("$.data.total", is(1)))
                .andExpect(jsonPath("$.data.page", is(1)))
                .andExpect(jsonPath("$.data.size", is(10)))
                .andExpect(jsonPath("$.data.pages", is(1)))
                .andExpect(jsonPath("$.data.versions", hasSize(1)))
                .andExpect(jsonPath("$.data.versions[0].versionId", is("v1.0.0")))
                .andExpect(jsonPath("$.data.versions[0].etag", is("d41d8cd98f00b204e9800998ecf8427e")))
                .andExpect(jsonPath("$.data.versions[0].fileSize", is(102400)))
                .andExpect(jsonPath("$.data.versions[0].isLatest", is(true)));
    }

    /**
     * 测试恢复文件版本接口 - 成功场景
     */
    @Test
    void testRestoreFileVersion_Success() throws Exception {
        // Mock服务调用
        when(fileUploadRecordsService.restoreFileVersion("F20241222123456789", "v1.0.0", 1001L))
                .thenReturn(mockRestoreResponse);

        // 执行测试
        mockMvc.perform(post("/file/F20241222123456789/versions/v1.0.0/restore")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("文件版本恢复成功")))
                .andExpect(jsonPath("$.data.fileId", is("F20241222123456789")))
                .andExpect(jsonPath("$.data.restoredVersionId", is("v1.0.0")))
                .andExpect(jsonPath("$.data.newVersionId", is("v1.1.0")))
                .andExpect(jsonPath("$.data.operationType", is("VERSION_RESTORE")))
                .andExpect(jsonPath("$.data.operatorId", is(1001)))
                .andExpect(jsonPath("$.data.restoreStatus", is("SUCCESS")))
                .andExpect(jsonPath("$.data.restoreMessage", is("版本恢复成功")));
    }

    /**
     * 测试获取存储桶列表接口 - 成功场景
     */
    @Test
    void testGetBucketList_Success() throws Exception {
        // Mock服务调用
        when(fileUploadRecordsService.getBucketList(1001L))
                .thenReturn(mockBucketListResponse);

        // 执行测试
        mockMvc.perform(get("/file/buckets")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("获取存储桶列表成功")))
                .andExpect(jsonPath("$.data.buckets", hasSize(1)))
                .andExpect(jsonPath("$.data.buckets[0].name", is("avatar-bucket")))
                .andExpect(jsonPath("$.data.buckets[0].region", is("us-east-1")))
                .andExpect(jsonPath("$.data.buckets[0].versioning", is(true)))
                .andExpect(jsonPath("$.data.buckets[0].encryption", is("SSE-S3")))
                .andExpect(jsonPath("$.data.buckets[0].storageClass", is("STANDARD")))
                .andExpect(jsonPath("$.data.buckets[0].accessPolicy", is("private")))
                .andExpect(jsonPath("$.data.buckets[0].objectCount", is(10)))
                .andExpect(jsonPath("$.data.buckets[0].totalSize", is(1024000)));
    }

    /**
     * 测试创建存储桶接口 - 成功场景
     */
    @Test
    void testCreateBucket_Success() throws Exception {
        // 准备请求数据
        BucketCreateRequest request = new BucketCreateRequest();
        request.setName("new-bucket");
        request.setDescription("新建存储桶");
        request.setVersioning(true);
        request.setEncryption("SSE-S3");
        request.setStorageClass("STANDARD");
        request.setAccessPolicy("private");

        // Mock服务调用
        when(fileUploadRecordsService.createBucket(any(), eq(1001L)))
                .thenReturn(mockBucketCreateResponse);

        // 执行测试
        mockMvc.perform(post("/file/buckets")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.msg", is("存储桶创建成功")))
                .andExpect(jsonPath("$.data.name", is("new-bucket")))
                .andExpect(jsonPath("$.data.description", is("新建存储桶")))
                .andExpect(jsonPath("$.data.region", is("us-east-1")))
                .andExpect(jsonPath("$.data.versioning", is(true)))
                .andExpect(jsonPath("$.data.encryption", is("SSE-S3")))
                .andExpect(jsonPath("$.data.storageClass", is("STANDARD")))
                .andExpect(jsonPath("$.data.accessPolicy", is("private")))
                .andExpect(jsonPath("$.data.creatorId", is(1001)))
                .andExpect(jsonPath("$.data.bucketUrl", is("https://minio.example.com/new-bucket")))
                .andExpect(jsonPath("$.data.createStatus", is("SUCCESS")))
                .andExpect(jsonPath("$.data.createMessage", is("存储桶创建成功")));
    }

    /**
     * 测试文件上传接口 - 缺少文件参数
     */
    @Test
    void testUploadFile_MissingFile() throws Exception {
        // 执行测试
        mockMvc.perform(post("/file/upload")
                        .param("type", "avatar")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试获取文件信息接口 - 缺少用户ID
     */
    @Test
    void testGetFileInfo_MissingUserId() throws Exception {
        // 执行测试（不提供X-User-Id头）
        mockMvc.perform(get("/file/F20241222123456789"))
                .andDo(print())
                .andExpect(status().isOk()) // 应该使用默认用户ID
                .andExpect(jsonPath("$.code").exists());
    }

    /**
     * 测试获取文件列表接口 - 带筛选条件
     */
    @Test
    void testGetFileList_WithFilters() throws Exception {
        // Mock服务调用
        when(fileUploadRecordsService.getFileList(
                eq("avatar"), eq("avatar-bucket"), eq("image/jpeg"), 
                any(), any(), eq("头像"), eq(1), eq(20), eq(1001L)))
                .thenReturn(mockFileListResponse);

        // 执行测试
        mockMvc.perform(get("/file/list")
                        .param("page", "1")
                        .param("size", "20")
                        .param("type", "avatar")
                        .param("bucketName", "avatar-bucket")
                        .param("fileType", "image/jpeg")
                        .param("keyword", "头像")
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.data.files", hasSize(1)));
    }

    /**
     * 测试生成预签名URL接口 - 无效的请求参数
     */
    @Test
    void testGeneratePresignedUrl_InvalidRequest() throws Exception {
        // 准备无效请求数据（缺少fileId）
        PresignedUrlRequest request = new PresignedUrlRequest();
        request.setMethod("GET");
        request.setExpiresIn(3600);

        // 执行测试
        mockMvc.perform(post("/file/presigned-url")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk()); // 控制器层不做参数验证，由服务层处理
    }

    /**
     * 测试创建存储桶接口 - 无效的请求参数
     */
    @Test
    void testCreateBucket_InvalidRequest() throws Exception {
        // 准备无效请求数据（缺少name）
        BucketCreateRequest request = new BucketCreateRequest();
        request.setDescription("新建存储桶");

        // 执行测试
        mockMvc.perform(post("/file/buckets")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .header("X-User-Id", "1001"))
                .andDo(print())
                .andExpect(status().isOk()); // 控制器层不做参数验证，由服务层处理
    }
} 