<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代简历模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .resume-template {
            max-width: 900px;
            margin: 0 auto;
            padding: 40px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        /* 基本信息模块 */
        .basic-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .basic-info::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: rotate(45deg);
        }

        .basic-info-content {
            position: relative;
            z-index: 2;
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 30px;
            align-items: center;
        }

        .avatar-section {
            text-align: center;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid rgba(255,255,255,0.3);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }

        .info-section {
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .name {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .basic-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            font-size: 16px;
        }

        .detail-group {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
            font-size: 16px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .icon {
            width: 20px;
            height: 20px;
            opacity: 0.9;
        }

        /* 模块通用样式 */
        .module-section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .module-header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px 30px;
            position: relative;
        }

        .module-header.blue {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .module-header.green {
            background: linear-gradient(135deg, #00b894, #00a085);
        }

        .module-header.purple {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
        }

        .module-header.orange {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .module-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .module-content {
            padding: 30px;
        }

        /* 卡片样式 */
        .card-item {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .card-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-subtitle {
            font-size: 16px;
            color: #667eea;
            font-weight: 500;
        }

        .card-date {
            font-size: 14px;
            color: #74b9ff;
            background: rgba(116, 185, 255, 0.1);
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .card-description {
            font-size: 15px;
            color: #636e72;
            line-height: 1.7;
        }

        /* 技能网格 */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .skill-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .skill-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .skill-progress {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .skill-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .skill-level {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: #636e72;
        }

        /* 标签样式 */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        /* 网格布局 */
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .grid-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            text-align: center;
        }

        /* 文本内容 */
        .text-content {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            font-size: 16px;
            line-height: 1.8;
            color: #2c3e50;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #b2bec3;
            font-style: italic;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .resume-template {
                padding: 20px;
            }

            .basic-info-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 20px;
            }

            .name {
                font-size: 32px;
            }

            .basic-details {
                grid-template-columns: 1fr;
            }

            .contact-info {
                justify-content: center;
            }

            .module-content {
                padding: 20px;
            }

            .card-header {
                flex-direction: column;
                gap: 10px;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 打印样式 */
        @media print {
            .resume-template {
                box-shadow: none;
                padding: 0;
                font-size: 12px;
            }

            .basic-info {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
            }

            .module-header {
                -webkit-print-color-adjust: exact;
            }

            .card-item:hover {
                transform: none;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }
        }
    </style>
</head>
<body>
    <div class="resume-template" data-module-root="true">
        
        <!-- 基本信息模块 -->
        <section class="basic-info" data-module="basic_info">
            <div class="basic-info-content">
                <div class="avatar-section">
                    <img data-field="photo" data-vue-src="resumeData.basic_info.photo" src="https://via.placeholder.com/120x120/667eea/ffffff?text=头像" alt="头像" class="avatar" />
                </div>
                <div class="info-section">
                    <h1 class="name" data-field="name" data-vue-text="resumeData.basic_info.name">张三</h1>
                    
                    <div class="basic-details">
                        <div class="detail-group">
                            <div><span data-field="gender" data-vue-text="resumeData.basic_info.gender">男</span> · <span data-field="age" data-vue-text="resumeData.basic_info.age">28岁</span> · <span data-field="currentCity" data-vue-text="resumeData.basic_info.currentCity">北京</span></div>
                            <div>意向城市：<span data-field="intendedCity" data-vue-text="resumeData.basic_info.intendedCity">上海</span></div>
                            <div>居住地址：<span data-field="address" data-vue-text="resumeData.basic_info.address">北京市朝阳区</span></div>
                        </div>
                        <div class="detail-group">
                            <div>当前状态：<span data-field="currentStatus" data-vue-text="resumeData.basic_info.currentStatus">在职-考虑机会</span></div>
                            <div>期望职位：<span data-field="expectedPosition" data-vue-text="resumeData.basic_info.expectedPosition">前端开发工程师</span></div>
                            <div>期望薪资：<span data-field="expectedSalary" data-vue-text="resumeData.basic_info.expectedSalary">20-30K</span></div>
                        </div>
                        <div class="detail-group" style="grid-column: 1 / -1;">
                            <div>身高：<span data-field="height" data-vue-text="resumeData.basic_info.height">175cm</span> · 
                                 体重：<span data-field="weight" data-vue-text="resumeData.basic_info.weight">70kg</span> · 
                                 政治面貌：<span data-field="politicalStatus" data-vue-text="resumeData.basic_info.politicalStatus">党员</span></div>
                            <div>婚姻状况：<span data-field="maritalStatus" data-vue-text="resumeData.basic_info.maritalStatus">未婚</span> · 
                                 籍贯：<span data-field="hometown" data-vue-text="resumeData.basic_info.hometown">山东济南</span> · 
                                 民族：<span data-field="ethnicity" data-vue-text="resumeData.basic_info.ethnicity">汉族</span></div>
                        </div>
                    </div>
                    
                    <div class="contact-info">
                        <div class="contact-item">
                            <span class="icon">📱</span>
                            <span data-field="phone" data-vue-text="resumeData.basic_info.phone">138-0000-0000</span>
                        </div>
                        <div class="contact-item">
                            <span class="icon">📧</span>
                            <span data-field="email" data-vue-text="resumeData.basic_info.email"><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <span class="icon">💬</span>
                            <span data-field="wechat" data-vue-text="resumeData.basic_info.wechat">zhangsan_wx</span>
                        </div>
                        <div class="contact-item">
                            <span class="icon">🌐</span>
                            <span data-field="website" data-vue-text="resumeData.basic_info.website">www.zhangsan.dev</span>
                        </div>
                        <div class="contact-item">
                            <span class="icon">💻</span>
                            <span data-field="github" data-vue-text="resumeData.basic_info.github">github.com/zhangsan</span>
                        </div>
                    </div>

                    <!-- 自定义社交信息 -->
                    <div data-vue-for="social in resumeData.basic_info.customSocials" data-vue-key="social.id" class="contact-item">
                        <span data-field="label" data-vue-text="social.label">微博</span>：
                        <span data-field="value" data-vue-text="social.value">@zhangsan</span>
                    </div>

                    <!-- 自定义信息 -->
                    <div data-vue-for="info in resumeData.basic_info.customInfos" data-vue-key="info.id" class="contact-item">
                        <span data-field="label" data-vue-text="info.label">特长</span>：
                        <span data-field="value" data-vue-text="info.value">摄影</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 工作经历模块 -->
        <section class="module-section" data-module="work_experience">
            <div class="module-header">
                <h2 class="module-title">💼 工作经历</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.work_experience" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="company" data-vue-text="item.company">阿里巴巴集团</div>
                            <div class="card-subtitle" data-field="position" data-vue-text="item.position">高级前端工程师</div>
                            <div style="font-size: 14px; color: #74b9ff; margin-top: 5px;" data-field="department" data-vue-text="item.department">技术部</div>
                        </div>
                        <div class="card-date">
                            <span data-field="startDate" data-vue-text="item.startDate">2021-03</span> ~ 
                            <span data-field="endDate" data-vue-text="item.endDate">至今</span>
                        </div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        负责公司核心业务前端开发，参与架构设计，技术选型，代码review等工作。主导了多个重要项目的前端开发，提升了用户体验和系统性能。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.work_experience || resumeData.work_experience.length === 0">
                    暂无工作经历信息
                </div>
            </div>
        </section>

        <!-- 教育经历模块 -->
        <section class="module-section" data-module="education">
            <div class="module-header blue">
                <h2 class="module-title">🎓 教育经历</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.education" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="school" data-vue-text="item.school">北京大学</div>
                            <div class="card-subtitle">
                                <span data-field="major" data-vue-text="item.major">计算机科学与技术</span> · 
                                <span data-field="degree" data-vue-text="item.degree">本科</span>
                                <span v-if="item.customDegree" data-field="customDegree" data-vue-text="item.customDegree"></span>
                            </div>
                        </div>
                        <div class="card-date">
                            <span data-field="startDate" data-vue-text="item.startDate">2016-09</span> ~ 
                            <span data-field="endDate" data-vue-text="item.endDate">2020-06</span>
                        </div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        主修计算机科学相关课程，GPA 3.8/4.0，获得优秀毕业生称号。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.education || resumeData.education.length === 0">
                    暂无教育经历信息
                </div>
            </div>
        </section>

        <!-- 项目经历模块 -->
        <section class="module-section" data-module="project">
            <div class="module-header green">
                <h2 class="module-title">🚀 项目经历</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.project" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="name" data-vue-text="item.name">电商管理平台</div>
                            <div class="card-subtitle">
                                <span data-field="role" data-vue-text="item.role">前端负责人</span> · 
                                <span data-field="company" data-vue-text="item.company">阿里巴巴</span>
                            </div>
                        </div>
                        <div class="card-date">
                            <span data-field="startDate" data-vue-text="item.startDate">2022-01</span> ~ 
                            <span data-field="endDate" data-vue-text="item.endDate">2022-12</span>
                        </div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        负责电商管理平台的前端开发，使用React+TypeScript技术栈，实现了商品管理、订单处理、数据分析等核心功能。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.project || resumeData.project.length === 0">
                    暂无项目经历信息
                </div>
            </div>
        </section>

        <!-- 技能特长模块 -->
        <section class="module-section" data-module="skills">
            <div class="module-header purple">
                <h2 class="module-title">⚡ 技能特长</h2>
            </div>
            <div class="module-content">
                <div class="skills-grid">
                    <div class="skill-item" data-vue-for="skill in resumeData.skills" data-vue-key="skill.id">
                        <div class="skill-name" data-field="name" data-vue-text="skill.name">JavaScript</div>
                        <div class="skill-progress">
                            <div class="skill-progress-bar" data-field="level" :style="{width: skill.level + '%'}"></div>
                        </div>
                        <div class="skill-level">
                            <span data-field="proficiency" data-vue-text="skill.proficiency">精通</span>
                            <span><span data-field="level" data-vue-text="skill.level">90</span>%</span>
                        </div>
                        <div style="font-size: 12px; color: #999; margin-top: 5px;">
                            显示模式：<span data-field="displayMode" data-vue-text="skill.displayMode">percentage</span>
                        </div>
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.skills || resumeData.skills.length === 0">
                    暂无技能信息
                </div>
            </div>
        </section>

        <!-- 语言能力模块 -->
        <section class="module-section" data-module="language">
            <div class="module-header orange">
                <h2 class="module-title">🌐 语言能力</h2>
            </div>
            <div class="module-content">
                <div class="grid-container">
                    <div class="grid-item" data-vue-for="item in resumeData.language" data-vue-key="item.id">
                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;" data-field="name" data-vue-text="item.name">英语</div>
                        <div style="font-size: 14px; color: #667eea; margin-bottom: 8px;" data-field="level" data-vue-text="item.level">流利</div>
                        <div style="font-size: 13px; color: #999; margin-bottom: 8px;" data-field="certificate" data-vue-text="item.certificate">CET-6</div>
                        <div style="font-size: 14px; color: #636e72;" data-field="description" data-vue-text="item.description">能够流利进行英语交流</div>
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.language || resumeData.language.length === 0">
                    暂无语言能力信息
                </div>
            </div>
        </section>

        <!-- 获奖情况模块 -->
        <section class="module-section" data-module="award">
            <div class="module-header">
                <h2 class="module-title">🏆 获奖情况</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.award" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="name" data-vue-text="item.name">优秀员工奖</div>
                            <div class="card-subtitle">
                                <span data-field="organization" data-vue-text="item.organization">阿里巴巴集团</span> · 
                                <span data-field="level" data-vue-text="item.level">公司级</span>
                            </div>
                        </div>
                        <div class="card-date" data-field="date" data-vue-text="item.date">2022-12</div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        因在项目中的杰出表现，获得公司优秀员工奖励。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.award || resumeData.award.length === 0">
                    暂无获奖情况信息
                </div>
            </div>
        </section>

        <!-- 证书资质模块 -->
        <section class="module-section" data-module="certificate">
            <div class="module-header blue">
                <h2 class="module-title">📜 证书资质</h2>
            </div>
            <div class="module-content">
                <div class="grid-container">
                    <div class="grid-item" data-vue-for="item in resumeData.certificate" data-vue-key="item.id">
                        <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px;" data-field="name" data-vue-text="item.name">PMP项目管理认证</div>
                        <div style="font-size: 14px; color: #667eea; margin-bottom: 5px;" data-field="organization" data-vue-text="item.organization">PMI</div>
                        <div style="font-size: 13px; color: #999; margin-bottom: 5px;" data-field="date" data-vue-text="item.date">2022-06</div>
                        <div style="font-size: 12px; color: #999; margin-bottom: 8px;">证书编号：<span data-field="number" data-vue-text="item.number">PMP20220001</span></div>
                        <div style="font-size: 14px; color: #636e72;" data-field="description" data-vue-text="item.description">项目管理专业人士认证</div>
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.certificate || resumeData.certificate.length === 0">
                    暂无证书资质信息
                </div>
            </div>
        </section>

        <!-- 实习经历模块 -->
        <section class="module-section" data-module="internship">
            <div class="module-header green">
                <h2 class="module-title">🎯 实习经历</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.internship" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="company" data-vue-text="item.company">腾讯科技</div>
                            <div class="card-subtitle" data-field="position" data-vue-text="item.position">前端开发实习生</div>
                        </div>
                        <div class="card-date">
                            <span data-field="startDate" data-vue-text="item.startDate">2019-07</span> ~ 
                            <span data-field="endDate" data-vue-text="item.endDate">2019-09</span>
                        </div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        参与公司内部管理系统的前端开发，学习了React开发流程。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.internship || resumeData.internship.length === 0">
                    暂无实习经历信息
                </div>
            </div>
        </section>

        <!-- 志愿经历模块 -->
        <section class="module-section" data-module="volunteer_experience">
            <div class="module-header purple">
                <h2 class="module-title">❤️ 志愿经历</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.volunteer_experience" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="organization" data-vue-text="item.organization">红十字会</div>
                            <div class="card-subtitle">
                                <span data-field="role" data-vue-text="item.role">志愿者</span> · 
                                <span data-field="location" data-vue-text="item.location">北京</span>
                            </div>
                        </div>
                        <div class="card-date">
                            <span data-field="startDate" data-vue-text="item.startDate">2020-01</span> ~ 
                            <span data-field="endDate" data-vue-text="item.endDate">2020-03</span>
                        </div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        参与疫情期间的志愿服务工作，协助社区防疫工作。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.volunteer_experience || resumeData.volunteer_experience.length === 0">
                    暂无志愿经历信息
                </div>
            </div>
        </section>

        <!-- 论文发表模块 -->
        <section class="module-section" data-module="publication">
            <div class="module-header orange">
                <h2 class="module-title">📚 论文发表</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.publication" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="title" data-vue-text="item.title">基于深度学习的前端性能优化研究</div>
                            <div class="card-subtitle">
                                <span data-field="type" data-vue-text="item.type">期刊论文</span> · 
                                <span data-field="journal" data-vue-text="item.journal">计算机学报</span>
                            </div>
                        </div>
                        <div class="card-date" data-field="date" data-vue-text="item.date">2022-06</div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        研究了深度学习在前端性能优化中的应用。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.publication || resumeData.publication.length === 0">
                    暂无论文发表信息
                </div>
            </div>
        </section>

        <!-- 科研经历模块 -->
        <section class="module-section" data-module="research_experience">
            <div class="module-header">
                <h2 class="module-title">🔬 科研经历</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.research_experience" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="topic" data-vue-text="item.topic">人工智能在前端开发中的应用</div>
                            <div class="card-subtitle">
                                <span data-field="role" data-vue-text="item.role">研究助理</span> · 
                                <span data-field="organization" data-vue-text="item.organization">北京大学</span>
                            </div>
                        </div>
                        <div class="card-date">
                            <span data-field="startDate" data-vue-text="item.startDate">2019-09</span> ~ 
                            <span data-field="endDate" data-vue-text="item.endDate">2020-06</span>
                        </div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        参与了人工智能在前端开发中的应用研究项目。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.research_experience || resumeData.research_experience.length === 0">
                    暂无科研经历信息
                </div>
            </div>
        </section>

        <!-- 培训经历模块 -->
        <section class="module-section" data-module="training">
            <div class="module-header blue">
                <h2 class="module-title">📖 培训经历</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.training" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="course" data-vue-text="item.course">React高级开发培训</div>
                            <div class="card-subtitle" data-field="company" data-vue-text="item.company">极客时间</div>
                        </div>
                        <div class="card-date">
                            <span data-field="startDate" data-vue-text="item.startDate">2021-06</span> ~ 
                            <span data-field="endDate" data-vue-text="item.endDate">2021-08</span>
                        </div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        深入学习React框架的高级特性和最佳实践。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.training || resumeData.training.length === 0">
                    暂无培训经历信息
                </div>
            </div>
        </section>

        <!-- 作品集模块 -->
        <section class="module-section" data-module="portfolio">
            <div class="module-header green">
                <h2 class="module-title">🎨 作品集</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.portfolio" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="title" data-vue-text="item.title">个人博客网站</div>
                            <div class="card-subtitle">
                                <span data-field="type" data-vue-text="item.type">Web应用</span> · 
                                <a data-field="url" data-vue-text="item.url" data-vue-href="item.url" href="#" style="color: #667eea;">查看作品</a>
                            </div>
                        </div>
                        <div class="card-date" data-field="date" data-vue-text="item.date">2022-03</div>
                    </div>
                    <div class="card-description" data-field="description" data-vue-text="item.description">
                        使用React和Node.js开发的个人博客网站，支持文章管理和评论功能。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.portfolio || resumeData.portfolio.length === 0">
                    暂无作品集信息
                </div>
            </div>
        </section>

        <!-- 兴趣爱好模块 -->
        <section class="module-section" data-module="hobbies">
            <div class="module-header purple">
                <h2 class="module-title">🎯 兴趣爱好</h2>
            </div>
            <div class="module-content">
                <div class="tags-container">
                    <div class="tag" data-vue-for="hobby in resumeData.hobbies" data-vue-key="hobby.id">
                        <span data-field="name" data-vue-text="hobby.name">摄影</span>
                        <span style="margin-left: 5px; opacity: 0.8;" data-field="level" data-vue-text="hobby.level">精通</span>
                    </div>
                </div>
                <div style="margin-top: 20px;" data-vue-for="hobby in resumeData.hobbies" data-vue-key="hobby.id">
                    <div style="font-weight: 600; margin-bottom: 5px;" data-field="name" data-vue-text="hobby.name">摄影</div>
                    <div style="font-size: 14px; color: #636e72;" data-field="description" data-vue-text="hobby.description">
                        喜欢拍摄风景和人物，有5年摄影经验。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.hobbies || resumeData.hobbies.length === 0">
                    暂无兴趣爱好信息
                </div>
            </div>
        </section>

        <!-- 自我评价模块 -->
        <section class="module-section" data-module="self_evaluation">
            <div class="module-header orange">
                <h2 class="module-title">💭 自我评价</h2>
            </div>
            <div class="module-content">
                <div class="text-content" data-field="content" data-vue-text="resumeData.self_evaluation.content">
                    本人性格开朗，积极向上，有较强的学习能力和适应能力。在工作中认真负责，善于沟通协调，具备良好的团队合作精神。熟练掌握前端开发技术，对新技术有浓厚兴趣。希望能够在新的岗位上发挥自己的专业优势，为团队和公司创造更大价值。
                </div>
            </div>
        </section>

        <!-- 自荐信模块 -->
        <section class="module-section" data-module="cover_letter">
            <div class="module-header">
                <h2 class="module-title">✉️ 自荐信</h2>
            </div>
            <div class="module-content">
                <div class="text-content" data-field="content" data-vue-text="resumeData.cover_letter.content">
                    尊敬的HR您好，我是一名专业的前端开发工程师，拥有3年以上的实际工作经验。我熟练掌握React、Vue等主流前端框架，具备丰富的项目经验。我相信我的技能和经验能够为贵公司带来价值，期待有机会加入贵公司的团队。
                </div>
            </div>
        </section>

        <!-- 自定义模块示例 -->
        <section class="module-section" data-module="custom_1640000001">
            <div class="module-header blue">
                <h2 class="module-title">🌟 社团活动</h2>
            </div>
            <div class="module-content">
                <div class="card-item" data-vue-for="item in resumeData.custom_1640000001.items" data-vue-key="item.id">
                    <div class="card-header">
                        <div>
                            <div class="card-title" data-field="name" data-vue-text="item.name">学生会</div>
                            <div class="card-subtitle" data-field="role" data-vue-text="item.role">主席</div>
                        </div>
                        <div class="card-date">
                            <span data-field="startDate" data-vue-text="item.startDate">2018-09</span> ~ 
                            <span data-field="endDate" data-vue-text="item.endDate">2019-06</span>
                        </div>
                    </div>
                    <div class="card-description" data-field="content" data-vue-text="item.content">
                        担任学生会主席，组织了多次大型校园活动，锻炼了组织协调能力。
                    </div>
                </div>
                <div class="empty-state" data-vue-if="!resumeData.custom_1640000001 || !resumeData.custom_1640000001.items || resumeData.custom_1640000001.items.length === 0">
                    暂无社团活动信息
                </div>
            </div>
        </section>

    </div>
</body>
</html>