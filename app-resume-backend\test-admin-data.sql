-- 测试管理员数据插入脚本
-- 用于测试后台管理系统的权限功能

-- 插入测试用户（管理员）
INSERT INTO `users` (`id`, `username`, `phone`, `password`, `nickname`, `status`, `register_platform`, `create_time`, `update_time`) 
VALUES (1001, 'admin', '13800138001', '123456', '系统管理员', 1, 'web', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `username` = VALUES(`username`),
    `phone` = VALUES(`phone`),
    `password` = VALUES(`password`),
    `nickname` = VALUES(`nickname`),
    `update_time` = NOW();

-- 插入测试用户（超级管理员）
INSERT INTO `users` (`id`, `username`, `phone`, `password`, `nickname`, `status`, `register_platform`, `create_time`, `update_time`) 
VALUES (1002, 'superadmin', '13800138002', '123456', '超级管理员', 1, 'web', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `username` = VALUES(`username`),
    `phone` = VALUES(`phone`),
    `password` = VALUES(`password`),
    `nickname` = VALUES(`nickname`),
    `update_time` = NOW();

-- 确保角色数据存在
INSERT INTO `roles` (`id`, `role_name`, `role_code`, `description`, `sort_order`, `status`, `create_time`, `update_time`) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, 1, NOW(), NOW()),
(2, '管理员', 'ADMIN', '系统管理员，拥有大部分管理权限', 2, 1, NOW(), NOW()),
(3, '普通用户', 'USER', '普通用户，只能使用基础功能', 3, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `role_name` = VALUES(`role_name`),
    `description` = VALUES(`description`),
    `update_time` = NOW();

-- 确保权限数据存在
INSERT INTO `permissions` (`id`, `permission_name`, `permission_code`, `resource_type`, `resource_url`, `parent_id`, `sort_order`, `status`, `create_time`, `update_time`) VALUES
(1, '后台管理', 'admin', 'menu', '/admin', 0, 1, 1, NOW(), NOW()),
(2, '用户管理', 'admin:user', 'menu', '/admin/users', 1, 2, 1, NOW(), NOW()),
(3, '模板管理', 'admin:template', 'menu', '/admin/templates', 1, 3, 1, NOW(), NOW()),
(4, '订单管理', 'admin:order', 'menu', '/admin/orders', 1, 4, 1, NOW(), NOW()),
(5, '系统设置', 'admin:system', 'menu', '/admin/system', 1, 5, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `permission_name` = VALUES(`permission_name`),
    `resource_url` = VALUES(`resource_url`),
    `update_time` = NOW();

-- 给用户分配角色
INSERT INTO `user_roles` (`user_id`, `role_id`, `create_time`, `update_time`) VALUES
(1001, 2, NOW(), NOW()),  -- 普通管理员
(1002, 1, NOW(), NOW())   -- 超级管理员
ON DUPLICATE KEY UPDATE 
    `update_time` = NOW();

-- 给超级管理员分配所有权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `create_time`, `update_time`) VALUES
(1, 1, NOW(), NOW()), 
(1, 2, NOW(), NOW()), 
(1, 3, NOW(), NOW()), 
(1, 4, NOW(), NOW()), 
(1, 5, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `update_time` = NOW();

-- 给管理员分配部分权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `create_time`, `update_time`) VALUES
(2, 1, NOW(), NOW()), 
(2, 2, NOW(), NOW()), 
(2, 3, NOW(), NOW()), 
(2, 4, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `update_time` = NOW();

-- 查询验证数据
SELECT '=== 用户数据 ===' as info;
SELECT id, username, phone, nickname, status FROM users WHERE id IN (1001, 1002);

SELECT '=== 角色数据 ===' as info;
SELECT id, role_name, role_code, description FROM roles WHERE id IN (1, 2, 3);

SELECT '=== 用户角色关联 ===' as info;
SELECT ur.user_id, u.username, r.role_name, r.role_code 
FROM user_roles ur 
JOIN users u ON ur.user_id = u.id 
JOIN roles r ON ur.role_id = r.id 
WHERE ur.user_id IN (1001, 1002);

SELECT '=== 角色权限关联 ===' as info;
SELECT rp.role_id, r.role_name, p.permission_name, p.permission_code 
FROM role_permissions rp 
JOIN roles r ON rp.role_id = r.id 
JOIN permissions p ON rp.permission_id = p.id 
WHERE rp.role_id IN (1, 2); 