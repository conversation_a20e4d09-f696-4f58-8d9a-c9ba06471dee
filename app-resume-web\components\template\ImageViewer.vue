<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div 
        v-if="visible"
        class="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm"
        @click="handleClose"
      >
        <!-- 图片容器 -->
        <div class="relative w-full h-full flex items-center justify-center p-4">
          <!-- 图片 -->
          <img
            ref="imageRef"
            :src="imageUrl"
            :alt="imageAlt"
            class="max-w-full max-h-full object-contain transition-transform duration-300 ease-out select-none"
            :style="{ transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)` }"
            @click.stop
            @wheel.prevent="handleWheel"
            @mousedown="handleMouseDown"
            @dragstart.prevent
          />

          <!-- 控制按钮组 -->
          <div class="absolute top-4 right-4 flex items-center space-x-2">
            <!-- 放大按钮 -->
            <button
              @click.stop="zoomIn"
              class="p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors duration-200"
              title="放大"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
            </button>

            <!-- 缩小按钮 -->
            <button
              @click.stop="zoomOut"
              class="p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors duration-200"
              title="缩小"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
              </svg>
            </button>

            <!-- 重置按钮 -->
            <button
              @click.stop="resetZoom"
              class="p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors duration-200"
              title="重置"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </button>

            <!-- 关闭按钮 -->
            <button
              @click.stop="handleClose"
              class="p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors duration-200"
              title="关闭"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- 缩放比例显示 -->
          <div class="absolute bottom-4 left-4 px-3 py-1 bg-white/10 backdrop-blur-sm rounded-lg text-white text-sm">
            {{ Math.round(scale * 100) }}%
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { defineProps, defineEmits, ref, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    required: true
  },
  imageAlt: {
    type: String,
    default: '图片预览'
  }
})

// Emits
const emit = defineEmits(['close'])

// 响应式数据
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)
const isDragging = ref(false)
const lastMouseX = ref(0)
const lastMouseY = ref(0)
const imageRef = ref(null)

// 缩放相关常量
const MIN_SCALE = 0.1
const MAX_SCALE = 5
const ZOOM_STEP = 0.2

// 放大
const zoomIn = () => {
  scale.value = Math.min(scale.value + ZOOM_STEP, MAX_SCALE)
}

// 缩小
const zoomOut = () => {
  scale.value = Math.max(scale.value - ZOOM_STEP, MIN_SCALE)
}

// 重置缩放
const resetZoom = () => {
  scale.value = 1
  translateX.value = 0
  translateY.value = 0
}

// 处理鼠标滚轮缩放
const handleWheel = (event) => {
  const delta = event.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP
  scale.value = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale.value + delta))
}

// 处理鼠标按下开始拖拽
const handleMouseDown = (event) => {
  if (scale.value <= 1) return
  
  isDragging.value = true
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 处理鼠标移动拖拽
const handleMouseMove = (event) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - lastMouseX.value
  const deltaY = event.clientY - lastMouseY.value
  
  translateX.value += deltaX
  translateY.value += deltaY
  
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY
}

// 处理鼠标松开结束拖拽
const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 处理关闭
const handleClose = () => {
  resetZoom()
  emit('close')
}

// 处理ESC键关闭
const handleKeyDown = (event) => {
  if (event.key === 'Escape' && props.visible) {
    handleClose()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})
</script> 