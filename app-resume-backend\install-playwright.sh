#!/bin/bash

# 安装Playwright脚本
echo "开始安装Playwright..."

# 检查Node.js是否已安装
if ! command -v node &> /dev/null; then
    echo "错误: Node.js未安装。请先安装Node.js。"
    exit 1
fi

# 检查npm是否已安装
if ! command -v npm &> /dev/null; then
    echo "错误: npm未安装。请先安装npm。"
    exit 1
fi

# 创建package.json文件
cat > package.json << EOF
{
  "name": "resume-pdf-converter",
  "version": "1.0.0",
  "description": "PDF converter for resume export",
  "main": "index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "dependencies": {
    "playwright": "^1.40.0"
  },
  "author": "",
  "license": "ISC"
}
EOF

# 安装依赖
echo "正在安装Playwright..."
npm install

# 安装浏览器
echo "正在安装Playwright浏览器..."
npx playwright install chromium

echo "Playwright安装完成！"
echo "现在可以使用真正的HTML到PDF转换功能了。" 