package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 模板状态更新请求DTO
 * 
 * @description 用于更新模板启用/禁用状态
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "模板状态更新请求")
public class TemplateStatusUpdateRequest {

    /**
     * 模板状态
     * 0: 禁用
     * 1: 启用
     */
    @NotNull(message = "状态不能为空")
    @Schema(description = "模板状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;
} 