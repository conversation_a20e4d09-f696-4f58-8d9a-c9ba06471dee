package com.alan6.resume.service;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * <p>
 * 简历导出缓存服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface IResumeExportCacheService {

    /**
     * 缓存简历数据用于导出
     * 
     * @param resumeId 简历ID
     * @param resumeData 简历数据
     * @param expireSeconds 过期时间（秒）
     * @return 缓存key
     */
    String cacheResumeData(Long resumeId, JsonNode resumeData, int expireSeconds);

    /**
     * 获取缓存的简历数据
     * 
     * @param resumeId 简历ID
     * @return 简历数据，如果不存在则返回null
     */
    JsonNode getCachedResumeData(Long resumeId);

    /**
     * 删除缓存的简历数据
     * 
     * @param resumeId 简历ID
     * @return 删除成功返回true
     */
    boolean removeCachedResumeData(Long resumeId);

    /**
     * 获取重试次数
     * 
     * @param jobId 任务ID
     * @return 重试次数
     */
    int getRetryCount(String jobId);

    /**
     * 增加重试次数
     * 
     * @param jobId 任务ID
     * @return 增加后的重试次数
     */
    int incrementRetryCount(String jobId);

    /**
     * 重置重试次数
     * 
     * @param jobId 任务ID
     * @return 重置成功返回true
     */
    boolean resetRetryCount(String jobId);

    /**
     * 缓存导出选项
     * 
     * @param jobId 任务ID
     * @param exportOptions 导出选项
     * @param expireSeconds 过期时间（秒）
     * @return 缓存key
     */
    String cacheExportOptions(String jobId, JsonNode exportOptions, int expireSeconds);

    /**
     * 获取缓存的导出选项
     * 
     * @param jobId 任务ID
     * @return 导出选项，如果不存在则返回null
     */
    JsonNode getCachedExportOptions(String jobId);

    /**
     * 删除缓存的导出选项
     * 
     * @param jobId 任务ID
     * @return 删除成功返回true
     */
    boolean removeCachedExportOptions(String jobId);

    /**
     * 生成简历数据缓存key
     * 
     * @param resumeId 简历ID
     * @return 缓存key
     */
    String generateResumeDataKey(Long resumeId);

    /**
     * 生成重试次数缓存key
     * 
     * @param jobId 任务ID
     * @return 缓存key
     */
    String generateRetryCountKey(String jobId);

    /**
     * 生成导出选项缓存key
     * 
     * @param jobId 任务ID
     * @return 缓存key
     */
    String generateExportOptionsKey(String jobId);
} 