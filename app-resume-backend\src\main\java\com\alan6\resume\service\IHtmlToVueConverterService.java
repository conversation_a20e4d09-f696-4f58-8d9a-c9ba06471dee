package com.alan6.resume.service;

import com.alan6.resume.dto.template.HtmlConversionRequest;
import com.alan6.resume.dto.template.HtmlConversionResponse;

import java.io.File;
import java.util.List;

/**
 * HTML转Vue转换服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IHtmlToVueConverterService {
    
    /**
     * 转换单个HTML文件为Vue组件
     * 
     * @param htmlFile HTML文件
     * @param taskId 任务ID
     * @return 转换结果
     */
    HtmlConversionResponse convertSingleFile(File htmlFile, String taskId);
    
    /**
     * 批量转换HTML文件为Vue组件
     * 
     * @param htmlFiles HTML文件列表
     * @param taskId 任务ID
     * @return 转换结果
     */
    HtmlConversionResponse convertBatchFiles(List<File> htmlFiles, String taskId);
    
    /**
     * 根据转换请求进行转换
     * 
     * @param request 转换请求
     * @param taskId 任务ID
     * @return 转换结果
     */
    HtmlConversionResponse convertWithRequest(HtmlConversionRequest request, String taskId);
    
    /**
     * 转换HTML内容为Vue组件内容
     * 
     * @param htmlContent HTML内容
     * @param fileName 文件名
     * @param options 转换选项
     * @return Vue组件内容
     */
    String convertHtmlToVue(String htmlContent, String fileName, HtmlConversionRequest.ConversionOptions options);
    
    /**
     * 创建转换任务目录
     * 
     * @param taskId 任务ID
     * @return 任务目录路径
     */
    String createTaskDirectory(String taskId);
    
    /**
     * 获取转换任务目录
     * 
     * @param taskId 任务ID
     * @return 任务目录路径
     */
    String getTaskDirectory(String taskId);
    
    /**
     * 清理转换任务（可选，管理员手动清理）
     * 
     * @param taskId 任务ID
     * @return 是否清理成功
     */
    boolean cleanupTask(String taskId);
    
    /**
     * 生成任务ID
     * 
     * @return 任务ID
     */
    String generateTaskId();
    
    /**
     * 保存转换结果到文件
     * 
     * @param vueContent Vue组件内容
     * @param fileName 文件名
     * @param taskDirectory 任务目录
     * @return 保存的文件路径
     */
    String saveVueFile(String vueContent, String fileName, String taskDirectory);
    
    /**
     * 复制HTML文件到任务目录
     * 
     * @param htmlFile HTML文件
     * @param taskDirectory 任务目录
     * @return 复制后的文件路径
     */
    String copyHtmlFile(File htmlFile, String taskDirectory);
} 