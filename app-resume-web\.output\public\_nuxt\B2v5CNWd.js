import{_ as D}from"./BXnw39BI.js";import{G as U,L as H}from"./D3iq3ewo.js";import{r as E,k,K as w,p as O,c as r,o,a as t,b as l,s as T,w as d,q as $,t as b,F as A,g as G,z as S,M as P,T as F,n as B,i as q,B as K,d as p,h,E as J,f as L}from"./CURHyiUL.js";import{u as N}from"./CKDEb6Y1.js";import{$ as Q,u as R}from"./D1FrdRFX.js";import{_ as W}from"./DlAUqK2U.js";import"./x_rD_Ya3.js";const X={class:"relative"},Y={class:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center"},Z=["src","alt"],ee={key:1,class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},te={class:"p-4 bg-gradient-to-br from-secondary-50 to-secondary-100"},se={class:"flex items-start space-x-3"},oe={class:"w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0"},re=["src","alt"],ne={key:1,class:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ae={class:"flex-1 min-w-0"},le={class:"text-sm font-medium text-secondary-900 truncate"},ie={class:"text-xs text-secondary-500"},de={class:"mt-3 p-3 bg-white/80 rounded-lg"},ce={key:0,class:"flex items-center justify-between"},ue={key:1,class:"flex items-center space-x-2"},me={key:0,class:"text-xs text-secondary-500"},ve={class:"py-2"},fe={__name:"UserDropdown",props:{userInfo:{type:Object,default:()=>({})}},setup(a){const u=a,m=N(),i=E(!1),_=k(()=>{var s,e;return((e=(s=u.userInfo)==null?void 0:s.membershipInfo)==null?void 0:e.isMember)||!1}),c=k(()=>{var s,e;return((e=(s=u.userInfo)==null?void 0:s.membershipInfo)==null?void 0:e.remainDays)||0}),M=[{path:"/my-resumes",label:"我的简历",icon:w("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[w("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])},{path:"/profile",label:"账户信息",icon:w("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[w("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])}],v=()=>{i.value=!1},C=()=>{v(),B("/profile?tab=membership")},V=async()=>{v();try{await m.logout(),await B("/")}catch(s){console.error("退出登录失败:",s),Q.error("退出登录失败，请重试")}},g={mounted(s,e){s.clickOutsideEvent=n=>{s===n.target||s.contains(n.target)||e.value()},document.addEventListener("click",s.clickOutsideEvent)},unmounted(s){document.removeEventListener("click",s.clickOutsideEvent)}};return(s,e)=>{var x;const n=D;return O((o(),r("div",X,[t("button",{onMouseenter:e[0]||(e[0]=y=>i.value=!0),onClick:e[1]||(e[1]=y=>i.value=!i.value),class:"flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-secondary-700 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[t("div",Y,[(x=a.userInfo)!=null&&x.avatar?(o(),r("img",{key:0,src:a.userInfo.avatar,alt:a.userInfo.nickname||"用户头像",class:"w-8 h-8 rounded-full object-cover"},null,8,Z)):(o(),r("svg",ee,e[4]||(e[4]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),e[6]||(e[6]=t("span",null,"个人中心",-1)),(o(),r("svg",{class:T(["w-4 h-4 transition-transform duration-200",{"rotate-180":i.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[5]||(e[5]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))],32),l(F,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 translate-y-1","enter-to-class":"opacity-100 translate-y-0","leave-active-class":"transition ease-in duration-150","leave-from-class":"opacity-100 translate-y-0","leave-to-class":"opacity-0 translate-y-1"},{default:d(()=>{var y,I,j,z;return[i.value?(o(),r("div",{key:0,onMouseenter:e[2]||(e[2]=f=>i.value=!0),onMouseleave:e[3]||(e[3]=f=>i.value=!1),class:"absolute right-0 mt-2 w-72 bg-white rounded-xl shadow-xl border border-secondary-100 overflow-hidden"},[t("div",te,[t("div",se,[t("div",oe,[(y=a.userInfo)!=null&&y.avatar?(o(),r("img",{key:0,src:a.userInfo.avatar,alt:a.userInfo.nickname||"用户头像",class:"w-12 h-12 rounded-full object-cover"},null,8,re)):(o(),r("svg",ne,e[7]||(e[7]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),t("div",ae,[t("p",le,b(((I=a.userInfo)==null?void 0:I.phone)||"未设置手机号"),1),t("p",ie," ID: "+b(((j=a.userInfo)==null?void 0:j.userId)||((z=a.userInfo)==null?void 0:z.id)||"-"),1)])]),t("div",de,[_.value?(o(),r("div",ue,[e[9]||(e[9]=t("div",{class:"w-6 h-6 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center shadow-sm"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})])],-1)),e[10]||(e[10]=t("span",{class:"text-sm font-medium bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent"}," VIP会员 ",-1)),c.value>0?(o(),r("span",me," 剩余"+b(c.value)+"天 ",1)):$("",!0)])):(o(),r("div",ce,[e[8]||(e[8]=t("div",{class:"flex items-center space-x-2"},[t("div",{class:"w-6 h-6 bg-secondary-200 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-secondary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),t("span",{class:"text-sm text-secondary-600"},"免费用户")],-1)),t("button",{onClick:C,class:"px-3 py-1 text-xs font-medium bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-lg hover:from-amber-600 hover:to-orange-600 transition-all duration-200 shadow-sm hover:shadow"}," 升级VIP ")]))])]),t("div",ve,[(o(),r(A,null,G(M,f=>l(n,{key:f.path,to:f.path,onClick:v,class:"flex items-center space-x-3 px-4 py-3 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-150"},{default:d(()=>[(o(),S(P(f.icon),{class:"w-5 h-5 text-secondary-400"})),t("span",null,b(f.label),1)]),_:2},1032,["to"])),64)),e[12]||(e[12]=t("div",{class:"my-2 border-t border-secondary-100"},null,-1)),t("button",{onClick:V,class:"w-full flex items-center space-x-3 px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"},e[11]||(e[11]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1),t("span",null,"退出登录",-1)]))])],32)):$("",!0)]}),_:1})])),[[g,v]])}}},pe=W(fe,[["__scopeId","data-v-80b83e79"]]),xe={class:"min-h-screen bg-white"},he={class:"fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-secondary-100"},ge={class:"max-w-screen-2xl mx-auto px-6 lg:px-8"},ye={class:"flex items-center justify-between h-16"},we={class:"hidden md:flex items-center space-x-8"},be={class:"flex items-center"},ke={key:0,class:"px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 bg-secondary-200 text-secondary-500"},_e={class:"relative"},Me={class:"bg-secondary-900 text-white"},Ce={class:"max-w-screen-2xl mx-auto px-6 lg:px-8"},Ve={class:"py-16 grid md:grid-cols-2 lg:grid-cols-4 gap-8"},Ie={class:"space-y-4"},Se={__name:"default",setup(a){const u=N(),{messageState:m,hideMessage:i,showSuccess:_}=R(),c=E(!1),M=k(()=>u.isLoggedIn.value),v=k(()=>u.currentUser.value),C=s=>{console.log("登录成功:",s),c.value=!1,_("登录成功！欢迎回来")},V=()=>{i()},g=()=>{c.value=!0};return q(()=>{window.addEventListener("open-login-modal",g),u.initAuth()}),K(()=>{window.removeEventListener("open-login-modal",g)}),(s,e)=>{const n=D;return o(),r("div",xe,[t("header",he,[t("nav",ge,[t("div",ye,[l(n,{to:"/",class:"flex items-center space-x-3"},{default:d(()=>e[2]||(e[2]=[t("div",{class:"w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center relative"},[t("svg",{class:"w-6 h-6 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M12 2c-1.1 0-2 .9-2 2 0 .74.4 1.39 1 1.73v1.77L9 9.5c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2s2 .9 2 2c0 .74-.4 1.39-1 1.73V7.5h1c.55 0 1 .45 1 1s-.45 1-1 1l-2 2v1.77c.6.34 1 .99 1 1.73 0 1.1-.9 2-2 2s-2-.9-2-2c0-.74.4-1.39 1-1.73V11.5l-2-2c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2z"})]),t("div",{class:"absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"})],-1),t("span",{class:"font-display font-bold text-xl text-secondary-900"},"火花简历",-1)])),_:1,__:[2]}),t("div",we,[l(n,{to:"/",class:"text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"},{default:d(()=>e[3]||(e[3]=[p(" 首页 ")])),_:1,__:[3]}),l(n,{to:"/my-resumes",class:"text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"},{default:d(()=>e[4]||(e[4]=[p(" 我的简历 ")])),_:1,__:[4]}),l(n,{to:"/templates",class:"text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"},{default:d(()=>e[5]||(e[5]=[p(" 模板库 ")])),_:1,__:[5]}),l(n,{to:"/guide",class:"text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"},{default:d(()=>e[6]||(e[6]=[p(" 简历攻略 ")])),_:1,__:[6]}),l(n,{to:"/tutorial",class:"text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"},{default:d(()=>e[7]||(e[7]=[p(" 使用教程 ")])),_:1,__:[7]})]),t("div",be,[h(u).isInitialized.value?M.value?(o(),S(pe,{key:2,"user-info":v.value},null,8,["user-info"])):(o(),r("button",{key:1,onClick:e[0]||(e[0]=x=>c.value=!0),class:"px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 bg-primary-600 text-white hover:bg-primary-700 shadow-md hover:shadow-lg"}," 登录 | 注册 ")):(o(),r("div",ke,e[8]||(e[8]=[t("div",{class:"flex items-center space-x-2"},[t("div",{class:"w-4 h-4 border-2 border-secondary-400 border-t-transparent rounded-full animate-spin"}),t("span",null,"加载中...")],-1)])))])])])]),l(U,{type:h(m).type,message:h(m).message,duration:h(m).duration,visible:h(m).visible,onClose:V},null,8,["type","message","duration","visible"]),t("main",_e,[J(s.$slots,"default")]),l(H,{modelValue:c.value,"onUpdate:modelValue":e[1]||(e[1]=x=>c.value=x),onLoginSuccess:C},null,8,["modelValue"]),t("footer",Me,[t("div",Ce,[t("div",Ve,[e[11]||(e[11]=L('<div class="lg:col-span-2"><div class="flex items-center space-x-3 mb-6"><div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center relative"><svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2c-1.1 0-2 .9-2 2 0 .74.4 1.39 1 1.73v1.77L9 9.5c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2s2 .9 2 2c0 .74-.4 1.39-1 1.73V7.5h1c.55 0 1 .45 1 1s-.45 1-1 1l-2 2v1.77c.6.34 1 .99 1 1.73 0 1.1-.9 2-2 2s-2-.9-2-2c0-.74.4-1.39 1-1.73V11.5l-2-2c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2z"></path></svg><div class="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div></div><span class="font-display font-bold text-xl">火花简历</span></div><p class="text-secondary-300 mb-6 max-w-md"> 专业的在线简历制作平台，为求职者提供优质的简历模板和便捷的编辑体验，助力职业发展。 </p><div class="flex space-x-4"><a href="#" class="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a><a href="#" class="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path></svg></a><a href="#" class="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path></svg></a></div></div>',1)),t("div",null,[e[10]||(e[10]=t("h3",{class:"font-display font-semibold text-lg mb-6 text-white"},"快速链接",-1)),t("ul",Ie,[t("li",null,[l(n,{to:"/templates",class:"text-secondary-300 hover:text-primary-400 transition-colors duration-200"},{default:d(()=>e[9]||(e[9]=[p(" 模板中心 ")])),_:1,__:[9]})])])]),e[12]||(e[12]=t("div",null,[t("h3",{class:"font-display font-semibold text-lg mb-6 text-white"},"支持服务"),t("ul",{class:"space-y-4"})],-1))]),e[13]||(e[13]=L('<div class="py-8 border-t border-secondary-800 flex flex-col md:flex-row justify-between items-center"><p class="text-secondary-400 text-sm"> © 2024 火花简历. All rights reserved. </p><div class="flex items-center space-x-6 mt-4 md:mt-0"><span class="text-secondary-400 text-sm">简体中文</span><div class="flex items-center space-x-2 text-secondary-400 text-sm"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><span>安全可靠</span></div></div></div>',1))])])])}}};export{Se as default};
