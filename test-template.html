<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试简历模板</title>
    <style>
        .resume-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="resume-container" data-module-root="true">
        <!-- 个人信息 -->
        <div class="header" data-module="basic_info">
                        <h1 data-field="name" data-vue-text="resumeData.basic_info.name">张三</h1>
            <p data-field="job_title" data-vue-text="resumeData.basic_info.job_title">前端开发工程师</p>
            <p data-field="contact">
                电话：<span data-field="phone" data-vue-text="resumeData.basic_info.phone">138-0000-0000</span> | 
                邮箱：<span data-field="email" data-vue-text="resumeData.basic_info.email"><EMAIL></span>
            </p>
        </div>

        <!-- 工作经历 -->
        <div class="section" data-module="work_experiences">
            <h2 class="section-title">工作经历</h2>
                            <div data-vue-for="item in resumeData.work_experiences" data-vue-key="item.id">
                <h3 data-field="position" data-vue-text="item.position">高级前端开发工程师</h3>
                <p data-field="company" data-vue-text="item.company">某科技公司</p>
                <p data-field="start_date" data-vue-text="item.start_date">2020.01</p>
                <p data-field="end_date" data-vue-text="item.end_date">2023.12</p>
                <p data-field="description" data-vue-text="item.description">负责前端项目开发和维护</p>
            </div>
        </div>

        <!-- 技能特长 -->
        <div class="section" data-module="skills">
            <h2 class="section-title">技能特长</h2>
            <ul>
                <li data-vue-for="skill in resumeData.skills" data-vue-key="skill.id" data-vue-text="skill.skill_name">JavaScript</li>
            </ul>
        </div>
    </div>
</body>
</html> 