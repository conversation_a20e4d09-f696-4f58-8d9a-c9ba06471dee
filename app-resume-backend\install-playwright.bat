@echo off

REM 安装Playwright脚本 (Windows版本)
echo 开始安装Playwright...

REM 检查Node.js是否已安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Node.js未安装。请先安装Node.js。
    pause
    exit /b 1
)

REM 检查npm是否已安装
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: npm未安装。请先安装npm。
    pause
    exit /b 1
)

REM 创建package.json文件
echo {> package.json
echo   "name": "resume-pdf-converter",>> package.json
echo   "version": "1.0.0",>> package.json
echo   "description": "PDF converter for resume export",>> package.json
echo   "main": "index.js",>> package.json
echo   "scripts": {>> package.json
echo     "test": "echo \"Error: no test specified\" && exit 1">> package.json
echo   },>> package.json
echo   "dependencies": {>> package.json
echo     "playwright": "^1.40.0">> package.json
echo   },>> package.json
echo   "author": "",>> package.json
echo   "license": "ISC">> package.json
echo }>> package.json

REM 安装依赖
echo 正在安装Playwright...
call npm install

REM 安装浏览器
echo 正在安装Playwright浏览器...
call npx playwright install chromium

echo Playwright安装完成！
echo 现在可以使用真正的HTML到PDF转换功能了。
pause 