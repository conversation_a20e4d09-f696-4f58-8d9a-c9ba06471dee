/**
 * 模板加载组合式函数
 * @description 前端动态加载Vue模板的核心功能，支持从后端获取模板并动态渲染
 *              现在支持Vue+JSON文件组合和默认内容获取
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, reactive, computed, nextTick } from 'vue'
import { compile, createApp } from 'vue'

/**
 * 模板加载器
 * @description 负责从后端加载Vue模板并动态编译渲染，支持Vue+JSON组合
 * @returns {Object} 模板加载相关的状态和方法
 */
export function useTemplateLoader() {
  
  // ================================
  // 响应式状态管理
  // ================================
  
  /**
   * 加载状态
   * @description 跟踪模板加载的各种状态
   */
  const loadingState = reactive({
    /** 是否正在加载 */
    isLoading: false,
    /** 加载进度（0-100） */
    progress: 0,
    /** 当前加载的模板ID */
    currentTemplateId: null,
    /** 加载错误信息 */
    error: null,
    /** 加载开始时间 */
    startTime: null,
    /** 加载耗时（毫秒） */
    loadTime: 0,
    /** 是否正在加载默认内容 */
    isLoadingDefaultContent: false
  })
  
  /**
   * 缓存的模板
   * @description 本地缓存已加载的模板，避免重复请求
   */
  const templateCache = reactive(new Map())
  
  /**
   * 缓存的默认内容
   * @description 本地缓存已加载的默认内容
   */
  const defaultContentCache = reactive(new Map())
  
  /**
   * 当前活动的模板
   * @description 当前正在使用的模板信息
   */
  const activeTemplate = ref(null)
  
  /**
   * 当前默认内容
   * @description 当前模板的默认内容数据
   */
  const currentDefaultContent = ref(null)
  
  /**
   * 编译后的组件
   * @description 动态编译后的Vue组件
   */
  const compiledComponent = ref(null)
  
  // ================================
  // 计算属性
  // ================================
  
  /**
   * 是否有可用模板
   * @description 检查是否有已加载的可用模板
   */
  const hasTemplate = computed(() => {
    return activeTemplate.value && activeTemplate.value.templateContent
  })
  
  /**
   * 是否有默认内容
   * @description 检查是否有可用的默认内容
   */
  const hasDefaultContent = computed(() => {
    return currentDefaultContent.value && currentDefaultContent.value.defaultContent
  })
  
  /**
   * 模板元信息
   * @description 当前模板的元信息
   */
  const templateMeta = computed(() => {
    if (!activeTemplate.value) return null
    
    return {
      id: activeTemplate.value.templateId,
      code: activeTemplate.value.templateCode,
      name: activeTemplate.value.templateName,
      version: activeTemplate.value.version,
      features: activeTemplate.value.features || [],
      tags: activeTemplate.value.tags || [],
      isPremium: activeTemplate.value.isPremium || false,
      dataSource: activeTemplate.value.dataSource,
      loadTime: activeTemplate.value.loadTime,
      cacheTime: activeTemplate.value.cacheTime,
      hasDefaultContent: activeTemplate.value.hasDefaultContent || false
    }
  })
  
  /**
   * 缓存统计
   * @description 模板缓存的统计信息
   */
  const cacheStats = computed(() => {
    return {
      totalCached: templateCache.size,
      totalDefaultContentCached: defaultContentCache.size,
      cacheKeys: Array.from(templateCache.keys()),
      defaultContentKeys: Array.from(defaultContentCache.keys()),
      memoryUsage: calculateCacheMemoryUsage()
    }
  })
  
  // ================================
  // 核心加载方法
  // ================================
  
  /**
   * 加载模板
   * @description 从后端加载指定的Vue模板，现在支持Vue+JSON组合
   * @param {Object} options 加载选项
   * @param {number} options.templateId 模板ID
   * @param {string} options.templateCode 模板代码
   * @param {boolean} options.forceRefresh 是否强制刷新
   * @param {string} options.source 请求来源
   * @returns {Promise<Object>} 加载结果，包含模板和默认内容
   */
  const loadTemplate = async (options = {}) => {
    const {
      templateId,
      templateCode,
      forceRefresh = false,
      source = 'frontend'
    } = options
    
    // 参数验证
    if (!templateId && !templateCode) {
      throw new Error('必须提供 templateId 或 templateCode')
    }
    
    // 生成缓存键
    const cacheKey = templateId ? `id:${templateId}` : `code:${templateCode}`
    
    try {
      // 设置加载状态
      setLoadingState(true, templateId || templateCode)
      
      // 检查本地缓存
      if (!forceRefresh && templateCache.has(cacheKey)) {
        log('从本地缓存获取模板', { cacheKey })
        const cachedTemplate = templateCache.get(cacheKey)
        setActiveTemplate(cachedTemplate)
        
        // 如果缓存中有默认内容，也要设置
        if (cachedTemplate.hasDefaultContent && cachedTemplate.defaultContent) {
          currentDefaultContent.value = {
            defaultContent: cachedTemplate.defaultContent,
            contentSource: 'cache'
          }
        }
        
        setLoadingState(false)
        return {
          template: cachedTemplate,
          defaultContent: currentDefaultContent.value
        }
      }
      
      // 更新加载进度
      updateProgress(20)
      
      // 准备请求参数
      const requestData = {
        templateId,
        templateCode,
        forceRefresh,
        source,
        clientType: 'web'
      }
      
      log('开始从后端加载模板', requestData)
      
      // 调用后端API加载模板
      updateProgress(40)
      const result = await callTemplateLoaderAPI(requestData)
      
      updateProgress(80)
      
      if (result.success && result.data) {
        const templateData = result.data
        
        // 缓存模板数据
        templateCache.set(cacheKey, templateData)
        
        // 设置当前活动模板
        setActiveTemplate(templateData)
        
        // 如果模板包含默认内容，设置默认内容
        if (templateData.hasDefaultContent && templateData.defaultContent) {
          currentDefaultContent.value = {
            defaultContent: templateData.defaultContent,
            contentSource: templateData.dataSource || 'database+minio',
            contentId: templateData.contentId
          }
          
          // 缓存默认内容
          const contentCacheKey = `template:${templateData.templateId}`
          defaultContentCache.set(contentCacheKey, currentDefaultContent.value)
        } else {
          // 如果模板没有默认内容，清除当前默认内容
          currentDefaultContent.value = null
        }
        
        updateProgress(100)
        setLoadingState(false)
        
        log('模板加载成功', {
          templateId: templateData.templateId,
          templateCode: templateData.templateCode,
          hasDefaultContent: templateData.hasDefaultContent,
          contentId: templateData.contentId,
          dataSource: templateData.dataSource,
          loadTime: templateData.loadTime
        })
        
        return {
          template: templateData,
          defaultContent: currentDefaultContent.value
        }
      } else {
        throw new Error(result.message || '模板加载失败')
      }
      
    } catch (error) {
      setLoadingError(error)
      throw error
    }
  }
  
  /**
   * 获取默认内容
   * @description 当模板没有默认内容时，根据条件获取默认内容
   * @param {Object} options 获取选项
   * @param {string} options.templateCategory 模板类别
   * @param {string} options.industry 行业类型（可选）
   * @param {string} options.position 职位类型（可选）
   * @param {string} options.language 语言（可选）
   * @returns {Promise<Object>} 默认内容响应
   */
  const getDefaultContent = async (options = {}) => {
    const {
      templateCategory,
      industry,
      position,
      language = 'zh-CN'
    } = options
    
    // 参数验证
    if (!templateCategory) {
      throw new Error('必须提供 templateCategory')
    }
    
    // 生成缓存键
    const cacheKey = `${templateCategory}-${industry || 'any'}-${position || 'any'}-${language}`
    
    try {
      loadingState.isLoadingDefaultContent = true
      
      // 检查本地缓存
      if (defaultContentCache.has(cacheKey)) {
        log('从本地缓存获取默认内容', { cacheKey })
        const cachedContent = defaultContentCache.get(cacheKey)
        currentDefaultContent.value = cachedContent
        return cachedContent
      }
      
      // 准备请求参数
      const requestData = {
        templateCategory,
        industry,
        position,
        language,
        source: 'frontend'
      }
      
      log('开始从后端获取默认内容', requestData)
      
      // 调用后端API获取默认内容
      const result = await callDefaultContentAPI(requestData)
      
      if (result.success && result.data) {
        const contentData = result.data
        
        // 缓存默认内容
        defaultContentCache.set(cacheKey, contentData)
        
        // 设置当前默认内容
        currentDefaultContent.value = contentData
        
        log('默认内容获取成功', {
          contentSource: contentData.contentSource,
          templateCategory: contentData.templateCategory
        })
        
        return contentData
      } else {
        throw new Error(result.message || '获取默认内容失败')
      }
      
    } catch (error) {
      log('获取默认内容失败', { error: error.message }, 'error')
      throw error
    } finally {
      loadingState.isLoadingDefaultContent = false
    }
  }
  
  /**
   * 加载模板和默认内容
   * @description 完整的模板加载流程，包含Vue模板和默认内容
   * @param {Object} templateOptions 模板加载选项
   * @param {Object} contentOptions 内容获取选项（可选）
   * @returns {Promise<Object>} 完整的加载结果
   */
  const loadTemplateWithContent = async (templateOptions, contentOptions = null) => {
    try {
      // 首先加载模板
      const templateResult = await loadTemplate(templateOptions)
      
      // 如果模板已包含默认内容，直接返回
      if (templateResult.defaultContent) {
        return templateResult
      }
      
      // 如果模板没有默认内容且提供了内容选项，获取默认内容
      if (contentOptions) {
        try {
          const defaultContent = await getDefaultContent(contentOptions)
          return {
            template: templateResult.template,
            defaultContent: defaultContent
          }
        } catch (contentError) {
          log('获取默认内容失败，但模板加载成功', { error: contentError.message }, 'warn')
          // 即使默认内容获取失败，也返回模板
          return templateResult
        }
      }
      
      return templateResult
      
    } catch (error) {
      log('模板加载失败', { error: error.message }, 'error')
      throw error
    }
  }
  
  /**
   * 通过代码加载模板
   * @description 使用模板代码快速加载模板
   * @param {string} templateCode 模板代码
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Object>} 加载结果
   */
  const loadTemplateByCode = async (templateCode, forceRefresh = false) => {
    return await loadTemplate({
      templateCode,
      forceRefresh,
      source: 'code-loader'
    })
  }
  
  // ================================
  // API调用方法
  // ================================
  
  /**
   * 调用模板加载API
   * @description 调用后端的模板加载接口
   * @param {Object} requestData 请求数据
   * @returns {Promise<Object>} API响应
   */
  const callTemplateLoaderAPI = async (requestData) => {
    try {
      // 构建请求参数
      const params = new URLSearchParams()
      if (requestData.templateId) params.append('templateId', requestData.templateId)
      if (requestData.templateCode) params.append('templateCode', requestData.templateCode)
      if (requestData.forceRefresh) params.append('forceRefresh', 'true')
      
      // 使用新的后端API接口
      const apiUrl = `/api/template-loader/load?${params.toString()}`
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Client-Type': 'web'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      log('模板加载API响应', result)
      
      // 适配后端响应格式
      if (result.code === 200) {
        return {
          success: true,
          data: result.data
        }
      } else {
        return {
          success: false,
          message: result.msg || '模板加载失败'
        }
      }
      
    } catch (error) {
      log('调用模板加载API失败', { error: error.message }, 'error')
      // 返回模拟数据作为降级方案
      return getMockTemplateResponse(requestData)
    }
  }
  
  /**
   * 调用默认内容API
   * @description 调用后端的默认内容获取接口
   * @param {Object} requestData 请求数据
   * @returns {Promise<Object>} API响应
   */
  const callDefaultContentAPI = async (requestData) => {
    try {
      // 使用useApiConfig获取API配置
      const { getApiUrl } = await import('../shared/useApiConfig.js')
      const apiUrl = getApiUrl('/template-loader/default-content')
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      return result
      
    } catch (error) {
      log('调用默认内容API失败', { error: error.message }, 'error')
      // 返回模拟数据作为降级方案
      return getMockDefaultContentResponse(requestData)
    }
  }
  
  // ================================
  // 模拟数据方法
  // ================================
  
  /**
   * 获取模拟模板响应
   * @description 用于开发和测试的模拟模板数据
   * @param {Object} requestData 请求数据
   * @returns {Object} 模拟响应
   */
  const getMockTemplateResponse = (requestData) => {
    return {
      success: true,
      message: '模板加载成功（模拟数据）',
      data: {
        templateId: 1,
        templateCode: requestData.templateCode || 'modern-simple',
        templateName: '现代简约模板',
        version: '1.0.0',
        templateContent: getMockVueTemplate(),
        defaultContent: getMockDefaultContent(),
        hasDefaultContent: true,
        contentId: 1001,
        features: ['响应式设计', '简洁美观', '易于定制'],
        tags: ['现代', '简约', '商务'],
        isPremium: false,
        dataSource: 'database+minio',
        loadTime: 100,
        cacheTime: new Date().toISOString(),
        fileSize: 15360,
        fileHash: 'mock-hash-123'
      }
    }
  }
  
  /**
   * 获取模拟默认内容响应
   * @description 用于开发和测试的模拟默认内容数据
   * @param {Object} requestData 请求数据
   * @returns {Object} 模拟响应
   */
  const getMockDefaultContentResponse = (requestData) => {
    return {
      success: true,
      message: '默认内容获取成功（模拟数据）',
      data: {
        defaultContent: getMockDefaultContent(),
        contentSource: 'category',
        templateCategory: requestData.templateCategory,
        industry: requestData.industry,
        position: requestData.position,
        language: requestData.language || 'zh-CN',
        contentVersion: '1.0.0',
        generateTime: new Date().toISOString()
      }
    }
  }
  
  /**
   * 获取模拟Vue模板
   * @description 返回模拟的Vue模板内容
   * @returns {string} Vue模板字符串
   */
  const getMockVueTemplate = () => {
    return `<template>
  <div class="resume-template modern-simple">
    <div class="resume-header">
      <h1 class="name">{{ resumeData.basicInfo?.name || '姓名' }}</h1>
      <h2 class="title">{{ resumeData.basicInfo?.title || '职位' }}</h2>
      <div class="contact">
        <span v-if="resumeData.basicInfo?.email">{{ resumeData.basicInfo.email }}</span>
        <span v-if="resumeData.basicInfo?.phone"> | {{ resumeData.basicInfo.phone }}</span>
        <span v-if="resumeData.basicInfo?.address"> | {{ resumeData.basicInfo.address }}</span>
      </div>
    </div>
    
    <div class="resume-section" v-if="resumeData.selfEvaluation?.content">
      <h3>个人简介</h3>
      <p>{{ resumeData.selfEvaluation.content }}</p>
    </div>
    
    <div class="resume-section" v-if="resumeData.workExperiences && resumeData.workExperiences.length > 0">
      <h3>工作经历</h3>
      <div v-for="exp in resumeData.workExperiences" :key="exp.id" class="experience-item">
        <h4>{{ exp.company }} - {{ exp.position }}</h4>
        <p class="duration">{{ exp.startDate }} - {{ exp.endDate }}</p>
        <p class="description">{{ exp.description }}</p>
      </div>
    </div>
    
    <div class="resume-section" v-if="resumeData.educations && resumeData.educations.length > 0">
      <h3>教育背景</h3>
      <div v-for="edu in resumeData.educations" :key="edu.id" class="education-item">
        <h4>{{ edu.school }} - {{ edu.major }}</h4>
        <p class="duration">{{ edu.startDate }} - {{ edu.endDate }}</p>
        <p class="degree">{{ edu.degree }}</p>
      </div>
    </div>
    
    <div class="resume-section" v-if="resumeData.skills && resumeData.skills.length > 0">
      <h3>技能特长</h3>
      <div class="skills-list">
        <div v-for="skill in resumeData.skills" :key="skill.name" class="skill-item">
          <span class="skill-name">{{ skill.name }}</span>
          <div class="skill-level">
            <div class="skill-bar" :style="{ width: skill.level + '%' }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModernSimpleTemplate',
  props: {
    resumeData: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>

<style scoped>
.resume-template {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

.resume-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 2px solid #3B82F6;
}

.name {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  color: #1F2937;
}

.title {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 15px 0;
  color: #3B82F6;
}

.contact {
  font-size: 0.9rem;
  color: #6B7280;
}

.resume-section {
  margin-bottom: 30px;
}

.resume-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 15px 0;
  color: #1F2937;
  border-bottom: 1px solid #E5E7EB;
  padding-bottom: 5px;
}

.experience-item,
.education-item {
  margin-bottom: 20px;
}

.experience-item h4,
.education-item h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 5px 0;
  color: #1F2937;
}

.duration {
  font-size: 0.9rem;
  color: #6B7280;
  margin: 0 0 8px 0;
}

.description,
.degree {
  margin: 0;
  color: #4B5563;
}

.skills-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.skill-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.skill-name {
  font-weight: 500;
  min-width: 80px;
}

.skill-level {
  flex: 1;
  height: 8px;
  background: #E5E7EB;
  border-radius: 4px;
  overflow: hidden;
}

.skill-bar {
  height: 100%;
  background: #3B82F6;
  transition: width 0.3s ease;
}

@media print {
  .resume-template {
    padding: 20px;
  }
}
</style>`
  }
  
  /**
   * 获取模拟默认内容
   * @description 返回模拟的默认简历内容
   * @returns {string} JSON格式的默认内容
   */
  const getMockDefaultContent = () => {
    return JSON.stringify({
      basicInfo: {
        name: '张三',
        title: '前端开发工程师',
        phone: '13800138000',
        email: '<EMAIL>',
        address: '北京市朝阳区'
      },
      selfEvaluation: {
        content: '我是一名专业的前端开发工程师，拥有5年的Web开发经验。熟练掌握主流前端技术栈，具有良好的代码规范和团队协作能力。热爱学习新技术，善于解决复杂的技术问题。'
      },
      workExperiences: [
        {
          id: 'work_1',
          company: '字节跳动',
          position: '前端开发工程师',
          startDate: '2022-07',
          endDate: '至今',
          location: '北京',
          description: '负责公司核心产品的前端开发工作，使用Vue.js、React等技术栈开发高质量的Web应用。'
        }
      ],
      educations: [
        {
          id: 'edu_1',
          school: '北京大学',
          degree: '本科',
          major: '计算机科学与技术',
          startDate: '2018-09',
          endDate: '2022-06',
          gpa: '3.8/4.0',
          description: '主修课程：数据结构、算法设计、操作系统、计算机网络等'
        }
      ],
      skills: [
        { name: 'JavaScript', level: 90 },
        { name: 'Vue.js', level: 85 },
        { name: 'React', level: 80 },
        { name: 'Node.js', level: 75 },
        { name: 'TypeScript', level: 70 }
      ]
    }, null, 2)
  }
  
  // ================================
  // 组件编译方法（保持原有功能）
  // ================================
  
  /**
   * 编译模板
   * @description 将Vue模板字符串编译为可执行的组件
   * @param {Object} templateData 模板数据
   * @returns {Promise<Object>} 编译后的组件
   */
  const compileTemplate = async (templateData) => {
    if (!templateData || !templateData.templateContent) {
      throw new Error('模板内容不能为空')
    }
    
    try {
      updateProgress(70)
      
      // 解析Vue组件
      const componentInfo = parseVueComponent(templateData.templateContent)
      
      updateProgress(85)
      
      // 编译模板
      const render = compile(componentInfo.template)
      
      updateProgress(95)
      
      // 创建组件对象
      const component = {
        name: templateData.templateName || 'DynamicTemplate',
        render,
        ...componentInfo.script,
        __templateId: templateData.templateId,
        __templateCode: templateData.templateCode
      }
      
      // 注入样式
      if (componentInfo.style) {
        injectStyles(componentInfo.style, templateData.templateId)
      }
      
      // 缓存编译后的组件
      compiledComponent.value = component
      
      log('模板编译成功', {
        templateId: templateData.templateId,
        componentName: component.name
      })
      
      return component
      
    } catch (error) {
      log('模板编译失败', { error: error.message }, 'error')
      throw new Error(`模板编译失败: ${error.message}`)
    }
  }
  
  /**
   * 解析Vue组件
   * @description 解析Vue单文件组件的各个部分
   * @param {string} content Vue组件内容
   * @returns {Object} 解析后的组件信息
   */
  const parseVueComponent = (content) => {
    const templateMatch = content.match(/<template[^>]*>([\s\S]*?)<\/template>/)
    const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/)
    const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/)
    
    return {
      template: templateMatch ? templateMatch[1].trim() : '',
      script: scriptMatch ? evaluateScript(scriptMatch[1]) : {},
      style: styleMatch ? styleMatch[1].trim() : ''
    }
  }
  
  /**
   * 执行脚本代码
   * @description 安全地执行Vue组件的script部分
   * @param {string} scriptCode script代码
   * @returns {Object} 执行结果
   */
  const evaluateScript = (scriptCode) => {
    try {
      // 创建安全的执行环境
      const require = createMockRequire()
      const exports = {}
      const module = { exports }
      
      // 创建函数并执行
      const func = new Function('require', 'exports', 'module', scriptCode)
      func(require, exports, module)
      
      // 返回exports或module.exports
      return module.exports.default || module.exports || exports
    } catch (error) {
      log('脚本执行失败', { error: error.message }, 'warn')
      return {}
    }
  }
  
  /**
   * 注入样式
   * @description 将CSS样式注入到页面中
   * @param {string} css CSS代码
   * @param {string} templateId 模板ID
   */
  const injectStyles = (css, templateId) => {
    if (!css || typeof window === 'undefined') return
    
    try {
      // 检查是否已经注入过该模板的样式
      const existingStyle = document.getElementById(`template-style-${templateId}`)
      if (existingStyle) {
        existingStyle.remove()
      }
      
      // 创建新的style元素
      const styleElement = document.createElement('style')
      styleElement.id = `template-style-${templateId}`
      styleElement.textContent = css
      
      // 添加到head中
      document.head.appendChild(styleElement)
      
      log('样式注入成功', { templateId })
    } catch (error) {
      log('样式注入失败', { error: error.message }, 'warn')
    }
  }
  
  /**
   * 创建模拟require函数
   * @description 为组件脚本提供基础的require支持
   * @returns {Function} 模拟的require函数
   */
  const createMockRequire = () => {
    return (moduleName) => {
      // 提供基础的模块支持
      switch (moduleName) {
        case 'vue':
          return { ref, reactive, computed, nextTick }
        default:
          log(`未知模块: ${moduleName}`, {}, 'warn')
          return {}
      }
    }
  }
  
  // ================================
  // 工具方法
  // ================================
  
  /**
   * 计算缓存内存使用量
   * @description 估算缓存占用的内存大小
   * @returns {number} 内存使用量（字节）
   */
  const calculateCacheMemoryUsage = () => {
    let totalSize = 0
    
    // 计算模板缓存大小
    templateCache.forEach((template) => {
      totalSize += JSON.stringify(template).length * 2 // 粗略估算
    })
    
    // 计算默认内容缓存大小
    defaultContentCache.forEach((content) => {
      totalSize += JSON.stringify(content).length * 2 // 粗略估算
    })
    
    return totalSize
  }
  
  /**
   * 设置加载状态
   * @description 更新加载状态信息
   * @param {boolean} isLoading 是否正在加载
   * @param {string} templateId 模板ID
   */
  const setLoadingState = (isLoading, templateId = null) => {
    loadingState.isLoading = isLoading
    loadingState.currentTemplateId = templateId
    
    if (isLoading) {
      loadingState.startTime = Date.now()
      loadingState.progress = 0
      loadingState.error = null
    } else {
      if (loadingState.startTime) {
        loadingState.loadTime = Date.now() - loadingState.startTime
      }
      loadingState.progress = 100
    }
  }
  
  /**
   * 更新加载进度
   * @description 更新当前加载进度
   * @param {number} progress 进度值（0-100）
   */
  const updateProgress = (progress) => {
    loadingState.progress = Math.min(100, Math.max(0, progress))
  }
  
  /**
   * 设置加载错误
   * @description 设置加载过程中的错误信息
   * @param {Error} error 错误对象
   */
  const setLoadingError = (error) => {
    loadingState.error = error.message || '未知错误'
    loadingState.isLoading = false
    loadingState.isLoadingDefaultContent = false
    log('加载错误', { error: error.message }, 'error')
  }
  
  /**
   * 设置活动模板
   * @description 设置当前活动的模板
   * @param {Object} template 模板对象
   */
  const setActiveTemplate = (template) => {
    activeTemplate.value = template
    log('设置活动模板', {
      templateId: template.templateId,
      templateName: template.templateName
    })
  }
  
  /**
   * 日志记录
   * @description 统一的日志记录方法
   * @param {string} message 日志消息
   * @param {Object} data 附加数据
   * @param {string} level 日志级别
   */
  const log = (message, data = {}, level = 'info') => {
    const logData = {
      timestamp: new Date().toISOString(),
      component: 'useTemplateLoader',
      message,
      ...data
    }
    
    switch (level) {
      case 'error':
        console.error('[TemplateLoader]', message, logData)
        break
      case 'warn':
        console.warn('[TemplateLoader]', message, logData)
        break
      case 'debug':
        console.debug('[TemplateLoader]', message, logData)
        break
      default:
        console.log('[TemplateLoader]', message, logData)
    }
  }
  
  /**
   * 清除缓存
   * @description 清除指定或全部缓存
   * @param {string} cacheKey 缓存键（可选）
   */
  const clearCache = (cacheKey = null) => {
    if (cacheKey) {
      templateCache.delete(cacheKey)
      defaultContentCache.delete(cacheKey)
      log('清除指定缓存', { cacheKey })
    } else {
      templateCache.clear()
      defaultContentCache.clear()
      log('清除全部缓存')
    }
  }
  
  /**
   * 预加载模板
   * @description 批量预加载模板列表
   * @param {Array} templateList 模板列表
   * @returns {Promise<Array>} 预加载结果
   */
  const preloadTemplates = async (templateList) => {
    const results = []
    
    for (const templateInfo of templateList) {
      try {
        const result = await loadTemplate(templateInfo)
        results.push({ success: true, templateInfo, result })
      } catch (error) {
        results.push({ success: false, templateInfo, error: error.message })
      }
    }
    
    log('批量预加载完成', {
      total: templateList.length,
      success: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    })
    
    return results
  }
  
  // ================================
  // 返回API
  // ================================
  return {
    // 状态
    loadingState,
    activeTemplate,
    currentDefaultContent,
    compiledComponent,
    
    // 计算属性
    hasTemplate,
    hasDefaultContent,
    templateMeta,
    cacheStats,
    
    // 核心方法
    loadTemplate,
    getDefaultContent,
    loadTemplateWithContent,
    loadTemplateByCode,
    compileTemplate,
    
    // 工具方法
    clearCache,
    preloadTemplates,
    
    // 内部方法（用于测试）
    parseVueComponent,
    getMockTemplateResponse,
    getMockDefaultContentResponse
  }
} 