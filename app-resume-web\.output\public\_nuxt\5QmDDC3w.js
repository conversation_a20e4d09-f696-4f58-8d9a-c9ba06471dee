import{_ as k}from"./DlAUqK2U.js";import{r as v,c as i,a,f as m,s as d,h as o,q as h,F as u,g as r,o as c,t as e}from"./CURHyiUL.js";const f={class:"admin-memberships"},P={class:"content-area"},y={class:"tabs-container"},C={class:"tabs"},T={class:"tab-content"},V={key:0,class:"members-section"},I={class:"table-content"},M={class:"data-table"},w={key:1,class:"packages-section"},B={class:"packages-grid"},D={class:"package-header"},F={class:"package-price"},G={class:"package-features"},K={class:"package-stats"},N={class:"stat"},S={class:"value"},U={class:"stat"},z={class:"package-actions"},L={__name:"memberships",setup($){const n=v("members"),_=v([{id:"U001",username:"张三",email:"<EMAIL>",level:"premium",expireTime:"2024-12-31",status:"active"},{id:"U002",username:"李四",email:"<EMAIL>",level:"vip",expireTime:"2024-06-30",status:"active"},{id:"U003",username:"王五",email:"<EMAIL>",level:"basic",expireTime:"2024-03-15",status:"expired"}]),g=v([{id:"PKG001",name:"基础会员",price:29,duration:"月",features:["下载5个模板","基础客服支持","简历导出PDF"],purchaseCount:234,status:"active"},{id:"PKG002",name:"高级会员",price:99,duration:"月",features:["下载20个模板","优先客服支持","简历导出多种格式","简历在线分享"],purchaseCount:89,status:"active"},{id:"PKG003",name:"VIP会员",price:299,duration:"年",features:["无限下载模板","专属客服","所有高级功能","定制模板服务"],purchaseCount:23,status:"active"}]),x=l=>({basic:"基础会员",premium:"高级会员",vip:"VIP会员"})[l]||l,p=l=>({active:"正常",expired:"已过期",suspended:"已暂停",inactive:"未激活"})[l]||l;return(l,t)=>(c(),i("div",f,[t[10]||(t[10]=a("div",{class:"page-header"},[a("h1",{class:"page-title"},"会员管理"),a("p",{class:"page-description"},"管理会员套餐、会员用户和会员权益")],-1)),a("div",P,[t[9]||(t[9]=m('<div class="stats-cards" data-v-a94bca76><div class="stat-card" data-v-a94bca76><div class="stat-icon" data-v-a94bca76>👥</div><div class="stat-content" data-v-a94bca76><h3 data-v-a94bca76>会员总数</h3><p class="stat-number" data-v-a94bca76>456</p></div></div><div class="stat-card" data-v-a94bca76><div class="stat-icon" data-v-a94bca76>⭐</div><div class="stat-content" data-v-a94bca76><h3 data-v-a94bca76>高级会员</h3><p class="stat-number" data-v-a94bca76>89</p></div></div><div class="stat-card" data-v-a94bca76><div class="stat-icon" data-v-a94bca76>💎</div><div class="stat-content" data-v-a94bca76><h3 data-v-a94bca76>VIP会员</h3><p class="stat-number" data-v-a94bca76>23</p></div></div><div class="stat-card" data-v-a94bca76><div class="stat-icon" data-v-a94bca76>💰</div><div class="stat-content" data-v-a94bca76><h3 data-v-a94bca76>会员收入</h3><p class="stat-number" data-v-a94bca76>¥45,678</p></div></div></div>',1)),a("div",y,[a("div",C,[a("button",{class:d(["tab-btn",{active:o(n)==="members"}]),onClick:t[0]||(t[0]=s=>n.value="members")}," 会员用户 ",2),a("button",{class:d(["tab-btn",{active:o(n)==="packages"}]),onClick:t[1]||(t[1]=s=>n.value="packages")}," 会员套餐 ",2)]),a("div",T,[o(n)==="members"?(c(),i("div",V,[t[4]||(t[4]=m('<div class="section-header" data-v-a94bca76><h2 data-v-a94bca76>会员用户</h2><div class="section-actions" data-v-a94bca76><input type="text" placeholder="搜索用户..." class="search-input" data-v-a94bca76><select class="filter-select" data-v-a94bca76><option value="" data-v-a94bca76>全部等级</option><option value="basic" data-v-a94bca76>基础会员</option><option value="premium" data-v-a94bca76>高级会员</option><option value="vip" data-v-a94bca76>VIP会员</option></select></div></div>',1)),a("div",I,[a("table",M,[t[3]||(t[3]=a("thead",null,[a("tr",null,[a("th",null,"用户ID"),a("th",null,"用户名"),a("th",null,"邮箱"),a("th",null,"会员等级"),a("th",null,"到期时间"),a("th",null,"状态"),a("th",null,"操作")])],-1)),a("tbody",null,[(c(!0),i(u,null,r(o(_),s=>(c(),i("tr",{key:s.id},[a("td",null,e(s.id),1),a("td",null,e(s.username),1),a("td",null,e(s.email),1),a("td",null,[a("span",{class:d(["level-badge",s.level])},e(x(s.level)),3)]),a("td",null,e(s.expireTime),1),a("td",null,[a("span",{class:d(["status-badge",s.status])},e(p(s.status)),3)]),t[2]||(t[2]=a("td",null,[a("div",{class:"action-buttons"},[a("button",{class:"btn-view"},"查看"),a("button",{class:"btn-edit"},"编辑"),a("button",{class:"btn-extend"},"续期")])],-1))]))),128))])])])])):h("",!0),o(n)==="packages"?(c(),i("div",w,[t[8]||(t[8]=a("div",{class:"section-header"},[a("h2",null,"会员套餐"),a("div",{class:"section-actions"},[a("button",{class:"btn-add"},"+ 添加套餐")])],-1)),a("div",B,[(c(!0),i(u,null,r(o(g),s=>(c(),i("div",{key:s.id,class:"package-card"},[a("div",D,[a("h3",null,e(s.name),1),a("span",F,"¥"+e(s.price)+"/"+e(s.duration),1)]),a("div",G,[a("ul",null,[(c(!0),i(u,null,r(s.features,b=>(c(),i("li",{key:b},e(b),1))),128))])]),a("div",K,[a("div",N,[t[5]||(t[5]=a("span",{class:"label"},"购买数量:",-1)),a("span",S,e(s.purchaseCount),1)]),a("div",U,[t[6]||(t[6]=a("span",{class:"label"},"状态:",-1)),a("span",{class:d(["status-badge",s.status])},e(p(s.status)),3)])]),a("div",z,[t[7]||(t[7]=a("button",{class:"btn-edit"},"编辑",-1)),a("button",{class:d(["btn-toggle",s.status])},e(s.status==="active"?"禁用":"启用"),3)])]))),128))])])):h("",!0)])])])]))}},j=k(L,[["__scopeId","data-v-a94bca76"]]);export{j as default};
