import{r as c,k as C,i as j,n as y,c as i,h as s,a as t,q as V,f as L,m as B,p as v,v as z,x as S,y as A,d as T,t as M,s as q,o as a}from"./CURHyiUL.js";import{u as _}from"./CwmtQiCm.js";import{_ as D}from"./DlAUqK2U.js";const H={key:0,class:"min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 flex items-center justify-center"},N={key:1,class:"min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8"},U={class:"max-w-md w-full space-y-8"},$={class:"bg-white/10 backdrop-blur-md rounded-xl p-8 shadow-2xl"},I={class:"space-y-6"},E={class:"relative"},F=["disabled"],P={class:"relative"},R=["type","disabled"],G={key:0,class:"h-5 w-5 text-blue-300 hover:text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},J={key:1,class:"h-5 w-5 text-blue-300 hover:text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},K={class:"flex items-center justify-between"},O={class:"flex items-center"},Q=["disabled"],W={class:"mt-8"},X=["disabled"],Y={class:"absolute left-0 inset-y-0 flex items-center pl-3"},Z={key:0,class:"h-5 w-5 text-blue-300 group-hover:text-blue-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ee={key:1,class:"h-5 w-5 text-blue-300 animate-spin",fill:"none",viewBox:"0 0 24 24"},te={class:"mt-6 pt-6 border-t border-white/20"},se={class:"flex space-x-3"},oe=["disabled"],le=["disabled"],re={class:"flex items-center"},ne={key:0,class:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ie={key:1,class:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ae={__name:"login",setup(de){const o=c({phone:"",password:"",rememberMe:!1}),l=c(!1),p=c(!1),x=c(!1),u=c({show:!1,type:"success",text:""});C(()=>o.value.phone.trim()!==""&&o.value.password.trim()!=="");const b=(n,e="success")=>{u.value={show:!0,text:n,type:e},setTimeout(()=>{u.value.show=!1},3e3)},f=async()=>{var n,e,r,w,g;if(!l.value){if(!o.value.phone.trim()||!o.value.password.trim()){b("请填写完整的登录信息","error");return}l.value=!0;try{const d=_(),m={phone:o.value.phone,password:o.value.password,platform:"web"},k=await d.adminLogin(m);k.success?(b("管理员登录成功，正在跳转...","success"),setTimeout(()=>{y("/admin")},1e3)):b(k.error||"管理员登录失败","error")}catch(d){console.error("管理员登录失败:",d);let m="管理员登录失败，请重试";(n=d.message)!=null&&n.includes("权限不足")?m="权限不足，您不是管理员用户":(e=d.message)!=null&&e.includes("用户不存在")||(r=d.message)!=null&&r.includes("密码错误")?m="用户名或密码错误":(w=d.message)!=null&&w.includes("500")?m="服务器错误，请稍后重试":(g=d.message)!=null&&g.includes("网络")&&(m="网络连接失败，请检查网络"),b(m,"error")}finally{l.value=!1}}},h=async n=>{n==="admin"?(o.value.phone="13800138001",o.value.password="123456"):n==="super"&&(o.value.phone="13800138002",o.value.password="123456"),await f()};return j(()=>{x.value=!0,_().isAdminLoggedIn()&&y("/admin")}),(n,e)=>s(x)?(a(),i("div",N,[t("div",U,[e[18]||(e[18]=L('<div data-v-18ee7e11><div class="mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center mb-6" data-v-18ee7e11><svg class="h-10 w-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-18ee7e11><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" data-v-18ee7e11></path></svg></div><h2 class="mt-6 text-center text-3xl font-extrabold text-white" data-v-18ee7e11> 管理员登录 </h2><p class="mt-2 text-center text-sm text-blue-200" data-v-18ee7e11> 请使用管理员账户登录后台管理系统 </p></div>',1)),t("form",{class:"mt-8 space-y-6",onSubmit:B(f,["prevent"])},[t("div",$,[t("div",I,[t("div",null,[e[8]||(e[8]=t("label",{for:"phone",class:"block text-sm font-medium text-blue-100 mb-2"}," 手机号 ",-1)),t("div",E,[e[7]||(e[7]=t("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[t("svg",{class:"h-5 w-5 text-blue-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1)),v(t("input",{id:"phone","onUpdate:modelValue":e[0]||(e[0]=r=>s(o).phone=r),type:"tel",required:"",class:"appearance-none relative block w-full px-3 py-3 pl-10 border border-white/20 placeholder-blue-300 text-white bg-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent sm:text-sm backdrop-blur-sm",placeholder:"请输入手机号",disabled:s(l)},null,8,F),[[z,s(o).phone]])])]),t("div",null,[e[12]||(e[12]=t("label",{for:"password",class:"block text-sm font-medium text-blue-100 mb-2"}," 密码 ",-1)),t("div",P,[e[11]||(e[11]=t("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[t("svg",{class:"h-5 w-5 text-blue-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),v(t("input",{id:"password","onUpdate:modelValue":e[1]||(e[1]=r=>s(o).password=r),type:s(p)?"text":"password",required:"",class:"appearance-none relative block w-full px-3 py-3 pl-10 pr-10 border border-white/20 placeholder-blue-300 text-white bg-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent sm:text-sm backdrop-blur-sm",placeholder:"请输入密码",disabled:s(l)},null,8,R),[[S,s(o).password]]),t("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:e[2]||(e[2]=r=>p.value=!s(p))},[s(p)?(a(),i("svg",J,e[10]||(e[10]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878L12 12m0 0l2.122 2.122M12 12L8.464 8.464"},null,-1)]))):(a(),i("svg",G,e[9]||(e[9]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))])])]),t("div",K,[t("div",O,[v(t("input",{id:"remember-me","onUpdate:modelValue":e[3]||(e[3]=r=>s(o).rememberMe=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-white/30 rounded bg-white/10",disabled:s(l)},null,8,Q),[[A,s(o).rememberMe]]),e[13]||(e[13]=t("label",{for:"remember-me",class:"ml-2 block text-sm text-blue-200"}," 记住登录状态 ",-1))]),e[14]||(e[14]=t("div",{class:"text-sm"},[t("a",{href:"#",class:"font-medium text-blue-300 hover:text-white transition-colors"}," 忘记密码？ ")],-1))])]),t("div",W,[t("button",{type:"submit",disabled:s(l),class:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"},[t("span",Y,[s(l)?(a(),i("svg",ee,e[16]||(e[16]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(a(),i("svg",Z,e[15]||(e[15]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"},null,-1)])))]),T(" "+M(s(l)?"登录中...":"登录"),1)],8,X)]),t("div",te,[e[17]||(e[17]=t("div",{class:"text-center text-sm text-blue-200 mb-4"}," 开发环境快速登录 ",-1)),t("div",se,[t("button",{type:"button",onClick:e[4]||(e[4]=r=>h("admin")),disabled:s(l),class:"flex-1 py-2 px-3 text-xs font-medium text-blue-200 bg-white/10 hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50"}," 管理员 ",8,oe),t("button",{type:"button",onClick:e[5]||(e[5]=r=>h("super")),disabled:s(l),class:"flex-1 py-2 px-3 text-xs font-medium text-blue-200 bg-white/10 hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50"}," 超级管理员 ",8,le)])])])],32),e[19]||(e[19]=t("div",{class:"text-center"},[t("p",{class:"text-xs text-blue-300"}," © 2025 Resume System. All rights reserved. ")],-1))]),s(u).show?(a(),i("div",{key:0,class:q(["fixed top-4 right-4 max-w-sm w-full p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300",s(u).type==="success"?"bg-green-500 text-white":"bg-red-500 text-white",s(u).show?"translate-x-0 opacity-100":"translate-x-full opacity-0"])},[t("div",re,[s(u).type==="success"?(a(),i("svg",ne,e[20]||(e[20]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))):(a(),i("svg",ie,e[21]||(e[21]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1)]))),t("span",null,M(s(u).text),1)])],2)):V("",!0)])):(a(),i("div",H,e[6]||(e[6]=[t("div",{class:"text-white text-center"},[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),t("p",null,"加载中...")],-1)])))}},pe=D(ae,[["__scopeId","data-v-18ee7e11"]]);export{pe as default};
