<template>
  <div>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-primary-50 via-white to-orange-50 pt-16">
      <div class="max-w-screen-2xl mx-auto px-6 lg:px-8 py-20 lg:py-32">
        <div class="max-w-4xl mx-auto text-center">
          <!-- Main Content -->
          <div class="animate-slide-up">
            <h1 class="font-display font-bold text-4xl sm:text-5xl lg:text-6xl text-secondary-900 leading-tight mb-6">
              <span class="gradient-text">火花一点，职场即燃！</span>
            </h1>
            <p class="text-xl text-secondary-600 mb-8 leading-relaxed max-w-2xl mx-auto">
              海量精美模板，智能编辑，一键导出。让你获得更多面试机会！
            </p>
            
            <div class="flex justify-center mb-8">
              <NuxtLink to="/templates" class="btn-primary text-lg px-8 py-4">
                免费制作简历
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </NuxtLink>
            </div>

            <!-- Features -->
            <div class="flex flex-wrap justify-center items-center gap-8 text-secondary-700">
              <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-orange-500 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                </div>
                <span class="font-medium">海量简历模板</span>
              </div>
              
              <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-gradient-to-br from-success-500 to-emerald-500 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <span class="font-medium">5分钟制作简历</span>
              </div>
              
              <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
                <span class="font-medium">服务百万用户</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
      <div class="max-w-screen-2xl mx-auto px-6 lg:px-8">
        <div class="text-center mb-16 animate-slide-up">
          <h2 class="font-display font-bold text-3xl lg:text-4xl text-secondary-900 mb-4">
            为什么选择我们的简历工具
          </h2>
          <p class="text-xl text-secondary-600 max-w-3xl mx-auto">
            专业设计师精心打造的模板，智能化编辑体验，让简历制作变得轻松高效
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div class="card-hover p-8 text-center animate-slide-up" style="animation-delay: 0.1s;">
            <div class="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="font-display font-semibold text-xl text-secondary-900 mb-4">专业模板</h3>
            <p class="text-secondary-600">150+ 由专业设计师创作的简历模板，覆盖各行各业，总有一款适合你</p>
          </div>

          <div class="card-hover p-8 text-center animate-slide-up" style="animation-delay: 0.2s;">
            <div class="w-16 h-16 bg-success-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="font-display font-semibold text-xl text-secondary-900 mb-4">智能编辑</h3>
            <p class="text-secondary-600">AI 智能推荐内容，实时预览效果，拖拽式编辑，让简历制作变得简单</p>
          </div>

          <div class="card-hover p-8 text-center animate-slide-up" style="animation-delay: 0.3s;">
            <div class="w-16 h-16 bg-warning-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <h3 class="font-display font-semibold text-xl text-secondary-900 mb-4">隐私保护</h3>
            <p class="text-secondary-600">严格的数据加密和隐私保护，你的个人信息安全我们来守护</p>
          </div>

          <div class="card-hover p-8 text-center animate-slide-up" style="animation-delay: 0.4s;">
            <div class="w-16 h-16 bg-danger-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-danger-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
            </div>
            <h3 class="font-display font-semibold text-xl text-secondary-900 mb-4">多格式导出</h3>
            <p class="text-secondary-600">支持 PDF、Word、图片等多种格式导出，满足不同场景使用需求</p>
          </div>

          <div class="card-hover p-8 text-center animate-slide-up" style="animation-delay: 0.5s;">
            <div class="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
              </svg>
            </div>
            <h3 class="font-display font-semibold text-xl text-secondary-900 mb-4">便捷分享</h3>
            <p class="text-secondary-600">一键生成分享链接，在线展示简历，让 HR 随时随地查看你的简历</p>
          </div>

          <div class="card-hover p-8 text-center animate-slide-up" style="animation-delay: 0.6s;">
            <div class="w-16 h-16 bg-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
            <h3 class="font-display font-semibold text-xl text-secondary-900 mb-4">24/7 支持</h3>
            <p class="text-secondary-600">专业客服团队随时为你解答问题，让你的简历制作过程无忧无虑</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Popular Templates Section -->
    <section class="py-20 bg-gradient-to-br from-orange-50 to-primary-50">
      <div class="max-w-screen-2xl mx-auto px-6 lg:px-8">
        <div class="text-center mb-16 animate-slide-up">
          <h2 class="font-display font-bold text-3xl lg:text-4xl text-secondary-900 mb-4">
            热门简历模板
          </h2>
          <p class="text-xl text-secondary-600 max-w-3xl mx-auto">
            精选最受欢迎的简历模板，适合不同行业和职位需求
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <div 
            v-for="template in popularTemplates" 
            :key="template.id"
            class="card-hover group cursor-pointer animate-slide-up"
          >
            <div class="aspect-[3/4] bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-t-2xl relative overflow-hidden">
              <div class="absolute inset-4 bg-white rounded-lg shadow-sm p-4">
                <div class="space-y-3">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-200 rounded-full"></div>
                    <div>
                      <div class="h-2 bg-secondary-300 rounded w-16 mb-1"></div>
                      <div class="h-1.5 bg-secondary-200 rounded w-12"></div>
                    </div>
                  </div>
                  <div class="space-y-1.5">
                    <div class="h-1.5 bg-secondary-300 rounded w-full"></div>
                    <div class="h-1.5 bg-secondary-200 rounded w-4/5"></div>
                    <div class="h-1.5 bg-secondary-200 rounded w-3/4"></div>
                  </div>
                  <div class="grid grid-cols-2 gap-2">
                    <div>
                      <div class="h-1.5 bg-primary-200 rounded w-full mb-1"></div>
                      <div class="h-1 bg-secondary-200 rounded w-3/4"></div>
                    </div>
                    <div>
                      <div class="h-1.5 bg-success-200 rounded w-full mb-1"></div>
                      <div class="h-1 bg-secondary-200 rounded w-2/3"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Hover Overlay -->
              <div class="absolute inset-0 bg-primary-600/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <button class="btn-primary">
                  使用模板
                </button>
              </div>
            </div>
            
            <div class="p-6">
              <h3 class="font-display font-semibold text-lg text-secondary-900 mb-2">{{ template.name }}</h3>
              <p class="text-secondary-600 text-sm mb-4">{{ template.description }}</p>
              <div class="flex items-center justify-between">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {{ template.category }}
                </span>
                <div class="flex items-center text-sm text-secondary-500">
                  <svg class="w-4 h-4 mr-1 text-warning-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                  </svg>
                  {{ template.rating }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center">
          <NuxtLink to="/templates" class="btn-outline">
            查看全部模板
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-primary-600 to-red-600">
      <div class="max-w-screen-2xl mx-auto px-6 lg:px-8 text-center">
        <div class="max-w-3xl mx-auto animate-slide-up">
          <h2 class="font-display font-bold text-3xl lg:text-4xl text-white mb-6">
            准备好制作你的专业简历了吗？
          </h2>
          <p class="text-xl text-primary-100 mb-8">
            加入超过 100,000 名求职者的行列，使用我们的工具制作出色的简历
          </p>
          <NuxtLink to="/templates" class="btn-secondary bg-white text-primary-600 hover:bg-primary-50 text-lg px-8 py-4">
            立即免费开始
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
          </NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// SEO Meta
useSeoMeta({
  title: '火花简历 - 专业在线简历生成器',
  ogTitle: '火花简历 - 专业在线简历生成器',
  description: '提供100+专业简历模板，支持在线编辑、一键导出。帮助求职者制作出色的简历，提升面试成功率。',
  ogDescription: '提供100+专业简历模板，支持在线编辑、一键导出。',
  keywords: '在线简历制作,免费简历模板,简历生成器,求职简历,简历设计,火花简历',
  ogImage: '/images/og-homepage.jpg',
  twitterCard: 'summary_large_image'
})

// 检查 URL 参数，如果有 showLogin 参数则打开登录弹窗
onMounted(() => {
  const route = useRoute()
  if (route.query.showLogin === 'true') {
    // 通过全局事件触发登录弹窗
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('open-login-modal'))
    }, 100)
  }
})

// 结构化数据 (待后续SEO模块添加后启用)
// useSchemaOrg([
//   defineWebSite({
//     name: '简历制作工具',
//     url: 'https://your-domain.com',
//     description: '专业的在线简历制作平台'
//   }),
//   defineOrganization({
//     name: '简历制作工具 Inc.',
//     url: 'https://your-domain.com',
//     logo: 'https://your-domain.com/logo.png'
//   })
// ])

// 模拟热门模板数据
const popularTemplates = ref([
  {
    id: 1,
    name: '简约商务',
    description: '专业商务风格，适合职场精英',
    category: '商务类',
    rating: 4.8
  },
  {
    id: 2,
    name: '创意设计',
    description: '创意十足，展现个人特色',
    category: '创意类',
    rating: 4.7
  },
  {
    id: 3,
    name: '技术极客',
    description: '科技感十足，程序员首选',
    category: '技术类',
    rating: 4.9
  },
  {
    id: 4,
    name: '清新文艺',
    description: '文艺清新，适合文创行业',
    category: '文艺类',
    rating: 4.6
  },
  {
    id: 5,
    name: '经典传统',
    description: '传统格式，稳重可靠',
    category: '传统类',
    rating: 4.5
  },
  {
    id: 6,
    name: '现代时尚',
    description: '时尚前卫，彰显个性',
    category: '时尚类',
    rating: 4.7
  }
])
</script> 