const sources = [
    {
        "context": {
            "name": "sitemap:urls",
            "description": "Set with the `sitemap.urls` config."
        },
        "urls": [],
        "sourceType": "user"
    },
    {
        "context": {
            "name": "nuxt:pages",
            "description": "Generated from your static page files.",
            "tips": [
                "Can be disabled with `{ excludeAppSources: ['nuxt:pages'] }`."
            ]
        },
        "urls": [
            {
                "loc": "/guide"
            },
            {
                "loc": "/"
            },
            {
                "loc": "/login"
            },
            {
                "loc": "/register"
            },
            {
                "loc": "/tutorial"
            },
            {
                "loc": "/my-resumes"
            },
            {
                "loc": "/admin"
            },
            {
                "loc": "/admin/login"
            },
            {
                "loc": "/admin/users"
            },
            {
                "loc": "/admin/orders"
            },
            {
                "loc": "/editor"
            },
            {
                "loc": "/admin/resumes"
            },
            {
                "loc": "/profile"
            },
            {
                "loc": "/__tests__/test"
            },
            {
                "loc": "/templates"
            },
            {
                "loc": "/admin/memberships"
            },
            {
                "loc": "/__tests__/auth-test"
            },
            {
                "loc": "/admin/template/list"
            },
            {
                "loc": "/__tests__/auth-debug"
            },
            {
                "loc": "/__tests__/click-test"
            },
            {
                "loc": "/__tests__/route-test"
            },
            {
                "loc": "/__tests__/basic-debug"
            },
            {
                "loc": "/__tests__/simple-test"
            },
            {
                "loc": "/admin/template/upload"
            },
            {
                "loc": "/__tests__/preview-test"
            },
            {
                "loc": "/__tests__/sidebar-test"
            },
            {
                "loc": "/__tests__/admin-test-ui"
            },
            {
                "loc": "/__tests__/download-demo"
            },
            {
                "loc": "/__tests__/download-test"
            },
            {
                "loc": "/__tests__/template-test"
            },
            {
                "loc": "/__tests__/test-download"
            },
            {
                "loc": "/__tests__/data-flow-test"
            },
            {
                "loc": "/admin/template/converter"
            },
            {
                "loc": "/__tests__/basic-info-test"
            },
            {
                "loc": "/admin/template/categories"
            },
            {
                "loc": "/__tests__/admin-auth-debug"
            },
            {
                "loc": "/__tests__/admin-login-test"
            },
            {
                "loc": "/__tests__/admin-test-guide"
            },
            {
                "loc": "/__tests__/simple-route-test"
            },
            {
                "loc": "/profile/components/MyOrders"
            },
            {
                "loc": "/profile/components/MyResumes"
            },
            {
                "loc": "/__tests__/admin-template-test"
            },
            {
                "loc": "/profile/components/MyFavorites"
            },
            {
                "loc": "/__tests__/rich-text-editor-test"
            },
            {
                "loc": "/__tests__/project-data-sync-test"
            },
            {
                "loc": "/__tests__/html-specification-test"
            },
            {
                "loc": "/admin/template/html-specification"
            },
            {
                "loc": "/profile/components/AccountSettings"
            },
            {
                "loc": "/profile/components/MembershipPurchase"
            }
        ],
        "sourceType": "app"
    },
    {
        "context": {
            "name": "nuxt:route-rules",
            "description": "Generated from your route rules config.",
            "tips": [
                "Can be disabled with `{ excludeAppSources: ['nuxt:route-rules'] }`."
            ]
        },
        "urls": [
            "/",
            "/my-resumes",
            "/profile",
            "/login",
            "/register"
        ],
        "sourceType": "app"
    },
    {
        "context": {
            "name": "nuxt:prerender",
            "description": "Generated at build time when prerendering.",
            "tips": [
                "Can be disabled with `{ excludeAppSources: ['nuxt:prerender'] }`."
            ]
        },
        "urls": [
            "/",
            "/templates",
            {
                "loc": "/templates"
            },
            {
                "loc": "/"
            }
        ],
        "sourceType": "app"
    }
];

export { sources };
//# sourceMappingURL=global-sources.mjs.map
