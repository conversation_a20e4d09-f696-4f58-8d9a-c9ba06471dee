package com.alan6.resume.service.impl;

import com.alan6.resume.dto.category.CategoryRequest;
import com.alan6.resume.dto.category.CategoryResponse;
import com.alan6.resume.entity.ResumeCategories;
import com.alan6.resume.mapper.ResumeCategoriesMapper;
import com.alan6.resume.service.IResumeCategoriesService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 简历分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeCategoriesServiceImpl extends ServiceImpl<ResumeCategoriesMapper, ResumeCategories> implements IResumeCategoriesService {

    @Override
    public List<CategoryResponse> getCategoryTree() {
        log.info("获取分类树结构");
        
        // 查询所有启用的分类
        LambdaQueryWrapper<ResumeCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResumeCategories::getStatus, 1)
               .eq(ResumeCategories::getIsDeleted, 0)
               .orderByAsc(ResumeCategories::getSortOrder)
               .orderByAsc(ResumeCategories::getId);
        
        List<ResumeCategories> allCategories = list(wrapper);
        
        // 转换为DTO
        List<CategoryResponse> allResponses = allCategories.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        // 构建树结构
        return buildTree(allResponses);
    }

    @Override
    public List<CategoryResponse> getCategoriesByType(String categoryType) {
        log.info("获取分类类型为 {} 的分类列表", categoryType);
        
        LambdaQueryWrapper<ResumeCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResumeCategories::getCategoryType, categoryType)
               .eq(ResumeCategories::getStatus, 1)
               .eq(ResumeCategories::getIsDeleted, 0)
               .orderByAsc(ResumeCategories::getSortOrder);
        
        List<ResumeCategories> categories = list(wrapper);
        return categories.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public boolean createCategory(CategoryRequest request) {
        log.info("创建分类: {}", request.getName());
        
        // 检查代码是否重复
        if (existsByCode(request.getCode(), null)) {
            throw new RuntimeException("分类代码已存在");
        }
        
        ResumeCategories category = new ResumeCategories();
        BeanUtils.copyProperties(request, category);
        category.setStatus((byte) 1);
        
        boolean result = save(category);
        log.info("创建分类结果: {}", result);
        return result;
    }

    @Override
    public boolean updateCategory(CategoryRequest request) {
        log.info("更新分类: {}", request.getId());
        
        // 检查代码是否重复
        if (existsByCode(request.getCode(), request.getId())) {
            throw new RuntimeException("分类代码已存在");
        }
        
        ResumeCategories category = new ResumeCategories();
        BeanUtils.copyProperties(request, category);
        
        boolean result = updateById(category);
        log.info("更新分类结果: {}", result);
        return result;
    }

    @Override
    public boolean deleteCategory(Long id) {
        log.info("删除分类: {}", id);
        
        // 检查是否有子分类
        LambdaQueryWrapper<ResumeCategories> childWrapper = new LambdaQueryWrapper<>();
        childWrapper.eq(ResumeCategories::getParentId, id)
                   .eq(ResumeCategories::getIsDeleted, 0);
        long childCount = count(childWrapper);
        
        if (childCount > 0) {
            throw new RuntimeException("该分类下还有子分类，无法删除");
        }
        
        // 检查是否有关联的模板
        Integer templateCount = baseMapper.getTemplateCountByCategoryId(id);
        if (templateCount != null && templateCount > 0) {
            throw new RuntimeException("该分类下还有模板，无法删除");
        }
        
        boolean result = removeById(id);
        log.info("删除分类结果: {}", result);
        return result;
    }

    @Override
    public List<CategoryResponse> getChildCategories(Long parentId) {
        log.info("获取父分类 {} 的子分类", parentId);
        
        LambdaQueryWrapper<ResumeCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResumeCategories::getParentId, parentId)
               .eq(ResumeCategories::getStatus, 1)
               .eq(ResumeCategories::getIsDeleted, 0)
               .orderByAsc(ResumeCategories::getSortOrder);
        
        List<ResumeCategories> categories = list(wrapper);
        return categories.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByCode(String code, Long excludeId) {
        LambdaQueryWrapper<ResumeCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResumeCategories::getCode, code)
               .eq(ResumeCategories::getIsDeleted, 0);
        
        if (excludeId != null) {
            wrapper.ne(ResumeCategories::getId, excludeId);
        }
        
        return count(wrapper) > 0;
    }

    /**
     * 转换为响应DTO
     */
    private CategoryResponse convertToResponse(ResumeCategories category) {
        CategoryResponse response = new CategoryResponse();
        BeanUtils.copyProperties(category, response);
        
        // 获取模板数量
        Integer templateCount = baseMapper.getTemplateCountByCategoryId(category.getId());
        response.setTemplateCount(templateCount != null ? templateCount : 0);
        
        return response;
    }

    /**
     * 构建树结构
     */
    private List<CategoryResponse> buildTree(List<CategoryResponse> allCategories) {
        // 按父ID分组
        Map<Long, List<CategoryResponse>> parentMap = allCategories.stream()
                .collect(Collectors.groupingBy(CategoryResponse::getParentId));
        
        // 获取根节点
        List<CategoryResponse> roots = parentMap.getOrDefault(0L, new ArrayList<>());
        
        // 递归构建树
        for (CategoryResponse root : roots) {
            buildChildren(root, parentMap);
        }
        
        return roots;
    }

    /**
     * 递归构建子节点
     */
    private void buildChildren(CategoryResponse parent, Map<Long, List<CategoryResponse>> parentMap) {
        List<CategoryResponse> children = parentMap.getOrDefault(parent.getId(), new ArrayList<>());
        parent.setChildren(children);
        
        for (CategoryResponse child : children) {
            buildChildren(child, parentMap);
        }
    }
} 