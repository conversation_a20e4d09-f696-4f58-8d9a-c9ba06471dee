<template>
  <div class="auth-debug-page">
    <h1>认证调试页面</h1>
    
    <div class="debug-section">
      <h2>1. 认证状态检查</h2>
      <div class="debug-info">
        <p><strong>Auth Token:</strong> {{ authToken || '未设置' }}</p>
        <p><strong>Token长度:</strong> {{ authToken ? authToken.length : 0 }}</p>
        <p><strong>Token前缀:</strong> {{ authToken ? authToken.substring(0, 20) + '...' : 'N/A' }}</p>
        <button @click="refreshAuthInfo">刷新认证信息</button>
        <button @click="clearAuth">清除认证信息</button>
      </div>
    </div>

    <div class="debug-section">
      <h2>2. 手动测试API调用</h2>
      <div class="test-controls">
        <div>
          <label>模板代码:</label>
          <input v-model="testTemplateCode" placeholder="输入测试代码" />
          <button @click="testCheckCode" :disabled="testing">测试代码检查</button>
        </div>
        <div>
          <label>桶名称:</label>
          <input v-model="testBucketName" placeholder="输入桶名称" />
          <button @click="testCheckBucket" :disabled="testing">测试桶检查</button>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h2>3. 直接API测试</h2>
      <div class="direct-test">
        <button @click="testDirectAPI" :disabled="testing">直接测试API</button>
        <p>这将直接使用fetch调用API，绕过Service层</p>
      </div>
    </div>

    <div class="debug-section">
      <h2>4. 调试日志</h2>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs">清除日志</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const authToken = ref('')
const testTemplateCode = ref('test-code-123')
const testBucketName = ref('resume-template')
const testing = ref(false)
const logs = ref([])

const addLog = (message, type = 'info') => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
}

const refreshAuthInfo = () => {
  authToken.value = localStorage.getItem('admin_token') || ''
  addLog(`认证令牌获取: ${authToken.value ? '有令牌' : '无令牌'}`)
}

const clearAuth = () => {
  localStorage.removeItem('admin_token')
  authToken.value = ''
  addLog('认证信息已清除', 'warning')
}

const testCheckCode = async () => {
  testing.value = true
  addLog(`开始测试模板代码检查: ${testTemplateCode.value}`)
  
  try {
    const token = localStorage.getItem('admin_token')
    addLog(`使用令牌: ${token ? token.substring(0, 20) + '...' : '无令牌'}`)
    
    const url = new URL('/api/admin/template/check-code', window.location.origin)
    url.searchParams.append('templateCode', testTemplateCode.value)
    
    const headers = {
      'Accept': 'application/json'
    }
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
      addLog('已添加Authorization头')
    } else {
      addLog('未添加Authorization头', 'warning')
    }
    
    addLog(`请求URL: ${url.toString()}`)
    addLog(`请求头: ${JSON.stringify(headers)}`)
    
    const response = await fetch(url, {
      method: 'GET',
      headers
    })
    
    addLog(`响应状态: ${response.status} ${response.statusText}`)
    
    if (response.ok) {
      const result = await response.json()
      addLog(`响应结果: ${JSON.stringify(result)}`, 'success')
    } else {
      const errorText = await response.text()
      addLog(`错误响应: ${errorText}`, 'error')
    }
    
  } catch (error) {
    addLog(`请求失败: ${error.message}`, 'error')
  } finally {
    testing.value = false
  }
}

const testCheckBucket = async () => {
  testing.value = true
  addLog(`开始测试桶状态检查: ${testBucketName.value}`)
  
  try {
    const token = localStorage.getItem('admin_token')
    addLog(`使用令牌: ${token ? token.substring(0, 20) + '...' : '无令牌'}`)
    
    const url = new URL('/api/admin/template/check-bucket', window.location.origin)
    url.searchParams.append('bucketName', testBucketName.value)
    
    const headers = {
      'Accept': 'application/json'
    }
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
      addLog('已添加Authorization头')
    } else {
      addLog('未添加Authorization头', 'warning')
    }
    
    addLog(`请求URL: ${url.toString()}`)
    addLog(`请求头: ${JSON.stringify(headers)}`)
    
    const response = await fetch(url, {
      method: 'GET',
      headers
    })
    
    addLog(`响应状态: ${response.status} ${response.statusText}`)
    
    if (response.ok) {
      const result = await response.json()
      addLog(`响应结果: ${JSON.stringify(result)}`, 'success')
    } else {
      const errorText = await response.text()
      addLog(`错误响应: ${errorText}`, 'error')
    }
    
  } catch (error) {
    addLog(`请求失败: ${error.message}`, 'error')
  } finally {
    testing.value = false
  }
}

const testDirectAPI = async () => {
  testing.value = true
  addLog('开始直接API测试')
  
  try {
    const token = localStorage.getItem('admin_token')
    
    // 测试1: 检查当前域名和端口
    addLog(`当前域名: ${window.location.origin}`)
    addLog(`当前路径: ${window.location.pathname}`)
    
    // 测试2: 检查代理是否工作
    const testUrl = '/api/admin/template/check-code?templateCode=test'
    addLog(`测试URL: ${testUrl}`)
    
    const headers = {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
      addLog(`Authorization头: Bearer ${token.substring(0, 20)}...`)
    } else {
      addLog('警告: 没有找到认证令牌', 'warning')
    }
    
    // 详细记录请求信息
    addLog(`请求方法: GET`)
    addLog(`请求头: ${JSON.stringify(headers, null, 2)}`)
    
    const response = await fetch(testUrl, {
      method: 'GET',
      headers
    })
    
    addLog(`响应状态: ${response.status} ${response.statusText}`)
    addLog(`响应头: ${JSON.stringify([...response.headers.entries()], null, 2)}`)
    
    const responseText = await response.text()
    addLog(`响应内容: ${responseText}`)
    
    // 如果是401错误，尝试重新登录
    if (response.status === 401) {
      addLog('检测到401错误，可能需要重新登录', 'error')
      addLog('建议: 1. 检查localStorage中的token是否有效', 'warning')
      addLog('建议: 2. 重新登录管理后台', 'warning')
      addLog('建议: 3. 检查后端服务是否正常运行', 'warning')
    }
    
    // 测试3: 测试另一个接口
    if (response.status === 200) {
      addLog('第一个接口测试成功，继续测试桶检查接口')
      
      const bucketTestUrl = '/api/admin/template/check-bucket?bucketName=resume-template'
      addLog(`桶测试URL: ${bucketTestUrl}`)
      
      const bucketResponse = await fetch(bucketTestUrl, {
        method: 'GET',
        headers
      })
      
      addLog(`桶检查响应状态: ${bucketResponse.status} ${bucketResponse.statusText}`)
      const bucketResponseText = await bucketResponse.text()
      addLog(`桶检查响应内容: ${bucketResponseText}`)
    }
    
  } catch (error) {
    addLog(`直接API测试失败: ${error.message}`, 'error')
    addLog(`错误堆栈: ${error.stack}`, 'error')
  } finally {
    testing.value = false
  }
}

const clearLogs = () => {
  logs.value = []
}

onMounted(() => {
  refreshAuthInfo()
  addLog('页面加载完成')
})
</script>

<style scoped>
.auth-debug-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: monospace;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.debug-section h2 {
  margin-top: 0;
  color: #333;
}

.debug-info p {
  margin: 10px 0;
  padding: 5px 10px;
  background: white;
  border-radius: 4px;
}

.test-controls > div {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.test-controls label {
  min-width: 80px;
}

.test-controls input {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-width: 200px;
}

button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background: #0056b3;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  background: #000;
  color: #fff;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.log-item {
  margin-bottom: 5px;
  padding: 2px 0;
}

.log-item.info {
  color: #fff;
}

.log-item.success {
  color: #4caf50;
}

.log-item.warning {
  color: #ff9800;
}

.log-item.error {
  color: #f44336;
}

.log-time {
  color: #888;
  margin-right: 10px;
}

.direct-test p {
  font-size: 14px;
  color: #666;
  margin: 10px 0;
}
</style> 