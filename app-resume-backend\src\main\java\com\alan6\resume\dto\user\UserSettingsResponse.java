package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户设置响应DTO
 * 
 * 主要功能：
 * 1. 返回用户的个性化设置信息
 * 2. 包含语言偏好等设置项
 * 3. 为后续扩展其他设置预留结构
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "用户设置响应", description = "用户个性化设置信息")
public class UserSettingsResponse {

    /**
     * 界面语言偏好
     * 用户选择的界面语言
     */
    @Schema(description = "界面语言偏好，支持的语言：zh-CN, en, ja, ko")
    private String preferredLanguage;

    /**
     * 创建用户设置响应对象
     * 
     * @param preferredLanguage 语言偏好
     * @return 用户设置响应对象
     */
    public static UserSettingsResponse create(String preferredLanguage) {
        return UserSettingsResponse.builder()
                .preferredLanguage(preferredLanguage)
                .build();
    }
} 