<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>个人简历</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: "Microsoft YaHei", sans-serif;
      background: #fff;
      color: #333;
      padding: 30px;
    }
    .resume {
      width: 794px;
      margin: auto;
      background: #fff;
      border: 1px solid #ccc;
      padding: 40px;
    }
    .header {
      background-color: #4ea1db;
      color: white;
      padding: 20px;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 6px 6px 0 0;
    }

    .section {
      margin-top: 30px;
      position: relative;
    }

.section-title {
  display: inline-flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  position: relative;
}

.section-title span {
  background-color: #4ea1db;
  color: white;
  padding: 4px 10px;
  border-radius: 3px;
  display: inline-block;
  position: relative;
  padding-right: 22px; /* 给三角形留空间 */
}

.section-title span::after {
  content: "";
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
  border-left: 10px solid white;
}


    hr.divider {
      border: none;
      border-top: 1px solid #ccc;
      margin-top: 24px;
    }

    .info-block {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
    }

    .info-left {
      width: 72%;
      line-height: 1.8;
    }

    .info-left p {
      margin-bottom: 4px;
      display: flex;
      justify-content: space-between;
    }

    .info-right img {
      width: 110px;
      height: 140px;
      object-fit: cover;
      border-radius: 3px;
      border: 1px solid #ccc;
    }

    ul {
      padding-left: 20px;
      margin-top: 10px;
    }

    li {
      margin-bottom: 6px;
      text-indent: -1em;
      padding-left: 1em;
    }

    .item {
      margin-top: 16px;
    }

    .item-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 4px;
    }

    .item-title {
      font-size: 15px;
    }

    .item-date {
      font-size: 14px;
      color: #666;
    }

    .education-list, .project-list {
      margin-top: 12px;
    }

  </style>
</head>
<body>
  <div class="resume">
    <div class="header">
      <div>个人简历</div>
      <div>PERSONAL RESUME</div>
    </div>

    <div class="section">
      <div class="section-title"><span>基本信息</span></div>
      <div class="info-block">
        <div class="info-left">
          <p><span>姓名：妮蔓</span><span>求职意向：CAD 设计师</span></p>
          <p><span>期望薪资：10K-11K</span><span>工作地点：深圳</span></p>
          <p><span>求职状态：离职</span><span>性别：女</span></p>
          <p><span>现居城市：深圳</span><span>民族：汉</span></p>
          <p><span>婚姻状况：未婚</span><span>政治面貌：群众</span></p>
          <p><span>电话：13815698369</span><span>邮箱：<EMAIL></span></p>
        </div>
        <div class="info-right">
          <img src="https://via.placeholder.com/110x140" alt="头像" />
        </div>
      </div>
      <hr class="divider" />
    </div>

    <div class="section">
      <div class="section-title"><span>我的优势</span></div>
      <ul>
        <li>技术精湛：熟练操作CAD软件，能够快速完成复杂图形与三维建模。</li>
        <li>经验丰富：参与建筑、机械等多个项目，具备丰富实践经验。</li>
        <li>高效协作：跨部门合作良好，推动项目顺利实施。</li>
      </ul>
      <hr class="divider" />
    </div>

    <div class="section">
      <div class="section-title"><span>工作经历</span></div>
      <div class="item">
        <div class="item-header">
          <div class="item-title">深圳潮图设计院 - 平面设计师</div>
          <div class="item-date">2014.05 ~ 至今</div>
        </div>
        <ul>
          <li>完成建筑与机械类CAD图纸设计，一次通过率达85%，参与设计优化反馈。</li>
          <li>主导50余套CAD图绘制，多个项目获得年度优秀奖。</li>
        </ul>
      </div>
      <hr class="divider" />
    </div>

    <div class="section">
      <div class="section-title"><span>教育经历</span></div>
      <div class="item education-list">
        <div class="item-header">
          <div class="item-title">武汉大学 - 土木工程（本科）</div>
          <div class="item-date">2019.12 ~ 至今</div>
        </div>
        <ul>
          <li>工程设计</li>
          <li>施工安全</li>
        </ul>
      </div>
      <hr class="divider" />
    </div>

    <div class="section">
      <div class="section-title"><span>项目经历</span></div>
      <div class="item project-list">
        <div class="item-header">
          <div class="item-title">电商平台优化 - 设计师</div>
          <div class="item-date">2022.12 ~ 至今</div>
        </div>
        <ul>
          <li>提升用户体验与转化率，简化流程，优化界面交互。</li>
          <li>使用动态图层，整体转化率提升15%。</li>
        </ul>
      </div>

      <div class="item project-list">
        <div class="item-header">
          <div class="item-title">美妆品牌视觉升级 - 设计师</div>
          <div class="item-date">2022.12 ~ 2024.12</div>
        </div>
        <ul>
          <li>品牌视觉重构，提升市场竞争力，实现销量增长。</li>
        </ul>
      </div>
    </div>
  </div>
</body>
</html>
