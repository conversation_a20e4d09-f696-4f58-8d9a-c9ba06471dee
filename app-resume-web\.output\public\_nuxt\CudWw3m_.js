import{R as g}from"./Ciwj-CUv.js";import{_ as c}from"./DlAUqK2U.js";import{r as p,c as a,a as s,f as n,b as v,t as b,q as m,o}from"./CURHyiUL.js";const u={class:"test-page"},x={class:"container mx-auto p-8"},h={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},y={class:"space-y-6"},f={class:"bg-white rounded-lg shadow-lg p-6"},k={class:"bg-white rounded-lg shadow-lg p-6"},_={class:"bg-gray-100 p-4 rounded border font-mono text-sm"},w={class:"space-y-6"},C={class:"bg-white rounded-lg shadow-lg p-6"},T={class:"border border-gray-200 rounded p-4 min-h-[200px]"},V=["innerHTML"],M={key:1,class:"text-gray-500 italic"},H={class:"bg-white rounded-lg shadow-lg p-6"},L={class:"education-item bg-gray-50 rounded-lg p-4"},B=["innerHTML"],N={class:"mt-8 bg-white rounded-lg shadow-lg p-6"},$={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},E={__name:"rich-text-editor-test",setup(I){const e=p(""),r={bold:"<strong>这是粗体文本</strong>，还有<em>斜体文本</em>和<u>下划线文本</u>。",list:`<ul>
<li>主修课程：数据结构、算法设计、计算机网络</li>
<li>获得荣誉：国家奖学金、优秀学生干部</li>
<li>参与项目：校园信息管理系统开发</li>
</ul>`,link:'访问我的个人网站：<a href="https://example.com">https://example.com</a>',mixed:`<div style="text-align: center;"><strong>北京大学计算机科学与技术专业</strong></div>
<br>
<strong>主修课程：</strong>
<ul>
<li>数据结构与算法</li>
<li>计算机网络</li>
<li>操作系统</li>
</ul>
<br>
<strong>获得荣誉：</strong>
<ol>
<li>国家奖学金（2022年）</li>
<li>优秀学生干部（2021年）</li>
<li>ACM程序设计竞赛三等奖</li>
</ol>
<br>
<em>联系方式：</em> <a href="mailto:<EMAIL>"><EMAIL></a>`},l=i=>{e.value=r[i]};return(i,t)=>(o(),a("div",u,[s("div",x,[t[11]||(t[11]=s("h1",{class:"text-2xl font-bold mb-6"},"富文本编辑器测试",-1)),s("div",h,[s("div",y,[s("div",f,[t[5]||(t[5]=s("h2",{class:"text-lg font-semibold mb-4"},"富文本编辑器",-1)),v(g,{modelValue:e.value,"onUpdate:modelValue":t[0]||(t[0]=d=>e.value=d),placeholder:"请输入教育经历详情，支持粗体、斜体、列表等格式...","min-height":"200px"},null,8,["modelValue"])]),s("div",k,[t[6]||(t[6]=s("h2",{class:"text-lg font-semibold mb-4"},"原始HTML内容",-1)),s("div",_,[s("pre",null,b(e.value||"(空内容)"),1)])])]),s("div",w,[s("div",C,[t[7]||(t[7]=s("h2",{class:"text-lg font-semibold mb-4"},"预览效果",-1)),s("div",T,[e.value?(o(),a("div",{key:0,class:"rich-text-preview",innerHTML:e.value},null,8,V)):(o(),a("div",M," 暂无内容 "))])]),s("div",H,[t[9]||(t[9]=s("h2",{class:"text-lg font-semibold mb-4"},"教育经历模拟",-1)),s("div",L,[t[8]||(t[8]=n('<div class="education-header mb-3" data-v-c0169705><h3 class="text-lg font-bold text-gray-900" data-v-c0169705>北京大学</h3><div class="flex gap-2 text-gray-700" data-v-c0169705><span class="font-semibold" data-v-c0169705>本科</span><span class="text-gray-600" data-v-c0169705>计算机科学与技术</span></div><div class="text-sm text-gray-500 font-medium" data-v-c0169705>2020-09 — 2024-06</div></div>',1)),e.value?(o(),a("div",{key:0,class:"education-description",innerHTML:e.value},null,8,B)):m("",!0)])])])]),t[12]||(t[12]=n('<div class="mt-8 bg-white rounded-lg shadow-lg p-6" data-v-c0169705><h2 class="text-lg font-semibold mb-4" data-v-c0169705>键盘快捷键</h2><div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm" data-v-c0169705><div class="flex items-center gap-2" data-v-c0169705><kbd class="px-2 py-1 bg-gray-100 rounded text-xs" data-v-c0169705>Ctrl+B</kbd><span data-v-c0169705>粗体</span></div><div class="flex items-center gap-2" data-v-c0169705><kbd class="px-2 py-1 bg-gray-100 rounded text-xs" data-v-c0169705>Ctrl+I</kbd><span data-v-c0169705>斜体</span></div><div class="flex items-center gap-2" data-v-c0169705><kbd class="px-2 py-1 bg-gray-100 rounded text-xs" data-v-c0169705>Ctrl+U</kbd><span data-v-c0169705>下划线</span></div><div class="flex items-center gap-2" data-v-c0169705><kbd class="px-2 py-1 bg-gray-100 rounded text-xs" data-v-c0169705>Ctrl+K</kbd><span data-v-c0169705>超链接</span></div></div></div>',1)),s("div",N,[t[10]||(t[10]=s("h2",{class:"text-lg font-semibold mb-4"},"快速测试内容",-1)),s("div",$,[s("button",{onClick:t[1]||(t[1]=d=>l("bold")),class:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"}," 粗体测试 "),s("button",{onClick:t[2]||(t[2]=d=>l("list")),class:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"}," 列表测试 "),s("button",{onClick:t[3]||(t[3]=d=>l("link")),class:"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"}," 链接测试 "),s("button",{onClick:t[4]||(t[4]=d=>l("mixed")),class:"px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"}," 混合测试 ")])])])]))}},q=c(E,[["__scopeId","data-v-c0169705"]]);export{q as default};
