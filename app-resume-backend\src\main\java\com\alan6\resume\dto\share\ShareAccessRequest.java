package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分享访问请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareAccessRequest", description = "分享访问请求")
public class ShareAccessRequest {

    /**
     * 访问密码（如果设置了密码则必填）
     */
    @Schema(description = "访问密码", example = "123456")
    private String password;

    /**
     * 客户端信息
     */
    @Schema(description = "客户端信息")
    private ClientInfo clientInfo;

    /**
     * 客户端信息内部类
     */
    @Data
    @Schema(name = "ClientInfo", description = "客户端信息")
    public static class ClientInfo {

        /**
         * 用户代理
         */
        @Schema(description = "用户代理", example = "Mozilla/5.0...")
        private String userAgent;

        /**
         * 客户端IP
         */
        @Schema(description = "客户端IP", example = "*************")
        private String ip;

        /**
         * 来源页面
         */
        @Schema(description = "来源页面", example = "https://example.com")
        private String referer;
    }
} 