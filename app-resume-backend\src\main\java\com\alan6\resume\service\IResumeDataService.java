package com.alan6.resume.service;

import com.alan6.resume.document.ResumeData;

/**
 * 简历数据服务接口（MongoDB）
 * 
 * @description 定义简历数据在MongoDB中的操作方法
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IResumeDataService {

    /**
     * 保存简历数据
     * 
     * @param resumeData 简历数据
     * @return MongoDB文档ID
     */
    String saveResumeData(ResumeData resumeData);

    /**
     * 获取简历数据
     * 
     * @param documentId MongoDB文档ID
     * @return 简历数据
     */
    ResumeData getResumeData(String documentId);

    /**
     * 根据简历ID获取数据
     * 
     * @param resumeId 简历ID
     * @return 简历数据
     */
    ResumeData getResumeDataByResumeId(String resumeId);

    /**
     * 更新简历数据
     * 
     * @param documentId MongoDB文档ID
     * @param resumeData 简历数据
     * @return 是否更新成功
     */
    boolean updateResumeData(String documentId, ResumeData resumeData);

    /**
     * 删除简历数据
     * 
     * @param documentId MongoDB文档ID
     * @return 是否删除成功
     */
    boolean deleteResumeData(String documentId);

    /**
     * 增量更新简历数据
     * 
     * @param documentId MongoDB文档ID
     * @param modulePath 模块路径
     * @param moduleData 模块数据
     * @return 是否更新成功
     */
    boolean updateModuleData(String documentId, String modulePath, Object moduleData);

    /**
     * 更新全局设置
     * 
     * @param documentId MongoDB文档ID
     * @param globalSettings 全局设置
     * @return 是否更新成功
     */
    boolean updateGlobalSettings(String documentId, ResumeData.GlobalSettings globalSettings);
} 