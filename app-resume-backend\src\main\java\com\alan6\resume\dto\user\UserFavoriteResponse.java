package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户收藏响应DTO
 * 
 * 用于返回用户收藏列表，包含收藏的基本信息和目标对象的详细信息。
 * 支持模板收藏和简历收藏两种类型。
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(name = "UserFavoriteResponse", description = "用户收藏响应")
public class UserFavoriteResponse {

    /**
     * 收藏ID
     */
    @Schema(description = "收藏ID", example = "1001")
    private Long id;

    /**
     * 收藏类型
     * 1-模板收藏，2-简历收藏
     */
    @Schema(description = "收藏类型（1:模板, 2:简历）", example = "1")
    private Byte targetType;

    /**
     * 目标ID（模板ID或简历ID）
     */
    @Schema(description = "目标ID（模板ID或简历ID）", example = "3001")
    private Long targetId;

    /**
     * 目标名称（模板名称或简历名称）
     */
    @Schema(description = "目标名称", example = "简约商务模板")
    private String targetName;

    /**
     * 预览图片URL
     * 模板收藏时为模板预览图，简历收藏时为简历缩略图
     */
    @Schema(description = "预览图片URL", example = "https://cdn.example.com/preview/3001.jpg")
    private String previewImage;

    /**
     * 目标描述信息
     * 模板收藏时为模板描述，简历收藏时为简历描述
     */
    @Schema(description = "目标描述信息", example = "适合商务人士的简约风格模板")
    private String description;

    /**
     * 是否为付费内容
     * 仅对模板收藏有效
     */
    @Schema(description = "是否为付费内容", example = "false")
    private Boolean isPremium;

    /**
     * 收藏时间
     */
    @Schema(description = "收藏时间", example = "2024-01-01 10:00:00")
    private LocalDateTime createTime;

    /**
     * 构造方法 - 创建模板收藏响应
     * 
     * @param id 收藏ID
     * @param targetId 模板ID
     * @param targetName 模板名称
     * @param previewImage 预览图片
     * @param description 模板描述
     * @param isPremium 是否付费
     * @param createTime 收藏时间
     * @return 模板收藏响应对象
     */
    public static UserFavoriteResponse createTemplateResponse(Long id, Long targetId, 
            String targetName, String previewImage, String description, 
            Boolean isPremium, LocalDateTime createTime) {
        UserFavoriteResponse response = new UserFavoriteResponse();
        response.setId(id);
        response.setTargetType((byte) 1);
        response.setTargetId(targetId);
        response.setTargetName(targetName);
        response.setPreviewImage(previewImage);
        response.setDescription(description);
        response.setIsPremium(isPremium);
        response.setCreateTime(createTime);
        return response;
    }

    /**
     * 构造方法 - 创建简历收藏响应
     * 
     * @param id 收藏ID
     * @param targetId 简历ID
     * @param targetName 简历名称
     * @param previewImage 预览图片
     * @param description 简历描述
     * @param createTime 收藏时间
     * @return 简历收藏响应对象
     */
    public static UserFavoriteResponse createResumeResponse(Long id, Long targetId, 
            String targetName, String previewImage, String description, 
            LocalDateTime createTime) {
        UserFavoriteResponse response = new UserFavoriteResponse();
        response.setId(id);
        response.setTargetType((byte) 2);
        response.setTargetId(targetId);
        response.setTargetName(targetName);
        response.setPreviewImage(previewImage);
        response.setDescription(description);
        response.setIsPremium(false); // 简历收藏不涉及付费
        response.setCreateTime(createTime);
        return response;
    }
} 