package com.alan6.resume.service.impl;

import com.alan6.resume.dto.template.*;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.entity.TemplateCategories;
import com.alan6.resume.entity.Resumes;
import com.alan6.resume.mapper.ResumeTemplatesMapper;
import com.alan6.resume.mapper.TemplateCategoriesMapper;
import com.alan6.resume.mapper.ResumesMapper;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.IUserFavoritesService;
import com.alan6.resume.common.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 简历模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeTemplatesServiceImpl extends ServiceImpl<ResumeTemplatesMapper, ResumeTemplates> implements IResumeTemplatesService {

    private final TemplateCategoriesMapper templateCategoriesMapper;
    private final ResumesMapper resumesMapper;
    private final IUserFavoritesService userFavoritesService;

    /**
     * 获取模板列表（带筛选和分页）
     *
     * @param request 列表请求参数
     * @param userId  当前用户ID（用于判断收藏状态）
     * @return 模板列表响应
     */
    @Override
    public TemplateListResponse getTemplateList(TemplateListRequest request, Long userId) {
        log.info("开始获取模板列表，用户ID: {}, 请求参数: {}", userId, request);
        
        try {
            // 参数校验和处理
            validateListRequest(request);
            
            // 构建查询条件
            LambdaQueryWrapper<ResumeTemplates> wrapper = buildListQueryWrapper(request);
            
            // 分页查询
            Page<ResumeTemplates> page = new Page<>(request.getCurrent(), request.getSize());
            IPage<ResumeTemplates> pageResult = page(page, wrapper);
            
            log.info("查询到 {} 个模板，总数: {}", pageResult.getRecords().size(), pageResult.getTotal());
            
            // 转换为响应DTO
            List<TemplateResponse> templateResponses = convertToTemplateResponses(pageResult.getRecords(), userId);
            
            // 构建响应对象
            TemplateListResponse response = new TemplateListResponse();
            response.setRecords(templateResponses);
            response.setTotal(pageResult.getTotal());
            response.setCurrent((int) pageResult.getCurrent());
            response.setSize((int) pageResult.getSize());
            response.setPages((int) pageResult.getPages());
            response.setFilters(buildCurrentFilters(request));
            response.setAvailableFilters(buildAvailableFilters());
            
            log.info("成功获取模板列表，返回 {} 个模板", templateResponses.size());
            return response;
            
        } catch (Exception e) {
            log.error("获取模板列表失败，用户ID: {}", userId, e);
            throw new BusinessException("获取模板列表失败: " + e.getMessage());
        }
    }

    /**
     * 管理员获取模板列表（包含所有状态的模板）
     *
     * @param request 列表请求参数
     * @param adminUserId 管理员用户ID
     * @return 模板列表响应
     */
    @Override
    public TemplateListResponse getAdminTemplateList(TemplateListRequest request, Long adminUserId) {
        log.info("管理员开始获取模板列表，管理员ID: {}, 请求参数: {}", adminUserId, request);
        
        try {
            // 参数校验和处理
            validateListRequest(request);
            
            // 构建管理员查询条件（包含所有状态）
            LambdaQueryWrapper<ResumeTemplates> wrapper = buildAdminListQueryWrapper(request);
            
            // 分页查询
            Page<ResumeTemplates> page = new Page<>(request.getCurrent(), request.getSize());
            IPage<ResumeTemplates> pageResult = page(page, wrapper);
            
            log.info("管理员查询到 {} 个模板，总数: {}", pageResult.getRecords().size(), pageResult.getTotal());

            
            // 转换为响应DTO（管理员视图）
            List<TemplateResponse> templateResponses = convertToAdminTemplateResponses(pageResult.getRecords(), adminUserId);
            
            // 修复：确保分页信息正确
            long totalCount = pageResult.getTotal();
            int pageSize = (int) pageResult.getSize();
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            
            // 构建响应对象
            TemplateListResponse response = new TemplateListResponse();
            response.setRecords(templateResponses);
            response.setTotal(totalCount);
            response.setCurrent((int) pageResult.getCurrent());
            response.setSize(pageSize);
            response.setPages(totalPages);
            response.setFilters(buildCurrentFilters(request));
            response.setAvailableFilters(buildAdminAvailableFilters());
            
            log.info("管理员成功获取模板列表，返回 {} 个模板", templateResponses.size());

            return response;
            
        } catch (Exception e) {
            log.error("管理员获取模板列表失败，管理员ID: {}", adminUserId, e);
            throw new BusinessException("管理员获取模板列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板详情
     *
     * @param templateId 模板ID
     * @param userId     当前用户ID（用于判断收藏状态和使用权限）
     * @return 模板详情响应
     */
    @Override
    public TemplateDetailResponse getTemplateDetail(Long templateId, Long userId) {
        log.info("开始获取模板详情，模板ID: {}, 用户ID: {}", templateId, userId);
        
        try {
            // 查询模板信息
            ResumeTemplates template = getById(templateId);
            if (template == null || template.getIsDeleted() == 1) {
                throw new BusinessException("模板不存在或已删除");
            }
            
            if (template.getStatus() != 1) {
                throw new BusinessException("模板未上架，无法查看");
            }
            
            // 转换为详情响应DTO
            TemplateDetailResponse response = convertToTemplateDetailResponse(template, userId);
            
            log.info("成功获取模板详情，模板ID: {}", templateId);
            return response;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取模板详情失败，模板ID: {}, 用户ID: {}", templateId, userId, e);
            throw new BusinessException("获取模板详情失败: " + e.getMessage());
        }
    }

    /**
     * 使用模板创建简历
     *
     * @param templateId 模板ID
     * @param request    使用模板请求
     * @param userId     当前用户ID
     * @return 使用模板响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateUseResponse useTemplate(Long templateId, TemplateUseRequest request, Long userId) {
        log.info("开始使用模板创建简历，模板ID: {}, 用户ID: {}, 简历名称: {}", templateId, userId, request.getResumeName());
        
        try {
            // 检查模板是否存在和可用
            ResumeTemplates template = getById(templateId);
            if (template == null || template.getIsDeleted() == 1) {
                throw new BusinessException("模板不存在或已删除");
            }
            
            if (template.getStatus() != 1) {
                throw new BusinessException("模板未上架，无法使用");
            }
            
            // 检查用户权限
            if (!checkUserPermission(templateId, userId)) {
                throw new BusinessException("您没有使用此模板的权限");
            }
            
            // 创建简历记录
            Resumes resume = new Resumes();
            resume.setUserId(userId);
            resume.setName(request.getResumeName());
            resume.setTemplateId(templateId);
            resume.setLanguage(request.getLanguage());
            resume.setStatus((byte) 0);  // 草稿状态
            resume.setIsDeleted((byte) 0);
            resume.setCreateTime(LocalDateTime.now());
            resume.setUpdateTime(LocalDateTime.now());
            
            resumesMapper.insert(resume);
            
            // 增加模板使用次数
            incrementUseCount(templateId);
            
            // 构建响应
            TemplateUseResponse response = new TemplateUseResponse();
            response.setResumeId(resume.getId());
            response.setResumeName(resume.getName());
            response.setTemplateId(templateId);
            response.setRedirectUrl("/resume/edit/" + resume.getId());
            
            log.info("成功使用模板创建简历，简历ID: {}", resume.getId());
            return response;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("使用模板创建简历失败，模板ID: {}, 用户ID: {}", templateId, userId, e);
            throw new BusinessException("使用模板创建简历失败: " + e.getMessage());
        }
    }

    /**
     * 搜索模板
     *
     * @param request 搜索请求参数
     * @param userId  当前用户ID
     * @return 搜索结果响应
     */
    @Override
    public TemplateListResponse searchTemplates(TemplateSearchRequest request, Long userId) {
        log.info("开始搜索模板，用户ID: {}, 搜索关键词: {}", userId, request.getKeyword());
        
        try {
            // 转换为列表请求参数
            TemplateListRequest listRequest = new TemplateListRequest();
            BeanUtils.copyProperties(request, listRequest);
            
            // 调用列表查询方法
            TemplateListResponse response = getTemplateList(listRequest, userId);
            
            // 添加搜索相关信息
            Map<String, Object> searchInfo = new HashMap<>();
            searchInfo.put("searchKeyword", request.getKeyword());
            searchInfo.put("searchTime", 0.15);  // 模拟搜索耗时
            
            // 将搜索信息合并到响应中
            if (response.getFilters() == null) {
                response.setFilters(new HashMap<>());
            }
            response.getFilters().putAll(searchInfo);
            
            log.info("搜索模板完成，关键词: {}, 结果数量: {}", request.getKeyword(), response.getRecords().size());
            return response;
            
        } catch (Exception e) {
            log.error("搜索模板失败，用户ID: {}, 关键词: {}", userId, request.getKeyword(), e);
            throw new BusinessException("搜索模板失败: " + e.getMessage());
        }
    }

    /**
     * 推荐模板
     *
     * @param request 推荐请求参数
     * @param userId  当前用户ID
     * @return 推荐模板列表
     */
    @Override
    public List<TemplateResponse> recommendTemplates(TemplateRecommendRequest request, Long userId) {
        log.info("开始推荐模板，用户ID: {}, 推荐数量: {}", userId, request.getLimit());
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<ResumeTemplates> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResumeTemplates::getStatus, 1)  // 上架状态
                   .eq(ResumeTemplates::getIsDeleted, 0);  // 未删除
            
            // 基于分类推荐
            if (request.getCategoryId() != null) {
                wrapper.eq(ResumeTemplates::getCategoryId, request.getCategoryId());
            }
            
            // 基于行业推荐
            if (StringUtils.hasText(request.getIndustry())) {
                wrapper.eq(ResumeTemplates::getIndustry, request.getIndustry());
            }
            
            // 排除指定模板
            if (StringUtils.hasText(request.getExcludeIds())) {
                String[] excludeIdArray = request.getExcludeIds().split(",");
                List<Long> excludeIds = Arrays.stream(excludeIdArray)
                        .map(String::trim)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                wrapper.notIn(ResumeTemplates::getId, excludeIds);
            }
            
            // 按使用次数和评分排序
            wrapper.orderByDesc(ResumeTemplates::getUseCount)
                   .orderByDesc(ResumeTemplates::getId);
            
            // 限制数量
            int limit = Math.min(request.getLimit(), 20);  // 最大20个
            List<ResumeTemplates> templates = list(wrapper).stream()
                    .limit(limit)
                    .collect(Collectors.toList());
            
            // 转换为响应DTO
            List<TemplateResponse> responses = convertToTemplateResponses(templates, userId);
            
            log.info("推荐模板完成，用户ID: {}, 推荐数量: {}", userId, responses.size());
            return responses;
            
        } catch (Exception e) {
            log.error("推荐模板失败，用户ID: {}", userId, e);
            throw new BusinessException("推荐模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门模板
     *
     * @param request 热门模板请求参数
     * @param userId  当前用户ID
     * @return 热门模板列表
     */
    @Override
    public List<TemplateResponse> getHotTemplates(TemplateHotRequest request, Long userId) {
        log.info("开始获取热门模板，用户ID: {}, 数量: {}, 时间范围: {}", userId, request.getLimit(), request.getTimeRange());
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<ResumeTemplates> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResumeTemplates::getStatus, 1)  // 上架状态
                   .eq(ResumeTemplates::getIsDeleted, 0);  // 未删除
            
            // 分类筛选
            if (request.getCategoryId() != null) {
                wrapper.eq(ResumeTemplates::getCategoryId, request.getCategoryId());
            }
            
            // 热门模板通常按使用次数排序
            wrapper.orderByDesc(ResumeTemplates::getUseCount)
                   .orderByDesc(ResumeTemplates::getId);
            
            // 限制数量
            int limit = Math.min(request.getLimit(), 50);  // 最大50个
            List<ResumeTemplates> templates = list(wrapper).stream()
                    .limit(limit)
                    .collect(Collectors.toList());
            
            // 转换为响应DTO
            List<TemplateResponse> responses = convertToTemplateResponses(templates, userId);
            
            log.info("获取热门模板完成，用户ID: {}, 返回数量: {}", userId, responses.size());
            return responses;
            
        } catch (Exception e) {
            log.error("获取热门模板失败，用户ID: {}", userId, e);
            throw new BusinessException("获取热门模板失败: " + e.getMessage());
        }
    }

    /**
     * 增加模板使用次数
     *
     * @param templateId 模板ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementUseCount(Long templateId) {
        log.debug("开始增加模板使用次数，模板ID: {}", templateId);
        
        try {
            ResumeTemplates template = getById(templateId);
            if (template != null) {
                template.setUseCount(template.getUseCount() + 1);
                template.setUpdateTime(LocalDateTime.now());
                updateById(template);
                log.debug("成功增加模板使用次数，模板ID: {}, 当前使用次数: {}", templateId, template.getUseCount());
            }
        } catch (Exception e) {
            log.error("增加模板使用次数失败，模板ID: {}", templateId, e);
            // 这里不抛出异常，避免影响主流程
        }
    }

    /**
     * 检查用户是否有使用模板的权限
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @return 是否有权限
     */
    @Override
    public boolean checkUserPermission(Long templateId, Long userId) {
        log.debug("开始检查用户模板使用权限，模板ID: {}, 用户ID: {}", templateId, userId);
        
        try {
            ResumeTemplates template = getById(templateId);
            if (template == null) {
                return false;
            }
            
            // 免费模板所有用户都可以使用
            if (template.getIsPremium() == 0) {
                return true;
            }
            
            // 付费模板需要检查用户会员状态或购买记录
            // 这里简化处理，实际应该检查用户会员状态
            // TODO: 实现会员权限检查逻辑
            return true;  // 暂时返回true
            
        } catch (Exception e) {
            log.error("检查用户模板使用权限失败，模板ID: {}, 用户ID: {}", templateId, userId, e);
            return false;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 校验列表请求参数
     */
    private void validateListRequest(TemplateListRequest request) {
        if (request.getCurrent() == null || request.getCurrent() < 1) {
            request.setCurrent(1);
        }
        if (request.getSize() == null || request.getSize() < 1) {
            request.setSize(10);
        }
        if (request.getSize() > 50) {
            request.setSize(50);  // 限制最大页面大小
        }
    }

    /**
     * 构建列表查询条件
     */
    private LambdaQueryWrapper<ResumeTemplates> buildListQueryWrapper(TemplateListRequest request) {
        LambdaQueryWrapper<ResumeTemplates> wrapper = new LambdaQueryWrapper<>();
        
        // 基础条件：上架且未删除
        wrapper.eq(ResumeTemplates::getStatus, 1)
               .eq(ResumeTemplates::getIsDeleted, 0);
        
        // 分类筛选
        if (request.getCategoryId() != null) {
            wrapper.eq(ResumeTemplates::getCategoryId, request.getCategoryId());
        }
        
        // 行业筛选
        if (StringUtils.hasText(request.getIndustry())) {
            wrapper.eq(ResumeTemplates::getIndustry, request.getIndustry());
        }
        
        // 风格筛选
        if (StringUtils.hasText(request.getStyle())) {
            wrapper.eq(ResumeTemplates::getStyle, request.getStyle());
        }
        
        // 配色方案筛选
        if (StringUtils.hasText(request.getColorScheme())) {
            wrapper.eq(ResumeTemplates::getColorScheme, request.getColorScheme());
        }
        
        // 付费筛选
        if (request.getIsPremium() != null && request.getIsPremium() != 2) {
            wrapper.eq(ResumeTemplates::getIsPremium, request.getIsPremium());
        }
        
        // 关键词搜索
        if (StringUtils.hasText(request.getKeyword())) {
            wrapper.and(w -> w.like(ResumeTemplates::getName, request.getKeyword())
                             .or()
                             .like(ResumeTemplates::getDescription, request.getKeyword()));
        }
        
        // 价格区间筛选
        if (StringUtils.hasText(request.getPriceRange())) {
            String[] priceArray = request.getPriceRange().split("-");
            if (priceArray.length == 2) {
                BigDecimal minPrice = new BigDecimal(priceArray[0]);
                BigDecimal maxPrice = new BigDecimal(priceArray[1]);
                wrapper.between(ResumeTemplates::getPrice, minPrice, maxPrice);
            }
        }
        
        // 排序
        applySorting(wrapper, request.getSortBy());
        
        return wrapper;
    }

    /**
     * 构建管理员列表查询条件
     */
    private LambdaQueryWrapper<ResumeTemplates> buildAdminListQueryWrapper(TemplateListRequest request) {
        LambdaQueryWrapper<ResumeTemplates> wrapper = new LambdaQueryWrapper<>();
        
        // 管理员可以看到所有模板（包括已删除的）
        // 根据请求参数决定是否筛选删除状态
        if (request.getIsDeleted() != null) {
            wrapper.eq(ResumeTemplates::getIsDeleted, request.getIsDeleted());
        }
        
        // 分类筛选 - 修复空值问题
        if (request.getCategoryId() != null && request.getCategoryId() > 0) {
            wrapper.eq(ResumeTemplates::getCategoryId, request.getCategoryId());
        }
        
        // 行业筛选
        if (StringUtils.hasText(request.getIndustry())) {
            wrapper.eq(ResumeTemplates::getIndustry, request.getIndustry());
        }
        
        // 风格筛选
        if (StringUtils.hasText(request.getStyle())) {
            wrapper.eq(ResumeTemplates::getStyle, request.getStyle());
        }
        
        // 配色方案筛选
        if (StringUtils.hasText(request.getColorScheme())) {
            wrapper.eq(ResumeTemplates::getColorScheme, request.getColorScheme());
        }
        
        // 付费筛选 - 修复空字符串问题
        if (request.getIsPremium() != null && request.getIsPremium() >= 0 && request.getIsPremium() != 2) {
            wrapper.eq(ResumeTemplates::getIsPremium, request.getIsPremium());
        }
        
        // 关键词搜索
        if (StringUtils.hasText(request.getKeyword())) {
            wrapper.and(w -> w.like(ResumeTemplates::getName, request.getKeyword())
                             .or()
                             .like(ResumeTemplates::getDescription, request.getKeyword()));
        }
        
        // 价格区间筛选
        if (StringUtils.hasText(request.getPriceRange())) {
            String[] priceArray = request.getPriceRange().split("-");
            if (priceArray.length == 2) {
                BigDecimal minPrice = new BigDecimal(priceArray[0]);
                BigDecimal maxPrice = new BigDecimal(priceArray[1]);
                wrapper.between(ResumeTemplates::getPrice, minPrice, maxPrice);
            }
        }
        
        // 排序
        applySorting(wrapper, request.getSortBy());
        
        return wrapper;
    }

    /**
     * 应用排序条件
     */
    private void applySorting(LambdaQueryWrapper<ResumeTemplates> wrapper, String sortBy) {
        switch (sortBy) {
            case "popular":
                wrapper.orderByDesc(ResumeTemplates::getUseCount);
                break;
            case "latest":
                wrapper.orderByDesc(ResumeTemplates::getCreateTime);
                break;
            case "price_asc":
                wrapper.orderByAsc(ResumeTemplates::getPrice);
                break;
            case "price_desc":
                wrapper.orderByDesc(ResumeTemplates::getPrice);
                break;
            default:
                wrapper.orderByAsc(ResumeTemplates::getSortOrder)
                       .orderByDesc(ResumeTemplates::getId);
                break;
        }
    }

    /**
     * 转换为模板响应DTO列表
     */
    private List<TemplateResponse> convertToTemplateResponses(List<ResumeTemplates> templates, Long userId) {
        return templates.stream()
                .map(template -> convertToTemplateResponse(template, userId))
                .collect(Collectors.toList());
    }

    /**
     * 转换为管理员模板响应DTO列表
     */
    private List<TemplateResponse> convertToAdminTemplateResponses(List<ResumeTemplates> templates, Long adminUserId) {
        return templates.stream()
                .map(template -> convertToTemplateResponse(template, adminUserId)) // 管理员视图也使用普通模板转换逻辑
                .collect(Collectors.toList());
    }

    /**
     * 转换为模板响应DTO
     */
    private TemplateResponse convertToTemplateResponse(ResumeTemplates template, Long userId) {
        TemplateResponse response = new TemplateResponse();
        BeanUtils.copyProperties(template, response);
        
        // 获取分类名称
        String categoryName = getCategoryName(template.getCategoryId());
        response.setCategoryName(categoryName);
        
        // 模拟评分数据
        response.setRating(4.5 + Math.random() * 0.5);  // 4.5-5.0之间的随机评分
        response.setRatingCount((int) (Math.random() * 200) + 50);  // 50-250之间的评分数量
        
        // 检查收藏状态
        response.setIsFavorited(checkIsFavorited(template.getId(), userId));
        
        // 判断是否热门（使用次数大于100的认为是热门）
        response.setIsHot(template.getUseCount() > 100);
        
        // 模拟标签数据
        response.setTags(generateTags(template));
        
        return response;
    }

    /**
     * 转换为模板详情响应DTO
     */
    private TemplateDetailResponse convertToTemplateDetailResponse(ResumeTemplates template, Long userId) {
        TemplateDetailResponse response = new TemplateDetailResponse();
        BeanUtils.copyProperties(template, response);
        
        // 获取分类名称
        String categoryName = getCategoryName(template.getCategoryId());
        response.setCategoryName(categoryName);
        
        // 设置多尺寸预览图
        Map<String, String> previewImages = new HashMap<>();
        previewImages.put("thumbnail", template.getPreviewImageUrl().replace(".jpg", "_thumb.jpg"));
        previewImages.put("medium", template.getPreviewImageUrl());
        previewImages.put("large", template.getPreviewImageUrl().replace(".jpg", "_large.jpg"));
        response.setPreviewImages(previewImages);
        
        // 解析模板配置数据
        response.setTemplateData(parseTemplateData(template.getConfigData()));
        
        // 设置原价（暂时与价格相同）
        response.setOriginalPrice(template.getPrice());
        
        // 模拟评分数据
        response.setRating(4.5 + Math.random() * 0.5);
        response.setRatingCount((int) (Math.random() * 200) + 50);
        
        // 检查收藏状态
        response.setIsFavorited(checkIsFavorited(template.getId(), userId));
        
        // 判断是否热门
        response.setIsHot(template.getUseCount() > 100);
        
        // 模拟标签、功能特性、适用人群
        response.setTags(generateTags(template));
        response.setFeatures(generateFeatures(template));
        response.setSuitableFor(generateSuitableFor(template.getIndustry()));
        
        // 获取相关模板
        response.setRelatedTemplates(getRelatedTemplates(template, userId));
        
        // 模拟评价信息
        response.setReviews(generateReviews());
        
        // 检查使用权限
        response.setCanUse(checkUserPermission(template.getId(), userId));
        response.setUsageRequirement(generateUsageRequirement(template));
        
        return response;
    }

    /**
     * 获取分类名称
     */
    private String getCategoryName(Long categoryId) {
        if (categoryId == null) {
            return null;
        }
        TemplateCategories category = templateCategoriesMapper.selectById(categoryId);
        return category != null ? category.getName() : null;
    }

    /**
     * 检查是否收藏
     */
    private Boolean checkIsFavorited(Long templateId, Long userId) {
        if (userId == null) {
            return false;
        }
        // 这里应该调用收藏服务检查，暂时返回false
        return false;
    }

    /**
     * 生成标签
     */
    private List<String> generateTags(ResumeTemplates template) {
        List<String> tags = new ArrayList<>();
        
        // 从tags字段解析标签
        if (StringUtils.hasText(template.getTags())) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<String> tagList = objectMapper.readValue(template.getTags(), List.class);
                tags.addAll(tagList);
            } catch (Exception e) {
                log.warn("解析模板标签失败: {}", e.getMessage());
                // 如果解析失败，尝试按逗号分割
                String[] tagArray = template.getTags().split(",");
                for (String tag : tagArray) {
                    if (StringUtils.hasText(tag.trim())) {
                        tags.add(tag.trim());
                    }
                }
            }
        }
        
        // 添加其他字段作为标签
        if (StringUtils.hasText(template.getStyle())) {
            tags.add(template.getStyle());
        }
        if (StringUtils.hasText(template.getIndustry())) {
            tags.add(template.getIndustry());
        }
        if (StringUtils.hasText(template.getColorScheme())) {
            tags.add(template.getColorScheme());
        }
        
        return tags;
    }

    /**
     * 解析模板配置数据
     */
    private Object parseTemplateData(String configData) {
        if (!StringUtils.hasText(configData)) {
            return new HashMap<String, Object>();
        }
        
        try {
            // 解析JSON配置数据
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(configData, Map.class);
        } catch (Exception e) {
            log.warn("解析模板配置数据失败: {}", e.getMessage());
            return new HashMap<String, Object>();
        }
    }

    /**
     * 生成功能特性
     */
    private List<String> generateFeatures(ResumeTemplates template) {
        List<String> features = new ArrayList<>();
        
        // 从features字段解析功能特性
        if (StringUtils.hasText(template.getFeatures())) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<String> featureList = objectMapper.readValue(template.getFeatures(), List.class);
                features.addAll(featureList);
            } catch (Exception e) {
                log.warn("解析模板功能特性失败: {}", e.getMessage());
                // 如果解析失败，尝试按逗号分割
                String[] featureArray = template.getFeatures().split(",");
                for (String feature : featureArray) {
                    if (StringUtils.hasText(feature.trim())) {
                        features.add(feature.trim());
                    }
                }
            }
        }
        
        // 如果没有配置功能特性，返回默认值
        if (features.isEmpty()) {
            features.addAll(Arrays.asList("支持多语言", "响应式设计", "PDF导出", "在线预览", "一键填充"));
        }
        
        return features;
    }

    /**
     * 生成适用人群
     */
    private List<String> generateSuitableFor(String industry) {
        if ("商务".equals(industry)) {
            return Arrays.asList("商务人士", "金融从业者", "咨询顾问", "销售经理", "项目经理");
        } else if ("设计".equals(industry)) {
            return Arrays.asList("UI设计师", "平面设计师", "产品设计师", "创意总监", "视觉设计师");
        } else {
            return Arrays.asList("应届毕业生", "职场新人", "资深专家", "管理人员", "技术人员");
        }
    }

    /**
     * 获取相关模板
     */
    private List<TemplateResponse> getRelatedTemplates(ResumeTemplates template, Long userId) {
        LambdaQueryWrapper<ResumeTemplates> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResumeTemplates::getStatus, 1)
               .eq(ResumeTemplates::getIsDeleted, 0)
               .eq(ResumeTemplates::getCategoryId, template.getCategoryId())
               .ne(ResumeTemplates::getId, template.getId())
               .orderByDesc(ResumeTemplates::getUseCount)
               .last("LIMIT 3");
        
        List<ResumeTemplates> relatedTemplates = list(wrapper);
        return convertToTemplateResponses(relatedTemplates, userId);
    }

    /**
     * 生成评价信息
     */
    private Object generateReviews() {
        Map<String, Object> reviews = new HashMap<>();
        reviews.put("averageRating", 4.8);
        reviews.put("totalCount", 156);
        
        Map<String, Integer> ratingDistribution = new HashMap<>();
        ratingDistribution.put("5", 98);
        ratingDistribution.put("4", 42);
        ratingDistribution.put("3", 12);
        ratingDistribution.put("2", 3);
        ratingDistribution.put("1", 1);
        reviews.put("ratingDistribution", ratingDistribution);
        
        List<Map<String, Object>> latestReviews = new ArrayList<>();
        Map<String, Object> review = new HashMap<>();
        review.put("userId", 1001);
        review.put("username", "张***");
        review.put("rating", 5);
        review.put("comment", "模板很棒，布局清晰，使用简单");
        review.put("createTime", "2024-11-20 14:30:00");
        latestReviews.add(review);
        reviews.put("latestReviews", latestReviews);
        
        return reviews;
    }

    /**
     * 生成使用要求
     */
    private Object generateUsageRequirement(ResumeTemplates template) {
        Map<String, Object> requirement = new HashMap<>();
        requirement.put("membershipRequired", template.getIsPremium() == 1);
        requirement.put("paymentRequired", template.getIsPremium() == 1);
        requirement.put("message", template.getIsPremium() == 1 ? "需要会员权限" : "免费使用");
        return requirement;
    }

    /**
     * 构建当前筛选条件
     */
    private Map<String, Object> buildCurrentFilters(TemplateListRequest request) {
        Map<String, Object> filters = new HashMap<>();
        filters.put("categoryId", request.getCategoryId());
        filters.put("industry", request.getIndustry());
        filters.put("style", request.getStyle());
        filters.put("isPremium", request.getIsPremium());
        filters.put("priceRange", request.getPriceRange());
        filters.put("ratingMin", request.getRatingMin());
        filters.put("isHot", request.getIsHot());
        filters.put("tags", StringUtils.hasText(request.getTags()) ? 
                Arrays.asList(request.getTags().split(",")) : Collections.emptyList());
        return filters;
    }

    /**
     * 构建可用筛选选项
     */
    private Map<String, Object> buildAvailableFilters() {
        Map<String, Object> availableFilters = new HashMap<>();
        
        // 行业选项
        availableFilters.put("industries", Arrays.asList("商务", "设计", "技术", "教育", "医疗"));
        
        // 风格选项
        availableFilters.put("styles", Arrays.asList("简约", "创意", "经典", "现代", "艺术"));
        
        // 配色方案选项
        availableFilters.put("colorSchemes", Arrays.asList("蓝色", "红色", "绿色", "黑白", "多彩"));
        
        // 价格区间选项
        List<Map<String, String>> priceRanges = new ArrayList<>();
        priceRanges.add(Map.of("label", "免费", "value", "0-0"));
        priceRanges.add(Map.of("label", "1-10元", "value", "1-10"));
        priceRanges.add(Map.of("label", "11-50元", "value", "11-50"));
        priceRanges.add(Map.of("label", "50元以上", "value", "50-999"));
        availableFilters.put("priceRanges", priceRanges);
        
        // 标签选项
        availableFilters.put("tags", Arrays.asList("简约", "商务", "创意", "现代", "经典", "艺术", "蓝色", "红色"));
        
        return availableFilters;
    }

    /**
     * 构建管理员可用筛选选项
     */
    private Map<String, Object> buildAdminAvailableFilters() {
        Map<String, Object> availableFilters = new HashMap<>();
        
        // 行业选项
        availableFilters.put("industries", Arrays.asList("商务", "设计", "技术", "教育", "医疗", "其他"));
        
        // 风格选项
        availableFilters.put("styles", Arrays.asList("简约", "创意", "经典", "现代", "艺术", "复古"));
        
        // 配色方案选项
        availableFilters.put("colorSchemes", Arrays.asList("蓝色", "红色", "绿色", "黑白", "多彩", "单色"));
        
        // 价格区间选项
        List<Map<String, String>> priceRanges = new ArrayList<>();
        priceRanges.add(Map.of("label", "全部", "value", ""));
        priceRanges.add(Map.of("label", "免费", "value", "0-0"));
        priceRanges.add(Map.of("label", "1-10元", "value", "1-10"));
        priceRanges.add(Map.of("label", "11-50元", "value", "11-50"));
        priceRanges.add(Map.of("label", "50元以上", "value", "50-999"));
        availableFilters.put("priceRanges", priceRanges);
        
        // 标签选项
        availableFilters.put("tags", Arrays.asList("简约", "商务", "创意", "现代", "经典", "艺术", "蓝色", "红色", "绿色", "黑白", "多彩", "单色", "复古"));
        
        return availableFilters;
    }

    /**
     * 模板预览
     *
     * @param templateId 模板ID
     * @param request    预览请求参数
     * @param userId     当前用户ID（可选，用于权限判断）
     * @return 预览响应
     */
    @Override
    public TemplatePreviewResponse previewTemplate(Long templateId, TemplatePreviewRequest request, Long userId) {
        log.info("开始生成模板预览，模板ID: {}, 用户ID: {}, 预览格式: {}", templateId, userId, request.getFormat());
        
        try {
            // 验证模板是否存在
            ResumeTemplates template = getById(templateId);
            if (template == null || template.getIsDeleted() == 1) {
                throw new BusinessException("模板不存在或已删除");
            }
            
            // 检查模板状态
            if (template.getStatus() == 0) {
                throw new BusinessException("模板已下架，无法预览");
            }
            
            // 设置默认参数
            TemplatePreviewRequest previewRequest = setDefaultPreviewParams(request);
            
            // 生成预览文件
            TemplatePreviewResponse response = generatePreviewFile(template, previewRequest, false);
            
            log.info("模板预览生成成功，模板ID: {}, 预览URL: {}", templateId, response.getPreviewUrl());
            return response;
            
        } catch (BusinessException e) {
            log.error("模板预览生成失败，模板ID: {}, 用户ID: {}, 错误: {}", templateId, userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("模板预览生成异常，模板ID: {}, 用户ID: {}", templateId, userId, e);
            throw new BusinessException("模板预览生成失败: " + e.getMessage());
        }
    }

    /**
     * 模板预览（带数据）
     *
     * @param templateId 模板ID
     * @param request    预览请求参数（包含简历数据）
     * @param userId     当前用户ID（可选，用于权限判断）
     * @return 预览响应
     */
    @Override
    public TemplatePreviewResponse previewTemplateWithData(Long templateId, TemplatePreviewWithDataRequest request, Long userId) {
        log.info("开始生成模板预览（带数据），模板ID: {}, 用户ID: {}, 预览格式: {}", templateId, userId, request.getFormat());
        
        try {
            // 验证模板是否存在
            ResumeTemplates template = getById(templateId);
            if (template == null || template.getIsDeleted() == 1) {
                throw new BusinessException("模板不存在或已删除");
            }
            
            // 检查模板状态
            if (template.getStatus() == 0) {
                throw new BusinessException("模板已下架，无法预览");
            }
            
            // 验证简历数据
            if (request.getResumeData() == null || request.getResumeData().isEmpty()) {
                throw new BusinessException("简历数据不能为空");
            }
            
            // 设置默认参数
            TemplatePreviewRequest previewRequest = convertToPreviewRequest(request);
            
            // 生成预览文件（带数据）
            TemplatePreviewResponse response = generatePreviewFileWithData(template, previewRequest, request.getResumeData());
            
            log.info("模板预览（带数据）生成成功，模板ID: {}, 预览URL: {}", templateId, response.getPreviewUrl());
            return response;
            
        } catch (BusinessException e) {
            log.error("模板预览（带数据）生成失败，模板ID: {}, 用户ID: {}, 错误: {}", templateId, userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("模板预览（带数据）生成异常，模板ID: {}, 用户ID: {}", templateId, userId, e);
            throw new BusinessException("模板预览生成失败: " + e.getMessage());
        }
    }

    // ==================== 预览相关私有方法 ====================

    /**
     * 设置默认预览参数
     *
     * @param request 原始请求
     * @return 设置默认值后的请求
     */
    private TemplatePreviewRequest setDefaultPreviewParams(TemplatePreviewRequest request) {
        TemplatePreviewRequest previewRequest = new TemplatePreviewRequest();
        previewRequest.setFormat(StringUtils.hasText(request.getFormat()) ? request.getFormat() : "image");
        previewRequest.setQuality(StringUtils.hasText(request.getQuality()) ? request.getQuality() : "medium");
        previewRequest.setWidth(request.getWidth() != null ? request.getWidth() : 800);
        previewRequest.setHeight(request.getHeight() != null ? request.getHeight() : 0);
        previewRequest.setScale(request.getScale() != null ? request.getScale() : 1.0);
        return previewRequest;
    }

    /**
     * 将带数据预览请求转换为预览请求
     *
     * @param withDataRequest 带数据预览请求
     * @return 预览请求
     */
    private TemplatePreviewRequest convertToPreviewRequest(TemplatePreviewWithDataRequest withDataRequest) {
        TemplatePreviewRequest previewRequest = new TemplatePreviewRequest();
        previewRequest.setFormat(StringUtils.hasText(withDataRequest.getFormat()) ? withDataRequest.getFormat() : "image");
        previewRequest.setQuality(StringUtils.hasText(withDataRequest.getQuality()) ? withDataRequest.getQuality() : "high");
        previewRequest.setWidth(withDataRequest.getWidth() != null ? withDataRequest.getWidth() : 1200);
        previewRequest.setHeight(withDataRequest.getHeight() != null ? withDataRequest.getHeight() : 0);
        previewRequest.setScale(withDataRequest.getScale() != null ? withDataRequest.getScale() : 1.0);
        return previewRequest;
    }

    /**
     * 生成预览文件
     *
     * @param template        模板信息
     * @param previewRequest  预览请求参数
     * @param isTemporary     是否为临时文件
     * @return 预览响应
     */
    private TemplatePreviewResponse generatePreviewFile(ResumeTemplates template, TemplatePreviewRequest previewRequest, Boolean isTemporary) {
        // 计算实际尺寸
        Integer actualWidth = previewRequest.getWidth();
        Integer actualHeight = previewRequest.getHeight() == 0 ? 
                calculateHeight(actualWidth) : previewRequest.getHeight();
        
        // 生成预览文件URL（实际应该调用专门的预览生成服务）
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String previewUrl = generatePreviewUrl(template.getId(), previewRequest.getFormat(), actualWidth, timestamp);
        
        // 构建响应
        TemplatePreviewResponse response = new TemplatePreviewResponse();
        response.setTemplateId(template.getId());
        response.setTemplateName(template.getName());
        response.setPreviewType(previewRequest.getFormat());
        response.setPreviewUrl(previewUrl);
        
        // 设置多尺寸预览URL
        response.setPreviewUrls(generateMultiSizePreviewUrls(template.getId(), timestamp, previewRequest.getFormat()));
        
        // 设置尺寸信息
        response.setDimensions(new TemplatePreviewResponse.PreviewDimensions(actualWidth, actualHeight));
        
        // 设置其他信息
        response.setGenerateTime(LocalDateTime.now());
        response.setExpiresIn(isTemporary ? 1800 : 3600); // 临时文件30分钟，普通文件1小时
        response.setIsTemporary(isTemporary);
        response.setFileSize(estimateFileSize(actualWidth, actualHeight, previewRequest.getQuality()));
        
        return response;
    }

    /**
     * 生成预览文件（带数据）
     *
     * @param template       模板信息
     * @param previewRequest 预览请求参数
     * @param resumeData     简历数据
     * @return 预览响应
     */
    private TemplatePreviewResponse generatePreviewFileWithData(ResumeTemplates template, TemplatePreviewRequest previewRequest, Map<String, Object> resumeData) {
        // 验证数据完整性
        validateResumeData(resumeData);
        
        // 计算实际尺寸
        Integer actualWidth = previewRequest.getWidth();
        Integer actualHeight = previewRequest.getHeight() == 0 ? 
                calculateHeightWithData(actualWidth, resumeData) : previewRequest.getHeight();
        
        // 生成预览文件URL（实际应该调用专门的预览生成服务，并传入简历数据）
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String previewUrl = generatePreviewUrlWithData(template.getId(), previewRequest.getFormat(), actualWidth, timestamp);
        
        // 构建响应
        TemplatePreviewResponse response = new TemplatePreviewResponse();
        response.setTemplateId(template.getId());
        response.setTemplateName(template.getName());
        response.setPreviewType(previewRequest.getFormat());
        response.setPreviewUrl(previewUrl);
        
        // 设置尺寸信息
        response.setDimensions(new TemplatePreviewResponse.PreviewDimensions(actualWidth, actualHeight));
        
        // 设置其他信息
        response.setGenerateTime(LocalDateTime.now());
        response.setExpiresIn(1800); // 临时文件30分钟过期
        response.setIsTemporary(true);
        response.setFileSize(estimateFileSize(actualWidth, actualHeight, previewRequest.getQuality()));
        
        return response;
    }

    /**
     * 计算预览图高度
     *
     * @param width 宽度
     * @return 计算出的高度
     */
    private Integer calculateHeight(Integer width) {
        // 根据A4纸张比例计算高度 (297/210 ≈ 1.414)
        return (int) (width * 1.414);
    }

    /**
     * 根据数据计算预览图高度
     *
     * @param width      宽度
     * @param resumeData 简历数据
     * @return 计算出的高度
     */
    private Integer calculateHeightWithData(Integer width, Map<String, Object> resumeData) {
        // 基础高度
        int baseHeight = calculateHeight(width);
        
        // 根据数据内容调整高度（简化计算）
        int dataFactor = calculateDataComplexity(resumeData);
        return baseHeight + (dataFactor * 100); // 每个复杂度单位增加100像素
    }

    /**
     * 计算数据复杂度
     *
     * @param resumeData 简历数据
     * @return 复杂度分数
     */
    private int calculateDataComplexity(Map<String, Object> resumeData) {
        int complexity = 0;
        
        // 统计各个模块的数据量
        if (resumeData.containsKey("workExperiences")) {
            Object workExp = resumeData.get("workExperiences");
            if (workExp instanceof List) {
                complexity += ((List<?>) workExp).size();
            }
        }
        
        if (resumeData.containsKey("educations")) {
            Object edu = resumeData.get("educations");
            if (edu instanceof List) {
                complexity += ((List<?>) edu).size();
            }
        }
        
        if (resumeData.containsKey("projects")) {
            Object projects = resumeData.get("projects");
            if (projects instanceof List) {
                complexity += ((List<?>) projects).size();
            }
        }
        
        return Math.min(complexity, 5); // 最大复杂度为5
    }

    /**
     * 生成预览URL
     *
     * @param templateId 模板ID
     * @param format     预览格式
     * @param width      宽度
     * @param timestamp  时间戳
     * @return 预览URL
     */
    private String generatePreviewUrl(Long templateId, String format, Integer width, String timestamp) {
        return String.format("https://cdn.example.com/templates/%d/preview_%d_%s.%s", 
                templateId, width, timestamp, format.equals("image") ? "jpg" : format);
    }

    /**
     * 生成带数据的预览URL
     *
     * @param templateId 模板ID
     * @param format     预览格式
     * @param width      宽度
     * @param timestamp  时间戳
     * @return 预览URL
     */
    private String generatePreviewUrlWithData(Long templateId, String format, Integer width, String timestamp) {
        return String.format("https://cdn.example.com/previews/temp_%d_%d_%s.%s", 
                templateId, width, timestamp, format.equals("image") ? "jpg" : format);
    }

    /**
     * 生成多尺寸预览URL
     *
     * @param templateId 模板ID
     * @param timestamp  时间戳
     * @param format     预览格式
     * @return 多尺寸预览URL映射
     */
    private Map<String, String> generateMultiSizePreviewUrls(Long templateId, String timestamp, String format) {
        Map<String, String> previewUrls = new HashMap<>();
        String extension = format.equals("image") ? "jpg" : format;
        
        previewUrls.put("thumbnail", String.format("https://cdn.example.com/templates/%d/preview_200_%s.%s", 
                templateId, timestamp, extension));
        previewUrls.put("medium", String.format("https://cdn.example.com/templates/%d/preview_800_%s.%s", 
                templateId, timestamp, extension));
        previewUrls.put("large", String.format("https://cdn.example.com/templates/%d/preview_1200_%s.%s", 
                templateId, timestamp, extension));
        
        return previewUrls;
    }

    /**
     * 估算文件大小
     *
     * @param width   宽度
     * @param height  高度
     * @param quality 质量
     * @return 估算的文件大小（字节）
     */
    private Long estimateFileSize(Integer width, Integer height, String quality) {
        long baseSize = (long) width * height;
        
        // 根据质量调整大小
        double qualityFactor = switch (quality) {
            case "low" -> 0.3;
            case "medium" -> 0.6;
            case "high" -> 1.0;
            default -> 0.6;
        };
        
        // 假设每像素平均3字节（RGB），再乘以质量因子和压缩比
        return (long) (baseSize * 3 * qualityFactor * 0.3); // 30%的压缩比
    }

    /**
     * 验证简历数据
     *
     * @param resumeData 简历数据
     */
    private void validateResumeData(Map<String, Object> resumeData) {
        // 检查基本信息
        if (!resumeData.containsKey("basicInfo")) {
            log.warn("简历数据缺少基本信息模块");
        }
        
        // 记录数据统计信息
        log.debug("简历数据包含模块数量: {}", resumeData.size());
        resumeData.keySet().forEach(key -> log.debug("数据模块: {}", key));
    }

    /**
     * 检查模板是否存在
     *
     * @param templateId 模板ID
     * @return 是否存在
     */
    @Override
    public boolean existsById(Long templateId) {
        if (templateId == null || templateId <= 0) {
            return false;
        }
        
        try {
            LambdaQueryWrapper<ResumeTemplates> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResumeTemplates::getId, templateId)
                   .eq(ResumeTemplates::getIsDeleted, 0);
            
            return count(wrapper) > 0;
        } catch (Exception e) {
            log.error("检查模板是否存在失败，模板ID: {}", templateId, e);
            return false;
        }
    }

    /**
     * 删除模板（软删除）
     *
     * @param templateId 模板ID
     * @param adminUserId 管理员用户ID
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Long templateId, Long adminUserId) {
        log.info("开始删除模板，模板ID: {}, 管理员ID: {}", templateId, adminUserId);
        
        try {
            // 查询模板信息
            ResumeTemplates template = getById(templateId);
            log.info("查询到的模板信息: {}", template != null ? 
                String.format("ID=%d, name=%s, isDeleted=%d", template.getId(), template.getName(), template.getIsDeleted()) : 
                "null");
                
            if (template == null || template.getIsDeleted() == 1) {
                log.warn("模板不存在或已删除，模板ID: {}, template: {}", templateId, template);
                return false;
            }
            
            // 执行软删除 - 使用LambdaUpdateWrapper确保字段正确更新
            LambdaUpdateWrapper<ResumeTemplates> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ResumeTemplates::getId, templateId)
                         .eq(ResumeTemplates::getIsDeleted, 0) // 确保只更新未删除的记录
                         .set(ResumeTemplates::getIsDeleted, 1)
                         .set(ResumeTemplates::getUpdateTime, LocalDateTime.now());
            
            boolean success = update(updateWrapper);
            
            if (success) {
                log.info("模板删除成功，模板ID: {}, 管理员ID: {}", templateId, adminUserId);
            } else {
                log.warn("模板删除失败，模板ID: {}, 管理员ID: {}", templateId, adminUserId);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("删除模板异常，模板ID: {}, 管理员ID: {}", templateId, adminUserId, e);
            throw new BusinessException("删除模板失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除模板（软删除）
     *
     * @param templateIds 模板ID列表
     * @param adminUserId 管理员用户ID
     * @return 删除结果统计
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchDeleteTemplates(List<Long> templateIds, Long adminUserId) {
        log.info("开始批量删除模板，模板数量: {}, 管理员ID: {}", templateIds.size(), adminUserId);
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failureCount = 0;
        List<String> failureReasons = new ArrayList<>();
        
        try {
            for (Long templateId : templateIds) {
                try {
                    boolean success = deleteTemplate(templateId, adminUserId);
                    if (success) {
                        successCount++;
                    } else {
                        failureCount++;
                        failureReasons.add("模板ID " + templateId + ": 删除失败");
                    }
                } catch (Exception e) {
                    failureCount++;
                    failureReasons.add("模板ID " + templateId + ": " + e.getMessage());
                    log.error("批量删除中单个模板删除失败，模板ID: {}", templateId, e);
                }
            }
            
            result.put("successCount", successCount);
            result.put("failureCount", failureCount);
            result.put("totalCount", templateIds.size());
            result.put("failureReasons", failureReasons);
            
            log.info("批量删除模板完成，成功: {}, 失败: {}, 管理员ID: {}", 
                    successCount, failureCount, adminUserId);
            
            return result;
            
        } catch (Exception e) {
            log.error("批量删除模板异常，管理员ID: {}", adminUserId, e);
            throw new BusinessException("批量删除模板失败: " + e.getMessage());
        }
    }

}
