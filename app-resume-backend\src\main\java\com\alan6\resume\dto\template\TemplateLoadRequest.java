package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 模板加载请求DTO
 * @description 用于前端请求获取Vue模板文件的请求参数
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(name = "TemplateLoadRequest", description = "模板加载请求参数")
public class TemplateLoadRequest {

    /**
     * 模板ID
     * @description 要加载的模板ID
     */
    @NotNull(message = "模板ID不能为空")
    @Schema(description = "模板ID", required = true, example = "1")
    private Long templateId;

    /**
     * 模板代码
     * @description 模板的唯一标识代码，可以作为备用查询条件
     */
    @Schema(description = "模板代码", example = "modern-simple")
    private String templateCode;

    /**
     * 是否强制刷新缓存
     * @description 是否跳过Redis缓存，直接从MinIO或本地加载
     */
    @Schema(description = "是否强制刷新缓存", example = "false")
    private Boolean forceRefresh = false;

    /**
     * 客户端类型
     * @description 客户端类型，用于统计和优化
     */
    @Schema(description = "客户端类型", example = "web")
    private String clientType = "web";

    /**
     * 请求来源
     * @description 请求来源页面，用于统计分析
     */
    @Schema(description = "请求来源", example = "editor")
    private String source;
} 