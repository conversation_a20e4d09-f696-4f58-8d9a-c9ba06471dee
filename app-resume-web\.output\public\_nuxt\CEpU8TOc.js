import{r as h,W as oe,k as A}from"./CURHyiUL.js";import{s as ue}from"./x_rD_Ya3.js";const x={RESUME:{LIST:"/api/resume/list",DETAIL:"/api/resume/detail",CREATE:"/api/resume/create",UPDATE:"/api/resume/update",DELETE:"/api/resume/delete",EXPORT:"/api/resume/export"},TEMPLATE:{LIST:"/api/template/list",DETAIL:"/api/template/detail"}},ie=()=>{const t=h(!1),o=h(null),l=async(c,a={})=>{var u;try{const{$fetch:g}=oe();return await g(c,{...a,headers:{"Content-Type":"application/json",...a.headers}})}catch(g){throw console.error("请求失败:",g),new Error(((u=g.data)==null?void 0:u.message)||g.message||"网络请求失败")}};return{loading:t,error:o,fetchResumeList:async(c={})=>{try{return t.value=!0,o.value=null,(await l(x.RESUME.LIST,{method:"GET",query:c})).data||[]}catch(a){throw o.value=a.message,a}finally{t.value=!1}},fetchResume:async c=>{try{t.value=!0,o.value=null;const a=await l(`${x.RESUME.DETAIL}/${c}`,{method:"GET"});if(!a.data)throw new Error("简历数据不存在");return a.data}catch(a){throw o.value=a.message,a}finally{t.value=!1}},createResume:async c=>{try{return t.value=!0,o.value=null,(await l(x.RESUME.CREATE,{method:"POST",body:c})).data}catch(a){throw o.value=a.message,a}finally{t.value=!1}},saveResume:async c=>{try{t.value=!0,o.value=null;const a=!c.id,u=a?x.RESUME.CREATE:x.RESUME.UPDATE;return(await l(u,{method:a?"POST":"PUT",body:c})).data}catch(a){throw o.value=a.message,a}finally{t.value=!1}},deleteResume:async c=>{try{return t.value=!0,o.value=null,await l(`${x.RESUME.DELETE}/${c}`,{method:"DELETE"}),!0}catch(a){throw o.value=a.message,a}finally{t.value=!1}},exportResume:async(c,a="pdf")=>{try{return t.value=!0,o.value=null,await l(`${x.RESUME.EXPORT}/${c}`,{method:"POST",body:{format:a},responseType:"blob"})}catch(u){throw o.value=u.message,u}finally{t.value=!1}},fetchTemplateList:async(c={})=>{try{return t.value=!0,o.value=null,(await l(x.TEMPLATE.LIST,{method:"GET",query:c})).data||[]}catch(a){throw o.value=a.message,a}finally{t.value=!1}},fetchTemplate:async c=>{try{t.value=!0,o.value=null;const a=await l(`${x.TEMPLATE.DETAIL}/${c}`,{method:"GET"});if(!a.data)throw new Error("模板数据不存在");return a.data}catch(a){throw o.value=a.message,a}finally{t.value=!1}},clearError:()=>{o.value=null},validateResumeData:c=>{var g,R,I;const a=[];(g=c.title)!=null&&g.trim()||a.push("简历标题不能为空"),(R=c.modules)!=null&&R.length||a.push("简历必须包含至少一个模块");const u=(I=c.modules)==null?void 0:I.find(b=>b.type==="basic-info");if(!u)a.push("简历必须包含基本信息模块");else{const{name:b,phone:_,email:L}=u.data||{};b!=null&&b.trim()||a.push("姓名不能为空"),_!=null&&_.trim()||a.push("联系电话不能为空"),L!=null&&L.trim()?ce(L)||a.push("邮箱格式不正确"):a.push("邮箱不能为空")}return{isValid:a.length===0,errors:a}}}};function ce(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)}const de=(t,o=50)=>{const l=h([]),n=h(-1),m=A(()=>n.value>0),p=A(()=>n.value<l.value.length-1),v=(a=null)=>{try{const u=a||$(t.value);n.value<l.value.length-1&&(l.value=l.value.slice(0,n.value+1)),l.value.push(u),n.value=l.value.length-1,l.value.length>o&&(l.value.shift(),n.value=Math.max(0,n.value-1)),console.log(`状态已保存，当前历史位置: ${n.value}`)}catch(u){console.error("保存状态失败:",u)}},M=()=>{if(!m.value)return console.warn("无法撤销：已到达历史记录开始"),null;try{n.value-=1;const a=$(l.value[n.value]);return console.log(`撤销操作，回到历史位置: ${n.value}`),a}catch(a){return console.error("撤销操作失败:",a),null}},E=()=>{if(!p.value)return console.warn("无法重做：已到达历史记录末尾"),null;try{n.value+=1;const a=$(l.value[n.value]);return console.log(`重做操作，前进到历史位置: ${n.value}`),a}catch(a){return console.error("重做操作失败:",a),null}},y=(a=null)=>{try{const u=a||$(t.value);l.value=[u],n.value=0,console.log("历史记录已重置")}catch(u){console.error("重置历史记录失败:",u)}},q=()=>{l.value=[],n.value=-1,console.log("历史记录已清空")},C=()=>({totalHistory:l.value.length,currentPosition:n.value+1,canUndo:m.value,canRedo:p.value,undoCount:n.value,redoCount:l.value.length-1-n.value}),D=a=>{if(a<0||a>=l.value.length)return console.warn(`无效的历史位置: ${a}`),null;try{n.value=a;const u=$(l.value[a]);return console.log(`跳转到历史位置: ${a}`),u}catch(u){return console.error("跳转历史位置失败:",u),null}},c=(a=3e4)=>{let u=null,g=null;const R=()=>{u&&I(),u=ue(()=>{const b=JSON.stringify(t.value);g!==b&&(v(),g=b,console.log("自动保存状态"))},a),console.log(`自动保存已启动，间隔: ${a}ms`)},I=()=>{u&&(clearInterval(u),u=null,console.log("自动保存已停止"))};return{start:R,stop:I}};return{history:H(l),currentIndex:H(n),canUndo:m,canRedo:p,saveState:v,undo:M,redo:E,reset:y,clear:q,getStats:C,jumpTo:D,createAutoSaver:c}};function $(t){if(t===null||typeof t!="object")return t;if(t instanceof Date)return new Date(t.getTime());if(t instanceof Array)return t.map(o=>$(o));if(typeof t=="object"){const o={};for(const l in t)t.hasOwnProperty(l)&&(o[l]=$(t[l]));return o}return t}function H(t){return A(()=>t.value)}const ve=[{id:"basic_info",type:"basic_info",name:"基本信息",icon:"user",order:1,enabled:!0,required:!0,description:"个人基本信息，包括姓名、联系方式等",tips:"填写准确的个人信息，确保HR能够联系到您",examples:["姓名应使用真实姓名","手机号保持畅通","邮箱地址要准确"]},{id:"education",type:"education",name:"教育经历",icon:"graduation-cap",order:2,enabled:!0,required:!1,description:"教育背景和学历信息",tips:"按时间倒序排列，突出相关专业和优秀成绩",examples:["2018-2022 北京大学 计算机科学与技术 本科","主修课程：数据结构、算法、数据库等"]},{id:"work_experience",type:"work_experience",name:"工作经历",icon:"briefcase",order:3,enabled:!0,required:!1,description:"工作经验和职业发展",tips:"用数据说话，突出工作成果和贡献",examples:["负责用户增长项目，使活跃用户提升30%","优化系统性能，响应时间减少50%"]},{id:"project",type:"project",name:"项目经验",icon:"folder",order:4,enabled:!0,required:!1,description:"参与的重要项目经验",tips:"重点介绍技术栈、个人职责和项目成果",examples:["使用Vue3+TypeScript开发管理系统","负责前端架构设计和核心功能开发"]},{id:"skills",type:"skills",name:"技能标签",icon:"star",order:5,enabled:!0,required:!1,description:"专业技能和特长",tips:"列出与目标职位相关的技能，可分类展示",examples:["编程语言：JavaScript、Python、Java","框架技术：Vue.js、React、Node.js"]},{id:"language",type:"language",name:"语言能力",icon:"globe",order:6,enabled:!0,required:!1,description:"掌握的语言及熟练程度",tips:"诚实填写语言水平，可标注相关证书",examples:["英语：CET-6，流利的听说读写","日语：N2水平，商务交流无障碍"]},{id:"award",type:"award",name:"获奖荣誉",icon:"trophy",order:7,enabled:!0,required:!1,description:"获得的奖项和荣誉",tips:"按重要性和时间排序，突出与职位相关的奖项",examples:["2023年度优秀员工","全国大学生数学建模竞赛一等奖"]},{id:"hobbies",type:"hobbies",name:"兴趣爱好",icon:"heart",order:8,enabled:!0,required:!1,description:"个人兴趣爱好和特长",tips:"展示个人特色，可体现团队协作或领导能力",examples:["摄影、旅行、阅读","篮球队队长，组织团队活动"]},{id:"self_evaluation",type:"self_evaluation",name:"自我评价",icon:"message-circle",order:9,enabled:!0,required:!1,description:"个人特点和优势总结",tips:"简洁地描述您的个人特点和职业优势",examples:["突出与目标职位相关的优势","展示学习能力和适应性","体现团队协作和沟通能力","保持真实，避免过度夸大","控制在100-200字左右"]},{id:"internship",type:"internship",name:"实习经历",icon:"briefcase",order:10,enabled:!1,required:!1,description:"实习经验和学习成果",tips:"重点描述实习期间的工作内容和收获",examples:["在阿里巴巴实习3个月，参与电商平台开发","负责前端页面优化，提升用户体验"]},{id:"training",type:"training",name:"培训经历",icon:"book",order:11,enabled:!1,required:!1,description:"参加的培训课程和学习经历",tips:"列出与职位相关的培训经历和技能提升",examples:["AWS云计算架构师培训","敏捷开发Scrum Master认证"]},{id:"research_experience",type:"research_experience",name:"研究经历",icon:"search",order:12,enabled:!1,required:!1,description:"参与的研究项目和学术活动",tips:"突出研究成果和学术贡献",examples:["参与导师的机器学习项目研究","发表SCI论文2篇"]},{id:"publication",type:"publication",name:"论文专利",icon:"file-text",order:13,enabled:!1,required:!1,description:"发表的论文和申请的专利",tips:"按影响因子和重要性排序",examples:["IEEE论文：基于深度学习的图像识别算法","发明专利：一种新型数据压缩方法"]},{id:"certificate",type:"certificate",name:"证书资质",icon:"award",order:14,enabled:!1,required:!1,description:"获得的专业证书和资质",tips:"列出与目标职位相关的证书",examples:["PMP项目管理专业人士认证","AWS解决方案架构师认证"]},{id:"volunteer_experience",type:"volunteer_experience",name:"志愿服务",icon:"users",order:15,enabled:!1,required:!1,description:"参与的志愿服务活动",tips:"体现社会责任感和奉献精神",examples:["支教志愿者，为山区儿童提供编程教育","社区志愿服务，组织老年人智能手机培训"]},{id:"portfolio",type:"portfolio",name:"作品集",icon:"images",order:16,enabled:!1,required:!1,description:"个人作品和项目展示",tips:"展示最佳作品，提供在线链接",examples:["个人博客：https://myblog.com","GitHub项目：https://github.com/username"]},{id:"cover_letter",type:"cover_letter",name:"自荐信",icon:"mail",order:17,enabled:!1,required:!1,description:"求职自荐信和求职意向",tips:"撰写针对性的求职自荐信",examples:["开头表达对公司和职位的兴趣","突出与职位匹配的经验和技能","展示对公司的了解和认同","表达加入团队的热情和期待","保持简洁，控制在300-500字"]}],O={theme:{primaryColor:"#3B82F6",fontFamily:"system-ui",fontSize:"14px",lineHeight:"1.6"},layout:{marginTop:"20px",marginBottom:"20px",marginLeft:"20px",marginRight:"20px",spacing:"16px"},typography:{headingSize:"18px",headingWeight:"600",bodySize:"14px",bodyWeight:"400"}};let k=null;const Ee=()=>(k?console.log("使用已存在的全局 store 实例"):(console.log("创建新的全局 store 实例"),k=fe()),k);function fe(){const t=h({modules:{},settings:{...O},title:"我的简历"}),o=h(null),l=h([...ve]),n=h([]),m=h("basic_info"),p=h({...O}),v=h("我的简历"),M=h(!1),E=h(!1),y=h(null),{canUndo:q,canRedo:C,saveState:D,undo:c,redo:a}=de(),{saveResume:u,fetchResume:g,loading:R}=ie(),I=A(()=>t.value&&Object.keys(t.value.modules||{}).length>0),b=A(()=>{const r=[...l.value.filter(s=>s.enabled),...n.value.filter(s=>s.enabled)].sort((s,d)=>(s.order||0)-(d.order||0));return console.log("🔄 enabledModules 计算属性更新:",r.map(s=>`${s.id}(${s.order})`)),r}),_=A(()=>l.value.find(e=>e.id===m.value)),L=A(()=>E.value||R.value),V=async(e,r=null)=>{try{E.value=!0,y.value=null;const s=await g(e);s&&(t.value=s,v.value=s.title||"我的简历",s.templateId&&(o.value={id:s.templateId}),D({resumeData:s,title:v.value,settings:p.value}),console.log("简历数据加载成功:",e))}catch(s){console.error("加载简历数据失败:",s),y.value=s.message||"加载简历数据失败"}finally{E.value=!1}},z=(e,r=null)=>{try{t.value=e,r&&(o.value=r),e.title&&(v.value=e.title),D({resumeData:e,title:v.value,settings:p.value}),console.log("简历数据设置成功")}catch(s){console.error("设置简历数据失败:",s),y.value=s.message||"设置简历数据失败"}},B=async(e={})=>{try{E.value=!0,y.value=null;const r={id:pe(),title:e.title||"我的简历",templateId:e.templateId||null,modules:{},settings:{...O},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};t.value=r,v.value=r.title,e.templateId&&(o.value={id:e.templateId,...e.templateInfo}),D({resumeData:r,title:v.value,settings:p.value}),console.log("新简历创建成功:",r.id)}catch(r){console.error("创建新简历失败:",r),y.value=r.message||"创建新简历失败"}finally{E.value=!1}},J=async()=>{if(!t.value||!t.value.id)return console.warn("没有可保存的简历数据或简历ID"),!1;try{E.value=!0,y.value=null,t.value.updatedAt=new Date().toISOString();const e=await u(t.value);if(e.success)return M.value=!1,console.log("简历保存成功:",e.id),!0;throw new Error(e.message||"保存失败")}catch(e){return console.error("保存简历失败:",e),y.value=e.message||"保存简历失败",!1}finally{E.value=!1}},X=e=>{if(l.value.find(r=>r.id===e)){m.value=e;return}if(n.value.find(r=>r.id===e)){m.value=e;return}console.warn("setCurrentModule - 未找到模块:",e)},j=e=>{var r;return(r=t.value)!=null&&r.modules?t.value.modules[e]:null},K=(e,r)=>{var f;console.log("🔄 updateModuleData 被调用:",e,r),(f=t.value)!=null&&f.modules||(t.value.modules={});const s=t.value.modules[e],d={...t.value.modules};d[e]=r,t.value={...t.value,modules:d},console.log("📊 模块数据更新:",{moduleId:e,oldData:s,newData:r,allModules:Object.keys(t.value.modules)}),S(),D({resumeData:t.value,title:v.value,settings:p.value})},Q=e=>{var d;const r=l.value.find(f=>f.id===e);if(r&&r.required){console.warn("不能删除必需的模块:",e);return}const s=l.value.find(f=>f.id===e);s&&(s.enabled=!1),(d=t.value)!=null&&d.modules&&delete t.value.modules[e],m.value===e&&(m.value="basic_info"),S()},Y=(e,r)=>{console.log(`🔄 开始移动模块: ${e}, 方向: ${r>0?"下移":"上移"}`);const s=["basic","basic_info","basicInfo"];if(s.includes(e)){console.warn("⚠️ 基本信息模块不允许移动，始终保持在第一位");return}const d=[...l.value.filter(i=>i.enabled),...n.value.filter(i=>i.enabled)];d.sort((i,P)=>(i.order||0)-(P.order||0)),console.log("📋 当前模块顺序:",d.map(i=>`${i.id}(${i.order})`));const f=d.findIndex(i=>i.type===e||i.id===e);if(f===-1){console.warn("未找到模块:",e);return}const U=f+r;if(U<0||U>=d.length){console.warn("模块已到达边界，无法移动");return}const w=d[U];if(s.includes(w.id)||s.includes(w.type)){console.warn("⚠️ 不能移动到基本信息模块的位置，基本信息模块必须保持在第一位");return}const T=d[f];console.log(`🔄 交换模块: ${T.id}(${T.order}) ↔ ${w.id}(${w.order})`);const se=T.order;T.order=w.order,w.order=se;const N=l.value.findIndex(i=>i.id===T.id);N!==-1&&(l.value[N].order=T.order);const G=l.value.findIndex(i=>i.id===w.id);G!==-1&&(l.value[G].order=w.order);const F=n.value.findIndex(i=>i.id===T.id);F!==-1&&(n.value[F].order=T.order);const W=n.value.findIndex(i=>i.id===w.id);W!==-1&&(n.value[W].order=w.order),l.value=[...l.value],n.value=[...n.value],S(),console.log(`✅ 模块 ${e} ${r>0?"下移":"上移"} 成功`),console.log("📋 移动后模块顺序:",[...l.value.filter(i=>i.enabled),...n.value.filter(i=>i.enabled)].sort((i,P)=>(i.order||0)-(P.order||0)).map(i=>`${i.id}(${i.order})`))},Z=e=>{if(n.value.length>=3)throw new Error("最多只能创建3个自定义模块");const r=`custom_${Date.now()}`,s={id:r,type:"custom",name:e,icon:"folder",order:50+n.value.length,enabled:!1,required:!1,description:"自定义模块",tips:"记录您的特殊经历或成就",examples:["填写名称和担任的角色","记录起始时间和详细内容"]};return n.value.push(s),S(),r},ee=e=>{const r=n.value.find(s=>s.id===e);if(r){if(r.enabled=!r.enabled,r.enabled){const s=[...l.value.filter(f=>f.enabled),...n.value.filter(f=>f.enabled)],d=s.length>0?Math.max(...s.map(f=>f.order||0)):0;r.order=d+1}S()}},te=e=>{var s;const r=n.value.findIndex(d=>d.id===e);r!==-1&&(n.value.splice(r,1),(s=t.value)!=null&&s.modules&&delete t.value.modules[e],m.value===e&&(m.value="basic_info"),S())},ae=e=>{p.value={...p.value,...e},t.value&&(t.value.settings=p.value),S()},le=e=>{v.value=e,t.value&&(t.value.title=e),S()},re=()=>{const e=c();e&&(t.value=e.resumeData,v.value=e.title,p.value=e.settings)},ne=()=>{const e=a();e&&(t.value=e.resumeData,v.value=e.title,p.value=e.settings)},S=()=>{M.value=!0};return{currentResumeData:t,resumeData:t,currentTemplate:o,moduleConfigs:l,customModules:n,currentModuleId:m,settings:p,title:v,isDirty:M,isLoading:L,error:y,hasResumeData:I,enabledModules:b,currentModule:_,canUndo:q,canRedo:C,loadResumeData:V,setResumeData:z,createNewResume:B,saveResume:J,setCurrentModule:X,getModuleData:j,updateModuleData:K,deleteModule:Q,moveModule:Y,createCustomModule:Z,toggleCustomModule:ee,deleteCustomModule:te,updateSettings:ae,updateTitle:le,undo:re,redo:ne,markAsDirty:S,clearError:()=>{y.value=null},reset:()=>{t.value={modules:{},settings:{...O},title:"我的简历"},o.value=null,m.value="basic_info",v.value="我的简历",M.value=!1,E.value=!1,y.value=null}}}function pe(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}export{Ee as u};
