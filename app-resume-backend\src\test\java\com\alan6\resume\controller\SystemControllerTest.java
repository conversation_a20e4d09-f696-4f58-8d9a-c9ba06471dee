package com.alan6.resume.controller;

import com.alan6.resume.dto.system.*;
import com.alan6.resume.service.ISystemConfigsService;
import com.alan6.resume.service.ISystemMonitorService;
import com.alan6.resume.service.ISupportedLanguagesService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemController 测试类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@WebMvcTest(SystemController.class)
class SystemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ISystemConfigsService systemConfigsService;

    @MockBean
    private ISystemMonitorService systemMonitorService;

    @MockBean
    private ISupportedLanguagesService supportedLanguagesService;

    @Autowired
    private ObjectMapper objectMapper;

    private SystemConfigResponse mockConfigResponse;
    private SystemConfigRequest mockConfigRequest;
    private SystemHealthResponse mockHealthResponse;
    private SystemStatusResponse mockStatusResponse;
    private SystemMetricsResponse mockMetricsResponse;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockConfigResponse = new SystemConfigResponse();
        mockConfigResponse.setId(1L);
        mockConfigResponse.setConfigKey("test.config.key");
        mockConfigResponse.setConfigValue("test_value");
        mockConfigResponse.setConfigType("string");
        mockConfigResponse.setConfigGroup("test");
        mockConfigResponse.setDescription("测试配置");
        mockConfigResponse.setCreateTime(LocalDateTime.now());
        mockConfigResponse.setUpdateTime(LocalDateTime.now());

        mockConfigRequest = new SystemConfigRequest();
        mockConfigRequest.setConfigKey("test.config.key");
        mockConfigRequest.setConfigValue("test_value");
        mockConfigRequest.setConfigType("string");
        mockConfigRequest.setConfigGroup("test");
        mockConfigRequest.setDescription("测试配置");

        // 初始化健康检查响应
        mockHealthResponse = new SystemHealthResponse();
        mockHealthResponse.setStatus("UP");
        mockHealthResponse.setCheckTime(LocalDateTime.now());
        mockHealthResponse.setComponents(new HashMap<>());

        // 初始化系统状态响应
        mockStatusResponse = new SystemStatusResponse();
        mockStatusResponse.setCheckTime(LocalDateTime.now());

        // 初始化系统指标响应
        mockMetricsResponse = new SystemMetricsResponse();
        mockMetricsResponse.setCheckTime(LocalDateTime.now());
    }

    /**
     * 测试获取系统配置列表 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetConfigList_WithAdminRole_Success() throws Exception {
        // 准备测试数据
        Page<SystemConfigResponse> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(mockConfigResponse));
        mockPage.setTotal(1);

        // 模拟service行为
        when(systemConfigsService.getConfigList(eq("test"), eq(1), eq(10)))
                .thenReturn(mockPage);

        // 执行测试
        mockMvc.perform(get("/system/configs")
                        .param("group", "test")
                        .param("page", "1")
                        .param("size", "10")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.records[0].configKey").value("test.config.key"))
                .andExpect(jsonPath("$.data.records[0].configValue").value("test_value"));
    }

    /**
     * 测试获取系统配置列表 - 无管理员权限
     */
    @Test
    @WithMockUser(roles = "USER")
    void testGetConfigList_WithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/system/configs")
                        .with(csrf()))
                .andExpect(status().isForbidden());
    }

    /**
     * 测试获取单个配置项 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetConfigByKey_WithAdminRole_Success() throws Exception {
        // 模拟service行为
        when(systemConfigsService.getConfigByKey("test.config.key"))
                .thenReturn(mockConfigResponse);

        // 执行测试
        mockMvc.perform(get("/system/configs/test.config.key")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.configKey").value("test.config.key"))
                .andExpect(jsonPath("$.data.configValue").value("test_value"));
    }

    /**
     * 测试更新配置项 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateConfig_WithAdminRole_Success() throws Exception {
        // 模拟service行为
        when(systemConfigsService.updateConfig(eq("test.config.key"), any(SystemConfigRequest.class)))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(put("/system/configs/test.config.key")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mockConfigRequest))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试创建配置项 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testCreateConfig_WithAdminRole_Success() throws Exception {
        // 模拟service行为
        when(systemConfigsService.createConfig(any(SystemConfigRequest.class)))
                .thenReturn(mockConfigResponse);

        // 执行测试
        mockMvc.perform(post("/system/configs")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mockConfigRequest))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.configKey").value("test.config.key"));
    }

    /**
     * 测试获取配置分组列表 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetConfigGroups_WithAdminRole_Success() throws Exception {
        // 模拟service行为
        List<String> mockGroups = Arrays.asList("group1", "group2", "group3");
        when(systemConfigsService.getConfigGroups()).thenReturn(mockGroups);

        // 执行测试
        mockMvc.perform(get("/system/configs/groups")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.length()").value(3))
                .andExpect(jsonPath("$.data[0]").value("group1"))
                .andExpect(jsonPath("$.data[1]").value("group2"))
                .andExpect(jsonPath("$.data[2]").value("group3"));
    }

    /**
     * 测试获取前端配置 - 公开接口
     */
    @Test
    void testGetFrontendConfig_PublicAccess_Success() throws Exception {
        // 模拟service行为
        Map<String, Object> mockFrontendConfig = new HashMap<>();
        mockFrontendConfig.put("frontend.title", "简历系统");
        mockFrontendConfig.put("frontend.version", "1.0.0");
        when(systemConfigsService.getFrontendConfig()).thenReturn(mockFrontendConfig);

        // 执行测试
        mockMvc.perform(get("/system/config"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data['frontend.title']").value("简历系统"))
                .andExpect(jsonPath("$.data['frontend.version']").value("1.0.0"));
    }

    /**
     * 测试系统健康检查 - 公开接口
     */
    @Test
    void testHealthCheck_PublicAccess_Success() throws Exception {
        // 模拟service行为
        when(systemMonitorService.healthCheck()).thenReturn(mockHealthResponse);

        // 执行测试
        mockMvc.perform(get("/system/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.status").value("UP"))
                .andExpect(jsonPath("$.data.checkTime").exists());
    }

    /**
     * 测试获取系统状态 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetSystemStatus_WithAdminRole_Success() throws Exception {
        // 模拟service行为
        when(systemMonitorService.getSystemStatus()).thenReturn(mockStatusResponse);

        // 执行测试
        mockMvc.perform(get("/system/status")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.checkTime").exists());
    }

    /**
     * 测试获取系统指标 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetSystemMetrics_WithAdminRole_Success() throws Exception {
        // 模拟service行为
        when(systemMonitorService.getSystemMetrics()).thenReturn(mockMetricsResponse);

        // 执行测试
        mockMvc.perform(get("/system/metrics")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.checkTime").exists());
    }

    /**
     * 测试清理缓存 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testClearCache_WithAdminRole_Success() throws Exception {
        CacheClearRequest request = new CacheClearRequest();
        request.setCacheType("all");

        // 执行测试
        mockMvc.perform(post("/system/cache/clear")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.clearedKeys").value(0))
                .andExpect(jsonPath("$.data.freedMemory").value("0MB"))
                .andExpect(jsonPath("$.data.operationTime").value("0ms"));
    }

    /**
     * 测试清理缓存 - 无请求体
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testClearCache_WithoutRequestBody_Success() throws Exception {
        // 执行测试
        mockMvc.perform(post("/system/cache/clear")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试系统维护模式 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testSetMaintenanceMode_WithAdminRole_Success() throws Exception {
        MaintenanceRequest request = new MaintenanceRequest();
        request.setEnabled(true);
        request.setMessage("系统维护中");

        // 执行测试
        mockMvc.perform(post("/system/maintenance")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.enabled").value(true))
                .andExpect(jsonPath("$.data.message").value("系统维护中"));
    }

    /**
     * 测试获取支持的语言 - 公开接口
     */
    @Test
    void testGetSupportedLanguages_PublicAccess_Success() throws Exception {
        // 执行测试
        mockMvc.perform(get("/system/languages"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    /**
     * 测试更新语言配置 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateLanguageConfig_WithAdminRole_Success() throws Exception {
        Map<String, Object> request = new HashMap<>();
        request.put("isActive", true);
        request.put("sortOrder", 1);

        // 执行测试
        mockMvc.perform(put("/system/languages/zh-CN")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试提交意见反馈 - 登录用户
     */
    @Test
    @WithMockUser
    void testSubmitFeedback_WithUser_Success() throws Exception {
        FeedbackRequest request = new FeedbackRequest();
        request.setType("bug");
        request.setTitle("文件上传失败");
        request.setContent("在上传简历照片时，系统提示文件格式不支持");
        request.setEmail("<EMAIL>");

        // 执行测试
        mockMvc.perform(post("/system/feedback")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.feedbackId").exists())
                .andExpect(jsonPath("$.data.status").value("pending"))
                .andExpect(jsonPath("$.data.message").value("反馈提交成功，我们将尽快处理"));
    }

    /**
     * 测试提交意见反馈 - 参数验证失败
     */
    @Test
    @WithMockUser
    void testSubmitFeedback_ValidationFailed() throws Exception {
        FeedbackRequest request = new FeedbackRequest();
        // 缺少必填字段

        // 执行测试
        mockMvc.perform(post("/system/feedback")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试获取反馈列表 - 管理员权限
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetFeedbackList_WithAdminRole_Success() throws Exception {
        // 执行测试
        mockMvc.perform(get("/system/feedback")
                        .param("type", "bug")
                        .param("status", "pending")
                        .param("page", "1")
                        .param("size", "10")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists());
    }

    /**
     * 测试获取帮助文档 - 公开接口
     */
    @Test
    void testGetHelpDocuments_PublicAccess_Success() throws Exception {
        // 执行测试
        mockMvc.perform(get("/system/help")
                        .param("category", "user"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.categories").exists())
                .andExpect(jsonPath("$.data.total").value(0));
    }

    /**
     * 测试获取帮助文章详情 - 公开接口
     */
    @Test
    void testGetHelpArticle_PublicAccess_Success() throws Exception {
        // 执行测试
        mockMvc.perform(get("/system/help/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.title").value("帮助文章标题"))
                .andExpect(jsonPath("$.data.content").value("帮助文章内容"));
    }

    /**
     * 测试创建配置项 - 参数验证失败
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testCreateConfig_ValidationFailed() throws Exception {
        SystemConfigRequest invalidRequest = new SystemConfigRequest();
        // 缺少必填字段configKey

        // 执行测试
        mockMvc.perform(post("/system/configs")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest))
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试更新配置项 - 参数验证失败
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateConfig_ValidationFailed() throws Exception {
        SystemConfigRequest invalidRequest = new SystemConfigRequest();
        // 缺少必填字段configKey

        // 执行测试
        mockMvc.perform(put("/system/configs/test.key")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest))
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试系统维护模式 - 参数验证失败
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testSetMaintenanceMode_ValidationFailed() throws Exception {
        // 执行测试（发送无效JSON）
        mockMvc.perform(post("/system/maintenance")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{invalid json}")
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试获取系统配置列表 - 默认参数
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetConfigList_DefaultParams_Success() throws Exception {
        // 准备测试数据
        Page<SystemConfigResponse> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(mockConfigResponse));
        mockPage.setTotal(1);

        // 模拟service行为
        when(systemConfigsService.getConfigList(isNull(), eq(1), eq(10)))
                .thenReturn(mockPage);

        // 执行测试（不传递group参数）
        mockMvc.perform(get("/system/configs")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(1));
    }

    /**
     * 测试获取反馈列表 - 默认参数
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetFeedbackList_DefaultParams_Success() throws Exception {
        // 执行测试（不传递可选参数）
        mockMvc.perform(get("/system/feedback")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试获取帮助文档 - 无分类参数
     */
    @Test
    void testGetHelpDocuments_NoCategory_Success() throws Exception {
        // 执行测试（不传递category参数）
        mockMvc.perform(get("/system/help"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.categories").exists())
                .andExpect(jsonPath("$.data.total").value(0));
    }
} 