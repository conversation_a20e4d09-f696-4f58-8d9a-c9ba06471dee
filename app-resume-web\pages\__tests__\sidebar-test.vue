<template>
  <div class="test-page">
    <h1>侧边栏测试页面</h1>
    
    <div class="test-info">
      <h2>当前状态</h2>
      <ul>
        <li>当前路由: {{ $route.path }}</li>
        <li>侧边栏折叠: {{ sidebarCollapsed }}</li>
        <li>模板子菜单展开: {{ templateSubmenuOpen }}</li>
        <li>活跃菜单项: {{ activeMenuItem }}</li>
      </ul>
    </div>
    
    <div class="test-actions">
      <h2>测试操作</h2>
      <button @click="toggleSubmenu" class="btn">切换模板子菜单</button>
      <button @click="navigateToConverter" class="btn">导航到转换器</button>
    </div>
    
    <div class="test-links">
      <h2>测试链接</h2>
      <ul>
        <li><NuxtLink to="/admin">管理后台首页</NuxtLink></li>
        <li><NuxtLink to="/admin/template/list">模板列表</NuxtLink></li>
        <li><NuxtLink to="/admin/template/converter">模板转换</NuxtLink></li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAdminStore } from '~/composables/admin/useAdminStore'

const router = useRouter()
const { sidebarCollapsed, activeMenuItem } = useAdminStore()

// 模拟模板子菜单状态
const templateSubmenuOpen = ref(false)

const toggleSubmenu = () => {
  templateSubmenuOpen.value = !templateSubmenuOpen.value
}

const navigateToConverter = () => {
  router.push('/admin/template/converter')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-info, .test-actions, .test-links {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.btn {
  padding: 8px 16px;
  margin-right: 10px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn:hover {
  background: #2563eb;
}

ul {
  list-style-type: disc;
  padding-left: 20px;
}

li {
  margin: 5px 0;
}

a {
  color: #3b82f6;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style> 