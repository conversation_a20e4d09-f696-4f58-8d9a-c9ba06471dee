package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.system.LanguageConfigResponse;
import com.alan6.resume.dto.system.LanguageListResponse;
import com.alan6.resume.entity.SupportedLanguages;
import com.alan6.resume.mapper.SupportedLanguagesMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * LanguageServiceImpl 测试类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("多语言服务测试")
class LanguageServiceImplTest {

    @Mock
    private SupportedLanguagesMapper supportedLanguagesMapper;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private LanguageServiceImpl languageService;

    private SupportedLanguages testLanguage;
    private SupportedLanguages defaultLanguage;

    /**
     * 测试数据初始化
     */
    @BeforeEach
    void setUp() {
        // 初始化测试语言
        testLanguage = new SupportedLanguages();
        testLanguage.setId(1L);
        testLanguage.setLanguageCode("en-US");
        testLanguage.setLanguageName("English");
        testLanguage.setNativeName("English");
        testLanguage.setIsActive((byte) 1);
        testLanguage.setIsDefault((byte) 0);
        testLanguage.setConfigContent("{\"common\":{\"save\":\"Save\"}}");
        testLanguage.setConfigVersion("v1.0.0");
        testLanguage.setCreateTime(LocalDateTime.now());

        // 初始化默认语言
        defaultLanguage = new SupportedLanguages();
        defaultLanguage.setId(2L);
        defaultLanguage.setLanguageCode("zh-CN");
        defaultLanguage.setLanguageName("简体中文");
        defaultLanguage.setNativeName("简体中文");
        defaultLanguage.setIsActive((byte) 1);
        defaultLanguage.setIsDefault((byte) 1);
        defaultLanguage.setConfigContent("{\"common\":{\"save\":\"保存\"}}");
        defaultLanguage.setConfigVersion("v1.0.0");
        defaultLanguage.setCreateTime(LocalDateTime.now());
    }

    /**
     * 测试获取支持的语言列表 - 成功
     */
    @Test
    @DisplayName("获取支持的语言列表 - 成功")
    void testGetSupportedLanguages_Success() {
        // 准备测试数据
        List<SupportedLanguages> languageList = Arrays.asList(defaultLanguage, testLanguage);

        // 模拟mapper调用
        when(supportedLanguagesMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(languageList);

        // 执行测试
        LanguageListResponse result = languageService.getSupportedLanguages();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getLanguages());
        assertEquals(2, result.getLanguages().size());

        // 验证第一个语言（默认语言）
        LanguageListResponse.LanguageInfo firstLanguage = result.getLanguages().get(0);
        assertEquals("zh-CN", firstLanguage.getCode());
        assertEquals("简体中文", firstLanguage.getName());
        assertTrue(firstLanguage.getIsDefault());
        assertTrue(firstLanguage.getEnabled());

        // 验证mapper调用
        verify(supportedLanguagesMapper).selectList(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试获取支持的语言列表 - 空列表
     */
    @Test
    @DisplayName("获取支持的语言列表 - 空列表")
    void testGetSupportedLanguages_EmptyList() {
        // 模拟mapper调用返回空列表
        when(supportedLanguagesMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        LanguageListResponse result = languageService.getSupportedLanguages();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getLanguages());
        assertEquals(0, result.getLanguages().size());

        // 验证mapper调用
        verify(supportedLanguagesMapper).selectList(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试获取语言配置 - 成功
     */
    @Test
    @DisplayName("获取语言配置 - 成功")
    void testGetLanguageConfig_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> configMap = new HashMap<>();
        configMap.put("common", Map.of("save", "Save"));

        // 模拟mapper调用
        when(supportedLanguagesMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testLanguage);
        when(objectMapper.readValue(eq(testLanguage.getConfigContent()), eq(java.util.Map.class)))
                .thenReturn(configMap);

        // 执行测试
        LanguageConfigResponse result = languageService.getLanguageConfig("en-US");

        // 验证结果
        assertNotNull(result);
        assertEquals("en-US", result.getLanguageCode());

        // 验证mapper调用
        verify(supportedLanguagesMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试获取语言配置 - 语言不存在
     */
    @Test
    @DisplayName("获取语言配置 - 语言不存在")
    void testGetLanguageConfig_LanguageNotFound() {
        // 模拟mapper调用
        when(supportedLanguagesMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> languageService.getLanguageConfig("unknown"));
        
        assertEquals("语言不存在", exception.getMessage());

        // 验证mapper调用
        verify(supportedLanguagesMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试更新语言配置 - 成功
     */
    @Test
    @DisplayName("更新语言配置 - 成功")
    void testUpdateLanguageConfig_Success() throws Exception {
        // 准备测试数据
        String newConfig = "{\"common\":{\"save\":\"Save\"}}";

        // 模拟mapper调用
        when(supportedLanguagesMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testLanguage);
        when(objectMapper.readValue(eq(newConfig), eq(java.util.Map.class))).thenReturn(new HashMap<>());
        when(supportedLanguagesMapper.updateById(any(SupportedLanguages.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> languageService.updateLanguageConfig("en-US", newConfig));

        // 验证mapper调用
        verify(supportedLanguagesMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(supportedLanguagesMapper).updateById(any(SupportedLanguages.class));
    }

    /**
     * 测试启用/禁用语言 - 成功
     */
    @Test
    @DisplayName("启用/禁用语言 - 成功")
    void testToggleLanguage_Success() {
        // 模拟mapper调用
        when(supportedLanguagesMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testLanguage);
        when(supportedLanguagesMapper.updateById(any(SupportedLanguages.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> languageService.toggleLanguage("en-US", true));

        // 验证mapper调用
        verify(supportedLanguagesMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(supportedLanguagesMapper).updateById(any(SupportedLanguages.class));
    }

    /**
     * 测试禁用默认语言 - 失败
     */
    @Test
    @DisplayName("禁用默认语言 - 失败")
    void testToggleLanguage_DisableDefault() {
        // 模拟mapper调用
        when(supportedLanguagesMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(defaultLanguage);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> languageService.toggleLanguage("zh-CN", false));
        
        assertEquals("默认语言不能被禁用", exception.getMessage());

        // 验证mapper调用
        verify(supportedLanguagesMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(supportedLanguagesMapper, never()).updateById(any(SupportedLanguages.class));
    }
} 