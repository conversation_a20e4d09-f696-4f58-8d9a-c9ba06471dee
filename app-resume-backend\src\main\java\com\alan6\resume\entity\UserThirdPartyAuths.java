package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户第三方授权表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("user_third_party_auths")
@Schema(name = "UserThirdPartyAuths对象", description = "用户第三方授权表")
public class UserThirdPartyAuths implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "授权记录ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "平台类型（wechat_mp:微信小程序,douyin_mp:抖音小程序,baidu_mp:百度小程序,alipay_mp:支付宝小程序,wechat_app:微信APP,qq:QQ等）")
    private String platform;

    @Schema(description = "平台OpenID")
    private String openid;

    @Schema(description = "平台UnionID（如果有）")
    private String unionid;

    @Schema(description = "平台昵称")
    private String platformNickname;

    @Schema(description = "平台头像")
    private String platformAvatar;

    @Schema(description = "访问令牌")
    private String accessToken;

    @Schema(description = "刷新令牌")
    private String refreshToken;

    @Schema(description = "token过期时间（秒）")
    private Integer expiresIn;

    @Schema(description = "绑定时间")
    private LocalDateTime bindTime;

    @Schema(description = "最后授权时间")
    private LocalDateTime lastAuthTime;

    @Schema(description = "状态（0:解绑,1:已绑定）")
    private Byte status;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
