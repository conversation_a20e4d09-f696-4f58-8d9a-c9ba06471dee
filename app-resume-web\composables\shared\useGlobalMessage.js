/**
 * 全局消息服务
 * @description 管理应用全局的消息提示功能
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, readonly } from 'vue'

// ================================
// 响应式数据
// ================================
const messageState = ref({
  visible: false,
  type: 'info',
  message: '',
  duration: 4000
})

// ================================
// 消息服务类
// ================================
class GlobalMessageService {
  /**
   * 显示消息
   * @param {string} message - 消息内容
   * @param {string} type - 消息类型 (success, error, warning, info)
   * @param {number} duration - 显示时长（毫秒）
   */
  static show(message, type = 'info', duration = 4000) {
    messageState.value = {
      visible: true,
      type,
      message,
      duration
    }
  }

  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长（毫秒）
   */
  static success(message, duration = 4000) {
    this.show(message, 'success', duration)
  }

  /**
   * 显示错误消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长（毫秒）
   */
  static error(message, duration = 5000) {
    this.show(message, 'error', duration)
  }

  /**
   * 显示警告消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长（毫秒）
   */
  static warning(message, duration = 4000) {
    this.show(message, 'warning', duration)
  }

  /**
   * 显示信息消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长（毫秒）
   */
  static info(message, duration = 4000) {
    this.show(message, 'info', duration)
  }

  /**
   * 隐藏消息
   */
  static hide() {
    messageState.value = {
      ...messageState.value,
      visible: false
    }
  }

  /**
   * 清除消息
   */
  static clear() {
    messageState.value = {
      visible: false,
      type: 'info',
      message: '',
      duration: 4000
    }
  }

  /**
   * 获取当前消息状态
   * @returns {Object} 消息状态对象
   */
  static getState() {
    return messageState.value
  }
}

// ================================
// Composable函数
// ================================
export const useGlobalMessage = () => {
  return {
    // 状态（只读）
    messageState: readonly(messageState),
    
    // 方法
    showMessage: GlobalMessageService.show.bind(GlobalMessageService),
    showSuccess: GlobalMessageService.success.bind(GlobalMessageService),
    showError: GlobalMessageService.error.bind(GlobalMessageService),
    showWarning: GlobalMessageService.warning.bind(GlobalMessageService),
    showInfo: GlobalMessageService.info.bind(GlobalMessageService),
    hideMessage: GlobalMessageService.hide.bind(GlobalMessageService),
    clearMessage: GlobalMessageService.clear.bind(GlobalMessageService)
  }
}

// ================================
// 默认导出服务类（用于直接调用）
// ================================
export default GlobalMessageService

// ================================
// 便捷的全局方法
// ================================
export const $message = {
  success: GlobalMessageService.success.bind(GlobalMessageService),
  error: GlobalMessageService.error.bind(GlobalMessageService),
  warning: GlobalMessageService.warning.bind(GlobalMessageService),
  info: GlobalMessageService.info.bind(GlobalMessageService),
  show: GlobalMessageService.show.bind(GlobalMessageService),
  hide: GlobalMessageService.hide.bind(GlobalMessageService),
  clear: GlobalMessageService.clear.bind(GlobalMessageService)
} 