<template>
  <div class="spacing-control">
    <!-- 间距按钮 -->
    <button 
      class="control-btn"
      @click="togglePanel"
      :class="{ 'active': isPanelOpen }"
      title="模块间距设置"
    >
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="8" y1="6" x2="21" y2="6"></line>
        <line x1="8" y1="12" x2="21" y2="12"></line>
        <line x1="8" y1="18" x2="21" y2="18"></line>
        <line x1="3" y1="6" x2="3.01" y2="6"></line>
        <line x1="3" y1="12" x2="3.01" y2="12"></line>
        <line x1="3" y1="18" x2="3.01" y2="18"></line>
      </svg>
      <span class="btn-label">模块间距</span>
      <svg class="dropdown-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="6,9 12,15 18,9"></polyline>
      </svg>
    </button>
    
    <!-- 间距设置面板 -->
    <Transition name="spacing-panel">
      <div v-show="isPanelOpen" class="spacing-panel">
        <div class="panel-header">
          <h4 class="panel-title">模块间距设置</h4>
        </div>
        
        <div class="spacing-sliders">
          <!-- 模块间距 -->
          <div class="slider-group">
            <label class="slider-label">模块间-间距</label>
            <div class="slider-container">
              <input
                v-model="localSpacings.module"
                type="range"
                :min="spacingConfig.module.min"
                :max="spacingConfig.module.max"
                :step="spacingConfig.module.step"
                class="slider"
                @input="handleSpacingChange"
              />
              <div class="slider-value">{{ localSpacings.module }}px</div>
            </div>
          </div>
          
          <!-- 模块项间距 -->
          <div class="slider-group">
            <label class="slider-label">模块项-间距</label>
            <div class="slider-container">
              <input
                v-model="localSpacings.item"
                type="range"
                :min="spacingConfig.item.min"
                :max="spacingConfig.item.max"
                :step="spacingConfig.item.step"
                class="slider"
                @input="handleSpacingChange"
              />
              <div class="slider-value">{{ localSpacings.item }}px</div>
            </div>
          </div>
          
          <!-- 模块内边距 -->
          <div class="slider-group">
            <label class="slider-label">模块内-边距</label>
            <div class="slider-container">
              <input
                v-model="localSpacings.modulePadding"
                type="range"
                :min="spacingConfig.modulePadding.min"
                :max="spacingConfig.modulePadding.max"
                :step="spacingConfig.modulePadding.step"
                class="slider"
                @input="handleSpacingChange"
              />
              <div class="slider-value">{{ localSpacings.modulePadding }}px</div>
            </div>
          </div>

          <!-- 行距 -->
          <div class="slider-group">
            <label class="slider-label">模块内-行距</label>
            <div class="slider-container">
              <input
                v-model="localSpacings.line"
                type="range"
                :min="spacingConfig.line.min"
                :max="spacingConfig.line.max"
                :step="spacingConfig.line.step"
                class="slider"
                @input="handleSpacingChange"
              />
              <div class="slider-value">{{ localSpacings.line }}</div>
            </div>
          </div>
        </div>
        
        <!-- 重置按钮 -->
        <div class="panel-footer">
          <button class="reset-btn" @click="resetSpacings">
            重置默认
          </button>
        </div>
      </div>
    </Transition>
    
    <!-- 点击遮罩层关闭 -->
    <div 
      v-if="isPanelOpen"
      class="spacing-overlay"
      @click="closePanel"
    ></div>
  </div>
</template>

<script setup>
/**
 * 间距控制组件
 * @description 提供间距调整功能，支持弹出式滑块面板
 * <AUTHOR>
 * @since 2.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 间距设置对象
   */
  modelValue: {
    type: Object,
    default: () => ({
      module: 24,   // 模块间距
      item: 16,     // 模块项间距
      line: 1.6,     // 行距
      modulePadding: 16 // 模块内边距
    })
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['update:modelValue', 'change'])

// ================================
// 响应式数据
// ================================

/**
 * 面板开启状态
 */
const isPanelOpen = ref(false)

/**
 * 本地间距设置
 */
const localSpacings = reactive({
  module: 24,
  item: 16,
  line: 1.6,
  modulePadding: 16
})

/**
 * 间距配置
 */
const spacingConfig = {
  module: {
    min: 8,
    max: 48,
    step: 2,
    default: 24
  },
  item: {
    min: 4,
    max: 32,
    step: 2,
    default: 16
  },
  modulePadding: {
    min: 0,
    max: 40,
    step: 2,
    default: 16
  },
  line: {
    min: 1.0,
    max: 2.5,
    step: 0.1,
    default: 1.6
  }
}

// ================================
// 生命周期钩子
// ================================
onMounted(() => {
  // 初始化本地设置
  Object.assign(localSpacings, props.modelValue)
  
  // 监听外部变化
  watch(() => props.modelValue, (newValue) => {
    Object.assign(localSpacings, newValue)
  }, { deep: true })
  
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// ================================
// 事件处理方法
// ================================

/**
 * 切换面板
 */
const togglePanel = () => {
  isPanelOpen.value = !isPanelOpen.value
}

/**
 * 关闭面板
 */
const closePanel = () => {
  isPanelOpen.value = false
}

/**
 * 处理间距变化
 */
const handleSpacingChange = () => {
  const newValue = { ...localSpacings }
  
  // 确保行距为数值类型
  newValue.line = parseFloat(newValue.line)
  
  // 添加调试信息
  console.log('🐛 SpacingControl 间距变化:', {
    moduleSpacing: newValue.module,
    itemSpacing: newValue.item,
    lineHeight: newValue.line,
    fullValue: newValue
  })
  
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

/**
 * 重置间距
 */
const resetSpacings = () => {
  localSpacings.module = spacingConfig.module.default
  localSpacings.item = spacingConfig.item.default
  localSpacings.line = spacingConfig.line.default
  localSpacings.modulePadding = spacingConfig.modulePadding.default
  handleSpacingChange()
}

/**
 * 处理点击外部区域
 * @param {Event} event - 点击事件
 */
const handleClickOutside = (event) => {
  const spacingControl = event.target.closest('.spacing-control')
  if (!spacingControl && isPanelOpen.value) {
    closePanel()
  }
}
</script>

<style scoped>
/* ================================
 * 间距控制容器
 * ================================ */
.spacing-control {
  @apply relative;
}

/* ================================
 * 控制按钮
 * ================================ */
.control-btn {
  @apply flex items-center gap-2;
  @apply px-3 py-1.5;
  @apply text-sm font-medium text-gray-700;
  @apply bg-white border border-gray-200 rounded-lg;
  @apply hover:bg-gray-50 hover:border-gray-300;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.control-btn.active {
  @apply bg-primary-50 border-primary-200 text-primary-700;
}

.btn-label {
  @apply text-sm;
}

.dropdown-icon {
  @apply text-gray-400 transition-transform duration-200;
}

.control-btn.active .dropdown-icon {
  @apply rotate-180 text-primary-500;
}

/* ================================
 * 间距设置面板
 * ================================ */
.spacing-panel {
  @apply absolute top-full right-0 mt-2 z-20;
  @apply bg-white border border-gray-200 rounded-lg shadow-lg;
  @apply p-4 min-w-72;
}

.panel-header {
  @apply mb-3 pb-2 border-b border-gray-100;
}

.panel-title {
  @apply text-sm font-semibold text-gray-900;
}

/* ================================
 * 滑块组
 * ================================ */
.spacing-sliders {
  @apply space-y-4;
}

.slider-group {
  @apply space-y-2;
}

.slider-label {
  @apply block text-xs font-medium text-gray-600;
}

.slider-container {
  @apply flex items-center gap-3;
}

/* ================================
 * 滑块样式
 * ================================ */
.slider {
  @apply flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-primary-500 rounded-full cursor-pointer;
  @apply hover:bg-primary-600 transition-colors duration-200;
  @apply shadow-sm;
}

.slider::-moz-range-thumb {
  @apply w-4 h-4 bg-primary-500 rounded-full cursor-pointer border-0;
  @apply hover:bg-primary-600 transition-colors duration-200;
  @apply shadow-sm;
}

.slider-value {
  @apply text-xs font-medium text-gray-600 min-w-12 text-center;
  @apply bg-gray-50 px-2 py-1 rounded;
}

/* ================================
 * 面板底部
 * ================================ */
.panel-footer {
  @apply mt-4 pt-3 border-t border-gray-100;
  @apply flex justify-center;
}

.reset-btn {
  @apply px-3 py-1.5 text-xs;
  @apply text-gray-600 hover:text-gray-900;
  @apply bg-gray-50 hover:bg-gray-100;
  @apply border border-gray-200 rounded;
  @apply transition-colors duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

/* ================================
 * 遮罩层
 * ================================ */
.spacing-overlay {
  @apply fixed inset-0 z-10;
  @apply bg-transparent;
}

/* ================================
 * 面板动画
 * ================================ */
.spacing-panel-enter-active,
.spacing-panel-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.spacing-panel-enter-from {
  @apply opacity-0 -translate-y-2 scale-95;
}

.spacing-panel-leave-to {
  @apply opacity-0 translate-y-2 scale-95;
}

.spacing-panel-enter-to,
.spacing-panel-leave-from {
  @apply opacity-100 translate-y-0 scale-100;
}

/* ================================
 * 响应式设计
 * ================================ */
@media (max-width: 768px) {
  .spacing-panel {
    @apply left-0 right-auto min-w-64;
  }
  
  .control-btn {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-label {
    @apply hidden;
  }
}
</style> 