package com.alan6.resume.common.constants;

/**
 * 认证相关常量定义类
 * 
 * 主要功能：
 * 1. 定义Token相关常量
 * 2. 定义Redis Key前缀常量
 * 3. 定义认证相关时间常量
 * 4. 定义HTTP请求头常量
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
public class AuthConstants {

    /**
     * Token相关常量
     */
    public static final class Token {
        
        /** Token前缀，用于HTTP请求头 */
        public static final String TOKEN_PREFIX = "Bearer ";
        
        /** HTTP请求头中Token的字段名 */
        public static final String HEADER_NAME = "Authorization";
        
        /** Token长度（32位随机字符串） */
        public static final int TOKEN_LENGTH = 32;
        
        /** Token过期时间（秒）- 7天 */
        public static final long TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60;
        
        /** Token刷新阈值（秒）- 剩余1天时可刷新 */
        public static final long TOKEN_REFRESH_THRESHOLD = 24 * 60 * 60;
    }

    /**
     * Redis Key相关常量
     */
    public static final class RedisKey {
        
        /** 用户Token信息的Redis Key前缀 */
        public static final String USER_TOKEN_KEY = "user:token:";
        
        /** 用户Token集合的Redis Key前缀（用于多设备Token管理） */
        public static final String USER_TOKENS_SET = "user:tokens:";
        
        /** 用户信息缓存的Redis Key前缀 */
        public static final String USER_INFO_KEY = "user:info:";
        
        /** 短信验证码的Redis Key前缀 */
        public static final String SMS_CODE_KEY = "sms:code:";
        
        /** 微信授权临时数据的Redis Key前缀 */
        public static final String WECHAT_AUTH_KEY = "wechat:auth:";
        
        /** 用户登录失败次数的Redis Key前缀 */
        public static final String LOGIN_FAIL_KEY = "login:fail:";
    }

    /**
     * 登录相关常量
     */
    public static final class Login {
        
        /** 登录类型：手机号登录 */
        public static final int TYPE_PHONE = 1;
        
        /** 登录类型：微信授权登录 */
        public static final int TYPE_WECHAT = 2;
        
        /** 登录类型：小程序登录 */
        public static final int TYPE_MINIPROGRAM = 3;
        
        /** 最大登录失败次数 */
        public static final int MAX_LOGIN_FAIL_COUNT = 5;
        
        /** 登录失败锁定时间（秒）- 30分钟 */
        public static final long LOGIN_FAIL_LOCK_TIME = 30 * 60;
    }

    /**
     * 平台相关常量
     */
    public static final class Platform {
        
        /** Web端 */
        public static final String WEB = "web";
        
        /** 微信小程序 */
        public static final String WECHAT_MP = "wechat_mp";
        
        /** 抖音小程序 */
        public static final String DOUYIN_MP = "douyin_mp";
        
        /** 百度小程序 */
        public static final String BAIDU_MP = "baidu_mp";
        
        /** 支付宝小程序 */
        public static final String ALIPAY_MP = "alipay_mp";
        
        /** iOS应用 */
        public static final String APP_IOS = "app_ios";
        
        /** Android应用 */
        public static final String APP_ANDROID = "app_android";
    }

    /**
     * 短信验证码相关常量
     */
    public static final class SmsCode {
        
        /** 验证码长度 */
        public static final int CODE_LENGTH = 6;
        
        /** 验证码有效期（秒）- 5分钟 */
        public static final long CODE_EXPIRE_TIME = 5 * 60;
        
        /** 验证码发送间隔（秒）- 1分钟 */
        public static final long CODE_SEND_INTERVAL = 60;
        
        /** 同一手机号每日最大发送次数 */
        public static final int MAX_SEND_COUNT_PER_DAY = 10;
    }

    /**
     * 用户状态相关常量
     */
    public static final class UserStatus {
        
        /** 用户状态：禁用 */
        public static final byte DISABLED = 0;
        
        /** 用户状态：正常 */
        public static final byte NORMAL = 1;
        
        /** 删除状态：未删除 */
        public static final byte NOT_DELETED = 0;
        
        /** 删除状态：已删除 */
        public static final byte DELETED = 1;
    }

    /**
     * 私有构造函数，防止实例化工具类
     */
    private AuthConstants() {
        throw new IllegalStateException("AuthConstants is a utility class and cannot be instantiated");
    }
} 