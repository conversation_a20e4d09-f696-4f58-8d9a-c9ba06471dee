<!-- 
  简历下载功能演示页面
  @description 演示下载下拉菜单的UI和交互效果，不依赖后端API
  <AUTHOR>
  @since 1.0.0
-->
<template>
  <div class="download-demo-page">
    <!-- 页面标题 -->
    <div class="demo-header">
      <h1 class="demo-title">简历下载功能演示</h1>
      <p class="demo-description">
        这是一个纯前端演示页面，展示下载下拉菜单的UI效果和交互体验
      </p>
    </div>

    <!-- 演示区域 -->
    <div class="demo-container">
      <!-- 模拟编辑器头部 -->
      <div class="mock-editor-header">
        <div class="mock-editor-title">
          <h2>我的简历</h2>
          <span class="mock-save-status">已保存</span>
        </div>
        
        <!-- 下载按钮区域 -->
        <div class="mock-actions">
          <DownloadDropdown 
            :is-exporting="isExporting"
            :export-status="exportStatus"
            @download="handleDownload"
          />
        </div>
      </div>

      <!-- 模拟简历预览 -->
      <div class="mock-resume-preview">
        <div class="mock-resume-content">
          <div class="mock-resume-header">
            <h3>张三</h3>
            <p>前端开发工程师</p>
          </div>
          <div class="mock-resume-section">
            <h4>工作经历</h4>
            <div class="mock-work-item">
              <strong>火花简历 - 高级前端工程师</strong>
              <span>2022-至今</span>
              <p>负责简历编辑器的开发和维护，实现了多种简历模板和导出功能</p>
            </div>
          </div>
          <div class="mock-resume-section">
            <h4>技能特长</h4>
            <div class="mock-skills">
              <span class="skill-tag">Vue.js</span>
              <span class="skill-tag">JavaScript</span>
              <span class="skill-tag">CSS3</span>
              <span class="skill-tag">Node.js</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作日志 -->
    <div class="demo-logs">
      <h3>操作日志</h3>
      <div class="log-container">
        <div 
          v-for="(log, index) in logs" 
          :key="index"
          :class="['log-item', `log-${log.type}`]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import DownloadDropdown from '~/components/editor/layout/DownloadDropdown.vue'

// ================================
// 页面元数据
// ================================
definePageMeta({
  title: '下载功能演示',
  description: '简历下载功能的UI演示页面'
})

// ================================
// 响应式状态
// ================================

/**
 * 是否正在导出
 */
const isExporting = ref(false)

/**
 * 导出状态文本
 */
const exportStatus = ref('准备就绪')

/**
 * 操作日志
 */
const logs = ref([])

// ================================
// 工具方法
// ================================

/**
 * 添加操作日志
 * @param {string} message - 日志消息
 * @param {string} type - 日志类型 (info/success/error/warning)
 */
const addLog = (message, type = 'info') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  
  logs.value.unshift({
    time: time,
    message: message,
    type: type
  })
  
  // 限制日志数量
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

/**
 * 模拟文件下载
 * @param {string} format - 文件格式
 * @param {string} fileName - 文件名
 */
const simulateDownload = (format, fileName) => {
  // 创建模拟文件内容
  let content = ''
  let mimeType = 'text/plain'
  
  switch (format) {
    case 'pdf':
      content = '%PDF-1.4\n这是一个模拟的PDF文件内容'
      mimeType = 'application/pdf'
      break
    case 'word':
      content = '这是一个模拟的Word文档内容'
      mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      break
    case 'image':
      content = '这是一个模拟的图片文件内容'
      mimeType = 'image/png'
      break
  }
  
  // 创建Blob对象
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  
  // 创建下载链接
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  link.style.display = 'none'
  
  // 添加到页面并触发点击
  document.body.appendChild(link)
  link.click()
  
  // 清理
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// ================================
// 事件处理
// ================================

/**
 * 处理下载请求
 * @param {Object} options - 下载选项
 * @param {string} options.format - 下载格式
 */
const handleDownload = async (options) => {
  const { format } = options
  
  addLog(`用户选择下载格式: ${format.toUpperCase()}`, 'info')
  
  try {
    // 设置导出状态
    isExporting.value = true
    exportStatus.value = `正在生成${format.toUpperCase()}文档...`
    
    addLog(`开始生成${format.toUpperCase()}文档`, 'info')
    
    // 模拟处理时间
    const processingTime = 1500 + Math.random() * 2000 // 1.5-3.5秒
    await new Promise(resolve => setTimeout(resolve, processingTime))
    
    // 模拟成功率（90%成功）
    const success = Math.random() > 0.1
    
    if (success) {
      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 10)
      const fileName = `简历_张三_${timestamp}.${format === 'image' ? 'png' : format === 'word' ? 'docx' : 'pdf'}`
      
      // 模拟下载
      simulateDownload(format, fileName)
      
      exportStatus.value = '下载完成'
      addLog(`${format.toUpperCase()}文档生成成功`, 'success')
      addLog(`文件已下载: ${fileName}`, 'success')
      
    } else {
      throw new Error('网络连接不稳定，请重试')
    }
    
  } catch (error) {
    exportStatus.value = '下载失败'
    addLog(`下载失败: ${error.message}`, 'error')
  } finally {
    // 延迟重置状态
    setTimeout(() => {
      isExporting.value = false
      exportStatus.value = '准备就绪'
    }, 2000)
  }
}

// ================================
// 页面初始化
// ================================
onMounted(() => {
  addLog('页面加载完成，可以开始测试下载功能', 'info')
})
</script>

<style scoped>
/* ================================
   页面整体样式
   ================================ */
.download-demo-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* ================================
   演示头部
   ================================ */
.demo-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.demo-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.demo-description {
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* ================================
   演示容器
   ================================ */
.demo-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
}

/* ================================
   模拟编辑器头部
   ================================ */
.mock-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.mock-editor-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.mock-editor-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.mock-save-status {
  background: #10b981;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.mock-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* ================================
   模拟简历预览
   ================================ */
.mock-resume-preview {
  padding: 30px;
  background: white;
}

.mock-resume-content {
  max-width: 800px;
  margin: 0 auto;
  background: #fafafa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.mock-resume-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #3b82f6;
}

.mock-resume-header h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 10px 0;
}

.mock-resume-header p {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0;
}

.mock-resume-section {
  margin-bottom: 25px;
}

.mock-resume-section h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e5e7eb;
}

.mock-work-item {
  margin-bottom: 15px;
}

.mock-work-item strong {
  display: block;
  color: #1f2937;
  margin-bottom: 5px;
}

.mock-work-item span {
  color: #6b7280;
  font-size: 0.9rem;
}

.mock-work-item p {
  margin: 8px 0 0 0;
  color: #4b5563;
  line-height: 1.6;
}

.mock-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.skill-tag {
  background: #3b82f6;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* ================================
   操作日志
   ================================ */
.demo-logs {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.demo-logs h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
}

.log-item {
  display: flex;
  gap: 15px;
  padding: 12px 15px;
  border-bottom: 1px solid #e5e7eb;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #6b7280;
  font-weight: 500;
  min-width: 80px;
}

.log-message {
  flex: 1;
  color: #1f2937;
}

/* 日志类型样式 */
.log-info .log-message {
  color: #3b82f6;
}

.log-success .log-message {
  color: #10b981;
  font-weight: 500;
}

.log-error .log-message {
  color: #ef4444;
  font-weight: 500;
}

.log-warning .log-message {
  color: #f59e0b;
  font-weight: 500;
}

/* ================================
   响应式设计
   ================================ */
@media (max-width: 768px) {
  .download-demo-page {
    padding: 15px;
  }
  
  .demo-title {
    font-size: 2rem;
  }
  
  .mock-editor-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .mock-resume-content {
    padding: 20px;
  }
  
  .mock-resume-header h3 {
    font-size: 1.5rem;
  }
  
  .log-item {
    flex-direction: column;
    gap: 5px;
  }
  
  .log-time {
    min-width: auto;
  }
}
</style> 