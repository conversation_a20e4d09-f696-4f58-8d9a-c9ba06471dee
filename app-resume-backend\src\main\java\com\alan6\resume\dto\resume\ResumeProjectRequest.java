package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 项目经验请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "项目经验请求")
public class ResumeProjectRequest {

    /**
     * 项目名称
     */
    @Schema(description = "项目名称", example = "电商管理系统", required = true)
    @NotBlank(message = "项目名称不能为空")
    @Size(min = 1, max = 100, message = "项目名称长度必须在1-100字符之间")
    private String projectName;

    /**
     * 项目角色
     */
    @Schema(description = "项目角色", example = "前端负责人", required = true)
    @NotBlank(message = "项目角色不能为空")
    @Size(min = 1, max = 50, message = "项目角色长度必须在1-50字符之间")
    private String projectRole;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2022-01-01", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2022-06-30")
    private LocalDate endDate;

    /**
     * 项目描述
     */
    @Schema(description = "项目描述", example = "基于React和Node.js开发的电商后台管理系统，支持商品管理、订单处理、用户管理等功能")
    private String projectDescription;

    /**
     * 主要职责
     */
    @Schema(description = "主要职责", example = "负责前端架构设计、组件开发、性能优化，与后端团队协作完成接口对接")
    private String responsibilities;

    /**
     * 技术栈
     */
    @Schema(description = "技术栈", example = "React, TypeScript, Ant Design, Node.js")
    private String technologies;

    /**
     * 项目链接
     */
    @Schema(description = "项目链接", example = "https://github.com/zhangsan/ecommerce-admin")
    @Size(max = 500, message = "项目链接长度不能超过500字符")
    private String projectUrl;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重", example = "1", required = true)
    @NotNull(message = "排序权重不能为空")
    private Integer sortOrder;
} 