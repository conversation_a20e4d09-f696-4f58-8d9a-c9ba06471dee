package com.alan6.resume.service;

import com.alan6.resume.entity.ResumeTemplateCategories;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 模板分类关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface IResumeTemplateCategoriesService extends IService<ResumeTemplateCategories> {

    /**
     * 根据模板ID获取分类ID列表
     * @param templateId 模板ID
     * @return 分类ID列表
     */
    List<Long> getCategoryIdsByTemplateId(Long templateId);

    /**
     * 根据分类ID列表获取模板ID列表
     * @param categoryIds 分类ID列表
     * @return 模板ID列表
     */
    List<Long> getTemplateIdsByCategoryIds(List<Long> categoryIds);

    /**
     * 更新模板的分类关联
     * @param templateId 模板ID
     * @param categoryIds 分类ID列表
     * @return 是否成功
     */
    boolean updateTemplateCategories(Long templateId, List<Long> categoryIds);

    /**
     * 删除模板的所有分类关联
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean deleteByTemplateId(Long templateId);

    /**
     * 批量创建模板分类关联
     * @param templateId 模板ID
     * @param categoryIds 分类ID列表
     * @return 是否成功
     */
    boolean batchCreateRelations(Long templateId, List<Long> categoryIds);
} 