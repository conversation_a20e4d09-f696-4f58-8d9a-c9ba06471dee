<template>
  <div class="pt-16">
    <!-- Header -->
    <section class="bg-gradient-to-br from-primary-50 to-orange-50 py-16">
      <div class="container-width section-padding">
        <div class="text-center max-w-3xl mx-auto">
          <h1 class="font-display font-bold text-4xl lg:text-5xl text-secondary-900 mb-6">
            使用教程
          </h1>
          <p class="text-xl text-secondary-600">
            快速上手火花简历，轻松制作专业简历
          </p>
        </div>
      </div>
    </section>

    <!-- Tutorial Steps -->
    <section class="py-20">
      <div class="container-width section-padding">
        <div class="space-y-12">
          <div v-for="(step, index) in tutorialSteps" :key="step.id" class="flex flex-col lg:flex-row items-center gap-12">
            <div class="flex-1 order-2 lg:order-1" :class="{ 'lg:order-2': index % 2 === 1 }">
              <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg mr-4">
                  {{ index + 1 }}
                </div>
                <h3 class="font-display font-bold text-2xl text-secondary-900">{{ step.title }}</h3>
              </div>
              <p class="text-lg text-secondary-600 mb-6">{{ step.description }}</p>
              <ul class="space-y-2">
                <li v-for="point in step.points" :key="point" class="flex items-start">
                  <svg class="w-5 h-5 text-success-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-secondary-700">{{ point }}</span>
                </li>
              </ul>
            </div>
            
            <div class="flex-1 order-1 lg:order-2" :class="{ 'lg:order-1': index % 2 === 1 }">
              <div class="bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl p-8 h-64 flex items-center justify-center">
                <div class="text-center">
                  <div class="w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="step.icon"></path>
                    </svg>
                  </div>
                  <p class="text-secondary-600">{{ step.imageText }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
useSeoMeta({
  title: '使用教程 - 火花简历',
  description: '学习如何使用火花简历制作专业简历'
})

const tutorialSteps = ref([
  {
    id: 1,
    title: '选择模板',
    description: '从150+精美模板中选择适合您行业和风格的模板。',
    points: [
      '浏览不同分类的模板',
      '预览模板效果',
      '选择最适合的设计风格'
    ],
    icon: 'M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z',
    imageText: '模板选择界面'
  },
  {
    id: 2,
    title: '填写信息',
    description: '使用我们的智能编辑器，轻松填写您的个人信息、工作经历、教育背景等。',
    points: [
      '智能表单引导填写',
      '实时预览效果',
      'AI智能推荐内容'
    ],
    icon: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z',
    imageText: '信息编辑器'
  },
  {
    id: 3,
    title: '预览调整',
    description: '实时预览简历效果，调整布局和内容，确保简历完美呈现。',
    points: [
      '实时预览功能',
      '拖拽调整布局',
      '字体和颜色自定义'
    ],
    icon: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z',
    imageText: '预览界面'
  },
  {
    id: 4,
    title: '导出分享',
    description: '一键导出为PDF、Word等格式，或生成在线分享链接。',
    points: [
      '多格式导出支持',
      '在线分享功能',
      '下载管理'
    ],
    icon: 'M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4',
    imageText: '导出选项'
  }
])
</script> 