package com.alan6.resume.service;

import com.alan6.resume.entity.UserRoles;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户角色关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
public interface IUserRolesService extends IService<UserRoles> {

    /**
     * 为用户分配角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 移除用户的所有角色
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean removeUserRoles(Long userId);

    /**
     * 获取用户的角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getUserRoleIds(Long userId);

    /**
     * 检查用户是否具有指定角色
     *
     * @param userId 用户ID
     * @param roleCode 角色代码
     * @return 是否具有角色
     */
    boolean hasRole(Long userId, String roleCode);

    /**
     * 检查用户是否为管理员
     *
     * @param userId 用户ID
     * @return 是否为管理员
     */
    boolean isAdmin(Long userId);
} 