package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模板推荐请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板推荐请求DTO")
public class TemplateRecommendRequest {

    /**
     * 推荐数量（默认: 6，最大: 20）
     */
    @Schema(description = "推荐数量", example = "6")
    private Integer limit = 6;

    /**
     * 排除的模板ID列表（可选，逗号分隔）
     */
    @Schema(description = "排除的模板ID列表", example = "1,2,3")
    private String excludeIds;

    /**
     * 指定分类ID（可选，基于分类推荐）
     */
    @Schema(description = "指定分类ID")
    private Long categoryId;

    /**
     * 指定行业（可选，基于行业推荐）
     */
    @Schema(description = "指定行业")
    private String industry;

    /**
     * 是否基于浏览历史推荐（可选，默认: true）
     */
    @Schema(description = "是否基于浏览历史推荐", example = "true")
    private Boolean basedOnHistory = true;
} 