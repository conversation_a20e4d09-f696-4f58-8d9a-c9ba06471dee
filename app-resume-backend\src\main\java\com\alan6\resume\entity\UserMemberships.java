package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户会员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("user_memberships")
@Schema(name = "UserMemberships对象", description = "用户会员表")
public class UserMemberships implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "会员记录ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "套餐ID")
    private Long packageId;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "会员开始时间")
    private LocalDateTime startTime;

    @Schema(description = "会员结束时间")
    private LocalDateTime endTime;

    @Schema(description = "已使用简历数量")
    private Integer usedResumeCount;

    @Schema(description = "本月已导出次数")
    private Integer usedExportCount;

    @Schema(description = "导出次数最后重置时间")
    private LocalDateTime lastExportResetTime;

    @Schema(description = "状态（0:失效,1:生效）")
    private Byte status;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
