package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.system.HelpArticleResponse;
import com.alan6.resume.dto.system.HelpDocumentResponse;
import com.alan6.resume.entity.HelpArticle;
import com.alan6.resume.entity.HelpCategory;
import com.alan6.resume.mapper.HelpArticleMapper;
import com.alan6.resume.mapper.HelpCategoryMapper;
import com.alan6.resume.service.IHelpDocumentService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 帮助文档服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HelpDocumentServiceImpl implements IHelpDocumentService {

    private final HelpCategoryMapper helpCategoryMapper;
    private final HelpArticleMapper helpArticleMapper;

    /**
     * 获取帮助文档列表
     *
     * @param category 分类代码（可选）
     * @param keyword  搜索关键词（可选）
     * @return 帮助文档响应
     */
    @Override
    public HelpDocumentResponse getHelpDocuments(String category, String keyword) {
        log.info("获取帮助文档列表, category: {}, keyword: {}", category, keyword);
        
        try {
            HelpDocumentResponse response = new HelpDocumentResponse();
            List<HelpDocumentResponse.CategoryInfo> categoryInfoList = new ArrayList<>();
            
            // 如果指定了分类，只查询该分类
            if (StringUtils.hasText(category)) {
                HelpDocumentResponse.CategoryInfo categoryInfo = getCategoryWithArticles(category, keyword);
                if (categoryInfo != null) {
                    categoryInfoList.add(categoryInfo);
                }
            } else {
                // 查询所有启用的分类
                LambdaQueryWrapper<HelpCategory> categoryQuery = new LambdaQueryWrapper<>();
                categoryQuery.eq(HelpCategory::getIsEnabled, 1)
                           .eq(HelpCategory::getIsDeleted, 0)
                           .orderByAsc(HelpCategory::getSortOrder);
                
                List<HelpCategory> categories = helpCategoryMapper.selectList(categoryQuery);
                
                // 为每个分类获取文章列表
                for (HelpCategory cat : categories) {
                    HelpDocumentResponse.CategoryInfo categoryInfo = getCategoryWithArticles(cat.getCode(), keyword);
                    if (categoryInfo != null && !categoryInfo.getArticles().isEmpty()) {
                        categoryInfoList.add(categoryInfo);
                    }
                }
            }
            
            response.setCategories(categoryInfoList);
            log.info("获取帮助文档列表成功, 分类数: {}", categoryInfoList.size());
            return response;
            
        } catch (Exception e) {
            log.error("获取帮助文档列表失败: {}", e.getMessage(), e);
            throw new BusinessException("获取帮助文档失败");
        }
    }

    /**
     * 获取帮助文章详情
     *
     * @param articleId 文章ID
     * @return 文章详情
     */
    @Override
    public HelpArticleResponse getHelpArticle(Long articleId) {
        log.info("获取帮助文章详情, articleId: {}", articleId);
        
        try {
            // 查询文章
            HelpArticle article = helpArticleMapper.selectById(articleId);
            if (article == null || article.getIsDeleted() == 1 || article.getIsPublished() == 0) {
                throw new BusinessException("帮助文章不存在或未发布");
            }
            
            // 查询分类信息
            HelpCategory category = helpCategoryMapper.selectById(article.getCategoryId());
            
            // 构建响应
            HelpArticleResponse response = new HelpArticleResponse();
            response.setId(article.getId());
            response.setTitle(article.getTitle());
            response.setContent(article.getContent());
            response.setCategory(category != null ? category.getCode() : "");
            response.setViewCount(article.getViewCount());
            response.setHelpful(article.getHelpfulCount());
            response.setUnhelpful(article.getUnhelpfulCount());
            response.setCreateTime(article.getCreateTime());
            response.setUpdateTime(article.getUpdateTime());
            
            // 解析标签
            if (StringUtils.hasText(article.getTags())) {
                List<String> tagList = Arrays.asList(article.getTags().split(","));
                response.setTags(tagList);
            } else {
                response.setTags(new ArrayList<>());
            }
            
            // 获取相关文章（同分类的其他文章）
            List<HelpArticleResponse.RelatedArticle> relatedArticles = getRelatedArticles(article.getCategoryId(), articleId);
            response.setRelatedArticles(relatedArticles);
            
            // 异步增加浏览次数
            incrementViewCount(articleId);
            
            log.info("获取帮助文章详情成功, articleId: {}, title: {}", articleId, article.getTitle());
            return response;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取帮助文章详情失败, articleId: {}, error: {}", articleId, e.getMessage(), e);
            throw new BusinessException("获取帮助文章详情失败");
        }
    }

    /**
     * 增加文章浏览次数
     *
     * @param articleId 文章ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementViewCount(Long articleId) {
        try {
            HelpArticle article = helpArticleMapper.selectById(articleId);
            if (article != null) {
                article.setViewCount(article.getViewCount() + 1);
                helpArticleMapper.updateById(article);
                log.debug("文章浏览次数增加成功, articleId: {}, newCount: {}", articleId, article.getViewCount());
            }
        } catch (Exception e) {
            log.warn("增加文章浏览次数失败, articleId: {}, error: {}", articleId, e.getMessage());
            // 不抛出异常，避免影响主要业务流程
        }
    }

    /**
     * 评价文章是否有用
     *
     * @param articleId 文章ID
     * @param helpful   是否有用（true:有用, false:无用）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rateArticle(Long articleId, boolean helpful) {
        log.info("评价文章, articleId: {}, helpful: {}", articleId, helpful);
        
        try {
            HelpArticle article = helpArticleMapper.selectById(articleId);
            if (article == null) {
                throw new BusinessException("文章不存在");
            }
            
            if (helpful) {
                article.setHelpfulCount(article.getHelpfulCount() + 1);
            } else {
                article.setUnhelpfulCount(article.getUnhelpfulCount() + 1);
            }
            
            helpArticleMapper.updateById(article);
            log.info("文章评价成功, articleId: {}, helpful: {}, helpfulCount: {}, unhelpfulCount: {}", 
                    articleId, helpful, article.getHelpfulCount(), article.getUnhelpfulCount());
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("评价文章失败, articleId: {}, error: {}", articleId, e.getMessage(), e);
            throw new BusinessException("评价文章失败");
        }
    }

    /**
     * 搜索帮助文章
     *
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    @Override
    public HelpDocumentResponse searchArticles(String keyword) {
        log.info("搜索帮助文章, keyword: {}", keyword);
        
        if (!StringUtils.hasText(keyword)) {
            return getHelpDocuments(null, null);
        }
        
        try {
            // 搜索文章（标题和内容）
            LambdaQueryWrapper<HelpArticle> articleQuery = new LambdaQueryWrapper<>();
            articleQuery.eq(HelpArticle::getIsDeleted, 0)
                       .eq(HelpArticle::getIsPublished, 1)
                       .and(wrapper -> wrapper
                           .like(HelpArticle::getTitle, keyword)
                           .or()
                           .like(HelpArticle::getContent, keyword)
                           .or()
                           .like(HelpArticle::getTags, keyword))
                       .orderByDesc(HelpArticle::getViewCount)
                       .orderByDesc(HelpArticle::getHelpfulCount);
            
            List<HelpArticle> articles = helpArticleMapper.selectList(articleQuery);
            
            // 按分类分组
            Map<Long, List<HelpArticle>> articlesByCategory = articles.stream()
                    .collect(Collectors.groupingBy(HelpArticle::getCategoryId));
            
            // 构建响应
            HelpDocumentResponse response = new HelpDocumentResponse();
            List<HelpDocumentResponse.CategoryInfo> categoryInfoList = new ArrayList<>();
            
            for (Map.Entry<Long, List<HelpArticle>> entry : articlesByCategory.entrySet()) {
                Long categoryId = entry.getKey();
                List<HelpArticle> categoryArticles = entry.getValue();
                
                // 查询分类信息
                HelpCategory category = helpCategoryMapper.selectById(categoryId);
                if (category == null || category.getIsEnabled() == 0) {
                    continue;
                }
                
                // 构建分类信息
                HelpDocumentResponse.CategoryInfo categoryInfo = new HelpDocumentResponse.CategoryInfo();
                categoryInfo.setId(category.getId());
                categoryInfo.setName(category.getCode());
                categoryInfo.setTitle(category.getTitle());
                categoryInfo.setDescription(category.getDescription());
                categoryInfo.setIcon(category.getIcon());
                
                // 构建文章列表
                List<HelpDocumentResponse.ArticleInfo> articleInfoList = categoryArticles.stream()
                        .map(this::convertToArticleInfo)
                        .collect(Collectors.toList());
                
                categoryInfo.setArticles(articleInfoList);
                categoryInfoList.add(categoryInfo);
            }
            
            response.setCategories(categoryInfoList);
            log.info("搜索帮助文章成功, keyword: {}, 结果数: {}", keyword, articles.size());
            return response;
            
        } catch (Exception e) {
            log.error("搜索帮助文章失败, keyword: {}, error: {}", keyword, e.getMessage(), e);
            throw new BusinessException("搜索帮助文章失败");
        }
    }

    /**
     * 获取分类及其文章列表
     *
     * @param categoryCode 分类代码
     * @param keyword      搜索关键词（可选）
     * @return 分类信息
     */
    private HelpDocumentResponse.CategoryInfo getCategoryWithArticles(String categoryCode, String keyword) {
        // 查询分类
        LambdaQueryWrapper<HelpCategory> categoryQuery = new LambdaQueryWrapper<>();
        categoryQuery.eq(HelpCategory::getCode, categoryCode)
                    .eq(HelpCategory::getIsEnabled, 1)
                    .eq(HelpCategory::getIsDeleted, 0);
        
        HelpCategory category = helpCategoryMapper.selectOne(categoryQuery);
        if (category == null) {
            return null;
        }
        
        // 查询该分类下的文章
        LambdaQueryWrapper<HelpArticle> articleQuery = new LambdaQueryWrapper<>();
        articleQuery.eq(HelpArticle::getCategoryId, category.getId())
                   .eq(HelpArticle::getIsDeleted, 0)
                   .eq(HelpArticle::getIsPublished, 1);
        
        // 如果有关键词，添加搜索条件
        if (StringUtils.hasText(keyword)) {
            articleQuery.and(wrapper -> wrapper
                .like(HelpArticle::getTitle, keyword)
                .or()
                .like(HelpArticle::getContent, keyword)
                .or()
                .like(HelpArticle::getTags, keyword));
        }
        
        articleQuery.orderByAsc(HelpArticle::getSortOrder)
                   .orderByDesc(HelpArticle::getViewCount);
        
        List<HelpArticle> articles = helpArticleMapper.selectList(articleQuery);
        
        // 构建响应
        HelpDocumentResponse.CategoryInfo categoryInfo = new HelpDocumentResponse.CategoryInfo();
        categoryInfo.setId(category.getId());
        categoryInfo.setName(category.getCode());
        categoryInfo.setTitle(category.getTitle());
        categoryInfo.setDescription(category.getDescription());
        categoryInfo.setIcon(category.getIcon());
        
        List<HelpDocumentResponse.ArticleInfo> articleInfoList = articles.stream()
                .map(this::convertToArticleInfo)
                .collect(Collectors.toList());
        
        categoryInfo.setArticles(articleInfoList);
        return categoryInfo;
    }

    /**
     * 获取相关文章
     *
     * @param categoryId     分类ID
     * @param currentArticleId 当前文章ID
     * @return 相关文章列表
     */
    private List<HelpArticleResponse.RelatedArticle> getRelatedArticles(Long categoryId, Long currentArticleId) {
        LambdaQueryWrapper<HelpArticle> query = new LambdaQueryWrapper<>();
        query.eq(HelpArticle::getCategoryId, categoryId)
             .eq(HelpArticle::getIsDeleted, 0)
             .eq(HelpArticle::getIsPublished, 1)
             .ne(HelpArticle::getId, currentArticleId)
             .orderByDesc(HelpArticle::getViewCount)
             .last("LIMIT 5"); // 最多返回5篇相关文章
        
        List<HelpArticle> articles = helpArticleMapper.selectList(query);
        
        return articles.stream()
                .map(article -> {
                    HelpArticleResponse.RelatedArticle relatedArticle = new HelpArticleResponse.RelatedArticle();
                    relatedArticle.setId(article.getId());
                    relatedArticle.setTitle(article.getTitle());
                    relatedArticle.setUrl("/help/article/" + article.getId());
                    return relatedArticle;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换为文章信息DTO
     *
     * @param article 文章实体
     * @return 文章信息DTO
     */
    private HelpDocumentResponse.ArticleInfo convertToArticleInfo(HelpArticle article) {
        HelpDocumentResponse.ArticleInfo articleInfo = new HelpDocumentResponse.ArticleInfo();
        articleInfo.setId(article.getId());
        articleInfo.setTitle(article.getTitle());
        articleInfo.setSummary(article.getSummary());
        articleInfo.setUrl("/help/article/" + article.getId());
        articleInfo.setViewCount(article.getViewCount());
        articleInfo.setHelpfulCount(article.getHelpfulCount());
        articleInfo.setUpdateTime(article.getUpdateTime());
        return articleInfo;
    }
} 