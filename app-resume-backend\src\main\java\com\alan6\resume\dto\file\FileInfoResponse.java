package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文件信息响应DTO
 * 
 * 主要功能：
 * 1. 返回文件的完整详细信息
 * 2. 包含MinIO特有的字段信息
 * 3. 提供文件管理所需的所有数据
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "文件信息响应", description = "文件详细信息")
public class FileInfoResponse {

    /**
     * 文件ID
     */
    @Schema(description = "文件ID", example = "F20241222123456789")
    private String fileId;

    /**
     * 文件名
     */
    @Schema(description = "文件名", example = "avatar_20241222123456.jpg")
    private String fileName;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名", example = "我的头像.jpg")
    private String originalName;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径", example = "avatar/2024/12/22/avatar_20241222123456.jpg")
    private String filePath;

    /**
     * 文件URL
     */
    @Schema(description = "文件URL", example = "https://minio.example.com/avatar/2024/12/22/avatar_20241222123456.jpg")
    private String fileUrl;

    /**
     * 存储桶名称
     */
    @Schema(description = "存储桶名称", example = "avatar-bucket")
    private String bucketName;

    /**
     * 对象键
     */
    @Schema(description = "对象键", example = "avatar/2024/12/22/avatar_20241222123456.jpg")
    private String objectKey;

    /**
     * ETag
     */
    @Schema(description = "ETag", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String etag;

    /**
     * 版本ID
     */
    @Schema(description = "版本ID", example = "v1.0.0")
    private String versionId;

    /**
     * 文件大小
     */
    @Schema(description = "文件大小", example = "102400")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型", example = "image/jpeg")
    private String fileType;

    /**
     * 存储类别
     */
    @Schema(description = "存储类别", example = "STANDARD")
    private String storageClass;

    /**
     * 访问策略
     */
    @Schema(description = "访问策略", example = "private")
    private String accessPolicy;

    /**
     * 内容编码
     */
    @Schema(description = "内容编码", example = "gzip")
    private String contentEncoding;

    /**
     * 缓存控制
     */
    @Schema(description = "缓存控制", example = "max-age=3600")
    private String cacheControl;

    /**
     * 文件过期时间
     */
    @Schema(description = "文件过期时间", example = "2025-12-22 15:30:00")
    private LocalDateTime expiresAt;

    /**
     * 加密类型
     */
    @Schema(description = "加密类型", example = "SSE-S3")
    private String encryptionType;

    /**
     * 自定义元数据
     */
    @Schema(description = "自定义元数据")
    private Map<String, Object> metadata;

    /**
     * 上传平台
     */
    @Schema(description = "上传平台", example = "web")
    private String uploadPlatform;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-22 15:30:00")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-12-22 15:30:00")
    private LocalDateTime updateTime;

    /**
     * 格式化文件大小为可读格式
     * 
     * @return 格式化后的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "未知";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 检查文件是否已过期
     * 
     * @return true-已过期，false-未过期
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    /**
     * 检查文件是否为图片类型
     * 
     * @return true-图片文件，false-非图片文件
     */
    public boolean isImageFile() {
        return fileType != null && fileType.startsWith("image/");
    }

    /**
     * 检查文件是否为文档类型
     * 
     * @return true-文档文件，false-非文档文件
     */
    public boolean isDocumentFile() {
        return fileType != null && 
               (fileType.equals("application/pdf") || 
                fileType.equals("application/msword") || 
                fileType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                fileType.equals("text/plain"));
    }

    /**
     * 检查文件是否为公开访问
     * 
     * @return true-公开访问，false-私有访问
     */
    public boolean isPublicAccess() {
        return accessPolicy != null && 
               (accessPolicy.equals("public-read") || accessPolicy.equals("public-read-write"));
    }

    /**
     * 获取文件扩展名
     * 
     * @return 文件扩展名
     */
    public String getFileExtension() {
        if (fileName == null) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }

    /**
     * 获取元数据中的指定键值
     * 
     * @param key 键名
     * @return 键值
     */
    public Object getMetadataValue(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
} 