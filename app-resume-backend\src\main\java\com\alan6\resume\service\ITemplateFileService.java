package com.alan6.resume.service;

import com.alan6.resume.entity.ResumeTemplates;

/**
 * 模板文件服务接口
 * @description 管理模板文件的读取，支持从MinIO和本地文件系统加载
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ITemplateFileService {

    /**
     * 加载模板内容
     * @description 根据模板ID加载Vue模板内容，优先从MinIO加载，失败则从本地加载
     * @param templateId 模板ID
     * @return 模板内容，如果加载失败则返回null
     */
    String loadTemplateContent(Long templateId);

    /**
     * 加载模板内容（通过模板代码）
     * @description 根据模板代码加载Vue模板内容
     * @param templateCode 模板代码
     * @return 模板内容，如果加载失败则返回null
     */
    String loadTemplateContent(String templateCode);

    /**
     * 从MinIO加载模板内容
     * @description 从MinIO对象存储中加载模板文件内容
     * @param template 模板实体对象
     * @return 模板内容，如果加载失败则返回null
     */
    String loadFromMinIO(ResumeTemplates template);

    /**
     * 从本地文件系统加载模板内容
     * @description 从本地文件系统中加载模板文件内容
     * @param template 模板实体对象
     * @return 模板内容，如果加载失败则返回null
     */
    String loadFromLocal(ResumeTemplates template);

    /**
     * 保存模板内容到MinIO
     * @description 将模板内容保存到MinIO对象存储中
     * @param templateId 模板ID
     * @param templateContent 模板内容
     * @return 是否保存成功
     */
    boolean saveToMinIO(Long templateId, String templateContent);

    /**
     * 保存模板内容到本地文件系统
     * @description 将模板内容保存到本地文件系统中
     * @param templateId 模板ID
     * @param templateContent 模板内容
     * @return 是否保存成功
     */
    boolean saveToLocal(Long templateId, String templateContent);

    /**
     * 检查模板文件是否存在于MinIO
     * @description 检查指定模板文件是否存在于MinIO中
     * @param template 模板实体对象
     * @return 是否存在
     */
    boolean existsInMinIO(ResumeTemplates template);

    /**
     * 检查模板文件是否存在于本地
     * @description 检查指定模板文件是否存在于本地文件系统中
     * @param template 模板实体对象
     * @return 是否存在
     */
    boolean existsInLocal(ResumeTemplates template);

    /**
     * 获取模板文件大小
     * @description 获取模板文件的大小（字节）
     * @param templateId 模板ID
     * @return 文件大小，如果获取失败则返回-1
     */
    long getTemplateFileSize(Long templateId);

    /**
     * 计算模板文件哈希值
     * @description 计算模板文件的MD5哈希值，用于验证文件完整性
     * @param templateContent 模板内容
     * @return 哈希值
     */
    String calculateFileHash(String templateContent);

    /**
     * 同步模板文件
     * @description 将模板文件在MinIO和本地之间同步
     * @param templateId 模板ID
     * @param sourceType 源类型（minio或local）
     * @return 是否同步成功
     */
    boolean syncTemplateFile(Long templateId, String sourceType);

    /**
     * 加载JSON内容文件
     * @description 根据模板代码加载对应的JSON默认内容
     * @param templateCode 模板代码
     * @return JSON内容，如果加载失败则返回null
     */
    String loadJsonContent(String templateCode);

    /**
     * 保存Vue文件
     * @description 保存Vue模板文件到MinIO
     * @param templateCode 模板代码
     * @param vueContent Vue文件内容
     * @return 是否保存成功
     */
    boolean saveVueFile(String templateCode, String vueContent);

    /**
     * 保存JSON文件
     * @description 保存JSON默认内容文件到MinIO
     * @param templateCode 模板代码
     * @param jsonContent JSON文件内容
     * @return 是否保存成功
     */
    boolean saveJsonFile(String templateCode, String jsonContent);

    /**
     * 保存HTML预览文件
     * @description 保存HTML预览文件到MinIO
     * @param templateCode 模板代码
     * @param htmlContent HTML文件内容
     * @return 是否保存成功
     */
    boolean saveHtmlFile(String templateCode, String htmlContent);


} 