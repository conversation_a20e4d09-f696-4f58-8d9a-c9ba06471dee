<template>
  <div class="min-h-screen bg-white">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-secondary-100">
      <nav class="max-w-screen-2xl mx-auto px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <NuxtLink to="/" class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center relative">
              <!-- 火花图标 -->
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2c-1.1 0-2 .9-2 2 0 .74.4 1.39 1 1.73v1.77L9 9.5c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2s2 .9 2 2c0 .74-.4 1.39-1 1.73V7.5h1c.55 0 1 .45 1 1s-.45 1-1 1l-2 2v1.77c.6.34 1 .99 1 1.73 0 1.1-.9 2-2 2s-2-.9-2-2c0-.74.4-1.39 1-1.73V11.5l-2-2c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2z"/>
              </svg>
              <!-- 火花效果小圆点 -->
              <div class="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div>
            </div>
            <span class="font-display font-bold text-xl text-secondary-900">火花简历</span>
          </NuxtLink>

          <!-- Desktop Navigation -->
          <div class="hidden md:flex items-center space-x-8">
            <NuxtLink 
              to="/" 
              class="text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"
            >
              首页
            </NuxtLink>
            <NuxtLink 
              to="/my-resumes" 
              class="text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"
            >
              我的简历
            </NuxtLink>
            <NuxtLink 
              to="/templates" 
              class="text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"
            >
              模板库
            </NuxtLink>
            <NuxtLink 
              to="/guide" 
              class="text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"
            >
              简历攻略
            </NuxtLink>
            <NuxtLink 
              to="/tutorial" 
              class="text-secondary-600 hover:text-primary-600 font-medium transition-colors duration-200"
            >
              使用教程
            </NuxtLink>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center">
            <!-- 认证状态初始化中 -->
            <div 
              v-if="!authService.isInitialized.value"
              class="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 bg-secondary-200 text-secondary-500"
            >
              <div class="flex items-center space-x-2">
                <div class="w-4 h-4 border-2 border-secondary-400 border-t-transparent rounded-full animate-spin"></div>
                <span>加载中...</span>
              </div>
            </div>
            
            <!-- 未登录状态 -->
            <button 
              v-else-if="!isLoggedIn"
              @click="showLoginModal = true"
              class="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 bg-primary-600 text-white hover:bg-primary-700 shadow-md hover:shadow-lg"
            >
              登录 | 注册
            </button>
            
            <!-- 已登录状态 -->
            <UserDropdown v-else :user-info="currentUser" />
          </div>
        </div>
      </nav>
    </header>
    
    <!-- 全局消息提示 -->
    <GlobalMessage 
      :type="messageState.type"
      :message="messageState.message"
      :duration="messageState.duration"
      :visible="messageState.visible"
      @close="handleMessageClose"
    />
    
    <!-- Main Content -->
    <main class="relative">
      <slot />
    </main>
    
    <!-- 登录弹窗 -->
    <LoginModal 
      v-model="showLoginModal" 
      @login-success="handleLoginSuccess"
    />
    
    <!-- Footer -->
    <footer class="bg-secondary-900 text-white">
      <div class="max-w-screen-2xl mx-auto px-6 lg:px-8">
        <!-- Main Footer Content -->
        <div class="py-16 grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- Company Info -->
          <div class="lg:col-span-2">
            <div class="flex items-center space-x-3 mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center relative">
                <!-- 火花图标 -->
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2c-1.1 0-2 .9-2 2 0 .74.4 1.39 1 1.73v1.77L9 9.5c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2s2 .9 2 2c0 .74-.4 1.39-1 1.73V7.5h1c.55 0 1 .45 1 1s-.45 1-1 1l-2 2v1.77c.6.34 1 .99 1 1.73 0 1.1-.9 2-2 2s-2-.9-2-2c0-.74.4-1.39 1-1.73V11.5l-2-2c-.55 0-1-.45-1-1s.45-1 1-1h1V6.27c-.6-.34-1-.99-1-1.73 0-1.1.9-2 2-2z"/>
                </svg>
                <!-- 火花效果小圆点 -->
                <div class="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div>
              </div>
              <span class="font-display font-bold text-xl">火花简历</span>
            </div>
            <p class="text-secondary-300 mb-6 max-w-md">
              专业的在线简历制作平台，为求职者提供优质的简历模板和便捷的编辑体验，助力职业发展。
            </p>
            <div class="flex space-x-4">
              <a href="#" class="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </a>
              <a href="#" class="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </a>
              <a href="#" class="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="font-display font-semibold text-lg mb-6 text-white">快速链接</h3>
            <ul class="space-y-4">
              <li>
                <NuxtLink to="/templates" class="text-secondary-300 hover:text-primary-400 transition-colors duration-200">
                  模板中心
                </NuxtLink>
              </li>
              <!--
              <li>
                <NuxtLink to="/help" class="text-secondary-300 hover:text-primary-400 transition-colors duration-200">
                  使用指南
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/pricing" class="text-secondary-300 hover:text-primary-400 transition-colors duration-200">
                  会员定价
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/about" class="text-secondary-300 hover:text-primary-400 transition-colors duration-200">
                  关于我们
                </NuxtLink>
              </li>
              -->
            </ul>
          </div>

          <!-- Support -->
          <div>
            <h3 class="font-display font-semibold text-lg mb-6 text-white">支持服务</h3>
            <ul class="space-y-4">
              <!--
              <li>
                <NuxtLink to="/help/faq" class="text-secondary-300 hover:text-primary-400 transition-colors duration-200">
                  常见问题
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/help/contact" class="text-secondary-300 hover:text-primary-400 transition-colors duration-200">
                  联系我们
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/privacy" class="text-secondary-300 hover:text-primary-400 transition-colors duration-200">
                  隐私政策
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/terms" class="text-secondary-300 hover:text-primary-400 transition-colors duration-200">
                  服务条款
                </NuxtLink>
              </li>
              -->
            </ul>
          </div>
        </div>

        <!-- Bottom Footer -->
        <div class="py-8 border-t border-secondary-800 flex flex-col md:flex-row justify-between items-center">
          <p class="text-secondary-400 text-sm">
            © 2024 火花简历. All rights reserved.
          </p>
          <div class="flex items-center space-x-6 mt-4 md:mt-0">
            <span class="text-secondary-400 text-sm">简体中文</span>
            <div class="flex items-center space-x-2 text-secondary-400 text-sm">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>安全可靠</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import LoginModal from '~/components/LoginModal.vue'
import GlobalMessage from '~/components/common/GlobalMessage.vue'
import UserDropdown from '~/components/common/UserDropdown.vue'
import { useAuthService } from '~/composables/auth/useAuthService'
import { useGlobalMessage } from '~/composables/shared/useGlobalMessage'

// 认证服务
const authService = useAuthService()

// 全局消息服务
const { messageState, hideMessage, showSuccess } = useGlobalMessage()

// 弹窗状态
const showLoginModal = ref(false)

// 计算用户登录状态
const isLoggedIn = computed(() => authService.isLoggedIn.value)
const currentUser = computed(() => authService.currentUser.value)

// 处理登录成功
const handleLoginSuccess = (loginData) => {
  console.log('登录成功:', loginData)
  showLoginModal.value = false
  showSuccess('登录成功！欢迎回来')
}

// 处理消息关闭
const handleMessageClose = () => {
  hideMessage()
}

// 监听全局事件来打开登录弹窗
const handleOpenLoginModal = () => {
  showLoginModal.value = true
}

// 初始化
onMounted(() => {
  // 添加全局事件监听器
  window.addEventListener('open-login-modal', handleOpenLoginModal)
  
  // 初始化认证状态
  authService.initAuth()
})

onUnmounted(() => {
  window.removeEventListener('open-login-modal', handleOpenLoginModal)
})
</script> 