<template>
  <nav class="flex items-center space-x-2 text-sm py-4" aria-label="面包屑导航">
    <div class="flex items-center space-x-2 text-secondary-600">
      <!-- 首页 -->
      <NuxtLink 
        to="/"
        class="hover:text-primary-600 transition-colors duration-200 flex items-center"
      >
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
        首页
      </NuxtLink>

      <!-- 分隔符 -->
      <svg class="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>

      <!-- 简历模板库 -->
      <NuxtLink 
        to="/templates"
        class="hover:text-primary-600 transition-colors duration-200"
      >
        简历模板库
      </NuxtLink>

      <!-- 分隔符 -->
      <svg class="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>

      <!-- 当前模板名称 -->
      <span class="text-secondary-900 font-medium">
        {{ templateDisplayName }}
      </span>
    </div>
  </nav>
</template>

<script setup>
import { defineProps, computed } from 'vue'

// Props
const props = defineProps({
  template: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value === 'object' && (value.name || value.industry || value.style)
    }
  }
})

// 计算模板显示名称：行业-风格
const templateDisplayName = computed(() => {
  const { name, industry, style } = props.template
  
  // 如果有完整名称，直接使用
  if (name) return name
  
  // 否则组合行业和风格
  const industryText = industry || '通用'
  const styleText = style || '经典'
  return `${industryText}-${styleText}`
})
</script> 