<template>
  <div class="pt-16 min-h-screen bg-gradient-to-br from-primary-50 to-orange-50 flex items-center justify-center">
    <div class="max-w-md w-full space-y-8 p-8 text-center">
      <div class="space-y-4">
        <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto">
          <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
        <h2 class="text-2xl font-display font-bold text-secondary-900">正在跳转到登录...</h2>
        <p class="text-secondary-600">如果没有自动跳转，请点击下方按钮</p>
        <button 
          @click="openLoginModal"
          class="btn-primary"
        >
          打开登录
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// 定义页面标题和meta
useSeoMeta({
  title: '登录 - 火花简历',
  description: '登录火花简历，开始制作您的专业简历',
  ogTitle: '登录 - 火花简历',
  ogDescription: '登录火花简历，开始制作您的专业简历'
})

// 页面加载时自动跳转到首页并打开登录弹窗
onMounted(() => {
  // 延迟跳转，给用户看到加载状态
  setTimeout(() => {
    navigateTo('/?showLogin=true')
  }, 1500)
})

// 手动打开登录弹窗
const openLoginModal = () => {
  navigateTo('/?showLogin=true')
}
</script> 