package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 会员套餐表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("membership_packages")
@Schema(name = "MembershipPackages对象", description = "会员套餐表")
public class MembershipPackages implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "套餐ID")
    private Long id;

    @Schema(description = "套餐名称")
    private String packageName;

    @Schema(description = "套餐代码")
    private String packageCode;

    @Schema(description = "套餐描述")
    private String description;

    @Schema(description = "时长类型（1:天,2:月,3:季,4:年,5:永久）")
    private Byte durationType;

    @Schema(description = "时长数值")
    private Integer durationValue;

    @Schema(description = "原价")
    private BigDecimal originalPrice;

    @Schema(description = "现价")
    private BigDecimal currentPrice;

    @Schema(description = "最大简历数量（-1表示无限制）")
    private Integer maxResumeCount;

    @Schema(description = "每月导出次数（-1表示无限制）")
    private Integer maxExportCount;

    @Schema(description = "是否可用付费模板（0:否,1:是）")
    private Byte premiumTemplates;

    @Schema(description = "是否推荐（0:否,1:是）")
    private Byte isRecommended;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "状态（0:下架,1:上架）")
    private Byte status;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
