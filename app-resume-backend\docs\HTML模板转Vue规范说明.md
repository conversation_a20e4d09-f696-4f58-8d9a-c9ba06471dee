# HTML模板转Vue规范说明

## 概述
本文档定义了HTML简历模板转换为Vue组件的标准规范，确保转换过程的准确性和一致性。

## 核心原则
1. **数据绑定规范化**：使用标准化的data属性进行数据绑定
2. **模块化结构**：按简历模块进行组织和标识
3. **样式兼容性**：确保CSS样式在Vue组件中正常工作
4. **交互功能保持**：保持原有的交互功能和用户体验

## 必需的Data属性规范

### 1. 模块标识属性
```html
<!-- 模块容器标识 -->
<div data-module="basic_info">
  <!-- 基本信息模块内容 -->
</div>

<div data-module="educations">
  <!-- 教育经历模块内容 -->
</div>
```

**支持的模块ID列表：**
- `basic_info` - 基本信息
- `educations` - 教育经历
- `work_experiences` - 工作经历
- `projects` - 项目经历
- `skills` - 技能特长
- `internships` - 实习经历
- `trainings` - 培训经历
- `volunteer_experiences` - 志愿经历
- `research_experiences` - 科研经历
- `publications` - 发表作品
- `awards` - 获奖情况
- `certificates` - 证书资质
- `languages` - 语言能力
- `hobbies` - 兴趣爱好
- `portfolios` - 作品集
- `cover_letters` - 求职信
- `self_evaluations` - 自我评价
- `custom` - 自定义模块

### 2. 字段绑定属性
```html
<!-- 单个字段绑定 -->
<span data-field="name">张三</span>
<span data-field="email"><EMAIL></span>

<!-- 数组字段绑定（需要指定索引和子字段） -->
<div data-field="educations[0].school">北京大学</div>
<div data-field="educations[0].major">计算机科学与技术</div>
<div data-field="educations[0].degree">本科</div>
```

**基本信息字段：**
- `name` - 姓名
- `email` - 邮箱
- `phone` - 电话
- `address` - 地址
- `website` - 个人网站
- `linkedin` - LinkedIn
- `github` - GitHub
- `avatar` - 头像URL
- `job_title` - 求职意向
- `summary` - 个人简介

**教育经历字段：**
- `educations[i].school` - 学校名称
- `educations[i].major` - 专业
- `educations[i].degree` - 学历
- `educations[i].start_date` - 开始时间
- `educations[i].end_date` - 结束时间
- `educations[i].gpa` - GPA
- `educations[i].description` - 描述

**工作经历字段：**
- `work_experiences[i].company` - 公司名称
- `work_experiences[i].position` - 职位
- `work_experiences[i].start_date` - 开始时间
- `work_experiences[i].end_date` - 结束时间
- `work_experiences[i].description` - 工作描述
- `work_experiences[i].achievements` - 主要成就

### 3. Vue文本绑定属性
```html
<!-- 直接文本绑定 -->
<span data-vue-text="resumeData.name">张三</span>

<!-- 条件显示 -->
<div data-vue-if="resumeData.email" data-vue-text="resumeData.email"><EMAIL></div>

<!-- 循环渲染 -->
<div data-vue-for="education in resumeData.educations">
  <span data-vue-text="education.school">北京大学</span>
</div>
```

### 4. 样式绑定属性
```html
<!-- CSS变量绑定 -->
<div data-style-var="--primary-color" data-style-value="primaryColor">
  <!-- 内容 -->
</div>

<!-- 动态类绑定 -->
<div data-class-bind="themeClass">
  <!-- 内容 -->
</div>
```

## 必需的HTML结构

### 1. 文档结构
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历模板</title>
    <style>
        /* CSS样式 */
    </style>
</head>
<body>
    <!-- 简历内容 -->
</body>
</html>
```

### 2. 根容器
```html
<div class="resume-container" data-module-root="true">
    <!-- 所有模块内容 -->
</div>
```

### 3. 模块容器
```html
<div data-module="basic_info" class="module-basic-info">
    <!-- 基本信息内容 -->
</div>
```

## 转换规则

### 1. 文本内容转换
```html
<!-- 原HTML -->
<span data-field="name">张三</span>

<!-- 转换后Vue -->
<span>{{ resumeData.name }}</span>
```

### 2. 条件渲染转换
```html
<!-- 原HTML -->
<div data-vue-if="email">邮箱信息</div>

<!-- 转换后Vue -->
<div v-if="resumeData.email">邮箱信息</div>
```

### 3. 循环渲染转换
```html
<!-- 原HTML -->
<div data-vue-for="education in educations">
    <span data-vue-text="education.school">学校</span>
</div>

<!-- 转换后Vue -->
<div v-for="education in resumeData.educations" :key="education.id">
    <span>{{ education.school }}</span>
</div>
```

### 4. 样式变量转换
```html
<!-- 原HTML -->
<div data-style-var="--primary-color">内容</div>

<!-- 转换后Vue -->
<div :style="{ '--primary-color': primaryColor }">内容</div>
```

## 验证规则

### 1. 必需元素检查
- [ ] 包含`<!DOCTYPE html>`声明
- [ ] 包含`<html>`、`<head>`、`<body>`标签
- [ ] 包含`<meta charset="UTF-8">`
- [ ] 包含`<title>`标签
- [ ] 包含根容器`data-module-root="true"`

### 2. 模块完整性检查
- [ ] 至少包含一个`data-module`属性
- [ ] 模块ID必须在支持列表中
- [ ] 每个模块至少包含一个`data-field`属性

### 3. 字段绑定检查
- [ ] `data-field`属性值符合命名规范
- [ ] 数组字段使用正确的索引格式
- [ ] 字段名称在支持列表中

### 4. CSS样式检查
- [ ] 包含`<style>`标签或外部CSS文件
- [ ] CSS选择器不冲突
- [ ] 使用相对单位（rem、em、%）

## 常见错误及修复建议

### 1. 缺少模块标识
**错误：**
```html
<div class="basic-info">
    <span data-field="name">张三</span>
</div>
```

**修复：**
```html
<div class="basic-info" data-module="basic_info">
    <span data-field="name">张三</span>
</div>
```

### 2. 字段名称不规范
**错误：**
```html
<span data-field="userName">张三</span>
```

**修复：**
```html
<span data-field="name">张三</span>
```

### 3. 数组字段格式错误
**错误：**
```html
<span data-field="education.school">北京大学</span>
```

**修复：**
```html
<span data-field="educations[0].school">北京大学</span>
```

### 4. 缺少根容器
**错误：**
```html
<div class="resume">
    <!-- 内容 -->
</div>
```

**修复：**
```html
<div class="resume" data-module-root="true">
    <!-- 内容 -->
</div>
```

## 转换后Vue组件结构

```vue
<template>
  <div class="resume-container">
    <!-- 转换后的HTML内容 -->
  </div>
</template>

<script setup>
import { useResumeTemplateBase } from '~/composables/template/useResumeTemplateBase'

// 使用基础组合式函数
const {
  resumeData,
  primaryColor,
  secondaryColor,
  fontFamily,
  fontSize,
  themeClass,
  // 其他响应式数据和方法
} = useResumeTemplateBase()
</script>

<style scoped>
/* 原HTML中的CSS样式 */
/* 适配Vue组件的样式修改 */
</style>
```

## 注意事项

1. **编码格式**：HTML文件必须使用UTF-8编码
2. **文件命名**：HTML文件名应使用英文和数字，避免特殊字符
3. **图片资源**：图片路径使用相对路径，转换后需要手动调整
4. **JavaScript代码**：原HTML中的JavaScript代码需要手动迁移到Vue组件
5. **响应式设计**：确保样式在不同屏幕尺寸下正常显示
6. **打印兼容**：考虑打印时的样式表现

## 示例模板

参考`/tmp/sample-templates/`目录下的示例模板，了解标准的HTML模板结构和规范用法。 