<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简历 - {{姓名}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', <PERSON>l, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .resume-container {
            max-width: 210mm; /* A4纸宽度 */
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 297mm; /* A4纸高度 */
        }

        /* 头部信息区域 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 20px;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #666;
        }

        .name {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .title {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            font-size: 0.9em;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            min-height: calc(297mm - 200px);
        }

        .left-column {
            width: 35%;
            background: #f8f9fa;
            padding: 30px;
        }

        .right-column {
            width: 65%;
            padding: 30px;
        }

        /* 模块标题 */
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: #764ba2;
        }

        /* 技能模块 */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .skill-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .skill-name {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .skill-level {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        /* 语言能力 */
        .language-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .language-item:last-child {
            border-bottom: none;
        }

        .language-level {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        /* 工作经历 */
        .experience-item {
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 1px solid #eee;
            position: relative;
        }

        .experience-item:last-child {
            border-bottom: none;
        }

        .experience-item::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 5px;
            width: 10px;
            height: 10px;
            background: #667eea;
            border-radius: 50%;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .job-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
        }

        .company-name {
            color: #667eea;
            font-weight: 500;
        }

        .date-range {
            color: #666;
            font-size: 0.9em;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .job-description {
            color: #555;
            margin-top: 10px;
        }

        .job-description ul {
            margin-left: 20px;
            margin-top: 5px;
        }

        .job-description li {
            margin-bottom: 5px;
        }

        /* 教育经历 */
        .education-item {
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .education-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .degree {
            font-weight: bold;
            color: #333;
        }

        .school {
            color: #667eea;
        }

        .education-date {
            color: #666;
            font-size: 0.9em;
        }

        /* 项目经历 */
        .project-item {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #fafafa;
        }

        .project-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .project-role {
            color: #667eea;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .project-tech {
            margin-top: 10px;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 5px;
        }

        .tech-tag {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        /* 获奖情况 */
        .award-item {
            margin-bottom: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #fff5f5 0%, #fff0f0 100%);
            border-left: 4px solid #ff6b6b;
            border-radius: 0 8px 8px 0;
        }

        .award-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .award-org {
            color: #666;
            font-size: 0.9em;
        }

        .award-date {
            color: #999;
            font-size: 0.8em;
            float: right;
        }

        /* 自我评价 */
        .self-evaluation {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-style: italic;
            color: #555;
            line-height: 1.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .resume-container {
                margin: 10px;
                max-width: none;
            }
            
            .main-content {
                flex-direction: column;
            }
            
            .left-column, .right-column {
                width: 100%;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 10px;
            }
            
            .experience-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .education-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
            }
            
            .resume-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
            
            .main-content {
                min-height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 头部信息 -->
        <header class="header">
            <div class="avatar">👤</div>
            <h1 class="name">张三</h1>
            <div class="title">高级前端开发工程师</div>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📱</span>
                    <span>138-0013-8001</span>
                </div>
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📍</span>
                    <span>北京市朝阳区</span>
                </div>
                <div class="contact-item">
                    <span>🎂</span>
                    <span>1990-01-01</span>
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- 左侧栏 -->
            <aside class="left-column">
                <!-- 专业技能 -->
                <section class="skills-section">
                    <h2 class="section-title">专业技能</h2>
                    <div class="skills-grid">
                        <div class="skill-item">
                            <div class="skill-name">JavaScript</div>
                            <div class="skill-level">
                                <div class="skill-progress" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Vue.js</div>
                            <div class="skill-level">
                                <div class="skill-progress" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">React</div>
                            <div class="skill-level">
                                <div class="skill-progress" style="width: 80%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Node.js</div>
                            <div class="skill-level">
                                <div class="skill-progress" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 语言能力 -->
                <section class="languages-section">
                    <h2 class="section-title">语言能力</h2>
                    <div class="language-item">
                        <span>中文</span>
                        <span class="language-level">母语</span>
                    </div>
                    <div class="language-item">
                        <span>英语</span>
                        <span class="language-level">CET-6</span>
                    </div>
                    <div class="language-item">
                        <span>日语</span>
                        <span class="language-level">N2</span>
                    </div>
                </section>

                <!-- 获奖情况 -->
                <section class="awards-section">
                    <h2 class="section-title">获奖情况</h2>
                    <div class="award-item">
                        <div class="award-name">优秀员工奖</div>
                        <div class="award-org">ABC科技有限公司</div>
                        <div class="award-date">2023-12</div>
                    </div>
                    <div class="award-item">
                        <div class="award-name">技术创新奖</div>
                        <div class="award-org">XYZ互联网公司</div>
                        <div class="award-date">2022-06</div>
                    </div>
                </section>

                <!-- 兴趣爱好 -->
                <section class="hobbies-section">
                    <h2 class="section-title">兴趣爱好</h2>
                    <div style="color: #555; line-height: 1.8;">
                        • 编程开发<br>
                        • 阅读技术书籍<br>
                        • 开源项目贡献<br>
                        • 摄影旅行<br>
                        • 健身运动
                    </div>
                </section>
            </aside>

            <!-- 右侧主要内容 -->
            <main class="right-column">
                <!-- 工作经历 -->
                <section class="experience-section">
                    <h2 class="section-title">工作经历</h2>
                    
                    <div class="experience-item">
                        <div class="experience-header">
                            <div>
                                <div class="job-title">高级前端开发工程师</div>
                                <div class="company-name">ABC科技有限公司</div>
                            </div>
                            <div class="date-range">2022-03 至 现在</div>
                        </div>
                        <div class="job-description">
                            <strong>主要职责：</strong>
                            <ul>
                                <li>负责公司核心产品的前端架构设计和开发</li>
                                <li>带领团队完成多个重要项目的技术攻关</li>
                                <li>制定前端开发规范，提升团队开发效率</li>
                                <li>参与产品需求分析，提供技术解决方案</li>
                            </ul>
                        </div>
                    </div>

                    <div class="experience-item">
                        <div class="experience-header">
                            <div>
                                <div class="job-title">前端开发工程师</div>
                                <div class="company-name">XYZ互联网公司</div>
                            </div>
                            <div class="date-range">2020-06 至 2022-02</div>
                        </div>
                        <div class="job-description">
                            <strong>主要职责：</strong>
                            <ul>
                                <li>负责电商平台前端功能开发和维护</li>
                                <li>优化页面性能，提升用户体验</li>
                                <li>与后端团队协作，完成接口对接</li>
                                <li>参与移动端H5页面开发</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- 项目经历 -->
                <section class="projects-section">
                    <h2 class="section-title">项目经历</h2>
                    
                    <div class="project-item">
                        <div class="project-name">企业级管理系统</div>
                        <div class="project-role">项目负责人 | 2023-01 至 2023-08</div>
                        <div class="project-description">
                            基于Vue3 + TypeScript构建的企业级后台管理系统，支持多租户、权限管理、数据可视化等功能。
                            项目采用微前端架构，支持模块化开发和部署。
                        </div>
                        <div class="project-tech">
                            <strong>技术栈：</strong>
                            <div class="tech-tags">
                                <span class="tech-tag">Vue3</span>
                                <span class="tech-tag">TypeScript</span>
                                <span class="tech-tag">Element Plus</span>
                                <span class="tech-tag">Vite</span>
                                <span class="tech-tag">Pinia</span>
                            </div>
                        </div>
                    </div>

                    <div class="project-item">
                        <div class="project-name">移动端商城应用</div>
                        <div class="project-role">核心开发者 | 2022-03 至 2022-10</div>
                        <div class="project-description">
                            基于React Native开发的跨平台移动端商城应用，支持商品浏览、购物车、支付、订单管理等完整电商功能。
                        </div>
                        <div class="project-tech">
                            <strong>技术栈：</strong>
                            <div class="tech-tags">
                                <span class="tech-tag">React Native</span>
                                <span class="tech-tag">Redux</span>
                                <span class="tech-tag">React Navigation</span>
                                <span class="tech-tag">Axios</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 教育经历 -->
                <section class="education-section">
                    <h2 class="section-title">教育经历</h2>
                    
                    <div class="education-item">
                        <div class="education-header">
                            <div>
                                <div class="degree">计算机科学与技术 - 本科</div>
                                <div class="school">北京理工大学</div>
                            </div>
                            <div class="education-date">2016-09 至 2020-06</div>
                        </div>
                        <div class="education-description">
                            <strong>主要课程：</strong>数据结构、算法设计、软件工程、数据库原理、计算机网络、操作系统
                        </div>
                    </div>
                </section>

                <!-- 自我评价 -->
                <section class="self-evaluation-section">
                    <h2 class="section-title">自我评价</h2>
                    <div class="self-evaluation">
                        具有5年前端开发经验，熟练掌握现代前端技术栈，具备良好的代码规范和工程化思维。
                        擅长团队协作，具有较强的学习能力和问题解决能力。对新技术保持敏锐度，
                        能够快速适应项目需求变化。注重用户体验，追求代码质量和性能优化。
                    </div>
                </section>
            </main>
        </div>
    </div>
</body>
</html>