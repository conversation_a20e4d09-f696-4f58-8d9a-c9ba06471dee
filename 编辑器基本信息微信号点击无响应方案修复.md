# 编辑器基本信息微信号点击无响应方案修复

## 问题描述
用户点击基本信息中的社交信息和其他信息字段按钮（如微信号、个人网站等）时，按钮无响应：
- 图标不会从"+"变成"✓"
- 按钮背景色不会变蓝
- 下方不会出现输入框

## 根本原因
`watch` 函数在数据更新后重新运行时，由于字段值为空字符串，错误地将 `activeSocialFields` 重置为空数组。

**问题代码：**
```javascript
// 错误的逻辑：只检查字段是否有值
activeSocialFields.value = socialOptions
  .filter(option => option.key !== 'custom' && newData[option.key])
  .map(option => option.key)
```

## 修复方案
修改 `watch` 函数的条件判断逻辑，不仅检查字段是否有值，还要检查字段是否已经被用户选择。

**修复代码：**
```javascript
// 正确的逻辑：检查字段是否有值或者在activeSocialFields中
activeSocialFields.value = socialOptions
  .filter(option => {
    if (option.key === 'custom') return false
    // 如果字段有值，或者当前已经在activeSocialFields中，则保留
    return (newData[option.key] && newData[option.key].trim() !== '') || 
           activeSocialFields.value.includes(option.key)
  })
  .map(option => option.key)
```

## 修改文件
- `app-resume-web/components/editor/sidebar/forms/BasicInfoForm.vue`
- 同时修复了 `activeOtherFields` 的相同问题

## 解决结果
- 点击按钮正常响应
- 图标和背景色正确切换
- 输入框正常显示和隐藏 