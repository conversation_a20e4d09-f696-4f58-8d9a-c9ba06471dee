package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 签名验证响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "SignatureVerifyResponse", description = "签名验证响应")
public class SignatureVerifyResponse {

    /**
     * 验证结果
     */
    @Schema(description = "验证结果", example = "true")
    private Boolean valid;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式", example = "wechat")
    private String paymentMethod;

    /**
     * 验证时间
     */
    @Schema(description = "验证时间", example = "2024-12-22 15:00:00")
    private LocalDateTime verifyTime;

    /**
     * 验证消息
     */
    @Schema(description = "验证消息", example = "签名验证通过")
    private String message;
} 