import{r,l as Y,k as ee,i as se,c as a,a as e,q as u,F as A,g as U,t as i,m as q,p as b,v as I,s as D,H as te,I as L,o as n}from"./CURHyiUL.js";import{_ as oe}from"./DlAUqK2U.js";const le={class:"template-categories-page"},ae={class:"page-header"},ne={class:"header-content"},ie={class:"header-right"},de={class:"page-content"},re={class:"content-card"},ce={class:"tree-container"},ue={key:0,class:"loading-state"},ve={key:1,class:"empty-state"},pe={key:2,class:"tree-list"},me={class:"tree-node parent-node"},fe={class:"node-content"},be=["onClick"],he={class:"node-name"},_e={class:"node-code"},ge={class:"node-type"},ke={class:"node-count"},ye={class:"node-actions"},we=["onClick"],Te=["onClick"],xe=["onClick"],Ce={key:0,class:"tree-children"},$e={class:"node-content"},Ee={class:"node-name"},Ie={class:"node-code"},De={class:"node-count"},Oe={class:"node-actions"},Se=["onClick"],Ve=["onClick"],Me={class:"modal-header"},Ae={class:"form-group"},Ue={key:0,class:"error-message"},qe={class:"form-group"},ze={key:0,class:"error-message"},Be={class:"form-group"},je={key:0,class:"form-group"},Fe=["value"],Pe={key:1,class:"form-group"},Le={class:"parent-info"},Ne={class:"parent-name"},Ze={class:"parent-type"},Ge={class:"form-group"},He={class:"form-group"},Re={class:"radio-group"},Je={class:"radio-option"},Ke={class:"radio-option"},Qe={class:"form-actions"},We=["disabled"],Xe={class:"modal-body"},Ye={class:"form-actions"},es=["disabled"],ss={__name:"categories",setup(ts){const O=r(!1),h=r([]),_=r(!1),$=r(!1),g=r(!1),E=r(!1),v=r(!1),z=r([]),k=r(null),y=r(new Set),w=r(!1),p=r(null),l=Y({id:null,name:"",code:"",description:"",parentId:0,categoryType:"",sortOrder:0,status:1}),d=r({}),T=r({show:!1,type:"success",text:""}),N=ee(()=>v.value?"编辑分类":w.value&&p.value?`为"${p.value.name}"添加子分类`:"新增父级分类"),B=t=>({style:"设计风格",industry:"适用行业",major:"高校专业"})[t]||t,Z=t=>{y.value.has(t)?y.value.delete(t):y.value.add(t)},S=t=>y.value.has(t),m=(t,s="success")=>{T.value={show:!0,text:t,type:s},setTimeout(()=>{j()},3e3)},j=()=>{T.value.show=!1},x=async()=>{var t;try{O.value=!0;const s=localStorage.getItem("admin_token");if(!s)throw new Error("请先登录");const f=await $fetch("/api/admin/template/category/tree",{method:"GET",headers:{Authorization:`Bearer ${s}`}});if(f.code===200)h.value=f.data||[],z.value=((t=f.data)==null?void 0:t.filter(o=>o.parentId===0))||[],h.value.length>0&&(y.value.clear(),y.value.add(h.value[0].id));else throw new Error(f.msg||"获取分类数据失败")}catch(s){console.error("获取分类数据失败:",s),m(s.message||"获取分类数据失败","error")}finally{O.value=!1}},G=()=>{x()},H=()=>(d.value={},l.name.trim()?l.name.length>50&&(d.value.name="分类名称长度不能超过50个字符"):d.value.name="分类名称不能为空",l.code.trim()?/^[a-zA-Z0-9_-]+$/.test(l.code)?l.code.length>50&&(d.value.code="分类代码长度不能超过50个字符"):d.value.code="分类代码只能包含字母、数字、下划线和连字符":d.value.code="分类代码不能为空",Object.keys(d.value).length===0),C=()=>{Object.assign(l,{id:null,name:"",code:"",description:"",parentId:0,categoryType:"",sortOrder:0,status:1}),d.value={},w.value=!1,p.value=null},V=()=>{_.value=!1,C()},R=()=>{C(),v.value=!1,w.value=!1,l.parentId=0,l.categoryType="general",_.value=!0},J=t=>{C(),w.value=!0,p.value=t,l.parentId=t.id,l.categoryType=t.categoryType,v.value=!1,_.value=!0},F=t=>{C(),Object.assign(l,{id:t.id,name:t.name,code:t.code,description:t.description||"",parentId:t.parentId,categoryType:t.categoryType,sortOrder:t.sortOrder||0,status:t.status}),v.value=!0,w.value=!1,_.value=!0},P=t=>{k.value=t,$.value=!0},M=()=>{$.value=!1,k.value=null},K=async()=>{if(k.value)try{E.value=!0;const t=localStorage.getItem("admin_token"),s=await $fetch(`/api/admin/template/category/${k.value.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}});if(s.code===200)m("删除成功","success"),await x(),$.value=!1,k.value=null;else throw new Error(s.msg||"删除失败")}catch(t){console.error("删除分类失败:",t),m(t.message||"删除失败","error")}finally{E.value=!1}},Q=async()=>{try{g.value=!0;const t=localStorage.getItem("admin_token"),s=await $fetch("/api/admin/template/category/init",{method:"POST",headers:{Authorization:`Bearer ${t}`}});if(s.code===200)m("分类数据初始化成功","success"),await x();else throw new Error(s.msg||"初始化失败")}catch(t){console.error("初始化分类数据失败:",t),m(t.message||"初始化失败","error")}finally{g.value=!1}},W=async()=>{if(H())try{g.value=!0;const t=localStorage.getItem("admin_token"),s=await $fetch("/api/admin/template/category",{method:v.value?"PUT":"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:l});if(s.code===200)m(v.value?"更新成功":"创建成功","success"),_.value=!1,await x(),C();else throw new Error(s.msg||"保存失败")}catch(t){console.error("保存分类失败:",t),m(t.message||"保存失败","error")}finally{g.value=!1}};return se(()=>{x()}),(t,s)=>{var f;return n(),a("div",le,[e("div",ae,[e("div",ne,[s[9]||(s[9]=e("div",{class:"header-left"},[e("h1",{class:"page-title"},"分类管理"),e("p",{class:"page-description"},"管理简历模板分类")],-1)),e("div",ie,[h.value.length===0?(n(),a("button",{key:0,onClick:Q,class:"btn-init"}," 🔧 初始化分类数据 ")):u("",!0),e("button",{onClick:R,class:"btn-primary"}," ➕ 新增分类 ")])])]),e("div",de,[e("div",re,[e("div",{class:"card-header"},[s[10]||(s[10]=e("span",null,"分类树",-1)),e("button",{onClick:G,class:"refresh-btn",title:"刷新"}," 🔄 ")]),e("div",ce,[O.value?(n(),a("div",ue,s[11]||(s[11]=[e("div",{class:"loading-spinner"},"⏳",-1),e("span",null,"加载中...",-1)]))):h.value.length===0?(n(),a("div",ve,s[12]||(s[12]=[e("div",{class:"empty-icon"},"📁",-1),e("h3",null,"暂无分类数据",-1),e("p",null,'点击上方"新增分类"按钮创建第一个分类',-1)]))):(n(),a("div",pe,[(n(!0),a(A,null,U(h.value,o=>(n(),a("div",{key:o.id,class:"tree-parent"},[e("div",me,[e("div",fe,[e("button",{onClick:c=>Z(o.id),class:D(["expand-btn",{expanded:S(o.id)}])},i(S(o.id)?"▼":"▶"),11,be),s[13]||(s[13]=e("span",{class:"node-icon"},"📂",-1)),e("span",he,i(o.name),1),e("span",_e,"["+i(o.code)+"]",1),e("span",ge,i(B(o.categoryType)),1),e("span",ke,"("+i(o.templateCount||0)+"个模板)",1)]),e("div",ye,[e("button",{onClick:c=>J(o),class:"action-btn add-btn",title:"添加子分类"}," ➕ 添加子分类 ",8,we),e("button",{onClick:c=>F(o),class:"action-btn edit-btn",title:"编辑"}," ✏️ 编辑 ",8,Te),e("button",{onClick:c=>P(o),class:"action-btn delete-btn",title:"删除"}," 🗑️ 删除 ",8,xe)])]),o.children&&o.children.length>0&&S(o.id)?(n(),a("div",Ce,[(n(!0),a(A,null,U(o.children,c=>(n(),a("div",{key:c.id,class:"tree-node child-node"},[e("div",$e,[s[14]||(s[14]=e("span",{class:"child-connector"},"└─",-1)),s[15]||(s[15]=e("span",{class:"node-icon"},"📄",-1)),e("span",Ee,i(c.name),1),e("span",Ie,"["+i(c.code)+"]",1),e("span",De,"("+i(c.templateCount||0)+"个模板)",1)]),e("div",Oe,[e("button",{onClick:X=>F(c),class:"action-btn edit-btn",title:"编辑"}," ✏️ 编辑 ",8,Se),e("button",{onClick:X=>P(c),class:"action-btn delete-btn",title:"删除"}," 🗑️ 删除 ",8,Ve)])]))),128))])):u("",!0)]))),128))]))])])]),_.value?(n(),a("div",{key:0,class:"modal-overlay",onClick:V},[e("div",{class:"modal-content",onClick:s[7]||(s[7]=q(()=>{},["stop"]))},[e("div",Me,[e("h3",null,i(N.value),1),e("button",{onClick:V,class:"close-btn"},"✕")]),e("form",{onSubmit:q(W,["prevent"]),class:"modal-form"},[e("div",Ae,[s[16]||(s[16]=e("label",{class:"form-label required"},"分类名称",-1)),b(e("input",{"onUpdate:modelValue":s[0]||(s[0]=o=>l.name=o),type:"text",class:D(["form-input",{error:d.value.name}]),placeholder:"请输入分类名称",required:""},null,2),[[I,l.name]]),d.value.name?(n(),a("span",Ue,i(d.value.name),1)):u("",!0)]),e("div",qe,[s[17]||(s[17]=e("label",{class:"form-label required"},"分类代码",-1)),b(e("input",{"onUpdate:modelValue":s[1]||(s[1]=o=>l.code=o),type:"text",class:D(["form-input",{error:d.value.code}]),placeholder:"请输入分类代码（英文字母、数字、连字符）",pattern:"[a-zA-Z0-9_-]+",required:""},null,2),[[I,l.code]]),d.value.code?(n(),a("span",ze,i(d.value.code),1)):u("",!0)]),e("div",Be,[s[18]||(s[18]=e("label",{class:"form-label"},"分类描述",-1)),b(e("textarea",{"onUpdate:modelValue":s[2]||(s[2]=o=>l.description=o),class:"form-textarea",rows:"3",placeholder:"请输入分类描述"},null,512),[[I,l.description]])]),v.value&&l.parentId!==0?(n(),a("div",je,[s[20]||(s[20]=e("label",{class:"form-label required"},"父级分类",-1)),b(e("select",{"onUpdate:modelValue":s[3]||(s[3]=o=>l.parentId=o),class:"form-select",required:""},[s[19]||(s[19]=e("option",{value:"0"},"顶级分类",-1)),(n(!0),a(A,null,U(z.value,o=>(n(),a("option",{key:o.id,value:o.id},i(o.name),9,Fe))),128))],512),[[te,l.parentId]])])):u("",!0),w.value&&p.value?(n(),a("div",Pe,[s[21]||(s[21]=e("label",{class:"form-label"},"父级分类",-1)),e("div",Le,[e("span",Ne,i(p.value.name),1),e("span",Ze,"["+i(B(p.value.categoryType))+"]",1)])])):u("",!0),e("div",Ge,[s[22]||(s[22]=e("label",{class:"form-label"},"排序权重",-1)),b(e("input",{"onUpdate:modelValue":s[4]||(s[4]=o=>l.sortOrder=o),type:"number",class:"form-input",min:"0",max:"9999",placeholder:"数值越小越靠前"},null,512),[[I,l.sortOrder,void 0,{number:!0}]])]),e("div",He,[s[25]||(s[25]=e("label",{class:"form-label"},"状态",-1)),e("div",Re,[e("label",Je,[b(e("input",{type:"radio",value:1,"onUpdate:modelValue":s[5]||(s[5]=o=>l.status=o)},null,512),[[L,l.status]]),s[23]||(s[23]=e("span",null,"启用",-1))]),e("label",Ke,[b(e("input",{type:"radio",value:0,"onUpdate:modelValue":s[6]||(s[6]=o=>l.status=o)},null,512),[[L,l.status]]),s[24]||(s[24]=e("span",null,"禁用",-1))])])]),e("div",Qe,[e("button",{type:"button",onClick:V,class:"btn-cancel"},"取消"),e("button",{type:"submit",class:"btn-primary",disabled:g.value},i(g.value?"保存中...":v.value?"更新":"创建"),9,We)])],32)])])):u("",!0),$.value?(n(),a("div",{key:1,class:"modal-overlay",onClick:M},[e("div",{class:"modal-content small",onClick:s[8]||(s[8]=q(()=>{},["stop"]))},[e("div",{class:"modal-header"},[s[26]||(s[26]=e("h3",null,"删除确认",-1)),e("button",{onClick:M,class:"close-btn"},"✕")]),e("div",Xe,[e("p",null,'确定要删除分类 "'+i((f=k.value)==null?void 0:f.name)+'" 吗？',1),s[27]||(s[27]=e("p",{class:"warning-text"},"此操作不可恢复，请谨慎操作。",-1))]),e("div",Ye,[e("button",{onClick:M,class:"btn-cancel"},"取消"),e("button",{onClick:K,class:"btn-danger",disabled:E.value},i(E.value?"删除中...":"确定删除"),9,es)])])])):u("",!0),T.value.show?(n(),a("div",{key:2,class:D(["message-toast",T.value.type])},[e("span",null,i(T.value.text),1),e("button",{onClick:j,class:"message-close"},"✕")],2)):u("",!0)])}}},as=oe(ss,[["__scopeId","data-v-e490007b"]]);export{as as default};
