import{r as D,k as y,i as k,c as x,a as l,p as I,v as N,t,d as o,h as M,o as w}from"./CURHyiUL.js";import{u as B}from"./CEpU8TOc.js";import{_ as C}from"./DlAUqK2U.js";import"./x_rD_Ya3.js";const E={class:"simple-test"},O={class:"test-section"},T={class:"test-section"},V={class:"test-section"},J={__name:"simple-test",setup(U){const e=B(),a=D(""),p=D(0),r=y(()=>e.currentResumeData),i=()=>{console.log("🔄 更新数据:",a.value);const n={name:a.value,title:"测试职位",phone:"13800138000",email:"<EMAIL>"};try{e.updateModuleData("basic_info",n),p.value++,console.log("✅ 数据更新完成")}catch(s){console.error("❌ 数据更新失败:",s)}},f=()=>{a.value="测试用户"+Math.random().toString(36).substr(2,3),i()},R=()=>{var n,s,u;console.log("📊 当前Store状态:",{editorStore:e,currentResumeData:e==null?void 0:e.currentResumeData,modules:(n=e==null?void 0:e.currentResumeData)==null?void 0:n.modules,basicInfo:(u=(s=e==null?void 0:e.currentResumeData)==null?void 0:s.modules)==null?void 0:u.basic_info})};return k(async()=>{var n,s,u;console.log("🚀 初始化测试"),console.log("📊 初始化前的状态:",{editorStore:e,currentResumeData:e==null?void 0:e.currentResumeData,modules:(n=e==null?void 0:e.currentResumeData)==null?void 0:n.modules});try{await e.createNewResume(),console.log("✅ 简历创建完成"),console.log("📊 初始化后的状态:",{currentResumeData:e==null?void 0:e.currentResumeData,modules:(s=e==null?void 0:e.currentResumeData)==null?void 0:s.modules}),a.value="张三"}catch(m){console.error("❌ 初始化失败:",m),console.log("📊 初始化失败后的状态:",{currentResumeData:e==null?void 0:e.currentResumeData,modules:(u=e==null?void 0:e.currentResumeData)==null?void 0:u.modules})}}),(n,s)=>{var u,m,d,c,v,g;return w(),x("div",E,[s[12]||(s[12]=l("h1",null,"简单数据流测试",-1)),l("div",O,[s[1]||(s[1]=l("h2",null,"输入测试",-1)),I(l("input",{"onUpdate:modelValue":s[0]||(s[0]=b=>a.value=b),onInput:i,placeholder:"输入姓名"},null,544),[[N,a.value]]),l("p",null,"当前输入: "+t(a.value),1)]),l("div",T,[s[9]||(s[9]=l("h2",null,"Store 状态",-1)),l("p",null,[s[2]||(s[2]=l("strong",null,"EditorStore 是否存在:",-1)),o(" "+t(M(e)?"✅ 是":"❌ 否"),1)]),l("p",null,[s[3]||(s[3]=l("strong",null,"currentResumeData 是否存在:",-1)),o(" "+t(r.value?"✅ 是":"❌ 否"),1)]),l("p",null,[s[4]||(s[4]=l("strong",null,"modules 是否存在:",-1)),o(" "+t((u=r.value)!=null&&u.modules?"✅ 是":"❌ 否"),1)]),l("p",null,[s[5]||(s[5]=l("strong",null,"modules 类型:",-1)),o(" "+t(typeof((m=r.value)==null?void 0:m.modules)),1)]),l("p",null,[s[6]||(s[6]=l("strong",null,"modules 内容:",-1)),o(" "+t(JSON.stringify((d=r.value)==null?void 0:d.modules)),1)]),l("p",null,[s[7]||(s[7]=l("strong",null,"basic_info 模块:",-1)),o(" "+t(JSON.stringify((v=(c=r.value)==null?void 0:c.modules)==null?void 0:v.basic_info)),1)]),l("p",null,[s[8]||(s[8]=l("strong",null,"所有模块:",-1)),o(" "+t(Object.keys(((g=r.value)==null?void 0:g.modules)||{})),1)])]),l("div",V,[s[11]||(s[11]=l("h2",null,"操作测试",-1)),l("button",{onClick:f,class:"test-btn"},"测试更新"),l("button",{onClick:R,class:"test-btn"},"打印Store状态"),l("p",null,[s[10]||(s[10]=l("strong",null,"更新次数:",-1)),o(" "+t(p.value),1)])])])}}},F=C(J,[["__scopeId","data-v-a6e90a9f"]]);export{F as default};
