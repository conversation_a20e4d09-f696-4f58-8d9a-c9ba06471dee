<template>
  <!-- 加载状态 -->
  <div v-if="!isMounted" class="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 flex items-center justify-center">
    <div class="text-white text-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
      <p>加载中...</p>
    </div>
  </div>
  
  <!-- 主要内容 -->
  <div v-else class="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Logo和标题 -->
      <div>
        <div class="mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center mb-6">
          <svg class="h-10 w-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
          </svg>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-white">
          管理员登录
        </h2>
        <p class="mt-2 text-center text-sm text-blue-200">
          请使用管理员账户登录后台管理系统
        </p>
      </div>

      <!-- 登录表单 -->
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-8 shadow-2xl">
          <div class="space-y-6">
            <!-- 手机号输入 -->
            <div>
              <label for="phone" class="block text-sm font-medium text-blue-100 mb-2">
                手机号
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <input
                  id="phone"
                  v-model="loginForm.phone"
                  type="tel"
                  required
                  class="appearance-none relative block w-full px-3 py-3 pl-10 border border-white/20 placeholder-blue-300 text-white bg-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent sm:text-sm backdrop-blur-sm"
                  placeholder="请输入手机号"
                  :disabled="loading"
                />
              </div>
            </div>

            <!-- 密码输入 -->
            <div>
              <label for="password" class="block text-sm font-medium text-blue-100 mb-2">
                密码
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                </div>
                <input
                  id="password"
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  class="appearance-none relative block w-full px-3 py-3 pl-10 pr-10 border border-white/20 placeholder-blue-300 text-white bg-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent sm:text-sm backdrop-blur-sm"
                  placeholder="请输入密码"
                  :disabled="loading"
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  @click="showPassword = !showPassword"
                >
                  <svg v-if="!showPassword" class="h-5 w-5 text-blue-300 hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  <svg v-else class="h-5 w-5 text-blue-300 hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878L12 12m0 0l2.122 2.122M12 12L8.464 8.464"></path>
                  </svg>
                </button>
              </div>
            </div>

            <!-- 记住我 -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <input
                  id="remember-me"
                  v-model="loginForm.rememberMe"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-white/30 rounded bg-white/10"
                  :disabled="loading"
                />
                <label for="remember-me" class="ml-2 block text-sm text-blue-200">
                  记住登录状态
                </label>
              </div>
              <div class="text-sm">
                <a href="#" class="font-medium text-blue-300 hover:text-white transition-colors">
                  忘记密码？
                </a>
              </div>
            </div>
          </div>

          <!-- 登录按钮 -->
          <div class="mt-8">
            <button
              type="submit"
              :disabled="loading"
              class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg v-if="!loading" class="h-5 w-5 text-blue-300 group-hover:text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                </svg>
                <svg v-else class="h-5 w-5 text-blue-300 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </div>

          <!-- 快速登录选项 -->
          <div class="mt-6 pt-6 border-t border-white/20">
            <div class="text-center text-sm text-blue-200 mb-4">
              开发环境快速登录
            </div>
            <div class="flex space-x-3">
              <button
                type="button"
                @click="quickLogin('admin')"
                :disabled="loading"
                class="flex-1 py-2 px-3 text-xs font-medium text-blue-200 bg-white/10 hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50"
              >
                管理员
              </button>
              <button
                type="button"
                @click="quickLogin('super')"
                :disabled="loading"
                class="flex-1 py-2 px-3 text-xs font-medium text-blue-200 bg-white/10 hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50"
              >
                超级管理员
              </button>
            </div>
          </div>
        </div>
      </form>

      <!-- 版权信息 -->
      <div class="text-center">
        <p class="text-xs text-blue-300">
          © 2025 Resume System. All rights reserved.
        </p>
      </div>
    </div>

    <!-- 消息提示 -->
    <div
      v-if="message.show"
      :class="[
        'fixed top-4 right-4 max-w-sm w-full p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300',
        message.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white',
        message.show ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
      ]"
    >
      <div class="flex items-center">
        <svg v-if="message.type === 'success'" class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <svg v-else class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>{{ message.text }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAdminLoginService } from '~/composables/admin/useAdminLoginService'

// 页面配置
definePageMeta({
  layout: false, // 不使用默认布局
  middleware: []  // 不需要认证中间件
})

// 响应式数据
const loginForm = ref({
  phone: '',
  password: '',
  rememberMe: false
})

const loading = ref(false)
const showPassword = ref(false)
const isMounted = ref(false)

const message = ref({
  show: false,
  type: 'success',
  text: ''
})

// 计算属性
const isFormValid = computed(() => {
  return loginForm.value.phone.trim() !== '' && loginForm.value.password.trim() !== ''
})

// 显示消息
const showMessage = (text, type = 'success') => {
  message.value = { show: true, text, type }
  setTimeout(() => {
    message.value.show = false
  }, 3000)
}

// 登录处理
const handleLogin = async () => {
  if (loading.value) return
  
  // 客户端验证表单
  if (!loginForm.value.phone.trim() || !loginForm.value.password.trim()) {
    showMessage('请填写完整的登录信息', 'error')
    return
  }

  loading.value = true

  try {
    // 使用专门的管理员登录服务
    const adminLoginService = useAdminLoginService()
    
    const loginData = {
      phone: loginForm.value.phone,
      password: loginForm.value.password,
      platform: 'web'
    }

    // 调用管理员登录API
    const result = await adminLoginService.adminLogin(loginData)

    if (result.success) {
      showMessage('管理员登录成功，正在跳转...', 'success')

      // 延迟跳转，让用户看到成功消息
      setTimeout(() => {
        navigateTo('/admin')
      }, 1000)
    } else {
      showMessage(result.error || '管理员登录失败', 'error')
    }
  } catch (error) {
    console.error('管理员登录失败:', error)
    
    // 处理不同类型的错误
    let errorMessage = '管理员登录失败，请重试'
    
    if (error.message?.includes('权限不足')) {
      errorMessage = '权限不足，您不是管理员用户'
    } else if (error.message?.includes('用户不存在') || error.message?.includes('密码错误')) {
      errorMessage = '用户名或密码错误'
    } else if (error.message?.includes('500')) {
      errorMessage = '服务器错误，请稍后重试'
    } else if (error.message?.includes('网络')) {
      errorMessage = '网络连接失败，请检查网络'
    }
    
    showMessage(errorMessage, 'error')
  } finally {
    loading.value = false
  }
}

// 快速登录
const quickLogin = async (type) => {
  if (type === 'admin') {
    loginForm.value.phone = '13800138001'
    loginForm.value.password = '123456'
  } else if (type === 'super') {
    loginForm.value.phone = '13800138002'
    loginForm.value.password = '123456'
  }
  
  await handleLogin()
}

// 页面加载时检查是否已登录管理员
onMounted(() => {
  // 标记组件已挂载，避免样式问题
  isMounted.value = true
  
  const adminLoginService = useAdminLoginService()
  if (adminLoginService.isAdminLoggedIn()) {
    // 如果已经登录管理员，直接跳转到管理页面
    navigateTo('/admin')
  }
})
</script>

<style scoped>
/* 自定义样式 */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* 输入框聚焦时的动画效果 */
input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 渐变背景动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-gradient-to-br {
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}
</style> 