package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 会员权益响应DTO
 * 
 * 主要功能：
 * 1. 展示用户当前会员的所有权益
 * 2. 按类别组织权益信息
 * 3. 显示权益的可用性和限制
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "会员权益响应", description = "会员权益详细信息")
public class MembershipBenefitsResponse {

    /**
     * 当前会员信息
     */
    @Schema(description = "当前会员信息")
    private CurrentMember currentMember;

    /**
     * 权益列表
     */
    @Schema(description = "权益列表，按类别分组")
    private List<BenefitCategory> benefits;

    /**
     * 当前会员信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "当前会员信息", description = "用户当前会员状态")
    public static class CurrentMember {

        /**
         * 是否为会员
         */
        @Schema(description = "是否为会员", example = "true")
        private Boolean isMember;

        /**
         * 套餐名称
         */
        @Schema(description = "套餐名称", example = "年度会员")
        private String packageName;

        /**
         * 会员等级
         */
        @Schema(description = "会员等级", example = "premium")
        private String level;
    }

    /**
     * 权益类别内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "权益类别", description = "权益分类信息")
    public static class BenefitCategory {

        /**
         * 类别名称
         */
        @Schema(description = "权益类别名称", example = "简历创建")
        private String category;

        /**
         * 权益项目列表
         */
        @Schema(description = "该类别下的权益项目列表")
        private List<BenefitItem> items;
    }

    /**
     * 权益项目内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "权益项目", description = "具体权益项目信息")
    public static class BenefitItem {

        /**
         * 权益名称
         */
        @Schema(description = "权益名称", example = "简历数量")
        private String name;

        /**
         * 权益描述
         */
        @Schema(description = "权益详细描述", example = "无限制创建简历")
        private String description;

        /**
         * 是否可用
         */
        @Schema(description = "该权益是否可用", example = "true")
        private Boolean available;

        /**
         * 数量限制
         * -1表示无限制
         */
        @Schema(description = "数量限制，-1表示无限制", example = "-1")
        private Integer limit;

        /**
         * 模板数量（仅模板相关权益）
         */
        @Schema(description = "可用模板数量", example = "50")
        private Integer templateCount;

        /**
         * 支持的格式列表（仅导出相关权益）
         */
        @Schema(description = "支持的导出格式", example = "[\"PDF\", \"DOCX\", \"PNG\", \"JPG\"]")
        private List<String> formats;

        /**
         * 响应时间（仅客服相关权益）
         */
        @Schema(description = "客服响应时间", example = "2小时内")
        private String responseTime;
    }

    /**
     * 创建简历创建权益类别
     * 
     * @param maxResumeCount 最大简历数量
     * @param available 是否可用
     * @return 权益类别
     */
    public static BenefitCategory createResumeCategory(Integer maxResumeCount, Boolean available) {
        BenefitItem resumeItem = BenefitItem.builder()
                .name("简历数量")
                .description(maxResumeCount == -1 ? "无限制创建简历" : "最多创建" + maxResumeCount + "份简历")
                .available(available)
                .limit(maxResumeCount)
                .build();

        return BenefitCategory.builder()
                .category("简历创建")
                .items(List.of(resumeItem))
                .build();
    }

    /**
     * 创建模板使用权益类别
     * 
     * @param premiumTemplates 是否可用付费模板
     * @param templateCount 模板数量
     * @return 权益类别
     */
    public static BenefitCategory createTemplateCategory(Boolean premiumTemplates, Integer templateCount) {
        BenefitItem templateItem = BenefitItem.builder()
                .name("付费模板")
                .description(premiumTemplates ? "使用所有付费模板" : "仅限免费模板")
                .available(premiumTemplates)
                .templateCount(templateCount)
                .build();

        return BenefitCategory.builder()
                .category("模板使用")
                .items(List.of(templateItem))
                .build();
    }

    /**
     * 创建导出功能权益类别
     * 
     * @param maxExportCount 最大导出次数
     * @param formats 支持的格式
     * @return 权益类别
     */
    public static BenefitCategory createExportCategory(Integer maxExportCount, List<String> formats) {
        BenefitItem exportCountItem = BenefitItem.builder()
                .name("导出次数")
                .description(maxExportCount == -1 ? "无限制导出次数" : "每月最多导出" + maxExportCount + "次")
                .available(true)
                .limit(maxExportCount)
                .build();

        BenefitItem exportFormatItem = BenefitItem.builder()
                .name("导出格式")
                .description("支持PDF、Word、图片格式")
                .available(true)
                .formats(formats)
                .build();

        return BenefitCategory.builder()
                .category("导出功能")
                .items(List.of(exportCountItem, exportFormatItem))
                .build();
    }

    /**
     * 创建客服支持权益类别
     * 
     * @param prioritySupport 是否有优先支持
     * @param responseTime 响应时间
     * @return 权益类别
     */
    public static BenefitCategory createSupportCategory(Boolean prioritySupport, String responseTime) {
        BenefitItem supportItem = BenefitItem.builder()
                .name("优先支持")
                .description(prioritySupport ? "优先客服响应" : "普通客服支持")
                .available(prioritySupport)
                .responseTime(responseTime)
                .build();

        return BenefitCategory.builder()
                .category("客服支持")
                .items(List.of(supportItem))
                .build();
    }
} 