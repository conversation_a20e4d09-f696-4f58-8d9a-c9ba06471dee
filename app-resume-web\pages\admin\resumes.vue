<template>
  <div class="resumes-page">
    <div class="page-header">
      <h1 class="page-title">简历管理</h1>
      <p class="page-description">管理用户创建的简历</p>
    </div>
    
    <div class="page-content">
      <div class="empty-state">
        <div class="empty-icon">📄</div>
        <h3>简历管理功能</h3>
        <p>此页面将显示和管理用户创建的所有简历</p>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  title: '简历管理 - 管理后台',
  layout: 'admin'
})
</script>

<style scoped>
.resumes-page {
  /* 使用布局提供的padding */
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.page-content {
  background: white;
  border-radius: 8px;
  padding: 48px;
  text-align: center;
}

.empty-state {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: #909399;
  margin: 0 0 24px 0;
}
</style> 