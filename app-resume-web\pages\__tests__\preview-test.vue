<template>
  <div class="preview-test-page">
    <div class="test-header">
      <h1>实时预览测试</h1>
      <p>测试简历编辑器的实时预览功能</p>
    </div>
    
    <div class="test-content">
      <div class="test-section">
        <h2>测试控制台</h2>
        <div class="test-controls">
          <button @click="testBasicInfo" class="test-btn">测试基本信息更新</button>
          <button @click="testSkills" class="test-btn">测试技能更新</button>
          <button @click="testWorkExperience" class="test-btn">测试工作经历更新</button>
          <button @click="checkDataFlow" class="test-btn">检查数据流</button>
        </div>
      </div>
      
      <div class="test-section">
        <h2>当前数据状态</h2>
        <div class="data-display">
          <h3>Store数据:</h3>
          <pre>{{ JSON.stringify(editorStore.resumeData, null, 2) }}</pre>
          
          <h3>模块配置:</h3>
          <pre>{{ JSON.stringify(editorStore.moduleConfigs, null, 2) }}</pre>
        </div>
      </div>
      
      <div class="test-section">
        <h2>预览组件</h2>
        <div class="preview-container">
          <ResumePreview
            :resume-data="editorStore.resumeData"
            :settings="testSettings"
            @module-order-change="handleModuleOrderChange"
            @module-delete="handleModuleDelete"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'
import ResumePreview from '~/components/editor/preview/ResumePreview.vue'

// 状态管理
const editorStore = useEditorStore()

// 测试设置
const testSettings = ref({
  fontFamily: 'system-ui',
  fontSize: '14px',
  textColor: '#333333',
  margins: {
    top: 60,
    bottom: 60,
    side: 60
  },
  spacings: {
    module: 24,
    item: 16,
    line: 1.6
  }
})

// 生命周期
onMounted(async () => {
  console.log('🚀 初始化预览测试页面')
  
  // 创建测试数据
  await editorStore.createNewResume()
  
  // 添加一些测试数据
  editorStore.updateModuleData('basic_info', {
    name: '张三',
    title: '前端开发工程师',
    phone: '13800138000',
    email: '<EMAIL>'
  })
  
  editorStore.updateModuleData('skills', {
    skills: [
      { name: 'JavaScript', level: 90 },
      { name: 'Vue.js', level: 85 },
      { name: 'React', level: 80 }
    ]
  })
  
  console.log('✅ 测试数据已初始化')
})

// 测试方法
const testBasicInfo = () => {
  console.log('🧪 测试基本信息更新')
  
  const newData = {
    name: '李四',
    title: '高级前端开发工程师',
    phone: '13900139000',
    email: '<EMAIL>'
  }
  
  editorStore.updateModuleData('basic_info', newData)
  console.log('✅ 基本信息已更新:', newData)
}

const testSkills = () => {
  console.log('🧪 测试技能更新')
  
  const newSkills = {
    skills: [
      { name: 'TypeScript', level: 85 },
      { name: 'Node.js', level: 80 },
      { name: 'Python', level: 75 },
      { name: 'Docker', level: 70 }
    ]
  }
  
  editorStore.updateModuleData('skills', newSkills)
  console.log('✅ 技能已更新:', newSkills)
}

const testWorkExperience = () => {
  console.log('🧪 测试工作经历更新')
  
  const newWork = [
    {
      company: '阿里巴巴',
      position: '高级前端开发工程师',
      startDate: '2022-01',
      endDate: '至今',
      description: '负责前端架构设计和核心功能开发'
    },
    {
      company: '腾讯',
      position: '前端开发工程师',
      startDate: '2020-06',
      endDate: '2021-12',
      description: '参与多个重要项目的前端开发工作'
    }
  ]
  
  editorStore.updateModuleData('work_experience', newWork)
  console.log('✅ 工作经历已更新:', newWork)
}

const checkDataFlow = () => {
  console.log('🔍 检查数据流')
  console.log('Store数据:', editorStore.resumeData)
  console.log('模块配置:', editorStore.moduleConfigs)
  console.log('当前模块:', editorStore.currentModuleId)
}

// 事件处理
const handleModuleOrderChange = (data) => {
  console.log('📋 模块顺序变更:', data)
}

const handleModuleDelete = (moduleId) => {
  console.log('🗑️ 模块删除:', moduleId)
}
</script>

<style scoped>
.preview-test-page {
  @apply min-h-screen bg-gray-50 p-6;
}

.test-header {
  @apply text-center mb-8;
}

.test-header h1 {
  @apply text-3xl font-bold text-gray-900 mb-2;
}

.test-header p {
  @apply text-gray-600;
}

.test-content {
  @apply max-w-7xl mx-auto space-y-8;
}

.test-section {
  @apply bg-white rounded-lg shadow-sm p-6;
}

.test-section h2 {
  @apply text-xl font-semibold text-gray-900 mb-4;
}

.test-controls {
  @apply flex flex-wrap gap-4;
}

.test-btn {
  @apply px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors;
}

.data-display {
  @apply space-y-4;
}

.data-display h3 {
  @apply text-lg font-medium text-gray-700;
}

.data-display pre {
  @apply bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-60;
}

.preview-container {
  @apply border border-gray-200 rounded-lg overflow-hidden;
  height: 600px;
}
</style> 