# 密码登录接口说明

## 概述

新增了基于密码的用户登录接口，专门用于已注册用户的登录验证。此接口与现有的短信验证码登录接口并行存在，为用户提供更多的登录选择。

## 接口信息

### 基本信息
- **接口路径**: `POST /auth/login`
- **接口描述**: 用户登录-基于密码
- **使用场景**: 已注册用户使用手机号和密码登录

### 请求参数

#### PasswordLoginRequest

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| loginType | Integer | 是 | 登录类型，固定为1 | 1 |
| platform | String | 是 | 登录平台标识 | "web" |
| phone | String | 是 | 手机号 | "13800138000" |
| password | String | 是 | 登录密码 | "password123" |
| deviceId | String | 否 | 设备标识 | "device_001" |

#### 平台标识取值范围
- `web` - Web端
- `wechat_mp` - 微信小程序
- `douyin_mp` - 抖音小程序
- `baidu_mp` - 百度小程序
- `alipay_mp` - 支付宝小程序
- `app_ios` - iOS应用
- `app_android` - Android应用

#### 参数验证规则
- **手机号**: 必须符合中国大陆手机号格式（11位，以1开头）
- **密码**: 长度必须在6-20位之间
- **平台**: 必须是预定义的平台标识之一

### 请求示例

```json
{
  "loginType": 1,
  "platform": "web",
  "phone": "13800138000",
  "password": "password123",
  "deviceId": "web_device_001"
}
```

### 响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "tokenExpireTime": "2024-12-24T15:30:00",
    "userId": 1001,
    "username": "user_13800138000",
    "nickname": "用户8000",
    "avatarUrl": "https://example.com/avatar.jpg",
    "phone": "138****8000",
    "email": "user****@example.com",
    "gender": 1,
    "registerPlatform": "web",
    "status": 1,
    "isPhoneVerified": 1,
    "preferredLanguage": "zh-CN",
    "isNewUser": false,
    "lastLoginTime": "2024-12-17T15:30:00"
  },
  "timestamp": 1703001234567
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "登录失败：用户不存在，请先注册",
  "data": null,
  "timestamp": 1703001234567
}
```

### 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 手机号不能为空 | 请求参数中手机号为空 |
| 400 | 密码不能为空 | 请求参数中密码为空 |
| 400 | 手机号格式不正确 | 手机号不符合格式要求 |
| 400 | 密码长度必须在6-20位之间 | 密码长度不符合要求 |
| 400 | 登录平台格式不正确 | 平台标识不在允许范围内 |
| 400 | 用户不存在，请先注册 | 手机号未注册 |
| 400 | 手机号或密码错误 | 密码验证失败 |
| 400 | 账号状态异常，请联系客服 | 用户账号被禁用 |
| 400 | 账号未设置密码，请使用短信验证码登录 | 用户尚未设置密码 |
| 429 | 密码登录尝试过于频繁，请5分钟后重试 | 触发限流保护 |

## 技术实现

### 核心组件

1. **PasswordLoginRequest**: 密码登录请求DTO，包含完整的参数验证
2. **AuthController.passwordLogin()**: 控制器方法，处理HTTP请求
3. **IAuthService.passwordLogin()**: 服务接口方法
4. **AuthServiceImpl.passwordLogin()**: 服务实现，包含核心业务逻辑

### 业务流程

1. **参数验证**: 验证手机号、密码、平台等参数格式
2. **用户查找**: 根据手机号查找用户记录
3. **状态检查**: 验证用户账号状态是否正常
4. **密码验证**: 验证输入密码与存储密码是否匹配
5. **Token生成**: 生成访问令牌并存储到Redis
6. **登录信息更新**: 更新用户最后登录时间、IP等信息
7. **响应构建**: 构建包含用户信息的登录响应

### 安全特性

1. **限流保护**: 同一手机号5分钟内最多尝试5次
2. **参数验证**: 严格的输入参数格式验证
3. **状态检查**: 验证用户账号状态，防止被禁用账号登录
4. **Token管理**: 支持多设备登录或单设备登录模式
5. **日志记录**: 完整的业务日志和性能监控

### 与短信登录的区别

| 特性 | 密码登录 | 短信验证码登录 |
|------|----------|----------------|
| 接口路径 | `/auth/login` | `/auth/login-sms` |
| 验证方式 | 密码验证 | 短信验证码验证 |
| 用户创建 | 不会创建新用户 | 会自动创建新用户 |
| 使用场景 | 已注册用户 | 新用户注册+老用户登录 |
| 安全级别 | 中等（依赖密码强度） | 较高（手机号实名验证） |

## 注意事项

1. **密码安全**: 当前实现使用简单的字符串比较，生产环境建议使用BCrypt等加密算法
2. **用户注册**: 此接口不会创建新用户，用户必须先通过其他方式注册
3. **密码设置**: 用户必须先设置密码才能使用此接口登录
4. **错误处理**: 建议前端根据不同错误码提供相应的用户提示
5. **限流策略**: 注意限流配置，避免影响正常用户使用

## 后续优化建议

1. **密码加密**: 集成BCrypt密码加密算法
2. **密码策略**: 增加密码复杂度要求和定期更换提醒
3. **安全日志**: 增加登录失败次数统计和异常登录检测
4. **多因子认证**: 考虑增加二次验证机制
5. **密码重置**: 提供忘记密码的重置功能 