import{D as h}from"./iG63TLyk.js";import{e as y,r as v,i as _,c as i,a as t,b as C,s as w,t as r,d as x,q as k,F as $,g as V,o as u}from"./CURHyiUL.js";import{_ as D}from"./DlAUqK2U.js";import"./D7AOVZt6.js";const U={class:"download-test-page"},z={class:"container mx-auto px-4 py-8"},B={class:"max-w-4xl mx-auto"},L={class:"bg-white rounded-xl shadow-lg p-8 mb-8"},j={class:"flex justify-center"},H={class:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 mb-8"},M={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},N={class:"bg-white rounded-lg p-4"},S={class:"bg-white rounded-lg p-4"},T={class:"text-lg font-medium"},E={class:"bg-white rounded-xl shadow-lg p-8 mb-8"},R={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},F=["disabled"],O=["disabled"],P=["disabled"],q={class:"bg-gray-900 rounded-xl p-6"},I={class:"bg-black rounded-lg p-4 h-48 overflow-y-auto"},W={key:0,class:"text-gray-500 text-center py-8"},A={class:"text-gray-400"},G={__name:"download-test",setup(J){y({title:"下载功能测试 - 火花简历",description:"测试简历下载功能"});const a=v(!1),l=v("准备就绪"),n=v([]),d=(o,e="info")=>{const s=new Date().toLocaleTimeString();n.value.unshift({timestamp:s,message:o,type:e}),n.value.length>50&&n.value.pop()},f=()=>l.value.includes("失败")?"text-red-500":l.value.includes("成功")||l.value.includes("完成")?"text-green-500":l.value.includes("正在")?"text-blue-500":"text-gray-500",m=async o=>{d(`用户选择下载格式: ${o.toUpperCase()}`,"info");try{if(a.value=!0,l.value=`正在生成${o.toUpperCase()}文档...`,d(`[演示模式] 开始生成${o.toUpperCase()}文档`,"info"),await new Promise(s=>setTimeout(s,2e3)),Math.random()>.2){l.value="下载完成",d(`${o.toUpperCase()}文档生成成功`,"success");const s=`简历_测试_${o}.${o==="image"?"png":o==="word"?"docx":"pdf"}`;d(`[演示模式] 模拟下载文件: ${s}`,"success");const p=new Blob(["这是一个模拟的"+o.toUpperCase()+"文件内容"],{type:"text/plain"}),b=URL.createObjectURL(p),g=document.createElement("a");g.href=b,g.download=s,g.click(),URL.revokeObjectURL(b)}else throw new Error("模拟网络连接超时")}catch(e){l.value="下载失败",d(`下载失败: ${e.message}`,"error")}finally{setTimeout(()=>{a.value=!1,l.value="准备就绪"},1500)}},c=o=>{m(o)};return _(()=>{d("下载功能测试页面已加载","success")}),(o,e)=>(u(),i("div",U,[t("div",z,[t("div",B,[e[12]||(e[12]=t("h1",{class:"text-4xl font-bold text-center mb-2"},"下载功能测试",-1)),e[13]||(e[13]=t("p",{class:"text-gray-600 text-center mb-12"},"测试新的下载下拉菜单组件",-1)),t("div",L,[e[3]||(e[3]=t("h2",{class:"text-2xl font-semibold mb-6"},"下载下拉菜单",-1)),t("div",j,[C(h,{"is-exporting":a.value,"export-status":l.value,onDownload:m},null,8,["is-exporting","export-status"])])]),t("div",H,[e[6]||(e[6]=t("h3",{class:"text-lg font-semibold mb-4"},"当前状态",-1)),t("div",M,[t("div",N,[e[4]||(e[4]=t("div",{class:"text-sm text-gray-500"},"导出状态",-1)),t("div",{class:w(["text-lg font-medium",f()])},r(l.value),3)]),t("div",S,[e[5]||(e[5]=t("div",{class:"text-sm text-gray-500"},"是否正在导出",-1)),t("div",T,r(a.value?"是":"否"),1)])])]),t("div",E,[e[10]||(e[10]=t("h3",{class:"text-lg font-semibold mb-4"},"快捷测试",-1)),t("div",R,[t("button",{onClick:e[0]||(e[0]=s=>c("pdf")),disabled:a.value,class:"flex items-center justify-center px-6 py-4 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"},e[7]||(e[7]=[t("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v12H4V4z"})],-1),x(" 测试PDF下载 ")]),8,F),t("button",{onClick:e[1]||(e[1]=s=>c("word")),disabled:a.value,class:"flex items-center justify-center px-6 py-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"},e[8]||(e[8]=[t("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v12H4V4z"})],-1),x(" 测试Word下载 ")]),8,O),t("button",{onClick:e[2]||(e[2]=s=>c("image")),disabled:a.value,class:"flex items-center justify-center px-6 py-4 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"},e[9]||(e[9]=[t("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v12H4V4z"})],-1),x(" 测试图片下载 ")]),8,P)])]),t("div",q,[e[11]||(e[11]=t("h3",{class:"text-lg font-semibold text-white mb-4"},"操作日志",-1)),t("div",I,[n.value.length===0?(u(),i("div",W," 暂无日志... ")):k("",!0),(u(!0),i($,null,V(n.value,(s,p)=>(u(),i("div",{key:p,class:"text-sm mb-1"},[t("span",A,"["+r(s.timestamp)+"]",1),t("span",{class:w(s.type==="error"?"text-red-400":s.type==="success"?"text-green-400":"text-blue-400")},r(s.message),3)]))),128))])])])])]))}},Z=D(G,[["__scopeId","data-v-f500c505"]]);export{Z as default};
