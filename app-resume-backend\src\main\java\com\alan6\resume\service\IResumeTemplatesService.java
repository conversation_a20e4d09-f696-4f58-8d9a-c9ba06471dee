package com.alan6.resume.service;

import com.alan6.resume.dto.template.*;
import com.alan6.resume.entity.ResumeTemplates;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 简历模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IResumeTemplatesService extends IService<ResumeTemplates> {

    /**
     * 获取模板列表（带筛选和分页）
     *
     * @param request 列表请求参数
     * @param userId  当前用户ID（用于判断收藏状态）
     * @return 模板列表响应
     */
    TemplateListResponse getTemplateList(TemplateListRequest request, Long userId);

    /**
     * 管理员获取模板列表（包含所有状态的模板）
     *
     * @param request 列表请求参数
     * @param adminUserId 管理员用户ID
     * @return 模板列表响应
     */
    TemplateListResponse getAdminTemplateList(TemplateListRequest request, Long adminUserId);

    /**
     * 获取模板详情
     *
     * @param templateId 模板ID
     * @param userId     当前用户ID（用于判断收藏状态和使用权限）
     * @return 模板详情响应
     */
    TemplateDetailResponse getTemplateDetail(Long templateId, Long userId);

    /**
     * 使用模板创建简历
     *
     * @param templateId 模板ID
     * @param request    使用模板请求
     * @param userId     当前用户ID
     * @return 使用模板响应
     */
    TemplateUseResponse useTemplate(Long templateId, TemplateUseRequest request, Long userId);

    /**
     * 搜索模板
     *
     * @param request 搜索请求参数
     * @param userId  当前用户ID
     * @return 搜索结果响应
     */
    TemplateListResponse searchTemplates(TemplateSearchRequest request, Long userId);

    /**
     * 推荐模板
     *
     * @param request 推荐请求参数
     * @param userId  当前用户ID
     * @return 推荐模板列表
     */
    List<TemplateResponse> recommendTemplates(TemplateRecommendRequest request, Long userId);

    /**
     * 获取热门模板
     *
     * @param request 热门模板请求参数
     * @param userId  当前用户ID
     * @return 热门模板列表
     */
    List<TemplateResponse> getHotTemplates(TemplateHotRequest request, Long userId);

    /**
     * 增加模板使用次数
     *
     * @param templateId 模板ID
     */
    void incrementUseCount(Long templateId);

    /**
     * 检查用户是否有使用模板的权限
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @return 是否有权限
     */
    boolean checkUserPermission(Long templateId, Long userId);

    /**
     * 模板预览
     *
     * @param templateId 模板ID
     * @param request    预览请求参数
     * @param userId     当前用户ID（可选，用于权限判断）
     * @return 预览响应
     */
    TemplatePreviewResponse previewTemplate(Long templateId, TemplatePreviewRequest request, Long userId);

    /**
     * 模板预览（带数据）
     *
     * @param templateId 模板ID
     * @param request    预览请求参数（包含简历数据）
     * @param userId     当前用户ID（可选，用于权限判断）
     * @return 预览响应
     */
    TemplatePreviewResponse previewTemplateWithData(Long templateId, TemplatePreviewWithDataRequest request, Long userId);

    /**
     * 检查模板是否存在
     *
     * @param templateId 模板ID
     * @return 是否存在
     */
    boolean existsById(Long templateId);

    /**
     * 删除模板（软删除）
     *
     * @param templateId 模板ID
     * @param adminUserId 管理员用户ID
     * @return 是否删除成功
     */
    boolean deleteTemplate(Long templateId, Long adminUserId);

    /**
     * 批量删除模板（软删除）
     *
     * @param templateIds 模板ID列表
     * @param adminUserId 管理员用户ID
     * @return 删除结果统计
     */
    Map<String, Object> batchDeleteTemplates(List<Long> templateIds, Long adminUserId);

}
