package com.alan6.resume.config;

import com.alan6.resume.common.utils.RedisUtils;
import com.alan6.resume.entity.Users;
import com.alan6.resume.service.IUsersService;
import com.alan6.resume.service.IUserRolesService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员权限拦截器
 * 统一检查所有 /admin/** 路径的访问权限
 */
@Component
public class AdminInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IUsersService usersService;

    @Autowired
    private IUserRolesService userRolesService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取请求头中的Token
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            token = request.getHeader("admin-token");
        }
        
        if (token == null || token.isEmpty()) {
            sendUnauthorizedResponse(response, "未提供管理员Token");
            return false;
        }

        // 处理Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7); // 移除"Bearer "前缀
        }

        // 从Redis中获取Token对应的用户信息
        Object userInfoObj = redisUtils.getUserByToken(token);
        if (userInfoObj == null) {
            sendUnauthorizedResponse(response, "管理员Token无效或已过期");
            return false;
        }

        try {
            // 处理从Redis获取的用户信息对象
            Long userId = null;
            
            if (userInfoObj instanceof com.alan6.resume.security.UserPrincipal) {
                // 直接是UserPrincipal对象
                com.alan6.resume.security.UserPrincipal userPrincipal = (com.alan6.resume.security.UserPrincipal) userInfoObj;
                userId = userPrincipal.getUserId();
            } else if (userInfoObj instanceof java.util.Map) {
                // Redis反序列化后可能是Map对象
                java.util.Map<?, ?> userMap = (java.util.Map<?, ?>) userInfoObj;
                Object userIdObj = userMap.get("userId");
                if (userIdObj == null) {
                    userIdObj = userMap.get("id");  // 尝试另一个可能的字段名
                }
                if (userIdObj != null) {
                    if (userIdObj instanceof Number) {
                        userId = ((Number) userIdObj).longValue();
                    } else {
                        userId = Long.parseLong(userIdObj.toString());
                    }
                }
            } else {
                // 尝试通过UserPrincipal的create方法处理
                com.alan6.resume.security.UserPrincipal userPrincipal = com.alan6.resume.security.UserPrincipal.create(userInfoObj);
                userId = userPrincipal.getUserId();
            }
            
            if (userId == null) {
                sendUnauthorizedResponse(response, "无法解析用户ID");
                return false;
            }
            
            Users user = usersService.getById(userId);
            
            if (user == null) {
                sendUnauthorizedResponse(response, "用户不存在");
                return false;
            }

            // 检查用户是否为管理员
            if (!userRolesService.isAdmin(userId)) {
                sendUnauthorizedResponse(response, "权限不足，需要管理员权限");
                return false;
            }

            // 将用户信息存储到请求属性中，供后续使用
            request.setAttribute("currentAdminUser", user);
            return true;

        } catch (NumberFormatException e) {
            sendUnauthorizedResponse(response, "Token格式错误");
            return false;
        }
    }

    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws Exception {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 401);
        result.put("message", message);
        result.put("data", null);
        
        ObjectMapper mapper = new ObjectMapper();
        response.getWriter().write(mapper.writeValueAsString(result));
    }
} 