import{r,i as _,B,c as m,o as u,a as t,b as n,s as f,w as D,q as b,t as y,T as S}from"./CURHyiUL.js";import{I as i}from"./D7AOVZt6.js";import{_ as M}from"./DlAUqK2U.js";const N={class:"download-dropdown-container"},T={class:"dropdown-content"},I=["disabled"],P={class:"item-icon pdf-icon"},V=["disabled"],$={class:"item-icon word-icon"},G=["disabled"],L={class:"item-icon image-icon"},h=["disabled"],q={class:"item-icon email-icon"},z={key:0,class:"export-status"},F={class:"status-icon"},O={class:"status-text"},U={__name:"DownloadDropdown",props:{isExporting:{type:Boolean,default:!1},exportStatus:{type:String,default:"正在导出..."}},emits:["download","send-email"],setup(d,{emit:g}){const v=g,s=r(!1),p=r(null),l=r(null),k=()=>{s.value=!s.value},x=e=>{setTimeout(()=>{var o;(o=l.value)!=null&&o.contains(e.relatedTarget)||(s.value=!1)},150)},C=e=>{e.stopPropagation()},c=e=>{v("download",e),s.value=!1},E=()=>{v("send-email"),s.value=!1},w=e=>{var o,a;!((o=p.value)!=null&&o.contains(e.target))&&!((a=l.value)!=null&&a.contains(e.target))&&(s.value=!1)};return _(()=>{document.addEventListener("click",w)}),B(()=>{document.removeEventListener("click",w)}),(e,o)=>(u(),m("div",N,[t("button",{ref_key:"downloadButton",ref:p,class:f(["download-btn",{"is-active":s.value}]),onClick:k,onBlur:x,title:"下载简历"},[n(i,{name:"download"}),o[3]||(o[3]=t("span",{class:"btn-text"},"下载",-1)),n(i,{name:"chevron-down",class:f(["dropdown-icon",{"is-rotated":s.value}])},null,8,["class"])],34),n(S,{name:"dropdown"},{default:D(()=>[s.value?(u(),m("div",{key:0,ref_key:"dropdownMenu",ref:l,class:"dropdown-menu",onClick:C},[t("div",T,[t("button",{class:"dropdown-item",disabled:d.isExporting,onClick:o[0]||(o[0]=a=>c("pdf"))},[t("div",P,[n(i,{name:"file-pdf"})]),o[4]||(o[4]=t("div",{class:"item-content"},[t("div",{class:"item-title"},"PDF文档"),t("div",{class:"item-description"},"适合打印和正式投递")],-1)),o[5]||(o[5]=t("div",{class:"item-badge"},"推荐",-1))],8,I),t("button",{class:"dropdown-item",disabled:d.isExporting,onClick:o[1]||(o[1]=a=>c("word"))},[t("div",$,[n(i,{name:"file-word"})]),o[6]||(o[6]=t("div",{class:"item-content"},[t("div",{class:"item-title"},"Word文档"),t("div",{class:"item-description"},"方便后续编辑修改")],-1))],8,V),t("button",{class:"dropdown-item",disabled:d.isExporting,onClick:o[2]||(o[2]=a=>c("image"))},[t("div",L,[n(i,{name:"file-image"})]),o[7]||(o[7]=t("div",{class:"item-content"},[t("div",{class:"item-title"},"图片格式"),t("div",{class:"item-description"},"PNG格式，适合在线分享")],-1))],8,G),o[9]||(o[9]=t("div",{class:"dropdown-divider"},null,-1)),t("button",{class:"dropdown-item",disabled:d.isExporting,onClick:E},[t("div",q,[n(i,{name:"email"})]),o[8]||(o[8]=t("div",{class:"item-content"},[t("div",{class:"item-title"},"发送至邮箱"),t("div",{class:"item-description"},"通过邮件分享简历")],-1))],8,h)]),d.isExporting?(u(),m("div",z,[t("div",F,[n(i,{name:"loading",class:"animate-spin"})]),t("div",O,y(d.exportStatus),1)])):b("",!0)],512)):b("",!0)]),_:1})]))}},H=M(U,[["__scopeId","data-v-60b660f4"]]);export{H as D};
