package com.alan6.resume.security.handler;

import com.alan6.resume.core.R;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 访问拒绝处理器
 * 
 * 主要功能：
 * 1. 处理已认证用户访问无权限资源的情况
 * 2. 返回统一格式的JSON错误响应
 * 3. 记录权限不足的日志信息
 * 4. 设置合适的HTTP状态码和响应头
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Component
public class AccessDeniedHandlerImpl implements AccessDeniedHandler {

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理访问拒绝异常
     * 
     * 当已认证用户访问没有权限的资源时，此方法被调用
     * 
     * 处理流程：
     * 1. 记录权限不足的详细信息
     * 2. 设置HTTP响应状态码为403（禁止访问）
     * 3. 设置响应头信息
     * 4. 返回标准格式的JSON错误响应
     * 
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param accessDeniedException 访问拒绝异常
     * @throws IOException IO异常
     * @throws ServletException Servlet异常
     */
    @Override
    public void handle(HttpServletRequest request, 
                      HttpServletResponse response, 
                      AccessDeniedException accessDeniedException) throws IOException, ServletException {
        
        // 获取请求相关信息用于日志记录
        String requestUri = request.getRequestURI();
        String method = request.getMethod();
        String userAgent = request.getHeader("User-Agent");
        String clientIp = getClientIpAddress(request);
        
        // 记录权限不足的日志
        log.warn("用户权限不足 - URI: {}, Method: {}, IP: {}, UserAgent: {}, Exception: {}", 
                requestUri, method, clientIp, userAgent, accessDeniedException.getMessage());
        
        // 设置响应状态码为403（禁止访问）
        response.setStatus(HttpStatus.FORBIDDEN.value());
        
        // 设置响应内容类型为JSON，并指定UTF-8编码
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        
        // 设置跨域相关的响应头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
        
        // 创建错误响应对象
        R<Object> errorResponse = createErrorResponse(accessDeniedException, requestUri);
        
        // 将错误响应对象转换为JSON字符串并写入响应体
        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
        
        log.debug("权限不足响应已发送，状态码: {}", HttpStatus.FORBIDDEN.value());
    }

    /**
     * 创建访问拒绝的错误响应对象
     * 
     * @param accessDeniedException 访问拒绝异常
     * @param requestUri 请求URI
     * @return 统一格式的错误响应对象
     */
    private R<Object> createErrorResponse(AccessDeniedException accessDeniedException, String requestUri) {
        // 确定错误消息
        String errorMessage = determineErrorMessage(requestUri);
        
        // 创建错误响应
        R<Object> errorResponse = R.fail(403, errorMessage);
        
        // 添加调试信息（仅在开发环境下）
        if (isDebugMode()) {
            log.debug("详细权限异常信息 - URI: {}, Exception: {}", requestUri, accessDeniedException.getMessage());
        }
        
        return errorResponse;
    }

    /**
     * 根据请求URI确定错误消息
     * 
     * @param requestUri 请求URI
     * @return 用户友好的错误消息
     */
    private String determineErrorMessage(String requestUri) {
        // 根据不同的接口返回相应的权限提示
        if (requestUri.contains("/admin/")) {
            return "需要管理员权限才能访问该资源";
        } else if (requestUri.contains("/vip/") || requestUri.contains("/premium/")) {
            return "需要会员权限才能使用该功能";
        } else if (requestUri.contains("/user/")) {
            return "权限不足，无法访问该用户资源";
        } else {
            return "权限不足，无法访问该资源";
        }
    }

    /**
     * 获取客户端真实IP地址
     * 
     * 考虑代理服务器和负载均衡器的情况
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // 检查是否通过代理
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况（取第一个非unknown的IP）
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }
        
        return ipAddress;
    }

    /**
     * 判断是否为调试模式
     * 
     * @return true-调试模式，false-生产模式
     */
    private boolean isDebugMode() {
        // 可以通过配置文件或环境变量来控制
        // 这里简单判断日志级别
        return log.isDebugEnabled();
    }
} 