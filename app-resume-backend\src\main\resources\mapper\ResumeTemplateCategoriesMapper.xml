<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alan6.resume.mapper.ResumeTemplateCategoriesMapper">

    <!-- 根据模板ID获取分类ID列表 -->
    <select id="getCategoryIdsByTemplateId" resultType="java.lang.Long">
        SELECT category_id 
        FROM resume_template_categories 
        WHERE template_id = #{templateId} 
          AND is_deleted = 0
        ORDER BY category_id
    </select>

    <!-- 根据分类ID列表获取模板ID列表 -->
    <select id="getTemplateIdsByCategoryIds" resultType="java.lang.Long">
        SELECT DISTINCT template_id 
        FROM resume_template_categories 
        WHERE category_id IN 
        <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
            #{categoryId}
        </foreach>
        AND is_deleted = 0
    </select>

    <!-- 批量删除模板的分类关联 -->
    <delete id="deleteByTemplateId">
        UPDATE resume_template_categories 
        SET is_deleted = 1, update_time = NOW()
        WHERE template_id = #{templateId}
          AND is_deleted = 0
    </delete>

    <!-- 批量插入模板分类关联 -->
    <insert id="batchInsert">
        INSERT INTO resume_template_categories 
        (template_id, category_id, create_time, update_time) 
        VALUES
        <foreach collection="relations" item="relation" separator=",">
            (#{relation.templateId}, #{relation.categoryId}, NOW(), NOW())
        </foreach>
    </insert>

</mapper> 