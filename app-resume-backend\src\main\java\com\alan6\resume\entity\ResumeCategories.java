package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 简历分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
@Setter
@TableName("resume_categories")
@Schema(name = "ResumeCategories对象", description = "简历分类表")
public class ResumeCategories implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "分类代码")
    private String code;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "父分类ID（0为顶级分类）")
    private Long parentId;

    @Schema(description = "分类类型（style:风格,industry:行业,major:专业等）")
    private String categoryType;

    @Schema(description = "分类图标URL")
    private String iconUrl;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "状态（0:禁用,1:启用）")
    private Byte status;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    @TableLogic
    private Byte isDeleted;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 