/**
 * 简化的CSS分页功能
 * @description 使用纯CSS和JavaScript实现A4分页显示
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, onMounted, onUnmounted, nextTick } from 'vue'

export function useSimplePagedJS() {
  // 分页状态
  const isPagedMode = ref(false)
  const isLoading = ref(false)
  const error = ref(null)
  const pageCount = ref(1)

  // DOM 元素引用
  let previewContainer = null
  let resizeObserver = null

  /**
   * 启用CSS分页模式
   * @description 切换到专业的A4分页显示
   */
  const enablePaging = async () => {
    try {
      isLoading.value = true
      error.value = null

      // 等待DOM更新
      await nextTick()

      // 查找预览容器
      previewContainer = document.querySelector('.preview-container')
      if (!previewContainer) {
        throw new Error('找不到预览容器')
      }

      // 添加CSS分页样式类
      previewContainer.classList.add('css-paged-container')
      
      // 立即计算页面数量
      calculatePages()
      
      // 设置响应式监听
      setupResizeObserver()
      
      isPagedMode.value = true
      console.log('✅ CSS分页模式已启用')

    } catch (err) {
      console.error('❌ 启用分页模式失败:', err)
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 禁用CSS分页模式
   * @description 恢复到正常滚动模式
   */
  const disablePaging = async () => {
    try {
      isLoading.value = true
      error.value = null

      // 等待DOM更新
      await nextTick()

      if (previewContainer) {
        // 移除CSS分页样式类
        previewContainer.classList.remove('css-paged-container')
        
        // 清理分页指示器
        clearPageIndicators()
        
        // 停止响应式监听
        if (resizeObserver) {
          resizeObserver.disconnect()
          resizeObserver = null
        }
      }

      isPagedMode.value = false
      pageCount.value = 1
      console.log('✅ CSS分页模式已禁用')

    } catch (err) {
      console.error('❌ 禁用分页模式失败:', err)
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 重新计算分页
   * @description 当内容变化时重新计算页面数量
   */
  const recalculate = async () => {
    if (!isPagedMode.value) return

    try {
      // 等待DOM更新
      await nextTick()
      
      // 重新计算页面
      calculatePages()
      
      console.log('🔄 分页重新计算完成')
    } catch (err) {
      console.error('❌ 重新计算分页失败:', err)
      error.value = err.message
    }
  }

  /**
   * 计算页面数量和创建分页指示器
   * @description 基于A4页面高度计算需要多少页
   */
  const calculatePages = () => {
    if (!previewContainer) return

    try {
      // 清理已存在的分页指示器
      clearPageIndicators()

      // 获取实际内容容器
      const contentContainer = previewContainer.querySelector('.resume-content') || 
                              previewContainer.querySelector('.resume-preview') || 
                              previewContainer.firstElementChild

      if (!contentContainer) {
        console.warn('⚠️ 找不到内容容器')
        return
      }

      // A4页面高度 (约1122px = 29.7cm)
      const A4_HEIGHT = 1122
      
      // 页面间隔 (32px)
      const PAGE_GAP = 32
      
      // 每页实际高度（包含间隔）
      const PAGE_TOTAL_HEIGHT = A4_HEIGHT + PAGE_GAP

      // 获取内容实际高度
      const contentHeight = contentContainer.scrollHeight || contentContainer.offsetHeight
      
      // 计算所需页面数
      const calculatedPageCount = Math.max(1, Math.ceil(contentHeight / A4_HEIGHT))
      pageCount.value = calculatedPageCount

      console.log(`📄 内容高度: ${contentHeight}px, 计算页数: ${calculatedPageCount}`)

      // 创建分页指示器
      createPageIndicators(calculatedPageCount, A4_HEIGHT, PAGE_GAP)

    } catch (err) {
      console.error('❌ 计算页面失败:', err)
      error.value = err.message
    }
  }

  /**
   * 创建分页指示器
   * @param {number} totalPages - 总页数
   * @param {number} pageHeight - 每页高度
   * @param {number} pageGap - 页面间隔
   */
  const createPageIndicators = (totalPages, pageHeight, pageGap) => {
    if (!previewContainer || totalPages <= 1) return

    try {
      const fragment = document.createDocumentFragment()

      // 为每个页面分界创建指示器（从第2页开始）
      for (let page = 2; page <= totalPages; page++) {
        // 计算分页位置（考虑页面间隔和累积偏移）
        const pageBreakPosition = (page - 1) * pageHeight + (page - 2) * pageGap + 30 // 30px是容器的顶部padding

        // 创建分页指示器容器
        const indicator = document.createElement('div')
        indicator.className = 'page-break-indicator'
        indicator.style.top = `${pageBreakPosition}px`
        indicator.setAttribute('data-page', page)

        // 创建分页线
        const breakLine = document.createElement('div')
        breakLine.className = 'page-break-line'
        indicator.appendChild(breakLine)

        // 创建页码指示器
        const pageNumber = document.createElement('div')
        pageNumber.className = 'page-number-indicator'
        pageNumber.textContent = `第 ${page} 页`
        indicator.appendChild(pageNumber)

        fragment.appendChild(indicator)
      }

      // 一次性添加所有指示器
      previewContainer.appendChild(fragment)

      console.log(`✅ 已创建 ${totalPages - 1} 个分页指示器`)

    } catch (err) {
      console.error('❌ 创建分页指示器失败:', err)
    }
  }

  /**
   * 清理分页指示器
   * @description 移除所有分页指示器元素
   */
  const clearPageIndicators = () => {
    if (!previewContainer) return

    try {
      const indicators = previewContainer.querySelectorAll('.page-break-indicator')
      indicators.forEach(indicator => indicator.remove())
      console.log(`🧹 已清理 ${indicators.length} 个分页指示器`)
    } catch (err) {
      console.error('❌ 清理分页指示器失败:', err)
    }
  }

  /**
   * 设置响应式监听
   * @description 监听容器大小变化，自动重新计算分页
   */
  const setupResizeObserver = () => {
    if (!window.ResizeObserver || !previewContainer) return

    try {
      // 创建 ResizeObserver 监听内容变化
      resizeObserver = new ResizeObserver((entries) => {
        // 使用防抖避免频繁计算
        clearTimeout(setupResizeObserver.timer)
        setupResizeObserver.timer = setTimeout(() => {
          calculatePages()
        }, 200)
      })

      // 监听预览容器
      resizeObserver.observe(previewContainer)

      // 如果有内容容器，也监听它
      const contentContainer = previewContainer.querySelector('.resume-content') || 
                              previewContainer.querySelector('.resume-preview') || 
                              previewContainer.firstElementChild

      if (contentContainer && contentContainer !== previewContainer) {
        resizeObserver.observe(contentContainer)
      }

      console.log('👀 响应式监听已设置')

    } catch (err) {
      console.error('❌ 设置响应式监听失败:', err)
    }
  }

  /**
   * 切换分页模式
   * @description 在分页模式和正常模式之间切换
   */
  const togglePaging = async () => {
    if (isPagedMode.value) {
      await disablePaging()
    } else {
      await enablePaging()
    }
  }

  /**
   * 获取分页信息
   * @returns {Object} 分页相关信息
   */
  const getPageInfo = () => {
    return {
      isPagedMode: isPagedMode.value,
      pageCount: pageCount.value,
      isLoading: isLoading.value,
      error: error.value,
      A4_WIDTH: '21cm',
      A4_HEIGHT: '29.7cm',
      A4_HEIGHT_PX: 1122
    }
  }

  /**
   * 组件卸载时的清理
   */
  const cleanup = () => {
    try {
      if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
      }

      if (previewContainer) {
        clearPageIndicators()
        previewContainer.classList.remove('css-paged-container')
      }

      // 清理定时器
      if (setupResizeObserver.timer) {
        clearTimeout(setupResizeObserver.timer)
      }

      console.log('🧹 useSimplePagedJS 清理完成')
    } catch (err) {
      console.error('❌ 清理失败:', err)
    }
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    cleanup()
  })

  // 导出API
  return {
    // 状态
    isPagedMode,
    isLoading,
    error,
    pageCount,

    // 方法
    enablePaging,
    disablePaging,
    recalculate,
    togglePaging,
    getPageInfo,
    cleanup,

    // 工具方法
    calculatePages,
    clearPageIndicators
  }
} 