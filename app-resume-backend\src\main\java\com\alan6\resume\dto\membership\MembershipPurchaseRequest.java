package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 会员购买请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "MembershipPurchaseRequest", description = "会员购买请求")
public class MembershipPurchaseRequest {

    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    @Positive(message = "套餐ID必须为正数")
    @Schema(description = "套餐ID", required = true, example = "1")
    private Long packageId;

    /**
     * 支付方式（wechat:微信支付, alipay:支付宝）
     */
    @NotNull(message = "支付方式不能为空")
    @Schema(description = "支付方式", required = true, example = "wechat", allowableValues = {"wechat", "alipay"})
    private String paymentMethod;
} 