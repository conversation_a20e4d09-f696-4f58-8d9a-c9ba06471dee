<template>
  <div class="education-form">
    <div class="form-header">
      <h4 class="form-title">教育经历</h4>
      <p class="form-desc">添加您的教育背景信息</p>
    </div>
    
    <div v-if="formData.education && formData.education.length > 0" class="education-list">
      <div v-for="(education, index) in formData.education" :key="index" class="education-item">
        <div class="education-header">
          <div class="form-group">
            <label class="form-label">学校</label>
            <input
              v-model="education.school"
              type="text"
              class="form-input"
              placeholder="如：北京大学"
              @input="handleEducationChange"
            />
          </div>
          <div class="form-group">
            <label class="form-label">专业</label>
            <input
              v-model="education.major"
              type="text"
              class="form-input"
              placeholder="如：市场营销"
              @input="handleEducationChange"
            />
          </div>
          <button 
            class="delete-btn"
            @click="removeEducation(index)"
            title="删除教育经历"
          >
            ×
          </button>
        </div>
        
        <div class="education-details">
          <div class="form-group">
            <label class="form-label">就读时间</label>
            <div class="date-range-group">
              <div class="date-picker-wrapper">
                <input
                  v-model="education.startDate"
                  type="text"
                  class="date-input"
                  placeholder="2020-09"
                  readonly
                  @click="showDatePicker(`${index}_start`)"
                />
                <div 
                  v-if="activeDatePicker === `${index}_start`" 
                  class="date-picker-dropdown"
                >
                  <div class="date-picker-header">
                    <button type="button" @click="changeYear(-1)">‹</button>
                    <span>{{ currentYear }}</span>
                    <button type="button" @click="changeYear(1)">›</button>
                  </div>
                  <div class="month-grid">
                    <button
                      v-for="month in 12"
                      :key="month"
                      type="button"
                      class="month-btn"
                      :class="{ active: isSelectedMonth(`${index}_start`, month) }"
                      @click="selectDate(`${index}_start`, currentYear, month)"
                    >
                      {{ month }}月
                    </button>
                  </div>
                </div>
              </div>
              
              <span class="date-separator">至</span>
              
              <div class="date-picker-wrapper">
                <input
                  v-model="education.endDate"
                  type="text"
                  class="date-input"
                  placeholder="2024-06 或 至今"
                  readonly
                  @click="showDatePicker(`${index}_end`)"
                />
                <div 
                  v-if="activeDatePicker === `${index}_end`" 
                  class="date-picker-dropdown"
                >
                  <div class="date-picker-header">
                    <button type="button" @click="changeYear(-1)">‹</button>
                    <span>{{ currentYear }}</span>
                    <button type="button" @click="changeYear(1)">›</button>
                  </div>
                  <div class="month-grid">
                    <button
                      v-for="month in 12"
                      :key="month"
                      type="button"
                      class="month-btn"
                      :class="{ active: isSelectedMonth(`${index}_end`, month) }"
                      @click="selectDate(`${index}_end`, currentYear, month)"
                    >
                      {{ month }}月
                    </button>
                    <button
                      type="button"
                      class="month-btn present-btn"
                      @click="selectPresent(`${index}_end`)"
                    >
                      至今
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">学历</label>
            <div class="degree-group">
              <select
                v-model="education.degree"
                class="degree-select"
                @change="handleEducationChange"
              >
                <option value="">选择学历</option>
                <option value="初中">初中</option>
                <option value="中专">中专</option>
                <option value="高中">高中</option>
                <option value="大专">大专</option>
                <option value="本科">本科</option>
                <option value="硕士">硕士</option>
                <option value="博士">博士</option>
                <option value="MBA">MBA</option>
                <option value="custom">自定义</option>
              </select>
              <input
                v-if="education.degree === 'custom'"
                v-model="education.customDegree"
                type="text"
                class="custom-degree-input"
                placeholder="请输入自定义学历"
                @input="handleEducationChange"
              />
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">教育经历</label>
            <RichTextEditor
              v-model="education.description"
              placeholder="请输入教育经历详情，如：主修课程、获得荣誉、参与项目等..."
              min-height="120px"
              @update:modelValue="handleEducationChange"
            />
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无教育经历信息</p>
      <p class="empty-hint">点击下方按钮添加教育经历</p>
    </div>
    
    <div class="form-actions">
      <button 
        class="add-btn" 
        @click="addEducation"
        :disabled="formData.education.length >= 5"
      >
        <Icon name="plus" size="sm" />
        <span>添加教育经历</span>
      </button>
      <p v-if="formData.education.length >= 5" class="limit-hint">
        最多可添加5个教育经历
      </p>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, ref, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ education: [] })
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = reactive({
  education: []
})

// 防抖定时器
let debounceTimer = null

// 日期选择器状态
const activeDatePicker = ref('')
const currentYear = ref(new Date().getFullYear())

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.education) {
    formData.education = [...newData.education]
  } else {
    formData.education = []
  }
}, { immediate: true, deep: true })

/**
 * 处理教育经历变化 - 实时更新
 */
const handleEducationChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的定时器，300ms后触发更新
  debounceTimer = setTimeout(() => {
    emit('update', { education: [...formData.education] })
  }, 300)
}

/**
 * 添加教育经历
 */
const addEducation = () => {
  if (formData.education.length >= 5) {
    return
  }
  
  formData.education.push({
    school: '',
    major: '',
    startDate: '',
    endDate: '',
    degree: '',
    customDegree: '',
    description: ''
  })
  handleEducationChange()
}

/**
 * 显示日期选择器
 */
const showDatePicker = (pickerId) => {
  activeDatePicker.value = pickerId
  // 根据当前值设置年份
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.education[index].startDate 
    : formData.education[index].endDate
  
  if (currentValue && currentValue !== '至今') {
    const year = parseInt(currentValue.split('-')[0])
    if (year) {
      currentYear.value = year
    }
  }
}

/**
 * 改变年份
 */
const changeYear = (delta) => {
  currentYear.value += delta
}

/**
 * 检查是否为选中的月份
 */
const isSelectedMonth = (pickerId, month) => {
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.education[index].startDate 
    : formData.education[index].endDate
  
  if (!currentValue || currentValue === '至今') return false
  
  const [year, selectedMonth] = currentValue.split('-')
  return parseInt(year) === currentYear.value && parseInt(selectedMonth) === month
}

/**
 * 选择日期
 */
const selectDate = (pickerId, year, month) => {
  const dateStr = `${year}-${String(month).padStart(2, '0')}`
  const [index, type] = pickerId.split('_')
  
  if (type === 'start') {
    formData.education[index].startDate = dateStr
  } else {
    formData.education[index].endDate = dateStr
  }
  
  activeDatePicker.value = ''
  handleEducationChange()
}

/**
 * 选择"至今"
 */
const selectPresent = (pickerId) => {
  const [index] = pickerId.split('_')
  formData.education[index].endDate = '至今'
  activeDatePicker.value = ''
  handleEducationChange()
}

/**
 * 点击外部关闭日期选择器
 */
const handleClickOutside = (event) => {
  if (!event.target.closest('.date-picker-wrapper')) {
    activeDatePicker.value = ''
  }
}

// 挂载时添加全局点击监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 卸载时移除监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

/**
 * 删除教育经历
 */
const removeEducation = (index) => {
  formData.education.splice(index, 1)
  handleEducationChange()
}
</script>

<style scoped>
.education-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.education-list {
  @apply space-y-4 mb-6;
}

.education-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-4;
}

.education-header {
  @apply flex items-start gap-4;
}

.form-group {
  @apply flex-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.delete-btn {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold mt-6;
}

.education-details {
  @apply space-y-4;
}

.date-range-group {
  @apply flex items-center gap-3;
}

.date-picker-wrapper {
  @apply relative flex-1;
}

.date-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer;
}

.date-picker-dropdown {
  @apply absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10;
  min-width: 240px;
}

.date-picker-header {
  @apply flex items-center justify-between px-4 py-3 border-b border-gray-200;
}

.date-picker-header button {
  @apply w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors duration-200;
}

.month-grid {
  @apply grid grid-cols-4 gap-2 p-3;
}

.month-btn {
  @apply px-3 py-2 text-sm text-gray-700 hover:bg-blue-100 hover:text-blue-800 rounded transition-colors duration-200;
  white-space: nowrap;
  min-width: 48px;
}

.month-btn.active {
  @apply bg-blue-500 text-white;
}

.month-btn.present-btn {
  @apply col-span-4 bg-green-100 text-green-800 hover:bg-green-200;
}

.date-separator {
  @apply text-sm text-gray-500 flex-shrink-0;
}

.degree-group {
  @apply flex items-center gap-2;
}

.degree-select {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
}

.custom-degree-input {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-sm text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}

.add-btn:disabled {
  @apply opacity-50 cursor-not-allowed hover:bg-blue-50;
}

.limit-hint {
  @apply text-sm text-gray-500 mt-2;
}
</style> 