package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.membership.MembershipBenefitsResponse;
import com.alan6.resume.dto.membership.MembershipCheckResponse;
import com.alan6.resume.dto.membership.MembershipCurrentResponse;
import com.alan6.resume.dto.membership.MembershipHistoryResponse;
import com.alan6.resume.dto.membership.MembershipPurchaseRequest;
import com.alan6.resume.dto.membership.MembershipPurchaseResponse;
import com.alan6.resume.entity.MembershipPackages;
import com.alan6.resume.entity.Orders;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.entity.UserMemberships;
import com.alan6.resume.mapper.UserMembershipsMapper;
import com.alan6.resume.service.IMembershipPackagesService;
import com.alan6.resume.service.IOrdersService;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.IResumeService;
import com.alan6.resume.service.IUserMembershipsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户会员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
public class UserMembershipsServiceImpl extends ServiceImpl<UserMembershipsMapper, UserMemberships> implements IUserMembershipsService {

    /**
     * 会员套餐服务
     */
    @Autowired
    private IMembershipPackagesService membershipPackagesService;

    /**
     * 简历服务
     */
    @Autowired
    private IResumeService resumeService;

    /**
     * 简历模板服务
     */
    @Autowired
    private IResumeTemplatesService resumeTemplatesService;

    /**
     * 订单服务
     */
    @Autowired
    private IOrdersService ordersService;

    @Override
    public MembershipCurrentResponse getCurrentMembership(Long userId) {
        log.info("获取用户当前会员信息，用户ID：{}", userId);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 获取当前有效会员记录
        UserMemberships currentMembership = getCurrentValidMembership(userId);
        
        if (currentMembership == null) {
            // 非会员用户
            return MembershipCurrentResponse.builder()
                    .isMember(false)
                    .membershipId(null)
                    .packageInfo(null)
                    .startTime(null)
                    .endTime(null)
                    .remainingDays(0L)
                    .status((byte) 0)
                    .privileges(null)
                    .usage(null)
                    .build();
        }
        
        // 获取套餐信息
        MembershipPackages membershipPackage = membershipPackagesService.getById(currentMembership.getPackageId());
        if (membershipPackage == null) {
            throw new BusinessException("会员套餐信息不存在");
        }
        
        // 构建套餐信息
        MembershipCurrentResponse.PackageInfo packageInfo = MembershipCurrentResponse.PackageInfo.builder()
                .id(membershipPackage.getId())
                .packageName(membershipPackage.getPackageName())
                .packageCode(membershipPackage.getPackageCode())
                .build();
        
        // 构建权限信息
        MembershipCurrentResponse.Privileges privileges = MembershipCurrentResponse.Privileges.builder()
                .maxResumeCount(membershipPackage.getMaxResumeCount())
                .maxExportCount(membershipPackage.getMaxExportCount())
                .premiumTemplates(membershipPackage.getPremiumTemplates() == 1)
                .prioritySupport(true) // 会员都有优先支持
                .build();
        
        // 构建使用情况
        MembershipCurrentResponse.Usage usage = MembershipCurrentResponse.Usage.builder()
                .usedResumeCount(currentMembership.getUsedResumeCount())
                .usedExportCount(currentMembership.getUsedExportCount())
                .lastExportResetTime(currentMembership.getLastExportResetTime())
                .build();
        
        // 计算剩余天数
        Long remainingDays = calculateRemainingDays(currentMembership.getEndTime());
        
        return MembershipCurrentResponse.builder()
                .isMember(true)
                .membershipId(currentMembership.getId())
                .packageInfo(packageInfo)
                .startTime(currentMembership.getStartTime())
                .endTime(currentMembership.getEndTime())
                .remainingDays(remainingDays)
                .status(currentMembership.getStatus())
                .privileges(privileges)
                .usage(usage)
                .build();
    }

    @Override
    public MembershipBenefitsResponse getMembershipBenefits(Long userId) {
        log.info("获取用户会员权益，用户ID：{}", userId);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 获取当前会员信息
        UserMemberships currentMembership = getCurrentValidMembership(userId);
        MembershipPackages membershipPackage = null;
        
        // 构建当前会员信息
        MembershipBenefitsResponse.CurrentMember currentMember;
        if (currentMembership != null) {
            membershipPackage = membershipPackagesService.getById(currentMembership.getPackageId());
            currentMember = MembershipBenefitsResponse.CurrentMember.builder()
                    .isMember(true)
                    .packageName(membershipPackage != null ? membershipPackage.getPackageName() : "未知套餐")
                    .level("premium")
                    .build();
        } else {
            currentMember = MembershipBenefitsResponse.CurrentMember.builder()
                    .isMember(false)
                    .packageName("非会员")
                    .level("basic")
                    .build();
        }
        
        // 构建权益列表
        List<MembershipBenefitsResponse.BenefitCategory> benefits = buildBenefitCategories(membershipPackage);
        
        return MembershipBenefitsResponse.builder()
                .currentMember(currentMember)
                .benefits(benefits)
                .build();
    }

    @Override
    public MembershipCheckResponse checkResumeLimit(Long userId) {
        log.info("检查用户简历创建权限，用户ID：{}", userId);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 获取当前会员信息
        UserMemberships currentMembership = getCurrentValidMembership(userId);
        
        // 获取用户当前简历数量
        Integer currentResumeCount = resumeService.getUserResumeCount(userId);
        
        if (currentMembership == null) {
            // 非会员用户，默认限制为3份简历
            Integer maxCount = 3;
            Boolean allowed = currentResumeCount < maxCount;
            
            return MembershipCheckResponse.createResumeCheckResponse(
                allowed, currentResumeCount, maxCount, !allowed
            );
        }
        
        // 会员用户
        MembershipPackages membershipPackage = membershipPackagesService.getById(currentMembership.getPackageId());
        if (membershipPackage == null) {
            throw new BusinessException("会员套餐信息不存在");
        }
        
        Integer maxCount = membershipPackage.getMaxResumeCount();
        Boolean allowed = maxCount == -1 || currentResumeCount < maxCount;
        
        return MembershipCheckResponse.createResumeCheckResponse(
            allowed, currentResumeCount, maxCount, false
        );
    }

    @Override
    public MembershipCheckResponse checkExportLimit(Long userId) {
        log.info("检查用户导出权限，用户ID：{}", userId);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 获取当前会员信息
        UserMemberships currentMembership = getCurrentValidMembership(userId);
        
        if (currentMembership == null) {
            // 非会员用户，默认每月限制5次导出
            Integer maxCount = 5;
            Integer currentCount = 0; // 这里简化处理，实际应该查询用户本月导出次数
            Boolean allowed = currentCount < maxCount;
            LocalDateTime resetTime = getNextMonthFirstDay();
            
            return MembershipCheckResponse.createExportCheckResponse(
                allowed, currentCount, maxCount, resetTime, !allowed
            );
        }
        
        // 会员用户
        MembershipPackages membershipPackage = membershipPackagesService.getById(currentMembership.getPackageId());
        if (membershipPackage == null) {
            throw new BusinessException("会员套餐信息不存在");
        }
        
        // 检查是否需要重置导出次数
        checkAndResetExportCount(currentMembership);
        
        Integer maxCount = membershipPackage.getMaxExportCount();
        Integer currentCount = currentMembership.getUsedExportCount();
        Boolean allowed = maxCount == -1 || currentCount < maxCount;
        LocalDateTime resetTime = getNextMonthFirstDay();
        
        return MembershipCheckResponse.createExportCheckResponse(
            allowed, currentCount, maxCount, resetTime, false
        );
    }

    @Override
    public MembershipCheckResponse checkTemplatePermission(Long userId, Long templateId) {
        log.info("检查用户模板使用权限，用户ID：{}，模板ID：{}", userId, templateId);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        if (templateId == null) {
            throw new BusinessException("模板ID不能为空");
        }
        
        // 获取模板信息
        ResumeTemplates template = resumeTemplatesService.getById(templateId);
        if (template == null) {
            throw new BusinessException("模板不存在");
        }
        
        // 构建模板信息
        MembershipCheckResponse.TemplateInfo templateInfo = MembershipCheckResponse.TemplateInfo.builder()
                .id(template.getId())
                .name(template.getName())
                .isPremium(template.getIsPremium() == 1)
                .build();
        
        // 如果是免费模板，直接允许
        if (template.getIsPremium() == 0) {
            MembershipCheckResponse.CurrentMembership currentMembership = MembershipCheckResponse.CurrentMembership.builder()
                    .isMember(isMember(userId))
                    .packageName("免费模板")
                    .premiumTemplates(true)
                    .build();
            
            return MembershipCheckResponse.createTemplateCheckResponse(
                true, templateInfo, currentMembership, false
            );
        }
        
        // 付费模板，检查会员权限
        UserMemberships userMembership = getCurrentValidMembership(userId);
        if (userMembership == null) {
            // 非会员无法使用付费模板
            MembershipCheckResponse.CurrentMembership currentMembership = MembershipCheckResponse.CurrentMembership.builder()
                    .isMember(false)
                    .packageName("非会员")
                    .premiumTemplates(false)
                    .build();
            
            return MembershipCheckResponse.createTemplateCheckResponse(
                false, templateInfo, currentMembership, true
            );
        }
        
        // 会员用户，检查套餐是否支持付费模板
        MembershipPackages membershipPackage = membershipPackagesService.getById(userMembership.getPackageId());
        if (membershipPackage == null) {
            throw new BusinessException("会员套餐信息不存在");
        }
        
        Boolean allowed = membershipPackage.getPremiumTemplates() == 1;
        MembershipCheckResponse.CurrentMembership currentMembership = MembershipCheckResponse.CurrentMembership.builder()
                .isMember(true)
                .packageName(membershipPackage.getPackageName())
                .premiumTemplates(allowed)
                .build();
        
        return MembershipCheckResponse.createTemplateCheckResponse(
            allowed, templateInfo, currentMembership, !allowed
        );
    }

    @Override
    public UserMemberships getCurrentValidMembership(Long userId) {
        log.info("获取用户当前有效会员记录，用户ID：{}", userId);
        
        if (userId == null) {
            return null;
        }
        
        LambdaQueryWrapper<UserMemberships> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMemberships::getUserId, userId)
                .eq(UserMemberships::getIsDeleted, 0)
                .eq(UserMemberships::getStatus, 1)
                .gt(UserMemberships::getEndTime, LocalDateTime.now())
                .orderByDesc(UserMemberships::getEndTime)
                .last("LIMIT 1");
        
        return getOne(queryWrapper);
    }

    @Override
    public Boolean isMember(Long userId) {
        log.info("检查用户是否为会员，用户ID：{}", userId);
        
        return getCurrentValidMembership(userId) != null;
    }

    @Override
    public Boolean isMembershipExpired(Long userId) {
        log.info("检查用户会员是否过期，用户ID：{}", userId);
        
        UserMemberships membership = getCurrentValidMembership(userId);
        if (membership == null) {
            return true; // 没有会员记录视为过期
        }
        
        return LocalDateTime.now().isAfter(membership.getEndTime());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean incrementResumeCount(Long userId) {
        log.info("增加用户简历使用数量，用户ID：{}", userId);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        UserMemberships membership = getCurrentValidMembership(userId);
        if (membership == null) {
            // 非会员用户，不需要记录使用量
            return true;
        }
        
        // 更新使用量
        membership.setUsedResumeCount(membership.getUsedResumeCount() + 1);
        membership.setUpdateTime(LocalDateTime.now());
        
        return updateById(membership);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean incrementExportCount(Long userId) {
        log.info("增加用户导出使用数量，用户ID：{}", userId);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        UserMemberships membership = getCurrentValidMembership(userId);
        if (membership == null) {
            // 非会员用户，不需要记录使用量
            return true;
        }
        
        // 检查是否需要重置导出次数
        checkAndResetExportCount(membership);
        
        // 更新使用量
        membership.setUsedExportCount(membership.getUsedExportCount() + 1);
        membership.setUpdateTime(LocalDateTime.now());
        
        return updateById(membership);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetExportCount(Long userId) {
        log.info("重置用户导出次数，用户ID：{}", userId);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        UserMemberships membership = getCurrentValidMembership(userId);
        if (membership == null) {
            return true;
        }
        
        membership.setUsedExportCount(0);
        membership.setLastExportResetTime(LocalDateTime.now());
        membership.setUpdateTime(LocalDateTime.now());
        
        return updateById(membership);
    }

    /**
     * 计算剩余天数
     * 
     * @param endTime 结束时间
     * @return 剩余天数
     */
    private Long calculateRemainingDays(LocalDateTime endTime) {
        if (endTime == null) {
            return 0L;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(endTime)) {
            return 0L;
        }
        
        return java.time.temporal.ChronoUnit.DAYS.between(now, endTime);
    }

    /**
     * 获取下月第一天
     * 
     * @return 下月第一天
     */
    private LocalDateTime getNextMonthFirstDay() {
        YearMonth currentMonth = YearMonth.now();
        YearMonth nextMonth = currentMonth.plusMonths(1);
        return nextMonth.atDay(1).atStartOfDay();
    }

    /**
     * 检查并重置导出次数（每月重置）
     * 
     * @param membership 会员记录
     */
    private void checkAndResetExportCount(UserMemberships membership) {
        if (membership.getLastExportResetTime() == null) {
            // 首次使用，设置重置时间为当前月份第一天
            YearMonth currentMonth = YearMonth.now();
            membership.setLastExportResetTime(currentMonth.atDay(1).atStartOfDay());
            membership.setUsedExportCount(0);
            updateById(membership);
            return;
        }
        
        YearMonth lastResetMonth = YearMonth.from(membership.getLastExportResetTime());
        YearMonth currentMonth = YearMonth.now();
        
        // 如果跨月了，重置导出次数
        if (lastResetMonth.isBefore(currentMonth)) {
            membership.setUsedExportCount(0);
            membership.setLastExportResetTime(currentMonth.atDay(1).atStartOfDay());
            updateById(membership);
        }
    }

    /**
     * 构建权益类别列表
     * 
     * @param membershipPackage 会员套餐
     * @return 权益类别列表
     */
    private List<MembershipBenefitsResponse.BenefitCategory> buildBenefitCategories(MembershipPackages membershipPackage) {
        if (membershipPackage == null) {
            // 非会员的基础权益
            return Arrays.asList(
                MembershipBenefitsResponse.createResumeCategory(3, true),
                MembershipBenefitsResponse.createTemplateCategory(false, 10),
                MembershipBenefitsResponse.createExportCategory(5, Arrays.asList("PDF")),
                MembershipBenefitsResponse.createSupportCategory(false, "24小时内")
            );
        }
        
        // 会员权益
        List<String> exportFormats = Arrays.asList("PDF", "DOCX", "PNG", "JPG");
        return Arrays.asList(
            MembershipBenefitsResponse.createResumeCategory(membershipPackage.getMaxResumeCount(), true),
            MembershipBenefitsResponse.createTemplateCategory(membershipPackage.getPremiumTemplates() == 1, 50),
            MembershipBenefitsResponse.createExportCategory(membershipPackage.getMaxExportCount(), exportFormats),
            MembershipBenefitsResponse.createSupportCategory(true, "2小时内")
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MembershipPurchaseResponse purchaseMembership(Long userId, MembershipPurchaseRequest request) {
        log.info("用户购买会员，用户ID：{}，套餐ID：{}", userId, request.getPackageId());
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 验证套餐是否存在且可用
        MembershipPackages membershipPackage = membershipPackagesService.getById(request.getPackageId());
        if (membershipPackage == null) {
            throw new BusinessException("套餐不存在");
        }
        
        if (!membershipPackagesService.isPackageAvailable(request.getPackageId())) {
            throw new BusinessException("套餐已下线");
        }
        
        // 检查用户是否已有有效会员
        UserMemberships existingMembership = getCurrentValidMembership(userId);
        if (existingMembership != null) {
            throw new BusinessException("您已是会员，请使用续费功能");
        }
        
        // 创建订单
        Orders order = ordersService.createMembershipOrder(
            userId, 
            request.getPackageId(), 
            membershipPackage.getPackageName(),
            membershipPackage.getCurrentPrice(),
            request.getPaymentMethod()
        );
        
        // 创建会员记录（待支付状态）
        UserMemberships membership = createMembershipRecord(userId, membershipPackage, order.getId());
        
        // 构建响应
        return buildPurchaseResponse(order, membership, membershipPackage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MembershipPurchaseResponse renewMembership(Long userId, MembershipPurchaseRequest request) {
        log.info("用户续费会员，用户ID：{}，套餐ID：{}", userId, request.getPackageId());
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 验证套餐是否存在且可用
        MembershipPackages membershipPackage = membershipPackagesService.getById(request.getPackageId());
        if (membershipPackage == null) {
            throw new BusinessException("套餐不存在");
        }
        
        if (!membershipPackagesService.isPackageAvailable(request.getPackageId())) {
            throw new BusinessException("套餐已下线");
        }
        
        // 获取当前会员记录（可以是过期的）
        UserMemberships currentMembership = getCurrentMembershipForRenewal(userId);
        
        // 创建订单
        Orders order = ordersService.createMembershipOrder(
            userId, 
            request.getPackageId(), 
            membershipPackage.getPackageName(),
            membershipPackage.getCurrentPrice(),
            request.getPaymentMethod()
        );
        
        // 创建新的会员记录或延长现有记录
        UserMemberships membership = createOrExtendMembershipRecord(userId, membershipPackage, order.getId(), currentMembership);
        
        // 构建响应
        return buildPurchaseResponse(order, membership, membershipPackage);
    }

    /**
     * 创建会员记录
     * 
     * @param userId 用户ID
     * @param membershipPackage 会员套餐
     * @param orderId 订单ID
     * @return 会员记录
     */
    private UserMemberships createMembershipRecord(Long userId, MembershipPackages membershipPackage, Long orderId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = calculateEndTime(now, membershipPackage);
        
        UserMemberships membership = new UserMemberships();
        membership.setUserId(userId);
        membership.setPackageId(membershipPackage.getId());
        membership.setOrderId(orderId);
        membership.setStartTime(now);
        membership.setEndTime(endTime);
        membership.setUsedResumeCount(0);
        membership.setUsedExportCount(0);
        membership.setLastExportResetTime(YearMonth.now().atDay(1).atStartOfDay());
        membership.setStatus((byte) 0); // 0:待激活（等待支付）
        membership.setIsDeleted((byte) 0);
        membership.setCreateTime(now);
        membership.setUpdateTime(now);
        
        save(membership);
        return membership;
    }

    /**
     * 创建或延长会员记录
     * 
     * @param userId 用户ID
     * @param membershipPackage 会员套餐
     * @param orderId 订单ID
     * @param currentMembership 当前会员记录
     * @return 会员记录
     */
    private UserMemberships createOrExtendMembershipRecord(Long userId, MembershipPackages membershipPackage, 
                                                          Long orderId, UserMemberships currentMembership) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime;
        
        // 如果有有效会员，从结束时间开始延长；否则从现在开始
        if (currentMembership != null && currentMembership.getEndTime().isAfter(now)) {
            startTime = currentMembership.getEndTime();
        } else {
            startTime = now;
        }
        
        LocalDateTime endTime = calculateEndTime(startTime, membershipPackage);
        
        UserMemberships membership = new UserMemberships();
        membership.setUserId(userId);
        membership.setPackageId(membershipPackage.getId());
        membership.setOrderId(orderId);
        membership.setStartTime(startTime);
        membership.setEndTime(endTime);
        membership.setUsedResumeCount(0);
        membership.setUsedExportCount(0);
        membership.setLastExportResetTime(YearMonth.now().atDay(1).atStartOfDay());
        membership.setStatus((byte) 0); // 0:待激活（等待支付）
        membership.setIsDeleted((byte) 0);
        membership.setCreateTime(now);
        membership.setUpdateTime(now);
        
        save(membership);
        return membership;
    }

    /**
     * 获取当前会员记录（用于续费，包括过期的）
     * 
     * @param userId 用户ID
     * @return 会员记录
     */
    private UserMemberships getCurrentMembershipForRenewal(Long userId) {
        LambdaQueryWrapper<UserMemberships> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMemberships::getUserId, userId)
                .eq(UserMemberships::getIsDeleted, 0)
                .orderByDesc(UserMemberships::getEndTime)
                .last("LIMIT 1");
        
        return getOne(queryWrapper);
    }

    /**
     * 计算会员结束时间
     * 
     * @param startTime 开始时间
     * @param membershipPackage 会员套餐
     * @return 结束时间
     */
    private LocalDateTime calculateEndTime(LocalDateTime startTime, MembershipPackages membershipPackage) {
        switch (membershipPackage.getDurationType()) {
            case 1: // 天
                return startTime.plusDays(membershipPackage.getDurationValue());
            case 2: // 月
                return startTime.plusMonths(membershipPackage.getDurationValue());
            case 3: // 季
                return startTime.plusMonths(membershipPackage.getDurationValue() * 3);
            case 4: // 年
                return startTime.plusYears(membershipPackage.getDurationValue());
            case 5: // 永久
                return LocalDateTime.of(2099, 12, 31, 23, 59, 59);
            default:
                throw new BusinessException("不支持的会员时长类型");
        }
    }

    /**
     * 构建购买响应
     * 
     * @param order 订单
     * @param membership 会员记录
     * @param membershipPackage 会员套餐
     * @return 购买响应
     */
    private MembershipPurchaseResponse buildPurchaseResponse(Orders order, UserMemberships membership, 
                                                           MembershipPackages membershipPackage) {
        MembershipPurchaseResponse response = new MembershipPurchaseResponse();
        response.setOrderId(order.getOrderNo());
        response.setMembershipId(membership.getId());
        response.setStartTime(membership.getStartTime());
        response.setEndTime(membership.getEndTime());
        response.setTotalAmount(order.getFinalAmount());
        
        // 构建套餐信息
        MembershipPurchaseResponse.PackageInfo packageInfo = new MembershipPurchaseResponse.PackageInfo();
        packageInfo.setId(membershipPackage.getId());
        packageInfo.setPackageName(membershipPackage.getPackageName());
        packageInfo.setPackageCode(membershipPackage.getPackageCode());
        response.setPackageInfo(packageInfo);
        
        // 构建支付信息（模拟）
        MembershipPurchaseResponse.PaymentInfo paymentInfo = new MembershipPurchaseResponse.PaymentInfo();
        paymentInfo.setPaymentMethod(order.getPaymentPlatform());
        paymentInfo.setPaymentUrl(generatePaymentUrl(order));
        paymentInfo.setQrCode(generateQrCode(order));
        response.setPaymentInfo(paymentInfo);
        
        return response;
    }

    /**
     * 生成支付URL（模拟）
     * 
     * @param order 订单
     * @return 支付URL
     */
    private String generatePaymentUrl(Orders order) {
        if ("wechat".equals(order.getPaymentPlatform())) {
            return "weixin://wxpay/bizpayurl?pr=" + order.getOrderNo();
        } else if ("alipay".equals(order.getPaymentPlatform())) {
            return "alipays://platformapi/startapp?saId=10000007&qrcode=" + order.getOrderNo();
        }
        return null;
    }

    /**
     * 生成二维码数据（模拟）
     * 
     * @param order 订单
     * @return 二维码数据
     */
    private String generateQrCode(Orders order) {
        // 这里应该调用真实的二维码生成服务
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
    }

    @Override
    public MembershipHistoryResponse getMembershipHistory(Long userId, Integer page, Integer size) {
        log.info("查询用户会员记录，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);
        
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 设置默认分页参数
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        // 分页查询会员记录
        Page<UserMemberships> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<UserMemberships> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMemberships::getUserId, userId)
                .eq(UserMemberships::getIsDeleted, 0)
                .orderByDesc(UserMemberships::getCreateTime);
        
        IPage<UserMemberships> pageResult = page(pageParam, queryWrapper);
        
        // 转换为响应DTO
        List<MembershipHistoryResponse.MembershipRecord> records = pageResult.getRecords().stream()
                .map(this::convertToMembershipRecord)
                .collect(Collectors.toList());
        
        MembershipHistoryResponse response = new MembershipHistoryResponse();
        response.setRecords(records);
        response.setTotal(pageResult.getTotal());
        
        log.info("查询用户会员记录完成，用户ID：{}，记录数：{}", userId, records.size());
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelMembership(Long userId, Long membershipId) {
        log.info("取消用户会员，用户ID：{}，会员记录ID：{}", userId, membershipId);
        
        if (userId == null || membershipId == null) {
            throw new BusinessException("用户ID和会员记录ID不能为空");
        }
        
        // 获取会员记录
        UserMemberships membership = getById(membershipId);
        if (membership == null) {
            throw new BusinessException("会员记录不存在");
        }
        
        // 验证会员记录属于当前用户
        if (!membership.getUserId().equals(userId)) {
            throw new BusinessException("无权操作此会员记录");
        }
        
        // 检查会员状态
        if (membership.getStatus() == 3) {
            throw new BusinessException("会员已取消");
        }
        
        if (membership.getStatus() == 2) {
            throw new BusinessException("会员已过期，无需取消");
        }
        
        // 更新会员状态为已取消
        membership.setStatus((byte) 3);
        membership.setUpdateTime(LocalDateTime.now());
        
        boolean updated = updateById(membership);
        if (updated) {
            log.info("取消用户会员成功，用户ID：{}，会员记录ID：{}", userId, membershipId);
        } else {
            log.error("取消用户会员失败，用户ID：{}，会员记录ID：{}", userId, membershipId);
        }
        
        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean activateMembership(Long membershipId) {
        log.info("激活会员，会员记录ID：{}", membershipId);
        
        if (membershipId == null) {
            throw new BusinessException("会员记录ID不能为空");
        }
        
        // 获取会员记录
        UserMemberships membership = getById(membershipId);
        if (membership == null) {
            throw new BusinessException("会员记录不存在");
        }
        
        // 检查会员状态
        if (membership.getStatus() != 0) {
            throw new BusinessException("会员状态异常，无法激活");
        }
        
        // 激活会员
        membership.setStatus((byte) 1);
        membership.setUpdateTime(LocalDateTime.now());
        
        boolean updated = updateById(membership);
        if (updated) {
            log.info("激活会员成功，会员记录ID：{}", membershipId);
        } else {
            log.error("激活会员失败，会员记录ID：{}", membershipId);
        }
        
        return updated;
    }

    /**
     * 转换会员记录为响应DTO
     * 
     * @param membership 会员记录
     * @return 会员记录DTO
     */
    private MembershipHistoryResponse.MembershipRecord convertToMembershipRecord(UserMemberships membership) {
        MembershipHistoryResponse.MembershipRecord record = new MembershipHistoryResponse.MembershipRecord();
        record.setId(membership.getId());
        record.setStartTime(membership.getStartTime());
        record.setEndTime(membership.getEndTime());
        record.setStatus(membership.getStatus());
        record.setStatusDesc(getMembershipStatusDesc(membership.getStatus()));
        record.setCreateTime(membership.getCreateTime());
        
        // 获取订单信息
        if (membership.getOrderId() != null) {
            Orders order = ordersService.getById(membership.getOrderId());
            if (order != null) {
                record.setOrderNo(order.getOrderNo());
                record.setAmount(order.getFinalAmount());
            }
        }
        
        // 获取套餐信息
        if (membership.getPackageId() != null) {
            MembershipPackages membershipPackage = membershipPackagesService.getById(membership.getPackageId());
            if (membershipPackage != null) {
                MembershipHistoryResponse.PackageInfo packageInfo = new MembershipHistoryResponse.PackageInfo();
                packageInfo.setId(membershipPackage.getId());
                packageInfo.setPackageName(membershipPackage.getPackageName());
                packageInfo.setPackageCode(membershipPackage.getPackageCode());
                packageInfo.setDurationDesc(getDurationDesc(membershipPackage));
                record.setPackageInfo(packageInfo);
            }
        }
        
        return record;
    }

    /**
     * 获取会员状态描述
     * 
     * @param status 状态码
     * @return 状态描述
     */
    private String getMembershipStatusDesc(Byte status) {
        switch (status) {
            case 0:
                return "待激活";
            case 1:
                return "有效";
            case 2:
                return "已过期";
            case 3:
                return "已取消";
            default:
                return "未知";
        }
    }

    /**
     * 获取套餐时长描述
     * 
     * @param membershipPackage 套餐信息
     * @return 时长描述
     */
    private String getDurationDesc(MembershipPackages membershipPackage) {
        String unit;
        switch (membershipPackage.getDurationType()) {
            case 1:
                unit = "天";
                break;
            case 2:
                unit = "月";
                break;
            case 3:
                unit = "季";
                break;
            case 4:
                unit = "年";
                break;
            case 5:
                return "永久";
            default:
                return "未知";
        }
        return membershipPackage.getDurationValue() + unit;
    }

}
