package com.alan6.resume.service.impl;

import com.alan6.resume.dto.template.DefaultContentRequest;
import com.alan6.resume.dto.template.DefaultContentResponse;
import com.alan6.resume.service.IDefaultContentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 默认内容服务实现类
 * @description 实现默认简历内容的管理和获取功能
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultContentServiceImpl implements IDefaultContentService {

    /**
     * 支持的模板类别
     */
    private static final List<String> SUPPORTED_CATEGORIES = Arrays.asList(
            "modern", "classic", "creative", "simple", "professional"
    );

    /**
     * 支持的行业类型
     */
    private static final List<String> SUPPORTED_INDUSTRIES = Arrays.asList(
            "tech", "finance", "design", "marketing", "education", "healthcare", "consulting"
    );

    /**
     * 支持的职位类型映射
     */
    private static final Map<String, List<String>> INDUSTRY_POSITIONS = new HashMap<>();

    static {
        // 初始化行业-职位映射关系
        INDUSTRY_POSITIONS.put("tech", Arrays.asList("frontend", "backend", "fullstack", "mobile", "devops", "qa"));
        INDUSTRY_POSITIONS.put("finance", Arrays.asList("analyst", "manager", "consultant", "trader", "risk"));
        INDUSTRY_POSITIONS.put("design", Arrays.asList("ui", "ux", "graphic", "product", "visual"));
        INDUSTRY_POSITIONS.put("marketing", Arrays.asList("digital", "content", "social", "brand", "growth"));
        INDUSTRY_POSITIONS.put("education", Arrays.asList("teacher", "researcher", "admin", "counselor"));
        INDUSTRY_POSITIONS.put("healthcare", Arrays.asList("nurse", "doctor", "admin", "technician"));
        INDUSTRY_POSITIONS.put("consulting", Arrays.asList("strategy", "management", "it", "hr", "financial"));
    }

    /**
     * 获取默认简历内容
     * @description 根据优先级获取最匹配的默认内容
     * @param request 默认内容请求参数
     * @return 默认内容响应
     */
    @Override
    public DefaultContentResponse getDefaultContent(DefaultContentRequest request) {
        log.info("获取默认简历内容，请求参数: {}", request);

        try {
            // 参数验证
            validateRequest(request);

            // 按优先级获取默认内容
            DefaultContentResponse response = getContentByPriority(request);

            log.info("成功获取默认内容，来源: {}, 类别: {}", 
                    response.getContentSource(), response.getTemplateCategory());

            return response;

        } catch (Exception e) {
            log.error("获取默认内容失败，请求参数: {}, 错误: {}", request, e.getMessage(), e);
            
            // 返回系统默认内容
            return getSystemDefaultContent(request);
        }
    }

    /**
     * 按优先级获取默认内容
     * @description 实现内容获取的优先级逻辑
     * @param request 请求参数
     * @return 默认内容响应
     */
    private DefaultContentResponse getContentByPriority(DefaultContentRequest request) {
        String category = request.getTemplateCategory();
        String industry = request.getIndustry();
        String position = request.getPosition();

        // 优先级1：行业+职位特定内容
        if (StringUtils.hasText(industry) && StringUtils.hasText(position)) {
            String content = getIndustryPositionContent(category, industry, position);
            if (content != null) {
                return buildResponse(content, "industry_position", request);
            }
        }

        // 优先级2：行业特定内容
        if (StringUtils.hasText(industry)) {
            String content = getIndustryContent(category, industry);
            if (content != null) {
                return buildResponse(content, "industry", request);
            }
        }

        // 优先级3：类别默认内容
        String content = getCategoryContent(category);
        if (content != null) {
            return buildResponse(content, "category", request);
        }

        // 优先级4：系统默认内容
        return getSystemDefaultContent(request);
    }

    /**
     * 获取行业+职位特定内容
     * @description 获取针对特定行业和职位的默认内容
     * @param category 模板类别
     * @param industry 行业
     * @param position 职位
     * @return JSON格式的默认内容
     */
    private String getIndustryPositionContent(String category, String industry, String position) {
        // TODO: 实际实现中应该从文件系统或数据库加载
        // 这里返回模拟数据
        if ("tech".equals(industry) && "frontend".equals(position)) {
            return generateTechFrontendContent(category);
        }
        return null;
    }

    /**
     * 获取行业特定内容
     * @description 获取针对特定行业的默认内容
     * @param category 模板类别
     * @param industry 行业
     * @return JSON格式的默认内容
     */
    private String getIndustryContent(String category, String industry) {
        // TODO: 实际实现中应该从文件系统或数据库加载
        // 这里返回模拟数据
        if ("tech".equals(industry)) {
            return generateTechContent(category);
        }
        return null;
    }

    /**
     * 获取类别默认内容
     * @description 获取模板类别的默认内容
     * @param category 模板类别
     * @return JSON格式的默认内容
     */
    private String getCategoryContent(String category) {
        // TODO: 实际实现中应该从文件系统或数据库加载
        // 这里返回模拟数据
        return generateCategoryContent(category);
    }

    /**
     * 获取系统默认内容
     * @description 获取系统通用的默认内容
     * @param request 请求参数
     * @return 默认内容响应
     */
    private DefaultContentResponse getSystemDefaultContent(DefaultContentRequest request) {
        String defaultContent = generateSystemDefaultContent();
        return buildResponse(defaultContent, "default", request);
    }

    /**
     * 构建响应对象
     * @description 构建标准的默认内容响应对象
     * @param content 内容JSON字符串
     * @param source 内容来源
     * @param request 原始请求
     * @return 默认内容响应
     */
    private DefaultContentResponse buildResponse(String content, String source, DefaultContentRequest request) {
        return DefaultContentResponse.builder()
                .defaultContent(content)
                .contentSource(source)
                .templateCategory(request.getTemplateCategory())
                .industry(request.getIndustry())
                .position(request.getPosition())
                .language(request.getLanguage())
                .contentVersion("1.0.0")
                .generateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 验证请求参数
     * @description 验证请求参数的有效性
     * @param request 请求参数
     * @throws IllegalArgumentException 参数无效时抛出异常
     */
    private void validateRequest(DefaultContentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        if (!StringUtils.hasText(request.getTemplateCategory())) {
            throw new IllegalArgumentException("模板类别不能为空");
        }

        if (!SUPPORTED_CATEGORIES.contains(request.getTemplateCategory())) {
            throw new IllegalArgumentException("不支持的模板类别: " + request.getTemplateCategory());
        }

        // 验证行业和职位的匹配关系
        if (StringUtils.hasText(request.getIndustry()) && StringUtils.hasText(request.getPosition())) {
            List<String> positions = INDUSTRY_POSITIONS.get(request.getIndustry());
            if (positions != null && !positions.contains(request.getPosition())) {
                log.warn("行业 {} 不支持职位 {}", request.getIndustry(), request.getPosition());
            }
        }
    }

    /**
     * 生成技术前端开发内容
     * @description 生成技术行业前端开发职位的默认内容
     * @param category 模板类别
     * @return JSON格式的默认内容
     */
    private String generateTechFrontendContent(String category) {
        // TODO: 实际实现中应该从模板文件生成
        return "{\n" +
                "  \"basicInfo\": {\n" +
                "    \"name\": \"张三\",\n" +
                "    \"title\": \"前端开发工程师\",\n" +
                "    \"phone\": \"13800138000\",\n" +
                "    \"email\": \"<EMAIL>\",\n" +
                "    \"address\": \"北京市朝阳区\"\n" +
                "  },\n" +
                "  \"skills\": [\n" +
                "    {\"name\": \"JavaScript\", \"level\": 90},\n" +
                "    {\"name\": \"Vue.js\", \"level\": 85},\n" +
                "    {\"name\": \"React\", \"level\": 80}\n" +
                "  ],\n" +
                "  \"workExperiences\": [\n" +
                "    {\n" +
                "      \"company\": \"科技公司\",\n" +
                "      \"position\": \"前端开发工程师\",\n" +
                "      \"startDate\": \"2022-01\",\n" +
                "      \"endDate\": \"至今\",\n" +
                "      \"description\": \"负责前端开发工作\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    /**
     * 生成技术行业内容
     * @description 生成技术行业的默认内容
     * @param category 模板类别
     * @return JSON格式的默认内容
     */
    private String generateTechContent(String category) {
        // 简化版的技术行业内容
        return generateTechFrontendContent(category);
    }

    /**
     * 生成类别默认内容
     * @description 生成模板类别的默认内容
     * @param category 模板类别
     * @return JSON格式的默认内容
     */
    private String generateCategoryContent(String category) {
        // TODO: 根据不同类别生成不同的默认内容
        return generateSystemDefaultContent();
    }

    /**
     * 生成系统默认内容
     * @description 生成系统通用的默认内容
     * @return JSON格式的默认内容
     */
    private String generateSystemDefaultContent() {
        return "{\n" +
                "  \"basicInfo\": {\n" +
                "    \"name\": \"姓名\",\n" +
                "    \"title\": \"求职意向\",\n" +
                "    \"phone\": \"联系电话\",\n" +
                "    \"email\": \"邮箱地址\",\n" +
                "    \"address\": \"居住地址\"\n" +
                "  },\n" +
                "  \"skills\": [],\n" +
                "  \"workExperiences\": [],\n" +
                "  \"educations\": [],\n" +
                "  \"projects\": []\n" +
                "}";
    }

    @Override
    public boolean hasDefaultContent(String templateCategory, String industry, String position) {
        // TODO: 实际实现中应该检查文件系统或数据库
        return SUPPORTED_CATEGORIES.contains(templateCategory);
    }

    @Override
    public List<String> getSupportedCategories() {
        return SUPPORTED_CATEGORIES;
    }

    @Override
    public List<String> getSupportedIndustries() {
        return SUPPORTED_INDUSTRIES;
    }

    @Override
    public List<String> getSupportedPositions(String industry) {
        if (StringUtils.hasText(industry)) {
            return INDUSTRY_POSITIONS.getOrDefault(industry, Arrays.asList());
        }
        // 返回所有职位
        return INDUSTRY_POSITIONS.values().stream()
                .flatMap(List::stream)
                .distinct()
                .sorted()
                .toList();
    }

    @Override
    public boolean refreshContentCache() {
        // TODO: 实际实现中应该清除缓存并重新加载
        log.info("刷新默认内容缓存");
        return true;
    }
} 