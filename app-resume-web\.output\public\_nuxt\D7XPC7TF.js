import{_ as Ve}from"./BXnw39BI.js";import{r as p,l as Ue,k as j,i as Me,A as je,c as a,a as e,q as f,b as Ie,w as Te,p as w,v as M,C as Ne,H as x,F as ee,g as se,h as D,t as n,s as S,f as Ae,d as G,m as te,o as l}from"./CURHyiUL.js";import{_ as Fe}from"./DlAUqK2U.js";const qe=()=>{const I=p([]),H=p([]),z=p(!1),T=p(null),u=Ue({current:1,size:10,total:0,pages:0}),c=Ue({keyword:"",categoryId:"",isPremium:"",isDeleted:"",sortBy:"default"}),U=j(()=>u.current<u.pages),N=j(()=>I.value.length===0&&!z.value),E=async(d={})=>{z.value=!0,T.value=null;try{const r={current:u.current,size:u.size,...d};c.keyword&&c.keyword.trim()&&(r.keyword=c.keyword.trim()),c.categoryId&&(r.categoryId=c.categoryId),c.isPremium!==""&&c.isPremium!==null&&c.isPremium!==void 0&&(r.isPremium=c.isPremium),c.isDeleted!==""&&c.isDeleted!==null&&c.isDeleted!==void 0&&(r.isDeleted=c.isDeleted),c.sortBy&&c.sortBy!=="default"&&(r.sortBy=c.sortBy);const v=await $fetch("/api/admin/template/list",{method:"GET",params:r,headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(v.code===200){let b=v.data.records||[];return b=b.map(h=>(h.previewImageUrl&&!h.previewImageUrl.startsWith("http")&&!h.previewImageUrl.startsWith("/api/")&&(h.previewImageUrl=`/api/admin/template/preview-image/${h.templateCode}`),h)),I.value=b,u.total=parseInt(v.data.total)||0,u.current=v.data.current||1,u.size=v.data.size||10,u.pages=v.data.pages||(u.total>0?Math.ceil(u.total/u.size):0),{success:!0,data:v.data}}else throw new Error(v.msg||"获取模板列表失败")}catch(r){return console.error("❌ 获取模板列表失败:",r),T.value=r.message||"获取模板列表失败",{success:!1,error:T.value}}finally{z.value=!1}};return{templates:I,categories:H,loading:z,error:T,pagination:u,filters:c,hasMore:U,isEmpty:N,fetchTemplates:E,searchTemplates:async d=>(c.keyword=d,u.current=1,await E()),filterTemplates:async d=>(Object.assign(c,d),u.current=1,await E()),loadPage:async d=>(u.current=d,await E()),loadMore:async()=>{if(U.value&&!z.value){u.current+=1;const d=await E();return d.success&&d.data.records&&I.value.push(...d.data.records),d}return{success:!1,error:"没有更多数据"}},refreshTemplates:async()=>(u.current=1,await E()),resetFilters:()=>{c.keyword="",c.categoryId="",c.isPremium="",c.isDeleted="",c.sortBy="default",u.current=1},fetchCategories:async()=>{try{const d=await $fetch("/api/admin/template/categories",{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(d.code===200)return H.value=d.data||[],{success:!0,data:d.data};throw new Error(d.msg||"获取模板分类失败")}catch(d){return console.error("❌ 获取模板分类失败:",d),T.value=d.message||"获取模板分类失败",{success:!1,error:T.value}}},deleteTemplate:async d=>{try{const r=await $fetch(`/api/admin/template/${d}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(r.code===200)return I.value=I.value.filter(v=>v.id!==d),{success:!0,message:"模板删除成功"};throw new Error(r.msg||"删除模板失败")}catch(r){return console.error("❌ 删除模板失败:",r),{success:!1,error:r.message||"删除模板失败"}}},updateTemplateStatus:async(d,r)=>{try{const v=await $fetch(`/api/admin/template/${d}/status`,{method:"PUT",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`,"Content-Type":"application/json"},body:{status:r}});if(v.code===200){const b=I.value.find(h=>h.id===d);return b&&(b.status=r),{success:!0,message:"模板状态更新成功"}}else throw new Error(v.msg||"更新模板状态失败")}catch(v){return console.error("❌ 更新模板状态失败:",v),{success:!1,error:v.message||"更新模板状态失败"}}},batchDeleteTemplates:async d=>{try{const r=await $fetch("/api/admin/template/batch-delete",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`,"Content-Type":"application/json"},body:{templateIds:d}});if(r.code===200)return I.value=I.value.filter(v=>!d.includes(v.id)),{success:!0,message:"模板批量删除成功"};throw new Error(r.msg||"批量删除模板失败")}catch(r){return console.error("❌ 批量删除模板失败:",r),{success:!1,error:r.message||"批量删除模板失败"}}}}},We={class:"template-list-page"},Re={class:"page-header"},Ge={class:"header-content"},He={class:"header-right"},Ke={class:"search-section"},Je={class:"search-filters"},Qe={class:"search-box"},Xe={class:"filter-controls"},Ye=["value"],Ze={class:"list-content"},es={key:0,class:"loading-state"},ss={key:1,class:"error-state"},ts={key:2,class:"empty-state"},os={key:3,class:"template-list"},as={class:"list-header"},ls={class:"list-info"},is={class:"total-count"},rs={class:"current-page"},ns={class:"view-controls"},ds={key:0,class:"list-view"},cs={class:"list-table"},us={class:"table-body"},vs={class:"col-preview"},ms={key:0,class:"preview-placeholder-small"},ps=["src","alt","onError","onLoad"],gs={class:"col-name"},fs={class:"template-name"},ys={class:"template-desc"},bs={class:"col-code"},_s={class:"template-code"},hs={class:"col-category"},ws={class:"col-type"},ks={key:0,class:"price"},Cs={class:"col-stats"},Ps={class:"stat-line"},Us={class:"stat-line"},Is={class:"col-status"},Ts={key:0,class:"status-indicator deleted"},$s={class:"col-time"},Ds={class:"col-actions"},Es={class:"action-buttons"},Ss=["onClick"],zs=["onClick"],Bs=["onClick","title"],Ls=["onClick"],xs=["onClick"],Os={key:1,class:"grid-view"},Vs={class:"template-preview"},Ms={key:0,class:"preview-placeholder"},js=["src","alt","onError","onLoad"],Ns={class:"template-overlay"},As={class:"overlay-actions"},Fs=["onClick"],qs=["onClick"],Ws={class:"template-info"},Rs={class:"template-header"},Gs={class:"template-name"},Hs={class:"template-badges"},Ks={key:0,class:"badge deleted"},Js={key:1,class:"badge premium"},Qs={key:2,class:"badge free"},Xs={key:3,class:"badge hot"},Ys={class:"template-meta"},Zs={class:"template-code"},et={class:"template-category"},st={class:"template-stats"},tt={class:"stat-item"},ot={class:"stat-item"},at={class:"stat-item"},lt={class:"template-actions"},it=["onClick"],rt=["onClick"],nt=["onClick"],dt={class:"pagination"},ct=["disabled"],ut={class:"page-numbers"},vt=["onClick"],mt=["disabled"],pt={class:"page-info"},gt={class:"modal-body"},ft={class:"modal-actions"},yt=["disabled"],bt={class:"modal-header"},_t={class:"modal-body preview-body"},ht={key:0,class:"preview-loading"},wt={key:1,class:"preview-error"},kt={key:2,class:"preview-container"},Ct={class:"preview-info"},Pt={class:"info-row"},Ut={class:"info-row"},It={class:"info-row"},Tt={class:"info-row"},$t={key:0,class:"price"},Dt={class:"preview-image-container"},Et=["src"],St={key:1,class:"no-preview"},zt={key:2,class:"image-loading"},Bt={class:"modal-actions"},Lt={class:"modal-header"},xt={class:"modal-body edit-body"},Ot={class:"form-row"},Vt={class:"form-group"},Mt={class:"form-group"},jt={class:"form-group"},Nt={class:"form-row"},At={class:"form-group"},Ft={key:0,class:"form-group"},qt={class:"form-row"},Wt={class:"form-group"},Rt={class:"form-group"},Gt={class:"modal-actions"},Ht=["disabled"],Kt={__name:"list",setup(I){const H=qe(),{loading:z,error:T,templates:u,categories:c,pagination:U,filters:N,isEmpty:E,fetchTemplates:ne,fetchCategories:de,deleteTemplate:ce,updateTemplateStatus:ue,loadPage:A}=H,y=p({keyword:"",categoryId:"",isPremium:"",isDeleted:"",sortBy:"default"}),B=p("list"),F=p(!1),k=p(null),q=p(!1),d=p(!1),r=p(null),v=p(!1),b=p(""),h=p(""),W=p(!1),oe=p(!1),ae=p(null),K=p(!1),i=p({id:null,name:"",templateCode:"",description:"",isPremium:0,price:null,sortOrder:0,status:1}),$e=j(()=>U.total),$=j(()=>U.current),J=p(10),le=j(()=>U.pages),De=j(()=>{const o=[],s=Math.max(1,$.value-2),m=Math.min(le.value,$.value+2);for(let C=s;C<=m;C++)o.push(C);return o}),O=async()=>{try{Object.assign(N,y.value),await ne()}catch(o){console.error("加载模板列表失败:",o)}},Ee=async()=>{try{const o=await de();if(!o.success)throw new Error(o.error)}catch(o){console.error("加载分类列表失败:",o)}},V=()=>{Object.assign(N,y.value),U.current=1,O()},Se=()=>{y.value={keyword:"",categoryId:"",isPremium:"",isDeleted:"",sortBy:"default"},Object.assign(N,y.value),U.current=1,O()},ze=()=>{U.size=parseInt(J.value),U.current=1,O()},R=async o=>{if(!o){L("模板数据不完整","error");return}try{r.value=o,d.value=!0,await ve(o)}catch(s){console.error("预览模板失败:",s),L("打开预览失败","error")}},ve=async o=>{if(!o){b.value="模板数据不完整";return}try{v.value=!0,b.value="",W.value=!0;const s=await $fetch(`/api/admin/template/${o.id}/preview`,{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(s.code===200&&s.data)h.value=s.data.previewUrl;else throw new Error(s.msg||"获取预览失败")}catch(s){console.error("加载模板预览失败:",s);let m="加载预览失败";s.statusCode===404?m="模板文件不存在":s.statusCode===401?m="登录已过期，请重新登录":s.statusCode===403?m="没有权限查看此模板":s.statusCode>=500?m="服务器错误，请稍后重试":s.message&&(m=s.message),b.value=m}finally{v.value=!1}},Be=()=>{r.value&&ve(r.value)},Q=()=>{d.value=!1,r.value=null,h.value="",b.value="",W.value=!1},Le=()=>{W.value=!1,console.log("预览图片加载成功")},xe=()=>{W.value=!1,console.error("预览图片加载失败"),b.value="预览图片加载失败"},ie=o=>{ae.value=o,Object.assign(i.value,{id:o.id,name:o.name,templateCode:o.templateCode,description:o.description||"",isPremium:o.isPremium,price:o.price,sortOrder:o.sortOrder||0,status:o.status}),oe.value=!0,d.value&&Q()},me=async()=>{var o,s,m,C;if(!((o=i.value.name)!=null&&o.trim())){L("模板名称不能为空","error");return}if(!((s=i.value.templateCode)!=null&&s.trim())){L("模板代码不能为空","error");return}if(i.value.isPremium===1&&(!i.value.price||i.value.price<=0)){L("付费模板必须设置有效价格","error");return}try{K.value=!0;const _=await $fetch(`/api/admin/template/${i.value.id}`,{method:"PUT",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`,"Content-Type":"application/json"},body:{name:i.value.name.trim(),templateCode:i.value.templateCode.trim(),description:((m=i.value.description)==null?void 0:m.trim())||"",isPremium:i.value.isPremium,price:i.value.isPremium===1?i.value.price:null,sortOrder:i.value.sortOrder||0,status:i.value.status}});if(_.code===200){const P=u.value.findIndex(Z=>Z.id===i.value.id);P!==-1&&Object.assign(u.value[P],{name:i.value.name,templateCode:i.value.templateCode,description:i.value.description,isPremium:i.value.isPremium,price:i.value.price,sortOrder:i.value.sortOrder,status:i.value.status,updateTime:new Date().toISOString()}),X(),L("模板更新成功","success")}else throw new Error(_.msg||"保存失败")}catch(_){console.error("保存模板失败:",_);let P="保存失败";_.message?P=_.message:(C=_.data)!=null&&C.msg?P=_.data.msg:_.statusCode===401?P="登录已过期，请重新登录":_.statusCode===403?P="没有权限执行此操作":_.statusCode>=500&&(P="服务器错误，请稍后重试"),L(P,"error")}finally{K.value=!1}},X=()=>{oe.value=!1,ae.value=null,Object.assign(i.value,{id:null,name:"",templateCode:"",description:"",isPremium:0,price:null,sortOrder:0,status:1})},L=(o,s="info")=>{console.log(`[${s.toUpperCase()}] ${o}`)},pe=async o=>{try{const s=o.status===1?0:1,m=await ue(o.id,s);if(m.success)console.log(`模板 ${o.name} 状态已更新为: ${s===1?"启用":"禁用"}`);else throw new Error(m.error)}catch(s){console.error("更新模板状态失败:",s),alert("更新模板状态失败: "+s.message)}},ge=o=>{k.value=o,F.value=!0},Oe=async()=>{if(k.value){q.value=!0;try{console.log("🗑️ 准备删除模板:",{id:k.value.id,name:k.value.name,templateCode:k.value.templateCode,isDeleted:k.value.isDeleted});const o=await ce(k.value.id);if(o.success)console.log(`模板 ${k.value.name} 删除成功`),F.value=!1,k.value=null,u.value.length===1&&$.value>1?A($.value-1):O();else throw new Error(o.error)}catch(o){console.error("删除模板失败:",o),alert("删除模板失败: "+o.message)}finally{q.value=!1}}},re=()=>{F.value=!1,k.value=null},fe=async o=>{try{console.log("恢复模板:",o),alert(`恢复模板功能开发中: ${o.name}`)}catch(s){console.error("恢复模板失败:",s),alert("恢复模板失败: "+s.message)}},ye=(o,s)=>{const m=Y(s);console.log("预览图加载成功:"),console.log("- 模板代码:",s.templateCode),console.log("- 原始URL:",s.previewImageUrl),console.log("- 实际使用URL:",m)},be=(o,s)=>{const m=Y(s);if(console.log("预览图加载失败:"),console.log("- 模板代码:",s.templateCode),console.log("- 原始URL:",s.previewImageUrl),console.log("- 实际使用URL:",m),console.log("- 错误事件:",o),s.previewImageUrl&&!s.previewImageUrl.includes("/api/admin/template/preview-image/")){const C=`/api/admin/template/preview-image/${s.templateCode}`;console.log("使用备用预览图路径:",C),s.previewImageUrl=C}else s.previewImageUrl=null},Y=o=>o.previewImageUrl?o.previewImageUrl.startsWith("http")||o.previewImageUrl.startsWith("/api/admin/template/preview-image/")||o.previewImageUrl.startsWith("/")?o.previewImageUrl:`/api/admin/template/preview-image/${o.templateCode}`:null,_e=o=>o?new Date(o).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-";return Me(async()=>{U.size=J.value,await Promise.all([Ee(),O()])}),je(T,o=>{o&&setTimeout(()=>{},5e3)}),(o,s)=>{var C,_,P,Z,he,we,ke,Ce,Pe;const m=Ve;return l(),a("div",We,[e("div",Re,[e("div",Ge,[s[22]||(s[22]=e("div",{class:"header-left"},[e("h1",{class:"page-title"},"模板列表"),e("p",{class:"page-description"},"管理所有已上传的简历模板")],-1)),e("div",He,[Ie(m,{to:"/admin/template/upload",class:"btn-primary"},{default:Te(()=>s[21]||(s[21]=[G(" ➕ 上传模板 ")])),_:1,__:[21]})])])]),e("div",Ke,[e("div",Je,[e("div",Qe,[w(e("input",{"onUpdate:modelValue":s[0]||(s[0]=t=>y.value.keyword=t),type:"text",placeholder:"搜索模板名称、代码、描述...",class:"search-input",onKeyup:Ne(V,["enter"])},null,544),[[M,y.value.keyword]]),e("button",{onClick:V,class:"search-btn"},"🔍")]),e("div",Xe,[w(e("select",{"onUpdate:modelValue":s[1]||(s[1]=t=>y.value.categoryId=t),onChange:V,class:"filter-select"},[s[23]||(s[23]=e("option",{value:""},"全部分类",-1)),(l(!0),a(ee,null,se(D(c),t=>(l(),a("option",{key:t.id,value:t.id},n(t.name),9,Ye))),128))],544),[[x,y.value.categoryId]]),w(e("select",{"onUpdate:modelValue":s[2]||(s[2]=t=>y.value.isPremium=t),onChange:V,class:"filter-select"},s[24]||(s[24]=[e("option",{value:""},"全部类型",-1),e("option",{value:"0"},"免费模板",-1),e("option",{value:"1"},"付费模板",-1)]),544),[[x,y.value.isPremium]]),w(e("select",{"onUpdate:modelValue":s[3]||(s[3]=t=>y.value.isDeleted=t),onChange:V,class:"filter-select"},s[25]||(s[25]=[e("option",{value:""},"全部状态",-1),e("option",{value:"0"},"正常模板",-1),e("option",{value:"1"},"已删除",-1)]),544),[[x,y.value.isDeleted]]),w(e("select",{"onUpdate:modelValue":s[4]||(s[4]=t=>y.value.sortBy=t),onChange:V,class:"filter-select"},s[26]||(s[26]=[e("option",{value:"default"},"默认排序",-1),e("option",{value:"latest"},"最新创建",-1),e("option",{value:"popular"},"使用次数",-1),e("option",{value:"rating"},"评分排序",-1)]),544),[[x,y.value.sortBy]]),e("button",{onClick:Se,class:"reset-btn"},"🔄 重置")])])]),e("div",Ze,[D(z)?(l(),a("div",es,s[27]||(s[27]=[e("div",{class:"loading-spinner"},null,-1),e("p",null,"正在加载模板列表...",-1)]))):D(T)?(l(),a("div",ss,[s[28]||(s[28]=e("div",{class:"error-icon"},"⚠️",-1)),s[29]||(s[29]=e("h3",null,"加载失败",-1)),e("p",null,n(D(T)),1),e("button",{onClick:O,class:"retry-btn"},"重试")])):D(E)?(l(),a("div",ts,[s[31]||(s[31]=e("div",{class:"empty-icon"},"📋",-1)),s[32]||(s[32]=e("h3",null,"暂无模板",-1)),s[33]||(s[33]=e("p",null,"还没有上传任何模板，点击上传按钮开始添加模板",-1)),Ie(m,{to:"/admin/template/upload",class:"btn-primary"},{default:Te(()=>s[30]||(s[30]=[G(" ➕ 上传模板 ")])),_:1,__:[30]})])):(l(),a("div",os,[e("div",as,[e("div",ls,[e("span",is,"共 "+n($e.value)+" 个模板",1),e("span",rs,"第 "+n($.value)+" / "+n(le.value)+" 页",1)]),e("div",ns,[e("button",{class:S(["view-btn",{active:B.value==="list"}]),onClick:s[5]||(s[5]=t=>B.value="list")}," 📋 列表 ",2),e("button",{class:S(["view-btn",{active:B.value==="grid"}]),onClick:s[6]||(s[6]=t=>B.value="grid")}," 🔲 网格 ",2)])]),B.value==="list"?(l(),a("div",ds,[e("div",cs,[s[34]||(s[34]=Ae('<div class="table-header" data-v-459ca0d5><div class="col-preview" data-v-459ca0d5>预览</div><div class="col-name" data-v-459ca0d5>模板名称</div><div class="col-code" data-v-459ca0d5>模板代码</div><div class="col-category" data-v-459ca0d5>分类</div><div class="col-type" data-v-459ca0d5>类型</div><div class="col-stats" data-v-459ca0d5>统计</div><div class="col-status" data-v-459ca0d5>状态</div><div class="col-time" data-v-459ca0d5>创建时间</div><div class="col-actions" data-v-459ca0d5>操作</div></div>',1)),e("div",us,[(l(!0),a(ee,null,se(D(u),t=>(l(),a("div",{key:t.id,class:"table-row"},[e("div",vs,[t.previewImageUrl?(l(),a("img",{key:1,src:Y(t),alt:t.name,class:"preview-thumb",onError:g=>be(g,t),onLoad:g=>ye(g,t)},null,40,ps)):(l(),a("div",ms," 📄 "))]),e("div",gs,[e("div",fs,n(t.name),1),e("div",ys,n(t.description),1)]),e("div",bs,[e("code",_s,n(t.templateCode),1)]),e("div",hs,n(t.categoryName||"未分类"),1),e("div",ws,[e("span",{class:S(["type-badge",t.isPremium?"premium":"free"])},n(t.isPremium?"付费":"免费"),3),t.isPremium?(l(),a("span",ks,"¥"+n(t.price),1)):f("",!0)]),e("div",Cs,[e("div",Ps,"👁️ "+n(t.useCount||0),1),e("div",Us,"⭐ "+n(t.rating||0),1)]),e("div",Is,[t.isDeleted===1?(l(),a("span",Ts," 已删除 ")):(l(),a("span",{key:1,class:S(["status-indicator",t.status===1?"enabled":"disabled"])},n(t.status===1?"启用":"禁用"),3))]),e("div",$s,n(_e(t.createTime)),1),e("div",Ds,[e("div",Es,[e("button",{onClick:g=>R(t),class:"action-btn preview-btn",title:"预览"}," 👁️ ",8,Ss),e("button",{onClick:g=>ie(t),class:"action-btn edit-btn",title:"编辑"}," ✏️ ",8,zs),t.isDeleted!==1?(l(),a("button",{key:0,onClick:g=>pe(t),class:S(["action-btn","status-btn",t.status===1?"disable":"enable"]),title:t.status===1?"禁用":"启用"},n(t.status===1?"⏸️":"▶️"),11,Bs)):f("",!0),t.isDeleted!==1?(l(),a("button",{key:1,onClick:g=>ge(t),class:"action-btn delete-btn",title:"删除"}," 🗑️ ",8,Ls)):f("",!0),t.isDeleted===1?(l(),a("button",{key:2,onClick:g=>fe(t),class:"action-btn restore-btn",title:"恢复"}," ↩️ ",8,xs)):f("",!0)])])]))),128))])])])):B.value==="grid"?(l(),a("div",Os,[(l(!0),a(ee,null,se(D(u),t=>(l(),a("div",{key:t.id,class:"template-card"},[e("div",Vs,[t.previewImageUrl?(l(),a("img",{key:1,src:Y(t),alt:t.name,class:"preview-image",onError:g=>be(g,t),onLoad:g=>ye(g,t)},null,40,js)):(l(),a("div",Ms,s[35]||(s[35]=[e("div",{class:"placeholder-icon"},"📄",-1),e("div",{class:"placeholder-text"},"暂无预览",-1)]))),e("div",Ns,[e("div",As,[e("button",{onClick:g=>R(t),class:"action-btn preview-btn"}," 👁️ 预览 ",8,Fs),e("button",{onClick:g=>ie(t),class:"action-btn edit-btn"}," ✏️ 编辑 ",8,qs)])])]),e("div",Ws,[e("div",Rs,[e("h3",Gs,n(t.name),1),e("div",Hs,[t.isDeleted===1?(l(),a("span",Ks,"已删除")):f("",!0),t.isPremium?(l(),a("span",Js,"付费")):(l(),a("span",Qs,"免费")),t.isHot?(l(),a("span",Xs,"热门")):f("",!0)])]),e("div",Ys,[e("span",Zs,n(t.templateCode),1),e("span",et,n(t.categoryName||"未分类"),1)]),e("div",st,[e("div",tt,[e("span",null,"👁️ "+n(t.useCount||0),1)]),e("div",ot,[e("span",null,"⭐ "+n(t.rating||0),1)]),e("div",at,[e("span",null,"🕒 "+n(_e(t.createTime)),1)])]),e("div",lt,[t.isDeleted!==1?(l(),a("button",{key:0,onClick:g=>pe(t),class:S(["status-btn",t.status===1?"enabled":"disabled"])},n(t.status===1?"✅ 已启用":"⏸️ 已禁用"),11,it)):f("",!0),t.isDeleted!==1?(l(),a("button",{key:1,onClick:g=>ge(t),class:"delete-btn"}," 🗑️ 删除 ",8,rt)):f("",!0),t.isDeleted===1?(l(),a("button",{key:2,onClick:g=>fe(t),class:"restore-btn"}," ↩️ 恢复 ",8,nt)):f("",!0)])])]))),128))])):f("",!0),e("div",dt,[e("button",{onClick:s[7]||(s[7]=t=>D(A)($.value-1)),disabled:$.value<=1,class:"page-btn"}," ⬅️ 上一页 ",8,ct),e("div",ut,[(l(!0),a(ee,null,se(De.value,t=>(l(),a("button",{key:t,onClick:g=>D(A)(t),class:S(["page-number",{active:t===$.value}])},n(t),11,vt))),128))]),e("button",{onClick:s[8]||(s[8]=t=>D(A)($.value+1)),disabled:$.value>=le.value,class:"page-btn"}," 下一页 ➡️ ",8,mt),e("div",pt,[s[37]||(s[37]=e("span",null,"每页",-1)),w(e("select",{"onUpdate:modelValue":s[9]||(s[9]=t=>J.value=t),onChange:ze,class:"page-size-select"},s[36]||(s[36]=[e("option",{value:10},"10",-1),e("option",{value:20},"20",-1),e("option",{value:50},"50",-1)]),544),[[x,J.value]]),s[38]||(s[38]=e("span",null,"条",-1))])])]))]),F.value?(l(),a("div",{key:0,class:"modal-overlay",onClick:re},[e("div",{class:"modal-content",onClick:s[10]||(s[10]=te(()=>{},["stop"]))},[e("div",{class:"modal-header"},[s[39]||(s[39]=e("h3",null,"确认删除",-1)),e("button",{onClick:re,class:"close-btn"},"×")]),e("div",gt,[e("p",null,[s[40]||(s[40]=G("确定要删除模板 ")),e("strong",null,n((C=k.value)==null?void 0:C.name),1),s[41]||(s[41]=G(" 吗？"))]),s[42]||(s[42]=e("p",{class:"warning-text"},"此操作不可恢复，请谨慎操作。",-1))]),e("div",ft,[e("button",{onClick:re,class:"btn-secondary"},"取消"),e("button",{onClick:Oe,class:"btn-danger",disabled:q.value},n(q.value?"删除中...":"确认删除"),9,yt)])])])):f("",!0),d.value?(l(),a("div",{key:1,class:"modal-overlay",onClick:Q},[e("div",{class:"modal-content large",onClick:s[12]||(s[12]=te(()=>{},["stop"]))},[e("div",bt,[e("h3",null,"模板预览 - "+n(R==null?void 0:R.name),1),e("button",{onClick:Q,class:"close-btn"},"×")]),e("div",_t,[v.value?(l(),a("div",ht,s[43]||(s[43]=[e("div",{class:"loading-spinner"},null,-1),e("p",null,"正在加载预览...",-1)]))):b.value?(l(),a("div",wt,[s[44]||(s[44]=e("div",{class:"error-icon"},"⚠️",-1)),e("p",null,"预览加载失败: "+n(b.value),1),e("button",{onClick:Be,class:"retry-btn"},"重试")])):(l(),a("div",kt,[e("div",Ct,[e("div",Pt,[s[45]||(s[45]=e("span",{class:"info-label"},"模板代码:",-1)),e("code",null,n((_=r.value)==null?void 0:_.templateCode),1)]),e("div",Ut,[s[46]||(s[46]=e("span",{class:"info-label"},"模板描述:",-1)),e("span",null,n(((P=r.value)==null?void 0:P.description)||"暂无描述"),1)]),e("div",It,[s[47]||(s[47]=e("span",{class:"info-label"},"分类:",-1)),e("span",null,n(((Z=r.value)==null?void 0:Z.categoryName)||"未分类"),1)]),e("div",Tt,[s[48]||(s[48]=e("span",{class:"info-label"},"类型:",-1)),e("span",{class:S(["type-badge",(he=r.value)!=null&&he.isPremium?"premium":"free"])},[G(n((we=r.value)!=null&&we.isPremium?"付费":"免费")+" ",1),(ke=r.value)!=null&&ke.isPremium?(l(),a("span",$t,"¥"+n((Ce=r.value)==null?void 0:Ce.price),1)):f("",!0)],2)])]),e("div",Dt,[h.value?(l(),a("img",{key:0,src:h.value,class:"preview-image",onError:xe,onLoad:Le,alt:"模板预览图"},null,40,Et)):(l(),a("div",St,s[49]||(s[49]=[e("div",{class:"no-preview-icon"},"📄",-1),e("p",null,"暂无预览",-1)]))),W.value?(l(),a("div",zt,s[50]||(s[50]=[e("div",{class:"loading-spinner"},null,-1),e("p",null,"加载中...",-1)]))):f("",!0)])]))]),e("div",Bt,[e("button",{onClick:s[11]||(s[11]=t=>ie(r.value)),class:"btn-primary"}," ✏️ 编辑模板 "),e("button",{onClick:Q,class:"btn-secondary"},"关闭")])])])):f("",!0),oe.value?(l(),a("div",{key:2,class:"modal-overlay",onClick:X},[e("div",{class:"modal-content large",onClick:s[20]||(s[20]=te(()=>{},["stop"]))},[e("div",Lt,[e("h3",null,"编辑模板 - "+n((Pe=ae.value)==null?void 0:Pe.name),1),e("button",{onClick:X,class:"close-btn"},"×")]),e("div",xt,[e("form",{onSubmit:te(me,["prevent"]),class:"edit-form"},[e("div",Ot,[e("div",Vt,[s[51]||(s[51]=e("label",{class:"form-label required"},"模板名称",-1)),w(e("input",{"onUpdate:modelValue":s[13]||(s[13]=t=>i.value.name=t),type:"text",class:"form-input",placeholder:"请输入模板名称",required:""},null,512),[[M,i.value.name]])]),e("div",Mt,[s[52]||(s[52]=e("label",{class:"form-label required"},"模板代码",-1)),w(e("input",{"onUpdate:modelValue":s[14]||(s[14]=t=>i.value.templateCode=t),type:"text",class:"form-input",placeholder:"请输入模板代码",disabled:!0,title:"模板代码不可修改"},null,512),[[M,i.value.templateCode]])])]),e("div",jt,[s[53]||(s[53]=e("label",{class:"form-label"},"模板描述",-1)),w(e("textarea",{"onUpdate:modelValue":s[15]||(s[15]=t=>i.value.description=t),class:"form-textarea",rows:"3",placeholder:"请输入模板描述"},null,512),[[M,i.value.description]])]),e("div",Nt,[e("div",At,[s[55]||(s[55]=e("label",{class:"form-label"},"模板类型",-1)),w(e("select",{"onUpdate:modelValue":s[16]||(s[16]=t=>i.value.isPremium=t),class:"form-select"},s[54]||(s[54]=[e("option",{value:0},"免费模板",-1),e("option",{value:1},"付费模板",-1)]),512),[[x,i.value.isPremium]])]),i.value.isPremium===1?(l(),a("div",Ft,[s[56]||(s[56]=e("label",{class:"form-label required"},"模板价格",-1)),w(e("input",{"onUpdate:modelValue":s[17]||(s[17]=t=>i.value.price=t),type:"number",step:"0.01",min:"0",class:"form-input",placeholder:"请输入价格（元）"},null,512),[[M,i.value.price]])])):f("",!0)]),e("div",qt,[e("div",Wt,[s[57]||(s[57]=e("label",{class:"form-label"},"排序权重",-1)),w(e("input",{"onUpdate:modelValue":s[18]||(s[18]=t=>i.value.sortOrder=t),type:"number",min:"0",class:"form-input",placeholder:"数值越大排序越靠前"},null,512),[[M,i.value.sortOrder]])]),e("div",Rt,[s[59]||(s[59]=e("label",{class:"form-label"},"状态",-1)),w(e("select",{"onUpdate:modelValue":s[19]||(s[19]=t=>i.value.status=t),class:"form-select"},s[58]||(s[58]=[e("option",{value:1},"启用",-1),e("option",{value:0},"禁用",-1)]),512),[[x,i.value.status]])])])],32)]),e("div",Gt,[e("button",{onClick:me,class:"btn-primary",disabled:K.value},n(K.value?"保存中...":"保存"),9,Ht),e("button",{onClick:X,class:"btn-secondary"},"取消")])])])):f("",!0)])}}},Yt=Fe(Kt,[["__scopeId","data-v-459ca0d5"]]);export{Yt as default};
