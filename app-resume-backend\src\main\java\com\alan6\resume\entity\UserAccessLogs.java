package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户访问日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("user_access_logs")
@Schema(name = "UserAccessLogs对象", description = "用户访问日志表")
public class UserAccessLogs implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "访问日志ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "国家代码")
    private String countryCode;

    @Schema(description = "国家名称")
    private String countryName;

    @Schema(description = "地区")
    private String region;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "检测到的语言")
    private String detectedLanguage;

    @Schema(description = "用户代理")
    private String userAgent;

    @Schema(description = "来源页面")
    private String referer;

    @Schema(description = "访问时间")
    private LocalDateTime accessTime;
}
