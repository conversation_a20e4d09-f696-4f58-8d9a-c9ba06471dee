/**
 * Paged.js 组合式函数
 * @description 动态加载 Paged.js 并在指定容器中实现分页
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, onUnmounted } from 'vue'

export function usePagedJS() {
  // ================================
  // 响应式数据
  // ================================
  const isLoading = ref(false)
  const isLoaded = ref(false)
  const error = ref(null)
  const pagedInstance = ref(null)
  
  // ================================
  // 加载状态管理
  // ================================
  
  /**
   * 动态加载 Paged.js 脚本
   * @returns {Promise<boolean>} 加载成功与否
   */
  const loadPagedJS = async () => {
    // 如果已经加载过，直接返回
    if (isLoaded.value) {
      return true
    }
    
    // 检查是否已经存在 Paged.js
    if (window.PagedPolyfill) {
      isLoaded.value = true
      return true
    }
    
    isLoading.value = true
    error.value = null
    
    try {
      // 动态创建 script 标签
      const script = document.createElement('script')
      script.src = 'https://unpkg.com/pagedjs@0.4.3/dist/paged.polyfill.js'
      script.async = true
      
      // 使用 Promise 包装脚本加载
      await new Promise((resolve, reject) => {
        script.onload = () => {
          console.log('✅ Paged.js 加载成功')
          resolve()
        }
        
        script.onerror = () => {
          reject(new Error('Paged.js 脚本加载失败'))
        }
        
        // 设置超时
        setTimeout(() => {
          reject(new Error('Paged.js 加载超时'))
        }, 10000)
        
        document.head.appendChild(script)
      })
      
      // 等待 Paged.js 初始化完成
      await waitForPagedJS()
      
      isLoaded.value = true
      return true
      
    } catch (err) {
      console.error('❌ Paged.js 加载失败:', err)
      error.value = err.message
      return false
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 等待 Paged.js 完全初始化
   * @returns {Promise<void>}
   */
  const waitForPagedJS = () => {
    return new Promise((resolve, reject) => {
      let attempts = 0
      const maxAttempts = 50
      
      const checkPagedJS = () => {
        if (window.PagedPolyfill) {
          resolve()
        } else if (attempts < maxAttempts) {
          attempts++
          setTimeout(checkPagedJS, 100)
        } else {
          reject(new Error('Paged.js 初始化超时'))
        }
      }
      
      checkPagedJS()
    })
  }
  
  // ================================
  // 分页功能
  // ================================
  
  /**
   * 初始化指定容器的分页
   * @param {HTMLElement|string} container - 容器元素或选择器
   * @param {Object} options - Paged.js 配置选项
   * @returns {Promise<boolean>} 初始化成功与否
   */
  const initializePaging = async (container, options = {}) => {
    try {
      // 确保 Paged.js 已加载
      const loaded = await loadPagedJS()
      if (!loaded) {
        throw new Error('Paged.js 加载失败')
      }
      
      // 获取容器元素
      const element = typeof container === 'string' 
        ? document.querySelector(container)
        : container
        
      if (!element) {
        throw new Error('找不到指定的容器元素')
      }
      
      console.log('🔄 开始 Paged.js 分页处理...')
      
      // 清理之前的分页结果
      clearPaging()
      
      // 创建 Paged.js 预览器实例
      const previewer = new window.PagedPolyfill.Previewer()
      
      // 配置分页选项
      const pagedConfig = {
        before: () => {
          console.log('📄 Paged.js 分页开始')
        },
        after: () => {
          console.log('✅ Paged.js 分页完成')
          setTimeout(() => {
            updatePagedJSInfo()
          }, 100)
        },
        ...options
      }
      
      // 执行分页处理
      await previewer.preview(element.innerHTML, [], element, pagedConfig)
      
      pagedInstance.value = previewer
      console.log('✅ 容器分页初始化完成')
      
      return true
      
    } catch (err) {
      console.error('❌ 分页初始化失败:', err)
      error.value = err.message
      return false
    }
  }
  
  /**
   * 重新计算分页
   * @param {HTMLElement|string} container - 容器元素或选择器
   * @returns {Promise<boolean>} 重新分页成功与否
   */
  const recalculatePaging = async (container) => {
    console.log('🔄 重新计算分页...')
    
    // 直接重新初始化，确保使用最新内容
    return await initializePaging(container)
  }
  
  /**
   * 清理分页效果
   */
  const clearPaging = () => {
    if (pagedInstance.value) {
      try {
        // 清理 Paged.js 生成的 DOM 结构
        const pagedElements = document.querySelectorAll('.pagedjs_pages, .pagedjs_page')
        pagedElements.forEach(el => el.remove())
        
        pagedInstance.value = null
        console.log('🧹 分页效果已清理')
      } catch (err) {
        console.error('❌ 清理分页效果失败:', err)
      }
    }
  }
  
  /**
   * 获取分页信息
   * @returns {Object} 分页统计信息
   */
  const getPageInfo = () => {
    if (!pagedInstance.value) {
      return {
        totalPages: 1,
        currentPage: 1,
        hasMultiplePages: false
      }
    }
    
    const pages = document.querySelectorAll('.pagedjs_page')
    
    return {
      totalPages: pages.length,
      currentPage: 1,
      hasMultiplePages: pages.length > 1,
      pages: Array.from(pages)
    }
  }
  
  /**
   * 更新分页信息（内部使用）
   */
  const updatePagedJSInfo = () => {
    if (pagedInstance.value) {
      const pages = document.querySelectorAll('.pagedjs_page')
      console.log(`📊 检测到 ${pages.length} 个页面`)
    }
  }
  
  // ================================
  // 生命周期管理
  // ================================
  
  onUnmounted(() => {
    clearPaging()
  })
  
  // ================================
  // 返回接口
  // ================================
  return {
    // 状态
    isLoading,
    isLoaded,
    error,
    
    // 核心方法
    loadPagedJS,
    initializePaging,
    recalculatePaging,
    clearPaging,
    getPageInfo,
    
    // 实例
    pagedInstance
  }
} 