package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.config.MinioConfig;
import com.alan6.resume.dto.template.TemplateUploadRequest;
import com.alan6.resume.dto.template.TemplateUploadResponse;
import com.alan6.resume.entity.FileUploadRecords;
import com.alan6.resume.entity.ResumeTemplateContent;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.service.IFileUploadRecordsService;
import com.alan6.resume.service.IResumeTemplateContentService;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.ITemplateUploadService;
import com.alan6.resume.service.IResumeTemplateCategoriesService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.minio.MinioClient;
import io.minio.ObjectWriteResponse;
import io.minio.PutObjectArgs;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 模板上传服务实现类
 * 
 * @description 实现模板文件上传的核心业务逻辑
 *              支持Vue+JSON分离架构：
 *              - Vue文件上传到MinIO并记录文件信息
 *              - JSON内容解析后存储到数据库
 *              - 其他文件上传但不记录（如CSS、图片等）
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateUploadServiceImpl implements ITemplateUploadService {

    /**
     * MinIO客户端
     */
    private final MinioClient minioClient;

    /**
     * MinIO配置属性
     */
    private final MinioConfig.MinioProperties minioProperties;

    /**
     * 模板服务
     */
    private final IResumeTemplatesService resumeTemplatesService;

    /**
     * 文件上传记录服务
     */
    private final IFileUploadRecordsService fileUploadRecordsService;

    /**
     * 模板内容服务
     */
    private final IResumeTemplateContentService templateContentService;

    /**
     * 模板分类关联服务
     */
    private final IResumeTemplateCategoriesService templateCategoriesService;

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 支持的文件类型
     */
    private static final Set<String> SUPPORTED_FILE_TYPES = Set.of(
            "application/javascript", "text/javascript", // JS文件
            "text/css", "text/scss", // CSS文件
            "text/html", "application/json", // HTML和JSON文件
            "text/plain", "text/vue", // Vue文件
            "image/jpeg", "image/png", "image/gif", "image/svg+xml", "image/webp" // 图片文件
    );

    /**
     * 文件扩展名与MIME类型映射
     */
    private static final Map<String, String> EXTENSION_MIME_MAP = Map.of(
            ".vue", "text/vue",
            ".js", "application/javascript",
            ".ts", "application/typescript",
            ".css", "text/css",
            ".scss", "text/scss",
            ".html", "text/html",
            ".json", "application/json",
            ".txt", "text/plain"
    );

    /**
     * 图片文件扩展名
     */
    private static final Set<String> IMAGE_EXTENSIONS = Set.of(
            ".jpg", ".jpeg", ".png", ".gif", ".svg", ".webp"
    );

    /**
     * 上传模板文件的核心方法
     * 
     * @description 实现新的上传逻辑：
     *              1. 识别Vue文件并上传到MinIO，创建文件记录
     *              2. 识别JSON文件并解析内容，存储到数据库
     *              3. 其他文件上传到MinIO但不创建记录
     * @param files 上传的文件列表
     * @param request 上传请求参数
     * @param uploadUserId 上传用户ID
     * @return 上传结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateUploadResponse uploadTemplateFiles(List<MultipartFile> files, 
                                                     MultipartFile previewImage,
                                                     TemplateUploadRequest request, 
                                                     Long uploadUserId) throws Exception {
        
        log.info("开始上传模板文件，模板代码: {}, 文件数量: {}, 预览图: {}, 上传用户: {}", 
                request.getTemplateCode(), files.size(), 
                previewImage != null ? previewImage.getOriginalFilename() : "无", uploadUserId);

        // 1. 参数验证
        validateUploadRequest(files, request);

        // 2. 检查模板代码唯一性
        if (!isTemplateCodeAvailable(request.getTemplateCode())) {
            throw new BusinessException("模板代码已存在: " + request.getTemplateCode());
        }

        // 3. 确保存储桶存在
        if (!ensureBucketExists(request.getBucketName())) {
            throw new BusinessException("存储桶创建失败: " + request.getBucketName());
        }

        // 4. 生成存储路径
        String storagePath = buildTemplatePath(request.getTemplateCode(), request.getLocalPath());

        // 5. 分类处理上传的文件
        UploadFileClassification classification = classifyUploadFiles(files);
        
        // 6. 处理Vue文件上传
        UploadedVueFileInfo vueFileInfo = null;
        if (classification.getVueFile() != null) {
            vueFileInfo = processVueFileUpload(classification.getVueFile(), 
                    request.getBucketName(), storagePath, uploadUserId);
        }

        // 7. 处理JSON内容
        ResumeTemplateContent templateContent = null;
        if (classification.getJsonFile() != null) {
            templateContent = processJsonContentUpload(classification.getJsonFile(), 
                    request, uploadUserId);
        }

        // 8. 处理预览图上传
        String previewImageUrl = null;
        if (previewImage != null && !previewImage.isEmpty()) {
            previewImageUrl = processPreviewImageUpload(previewImage, 
                    request.getBucketName(), storagePath, request.getTemplateCode());
            if (previewImageUrl != null) {
                request.setSavePreviewImageUrl(previewImageUrl);
                log.info("预览图上传成功: {}", previewImageUrl);
            }
        }

        // 9. 处理其他文件（上传但不记录）
        List<String> otherFileUrls = processOtherFilesUpload(classification.getOtherFiles(), 
                request.getBucketName(), storagePath);

        // 10. 创建模板记录
        ResumeTemplates template = createTemplateRecord(request, uploadUserId, 
                vueFileInfo, templateContent);

        // 10. 构建响应结果
        return buildUploadResponse(template, vueFileInfo, templateContent, 
                otherFileUrls, classification, request.getBucketName(), storagePath);
    }

    /**
     * 验证上传请求参数
     * 
     * @description 验证文件和请求参数的完整性和合法性
     * @param files 上传文件列表
     * @param request 上传请求
     */
    private void validateUploadRequest(List<MultipartFile> files, TemplateUploadRequest request) {
        if (files == null || files.isEmpty()) {
            throw new BusinessException("上传文件列表不能为空");
        }

        if (files.size() > 20) {
            throw new BusinessException("单次上传文件数量不能超过20个");
        }

        // 检查文件大小总和（不超过100MB）
        long totalSize = files.stream().mapToLong(MultipartFile::getSize).sum();
        if (totalSize > 100 * 1024 * 1024) {
            throw new BusinessException("上传文件总大小不能超过100MB");
        }

        // 检查必填字段
        if (request.getTemplateName() == null || request.getTemplateName().trim().isEmpty()) {
            throw new BusinessException("模板名称不能为空");
        }

        if (request.getTemplateCode() == null || request.getTemplateCode().trim().isEmpty()) {
            throw new BusinessException("模板代码不能为空");
        }

        // 检查是否包含必需的Vue文件
        boolean hasVueFile = files.stream()
                .anyMatch(file -> file.getOriginalFilename() != null && 
                         file.getOriginalFilename().toLowerCase().endsWith(".vue"));
        
        if (!hasVueFile) {
            throw new BusinessException("必须包含至少一个Vue模板文件");
        }
    }

    /**
     * 分类上传文件
     * 
     * @description 将上传的文件按类型分类处理
     * @param files 上传文件列表
     * @return 文件分类结果
     */
    private UploadFileClassification classifyUploadFiles(List<MultipartFile> files) {
        UploadFileClassification classification = new UploadFileClassification();
        
        for (MultipartFile file : files) {
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                continue;
            }
            
            String lowerFileName = fileName.toLowerCase();
            
            if (lowerFileName.endsWith(".vue")) {
                // Vue文件（只取第一个）
                if (classification.getVueFile() == null) {
                    classification.setVueFile(file);
                }
            } else if (lowerFileName.endsWith(".json")) {
                // JSON文件（只取第一个）
                if (classification.getJsonFile() == null) {
                    classification.setJsonFile(file);
                }
            } else {
                // 其他文件
                classification.getOtherFiles().add(file);
            }
        }
        
        log.info("文件分类完成，Vue文件: {}, JSON文件: {}, 其他文件: {}", 
                classification.getVueFile() != null ? 1 : 0,
                classification.getJsonFile() != null ? 1 : 0,
                classification.getOtherFiles().size());
        
        return classification;
    }

    /**
     * 处理Vue文件上传
     * 
     * @description 上传Vue文件到MinIO并创建文件记录
     * @param vueFile Vue文件
     * @param bucketName 存储桶名称
     * @param storagePath 存储路径
     * @param uploadUserId 上传用户ID
     * @return Vue文件上传信息
     */
    private UploadedVueFileInfo processVueFileUpload(MultipartFile vueFile, 
                                                    String bucketName, 
                                                    String storagePath, 
                                                    Long uploadUserId) throws Exception {
        
        String originalName = vueFile.getOriginalFilename();
        String fileName = generateUniqueFileName(originalName, storagePath);
        String objectKey = storagePath + "/" + fileName;
        
        log.info("开始上传Vue文件: {} -> {}", originalName, objectKey);

        // 上传文件到MinIO
        ObjectWriteResponse response = minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectKey)
                        .stream(vueFile.getInputStream(), vueFile.getSize(), -1)
                        .contentType(getFileContentType(vueFile))
                        .build()
        );

        // 生成文件URL
        String fileUrl = String.format("%s/%s/%s", minioProperties.getEndpoint(), bucketName, objectKey);

        // 创建文件上传记录
        FileUploadRecords fileRecord = createFileUploadRecord(vueFile, fileName, objectKey, 
                fileUrl, bucketName, response, uploadUserId);

        boolean saveResult = fileUploadRecordsService.save(fileRecord);
        if (!saveResult) {
            throw new BusinessException("Vue文件记录保存失败");
        }

        log.info("Vue文件上传成功: {}, 文件ID: {}", originalName, fileRecord.getId());

        return UploadedVueFileInfo.builder()
                .fileId(fileRecord.getId())
                .fileName(fileName)
                .filePath(objectKey)
                .fileUrl(fileUrl)
                .fileSize(vueFile.getSize())
                .build();
    }

    /**
     * 处理JSON内容上传
     * 
     * @description 解析JSON文件内容并存储到数据库
     * @param jsonFile JSON文件
     * @param request 上传请求
     * @param uploadUserId 上传用户ID
     * @return 模板内容对象
     */
    private ResumeTemplateContent processJsonContentUpload(MultipartFile jsonFile, 
                                                          TemplateUploadRequest request, 
                                                          Long uploadUserId) throws Exception {
        
        log.info("开始处理JSON内容文件: {}", jsonFile.getOriginalFilename());

        // 读取JSON文件内容
        String jsonContent = new String(jsonFile.getBytes(), StandardCharsets.UTF_8);
        
        // 验证JSON格式
        Object contentData;
        try {
            contentData = objectMapper.readValue(jsonContent, Object.class);
        } catch (Exception e) {
            throw new BusinessException("JSON文件格式错误: " + e.getMessage());
        }

        // 生成内容代码
        String contentCode = request.getTemplateCode() + "-content";
        
        // 检查内容代码唯一性
        if (!templateContentService.isCodeAvailable(contentCode)) {
            // 如果代码已存在，添加时间戳后缀
            contentCode = contentCode + "-" + System.currentTimeMillis();
        }

        // 创建模板内容对象
        ResumeTemplateContent templateContent = new ResumeTemplateContent();
        templateContent.setName(request.getTemplateName() + " - 默认内容");
        templateContent.setCode(contentCode);
        templateContent.setDescription("从" + jsonFile.getOriginalFilename() + "导入的默认内容");
        templateContent.setContentData(contentData);
        templateContent.setIndustry(request.getIndustry());
        templateContent.setLanguage(ResumeTemplateContent.DEFAULT_LANGUAGE);
        templateContent.setVersion(ResumeTemplateContent.DEFAULT_VERSION);
        templateContent.setStatus(ResumeTemplateContent.STATUS_ENABLED);
        templateContent.setCreateTime(LocalDateTime.now());
        templateContent.setUpdateTime(LocalDateTime.now());

        // 保存模板内容
        boolean saveResult = templateContentService.createContent(templateContent);
        if (!saveResult) {
            throw new BusinessException("JSON内容保存失败");
        }

        log.info("JSON内容处理成功，内容ID: {}, 内容代码: {}", 
                templateContent.getId(), templateContent.getCode());

        return templateContent;
    }

    /**
     * 处理其他文件上传
     * 
     * @description 上传其他文件到MinIO但不创建记录
     * @param otherFiles 其他文件列表
     * @param bucketName 存储桶名称
     * @param storagePath 存储路径
     * @return 其他文件的URL列表
     */
    private List<String> processOtherFilesUpload(List<MultipartFile> otherFiles, 
                                               String bucketName, 
                                               String storagePath) throws Exception {
        
        List<String> fileUrls = new ArrayList<>();
        
        for (MultipartFile file : otherFiles) {
            try {
                String originalName = file.getOriginalFilename();
                String fileName = generateUniqueFileName(originalName, storagePath);
                String objectKey = storagePath + "/" + fileName;
                
                log.debug("上传其他文件: {} -> {}", originalName, objectKey);

                // 上传文件到MinIO
                minioClient.putObject(
                        PutObjectArgs.builder()
                                .bucket(bucketName)
                                .object(objectKey)
                                .stream(file.getInputStream(), file.getSize(), -1)
                                .contentType(getFileContentType(file))
                                .build()
                );

                // 生成文件URL
                String fileUrl = String.format("%s/%s/%s", minioProperties.getEndpoint(), bucketName, objectKey);
                fileUrls.add(fileUrl);
                
                log.debug("其他文件上传成功: {}", originalName);
                
            } catch (Exception e) {
                log.warn("其他文件上传失败: {}, 错误: {}", file.getOriginalFilename(), e.getMessage());
                // 其他文件上传失败不影响主流程
            }
        }
        
        log.info("其他文件上传完成，成功: {}/{}", fileUrls.size(), otherFiles.size());
        return fileUrls;
    }

    /**
     * 创建模板记录
     * 
     * @description 创建模板数据库记录，关联Vue文件和JSON内容
     * @param request 上传请求
     * @param uploadUserId 上传用户ID
     * @param vueFileInfo Vue文件信息
     * @param templateContent 模板内容
     * @return 创建的模板记录
     */
    private ResumeTemplates createTemplateRecord(TemplateUploadRequest request, 
                                               Long uploadUserId,
                                               UploadedVueFileInfo vueFileInfo,
                                               ResumeTemplateContent templateContent) {
        
        ResumeTemplates template = new ResumeTemplates();
        template.setName(request.getTemplateName());
        template.setTemplateCode(request.getTemplateCode());
        template.setDescription(request.getDescription());
        template.setCategoryId(request.getCategoryId());
        template.setIndustry(request.getIndustry());
        template.setStyle(request.getStyle());
        template.setColorScheme(request.getColorScheme());
        template.setIsPremium(request.getIsPremium());
        template.setPrice(request.getPrice());
        template.setSortOrder(request.getSortOrder());
        template.setTags(request.getTags());
        template.setFeatures(request.getFeatures());
        template.setConfigData(request.getConfigData());
        
        // 设置预览图URL
        if (request.getSavePreviewImageUrl() != null && !request.getSavePreviewImageUrl().trim().isEmpty()) {
            template.setPreviewImageUrl(request.getSavePreviewImageUrl());
            log.info("设置模板预览图URL: {}", request.getSavePreviewImageUrl());
        }
        
        // 设置Vue文件信息
        if (vueFileInfo != null) {
            template.setTemplateFileId(vueFileInfo.getFileId());
            template.setVueFilePath(vueFileInfo.getFilePath());
        }
        
        // 设置内容关联
        if (templateContent != null) {
            template.setContentId(templateContent.getId());
        }
        
        template.setUseCount(0);
        template.setStatus((byte) 1); // 默认上架
        template.setIsDeleted((byte) 0);
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());

        boolean saveResult = resumeTemplatesService.save(template);
        if (!saveResult) {
            throw new BusinessException("创建模板记录失败");
        }

        // 处理分类关联
        if (request.getCategoryIds() != null && !request.getCategoryIds().isEmpty()) {
            boolean categoryResult = templateCategoriesService.batchCreateRelations(template.getId(), request.getCategoryIds());
            if (!categoryResult) {
                log.warn("创建模板分类关联失败，模板ID: {}, 分类ID: {}", template.getId(), request.getCategoryIds());
            } else {
                log.info("创建模板分类关联成功，模板ID: {}, 分类ID: {}", template.getId(), request.getCategoryIds());
            }
        }

        log.info("模板记录创建成功，模板ID: {}, 模板代码: {}, Vue文件ID: {}, 内容ID: {}", 
                template.getId(), template.getTemplateCode(), 
                vueFileInfo != null ? vueFileInfo.getFileId() : null,
                templateContent != null ? templateContent.getId() : null);
        
        return template;
    }

    /**
     * 构建上传响应结果
     * 
     * @description 构建完整的上传响应信息
     * @param template 模板记录
     * @param vueFileInfo Vue文件信息
     * @param templateContent 模板内容
     * @param otherFileUrls 其他文件URL列表
     * @param classification 文件分类
     * @param bucketName 存储桶名称
     * @param storagePath 存储路径
     * @return 上传响应
     */
    private TemplateUploadResponse buildUploadResponse(ResumeTemplates template,
                                                     UploadedVueFileInfo vueFileInfo,
                                                     ResumeTemplateContent templateContent,
                                                     List<String> otherFileUrls,
                                                     UploadFileClassification classification,
                                                     String bucketName,
                                                     String storagePath) {
        
        // 构建上传成功的文件信息
        List<TemplateUploadResponse.UploadedFileInfo> uploadedFiles = new ArrayList<>();
        
        // 添加Vue文件信息
        if (vueFileInfo != null) {
            uploadedFiles.add(TemplateUploadResponse.UploadedFileInfo.builder()
                    .fileId(vueFileInfo.getFileId())
                    .originalName(classification.getVueFile().getOriginalFilename())
                    .fileName(vueFileInfo.getFileName())
                    .fileType("text/vue")
                    .fileSize(vueFileInfo.getFileSize())
                    .fileUrl(vueFileInfo.getFileUrl())
                    .objectKey(vueFileInfo.getFilePath())
                    .build());
        }
        
        // 添加JSON内容信息（虽然不是文件，但作为处理结果）
        if (templateContent != null && classification.getJsonFile() != null) {
            uploadedFiles.add(TemplateUploadResponse.UploadedFileInfo.builder()
                    .fileId(templateContent.getId()) // 使用内容ID
                    .originalName(classification.getJsonFile().getOriginalFilename())
                    .fileName("content-data")
                    .fileType("application/json")
                    .fileSize(classification.getJsonFile().getSize())
                    .fileUrl("database-stored") // 标记为数据库存储
                    .objectKey("content-" + templateContent.getCode())
                    .build());
        }

        // 计算统计信息
        int totalFiles = (classification.getVueFile() != null ? 1 : 0) +
                        (classification.getJsonFile() != null ? 1 : 0) +
                        classification.getOtherFiles().size();
        
        int successCount = uploadedFiles.size() + otherFileUrls.size();

        return TemplateUploadResponse.builder()
                .templateId(template.getId())
                .templateName(template.getName())
                .templateCode(template.getTemplateCode())
                .uploadedFiles(uploadedFiles)
                .failedFiles(new ArrayList<>()) // 新逻辑下失败文件较少
                .totalFiles(totalFiles)
                .successCount(successCount)
                .failureCount(totalFiles - successCount)
                .mainTemplateFileId(vueFileInfo != null ? vueFileInfo.getFileId() : null)
                .contentId(templateContent != null ? templateContent.getId() : null)
                .otherFileUrls(otherFileUrls)
                .uploadTime(LocalDateTime.now())
                .uploadUserId(null) // 不在响应中暴露用户ID
                .bucketName(bucketName)
                .storagePath(storagePath)
                .build();
    }

    // ================================
    // 辅助方法和工具类
    // ================================

    /**
     * 创建文件上传记录
     * 
     * @description 创建文件上传的数据库记录
     * @param file 上传文件
     * @param fileName 文件名
     * @param objectKey 对象键
     * @param fileUrl 文件URL
     * @param bucketName 存储桶名称
     * @param response MinIO响应
     * @param uploadUserId 上传用户ID
     * @return 文件上传记录
     */
    private FileUploadRecords createFileUploadRecord(MultipartFile file, 
                                                   String fileName, 
                                                   String objectKey, 
                                                   String fileUrl, 
                                                   String bucketName, 
                                                   ObjectWriteResponse response, 
                                                   Long uploadUserId) {
        
        FileUploadRecords fileRecord = new FileUploadRecords();
        fileRecord.setUserId(uploadUserId);
        fileRecord.setFileName(fileName);
        fileRecord.setOriginalName(file.getOriginalFilename());
        fileRecord.setFilePath(objectKey);
        fileRecord.setFileUrl(fileUrl);
        fileRecord.setFileSize(file.getSize());
        fileRecord.setFileType(getFileContentType(file));
        fileRecord.setBucketName(bucketName);
        fileRecord.setObjectKey(objectKey);
        fileRecord.setEtag(response.etag());
        fileRecord.setVersionId(response.versionId());
        fileRecord.setStorageClass("STANDARD");
        fileRecord.setAccessPolicy("private");
        fileRecord.setUploadPlatform("admin");
        fileRecord.setIsDeleted((byte) 0);
        fileRecord.setCreateTime(LocalDateTime.now());
        fileRecord.setUpdateTime(LocalDateTime.now());
        
        return fileRecord;
    }

    /**
     * 生成唯一文件名
     * 
     * @description 为避免文件名冲突，生成带时间戳的唯一文件名
     * @param originalName 原始文件名
     * @param storagePath 存储路径
     * @return 唯一文件名
     */
    private String generateUniqueFileName(String originalName, String storagePath) {
        if (originalName == null) {
            originalName = "unknown";
        }

        String extension = "";
        int lastDotIndex = originalName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = originalName.substring(lastDotIndex);
            originalName = originalName.substring(0, lastDotIndex);
        }

        // 生成时间戳后缀，确保文件名唯一
        String timestamp = String.valueOf(System.currentTimeMillis());
        return originalName + "_" + timestamp + extension;
    }

    /**
     * 获取文件的正确MIME类型
     * 
     * @description 根据文件扩展名获取正确的MIME类型
     * @param file 上传文件
     * @return MIME类型
     */
    private String getFileContentType(MultipartFile file) {
        String originalName = file.getOriginalFilename();
        if (originalName != null) {
            String extension = originalName.substring(originalName.lastIndexOf('.')).toLowerCase();
            if (EXTENSION_MIME_MAP.containsKey(extension)) {
                return EXTENSION_MIME_MAP.get(extension);
            }
        }
        return file.getContentType();
    }

    // ================================
    // 接口实现方法
    // ================================

    @Override
    public boolean isTemplateCodeAvailable(String templateCode) {
        if (templateCode == null || templateCode.trim().isEmpty()) {
            return false;
        }

        long count = resumeTemplatesService.lambdaQuery()
                .eq(ResumeTemplates::getTemplateCode, templateCode.trim())
                .eq(ResumeTemplates::getIsDeleted, 0)
                .count();

        return count == 0;
    }

    @Override
    public boolean isTemplateNameAvailable(String templateName) {
        if (templateName == null || templateName.trim().isEmpty()) {
            return false;
        }

        long count = resumeTemplatesService.lambdaQuery()
                .eq(ResumeTemplates::getName, templateName.trim())
                .eq(ResumeTemplates::getIsDeleted, 0)
                .count();

        return count == 0;
    }

    @Override
    public boolean isFileTypeSupported(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        String contentType = getFileContentType(file);
        return SUPPORTED_FILE_TYPES.contains(contentType);
    }

    @Override
    public String generatePreviewImageUrl(List<MultipartFile> files, String bucketName, String templateCode) {
        // 查找上传的图片文件作为预览图
        Optional<MultipartFile> imageFile = files.stream()
                .filter(this::isImageFile)
                .filter(file -> {
                    String fileName = file.getOriginalFilename();
                    return fileName != null && (fileName.toLowerCase().contains("preview") || 
                                              fileName.toLowerCase().contains("thumb"));
                })
                .findFirst();

        if (imageFile.isPresent()) {
            // 生成预览图URL
            String fileName = imageFile.get().getOriginalFilename();
            return String.format("%s/%s/%s/%s", minioProperties.getEndpoint(), bucketName, templateCode, fileName);
        }

        return null; // 没有找到预览图
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanupFailedUpload(Long templateId, List<Long> uploadedFileIds) {
        log.info("开始清理失败的上传记录，模板ID: {}, 文件ID列表: {}", templateId, uploadedFileIds);

        try {
            // 删除模板记录
            if (templateId != null) {
                resumeTemplatesService.removeById(templateId);
                log.info("删除模板记录成功，ID: {}", templateId);
            }

            // 删除文件记录
            if (uploadedFileIds != null && !uploadedFileIds.isEmpty()) {
                fileUploadRecordsService.removeByIds(uploadedFileIds);
                log.info("删除文件记录成功，数量: {}", uploadedFileIds.size());
            }

        } catch (Exception e) {
            log.error("清理失败的上传记录异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean ensureBucketExists(String bucketName) {
        try {
            // 检查桶是否存在
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            
            if (!exists) {
                // 创建桶
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("创建存储桶成功: {}", bucketName);
            }
            
            return true;
        } catch (Exception e) {
            log.error("检查或创建存储桶失败: {}, 错误: {}", bucketName, e.getMessage());
            return false;
        }
    }

    @Override
    public String buildTemplatePath(String templateCode, String localPath) {
        // 规范化路径
        if (localPath == null || localPath.trim().isEmpty()) {
            localPath = "/templates";
        }
        
        // 确保路径以/开头，不以/结尾
        if (!localPath.startsWith("/")) {
            localPath = "/" + localPath;
        }
        if (localPath.endsWith("/")) {
            localPath = localPath.substring(0, localPath.length() - 1);
        }

        return localPath + "/" + templateCode;
    }

    /**
     * 判断是否为图片文件
     * 
     * @param file 上传文件
     * @return true表示是图片文件
     */
    private boolean isImageFile(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            return false;
        }

        String extension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
        return IMAGE_EXTENSIONS.contains(extension);
    }

    // ================================
    // 内部数据类
    // ================================

    /**
     * 文件分类结果
     */
    private static class UploadFileClassification {
        private MultipartFile vueFile;
        private MultipartFile jsonFile;
        private List<MultipartFile> otherFiles = new ArrayList<>();

        public MultipartFile getVueFile() { return vueFile; }
        public void setVueFile(MultipartFile vueFile) { this.vueFile = vueFile; }
        public MultipartFile getJsonFile() { return jsonFile; }
        public void setJsonFile(MultipartFile jsonFile) { this.jsonFile = jsonFile; }
        public List<MultipartFile> getOtherFiles() { return otherFiles; }
        public void setOtherFiles(List<MultipartFile> otherFiles) { this.otherFiles = otherFiles; }
    }

    /**
     * Vue文件上传信息
     */
    private static class UploadedVueFileInfo {
        private Long fileId;
        private String fileName;
        private String filePath;
        private String fileUrl;
        private Long fileSize;

        public static UploadedVueFileInfoBuilder builder() {
            return new UploadedVueFileInfoBuilder();
        }

        public Long getFileId() { return fileId; }
        public String getFileName() { return fileName; }
        public String getFilePath() { return filePath; }
        public String getFileUrl() { return fileUrl; }
        public Long getFileSize() { return fileSize; }

        public static class UploadedVueFileInfoBuilder {
            private Long fileId;
            private String fileName;
            private String filePath;
            private String fileUrl;
            private Long fileSize;

            public UploadedVueFileInfoBuilder fileId(Long fileId) {
                this.fileId = fileId;
                return this;
            }

            public UploadedVueFileInfoBuilder fileName(String fileName) {
                this.fileName = fileName;
                return this;
            }

            public UploadedVueFileInfoBuilder filePath(String filePath) {
                this.filePath = filePath;
                return this;
            }

            public UploadedVueFileInfoBuilder fileUrl(String fileUrl) {
                this.fileUrl = fileUrl;
                return this;
            }

            public UploadedVueFileInfoBuilder fileSize(Long fileSize) {
                this.fileSize = fileSize;
                return this;
            }

            public UploadedVueFileInfo build() {
                UploadedVueFileInfo info = new UploadedVueFileInfo();
                info.fileId = this.fileId;
                info.fileName = this.fileName;
                info.filePath = this.filePath;
                info.fileUrl = this.fileUrl;
                info.fileSize = this.fileSize;
                return info;
            }
        }
    }

    /**
     * 获取模板预览图数据
     * 
     * @description 从MinIO获取模板预览图的二进制数据
     * @param bucketName 存储桶名称
     * @param previewImageUrl 预览图文件路径
     * @return 预览图的二进制数据，如果失败则返回null
     */
    @Override
    public byte[] getPreviewImageData(String bucketName, String previewImageUrl) {
        if (bucketName == null || bucketName.trim().isEmpty() || 
            previewImageUrl == null || previewImageUrl.trim().isEmpty()) {
            log.warn("获取预览图数据失败: 桶名或文件路径为空 - bucketName: {}, previewImageUrl: {}", 
                     bucketName, previewImageUrl);
            return null;
        }

        try {
            // 使用MinIO客户端获取文件对象
            var response = minioClient.getObject(
                io.minio.GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(previewImageUrl)
                    .build()
            );

            // 读取所有字节数据
            byte[] imageData = response.readAllBytes();
            
            // 关闭输入流
            response.close();
            
            log.info("成功获取预览图数据: bucket={}, path={}, size={} bytes", 
                     bucketName, previewImageUrl, imageData.length);
            
            return imageData;

        } catch (Exception e) {
            log.error("从MinIO获取预览图数据失败: bucket={}, path={}", 
                      bucketName, previewImageUrl, e);
            return null;
        }
    }

    /**
     * 处理预览图上传
     * 
     * @description 将预览图上传到MinIO并返回访问路径
     * @param previewImage 预览图文件
     * @param bucketName 存储桶名称
     * @param storagePath 存储路径前缀
     * @param templateCode 模板代码
     * @return 预览图的存储路径，失败时返回null
     */
    private String processPreviewImageUpload(MultipartFile previewImage, 
                                           String bucketName, 
                                           String storagePath, 
                                           String templateCode) {
        if (previewImage == null || previewImage.isEmpty()) {
            return null;
        }

        try {
            // 1. 生成预览图文件名
            String originalFilename = previewImage.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            
            String previewFileName = templateCode + "_preview" + fileExtension;
            String previewFilePath = storagePath + "/" + previewFileName;

            // 2. 上传到MinIO
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(previewFilePath)
                    .stream(previewImage.getInputStream(), previewImage.getSize(), -1)
                    .contentType(previewImage.getContentType())
                    .build();

            ObjectWriteResponse response = minioClient.putObject(putObjectArgs);
            
            log.info("预览图上传成功: bucket={}, path={}, etag={}", 
                     bucketName, previewFilePath, response.etag());
            
            return previewFilePath;

        } catch (Exception e) {
            log.error("预览图上传失败: templateCode={}, filename={}", 
                      templateCode, previewImage.getOriginalFilename(), e);
            return null;
        }
    }
} 