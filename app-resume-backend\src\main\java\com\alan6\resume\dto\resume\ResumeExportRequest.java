package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * 简历导出请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(name = "ResumeExportRequest", description = "简历导出请求")
public class ResumeExportRequest {

    /**
     * 简历ID
     */
    @NotNull(message = "简历ID不能为空")
    @Schema(description = "简历ID", required = true)
    private Long resumeId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", required = true)
    private Long userId;

    /**
     * 导出格式（必填，pdf/word/image）
     */
    @NotBlank(message = "导出格式不能为空")
    @Pattern(regexp = "^(pdf|word|image|png|jpg)$", message = "导出格式只能是pdf、word、image、png或jpg")
    @Schema(description = "导出格式", example = "pdf", allowableValues = {"pdf", "word", "image", "png", "jpg"})
    private String format;

    /**
     * 优先级
     */
    @Schema(description = "优先级（1-10，数字越小优先级越高）", defaultValue = "5")
    private Integer priority;

    /**
     * 导出数据
     */
    @Schema(description = "导出数据")
    private Object exportData;

    /**
     * 使用的模板ID（可选，不填使用当前模板）
     */
    @Schema(description = "使用的模板ID", example = "3001")
    private Long templateId;

    /**
     * 是否添加水印（可选，默认false）
     */
    @Schema(description = "是否添加水印", example = "false")
    private Boolean watermark = false;

    /**
     * 导出质量（可选，low/medium/high，默认medium）
     */
    @Pattern(regexp = "^(low|medium|high)$", message = "导出质量只能是low、medium或high")
    @Schema(description = "导出质量", example = "high", allowableValues = {"low", "medium", "high"})
    private String quality = "medium";

    /**
     * 导出选项
     */
    @Schema(description = "导出选项")
    private ExportOptions options;

    /**
     * 导出选项内部类
     */
    @Data
    @Schema(description = "导出选项")
    public static class ExportOptions {
        /**
         * 页面大小
         */
        @Schema(description = "页面大小（A4/Letter等）", defaultValue = "A4")
        private String pageSize = "A4";

        /**
         * 页面方向
         */
        @Schema(description = "页面方向（portrait/landscape）", defaultValue = "portrait")
        private String orientation = "portrait";
    }
} 