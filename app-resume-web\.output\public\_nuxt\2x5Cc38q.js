import{_ as p}from"./BXnw39BI.js";import{S as v,c as f,a as o,t as u,U as a,b as s,w as e,o as _,d as i}from"./CURHyiUL.js";import{_ as b}from"./DlAUqK2U.js";const g={class:"route-test"},k={class:"current-route"},$={class:"navigation-test"},y={class:"nav-buttons"},N={class:"direct-links"},C={__name:"route-test",setup(S){const m=v(),r=async l=>{console.log("导航到:",l);try{await m.push(l)}catch(t){console.error("导航失败:",t)}};return(l,t)=>{const n=p;return _(),f("div",g,[t[12]||(t[12]=o("h1",null,"路由测试页面",-1)),o("div",k,[t[4]||(t[4]=o("h2",null,"当前路由信息",-1)),o("ul",null,[o("li",null,"路径: "+u((l._.provides[a]||l.$route).path),1),o("li",null,"名称: "+u((l._.provides[a]||l.$route).name),1),o("li",null,"参数: "+u(JSON.stringify((l._.provides[a]||l.$route).params)),1),o("li",null,"查询: "+u(JSON.stringify((l._.provides[a]||l.$route).query)),1)])]),o("div",$,[t[5]||(t[5]=o("h2",null,"导航测试",-1)),o("div",y,[o("button",{onClick:t[0]||(t[0]=d=>r("/admin")),class:"btn"},"管理首页"),o("button",{onClick:t[1]||(t[1]=d=>r("/admin/template/list")),class:"btn"},"模板列表"),o("button",{onClick:t[2]||(t[2]=d=>r("/admin/template/converter")),class:"btn"},"模板转换"),o("button",{onClick:t[3]||(t[3]=d=>r("/admin/template/upload")),class:"btn"},"模板上传")])]),o("div",N,[t[11]||(t[11]=o("h2",null,"直接链接测试",-1)),o("ul",null,[o("li",null,[s(n,{to:"/admin"},{default:e(()=>t[6]||(t[6]=[i("管理首页")])),_:1,__:[6]})]),o("li",null,[s(n,{to:"/admin/template/list"},{default:e(()=>t[7]||(t[7]=[i("模板列表")])),_:1,__:[7]})]),o("li",null,[s(n,{to:"/admin/template/converter"},{default:e(()=>t[8]||(t[8]=[i("模板转换")])),_:1,__:[8]})]),o("li",null,[s(n,{to:"/admin/template/upload"},{default:e(()=>t[9]||(t[9]=[i("模板上传")])),_:1,__:[9]})]),o("li",null,[s(n,{to:"/admin/template/categories"},{default:e(()=>t[10]||(t[10]=[i("分类管理")])),_:1,__:[10]})])])])])}}},V=b(C,[["__scopeId","data-v-0493ffdb"]]);export{V as default};
