const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./CKDEb6Y1.js","./CURHyiUL.js","./D1FrdRFX.js"])))=>i.map(i=>d[i]);
import{r as v,P as g,ad as y}from"./CURHyiUL.js";const k=()=>{const m=v(!1),c=v(0),s=v(null),I=()=>{const e=Date.now(),n=Math.random().toString(36).substr(2,9);return`export_${e}_${n}`},E=async e=>{for(let l=0;l<15;l++)try{let a=null;try{const{useAuthService:o}=await y(async()=>{const{useAuthService:d}=await import("./CKDEb6Y1.js").then(p=>p.a);return{useAuthService:d}},__vite__mapDeps([0,1,2]),import.meta.url);a=o().getToken()}catch(o){console.warn("无法获取认证服务，将以匿名模式请求:",o)}const r={};a&&(r.Authorization=`Bearer ${a}`);const i=await $fetch(`/api/resume/export/status/${e}`,{method:"GET",headers:r});if(i.code===200){const o=i.data;if(c.value=o.progress||0,o.status==="completed")return o;if(o.status==="failed")throw new Error(o.errorMessage||"导出失败");await new Promise(u=>setTimeout(u,2e3))}else throw new Error(i.message||"获取导出状态失败")}catch(a){if(console.error(`轮询导出状态失败 (尝试 ${l+1}/15):`,a),l===14)throw a;await new Promise(r=>setTimeout(r,2e3))}throw new Error("导出超时，请稍后重试")},D=(e,n)=>{const t=document.createElement("a");t.href=e,t.download=n,t.style.display="none",document.body.appendChild(t),t.click(),document.body.removeChild(t)},w=async e=>{const{resumeId:n,format:t,resumeData:l,settings:a}=e;if(!n)throw new Error("简历ID不能为空");if(!t)throw new Error("下载格式不能为空");if(!["pdf","word","image"].includes(t))throw new Error("不支持的下载格式");try{m.value=!0,c.value=0;const r=I();s.value=r,console.log(`开始下载任务，任务ID: ${r}，格式: ${t}`);const i={resumeId:parseInt(n),userId:1,format:t,priority:5,quality:"high",watermark:!1,exportData:{resumeData:l,settings:a,jobId:r},options:{pageSize:"A4",orientation:"portrait"}};let o=null;try{const{useAuthService:f}=await y(async()=>{const{useAuthService:x}=await import("./CKDEb6Y1.js").then(b=>b.a);return{useAuthService:x}},__vite__mapDeps([0,1,2]),import.meta.url);o=f().getToken()}catch(f){console.warn("无法获取认证服务，将以匿名模式请求:",f)}const u={"Content-Type":"application/json"};o&&(u.Authorization=`Bearer ${o}`);const d=await $fetch("/api/resume/export",{method:"POST",headers:u,body:i});if(d.code!==200)throw new Error(d.message||"提交导出任务失败");const p=d.data;console.log("导出任务提交成功:",p);const h=await E(p.jobId);if(h.downloadUrl)D(h.downloadUrl,h.fileName),console.log(`${t.toUpperCase()}下载完成:`,h.fileName);else throw new Error("未获取到下载链接")}catch(r){throw console.error("下载失败:",r),r.statusCode===401?new Error("请先登录后再进行下载操作"):r}finally{m.value=!1,c.value=0,s.value=null}},_=async e=>w({...e,format:"pdf"}),A=async e=>w({...e,format:"word"}),S=async e=>w({...e,format:"image"}),$=async()=>{if(s.value)try{console.log(`取消下载任务: ${s.value}`)}catch(e){console.error("取消下载任务失败:",e)}finally{m.value=!1,c.value=0,s.value=null}};return{isDownloading:g(m),downloadProgress:g(c),currentJobId:g(s),downloadResume:w,downloadPDF:_,downloadWord:A,downloadImage:S,cancelDownload:$}};export{k as u};
