-- 管理员测试数据初始化脚本
-- 使用此脚本手动插入测试数据

-- 1. 插入角色数据
INSERT IGNORE INTO roles (id, role_name, role_code, description, sort_order, status, create_time, update_time) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, 1, NOW(), NOW()),
(2, '管理员', 'ADMIN', '系统管理员，拥有大部分管理权限', 2, 1, NOW(), NOW()),
(3, '普通用户', 'USER', '普通用户，只能使用基础功能', 3, 1, NOW(), NOW());

-- 2. 插入权限数据
INSERT IGNORE INTO permissions (id, permission_name, permission_code, resource_type, resource_url, parent_id, sort_order, status, create_time, update_time) VALUES
(1, '后台管理', 'admin', 'menu', '/admin', 0, 1, 1, NOW(), NOW()),
(2, '用户管理', 'admin:user', 'menu', '/admin/users', 1, 2, 1, NOW(), NOW()),
(3, '模板管理', 'admin:template', 'menu', '/admin/templates', 1, 3, 1, NOW(), NOW()),
(4, '订单管理', 'admin:order', 'menu', '/admin/orders', 1, 4, 1, NOW(), NOW()),
(5, '系统设置', 'admin:system', 'menu', '/admin/system', 1, 5, 1, NOW(), NOW());

-- 3. 插入测试用户（如果不存在）
-- 密码使用BCrypt加密，明文密码为123456
INSERT IGNORE INTO users (phone, username, password, nickname, status, register_platform, create_time, update_time) VALUES
('13800138001', 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIgBTNjw6DFVV2.6x9W3eTYVzS', '系统管理员', 1, 'web', NOW(), NOW()),
('13800138002', 'superadmin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIgBTNjw6DFVV2.6x9W3eTYVzS', '超级管理员', 1, 'web', NOW(), NOW());

-- 4. 获取用户ID并分配角色
SET @admin_user_id = (SELECT id FROM users WHERE phone = '13800138001' LIMIT 1);
SET @superadmin_user_id = (SELECT id FROM users WHERE phone = '13800138002' LIMIT 1);

-- 5. 插入用户角色关联（先删除可能存在的记录）
DELETE FROM user_roles WHERE user_id = @admin_user_id;
DELETE FROM user_roles WHERE user_id = @superadmin_user_id;

INSERT INTO user_roles (user_id, role_id, create_time, update_time) VALUES
(@admin_user_id, 2, NOW(), NOW()),  -- 管理员角色
(@superadmin_user_id, 1, NOW(), NOW());  -- 超级管理员角色

-- 6. 插入角色权限关联（先删除可能存在的记录）
DELETE FROM role_permissions WHERE role_id IN (1, 2);

INSERT INTO role_permissions (role_id, permission_id, create_time, update_time) VALUES
-- 超级管理员拥有所有权限
(1, 1, NOW(), NOW()),
(1, 2, NOW(), NOW()),
(1, 3, NOW(), NOW()),
(1, 4, NOW(), NOW()),
(1, 5, NOW(), NOW()),
-- 管理员拥有除系统设置外的权限
(2, 1, NOW(), NOW()),
(2, 2, NOW(), NOW()),
(2, 3, NOW(), NOW()),
(2, 4, NOW(), NOW());

-- 验证数据
SELECT '=== 角色数据 ===' as info;
SELECT * FROM roles;

SELECT '=== 权限数据 ===' as info;
SELECT * FROM permissions;

SELECT '=== 测试用户 ===' as info;
SELECT id, phone, username, nickname FROM users WHERE phone IN ('13800138001', '13800138002');

SELECT '=== 用户角色关联 ===' as info;
SELECT ur.user_id, u.phone, u.nickname, r.role_name, r.role_code 
FROM user_roles ur
JOIN users u ON ur.user_id = u.id
JOIN roles r ON ur.role_id = r.id
WHERE u.phone IN ('13800138001', '13800138002');

SELECT '=== 角色权限关联 ===' as info;
SELECT rp.role_id, r.role_name, p.permission_name, p.permission_code
FROM role_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
ORDER BY r.sort_order, p.sort_order; 