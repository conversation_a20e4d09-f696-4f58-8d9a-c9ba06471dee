package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.common.utils.RedisUtils;
import com.alan6.resume.dto.resume.*;
import com.alan6.resume.entity.Resumes;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.entity.ResumeExportQueue;
import com.alan6.resume.mapper.ResumesMapper;
import com.alan6.resume.mapper.ResumeTemplatesMapper;
import com.alan6.resume.mapper.ResumeExportQueueMapper;
import com.alan6.resume.service.IResumeService;
import com.alan6.resume.service.IResumeExportService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 简历服务实现类
 * 
 * @description 实现简历管理的核心业务逻辑
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeServiceImpl extends ServiceImpl<ResumesMapper, Resumes> implements IResumeService {

    /**
     * 简历Mapper
     */
    private final ResumesMapper resumesMapper;

    /**
     * 模板Mapper
     */
    private final ResumeTemplatesMapper templatesMapper;

    /**
     * 导出队列Mapper
     */
    private final ResumeExportQueueMapper exportQueueMapper;

    /**
     * Redis工具类
     */
    private final RedisUtils redisUtils;

    /**
     * 简历导出服务
     */
    private final IResumeExportService resumeExportService;

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;

    /**
     * 导出状态缓存前缀
     */
    private static final String EXPORT_STATE_PREFIX = "export_state:";

    /**
     * 导出状态过期时间（30分钟）
     */
    private static final long EXPORT_STATE_EXPIRE = 30 * 60;

    /**
     * 创建新简历
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createResume(ResumeSaveRequest request) {
        log.info("开始创建简历，用户ID：{}，模板ID：{}", request.getUserId(), request.getTemplateId());

        try {
            // 1. 验证模板是否存在
            if (request.getTemplateId() != null) {
                ResumeTemplates template = templatesMapper.selectById(request.getTemplateId());
                if (template == null) {
                    throw new BusinessException("模板不存在");
                }
            }

            // 2. 创建MySQL记录
            Resumes resume = new Resumes();
            resume.setUserId(request.getUserId());
            resume.setTemplateId(request.getTemplateId());
            resume.setName(request.getName());
            resume.setLanguage(request.getLanguage());
            resume.setStatus(request.getStatus().byteValue());
            resume.setIsPublic((byte) 0);
            resume.setViewCount(0);
            resume.setDownloadCount(0);
            resume.setShareCount(0);
            resume.setIsDeleted((byte) 0);

            resumesMapper.insert(resume);
            log.info("MySQL简历记录创建成功，简历ID：{}", resume.getId());

            // 3. TODO: 保存到MongoDB
            String mongoDocumentId = saveResumeDataToMongoDB(resume.getId(), request);
            
            // 4. 更新MySQL记录的MongoDB文档ID
            resume.setMongoDocumentId(mongoDocumentId);
            resumesMapper.updateById(resume);

            log.info("简历创建成功，简历ID：{}，MongoDB文档ID：{}", resume.getId(), mongoDocumentId);
            return resume.getId();

        } catch (Exception e) {
            log.error("创建简历失败", e);
            throw new BusinessException("创建简历失败：" + e.getMessage());
        }
    }

    /**
     * 保存简历数据到MongoDB（临时实现）
     */
    private String saveResumeDataToMongoDB(Long resumeId, ResumeSaveRequest request) {
        // TODO: 实际保存到MongoDB
        // 这里临时返回一个生成的ID
        String mongoId = "mongo_" + resumeId + "_" + System.currentTimeMillis();
        
        // 将数据暂存到Redis
        String key = "resume_data:" + resumeId;
        redisUtils.set(key, request, 24 * 60 * 60); // 24小时过期
        
        return mongoId;
    }

    /**
     * 更新简历
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateResume(Long id, ResumeSaveRequest request) {
        log.info("开始更新简历，简历ID：{}", id);

        try {
            // 1. 验证简历是否存在
            Resumes resume = resumesMapper.selectById(id);
            if (resume == null || resume.getIsDeleted() == 1) {
                throw new BusinessException("简历不存在或已删除");
            }

            // 2. 验证权限
            if (!resume.getUserId().equals(request.getUserId())) {
                throw new BusinessException("无权限修改此简历");
            }

            // 3. 更新MySQL记录
            resume.setName(request.getName());
            resume.setLanguage(request.getLanguage());
            resume.setStatus(request.getStatus().byteValue());
            resume.setUpdateTime(LocalDateTime.now());
            resumesMapper.updateById(resume);

            // 4. TODO: 更新MongoDB文档
            saveResumeDataToMongoDB(id, request);

            log.info("简历更新成功，简历ID：{}", id);

        } catch (Exception e) {
            log.error("更新简历失败，简历ID：{}", id, e);
            throw new BusinessException("更新简历失败：" + e.getMessage());
        }
    }

    /**
     * 获取简历详情
     */
    @Override
    public ResumeDetailResponse getResumeDetail(Long id) {
        log.info("获取简历详情，简历ID：{}", id);

        try {
            // 1. 获取MySQL记录
            Resumes resume = resumesMapper.selectById(id);
            if (resume == null || resume.getIsDeleted() == 1) {
                throw new BusinessException("简历不存在或已删除");
            }

            // 2. TODO: 从MongoDB获取完整数据
            // 临时从Redis获取
            String key = "resume_data:" + id;
            ResumeSaveRequest savedData = (ResumeSaveRequest) redisUtils.get(key);

            // 3. 构建响应
            ResumeDetailResponse response = buildDetailResponse(resume, savedData);

            // 4. 获取模板信息
            if (resume.getTemplateId() != null) {
                ResumeTemplates template = templatesMapper.selectById(resume.getTemplateId());
                if (template != null) {
                    response.setTemplateInfo(buildTemplateInfo(template));
                }
            }

            return response;

        } catch (Exception e) {
            log.error("获取简历详情失败，简历ID：{}", id, e);
            throw new BusinessException("获取简历详情失败：" + e.getMessage());
        }
    }

    /**
     * 构建详情响应
     */
    private ResumeDetailResponse buildDetailResponse(Resumes resume, ResumeSaveRequest savedData) {
        ResumeDetailResponse response = new ResumeDetailResponse();
        
        // 基础信息
        response.setId(resume.getId());
        response.setUserId(resume.getUserId());
        response.setTemplateId(resume.getTemplateId());
        response.setName(resume.getName());
        response.setLanguage(resume.getLanguage());
        response.setStatus(resume.getStatus().intValue());
        response.setIsPublic(resume.getIsPublic().intValue());
        response.setViewCount(resume.getViewCount());
        response.setDownloadCount(resume.getDownloadCount());
        response.setShareCount(resume.getShareCount());
        response.setCreateTime(resume.getCreateTime());
        response.setUpdateTime(resume.getUpdateTime());
        response.setLastSaved(resume.getUpdateTime());
        response.setVersion(1L);

        // 如果有保存的数据，设置模块和全局设置
        if (savedData != null) {
            response.setModules(savedData.getModules());
            response.setGlobalSettings(savedData.getGlobalSettings());
        }

        return response;
    }

    /**
     * 构建模板信息
     */
    private ResumeDetailResponse.TemplateInfo buildTemplateInfo(ResumeTemplates template) {
        return ResumeDetailResponse.TemplateInfo.builder()
                .id(template.getId())
                .name(template.getName())
                .thumbnail(template.getPreviewImageUrl())
                .type(template.getIndustry())
                .style(template.getStyle())
                .build();
    }

    /**
     * 获取用户简历列表
     */
    @Override
    public List<ResumeListResponse> getResumeList(Long userId, Integer status) {
        log.info("获取用户简历列表，用户ID：{}，状态：{}", userId, status);

        try {
            // 构建查询条件
            LambdaQueryWrapper<Resumes> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Resumes::getUserId, userId)
                    .eq(Resumes::getIsDeleted, 0);
            
            if (status != null) {
                wrapper.eq(Resumes::getStatus, status.byteValue());
            }
            
            wrapper.orderByDesc(Resumes::getUpdateTime);

            // 查询列表
            List<Resumes> resumeList = resumesMapper.selectList(wrapper);

            // 转换响应
            return resumeList.stream()
                    .map(this::buildListResponse)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取简历列表失败，用户ID：{}", userId, e);
            throw new BusinessException("获取简历列表失败：" + e.getMessage());
        }
    }

    /**
     * 构建列表响应
     */
    private ResumeListResponse buildListResponse(Resumes resume) {
        ResumeListResponse response = new ResumeListResponse();
        
        // 基础信息
        response.setId(resume.getId());
        response.setName(resume.getName());
        response.setTemplateId(resume.getTemplateId());
        response.setLanguage(resume.getLanguage());
        response.setStatus(resume.getStatus().intValue());
        response.setIsPublic(resume.getIsPublic().intValue());
        response.setViewCount(resume.getViewCount());
        response.setDownloadCount(resume.getDownloadCount());
        response.setShareCount(resume.getShareCount());
        response.setCreateTime(resume.getCreateTime());
        response.setUpdateTime(resume.getUpdateTime());
        response.setLastSaved(resume.getUpdateTime());

        // 获取模板信息
        if (resume.getTemplateId() != null) {
            ResumeTemplates template = templatesMapper.selectById(resume.getTemplateId());
            if (template != null) {
                response.setTemplateName(template.getName());
                response.setTemplateThumbnail(template.getPreviewImageUrl());
            }
        }

        // 计算完成度
        response.setCompleteness(calculateCompleteness(resume.getId()));

        return response;
    }

    /**
     * 删除简历（软删除）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteResume(Long id) {
        log.info("删除简历，简历ID：{}", id);

        try {
            Resumes resume = resumesMapper.selectById(id);
            if (resume == null) {
                throw new BusinessException("简历不存在");
            }

            // 软删除
            resume.setIsDeleted((byte) 1);
            resume.setUpdateTime(LocalDateTime.now());
            resumesMapper.updateById(resume);

            log.info("简历删除成功，简历ID：{}", id);

        } catch (Exception e) {
            log.error("删除简历失败，简历ID：{}", id, e);
            throw new BusinessException("删除简历失败：" + e.getMessage());
        }
    }

    /**
     * 复制简历
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyResume(Long sourceId, String newName) {
        log.info("复制简历，源简历ID：{}，新名称：{}", sourceId, newName);

        try {
            // 1. 获取源简历
            Resumes sourceResume = resumesMapper.selectById(sourceId);
            if (sourceResume == null || sourceResume.getIsDeleted() == 1) {
                throw new BusinessException("源简历不存在或已删除");
            }

            // 2. 创建新简历
            Resumes newResume = new Resumes();
            newResume.setUserId(sourceResume.getUserId());
            newResume.setTemplateId(sourceResume.getTemplateId());
            newResume.setName(newName);
            newResume.setLanguage(sourceResume.getLanguage());
            newResume.setParentResumeId(sourceId);
            newResume.setStatus(sourceResume.getStatus());
            newResume.setIsPublic((byte) 0);
            newResume.setViewCount(0);
            newResume.setDownloadCount(0);
            newResume.setShareCount(0);
            newResume.setIsDeleted((byte) 0);
            
            resumesMapper.insert(newResume);

            // 3. TODO: 复制MongoDB数据
            // 临时从Redis复制
            String sourceKey = "resume_data:" + sourceId;
            String newKey = "resume_data:" + newResume.getId();
            Object sourceData = redisUtils.get(sourceKey);
            if (sourceData != null) {
                redisUtils.set(newKey, sourceData, 24 * 60 * 60);
            }

            log.info("简历复制成功，新简历ID：{}", newResume.getId());
            return newResume.getId();

        } catch (Exception e) {
            log.error("复制简历失败，源简历ID：{}", sourceId, e);
            throw new BusinessException("复制简历失败：" + e.getMessage());
        }
    }

    /**
     * 导出简历
     */
    @Override
    public ResumeExportResponse exportResume(ResumeExportRequest request) {
        log.info("提交简历导出任务，简历ID：{}，格式：{}", request.getResumeId(), request.getFormat());
        
        try {
            // 委托给导出服务处理
            return resumeExportService.submitExportJob(request);
        } catch (Exception e) {
            log.error("提交导出任务失败", e);
            throw new BusinessException("提交导出任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取导出状态
     */
    @Override
    public ResumeExportResponse getExportStatus(String jobId) {
        log.info("查询导出状态，任务ID：{}", jobId);
        
        try {
            return resumeExportService.getExportStatus(jobId);
        } catch (Exception e) {
            log.error("查询导出状态失败，任务ID：{}", jobId, e);
            throw new BusinessException("查询导出状态失败：" + e.getMessage());
        }
    }

    /**
     * 保存导出状态
     */
    @Override
    public String saveExportState(ResumePreviewRequest request) {
        log.info("保存导出状态，简历ID：{}", request.getResumeId());

        try {
            // 生成状态ID
            String stateId = UUID.randomUUID().toString();
            String cacheKey = EXPORT_STATE_PREFIX + stateId;

            // 保存到Redis
            redisUtils.set(cacheKey, request, EXPORT_STATE_EXPIRE);

            log.info("导出状态保存成功，状态ID：{}", stateId);
            return stateId;

        } catch (Exception e) {
            log.error("保存导出状态失败", e);
            throw new BusinessException("保存导出状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取导出数据
     */
    @Override
    public ResumePreviewResponse getExportData(String jobId) {
        log.info("获取导出数据，任务ID：{}", jobId);

        try {
            // 从导出队列获取任务信息
            ResumeExportQueue exportJob = exportQueueMapper.selectByJobId(jobId);
            if (exportJob == null) {
                throw new BusinessException("导出任务不存在");
            }

            // 构建响应
            ResumePreviewResponse response = new ResumePreviewResponse();
            response.setStateId(jobId);
            
            // 从Redis缓存或MongoDB获取导出数据
            // 首先尝试从Redis获取
            String cacheKey = "export:resume:" + exportJob.getResumeId();
            Object cachedData = redisUtils.get(cacheKey);
            if (cachedData != null) {
                try {
                    ResumePreviewRequest.ResumeExportData exportData = 
                        objectMapper.convertValue(cachedData, ResumePreviewRequest.ResumeExportData.class);
                    response.setExportData(exportData);
                } catch (Exception e) {
                    log.warn("转换缓存数据失败，任务ID：{}", jobId, e);
                }
            }

            // 设置预览URL
            response.setPreviewUrl("/resume/preview/" + exportJob.getResumeId() + "?export=true&jobId=" + jobId);
            response.setExpiresIn(1800L); // 30分钟

            return response;

        } catch (Exception e) {
            log.error("获取导出数据失败，任务ID：{}", jobId, e);
            throw new BusinessException("获取导出数据失败：" + e.getMessage());
        }
    }

    /**
     * 自动保存简历
     */
    @Override
    public void autoSaveResume(Long id, ResumeSaveRequest request) {
        log.debug("自动保存简历，简历ID：{}", id);

        try {
            // 直接调用更新方法
            updateResume(id, request);
        } catch (Exception e) {
            log.error("自动保存失败，简历ID：{}", id, e);
            // 自动保存失败不抛出异常，避免影响用户体验
        }
    }

    /**
     * 增加浏览次数
     */
    @Override
    public void incrementViewCount(Long id) {
        resumesMapper.incrementViewCount(id);
    }

    /**
     * 增加下载次数
     */
    @Override
    public void incrementDownloadCount(Long id) {
        resumesMapper.incrementDownloadCount(id);
    }

    /**
     * 增加分享次数
     */
    @Override
    public void incrementShareCount(Long id) {
        resumesMapper.incrementShareCount(id);
    }

    /**
     * 计算简历完成度
     */
    @Override
    public Integer calculateCompleteness(Long id) {
        // TODO: 实际计算逻辑
        // 这里返回一个模拟值
        return 75;
    }

    /**
     * 更新简历状态
     */
    @Override
    public void updateResumeStatus(Long id, Integer status) {
        Resumes resume = new Resumes();
        resume.setId(id);
        resume.setStatus(status.byteValue());
        resume.setUpdateTime(LocalDateTime.now());
        resumesMapper.updateById(resume);
    }

    /**
     * 更新简历公开状态
     */
    @Override
    public void updatePublicStatus(Long id, Integer isPublic) {
        Resumes resume = new Resumes();
        resume.setId(id);
        resume.setIsPublic(isPublic.byteValue());
        resume.setUpdateTime(LocalDateTime.now());
        resumesMapper.updateById(resume);
    }

    @Override
    public Integer getUserResumeCount(Long userId) {
        LambdaQueryWrapper<Resumes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Resumes::getUserId, userId)
                .eq(Resumes::getIsDeleted, 0);

        return Math.toIntExact(this.count(wrapper));
    }
} 