package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 手机号绑定/解绑请求DTO
 * 
 * 主要功能：
 * 1. 处理手机号绑定和解绑的请求参数
 * 2. 验证手机号格式和短信验证码
 * 3. 确保操作的安全性
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Schema(name = "手机号绑定请求", description = "手机号绑定/解绑的请求参数")
public class PhoneBindRequest {

    /**
     * 手机号
     * 绑定时必填，解绑时可选
     */
    @Schema(description = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 短信验证码
     * 6位数字验证码
     */
    @Schema(description = "短信验证码")
    @NotBlank(message = "短信验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确，应为6位数字")
    private String smsCode;

    /**
     * 验证绑定手机号的参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isBindValid() {
        return phone != null && !phone.trim().isEmpty() 
               && smsCode != null && !smsCode.trim().isEmpty();
    }

    /**
     * 验证解绑手机号的参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isUnbindValid() {
        return smsCode != null && !smsCode.trim().isEmpty();
    }
}

 