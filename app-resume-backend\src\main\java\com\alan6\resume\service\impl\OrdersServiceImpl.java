package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.order.OrderDetailResponse;
import com.alan6.resume.dto.order.OrderListResponse;
import com.alan6.resume.entity.Orders;
import com.alan6.resume.mapper.OrdersMapper;
import com.alan6.resume.service.IOrdersService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
public class OrdersServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements IOrdersService {

    /**
     * 创建会员购买订单
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @param packageName 套餐名称
     * @param amount 订单金额
     * @param paymentMethod 支付方式
     * @return 创建的订单
     */
    @Override
    public Orders createMembershipOrder(Long userId, Long packageId, String packageName, 
                                       BigDecimal amount, String paymentMethod) {
        log.info("创建会员购买订单，用户ID：{}，套餐ID：{}，金额：{}", userId, packageId, amount);
        
        // 参数验证
        if (userId == null || packageId == null || amount == null || !StringUtils.hasText(paymentMethod)) {
            throw new BusinessException("订单参数不能为空");
        }
        
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("订单金额必须大于0");
        }
        
        // 生成订单号
        String orderNo = generateOrderNo();
        
        // 创建订单对象
        Orders order = new Orders();
        order.setOrderNo(orderNo);
        order.setUserId(userId);
        order.setOrderType((byte) 1); // 1:会员充值
        order.setProductId(packageId);
        order.setProductName(packageName);
        order.setOriginalAmount(amount);
        order.setDiscountAmount(BigDecimal.ZERO);
        order.setFinalAmount(amount);
        order.setPaymentMethod(convertPaymentMethod(paymentMethod));
        order.setPaymentPlatform(paymentMethod);
        order.setOrderStatus((byte) 1); // 1:待支付
        order.setIsDeleted((byte) 0);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        
        // 保存订单
        boolean saved = save(order);
        if (!saved) {
            throw new BusinessException("订单创建失败");
        }
        
        log.info("会员购买订单创建成功，订单号：{}", orderNo);
        return order;
    }

    /**
     * 根据订单号获取订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    @Override
    public Orders getByOrderNo(String orderNo) {
        log.info("根据订单号获取订单，订单号：{}", orderNo);
        
        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单号不能为空");
        }
        
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Orders::getOrderNo, orderNo)
               .eq(Orders::getIsDeleted, 0);
        
        Orders order = getOne(wrapper);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        return order;
    }

    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param status 新状态
     * @param tradeNo 第三方交易号（可选）
     * @return 更新是否成功
     */
    @Override
    public Boolean updateOrderStatus(Long orderId, Byte status, String tradeNo) {
        log.info("更新订单状态，订单ID：{}，新状态：{}，交易号：{}", orderId, status, tradeNo);
        
        if (orderId == null || status == null) {
            throw new BusinessException("订单ID和状态不能为空");
        }
        
        Orders order = getById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 更新订单信息
        order.setOrderStatus(status);
        order.setUpdateTime(LocalDateTime.now());
        
        // 如果是支付成功，设置支付时间和交易号
        if (status == 2) { // 2:已支付
            order.setPayTime(LocalDateTime.now());
            if (StringUtils.hasText(tradeNo)) {
                order.setTradeNo(tradeNo);
            }
        }
        
        // 如果是完成订单，设置完成时间
        if (status == 3) { // 3:已完成
            order.setCompleteTime(LocalDateTime.now());
        }
        
        boolean updated = updateById(order);
        if (updated) {
            log.info("订单状态更新成功，订单ID：{}", orderId);
        } else {
            log.error("订单状态更新失败，订单ID：{}", orderId);
        }
        
        return updated;
    }

    /**
     * 生成订单号
     * 格式：ORD + yyyyMMddHHmmss + 4位随机数
     * 
     * @return 订单号
     */
    private String generateOrderNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = ThreadLocalRandom.current().nextInt(1000, 9999);
        return "ORD" + timestamp + random;
    }

    /**
     * 更新订单状态（通过订单号）
     * 
     * @param orderNo 订单号
     * @param status 新状态
     * @param tradeNo 第三方交易号
     * @param remark 备注
     * @return 更新是否成功
     */
    @Override
    public Boolean updateOrderStatusByOrderNo(String orderNo, Byte status, String tradeNo, String remark) {
        log.info("通过订单号更新订单状态，订单号：{}，新状态：{}，交易号：{}，备注：{}", orderNo, status, tradeNo, remark);
        
        if (!StringUtils.hasText(orderNo) || status == null) {
            throw new BusinessException("订单号和状态不能为空");
        }
        
        // 先查询订单
        Orders existingOrder = getByOrderNo(orderNo);
        if (existingOrder == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 更新订单信息
        existingOrder.setOrderStatus(status);
        existingOrder.setUpdateTime(LocalDateTime.now());
        
        // 如果是支付成功，设置支付时间和交易号
        if (status == 2) { // 2:已支付
            existingOrder.setPayTime(LocalDateTime.now());
            if (StringUtils.hasText(tradeNo)) {
                existingOrder.setTradeNo(tradeNo);
            }
        }
        
        // 如果是完成订单，设置完成时间
        if (status == 3) { // 3:已完成
            existingOrder.setCompleteTime(LocalDateTime.now());
        }
        
        boolean updated = updateById(existingOrder);
        if (updated) {
            log.info("订单状态更新成功，订单号：{}", orderNo);
        } else {
            log.error("订单状态更新失败，订单号：{}", orderNo);
        }
        
        return updated;
    }

    /**
     * 获取用户订单列表（分页）
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页数量
     * @param status 订单状态（可选）
     * @param paymentMethod 支付方式（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param orderType 订单类型（可选）
     * @return 订单列表
     */
    @Override
    public OrderListResponse getUserOrderList(Long userId, Integer page, Integer size, Byte status, 
                                            Byte paymentMethod, LocalDateTime startTime, LocalDateTime endTime, Byte orderType) {
        log.info("获取用户订单列表，用户ID：{}，页码：{}，每页数量：{}", userId, page, size);
        
        // 参数验证和默认值设置
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1 || size > 100) {
            size = 10;
        }
        
        // 构建查询条件
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Orders::getUserId, userId)
               .eq(Orders::getIsDeleted, 0);
        
        // 可选筛选条件
        if (status != null) {
            wrapper.eq(Orders::getOrderStatus, status);
        }
        if (paymentMethod != null) {
            wrapper.eq(Orders::getPaymentMethod, paymentMethod);
        }
        if (orderType != null) {
            wrapper.eq(Orders::getOrderType, orderType);
        }
        if (startTime != null) {
            wrapper.ge(Orders::getCreateTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(Orders::getCreateTime, endTime);
        }
        
        // 按创建时间倒序排列
        wrapper.orderByDesc(Orders::getCreateTime);
        
        // 分页查询
        Page<Orders> pageParam = new Page<>(page, size);
        IPage<Orders> pageResult = page(pageParam, wrapper);
        
        // 构建响应
        OrderListResponse response = new OrderListResponse();
        response.setTotal(pageResult.getTotal());
        response.setPage((int) pageResult.getCurrent());
        response.setSize((int) pageResult.getSize());
        response.setPages((int) pageResult.getPages());
        
        // 转换订单列表
        List<OrderListResponse.OrderItem> orderItems = pageResult.getRecords().stream()
                .map(this::convertToOrderItem)
                .collect(Collectors.toList());
        response.setOrders(orderItems);
        
        log.info("获取用户订单列表成功，用户ID：{}，总数：{}", userId, pageResult.getTotal());
        return response;
    }

    /**
     * 获取订单详情
     * 
     * @param orderNo 订单号
     * @param userId 用户ID（用于权限验证）
     * @return 订单详情
     */
    @Override
    public OrderDetailResponse getOrderDetail(String orderNo, Long userId) {
        log.info("获取订单详情，订单号：{}，用户ID：{}", orderNo, userId);
        
        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单号不能为空");
        }
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 查询订单
        Orders order = getByOrderNo(orderNo);
        if (order == null) {
            throw new BusinessException(4001, "订单不存在");
        }
        
        // 验证用户权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("无权限查看此订单");
        }
        
        // 构建订单详情响应
        OrderDetailResponse response = new OrderDetailResponse();
        response.setOrderNo(order.getOrderNo());
        response.setOrderType(convertOrderTypeToString(order.getOrderType()));
        response.setAmount(order.getFinalAmount());
        response.setCurrency("CNY");
        response.setStatus(convertOrderStatusToString(order.getOrderStatus()));
        response.setCreateTime(order.getCreateTime());
        response.setExpireTime(order.getCreateTime().plusMinutes(30));
        
        // 构建产品信息
        OrderDetailResponse.ProductInfo productInfo = new OrderDetailResponse.ProductInfo();
        productInfo.setProductId(order.getProductId().toString());
        productInfo.setProductName(order.getProductName());
        productInfo.setProductDesc("会员套餐，享受高级功能"); // 模拟描述
        productInfo.setOriginalPrice(order.getOriginalAmount());
        productInfo.setDiscountPrice(order.getFinalAmount());
        productInfo.setDiscountReason(order.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0 ? "优惠活动" : null);
        response.setProductInfo(productInfo);
        
        // 构建支付信息
        if (order.getOrderStatus() >= 2) { // 已支付
            OrderDetailResponse.PaymentInfo paymentInfo = new OrderDetailResponse.PaymentInfo();
            paymentInfo.setPaymentId("PAY" + order.getOrderNo().substring(3));
            paymentInfo.setPaymentMethod(convertPaymentMethodToString(order.getPaymentMethod()));
            paymentInfo.setPaymentStatus(convertPaymentStatusToString(order.getOrderStatus()));
            paymentInfo.setTransactionId(order.getTradeNo());
            paymentInfo.setPaymentTime(order.getPayTime());
            response.setPaymentInfo(paymentInfo);
        }
        
        // 构建用户信息（脱敏）
        OrderDetailResponse.UserInfo userInfo = new OrderDetailResponse.UserInfo();
        userInfo.setUserId("U" + userId);
        userInfo.setUserEmail("user***@example.com"); // 脱敏处理
        userInfo.setUserName("用户" + userId);
        response.setUserInfo(userInfo);
        
        // 构建时间线
        List<OrderDetailResponse.Timeline> timeline = buildOrderTimeline(order);
        response.setTimeline(timeline);
        
        log.info("获取订单详情成功，订单号：{}", orderNo);
        return response;
    }

    /**
     * 取消订单
     * 
     * @param orderNo 订单号
     * @param userId 用户ID（用于权限验证）
     * @param cancelReason 取消原因
     * @param cancelReasonCode 取消原因代码
     * @return 取消结果
     */
    @Override
    public Boolean cancelOrder(String orderNo, Long userId, String cancelReason, String cancelReasonCode) {
        log.info("取消订单，订单号：{}，用户ID：{}，取消原因：{}", orderNo, userId, cancelReason);
        
        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单号不能为空");
        }
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (!StringUtils.hasText(cancelReason)) {
            throw new BusinessException("取消原因不能为空");
        }
        
        // 查询订单
        Orders order = getByOrderNo(orderNo);
        if (order == null) {
            throw new BusinessException(4001, "订单不存在");
        }
        
        // 验证用户权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此订单");
        }
        
        // 验证订单状态
        if (order.getOrderStatus() != 1) { // 只有待支付状态可以取消
            throw new BusinessException(4012, "订单状态不允许取消");
        }
        
        // 检查订单是否超时（30分钟后不能取消）
        LocalDateTime expireTime = order.getCreateTime().plusMinutes(30);
        if (LocalDateTime.now().isAfter(expireTime)) {
            throw new BusinessException(4013, "订单取消时间超期");
        }
        
        // 更新订单状态为已取消
        order.setOrderStatus((byte) 4); // 4:已取消
        order.setUpdateTime(LocalDateTime.now());
        // 注意：Orders实体暂未包含cancelTime和cancelReason字段，需要时可扩展实体类
        
        boolean updated = updateById(order);
        if (updated) {
            log.info("取消订单成功，订单号：{}", orderNo);
        } else {
            log.error("取消订单失败，订单号：{}", orderNo);
        }
        
        return updated;
    }

    // ========== 私有方法 ==========

    /**
     * 转换订单实体为订单项
     */
    private OrderListResponse.OrderItem convertToOrderItem(Orders order) {
        OrderListResponse.OrderItem item = new OrderListResponse.OrderItem();
        item.setOrderNo(order.getOrderNo());
        item.setOrderType(convertOrderTypeToString(order.getOrderType()));
        item.setProductName(order.getProductName());
        item.setAmount(order.getFinalAmount());
        item.setCurrency("CNY");
        item.setStatus(convertOrderStatusToString(order.getOrderStatus()));
        item.setPaymentMethod(convertPaymentMethodToString(order.getPaymentMethod()));
        item.setPaymentStatus(convertPaymentStatusToString(order.getOrderStatus()));
        item.setCreateTime(order.getCreateTime());
        item.setPaymentTime(order.getPayTime());
        item.setExpireTime(order.getCreateTime().plusMinutes(30));
        return item;
    }

    /**
     * 构建订单时间线
     */
    private List<OrderDetailResponse.Timeline> buildOrderTimeline(Orders order) {
        List<OrderDetailResponse.Timeline> timeline = new ArrayList<>();
        
        // 订单创建
        OrderDetailResponse.Timeline created = new OrderDetailResponse.Timeline();
        created.setStatus("created");
        created.setTime(order.getCreateTime());
        created.setDescription("订单创建成功");
        timeline.add(created);
        
        // 支付完成
        if (order.getPayTime() != null) {
            OrderDetailResponse.Timeline paid = new OrderDetailResponse.Timeline();
            paid.setStatus("paid");
            paid.setTime(order.getPayTime());
            paid.setDescription("支付成功");
            timeline.add(paid);
        }
        
        // 订单完成
        if (order.getCompleteTime() != null) {
            OrderDetailResponse.Timeline completed = new OrderDetailResponse.Timeline();
            completed.setStatus("completed");
            completed.setTime(order.getCompleteTime());
            completed.setDescription("订单完成，会员权益已生效");
            timeline.add(completed);
        }
        
        // 订单取消（基于订单状态判断）
        if (order.getOrderStatus() == 4) { // 4:已取消
            OrderDetailResponse.Timeline cancelled = new OrderDetailResponse.Timeline();
            cancelled.setStatus("cancelled");
            cancelled.setTime(order.getUpdateTime()); // 使用更新时间作为取消时间
            cancelled.setDescription("订单已取消");
            timeline.add(cancelled);
        }
        
        return timeline;
    }

    /**
     * 转换订单类型为字符串
     */
    private String convertOrderTypeToString(Byte orderType) {
        switch (orderType) {
            case 1: return "membership";
            case 2: return "template";
            default: return "unknown";
        }
    }

    /**
     * 转换订单状态为字符串
     */
    private String convertOrderStatusToString(Byte orderStatus) {
        switch (orderStatus) {
            case 1: return "pending";     // 待支付
            case 2: return "paid";        // 已支付
            case 3: return "completed";   // 已完成
            case 4: return "cancelled";   // 已取消
            case 5: return "refunded";    // 已退款
            default: return "unknown";
        }
    }

    /**
     * 转换支付方式数字为字符串
     */
    private String convertPaymentMethodToString(Byte paymentMethod) {
        if (paymentMethod == null) {
            return "unknown";
        }
        switch (paymentMethod) {
            case 1: return "wechat";
            case 2: return "alipay";
            default: return "unknown";
        }
    }

    /**
     * 转换支付状态
     */
    private String convertPaymentStatusToString(Byte orderStatus) {
        switch (orderStatus) {
            case 1: return "unpaid";      // 待支付
            case 2: return "paid";        // 已支付
            case 3: return "paid";        // 已完成
            case 4: return "cancelled";   // 已取消
            case 5: return "refunded";    // 已退款
            default: return "unknown";
        }
    }

    /**
     * 转换支付方式字符串为数字
     * 
     * @param paymentMethod 支付方式字符串
     * @return 支付方式数字
     */
    private Byte convertPaymentMethod(String paymentMethod) {
        switch (paymentMethod.toLowerCase()) {
            case "wechat":
                return (byte) 1; // 1:微信
            case "alipay":
                return (byte) 2; // 2:支付宝
            default:
                throw new BusinessException("不支持的支付方式：" + paymentMethod);
        }
    }

}
