package com.alan6.resume.service.impl;

import com.alan6.resume.dto.template.HtmlConversionRequest;
import com.alan6.resume.dto.template.HtmlConversionResponse;
import com.alan6.resume.dto.template.HtmlValidationResult;
import com.alan6.resume.service.IHtmlToVueConverterService;
import com.alan6.resume.service.IHtmlValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * HTML转Vue转换服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HtmlToVueConverterServiceImpl implements IHtmlToVueConverterService {
    
    private final IHtmlValidationService htmlValidationService;
    
    /**
     * 转换根目录
     */
    private static final String CONVERSION_ROOT_DIR = "tmp/template-transfer";
    
    /**
     * 转换规则正则表达式
     */
    private static final Pattern DATA_FIELD_PATTERN = Pattern.compile("data-field=[\"']([^\"']+)[\"']");
    private static final Pattern DATA_VUE_TEXT_PATTERN = Pattern.compile("data-vue-text=[\"']([^\"']+)[\"']");
    private static final Pattern DATA_VUE_IF_PATTERN = Pattern.compile("data-vue-if=[\"']([^\"']+)[\"']");
    private static final Pattern DATA_VUE_FOR_PATTERN = Pattern.compile("data-vue-for=[\"']([^\"']+)[\"']");
    private static final Pattern DATA_MODULE_PATTERN = Pattern.compile("data-module=[\"']([^\"']+)[\"']");
    private static final Pattern DATA_MODULE_ROOT_PATTERN = Pattern.compile("data-module-root=[\"']true[\"']");
    private static final Pattern STYLE_TAG_PATTERN = Pattern.compile("<style[^>]*>(.*?)</style>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
    private static final Pattern BODY_CONTENT_PATTERN = Pattern.compile("<body[^>]*>(.*?)</body>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
    
    @Override
    public HtmlConversionResponse convertSingleFile(File htmlFile, String taskId) {
        return convertSingleFileWithOptions(htmlFile, taskId, HtmlConversionRequest.createDefaultOptions());
    }
    
    /**
     * 转换单个HTML文件（带选项）
     */
    public HtmlConversionResponse convertSingleFileWithOptions(File htmlFile, String taskId, HtmlConversionRequest.ConversionOptions options) {
        log.info("开始转换单个HTML文件: {}, 任务ID: {}", htmlFile.getName(), taskId);
        
        try {
            // 创建任务目录
            String taskDir = createTaskDirectory(taskId);
            
            // 复制HTML文件到任务目录
            String copiedHtmlPath = copyHtmlFile(htmlFile, taskDir);
            
            // 读取HTML内容
            String htmlContent = Files.readString(htmlFile.toPath());
            
            // 验证HTML文件
            HtmlValidationResult validation = htmlValidationService.validateHtmlContent(htmlContent, htmlFile.getName());
            
            // 修复：创建转换结果时传递用户选项
            HtmlConversionResponse.ConversionResult result = convertSingleHtmlFileWithOptions(
                    htmlFile, htmlContent, taskDir, validation, options
            );
            
            // 生成统计信息
            HtmlConversionResponse.ConversionStatistics statistics = generateSingleFileStatistics(result);
            
            // 返回响应
            return HtmlConversionResponse.success(
                    Collections.singletonList(result), 
                    statistics, 
                    taskId
            );
            
        } catch (Exception e) {
            log.error("转换单个HTML文件失败: {}", htmlFile.getName(), e);
            return HtmlConversionResponse.failure(
                    "转换失败: " + e.getMessage(), 
                    taskId
            );
        }
    }
    
    @Override
    public HtmlConversionResponse convertBatchFiles(List<File> htmlFiles, String taskId) {
        return convertBatchFilesWithOptions(htmlFiles, taskId, HtmlConversionRequest.createDefaultOptions());
    }
    
    /**
     * 批量转换HTML文件（带选项）
     */
    public HtmlConversionResponse convertBatchFilesWithOptions(List<File> htmlFiles, String taskId, HtmlConversionRequest.ConversionOptions options) {
        log.info("开始批量转换HTML文件，数量: {}, 任务ID: {}", htmlFiles.size(), taskId);
        
        try {
            // 创建任务目录
            String taskDir = createTaskDirectory(taskId);
            
            List<HtmlConversionResponse.ConversionResult> results = new ArrayList<>();
            long totalProcessingTime = 0;
            
            // 逐个转换文件
            for (File htmlFile : htmlFiles) {
                try {
                    long startTime = System.currentTimeMillis();
                    
                    // 复制HTML文件到任务目录
                    String copiedHtmlPath = copyHtmlFile(htmlFile, taskDir);
                    
                    // 读取HTML内容
                    String htmlContent = Files.readString(htmlFile.toPath());
                    
                    // 验证HTML文件
                    HtmlValidationResult validation = htmlValidationService.validateHtmlContent(htmlContent, htmlFile.getName());
                    
                    // 修复：转换文件时传递用户选项
                    HtmlConversionResponse.ConversionResult result = convertSingleHtmlFileWithOptions(
                            htmlFile, htmlContent, taskDir, validation, options
                    );
                    
                    long processingTime = System.currentTimeMillis() - startTime;
                    result.setProcessingTime(processingTime);
                    totalProcessingTime += processingTime;
                    
                    results.add(result);
                    
                } catch (Exception e) {
                    log.error("转换文件失败: {}", htmlFile.getName(), e);
                    
                    // 添加失败结果
                    HtmlConversionResponse.ConversionResult failedResult = HtmlConversionResponse.ConversionResult.builder()
                            .sourceFileName(htmlFile.getName())
                            .targetFileName(getVueFileName(htmlFile.getName()))
                            .status("FAILED")
                            .message("转换失败")
                            .error(e.getMessage())
                            .processingTime(0L)
                            .build();
                    
                    results.add(failedResult);
                }
            }
            
            // 生成统计信息
            HtmlConversionResponse.ConversionStatistics statistics = generateBatchStatistics(results, totalProcessingTime);
            
            // 返回响应
            boolean hasFailures = results.stream().anyMatch(r -> "FAILED".equals(r.getStatus()));
            if (hasFailures) {
                return HtmlConversionResponse.partialSuccess(results, statistics, taskId);
            } else {
                return HtmlConversionResponse.success(results, statistics, taskId);
            }
            
        } catch (Exception e) {
            log.error("批量转换HTML文件失败", e);
            return HtmlConversionResponse.failure(
                    "批量转换失败: " + e.getMessage(), 
                    taskId
            );
        }
    }
    
    @Override
    public HtmlConversionResponse convertWithRequest(HtmlConversionRequest request, String taskId) {
        log.info("根据请求转换HTML文件: {}, 任务ID: {}", request.getConversionType(), taskId);
        
        try {
            // 获取任务目录中的HTML文件
            String taskDir = getTaskDirectory(taskId);
            List<File> htmlFiles = new ArrayList<>();
            
            for (String fileName : request.getHtmlFiles()) {
                File htmlFile = new File(taskDir, fileName);
                if (htmlFile.exists()) {
                    htmlFiles.add(htmlFile);
                } else {
                    log.warn("HTML文件不存在: {}", fileName);
                }
            }
            
            if (htmlFiles.isEmpty()) {
                return HtmlConversionResponse.failure("未找到有效的HTML文件", taskId);
            }
            
            // 修复：根据转换类型进行转换，传递用户配置的选项
            if (request.isSingleConversion()) {
                return convertSingleFileWithOptions(htmlFiles.get(0), taskId, request.getOptions());
            } else {
                return convertBatchFilesWithOptions(htmlFiles, taskId, request.getOptions());
            }
            
        } catch (Exception e) {
            log.error("根据请求转换失败", e);
            return HtmlConversionResponse.failure(
                    "转换失败: " + e.getMessage(), 
                    taskId
            );
        }
    }
    
    @Override
    public String convertHtmlToVue(String htmlContent, String fileName, HtmlConversionRequest.ConversionOptions options) {
        log.debug("开始转换HTML内容为Vue组件: {}", fileName);
        
        try {
            // 提取样式内容
            String styleContent = extractStyleContent(htmlContent);
            
            // 提取模板内容
            String templateContent = extractTemplateContent(htmlContent);
            
            // 转换模板内容
            String convertedTemplate = convertTemplateContent(templateContent, options);
            
            // 生成Vue组件
            return generateVueComponent(convertedTemplate, styleContent, options);
            
        } catch (Exception e) {
            log.error("转换HTML为Vue失败: {}", fileName, e);
            throw new RuntimeException("转换失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String createTaskDirectory(String taskId) {
        try {
            String taskDir = getTaskDirectory(taskId);
            Path taskPath = Paths.get(taskDir);
            
            // 创建目录
            Files.createDirectories(taskPath);
            
            log.info("创建任务目录: {}", taskDir);
            return taskDir;
            
        } catch (IOException e) {
            log.error("创建任务目录失败: {}", taskId, e);
            throw new RuntimeException("创建任务目录失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getTaskDirectory(String taskId) {
        return CONVERSION_ROOT_DIR + "/" + taskId;
    }
    
    @Override
    public boolean cleanupTask(String taskId) {
        try {
            String taskDir = getTaskDirectory(taskId);
            Path taskPath = Paths.get(taskDir);
            
            if (Files.exists(taskPath)) {
                // 递归删除目录及其内容
                Files.walk(taskPath)
                        .sorted(Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(File::delete);
                
                log.info("清理任务目录: {}", taskDir);
                return true;
            }
            
            return false;
            
        } catch (IOException e) {
            log.error("清理任务目录失败: {}", taskId, e);
            return false;
        }
    }
    
    @Override
    public String generateTaskId() {
        // 生成基于时间戳的任务ID
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String randomSuffix = String.valueOf(System.currentTimeMillis() % 10000);
        return timestamp + "_" + randomSuffix;
    }
    
    @Override
    public String saveVueFile(String vueContent, String fileName, String taskDirectory) {
        try {
            String vueFileName = getVueFileName(fileName);
            Path vueFilePath = Paths.get(taskDirectory, vueFileName);
            
            // 保存Vue文件
            Files.writeString(vueFilePath, vueContent);
            
            log.debug("保存Vue文件: {}", vueFilePath);
            return vueFilePath.toString();
            
        } catch (IOException e) {
            log.error("保存Vue文件失败: {}", fileName, e);
            throw new RuntimeException("保存Vue文件失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String copyHtmlFile(File htmlFile, String taskDirectory) {
        try {
            Path targetPath = Paths.get(taskDirectory, htmlFile.getName());
            
            // 复制文件
            Files.copy(htmlFile.toPath(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            
            log.debug("复制HTML文件: {} -> {}", htmlFile.getPath(), targetPath);
            return targetPath.toString();
            
        } catch (IOException e) {
            log.error("复制HTML文件失败: {}", htmlFile.getName(), e);
            throw new RuntimeException("复制HTML文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 转换单个HTML文件（使用默认选项）
     * 
     * @param htmlFile HTML文件
     * @param htmlContent HTML内容
     * @param taskDir 任务目录
     * @param validation 验证结果
     * @return 转换结果
     */
    private HtmlConversionResponse.ConversionResult convertSingleHtmlFile(
            File htmlFile, String htmlContent, String taskDir, HtmlValidationResult validation) {
        return convertSingleHtmlFileWithOptions(htmlFile, htmlContent, taskDir, validation, 
                HtmlConversionRequest.createDefaultOptions());
    }
    
    /**
     * 转换单个HTML文件（带选项）
     * 
     * @param htmlFile HTML文件
     * @param htmlContent HTML内容
     * @param taskDir 任务目录
     * @param validation 验证结果
     * @param options 转换选项
     * @return 转换结果
     */
    private HtmlConversionResponse.ConversionResult convertSingleHtmlFileWithOptions(
            File htmlFile, String htmlContent, String taskDir, HtmlValidationResult validation, 
            HtmlConversionRequest.ConversionOptions options) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 如果验证失败，返回失败结果
            if (!validation.getValid()) {
                return HtmlConversionResponse.ConversionResult.builder()
                        .sourceFileName(htmlFile.getName())
                        .targetFileName(getVueFileName(htmlFile.getName()))
                        .status("FAILED")
                        .message("HTML验证失败")
                        .htmlFilePath(Paths.get(taskDir, htmlFile.getName()).toString())
                        .validationResult(validation)
                        .processingTime(System.currentTimeMillis() - startTime)
                        .build();
            }
            
            // 修复：转换HTML为Vue时使用用户配置的选项
            String vueContent = convertHtmlToVue(htmlContent, htmlFile.getName(), options);
            
            // 保存Vue文件
            String vueFilePath = saveVueFile(vueContent, htmlFile.getName(), taskDir);
            
            // 生成转换详情
            HtmlConversionResponse.ConversionDetails details = generateConversionDetails(htmlContent, vueContent);
            
            return HtmlConversionResponse.ConversionResult.builder()
                    .sourceFileName(htmlFile.getName())
                    .targetFileName(getVueFileName(htmlFile.getName()))
                    .status("SUCCESS")
                    .message("转换成功")
                    .vueFilePath(vueFilePath)
                    .htmlFilePath(Paths.get(taskDir, htmlFile.getName()).toString())
                    .fileSize((long) vueContent.length())
                    .processingTime(System.currentTimeMillis() - startTime)
                    .validationResult(validation)
                    .details(details)
                    .build();
            
        } catch (Exception e) {
            log.error("转换单个HTML文件失败: {}", htmlFile.getName(), e);
            
            return HtmlConversionResponse.ConversionResult.builder()
                    .sourceFileName(htmlFile.getName())
                    .targetFileName(getVueFileName(htmlFile.getName()))
                    .status("FAILED")
                    .message("转换失败")
                    .htmlFilePath(Paths.get(taskDir, htmlFile.getName()).toString())
                    .error(e.getMessage())
                    .processingTime(System.currentTimeMillis() - startTime)
                    .validationResult(validation)
                    .build();
        }
    }
    
    /**
     * 提取样式内容
     * 
     * @param htmlContent HTML内容
     * @return 样式内容
     */
    private String extractStyleContent(String htmlContent) {
        StringBuilder styleBuilder = new StringBuilder();
        
        Matcher styleMatcher = STYLE_TAG_PATTERN.matcher(htmlContent);
        while (styleMatcher.find()) {
            String styleContent = styleMatcher.group(1);
            if (StringUtils.hasText(styleContent)) {
                styleBuilder.append(styleContent.trim()).append("\n");
            }
        }
        
        return styleBuilder.toString();
    }
    
    /**
     * 提取模板内容
     * 
     * @param htmlContent HTML内容
     * @return 模板内容
     */
    private String extractTemplateContent(String htmlContent) {
        Matcher matcher = BODY_CONTENT_PATTERN.matcher(htmlContent);
        if (matcher.find()) {
            String bodyContent = matcher.group(1).trim();
            
            // 检查是否已经包含resume-template容器
            if (bodyContent.contains("class=\"resume-template\"")) {
                // 如果已经包含，直接返回body内容
                return bodyContent;
            } else {
                // 如果没有包含，需要添加容器
                return "<div class=\"resume-template\">\n" + bodyContent + "\n</div>";
            }
        }
        return htmlContent;
    }
    
    /**
     * 转换模板内容
     * 
     * @param templateContent 模板内容
     * @param options 转换选项
     * @return 转换后的内容
     */
    private String convertTemplateContent(String templateContent, HtmlConversionRequest.ConversionOptions options) {
        String result = templateContent;
        
        // 1. 转换data-vue-text属性为Vue文本插值
        result = convertDataVueTextAttributes(result);
        
        // 2. 转换data-vue-if属性为v-if指令
        result = convertDataVueIfAttributes(result);
        
        // 3. 转换data-vue-for属性为v-for指令
        result = convertDataVueForAttributes(result);
        
        // 4. 转换data-vue-key属性为:key指令
        result = convertDataVueKeyAttributes(result);
        
        // 5. 添加模块交互功能
        result = addModuleInteractions(result);
        
        // 6. 添加内容项交互功能
        result = addItemInteractions(result);
        
        // 7. 添加鼠标悬停事件绑定
        result = addHoverEvents(result);
        
        // 8. 添加拖拽样式类绑定
        result = addDraggableClasses(result);
        
        // 9. 添加图像属性绑定
        result = addImageBindings(result);
        
        // 10. 全局修复数据绑定路径
        result = fixDataBindingPaths(result);
        
        // 11. 清理data-field属性（已在文本插值中处理）
        result = cleanupDataFieldAttributes(result);
        
        // 11. 清理data-module-root属性
        result = result.replaceAll("\\s*data-module-root=[\"']true[\"']", "");
        
        // 12. 清理data-module属性（保留用于识别模块的属性）
        result = result.replaceAll("\\s*data-module=[\"'][^\"']*[\"']", "");
        
        // 13. 处理遗留的data-vue-text属性（可能在复杂HTML结构中遗漏）
        result = handleRemainingDataVueTextAttributes(result);
        
        // 14. 清理其他可能遗留的data-vue-*属性
        result = cleanupRemainingDataVueAttributes(result);
        
        return result;
    }
    
    /**
     * 清理data-field属性
     * 
     * @param content 内容
     * @return 清理后的内容
     */
    private String cleanupDataFieldAttributes(String content) {
        return DATA_FIELD_PATTERN.matcher(content).replaceAll("");
    }
    
    /**
     * 处理遗留的data-vue-text属性
     * 
     * @param content 内容
     * @return 处理后的内容
     */
    private String handleRemainingDataVueTextAttributes(String content) {
        // 修复：更全面地处理遗留的data-vue-text属性
        Pattern remainingPattern = Pattern.compile("\\s*data-vue-text=[\"']([^\"']+)[\"']");
        
        return remainingPattern.matcher(content).replaceAll(matchResult -> {
            String expression = matchResult.group(1);
            
            // 检查是否在标签内容中已经有Vue插值表达式
            String vueExpression = convertToVueExpression(expression);
            String interpolation = "{{ " + vueExpression + " }}";
            
            // 如果内容中已经包含对应的插值表达式，只移除属性
            if (content.contains(interpolation)) {
                log.debug("移除已处理的data-vue-text属性: {}", expression);
                return ""; // 移除遗留的属性
            } else {
                // 如果没有对应的插值表达式，记录警告但仍然移除属性
                log.warn("发现遗留的data-vue-text属性: {}，但未找到对应的插值表达式", expression);
                return ""; // 移除遗留的属性
            }
        });
    }
    
    /**
     * 清理其他可能遗留的data-vue-*属性
     * 
     * @param content 内容
     * @return 清理后的内容
     */
    private String cleanupRemainingDataVueAttributes(String content) {
        String result = content;
        
        // 清理可能遗留的data-vue-*属性
        result = result.replaceAll("\\s*data-vue-show=[\"'][^\"']*[\"']", "");
        result = result.replaceAll("\\s*data-vue-hide=[\"'][^\"']*[\"']", "");
        result = result.replaceAll("\\s*data-vue-class=[\"'][^\"']*[\"']", "");
        result = result.replaceAll("\\s*data-vue-style=[\"'][^\"']*[\"']", "");
        result = result.replaceAll("\\s*data-vue-attr=[\"'][^\"']*[\"']", "");
        
        // 清理任何其他未处理的data-vue-*属性
        result = result.replaceAll("\\s*data-vue-[a-zA-Z0-9-]+=[\"'][^\"']*[\"']", "");
        
        return result;
    }
    
    /**
     * 转换data-vue-text属性为Vue文本插值
     * 
     * @param content 内容
     * @return 转换后的内容
     */
    private String convertDataVueTextAttributes(String content) {
        // 修复：使用更精确的正则表达式处理data-vue-text属性
        // 处理自闭合标签和普通标签两种情况
        Pattern tagPattern = Pattern.compile("(<[^>]*\\s+data-vue-text=[\"']([^\"']+)[\"'][^>]*>)([^<]*?)(<[^>]*>|$)", Pattern.DOTALL);
        
        String result = tagPattern.matcher(content).replaceAll(matchResult -> {
            String openTag = matchResult.group(1);
            String expression = matchResult.group(2);
            String originalContent = matchResult.group(3);
            String nextPart = matchResult.group(4);
            
            // 转换表达式为正确的Vue表达式
            String vueExpression = convertToVueExpression(expression);
            
            // 移除data-vue-text属性
            String cleanOpenTag = openTag.replaceAll("\\s*data-vue-text=[\"'][^\"']+[\"']", "");
            
            // 检查是否是自闭合标签
            if (cleanOpenTag.endsWith("/>")) {
                // 自闭合标签，不能包含文本内容
                log.warn("自闭合标签不能包含data-vue-text属性: {}", expression);
                return cleanOpenTag + originalContent + nextPart;
            }
            
            // 检查下一部分是否是对应的闭合标签
            if (nextPart.startsWith("</")) {
                // 生成Vue文本插值
                return cleanOpenTag + "{{ " + vueExpression + " }}" + nextPart;
            } else {
                // 如果不是闭合标签，保持原样
                return cleanOpenTag + originalContent + nextPart;
            }
        });
        
        return result;
    }
    
    /**
     * 转换data-vue-if属性为v-if指令
     * 
     * @param content 内容
     * @return 转换后的内容
     */
    private String convertDataVueIfAttributes(String content) {
        return DATA_VUE_IF_PATTERN.matcher(content).replaceAll(matchResult -> {
            String condition = matchResult.group(1);
            
            // 修复：处理条件表达式
            String vueCondition = convertToVueCondition(condition);
            
            // 替换为v-if指令
            return "v-if=\"" + vueCondition + "\"";
        });
    }
    
    /**
     * 转换为Vue条件表达式
     * 
     * @param condition 条件表达式
     * @return Vue条件表达式
     */
    private String convertToVueCondition(String condition) {
        // 处理否定条件
        if (condition.startsWith("!")) {
            String positiveCondition = condition.substring(1);
            String vuePositiveCondition;
            
            // 对于循环变量条件，直接使用
            if (positiveCondition.startsWith("item.") || positiveCondition.startsWith("skill.") || 
                positiveCondition.startsWith("project.") || positiveCondition.startsWith("education.") ||
                positiveCondition.startsWith("social.") || positiveCondition.startsWith("info.") ||
                positiveCondition.startsWith("award.") || positiveCondition.startsWith("certificate.") ||
                positiveCondition.startsWith("language.") || positiveCondition.startsWith("hobby.") ||
                positiveCondition.startsWith("training.") || positiveCondition.startsWith("internship.") ||
                positiveCondition.startsWith("volunteer.") || positiveCondition.startsWith("research.") ||
                positiveCondition.startsWith("publication.") || positiveCondition.startsWith("portfolio.")) {
                vuePositiveCondition = positiveCondition;
            } else {
                vuePositiveCondition = convertToVueExpression(positiveCondition);
            }
            
            return "!" + vuePositiveCondition;
        }
        
        // 处理包含 || 的复合条件
        if (condition.contains(" || ")) {
            String[] parts = condition.split(" \\|\\| ");
            StringBuilder result = new StringBuilder();
            
            for (int i = 0; i < parts.length; i++) {
                String part = parts[i].trim();
                String vuePart = convertToVueCondition(part);
                
                if (i > 0) {
                    result.append(" || ");
                }
                result.append(vuePart);
            }
            
            return result.toString();
        }
        
        // 处理包含 && 的复合条件
        if (condition.contains(" && ")) {
            String[] parts = condition.split(" && ");
            StringBuilder result = new StringBuilder();
            
            for (int i = 0; i < parts.length; i++) {
                String part = parts[i].trim();
                String vuePart = convertToVueCondition(part);
                
                if (i > 0) {
                    result.append(" && ");
                }
                result.append(vuePart);
            }
            
            return result.toString();
        }
        
        // 对于循环变量条件，直接使用
        if (condition.startsWith("item.") || condition.startsWith("skill.") || 
            condition.startsWith("project.") || condition.startsWith("education.") ||
            condition.startsWith("social.") || condition.startsWith("info.") ||
            condition.startsWith("award.") || condition.startsWith("certificate.") ||
            condition.startsWith("language.") || condition.startsWith("hobby.") ||
            condition.startsWith("training.") || condition.startsWith("internship.") ||
            condition.startsWith("volunteer.") || condition.startsWith("research.") ||
            condition.startsWith("publication.") || condition.startsWith("portfolio.")) {
            return condition;
        }
        
        // 其他条件使用标准转换
        return convertToVueExpression(condition);
    }
    
    /**
     * 转换data-vue-for属性为v-for指令
     * 
     * @param content 内容
     * @return 转换后的内容
     */
    private String convertDataVueForAttributes(String content) {
        return DATA_VUE_FOR_PATTERN.matcher(content).replaceAll(matchResult -> {
            String forExpression = matchResult.group(1);
            String vueForExpression = convertToVueForExpression(forExpression);
            
            // 替换为v-for指令
            return "v-for=\"" + vueForExpression + "\"";
        });
    }
    
    /**
     * 转换data-vue-key属性为:key指令
     * 
     * @param content 内容
     * @return 转换后的内容
     */
    private String convertDataVueKeyAttributes(String content) {
        Pattern keyPattern = Pattern.compile("data-vue-key=[\"']([^\"']+)[\"']");
        
        return keyPattern.matcher(content).replaceAll(matchResult -> {
            String keyExpression = matchResult.group(1);
            // 对于:key属性，通常是item.id这样的简单表达式，不需要添加mergedResumeData前缀
            String vueKeyExpression = convertToVueKeyExpression(keyExpression);
            
            // 替换为:key指令
            return ":key=\"" + vueKeyExpression + "\"";
        });
    }
    
    /**
     * 转换为Vue key表达式
     * 
     * @param keyExpression key表达式
     * @return Vue key表达式
     */
    private String convertToVueKeyExpression(String keyExpression) {
        // 修复：处理item.xxx.id格式的错误
        if (keyExpression.startsWith("item.")) {
            // 如果是item.work.id这种格式，直接返回work.id（去掉item前缀）
            String[] parts = keyExpression.split("\\.", 3);
            if (parts.length == 3) {
                // 返回 {变量名}.id，去掉item前缀
                return parts[1] + ".id";
            }
            // 如果是item.xxx格式，去掉item前缀
            return keyExpression.substring(5);
        }
        
        // 对于直接的循环变量表达式，保持原样
        if (keyExpression.startsWith("skill.") || keyExpression.startsWith("project.") || 
            keyExpression.startsWith("education.") || keyExpression.startsWith("social.") ||
            keyExpression.startsWith("info.") || keyExpression.startsWith("award.") ||
            keyExpression.startsWith("certificate.") || keyExpression.startsWith("language.") ||
            keyExpression.startsWith("hobby.") || keyExpression.startsWith("training.") ||
            keyExpression.startsWith("internship.") || keyExpression.startsWith("volunteer.") ||
            keyExpression.startsWith("research.") || keyExpression.startsWith("publication.") ||
            keyExpression.startsWith("portfolio.") || keyExpression.startsWith("work.") ||
            keyExpression.startsWith("experience.")) {
            return keyExpression;
        }
        
        // 如果是简单的字段名，转换为变量名.字段名格式
        if (keyExpression.equals("id")) {
            return "item.id";
        }
        
        return keyExpression;
    }
    
    /**
     * 转换为Vue表达式
     * 
     * @param expression 表达式
     * @return Vue表达式
     */
    private String convertToVueExpression(String expression) {
        // 字段名称映射表
        Map<String, String> fieldMapping = createFieldMapping();
        
        // 如果表达式已经是完整的Vue表达式，直接返回
        if (expression.startsWith("mergedResumeData.")) {
            return expression;
        }
        
        // 修复：处理resumeData.xxx.yyy格式的表达式
        if (expression.startsWith("resumeData.")) {
            String withoutPrefix = expression.substring("resumeData.".length());
            
            // 解析路径部分
            String[] pathParts = withoutPrefix.split("\\.", 3);
            
            if (pathParts.length >= 2) {
                String moduleName = pathParts[0];
                String fieldName = pathParts[1];
                
                // 映射模块名称
                String mappedModuleName = mapModuleName(moduleName);
                
                // 映射字段名称
                String mappedFieldName = fieldMapping.getOrDefault(fieldName, fieldName);
                
                return "mergedResumeData." + mappedModuleName + "." + mappedFieldName;
            } else if (pathParts.length == 1) {
                // 处理resumeData.xxx格式
                String fieldName = pathParts[0];
                String mappedFieldName = fieldMapping.getOrDefault(fieldName, fieldName);
                return "mergedResumeData.basicInfo." + mappedFieldName;
            }
            
            // 如果解析失败，直接替换前缀
            return expression.replace("resumeData.", "mergedResumeData.");
        }
        
        // 修复：处理mergedResumeData.basic_info格式，统一为basicInfo
        if (expression.contains("basic_info")) {
            return expression.replace("basic_info", "basicInfo");
        }
        
        // 处理循环变量表达式（如item.company, skill.name等）
        if (expression.startsWith("item.") || expression.startsWith("skill.") || 
            expression.startsWith("project.") || expression.startsWith("education.") ||
            expression.startsWith("social.") || expression.startsWith("info.") ||
            expression.startsWith("award.") || expression.startsWith("certificate.") ||
            expression.startsWith("language.") || expression.startsWith("hobby.") ||
            expression.startsWith("training.") || expression.startsWith("internship.") ||
            expression.startsWith("volunteer.") || expression.startsWith("research.") ||
            expression.startsWith("publication.") || expression.startsWith("portfolio.") ||
            expression.startsWith("work.") || expression.startsWith("experience.")) {
            
            // 修复：对于item.开头的表达式，去掉item前缀，直接使用循环变量名
            if (expression.startsWith("item.")) {
                String[] itemParts = expression.split("\\.", 3);
                if (itemParts.length >= 3) {
                    // item.work.company -> work.company
                    String varName = itemParts[1];
                    String fieldName = itemParts[2];
                    String mappedFieldName = fieldMapping.getOrDefault(fieldName, fieldName);
                    return varName + "." + mappedFieldName;
                } else if (itemParts.length == 2) {
                    // item.company -> work.company (需要推断变量名)
                    String fieldName = itemParts[1];
                    String mappedFieldName = fieldMapping.getOrDefault(fieldName, fieldName);
                    // 这里返回原表达式，让后续处理决定变量名
                    return "item." + mappedFieldName;
                }
            }
            
            String[] parts = expression.split("\\.", 2);
            String varName = parts[0];
            String fieldName = parts.length > 1 ? parts[1] : "";
            
            // 映射字段名称
            String mappedFieldName = fieldMapping.getOrDefault(fieldName, fieldName);
            
            return varName + "." + mappedFieldName;
        }
        
        // 处理模块级别的表达式
        if (expression.contains(".")) {
            String[] parts = expression.split("\\.", 2);
            String moduleName = parts[0];
            String fieldName = parts.length > 1 ? parts[1] : "";
            
            // 映射模块名称
            String mappedModuleName = mapModuleName(moduleName);
            
            // 映射字段名称
            String mappedFieldName = fieldMapping.getOrDefault(fieldName, fieldName);
            
            return "mergedResumeData." + mappedModuleName + "." + mappedFieldName;
        }
        
        // 处理单个字段名称
        String mappedField = fieldMapping.getOrDefault(expression, expression);
        return "mergedResumeData.basicInfo." + mappedField;
    }
    
    /**
     * 转换为Vue for表达式
     * 
     * @param forExpression for表达式
     * @return Vue for表达式
     */
    private String convertToVueForExpression(String forExpression) {
        // 处理格式: "item in items" -> "(item, index) in mergedResumeData.items"
        if (forExpression.contains(" in ")) {
            String[] parts = forExpression.split(" in ");
            if (parts.length == 2) {
                String item = parts[0].trim();
                String items = parts[1].trim();
                
                // 如果已经包含mergedResumeData前缀，确保格式正确
                if (items.startsWith("mergedResumeData.")) {
                    // 修复：确保包含index参数
                    if (item.contains(",")) {
                        return item + " in " + items;
                    } else {
                        return "(" + item + ", index) in " + items;
                    }
                }
                
                // 修复：处理resumeData.xxx格式的集合路径
                if (items.startsWith("resumeData.")) {
                    String withoutPrefix = items.substring("resumeData.".length());
                    
                    // 如果路径中还有子路径，如 resumeData.xxx，直接映射
                    String mappedItems = mapCollectionName(withoutPrefix);
                    return "(" + item + ", index) in mergedResumeData." + mappedItems;
                }
                
                // 映射集合名称
                String mappedItems = mapCollectionName(items);
                
                // 修复：确保包含index参数
                return "(" + item + ", index) in mergedResumeData." + mappedItems;
            }
        }
        
        return forExpression;
    }
    
    /**
     * 创建字段名称映射表
     * 
     * @return 字段映射表
     */
    private Map<String, String> createFieldMapping() {
        Map<String, String> mapping = new HashMap<>();
        
        // 基本信息字段映射
        mapping.put("name", "name");
        mapping.put("fullname", "name");
        mapping.put("full_name", "name");
        mapping.put("email", "email");
        mapping.put("email_address", "email");
        mapping.put("phone", "phone");
        mapping.put("phone_number", "phone");
        mapping.put("mobile", "phone");
        mapping.put("address", "address");
        mapping.put("location", "address");
        mapping.put("website", "website");
        mapping.put("web", "website");
        mapping.put("linkedin", "linkedin");
        mapping.put("github", "github");
        mapping.put("avatar", "avatar");
        mapping.put("photo", "avatar");
        mapping.put("image", "avatar");
        mapping.put("job_title", "title");
        mapping.put("jobtitle", "title");
        mapping.put("title", "title");
        mapping.put("summary", "summary");
        mapping.put("bio", "summary");
        mapping.put("description", "summary");
        mapping.put("about", "summary");
        
        // 工作经历字段映射
        mapping.put("company", "company");
        mapping.put("company_name", "company");
        mapping.put("employer", "company");
        mapping.put("organization", "company");
        mapping.put("position", "position");
        mapping.put("role", "role");
        mapping.put("start_date", "startDate");
        mapping.put("startdate", "startDate");
        mapping.put("start", "startDate");
        mapping.put("from", "startDate");
        mapping.put("end_date", "endDate");
        mapping.put("enddate", "endDate");
        mapping.put("end", "endDate");
        mapping.put("to", "endDate");
        mapping.put("description", "description");
        mapping.put("desc", "description");
        mapping.put("details", "description");
        mapping.put("responsibilities", "description");
        mapping.put("location", "location");
        mapping.put("place", "location");
        mapping.put("city", "location");
        
        // 教育经历字段映射
        mapping.put("school", "school");
        mapping.put("university", "school");
        mapping.put("college", "school");
        mapping.put("institution", "school");
        mapping.put("degree", "degree");
        mapping.put("qualification", "degree");
        mapping.put("major", "major");
        mapping.put("field", "major");
        mapping.put("subject", "major");
        mapping.put("specialization", "major");
        mapping.put("gpa", "gpa");
        mapping.put("grade", "gpa");
        mapping.put("score", "gpa");
        
        // 项目经历字段映射
        mapping.put("project_name", "name");
        mapping.put("projectname", "name");
        mapping.put("name", "name");
        mapping.put("title", "name");
        mapping.put("technologies", "technologies");  // 修复：保持复数形式
        mapping.put("technology", "technologies");    // 映射到复数形式
        mapping.put("tech", "technologies");
        mapping.put("stack", "technologies");
        mapping.put("tools", "technologies");
        mapping.put("role", "role");
        mapping.put("responsibility", "role");
        mapping.put("url", "url");
        mapping.put("link", "url");
        mapping.put("website", "url");
        mapping.put("demo", "url");
        mapping.put("github", "url");
        
        // 技能字段映射
        mapping.put("skill_name", "name");
        mapping.put("skillname", "name");
        mapping.put("name", "name");
        mapping.put("skill", "name");
        mapping.put("skill_level", "level");
        mapping.put("skilllevel", "level");
        mapping.put("level", "level");
        mapping.put("proficiency", "level");
        mapping.put("rating", "level");
        
        // 奖项字段映射
        mapping.put("award_name", "name");
        mapping.put("awardname", "name");
        mapping.put("award", "name");
        mapping.put("honor", "name");
        mapping.put("achievement", "name");
        mapping.put("issuer", "organization");        // 修复：统一为organization
        mapping.put("organization", "organization");  // 保持一致
        mapping.put("institution", "organization");
        mapping.put("date", "date");
        mapping.put("year", "date");
        mapping.put("time", "date");
        mapping.put("level", "level");               // 添加奖项级别字段
        
        // 证书字段映射
        mapping.put("certificate_name", "name");
        mapping.put("certificatename", "name");
        mapping.put("certificate", "name");
        mapping.put("certification", "name");
        mapping.put("credential", "name");
        
        return mapping;
    }
    
    /**
     * 映射模块名称
     * 
     * @param moduleName 原模块名称
     * @return 映射后的模块名称
     */
    private String mapModuleName(String moduleName) {
        Map<String, String> moduleMapping = new HashMap<>();
        
        // 基础信息模块
        moduleMapping.put("basic_info", "basicInfo");
        moduleMapping.put("basicinfo", "basicInfo");
        moduleMapping.put("basic", "basicInfo");
        
        // 工作经历模块（前端使用workExperiences）
        moduleMapping.put("work_experience", "workExperiences");
        moduleMapping.put("work_experiences", "workExperiences");
        moduleMapping.put("workexperiences", "workExperiences");
        moduleMapping.put("work", "workExperiences");
        moduleMapping.put("experiences", "workExperiences");
        
        // 教育经历模块（前端使用educations）
        moduleMapping.put("education", "educations");
        moduleMapping.put("educations", "educations");
        moduleMapping.put("edu", "educations");
        
        // 项目经历模块（前端使用projects）
        moduleMapping.put("project", "projects");
        moduleMapping.put("projects", "projects");
        
        // 技能模块
        moduleMapping.put("skills", "skills");
        moduleMapping.put("skill", "skills");
        
        // 语言能力模块（前端使用languages）
        moduleMapping.put("language", "languages");
        moduleMapping.put("languages", "languages");
        
        // 获奖情况模块（前端使用awards）
        moduleMapping.put("award", "awards");
        moduleMapping.put("awards", "awards");
        
        // 证书资质模块（前端使用certificates）
        moduleMapping.put("certificate", "certificates");
        moduleMapping.put("certificates", "certificates");
        moduleMapping.put("certs", "certificates");
        
        // 实习经历模块（前端使用internships）
        moduleMapping.put("internship", "internships");
        moduleMapping.put("internships", "internships");
        
        // 培训经历模块（前端使用trainings）
        moduleMapping.put("training", "trainings");
        moduleMapping.put("trainings", "trainings");
        
        // 志愿服务模块（前端使用volunteerExperiences）
        moduleMapping.put("volunteer_experience", "volunteerExperiences");
        moduleMapping.put("volunteer_experiences", "volunteerExperiences");
        moduleMapping.put("volunteer", "volunteerExperiences");
        
        // 研究经历模块（前端使用researchExperiences）
        moduleMapping.put("research_experience", "researchExperiences");
        moduleMapping.put("research_experiences", "researchExperiences");
        moduleMapping.put("research", "researchExperiences");
        
        // 论文发表模块（前端使用publications）
        moduleMapping.put("publication", "publications");
        moduleMapping.put("publications", "publications");
        
        // 作品集模块（前端使用portfolios）
        moduleMapping.put("portfolio", "portfolios");
        moduleMapping.put("portfolios", "portfolios");
        
        // 自荐信模块（前端使用coverLetters）
        moduleMapping.put("cover_letter", "coverLetters");
        moduleMapping.put("cover_letters", "coverLetters");
        
        // 自我评价模块（前端使用selfEvaluation）
        moduleMapping.put("self_evaluation", "selfEvaluation");
        moduleMapping.put("self_evaluations", "selfEvaluation");
        
        // 兴趣爱好模块
        moduleMapping.put("hobbies", "hobbies");
        moduleMapping.put("hobby", "hobbies");
        
        // 自定义模块
        moduleMapping.put("custom", "custom");
        
        // 处理自定义模块ID格式：custom_[时间戳]
        if (moduleName != null && moduleName.startsWith("custom_")) {
            return "custom";
        }
        
        return moduleMapping.getOrDefault(moduleName, moduleName);
    }
    
    /**
     * 映射集合名称
     * 
     * @param collectionName 集合名称
     * @return 映射后的集合名称
     */
    private String mapCollectionName(String collectionName) {
        Map<String, String> collectionMapping = new HashMap<>();
        
        // 工作经历集合
        collectionMapping.put("work_experiences", "workExperiences");
        collectionMapping.put("work_experience", "workExperiences");
        collectionMapping.put("workexperiences", "workExperiences");
        collectionMapping.put("work", "workExperiences");
        collectionMapping.put("experiences", "workExperiences");
        
        // 教育经历集合
        collectionMapping.put("educations", "educations");
        collectionMapping.put("education", "educations");
        collectionMapping.put("edu", "educations");
        
        // 项目经历集合
        collectionMapping.put("projects", "projects");
        collectionMapping.put("project", "projects");
        
        // 技能集合
        collectionMapping.put("skills", "skills");
        collectionMapping.put("skill", "skills");
        
        // 语言能力集合
        collectionMapping.put("languages", "languages");
        collectionMapping.put("language", "languages");
        
        // 奖项集合
        collectionMapping.put("awards", "awards");
        collectionMapping.put("award", "awards");
        
        // 证书集合
        collectionMapping.put("certificates", "certificates");
        collectionMapping.put("certificate", "certificates");
        collectionMapping.put("certs", "certificates");
        
        // 实习经历集合
        collectionMapping.put("internships", "internships");
        collectionMapping.put("internship", "internships");
        
        // 培训经历集合
        collectionMapping.put("trainings", "trainings");
        collectionMapping.put("training", "trainings");
        
        // 志愿服务集合
        collectionMapping.put("volunteerExperiences", "volunteerExperiences");
        collectionMapping.put("volunteer_experiences", "volunteerExperiences");
        collectionMapping.put("volunteer", "volunteerExperiences");
        
        // 研究经历集合
        collectionMapping.put("researchExperiences", "researchExperiences");
        collectionMapping.put("research_experiences", "researchExperiences");
        collectionMapping.put("research", "researchExperiences");
        
        // 论文发表集合
        collectionMapping.put("publications", "publications");
        collectionMapping.put("publication", "publications");
        
        // 作品集集合
        collectionMapping.put("portfolios", "portfolios");
        collectionMapping.put("portfolio", "portfolios");
        
        // 兴趣爱好集合
        collectionMapping.put("hobbies", "hobbies");
        collectionMapping.put("hobby", "hobbies");
        
        return collectionMapping.getOrDefault(collectionName, collectionName);
    }
    
    /**
     * 生成Vue组件
     * 
     * @param templateContent 模板内容
     * @param styleContent 样式内容
     * @param options 转换选项
     * @return Vue组件内容
     */
    private String generateVueComponent(String templateContent, String styleContent, HtmlConversionRequest.ConversionOptions options) {
        StringBuilder vueBuilder = new StringBuilder();
        
        // 模板部分
        vueBuilder.append("<template>\n");
        
        // 修复：检查并处理容器结构冗余问题
        String processedContent = templateContent;
        
        // 检查是否存在多层resume-template容器
        Pattern resumeTemplatePattern = Pattern.compile("class=\"[^\"]*resume-template[^\"]*\"");
        int resumeTemplateCount = (int) resumeTemplatePattern.matcher(processedContent).results().count();
        
        if (resumeTemplateCount > 0) {
            // 如果已经包含resume-template容器，直接使用，并添加:class绑定
            String updatedContent = processedContent.replaceFirst(
                "class=\"([^\"]*resume-template[^\"]*?)\"", 
                "class=\"$1\" :class=\"{ 'preview-mode': !isDraggable }\""
            );
            
            // 修复：如果存在多层容器，移除内层的resume-template
            if (resumeTemplateCount > 1) {
                // 找到第一个resume-template后，移除后续的resume-template类
                boolean firstFound = false;
                Pattern multiplePattern = Pattern.compile("class=\"([^\"]*resume-template[^\"]*?)\"");
                StringBuilder sb = new StringBuilder();
                int lastEnd = 0;
                
                for (var matcher = multiplePattern.matcher(updatedContent); matcher.find(); ) {
                    sb.append(updatedContent, lastEnd, matcher.start());
                    if (!firstFound) {
                        // 保留第一个resume-template
                        sb.append(matcher.group(0));
                        firstFound = true;
                    } else {
                        // 移除后续的resume-template
                        String classValue = matcher.group(1);
                        String cleanedClass = classValue.replaceAll("\\s*resume-template\\s*", " ").trim();
                        if (!cleanedClass.isEmpty()) {
                            sb.append("class=\"").append(cleanedClass).append("\"");
                        }
                    }
                    lastEnd = matcher.end();
                }
                sb.append(updatedContent.substring(lastEnd));
                updatedContent = sb.toString();
                log.info("检测到多层resume-template容器，已移除冗余容器");
            }
            
            vueBuilder.append(indentContent(updatedContent, 1));
        } else {
            // 如果没有包含，添加外层容器
            vueBuilder.append("  <div class=\"resume-template\" :class=\"{ 'preview-mode': !isDraggable }\">\n");
            vueBuilder.append(indentContent(processedContent, 2));
            vueBuilder.append("\n  </div>\n");
        }
        
        vueBuilder.append("</template>\n\n");
        
        // 脚本部分
        vueBuilder.append("<script setup>\n");
        vueBuilder.append("import { useResumeTemplateBase } from '~/composables/template/useResumeTemplateBase'\n\n");
        
        // 定义props
        vueBuilder.append("// 定义组件属性\n");
        vueBuilder.append("const props = defineProps({\n");
        vueBuilder.append("  resumeData: {\n");
        vueBuilder.append("    type: Object,\n");
        vueBuilder.append("    default: () => ({})\n");
        vueBuilder.append("  },\n");
        vueBuilder.append("  textStyle: {\n");
        vueBuilder.append("    type: Object,\n");
        vueBuilder.append("    default: () => ({})\n");
        vueBuilder.append("  },\n");
        vueBuilder.append("  isDraggable: {\n");
        vueBuilder.append("    type: Boolean,\n");
        vueBuilder.append("    default: false\n");
        vueBuilder.append("  },\n");
        vueBuilder.append("  visibleModules: {\n");
        vueBuilder.append("    type: Array,\n");
        vueBuilder.append("    default: () => []\n");
        vueBuilder.append("  }\n");
        vueBuilder.append("})\n\n");
        
        // 定义emits
        vueBuilder.append("// 定义事件\n");
        vueBuilder.append("const emit = defineEmits([\n");
        vueBuilder.append("  'update-resume-data',\n");
        vueBuilder.append("  'update-basic-info',\n");
        vueBuilder.append("  'module-operation'\n");
        vueBuilder.append("])\n\n");
        
        // 使用基础组合式函数
        vueBuilder.append("// 使用基础组合式函数\n");
        vueBuilder.append("const {\n");
        vueBuilder.append("  // 数据相关\n");
        vueBuilder.append("  mergedResumeData,\n");
        vueBuilder.append("  sortedModules,\n");
        vueBuilder.append("  leftColumnModules,\n");
        vueBuilder.append("  rightColumnModules,\n");
        vueBuilder.append("  \n");
        vueBuilder.append("  // 样式相关\n");
        vueBuilder.append("  mergedTextStyle,\n");
        vueBuilder.append("  titleStyle,\n");
        vueBuilder.append("  subtitleStyle,\n");
        vueBuilder.append("  bodyStyle,\n");
        vueBuilder.append("  \n");
        vueBuilder.append("  // 操作相关\n");
        vueBuilder.append("  hoveredSkill,\n");
        vueBuilder.append("  hoveredExperience,\n");
        vueBuilder.append("  hoveredEducation,\n");
        vueBuilder.append("  hoveredProject,\n");
        vueBuilder.append("  hoveredAward,\n");
        vueBuilder.append("  hoveredCertificate,\n");
        vueBuilder.append("  hoveredLanguage,\n");
        vueBuilder.append("  hoveredHobby,\n");
        vueBuilder.append("  hoveredInternship,\n");
        vueBuilder.append("  hoveredTraining,\n");
        vueBuilder.append("  hoveredVolunteer,\n");
        vueBuilder.append("  hoveredResearch,\n");
        vueBuilder.append("  hoveredPublication,\n");
        vueBuilder.append("  hoveredPortfolio,\n");
        vueBuilder.append("  moveModuleUp,\n");
        vueBuilder.append("  moveModuleDown,\n");
        vueBuilder.append("  deleteModule,\n");
        vueBuilder.append("  handleBasicInfoUpdate,\n");
        vueBuilder.append("  \n");
        vueBuilder.append("  // 工具方法\n");
        vueBuilder.append("  formatWorkPeriod,\n");
        vueBuilder.append("  formatEducationPeriod,\n");
        vueBuilder.append("  formatProjectPeriod,\n");
        vueBuilder.append("  getSkillProgressStyle\n");
        vueBuilder.append("} = useResumeTemplateBase(props, emit)\n");
        vueBuilder.append("</script>\n\n");
        
        // 样式部分
        vueBuilder.append("<style scoped>\n");
        if (StringUtils.hasText(styleContent)) {
            // 处理样式内容，移除可能导致冲突的全局样式
            String processedStyle = processStyleContent(styleContent);
            vueBuilder.append(processedStyle);
        } else {
            vueBuilder.append("/* 在此添加样式 */\n");
        }
        vueBuilder.append("</style>\n");
        
        return vueBuilder.toString();
    }
    
    /**
     * 处理样式内容
     * 
     * @param styleContent 原始样式内容
     * @return 处理后的样式内容
     */
    private String processStyleContent(String styleContent) {
        String result = styleContent;
        
        // 移除body标签样式，因为在Vue组件中不需要
        result = result.replaceAll("(?s)\\s*body\\s*\\{[^}]*\\}", "");
        
        // 将html相关的样式移除或调整
        result = result.replaceAll("(?s)\\s*html\\s*\\{[^}]*\\}", "");
        
        // 调整根容器样式
        result = result.replace(".resume-template", ".resume-template");
        
        // 确保样式不会影响到组件外部
        result = result.replaceAll("\\*\\s*\\{", ".resume-template * {");
        
        return result;
    }
    
    /**
     * 缩进内容
     * 
     * @param content 内容
     * @param indentLevel 缩进级别
     * @return 缩进后的内容
     */
    private String indentContent(String content, int indentLevel) {
        String indent = "  ".repeat(indentLevel);
        return Arrays.stream(content.split("\n"))
                .map(line -> line.trim().isEmpty() ? "" : indent + line)
                .collect(Collectors.joining("\n"));
    }
    
    /**
     * 获取Vue文件名
     * 
     * @param htmlFileName HTML文件名
     * @return Vue文件名
     */
    private String getVueFileName(String htmlFileName) {
        if (htmlFileName.toLowerCase().endsWith(".html")) {
            return htmlFileName.substring(0, htmlFileName.length() - 5) + ".vue";
        }
        return htmlFileName + ".vue";
    }
    
    /**
     * 生成转换详情
     * 
     * @param htmlContent HTML内容
     * @param vueContent Vue内容
     * @return 转换详情
     */
    private HtmlConversionResponse.ConversionDetails generateConversionDetails(String htmlContent, String vueContent) {
        // 统计模块数量
        int moduleCount = (int) DATA_MODULE_PATTERN.matcher(htmlContent).results().count();
        
        // 统计字段数量
        int fieldCount = (int) DATA_FIELD_PATTERN.matcher(htmlContent).results().count();
        
        // 统计代码行数
        int vueCodeLines = vueContent.split("\n").length;
        
        return HtmlConversionResponse.ConversionDetails.builder()
                .moduleCount(moduleCount)
                .fieldCount(fieldCount)
                .vueCodeLines(vueCodeLines)
                .cssCodeLines(0) // 可以进一步统计
                .detectedComponents(Arrays.asList("useResumeTemplateBase"))
                .dataBindings(new HashMap<>()) // 可以进一步统计
                .vueFeatures(Arrays.asList("Composition API", "Scoped Styles"))
                .build();
    }
    
    /**
     * 生成单文件统计信息
     * 
     * @param result 转换结果
     * @return 统计信息
     */
    private HtmlConversionResponse.ConversionStatistics generateSingleFileStatistics(HtmlConversionResponse.ConversionResult result) {
        boolean isSuccess = "SUCCESS".equals(result.getStatus());
        
        return HtmlConversionResponse.ConversionStatistics.builder()
                .totalFiles(1)
                .successCount(isSuccess ? 1 : 0)
                .failedCount(isSuccess ? 0 : 1)
                .skippedCount(0)
                .totalProcessingTime(result.getProcessingTime())
                .averageProcessingTime(result.getProcessingTime())
                .successRate(isSuccess ? 100.0 : 0.0)
                .totalCodeLines(result.getDetails() != null ? result.getDetails().getVueCodeLines() : 0)
                .totalFileSize(result.getFileSize())
                .build();
    }
    
    /**
     * 生成批量统计信息
     * 
     * @param results 转换结果列表
     * @param totalProcessingTime 总处理时间
     * @return 统计信息
     */
    private HtmlConversionResponse.ConversionStatistics generateBatchStatistics(
            List<HtmlConversionResponse.ConversionResult> results, long totalProcessingTime) {
        
        int totalFiles = results.size();
        int successCount = (int) results.stream().filter(r -> "SUCCESS".equals(r.getStatus())).count();
        int failedCount = (int) results.stream().filter(r -> "FAILED".equals(r.getStatus())).count();
        int skippedCount = (int) results.stream().filter(r -> "SKIPPED".equals(r.getStatus())).count();
        
        long totalCodeLines = results.stream()
                .filter(r -> r.getDetails() != null)
                .mapToLong(r -> r.getDetails().getVueCodeLines())
                .sum();
        
        long totalFileSize = results.stream()
                .filter(r -> r.getFileSize() != null)
                .mapToLong(HtmlConversionResponse.ConversionResult::getFileSize)
                .sum();
        
        double successRate = totalFiles > 0 ? (double) successCount / totalFiles * 100 : 0.0;
        long averageProcessingTime = totalFiles > 0 ? totalProcessingTime / totalFiles : 0;
        
        return HtmlConversionResponse.ConversionStatistics.builder()
                .totalFiles(totalFiles)
                .successCount(successCount)
                .failedCount(failedCount)
                .skippedCount(skippedCount)
                .totalProcessingTime(totalProcessingTime)
                .averageProcessingTime(averageProcessingTime)
                .successRate(successRate)
                .totalCodeLines((int) totalCodeLines)
                .totalFileSize(totalFileSize)
                .build();
    }
    
    /**
     * 添加模块交互功能
     * 
     * @param content 内容
     * @return 添加交互功能后的内容
     */
    private String addModuleInteractions(String content) {
        // 识别模块标题的模式，通常是 h2、h3 标签
        Pattern modulePattern = Pattern.compile("(<section[^>]*>)\\s*(<div[^>]*module-header[^>]*>)?\\s*(<h[2-3][^>]*>)([^<]*技能[^<]*|[^<]*工作[^<]*|[^<]*教育[^<]*|[^<]*项目[^<]*|[^<]*语言[^<]*|[^<]*获奖[^<]*|[^<]*证书[^<]*|[^<]*实习[^<]*|[^<]*培训[^<]*|[^<]*志愿[^<]*|[^<]*研究[^<]*|[^<]*论文[^<]*|[^<]*作品[^<]*|[^<]*自荐[^<]*|[^<]*自我[^<]*|[^<]*兴趣[^<]*|[^<]*自定义[^<]*)(</h[2-3]>)", Pattern.CASE_INSENSITIVE);
        
        String result = modulePattern.matcher(content).replaceAll(matchResult -> {
            String sectionTag = matchResult.group(1);
            String headerDiv = matchResult.group(2) != null ? matchResult.group(2) : "";
            String h2Tag = matchResult.group(3);
            String titleText = matchResult.group(4);
            String h2CloseTag = matchResult.group(5);
            
            // 根据标题文本确定模块类型
            String moduleType = determineModuleType(titleText);
            
            // 生成模块操作按钮
            String moduleActions = generateModuleActions(moduleType);
            
            // 插入操作按钮
            if (!headerDiv.isEmpty()) {
                // 如果有header div，在header div后插入
                return sectionTag + headerDiv + moduleActions + h2Tag + titleText + h2CloseTag;
            } else {
                // 如果没有header div，在h2标签后插入
                return sectionTag + h2Tag + titleText + h2CloseTag + moduleActions;
            }
        });
        
        // 修复模块显示条件
        // 修复：将错误的基本信息字段条件改为正确的模块启用条件
        result = result.replaceAll(
            "v-if=\"mergedResumeData\\.basicInfo\\.name\\s*&&\\s*mergedResumeData\\.(\\w+)\\.length\\s*>\\s*0\"",
            "v-if=\"mergedResumeData.basicInfo.$1 && mergedResumeData.$1.length > 0\""
        );
        
        // 修复教育经历模块条件
        result = result.replaceAll(
            "v-if=\"mergedResumeData\\.basicInfo\\.education\\s*&&\\s*mergedResumeData\\.educations\\.length\\s*>\\s*0\"",
            "v-if=\"mergedResumeData.basicInfo.education && mergedResumeData.educations.length > 0\""
        );
        
        // 修复工作经历模块条件
        result = result.replaceAll(
            "v-if=\"mergedResumeData\\.basicInfo\\.work_experience\\s*&&\\s*mergedResumeData\\.workExperiences\\.length\\s*>\\s*0\"",
            "v-if=\"mergedResumeData.basicInfo.workExperience && mergedResumeData.workExperiences.length > 0\""
        );
        
        // 修复项目经历模块条件
        result = result.replaceAll(
            "v-if=\"mergedResumeData\\.basicInfo\\.project\\s*&&\\s*mergedResumeData\\.projects\\.length\\s*>\\s*0\"",
            "v-if=\"mergedResumeData.basicInfo.project && mergedResumeData.projects.length > 0\""
        );
        
        // 修复技能模块条件
        result = result.replaceAll(
            "v-if=\"mergedResumeData\\.basicInfo\\.skills\\s*&&\\s*mergedResumeData\\.skills\\.length\\s*>\\s*0\"",
            "v-if=\"mergedResumeData.basicInfo.skills && mergedResumeData.skills.length > 0\""
        );
        
        // 修复获奖模块条件
        result = result.replaceAll(
            "v-if=\"mergedResumeData\\.basicInfo\\.name\\s*&&\\s*mergedResumeData\\.awards\\.length\\s*>\\s*0\"",
            "v-if=\"mergedResumeData.basicInfo.award && mergedResumeData.awards.length > 0\""
        );
        
        // 修复自我评价模块条件
        result = result.replaceAll(
            "v-if=\"mergedResumeData\\.basicInfo\\.self_evaluation\\s*&&\\s*mergedResumeData\\.selfEvaluation\\.content\"",
            "v-if=\"mergedResumeData.basicInfo.selfEvaluation && mergedResumeData.selfEvaluation.content\""
        );
        
        return result;
    }
    
    /**
     * 添加内容项交互功能
     * 
     * @param content 内容
     * @return 添加交互功能后的内容
     */
    private String addItemInteractions(String content) {
        // 修复：查找带有 v-for 指令的元素，匹配包含index的格式，包含所有模块名称
        Pattern forPattern = Pattern.compile("(<[^>]+v-for=[\"']\\([^,]+,\\s*index\\)\\s+in\\s+mergedResumeData\\.(skills|workExperiences|educations|projects|awards|certificates|languages|hobbies|internships|trainings|volunteerExperiences|volunteer_experience|researchExperiences|research_experience|publications|portfolios)[^\"']*[\"'][^>]*>)", Pattern.CASE_INSENSITIVE);
        
        return forPattern.matcher(content).replaceAll(matchResult -> {
            String originalTag = matchResult.group(1);
            String collectionType = matchResult.group(2);
            
            // 生成内容项操作按钮
            String itemActions = generateItemActions(collectionType);
            
            // 在原标签后插入操作按钮
            return originalTag + itemActions;
        });
    }
    
    /**
     * 添加鼠标悬停事件绑定
     * 
     * @param content 内容
     * @return 添加悬停事件后的内容
     */
    private String addHoverEvents(String content) {
        // 修复：查找带有 v-for 指令的内容项元素，匹配正确的格式，包含所有模块名称
        Pattern forPattern = Pattern.compile("(<[^>]+v-for=[\"']\\(([^,]+),\\s*(index)\\)\\s+in\\s+mergedResumeData\\.(skills|workExperiences|educations|projects|awards|certificates|languages|hobbies|internships|trainings|volunteerExperiences|volunteer_experience|researchExperiences|research_experience|publications|portfolios)[^\"']*[\"'])([^>]*>)");
        
        return forPattern.matcher(content).replaceAll(matchResult -> {
            String forPart = matchResult.group(1);
            String itemVar = matchResult.group(2);
            String indexVar = matchResult.group(3); // 这里是"index"
            String collectionType = matchResult.group(4);
            String restOfTag = matchResult.group(5);
            
            // 确定悬停变量名
            String hoverVar = getHoverVariableName(collectionType);
            
            // 修复：添加悬停事件，确保事件绑定语法正确，注意空格
            String hoverEvents = " @mouseenter=\"" + hoverVar + " = " + indexVar + "\" @mouseleave=\"" + hoverVar + " = -1\"";
            
            return forPart + hoverEvents + restOfTag;
        });
    }
    
    /**
     * 添加拖拽样式类绑定
     * 
     * @param content 内容
     * @return 添加样式类后的内容
     */
    private String addDraggableClasses(String content) {
        // 修复：查找带有 v-for 指令的内容项元素，确保正确匹配并处理class属性，包含所有模块名称
        Pattern forPattern = Pattern.compile("(<[^>]+v-for=[\"']\\([^,]+,\\s*index\\)\\s+in\\s+mergedResumeData\\.(skills|workExperiences|educations|projects|awards|certificates|languages|hobbies|internships|trainings|volunteerExperiences|volunteer_experience|researchExperiences|research_experience|publications|portfolios)[^\"']*[\"'][^>]*?)\\s*(class=[\"']([^\"']*)[\"'])?([^>]*>)");
        
        return forPattern.matcher(content).replaceAll(matchResult -> {
            String beforeClass = matchResult.group(1);
            String collectionType = matchResult.group(2);
            String classAttr = matchResult.group(3); // 完整的class属性
            String existingClass = matchResult.group(4); // class的值
            String afterClass = matchResult.group(5);
            
            // 修复：构建新的class属性，确保语法正确
            String newClassBinding;
            if (existingClass != null && !existingClass.isEmpty()) {
                // 如果有现有class，保留并添加:class绑定
                newClassBinding = " class=\"" + existingClass + "\" :class=\"{ 'draggable': isDraggable }\"";
            } else {
                // 如果没有现有class，只添加:class绑定
                newClassBinding = " :class=\"{ 'draggable': isDraggable }\"";
            }
            
            // 如果原来有class属性，先移除它
            String cleanedBefore = beforeClass;
            if (classAttr != null) {
                cleanedBefore = beforeClass.replace(classAttr, "");
            }
            
            return cleanedBefore + newClassBinding + afterClass;
        });
    }
    
    /**
     * 确定模块类型
     * 
     * @param titleText 标题文本
     * @return 模块类型
     */
    private String determineModuleType(String titleText) {
        if (titleText.contains("技能")) return "skills";
        if (titleText.contains("工作")) return "workExperiences";
        if (titleText.contains("教育")) return "educations";
        if (titleText.contains("项目")) return "projects";
        if (titleText.contains("语言")) return "languages";
        if (titleText.contains("获奖")) return "awards";
        if (titleText.contains("证书")) return "certificates";
        if (titleText.contains("实习")) return "internships";
        if (titleText.contains("培训")) return "trainings";
        if (titleText.contains("志愿")) return "volunteerExperiences";
        if (titleText.contains("研究") || titleText.contains("科研")) return "researchExperiences";
        if (titleText.contains("论文")) return "publications";
        if (titleText.contains("作品")) return "portfolios";
        if (titleText.contains("自荐")) return "coverLetters";
        if (titleText.contains("自我")) return "selfEvaluation";
        if (titleText.contains("兴趣")) return "hobbies";
        if (titleText.contains("自定义")) return "custom";
        return "unknown";
    }
    
    /**
     * 生成模块操作按钮
     * 
     * @param moduleType 模块类型
     * @return 操作按钮HTML
     */
    private String generateModuleActions(String moduleType) {
        return "\n          <!-- 模块操作按钮 -->\n" +
               "          <div v-if=\"isDraggable\" class=\"module-actions\">\n" +
               "            <button @click=\"moveModuleUp('" + moduleType + "')\" class=\"module-action-btn\" title=\"模块上移\">\n" +
               "              <svg viewBox=\"0 0 20 20\" fill=\"currentColor\" class=\"w-4 h-4\">\n" +
               "                <path fill-rule=\"evenodd\" d=\"M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z\" clip-rule=\"evenodd\" />\n" +
               "              </svg>\n" +
               "            </button>\n" +
               "            <button @click=\"moveModuleDown('" + moduleType + "')\" class=\"module-action-btn\" title=\"模块下移\">\n" +
               "              <svg viewBox=\"0 0 20 20\" fill=\"currentColor\" class=\"w-4 h-4\">\n" +
               "                <path fill-rule=\"evenodd\" d=\"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z\" clip-rule=\"evenodd\" />\n" +
               "              </svg>\n" +
               "            </button>\n" +
               "            <button @click=\"deleteModule('" + moduleType + "')\" class=\"module-action-btn delete\" title=\"删除模块\">\n" +
               "              <svg viewBox=\"0 0 20 20\" fill=\"currentColor\" class=\"w-4 h-4\">\n" +
               "                <path fill-rule=\"evenodd\" d=\"M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z\" clip-rule=\"evenodd\" />\n" +
               "              </svg>\n" +
               "            </button>\n" +
               "          </div>\n";
    }
    
    /**
     * 生成内容项操作按钮
     * 
     * @param collectionType 集合类型
     * @return 操作按钮HTML
     */
    private String generateItemActions(String collectionType) {
        String hoverVar = getHoverVariableName(collectionType);
        String moveUpMethod = getMoveUpMethodName(collectionType);
        String moveDownMethod = getMoveDownMethodName(collectionType);
        String deleteMethod = getDeleteMethodName(collectionType);
        
        return "\n              <!-- " + collectionType + "项操作按钮 -->\n" +
               "              <div v-if=\"isDraggable && " + hoverVar + " === index\" class=\"item-actions\">\n" +
               "                <button \n" +
               "                  @click=\"" + moveUpMethod + "(index)\" \n" +
               "                  :disabled=\"index === 0\"\n" +
               "                  class=\"item-action-btn\"\n" +
               "                  title=\"上移\"\n" +
               "                >\n" +
               "                  ↑\n" +
               "                </button>\n" +
               "                <button \n" +
               "                  @click=\"" + moveDownMethod + "(index)\" \n" +
               "                  :disabled=\"index === mergedResumeData." + collectionType + ".length - 1\"\n" +
               "                  class=\"item-action-btn\"\n" +
               "                  title=\"下移\"\n" +
               "                >\n" +
               "                  ↓\n" +
               "                </button>\n" +
               "                <button \n" +
               "                  @click=\"" + deleteMethod + "(index)\"\n" +
               "                  class=\"item-action-btn delete\"\n" +
               "                  title=\"删除\"\n" +
               "                >\n" +
               "                  ×\n" +
               "                </button>\n" +
               "              </div>\n";
    }
    
    /**
     * 获取悬停变量名
     * 
     * @param collectionType 集合类型
     * @return 悬停变量名
     */
    private String getHoverVariableName(String collectionType) {
        switch (collectionType) {
            case "skills": return "hoveredSkill";
            case "workExperiences": return "hoveredExperience";
            case "educations": return "hoveredEducation";
            case "projects": return "hoveredProject";
            case "awards": return "hoveredAward";
            case "certificates": return "hoveredCertificate";
            case "languages": return "hoveredLanguage";
            case "hobbies": return "hoveredHobby";
            case "internships": return "hoveredInternship";
            case "trainings": return "hoveredTraining";
            case "volunteerExperiences": return "hoveredVolunteer";
            case "volunteer_experience": return "hoveredVolunteer";
            case "researchExperiences": return "hoveredResearch";
            case "research_experience": return "hoveredResearch";
            case "publications": return "hoveredPublication";
            case "portfolios": return "hoveredPortfolio";
            default: return "hoveredItem";
        }
    }
    
    /**
     * 获取上移方法名
     * 
     * @param collectionType 集合类型
     * @return 上移方法名
     */
    private String getMoveUpMethodName(String collectionType) {
        switch (collectionType) {
            case "skills": return "moveSkillUp";
            case "workExperiences": return "moveExperienceUp";
            case "educations": return "moveEducationUp";
            case "projects": return "moveProjectUp";
            case "awards": return "moveAwardUp";
            case "certificates": return "moveCertificateUp";
            case "languages": return "moveLanguageUp";
            case "hobbies": return "moveHobbyUp";
            case "internships": return "moveInternshipUp";
            case "trainings": return "moveTrainingUp";
            case "volunteerExperiences": return "moveVolunteerUp";
            case "volunteer_experience": return "moveVolunteerUp";
            case "researchExperiences": return "moveResearchUp";
            case "research_experience": return "moveResearchUp";
            case "publications": return "movePublicationUp";
            case "portfolios": return "movePortfolioUp";
            default: return "moveItemUp";
        }
    }
    
    /**
     * 获取下移方法名
     * 
     * @param collectionType 集合类型
     * @return 下移方法名
     */
    private String getMoveDownMethodName(String collectionType) {
        switch (collectionType) {
            case "skills": return "moveSkillDown";
            case "workExperiences": return "moveExperienceDown";
            case "educations": return "moveEducationDown";
            case "projects": return "moveProjectDown";
            case "awards": return "moveAwardDown";
            case "certificates": return "moveCertificateDown";
            case "languages": return "moveLanguageDown";
            case "hobbies": return "moveHobbyDown";
            case "internships": return "moveInternshipDown";
            case "trainings": return "moveTrainingDown";
            case "volunteerExperiences": return "moveVolunteerDown";
            case "volunteer_experience": return "moveVolunteerDown";
            case "researchExperiences": return "moveResearchDown";
            case "research_experience": return "moveResearchDown";
            case "publications": return "movePublicationDown";
            case "portfolios": return "movePortfolioDown";
            default: return "moveItemDown";
        }
    }
    
    /**
     * 获取删除方法名
     * 
     * @param collectionType 集合类型
     * @return 删除方法名
     */
    private String getDeleteMethodName(String collectionType) {
        switch (collectionType) {
            case "skills": return "deleteSkill";
            case "workExperiences": return "deleteExperience";
            case "educations": return "deleteEducation";
            case "projects": return "deleteProject";
            case "awards": return "deleteAward";
            case "certificates": return "deleteCertificate";
            case "languages": return "deleteLanguage";
            case "hobbies": return "deleteHobby";
            case "internships": return "deleteInternship";
            case "trainings": return "deleteTraining";
            case "volunteerExperiences": return "deleteVolunteer";
            case "volunteer_experience": return "deleteVolunteer";
            case "researchExperiences": return "deleteResearch";
            case "research_experience": return "deleteResearch";
            case "publications": return "deletePublication";
            case "portfolios": return "deletePortfolio";
            default: return "deleteItem";
        }
    }
    
    /**
     * 添加图像属性绑定
     * 
     * @param content 内容
     * @return 处理后的内容
     */
    private String addImageBindings(String content) {
        String result = content;
        
        // 处理头像图像 - 查找没有src属性的img标签
        Pattern imgPattern = Pattern.compile("(<img[^>]*?)\\s*(width=\"[^\"]*\"[^>]*?height=\"[^\"]*\"[^>]*?style=\"[^\"]*border-radius[^\"]*\"[^>]*?)(/?>)");
        result = imgPattern.matcher(result).replaceAll(matchResult -> {
            String imgStart = matchResult.group(1);
            String attributes = matchResult.group(2);
            String closure = matchResult.group(3);
            
            // 检查是否已经有src或:src属性
            if (!imgStart.contains("src=") && !attributes.contains("src=")) {
                // 添加头像绑定
                imgStart += " :src=\"mergedResumeData.basicInfo.avatar\" alt=\"头像\"";
            }
            
            return imgStart + " " + attributes + " " + closure;
        });
        
        // 处理其他可能的图像绑定
        Pattern generalImgPattern = Pattern.compile("(<img[^>]*?)(?<!:src=[\"\'][^\"\']*)(\\s*/?>)");
        result = generalImgPattern.matcher(result).replaceAll(matchResult -> {
            String imgTag = matchResult.group(1);
            String closure = matchResult.group(2);
            
            // 如果没有src属性且包含头像相关的类或ID，添加头像绑定
            if (!imgTag.contains("src=") && (imgTag.contains("avatar") || imgTag.contains("photo") || imgTag.contains("header-right"))) {
                imgTag += " :src=\"mergedResumeData.basicInfo.avatar\" alt=\"头像\"";
            }
            
            return imgTag + closure;
        });
        
        return result;
    }
    
    /**
     * 全局修复数据绑定路径
     * 
     * @param content 内容
     * @return 修复后的内容
     */
    private String fixDataBindingPaths(String content) {
        String result = content;
        
        // 修复基本信息路径：basic_info → basicInfo
        result = result.replaceAll("mergedResumeData\\.basic_info", "mergedResumeData.basicInfo");
        result = result.replaceAll("resumeData\\.basic_info", "resumeData.basicInfo");
        
        // 修复志愿服务路径：volunteer_experience → volunteerExperiences
        result = result.replaceAll("mergedResumeData\\.volunteer_experience", "mergedResumeData.volunteerExperiences");
        result = result.replaceAll("resumeData\\.volunteer_experience", "resumeData.volunteerExperiences");
        
        // 修复研究经历路径：research_experience → researchExperiences
        result = result.replaceAll("mergedResumeData\\.research_experience", "mergedResumeData.researchExperiences");
        result = result.replaceAll("resumeData\\.research_experience", "resumeData.researchExperiences");
        
        // 修复缺失的交互功能：为volunteer_experience和research_experience添加悬停事件和操作按钮
        result = addMissingInteractions(result);
        
        return result;
    }
    
    /**
     * 为缺失交互功能的模块添加悬停事件和操作按钮
     * 
     * @param content 内容
     * @return 修复后的内容
     */
    private String addMissingInteractions(String content) {
        String result = content;
        
        // 为volunteer_experience添加模块级和内容项交互功能
        result = addCompleteInteractionsForModule(result, "volunteerExperiences", "hoveredVolunteer", 
                "moveVolunteerUp", "moveVolunteerDown", "deleteVolunteer", "志愿");
        
        // 为research_experience添加模块级和内容项交互功能
        result = addCompleteInteractionsForModule(result, "researchExperiences", "hoveredResearch", 
                "moveResearchUp", "moveResearchDown", "deleteResearch", "科研");
        
        return result;
    }
    
    /**
     * 为指定模块添加完整的交互功能（包括模块级和内容项级）
     * 
     * @param content 内容
     * @param moduleName 模块名称
     * @param hoverVar 悬停变量名
     * @param moveUpMethod 上移方法名
     * @param moveDownMethod 下移方法名
     * @param deleteMethod 删除方法名
     * @param titleKeyword 模块标题关键词
     * @return 修复后的内容
     */
    private String addCompleteInteractionsForModule(String content, String moduleName, String hoverVar,
            String moveUpMethod, String moveDownMethod, String deleteMethod, String titleKeyword) {
        
        String result = content;
        
        // 1. 添加模块级操作按钮（如果缺失）
        result = addModuleActionsIfMissing(result, moduleName, titleKeyword);
        
        // 2. 添加内容项交互功能
        result = addItemInteractionsForModule(result, moduleName, hoverVar, moveUpMethod, moveDownMethod, deleteMethod);
        
        return result;
    }
    
    /**
     * 为指定模块添加模块级操作按钮（如果缺失）
     * 
     * @param content 内容
     * @param moduleName 模块名称
     * @param titleKeyword 模块标题关键词
     * @return 修复后的内容
     */
    private String addModuleActionsIfMissing(String content, String moduleName, String titleKeyword) {
        // 查找包含该关键词的模块标题，且还没有module-actions的
        Pattern modulePattern = Pattern.compile(
            "(<section[^>]*>)\\s*(<div[^>]*module-header[^>]*>)?\\s*(<h[2-3][^>]*>[^<]*" + titleKeyword + "[^<]*</h[2-3]>)",
            Pattern.CASE_INSENSITIVE
        );
        
        return modulePattern.matcher(content).replaceAll(matchResult -> {
            String sectionTag = matchResult.group(1);
            String headerDiv = matchResult.group(2) != null ? matchResult.group(2) : "";
            String titleTag = matchResult.group(3);
            
            // 检查是否已经有module-actions
            String fullMatch = matchResult.group(0);
            if (fullMatch.contains("module-actions")) {
                return fullMatch; // 已经有了，不需要添加
            }
            
            // 生成模块操作按钮
            String moduleActions = generateModuleActions(moduleName);
            
            // 插入操作按钮
            if (!headerDiv.isEmpty()) {
                return sectionTag + headerDiv + moduleActions + titleTag;
            } else {
                return sectionTag + "<div class=\"module-header\">" + moduleActions + titleTag + "</div>";
            }
        });
    }
    
    /**
     * 为指定模块添加内容项交互功能
     * 
     * @param content 内容
     * @param moduleName 模块名称
     * @param hoverVar 悬停变量名
     * @param moveUpMethod 上移方法名
     * @param moveDownMethod 下移方法名
     * @param deleteMethod 删除方法名
     * @return 修复后的内容
     */
    private String addItemInteractionsForModule(String content, String moduleName, String hoverVar,
            String moveUpMethod, String moveDownMethod, String deleteMethod) {
        
        // 查找该模块的v-for循环，添加悬停事件
        Pattern forPattern = Pattern.compile(
            "(<[^>]+v-for=[\"']\\([^,]+,\\s*index\\)\\s+in\\s+mergedResumeData\\." + moduleName + "[^\"']*[\"'][^>]*?)\\s*:key=",
            Pattern.CASE_INSENSITIVE
        );
        
        String result = forPattern.matcher(content).replaceAll(matchResult -> {
            String originalTag = matchResult.group(1);
            
            // 添加悬停事件和拖拽样式类
            String updatedTag = originalTag;
            
            // 添加悬停事件（如果还没有）
            if (!updatedTag.contains("@mouseenter")) {
                updatedTag += " @mouseenter=\"" + hoverVar + " = index\" @mouseleave=\"" + hoverVar + " = -1\"";
            }
            
            // 添加拖拽样式类（如果还没有）
            if (!updatedTag.contains(":class")) {
                updatedTag += " :class=\"{ 'draggable': isDraggable }\"";
            }
            
            return updatedTag + " :key=";
        });
        
        // 查找该模块的v-for循环后面，添加操作按钮
        Pattern itemPattern = Pattern.compile(
            "(<[^>]+v-for=[\"']\\([^,]+,\\s*index\\)\\s+in\\s+mergedResumeData\\." + moduleName + "[^>]*>)",
            Pattern.CASE_INSENSITIVE
        );
        
        result = itemPattern.matcher(result).replaceAll(matchResult -> {
            String originalTag = matchResult.group(1);
            
            // 生成操作按钮HTML
            String itemActions = "\n              <!-- " + moduleName + "项操作按钮 -->\n" +
                               "              <div v-if=\"isDraggable && " + hoverVar + " === index\" class=\"item-actions\">\n" +
                               "                <button \n" +
                               "                  @click=\"" + moveUpMethod + "(index)\" \n" +
                               "                  :disabled=\"index === 0\"\n" +
                               "                  class=\"item-action-btn\"\n" +
                               "                  title=\"上移\"\n" +
                               "                >\n" +
                               "                  ↑\n" +
                               "                </button>\n" +
                               "                <button \n" +
                               "                  @click=\"" + moveDownMethod + "(index)\" \n" +
                               "                  :disabled=\"index === mergedResumeData." + moduleName + ".length - 1\"\n" +
                               "                  class=\"item-action-btn\"\n" +
                               "                  title=\"下移\"\n" +
                               "                >\n" +
                               "                  ↓\n" +
                               "                </button>\n" +
                               "                <button \n" +
                               "                  @click=\"" + deleteMethod + "(index)\"\n" +
                               "                  class=\"item-action-btn delete\"\n" +
                               "                  title=\"删除\"\n" +
                               "                >\n" +
                               "                  ×\n" +
                               "                </button>\n" +
                               "              </div>\n";
            
            return originalTag + itemActions;
        });
        
        return result;
    }

} 