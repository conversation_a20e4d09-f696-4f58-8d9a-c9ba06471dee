package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Pattern;

/**
 * 用户设置请求DTO
 * 
 * 主要功能：
 * 1. 接收用户设置更新的请求参数
 * 2. 参数校验和格式化
 * 3. 确保设置的有效性
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Schema(name = "用户设置请求", description = "用户设置更新的请求参数")
public class UserSettingsRequest {

    /**
     * 界面语言偏好
     * 支持的语言：zh-CN, en, ja, ko
     */
    @Schema(description = "界面语言偏好")
    @Pattern(regexp = "^(zh-CN|en|ja|ko)$", message = "不支持的语言类型")
    private String preferredLanguage;

    /**
     * 检查是否有字段需要更新
     * 
     * @return true-有字段需要更新，false-没有字段需要更新
     */
    public boolean hasFieldsToUpdate() {
        return preferredLanguage != null && !preferredLanguage.trim().isEmpty();
    }
} 