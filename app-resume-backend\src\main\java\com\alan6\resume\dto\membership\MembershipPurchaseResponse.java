package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员购买响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "MembershipPurchaseResponse", description = "会员购买响应")
public class MembershipPurchaseResponse {

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "ORD20241201123456")
    private String orderId;

    /**
     * 会员记录ID
     */
    @Schema(description = "会员记录ID", example = "123")
    private Long membershipId;

    /**
     * 套餐信息
     */
    @Schema(description = "套餐信息")
    private PackageInfo packageInfo;

    /**
     * 会员开始时间
     */
    @Schema(description = "会员开始时间", example = "2024-12-01 10:00:00")
    private LocalDateTime startTime;

    /**
     * 会员结束时间
     */
    @Schema(description = "会员结束时间", example = "2024-11-30 23:59:59")
    private LocalDateTime endTime;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额", example = "199.90")
    private BigDecimal totalAmount;

    /**
     * 支付信息
     */
    @Schema(description = "支付信息")
    private PaymentInfo paymentInfo;

    /**
     * 套餐信息内部类
     */
    @Data
    @Schema(name = "PackageInfo", description = "套餐信息")
    public static class PackageInfo {
        /**
         * 套餐ID
         */
        @Schema(description = "套餐ID", example = "2")
        private Long id;

        /**
         * 套餐名称
         */
        @Schema(description = "套餐名称", example = "年度会员")
        private String packageName;

        /**
         * 套餐代码
         */
        @Schema(description = "套餐代码", example = "yearly")
        private String packageCode;
    }

    /**
     * 支付信息内部类
     */
    @Data
    @Schema(name = "PaymentInfo", description = "支付信息")
    public static class PaymentInfo {
        /**
         * 支付方式
         */
        @Schema(description = "支付方式", example = "wechat")
        private String paymentMethod;

        /**
         * 支付URL（用于跳转支付）
         */
        @Schema(description = "支付URL", example = "weixin://wxpay/bizpayurl?pr=xxx")
        private String paymentUrl;

        /**
         * 二维码数据（用于扫码支付）
         */
        @Schema(description = "二维码数据", example = "data:image/png;base64,xxx")
        private String qrCode;
    }
} 