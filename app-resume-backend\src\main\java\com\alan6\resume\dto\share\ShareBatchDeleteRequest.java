package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 批量删除分享请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareBatchDeleteRequest", description = "批量删除分享请求")
public class ShareBatchDeleteRequest {

    /**
     * 分享ID列表
     */
    @Schema(description = "分享ID列表", example = "[1, 2, 3]")
    @NotEmpty(message = "分享ID列表不能为空")
    @Size(max = 100, message = "单次最多删除100个分享")
    private List<Long> shareIds;
} 