package com.alan6.resume.service;

import com.alan6.resume.dto.template.TemplateUploadRequest;
import com.alan6.resume.dto.template.TemplateUploadResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 模板上传服务接口
 * 
 * @description 定义简历模板上传相关的业务操作
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ITemplateUploadService {

    /**
     * 上传简历模板文件
     * 
     * @description 处理多文件上传，创建模板记录和文件记录
     * @param files 上传的文件列表（支持多个文件）
     * @param previewImage 预览图文件（可选）
     * @param request 模板上传请求参数
     * @param uploadUserId 上传用户ID（管理员）
     * @return 模板上传响应结果
     * @throws Exception 上传过程中的异常
     */
    TemplateUploadResponse uploadTemplateFiles(
            List<MultipartFile> files, 
            MultipartFile previewImage,
            TemplateUploadRequest request, 
            Long uploadUserId) throws Exception;

    /**
     * 验证模板代码唯一性
     * 
     * @description 检查模板代码是否已存在
     * @param templateCode 模板代码
     * @return true表示代码可用，false表示已存在
     */
    boolean isTemplateCodeAvailable(String templateCode);

    /**
     * 验证模板名称唯一性
     * 
     * @description 检查模板名称是否已存在
     * @param templateName 模板名称
     * @return true表示名称可用，false表示已存在
     */
    boolean isTemplateNameAvailable(String templateName);

    /**
     * 验证文件类型是否支持
     * 
     * @description 检查上传的文件类型是否为模板允许的类型
     * @param file 上传的文件
     * @return true表示文件类型支持，false表示不支持
     */
    boolean isFileTypeSupported(MultipartFile file);

    /**
     * 生成模板预览图URL
     * 
     * @description 根据上传的文件生成或查找预览图
     * @param files 上传的文件列表
     * @param bucketName 存储桶名称
     * @param templateCode 模板代码
     * @return 预览图URL，如果没有则返回默认图片URL
     */
    String generatePreviewImageUrl(List<MultipartFile> files, String bucketName, String templateCode);

    /**
     * 清理失败的上传记录
     * 
     * @description 当上传过程中出现错误时，清理已创建的记录和文件
     * @param templateId 模板ID
     * @param uploadedFileIds 已上传的文件ID列表
     */
    void cleanupFailedUpload(Long templateId, List<Long> uploadedFileIds);

    /**
     * 检查存储桶是否存在
     * 
     * @description 验证指定的MinIO桶是否存在，不存在则创建
     * @param bucketName 存储桶名称
     * @return true表示桶存在或创建成功，false表示创建失败
     */
    boolean ensureBucketExists(String bucketName);

    /**
     * 获取模板文件存储路径
     * 
     * @description 根据模板代码和本地路径生成完整的存储路径
     * @param templateCode 模板代码
     * @param localPath 本地路径配置
     * @return 完整的存储路径
     */
    String buildTemplatePath(String templateCode, String localPath);

    /**
     * 获取模板预览图数据
     * 
     * @description 从MinIO获取模板预览图的二进制数据
     * @param bucketName 存储桶名称
     * @param previewImageUrl 预览图文件路径
     * @return 预览图的二进制数据，如果失败则返回null
     */
    byte[] getPreviewImageData(String bucketName, String previewImageUrl);
} 