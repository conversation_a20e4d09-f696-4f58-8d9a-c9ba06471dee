import{k as T,r as w,A as J,i as se,D as he,c as i,o as a,a as e,q as U,p as R,v as Z,s as C,t as _,x as le,y as we,m as ee,z as ae,b as S,w as q,T as N,F as be,g as ye,d as I,h as H,Q as ne}from"./CURHyiUL.js";import{A as xe,u as ie,H as ke}from"./CKDEb6Y1.js";import{$ as k}from"./D1FrdRFX.js";import{_ as te}from"./DlAUqK2U.js";import{s as Ce}from"./x_rD_Ya3.js";const W=w(!1),j=w(null);class Q{static validatePhone(l){return l?/^1[3-9]\d{9}$/.test(l)?{isValid:!0,message:""}:{isValid:!1,message:"请输入正确的手机号格式"}:{isValid:!1,message:"请输入手机号"}}static validatePassword(l){return l?l.length<6?{isValid:!1,message:"密码长度不能少于6位"}:l.length>20?{isValid:!1,message:"密码长度不能超过20位"}:{isValid:!0,message:""}:{isValid:!1,message:"请输入密码"}}static validateLoginForm(l){const r={};let f=!0;const s=this.validatePhone(l.phone);s.isValid||(r.phone=s.message,f=!1);const p=this.validatePassword(l.password);return p.isValid||(r.password=p.message,f=!1),{isValid:f,errors:r}}}class Pe{static async request(l,r={}){return await ke.request(l,r)}}class _e{static buildPasswordLoginRequest(l){return{loginType:2,platform:"web",phone:l.phone,password:l.password}}}class Y{static setAccessToken(l){localStorage.setItem("auth_token",l)}static setRefreshToken(l){localStorage.setItem("refresh_token",l)}static setUserInfo(l){localStorage.setItem("user_info",JSON.stringify(l))}static clearAuthData(){localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_info")}}class oe{static async login(l){try{W.value=!0,j.value=null;const r=Q.validateLoginForm(l);if(!r.isValid)throw new Error(Object.values(r.errors)[0]);const f=_e.buildPasswordLoginRequest(l),s=await Pe.request(xe.ENDPOINTS.AUTH.PASSWORD_LOGIN,{method:"POST",body:f});if(console.log("🔍 密码登录API响应:",s),s.token){console.log("✅ 找到token，开始处理登录状态"),Y.setAccessToken(s.token);const o={id:s.userId,userId:s.userId,username:s.username,nickname:s.nickname,phone:s.phone,email:s.email,avatar:s.avatarUrl,gender:s.gender,status:s.status,isPhoneVerified:s.isPhoneVerified,preferredLanguage:s.preferredLanguage,registerPlatform:s.registerPlatform,lastLoginTime:s.lastLoginTime,membershipInfo:s.membershipInfo,permissions:s.permissions,isNewUser:s.isNewUser};console.log("📝 构建的用户信息:",o),Y.setUserInfo(o),s.refreshToken&&Y.setRefreshToken(s.refreshToken);const n=ie();console.log("🔄 更新全局认证状态前，当前登录状态:",n.isLoggedIn.value),n.updateLoginState(o,s.token),console.log("✅ 更新全局认证状态后，当前登录状态:",n.isLoggedIn.value)}else console.error("❌ 响应中没有找到token字段"),console.log("🔍 完整响应内容:",JSON.stringify(s,null,2));const p={accessToken:s.token,refreshToken:s.refreshToken||null,tokenExpireTime:s.tokenExpireTime,userInfo:{id:s.userId,userId:s.userId,username:s.username,nickname:s.nickname,phone:s.phone,email:s.email,avatar:s.avatarUrl,gender:s.gender,status:s.status,isPhoneVerified:s.isPhoneVerified,preferredLanguage:s.preferredLanguage,registerPlatform:s.registerPlatform,lastLoginTime:s.lastLoginTime,membershipInfo:s.membershipInfo,permissions:s.permissions,isNewUser:s.isNewUser}};return k.success("登录成功！欢迎回来"),{success:!0,data:p,message:"登录成功"}}catch(r){const f=this.handleLoginError(r);return j.value=f,k.error(f),{success:!1,error:f,message:f}}finally{W.value=!1}}static handleLoginError(l){const r=l.message||"登录失败";return r.includes("用户不存在")?"账号不存在，请先注册":r.includes("密码错误")?"密码错误，请重新输入":r.includes("账户已被禁用")?"账户已被禁用，请联系客服":r.includes("登录失败次数过多")?"登录失败次数过多，请稍后再试":r.includes("参数验证失败")?"输入信息有误，请检查后重试":r}}const Me=()=>{const m=T(()=>W.value),l=T(()=>!!j.value),r=async o=>{const n=await oe.login(o);return n.success||(j.value=oe.handleLoginError(new Error(n.error))),n},f=()=>{j.value=null},s=o=>Q.validateLoginForm(o),p=(o,n)=>{switch(o){case"phone":return Q.validatePhone(n);case"password":return Q.validatePassword(n);default:return{isValid:!0,message:""}}};return{loading:re(W),error:re(j),isLoading:m,hasError:l,passwordLogin:r,clearError:f,validateForm:s,validateField:p}};function re(m){return T(()=>m.value)}const Ve={class:"password-login-form"},Le={class:"form-group"},Ie=["disabled"],Te={class:"error-message"},$e={class:"form-group"},Be={class:"password-input-wrapper"},Se=["type","disabled"],je=["disabled","aria-label"],Re={key:0,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ee={key:1,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Fe={class:"error-message"},ze={class:"remember-me-section"},Ae={class:"remember-me-label"},qe=["disabled"],Ne=["disabled"],Ue=["disabled"],Oe={key:0,class:"loading-spinner"},De={key:1},Ge={key:0,class:"global-error"},He={__name:"PasswordLoginForm",props:{autoFocus:{type:Boolean,default:!1},showRememberMe:{type:Boolean,default:!0}},emits:["login-success","login-error","forgot-password"],setup(m,{expose:l,emit:r}){const f=m,s=r,p=Me(),o=w({phone:"",password:"",rememberMe:!1}),n=w({phone:"",password:""}),b=w(!1),y=w(""),c=T(()=>p.loading.value),g=T(()=>o.value.phone.trim()!==""&&o.value.password.trim()!==""&&!c.value),V=()=>{const d=p.validateField("phone",o.value.phone);return n.value.phone=d.message,d.isValid},L=()=>{const d=p.validateField("password",o.value.password);return n.value.password=d.message,d.isValid},M=()=>{const d=V(),u=L();return d&&u},v=()=>{n.value.phone="",E()},K=()=>{n.value.password="",E()},E=()=>{y.value="",p.clearError()},F=()=>{n.value={phone:"",password:""},E()},X=()=>{b.value=!b.value},$=d=>{const u="form-input",x=n.value[d]?"form-input--error":"",z=c.value?"form-input--disabled":"";return[u,x,z].filter(Boolean).join(" ")},O=()=>{const d="submit-button",u=g.value?"":"submit-button--disabled",x=c.value?"submit-button--loading":"";return[d,u,x].filter(Boolean).join(" ")},D=async()=>{try{if(F(),!M())return;const d=await p.passwordLogin({phone:o.value.phone,password:o.value.password});d.success?(k.success("登录成功！欢迎回来"),s("login-success",{userInfo:d.data.userInfo,accessToken:d.data.accessToken,rememberMe:o.value.rememberMe})):(k.error(d.error||"登录失败，请重试"),s("login-error",d.error))}catch(d){console.error("登录过程中发生错误:",d),k.error("登录过程中发生错误，请重试"),s("login-error",d.message)}},G=()=>{s("forgot-password")},B=()=>{o.value={phone:"",password:"",rememberMe:!1},F(),b.value=!1};return J(()=>p.error.value,d=>{d&&(y.value=d)}),se(()=>{f.autoFocus&&he(()=>{const d=document.querySelector('.password-login-form input[type="tel"]');d&&d.focus()})}),l({resetForm:B,validateForm:M,clearAllErrors:F}),(d,u)=>(a(),i("div",Ve,[e("form",{onSubmit:ee(D,["prevent"]),class:"space-y-2"},[e("div",Le,[u[3]||(u[3]=e("label",{class:"form-label"},"手机号",-1)),R(e("input",{"onUpdate:modelValue":u[0]||(u[0]=x=>o.value.phone=x),type:"tel",class:C($("phone")),placeholder:"请输入手机号",onBlur:V,onInput:v,disabled:c.value,maxlength:"11",autocomplete:"tel"},null,42,Ie),[[Z,o.value.phone]]),e("div",Te,_(n.value.phone),1)]),e("div",$e,[u[6]||(u[6]=e("label",{class:"form-label"},"密码",-1)),e("div",Be,[R(e("input",{"onUpdate:modelValue":u[1]||(u[1]=x=>o.value.password=x),type:b.value?"text":"password",class:C($("password")),placeholder:"请输入密码",onBlur:L,onInput:K,disabled:c.value,maxlength:"20",autocomplete:"current-password"},null,42,Se),[[le,o.value.password]]),e("button",{type:"button",onClick:X,class:"password-toggle-btn",disabled:c.value,"aria-label":b.value?"隐藏密码":"显示密码"},[b.value?(a(),i("svg",Re,u[4]||(u[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.5 8.5M14.12 14.12L15.5 15.5"},null,-1)]))):(a(),i("svg",Ee,u[5]||(u[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))],8,je)]),e("div",Fe,_(n.value.password),1)]),e("div",ze,[e("label",Ae,[R(e("input",{"onUpdate:modelValue":u[2]||(u[2]=x=>o.value.rememberMe=x),type:"checkbox",class:"remember-me-checkbox",disabled:c.value},null,8,qe),[[we,o.value.rememberMe]]),u[7]||(u[7]=e("span",{class:"remember-me-text"},"记住登录状态",-1))]),e("button",{type:"button",class:"forgot-password-link",onClick:G,disabled:c.value}," 忘记密码？ ",8,Ne)]),e("button",{type:"submit",disabled:!g.value,class:C(O())},[c.value?(a(),i("div",Oe,u[8]||(u[8]=[e("div",{class:"spinner"},null,-1),e("span",null,"登录中...",-1)]))):(a(),i("span",De,"登录"))],10,Ue),y.value?(a(),i("div",Ge,[u[9]||(u[9]=e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("span",null,_(y.value),1)])):U("",!0)],32)]))}},Qe=te(He,[["__scopeId","data-v-1fee5600"]]),Je={class:"flex min-h-[500px] md:min-h-[600px]"},We={class:"w-full md:w-3/5 flex flex-col"},Ke={class:"flex justify-end items-center px-12 py-8 pb-2 pt-12"},Xe={class:"flex-1 px-12 py-4 mt-5"},Ye={key:"login"},Ze={key:"wechat",class:"space-y-6"},es={class:"text-center"},ss={class:"relative w-48 h-48 mx-auto mb-6 bg-white border-2 border-secondary-200 rounded-2xl flex items-center justify-center"},ts={key:0,class:"text-center"},os={key:1,class:"w-40 h-40 bg-black rounded-lg relative overflow-hidden"},rs={class:"absolute inset-2 bg-white rounded"},ls={class:"w-full h-full relative"},as={class:"absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px p-8"},ns={key:"account",class:"pt-3"},is={key:"register",class:"space-y-6"},ds={class:"mt-1 text-sm text-red-600 h-5"},us={class:"flex space-x-3"},cs=["disabled"],ms={class:"mt-1 text-sm text-red-600 h-5"},ps={class:"relative"},vs=["type"],fs={key:0,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},gs={key:1,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},hs={class:"mt-1 text-sm text-red-600 h-5"},ws=["disabled"],bs={key:0,class:"flex items-center justify-center"},ys={key:1},xs={key:0,class:"px-12 py-8 pt-0"},ks={class:"text-center"},Cs={class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24"},Ps={key:0,d:"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.146 4.203 2.943 5.558-.687 1.717-1.562 3.525-1.562 3.525s3.528-.612 5.125-1.36c.687.145 1.395.217 2.185.217 4.8 0 8.691-3.287 8.691-7.34 0-4.052-3.891-7.341-8.691-7.341z"},_s={key:1,fill:"none",stroke:"currentColor","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},Ms={__name:"LoginModal",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","login-success","switch-to-register"],setup(m,{emit:l}){const r=m,f=l,s=ie(),p=w(r.modelValue),o=w("account"),n=w("login"),b=w(!1),y=w(!1),c=w({phone:"",password:""}),g=w({phone:"",password:"",verifyCode:""}),V=w("获取验证码"),L=w(!1),M=w(0),v=w({loginPhone:"",loginPassword:"",registerPhone:"",registerVerifyCode:"",registerPassword:""});J(()=>r.modelValue,h=>{p.value=h,h&&(n.value="login",o.value="account",G())}),J(p,h=>{f("update:modelValue",h)});const K=h=>/^1[3-9]\d{9}$/.test(h),E=()=>{v.value.registerPhone=""},F=()=>{v.value.registerVerifyCode=""},X=()=>{v.value.registerPassword=""},$=()=>g.value.phone?K(g.value.phone)?(v.value.registerPhone="",!0):(v.value.registerPhone="请输入正确的手机号",!1):(v.value.registerPhone="请输入手机号",!1),O=()=>g.value.verifyCode?/^\d{6}$/.test(g.value.verifyCode)?(v.value.registerVerifyCode="",!0):(v.value.registerVerifyCode="请输入6位数字验证码",!1):(v.value.registerVerifyCode="请输入验证码",!1),D=()=>g.value.password?g.value.password.length<6?(v.value.registerPassword="密码长度不能少于6位",!1):(v.value.registerPassword="",!0):(v.value.registerPassword="请输入密码",!1),G=()=>{c.value={phone:"",password:""},g.value={phone:"",password:"",verifyCode:""},v.value={loginPhone:"",loginPassword:"",registerPhone:"",registerVerifyCode:"",registerPassword:""},fe()},B=()=>{p.value=!1,G()},d=()=>{o.value=o.value==="wechat"?"account":"wechat",o.value==="wechat"&&!b.value&&z()},u=()=>{n.value="register",o.value="account"},x=()=>{n.value="login"},z=()=>{b.value=!1,setTimeout(()=>{b.value=!0},1500)},de=()=>{z()},ue=h=>{try{f("login-success",h),B()}catch(t){console.error("处理登录成功回调时发生错误:",t)}},ce=h=>{try{console.error("密码登录失败:",h)}catch(t){console.error("处理登录错误回调时发生错误:",t)}},me=()=>{try{k.info("忘记密码功能即将上线"),console.log("用户点击了忘记密码")}catch(h){console.error("处理忘记密码时发生错误:",h)}},pe=async()=>{if(n.value==="register"&&!$())return;const h=n.value==="register"?g.value.phone:c.value.phone;try{const t=await s.sendSmsCode(h);t.success?(k.success("验证码发送成功"),ve()):k.error(t.error||"验证码发送失败")}catch(t){console.error("发送验证码失败:",t),k.error("验证码发送失败，请重试")}},ve=()=>{L.value=!0,M.value=60;const h=Ce(()=>{M.value--,V.value=`${M.value}秒后重发`,M.value<=0&&(clearInterval(h),L.value=!1,V.value="获取验证码")},1e3)},fe=()=>{L.value=!1,V.value="获取验证码",M.value=0},ge=async()=>{const h=$(),t=O(),P=D();if(!(!h||!t||!P))try{const A=await s.register({phone:g.value.phone,password:g.value.password,verifyCode:g.value.verifyCode});A.success?(k.success("注册成功"),f("login-success",A.data),B()):k.error(A.error||"注册失败")}catch(A){console.error("注册失败:",A),k.error("注册失败，请重试")}};return se(()=>{p.value&&o.value==="wechat"&&z()}),(h,t)=>(a(),ae(ne,{to:"body"},[S(N,{name:"modal-fade"},{default:q(()=>[p.value?(a(),i("div",{key:0,class:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm",onClick:B},[S(N,{name:"modal-scale"},{default:q(()=>[p.value?(a(),i("div",{key:0,class:"relative w-full max-w-4xl mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden",onClick:t[4]||(t[4]=ee(()=>{},["stop"]))},[e("button",{onClick:B,class:"absolute top-4 right-4 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-white/80 hover:bg-white transition-all duration-200 text-secondary-500 hover:text-secondary-700"},t[5]||(t[5]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),e("div",Je,[t[26]||(t[26]=e("div",{class:"hidden md:flex md:w-2/5 bg-gradient-to-br from-primary-500 via-primary-600 to-orange-600 relative overflow-hidden"},[e("div",{class:"absolute inset-0 opacity-10"},[e("div",{class:"absolute top-10 left-10 w-32 h-32 border-2 border-white rounded-full"}),e("div",{class:"absolute top-32 right-16 w-20 h-20 bg-white/20 rounded-lg rotate-45"}),e("div",{class:"absolute bottom-20 left-20 w-16 h-16 bg-white/15 rounded-full"}),e("div",{class:"absolute bottom-32 right-32 w-24 h-24 border border-white rounded-lg rotate-12"})]),e("div",{class:"relative z-10 flex flex-col justify-center p-12 text-white"},[e("div",{class:"mb-8"},[e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mr-4 shadow-lg"},[e("svg",{class:"w-7 h-7 text-white",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.19 0 2.34-.21 3.41-.6.3-.11.49-.4.49-.72 0-.43-.35-.78-.78-.78-.17 0-.33.06-.46.14-.82.29-1.69.44-2.58.44-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6c0 .89-.15 1.76-.44 2.58-.08.13-.14.29-.14.46 0 .43.35.78.78.78.32 0 .61-.19.72-.49.39-1.07.6-2.22.6-3.41C22 6.48 17.52 2 12 2z"}),e("path",{d:"M12 8l-2 6h4l-2-6z"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold"},"火花简历"),e("p",{class:"text-white/80 text-sm"},"点燃职场新可能")])]),e("p",{class:"text-lg text-white/90 mb-8"},"专业简历制作平台，助您脱颖而出")]),e("div",{class:"space-y-4"},[e("div",{class:"flex items-center"},[e("div",{class:"w-2 h-2 bg-white rounded-full mr-3"}),e("span",{class:"text-white/90"},"1000+ 专业简历模板")]),e("div",{class:"flex items-center"},[e("div",{class:"w-2 h-2 bg-white rounded-full mr-3"}),e("span",{class:"text-white/90"},"AI 智能内容优化")]),e("div",{class:"flex items-center"},[e("div",{class:"w-2 h-2 bg-white rounded-full mr-3"}),e("span",{class:"text-white/90"},"一键导出多种格式")]),e("div",{class:"flex items-center"},[e("div",{class:"w-2 h-2 bg-white rounded-full mr-3"}),e("span",{class:"text-white/90"},"安全云端同步")])])])],-1)),e("div",We,[e("div",Ke,[n.value==="login"?(a(),i("button",{key:0,onClick:d,class:"text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 flex items-center space-x-1"},[t[6]||(t[6]=e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"})],-1)),e("span",null,_(o.value==="wechat"?"密码登录":"微信登录"),1)])):(a(),i("button",{key:1,onClick:x,class:"text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 flex items-center space-x-1"},t[7]||(t[7]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1),e("span",null,"返回登录",-1)])))]),e("div",Xe,[S(N,{name:"slide-fade",mode:"out-in"},{default:q(()=>[n.value==="login"?(a(),i("div",Ye,[S(N,{name:"slide-fade",mode:"out-in"},{default:q(()=>[o.value==="wechat"?(a(),i("div",Ze,[e("div",es,[t[14]||(t[14]=e("h3",{class:"font-semibold text-lg text-secondary-900 mb-6"},"微信扫一扫",-1)),e("div",ss,[b.value?(a(),i("div",os,[e("div",rs,[e("div",ls,[t[9]||(t[9]=e("div",{class:"absolute top-1 left-1 w-6 h-6 border-2 border-black"},null,-1)),t[10]||(t[10]=e("div",{class:"absolute top-1 right-1 w-6 h-6 border-2 border-black"},null,-1)),t[11]||(t[11]=e("div",{class:"absolute bottom-1 left-1 w-6 h-6 border-2 border-black"},null,-1)),t[12]||(t[12]=e("div",{class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.146 4.203 2.943 5.558-.687 1.717-1.562 3.525-1.562 3.525s3.528-.612 5.125-1.36c.687.145 1.395.217 2.185.217 4.8 0 8.691-3.287 8.691-7.34 0-4.052-3.891-7.341-8.691-7.341z"})])],-1)),e("div",as,[(a(),i(be,null,ye(64,P=>e("div",{key:P,class:C([Math.random()>.5?"bg-black":"bg-white","w-full h-full"])},null,2)),64))])])])])):(a(),i("div",ts,t[8]||(t[8]=[e("div",{class:"w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"},null,-1),e("p",{class:"text-secondary-600 text-sm"},"正在生成二维码...",-1)]))),e("button",{onClick:de,class:"absolute top-2 right-2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-secondary-50 transition-colors duration-200"},t[13]||(t[13]=[e("svg",{class:"w-4 h-4 text-secondary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]))]),t[15]||(t[15]=e("div",{class:"space-y-3"},[e("p",{class:"text-sm text-secondary-600"},"微信扫码，关注公众号授权登录")],-1))])])):(a(),i("div",ns,[S(Qe,{onLoginSuccess:ue,onLoginError:ce,onForgotPassword:me}),t[17]||(t[17]=e("div",{class:"flex items-start space-x-3 mt-6"},[e("input",{type:"checkbox",class:"w-4 h-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500 mt-0.5",checked:"",required:""}),e("label",{class:"text-sm text-secondary-600"},[I(" 我已阅读并同意 "),e("button",{type:"button",class:"text-primary-600 hover:text-primary-700 transition-colors duration-200"},"用户协议"),I(" 和 "),e("button",{type:"button",class:"text-primary-600 hover:text-primary-700 transition-colors duration-200"},"隐私政策")])],-1)),e("div",{class:"mt-6 text-center"},[e("p",{class:"text-sm text-secondary-600"},[t[16]||(t[16]=I(" 还没有账户？ ")),e("button",{onClick:u,class:"font-medium text-primary-600 hover:underline"}," 立即注册 ")])])]))]),_:1})])):(a(),i("div",is,[e("form",{onSubmit:ee(ge,["prevent"])},[e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-secondary-700 mb-2"},"手机号",-1)),R(e("input",{"onUpdate:modelValue":t[0]||(t[0]=P=>g.value.phone=P),type:"tel",class:C(["w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200",v.value.registerPhone?"border-red-300 bg-red-50":"border-secondary-300"]),placeholder:"请输入手机号",onBlur:$,onInput:E,required:""},null,34),[[Z,g.value.phone]]),e("div",ds,_(v.value.registerPhone),1)]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-secondary-700 mb-2"},"验证码",-1)),e("div",us,[R(e("input",{"onUpdate:modelValue":t[1]||(t[1]=P=>g.value.verifyCode=P),type:"text",class:C(["flex-1 px-4 py-3 border rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200",v.value.registerVerifyCode?"border-red-300 bg-red-50":"border-secondary-300"]),placeholder:"请输入验证码",onBlur:O,onInput:F,required:""},null,34),[[Z,g.value.verifyCode]]),e("button",{type:"button",onClick:pe,disabled:L.value||H(s).loading.value,class:"px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 bg-primary-100 text-primary-700 hover:bg-primary-200 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"},_(V.value),9,cs)]),e("div",ms,_(v.value.registerVerifyCode),1)]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-secondary-700 mb-2"},"密码",-1)),e("div",ps,[R(e("input",{"onUpdate:modelValue":t[2]||(t[2]=P=>g.value.password=P),type:y.value?"text":"password",class:C(["w-full px-4 py-3 pr-12 border rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200",v.value.registerPassword?"border-red-300 bg-red-50":"border-secondary-300"]),placeholder:"请输入密码",onBlur:D,onInput:X,required:""},null,42,vs),[[le,g.value.password]]),e("button",{type:"button",onClick:t[3]||(t[3]=P=>y.value=!y.value),class:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"},[y.value?(a(),i("svg",fs,t[20]||(t[20]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)]))):(a(),i("svg",gs,t[21]||(t[21]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 711.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)])))])]),e("div",hs,_(v.value.registerPassword),1)]),t[25]||(t[25]=e("div",{class:"flex items-start space-x-3"},[e("input",{type:"checkbox",class:"w-4 h-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500 mt-0.5",checked:"",required:""}),e("label",{class:"text-sm text-secondary-600"},[I(" 我已阅读并同意 "),e("button",{type:"button",class:"text-primary-600 hover:text-primary-700 transition-colors duration-200"},"用户协议"),I(" 和 "),e("button",{type:"button",class:"text-primary-600 hover:text-primary-700 transition-colors duration-200"},"隐私政策")])],-1)),e("button",{type:"submit",disabled:H(s).loading.value,class:C(["w-full btn-primary mt-9",H(s).loading.value?"opacity-75 cursor-not-allowed":""])},[H(s).loading.value?(a(),i("span",bs,t[23]||(t[23]=[e("div",{class:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"},null,-1),I(" 注册中... ")]))):(a(),i("span",ys,"立即注册"))],10,ws),e("div",{class:"mt-6 text-center"},[e("p",{class:"text-sm text-secondary-600"},[t[24]||(t[24]=I(" 已有账户？ ")),e("button",{onClick:x,class:"font-medium text-primary-600 hover:underline"}," 立即登录 ")])])],32)]))]),_:1})]),n.value==="login"?(a(),i("div",xs,[e("div",ks,[e("button",{onClick:d,class:"text-secondary-600 hover:text-primary-600 transition-colors duration-200 flex items-center justify-center space-x-2 mx-auto"},[(a(),i("svg",Cs,[o.value==="wechat"?(a(),i("path",Ps)):(a(),i("path",_s))])),e("span",null,_(o.value==="wechat"?"使用密码登录":"使用微信登录"),1)])])])):U("",!0)])])])):U("",!0)]),_:1})])):U("",!0)]),_:1})]))}},qs=te(Ms,[["__scopeId","data-v-514546ee"]]),Vs={key:0,class:"fixed top-20 left-1/2 transform -translate-x-1/2 z-50 min-w-[320px] max-w-[600px] px-4"},Ls={class:"flex-shrink-0 mr-3"},Is={key:0,class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ts={key:1,class:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},$s={key:2,class:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Bs={key:3,class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ss={class:"flex-1"},js={__name:"GlobalMessage",props:{type:{type:String,default:"info",validator:m=>["success","error","warning","info"].includes(m)},message:{type:String,default:""},duration:{type:Number,default:4e3},visible:{type:Boolean,default:!1}},emits:["close"],setup(m,{emit:l}){const r=m,f=l;let s=null;const p=T(()=>{switch(r.type){case"success":return"bg-green-50/90 border-green-200 text-green-800";case"error":return"bg-red-50/90 border-red-200 text-red-800";case"warning":return"bg-yellow-50/90 border-yellow-200 text-yellow-800";default:return"bg-blue-50/90 border-blue-200 text-blue-800"}}),o=T(()=>{switch(r.type){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";default:return"text-blue-800"}}),n=()=>{s&&(clearTimeout(s),s=null),f("close")},b=()=>{s&&clearTimeout(s),r.duration>0&&(s=setTimeout(()=>{n()},r.duration))};return J(()=>r.visible,y=>{y&&r.message?b():s&&(clearTimeout(s),s=null)}),se(()=>{r.visible&&r.message&&b()}),(y,c)=>(a(),ae(ne,{to:"body"},[S(N,{name:"message-fade"},{default:q(()=>[m.visible&&m.message?(a(),i("div",Vs,[e("div",{class:C(["flex items-center px-6 py-4 rounded-lg shadow-lg backdrop-blur-md","border transition-all duration-300",p.value])},[e("div",Ls,[m.type==="success"?(a(),i("svg",Is,c[0]||(c[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))):m.type==="error"?(a(),i("svg",Ts,c[1]||(c[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1)]))):m.type==="warning"?(a(),i("svg",$s,c[2]||(c[2]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.963-.833-2.732 0L3.732 16.5C2.962 18.333 3.924 20 5.464 20z"},null,-1)]))):(a(),i("svg",Bs,c[3]||(c[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),e("div",Ss,[e("p",{class:C(["text-sm font-medium",o.value])},_(m.message),3)]),e("button",{onClick:n,class:"flex-shrink-0 ml-4 p-1 rounded-full hover:bg-black/10 transition-colors duration-200"},[(a(),i("svg",{class:C(["w-4 h-4",o.value]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},c[4]||(c[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1)]),2))])],2)])):U("",!0)]),_:1})]))}},Ns=te(js,[["__scopeId","data-v-d003cf35"]]);export{Ns as G,qs as L};
