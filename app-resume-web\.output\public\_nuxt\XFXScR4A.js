import{_ as v}from"./BXnw39BI.js";import{S as b,r as c,c as g,a as t,t as s,U as k,h as r,b as o,w as a,o as S,d as u}from"./CURHyiUL.js";import{u as x}from"./PsJl1LMp.js";import{_ as C}from"./DlAUqK2U.js";const N={class:"test-page"},B={class:"test-info"},T={class:"test-links"},V={__name:"sidebar-test",setup(w){const d=b(),{sidebarCollapsed:m,activeMenuItem:p}=x(),l=c(!1),_=()=>{l.value=!l.value},f=()=>{d.push("/admin/template/converter")};return(i,e)=>{const n=v;return S(),g("div",N,[e[6]||(e[6]=t("h1",null,"侧边栏测试页面",-1)),t("div",B,[e[0]||(e[0]=t("h2",null,"当前状态",-1)),t("ul",null,[t("li",null,"当前路由: "+s((i._.provides[k]||i.$route).path),1),t("li",null,"侧边栏折叠: "+s(r(m)),1),t("li",null,"模板子菜单展开: "+s(l.value),1),t("li",null,"活跃菜单项: "+s(r(p)),1)])]),t("div",{class:"test-actions"},[e[1]||(e[1]=t("h2",null,"测试操作",-1)),t("button",{onClick:_,class:"btn"},"切换模板子菜单"),t("button",{onClick:f,class:"btn"},"导航到转换器")]),t("div",T,[e[5]||(e[5]=t("h2",null,"测试链接",-1)),t("ul",null,[t("li",null,[o(n,{to:"/admin"},{default:a(()=>e[2]||(e[2]=[u("管理后台首页")])),_:1,__:[2]})]),t("li",null,[o(n,{to:"/admin/template/list"},{default:a(()=>e[3]||(e[3]=[u("模板列表")])),_:1,__:[3]})]),t("li",null,[o(n,{to:"/admin/template/converter"},{default:a(()=>e[4]||(e[4]=[u("模板转换")])),_:1,__:[4]})])])])])}}},D=C(V,[["__scopeId","data-v-3d2a62c7"]]);export{D as default};
