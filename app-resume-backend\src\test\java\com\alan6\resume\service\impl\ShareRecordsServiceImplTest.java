package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.entity.ShareRecords;
import com.alan6.resume.entity.Resumes;
import com.alan6.resume.service.IResumeService;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.IShareRecordsService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 分享记录Service新方法测试类
 * 专门测试新增的三个方法：预览、验证、获取访问日志
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("test")
class ShareRecordsServiceImplTest {

    @Mock
    private IShareRecordsService shareRecordsService;

    @Mock
    private IResumeService resumesService;

    @Mock
    private IResumeTemplatesService resumeTemplatesService;

    private ShareRecords mockShareRecord;
    private Resumes mockResume;

    /**
     * 测试数据初始化
     */
    @BeforeEach
    void setUp() {
        // 创建模拟分享记录
        mockShareRecord = new ShareRecords();
        mockShareRecord.setId(1L);
        mockShareRecord.setUserId(100L);
        mockShareRecord.setResourceType("resume");
        mockShareRecord.setResourceId(200L);
        mockShareRecord.setShareCode("TEST12345678");
        mockShareRecord.setShareType((byte) 2); // 密码保护
        mockShareRecord.setPassword("123456");
        mockShareRecord.setExpireTime(LocalDateTime.now().plusDays(7));
        mockShareRecord.setViewLimit(100);
        mockShareRecord.setViewCount(5);
        mockShareRecord.setAllowDownload((byte) 1);
        mockShareRecord.setIsActive((byte) 1);
        mockShareRecord.setDescription("测试分享");
        mockShareRecord.setCreateTime(LocalDateTime.now());

        // 创建模拟简历
        mockResume = new Resumes();
        mockResume.setId(200L);
        mockResume.setUserId(100L);
        mockResume.setName("测试简历");
    }

    /**
     * 测试预览分享内容 - 成功场景
     */
    @Test
    void testPreviewShare_Success() {
        // 准备测试数据
        String shareCode = "TEST12345678";
        String password = "123456";
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "isValid", true,
            "requirePassword", true,
            "passwordCorrect", true,
            "resourceType", "resume",
            "resourceName", "测试简历"
        );

        // Mock方法调用
        when(shareRecordsService.previewShare(shareCode, password)).thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.previewShare(shareCode, password);

        // 验证结果
        assertNotNull(result);
        assertEquals(true, result.get("isValid"));
        assertEquals(true, result.get("requirePassword"));
        assertEquals(true, result.get("passwordCorrect"));
        assertEquals("resume", result.get("resourceType"));
        assertEquals("测试简历", result.get("resourceName"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).previewShare(shareCode, password);
    }

    /**
     * 测试预览分享内容 - 分享不存在
     */
    @Test
    void testPreviewShare_NotFound() {
        // 准备测试数据
        String shareCode = "INVALID12345";
        String password = null;
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "isValid", false,
            "message", "分享链接不存在或已失效"
        );

        // Mock方法调用
        when(shareRecordsService.previewShare(shareCode, password)).thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.previewShare(shareCode, password);

        // 验证结果
        assertNotNull(result);
        assertEquals(false, result.get("isValid"));
        assertEquals("分享链接不存在或已失效", result.get("message"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).previewShare(shareCode, password);
    }

    /**
     * 测试验证分享访问权限 - 成功场景
     */
    @Test
    void testVerifyShareAccess_Success() {
        // 准备测试数据
        String shareCode = "TEST12345678";
        String password = "123456";
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "verified", true,
            "message", "验证成功",
            "shareId", 1L,
            "resourceType", "resume",
            "resourceId", 200L,
            "allowDownload", true
        );

        // Mock方法调用
        when(shareRecordsService.verifyShareAccess(shareCode, password)).thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.verifyShareAccess(shareCode, password);

        // 验证结果
        assertNotNull(result);
        assertEquals(true, result.get("verified"));
        assertEquals("验证成功", result.get("message"));
        assertEquals(1L, result.get("shareId"));
        assertEquals("resume", result.get("resourceType"));
        assertEquals(200L, result.get("resourceId"));
        assertEquals(true, result.get("allowDownload"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).verifyShareAccess(shareCode, password);
    }

    /**
     * 测试验证分享访问权限 - 密码错误
     */
    @Test
    void testVerifyShareAccess_WrongPassword() {
        // 准备测试数据
        String shareCode = "TEST12345678";
        String password = "wrong";
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "verified", false,
            "message", "访问密码错误"
        );

        // Mock方法调用
        when(shareRecordsService.verifyShareAccess(shareCode, password)).thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.verifyShareAccess(shareCode, password);

        // 验证结果
        assertNotNull(result);
        assertEquals(false, result.get("verified"));
        assertEquals("访问密码错误", result.get("message"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).verifyShareAccess(shareCode, password);
    }

    /**
     * 测试获取分享访问日志 - 成功场景
     */
    @Test
    void testGetShareAccessLogs_Success() {
        // 准备测试数据
        Long shareId = 1L;
        Integer current = 1;
        Integer size = 10;
        LocalDateTime startDate = LocalDateTime.now().minusDays(7);
        LocalDateTime endDate = LocalDateTime.now();
        Long userId = 100L;
        
        // 模拟分页结果
        Page<Map<String, Object>> expectedPage = new Page<>(current, size);
        expectedPage.setTotal(5);
        expectedPage.setRecords(java.util.List.of(
            Map.of("id", 1L, "shareId", shareId, "accessTime", LocalDateTime.now(), "clientIp", "***********"),
            Map.of("id", 2L, "shareId", shareId, "accessTime", LocalDateTime.now().minusHours(1), "clientIp", "***********")
        ));

        // Mock方法调用
        when(shareRecordsService.getShareAccessLogs(shareId, current, size, startDate, endDate, userId))
            .thenReturn(expectedPage);

        // 执行测试
        Page<Map<String, Object>> result = shareRecordsService.getShareAccessLogs(shareId, current, size, startDate, endDate, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.getTotal());
        assertEquals(2, result.getRecords().size());
        assertEquals(1, result.getCurrent());
        assertEquals(10, result.getSize());

        // 验证访问日志内容
        Map<String, Object> firstLog = result.getRecords().get(0);
        assertEquals(1L, firstLog.get("id"));
        assertEquals(shareId, firstLog.get("shareId"));
        assertNotNull(firstLog.get("accessTime"));
        assertEquals("***********", firstLog.get("clientIp"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).getShareAccessLogs(shareId, current, size, startDate, endDate, userId);
    }

    /**
     * 测试获取分享访问日志 - 无权限场景
     */
    @Test
    void testGetShareAccessLogs_NoPermission() {
        // 准备测试数据
        Long shareId = 1L;
        Integer current = 1;
        Integer size = 10;
        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        Long userId = 999L; // 不同的用户ID

        // Mock方法调用抛出异常
        when(shareRecordsService.getShareAccessLogs(shareId, current, size, startDate, endDate, userId))
            .thenThrow(new BusinessException("无权限查看该分享的访问日志"));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            shareRecordsService.getShareAccessLogs(shareId, current, size, startDate, endDate, userId);
        });

        assertEquals("无权限查看该分享的访问日志", exception.getMessage());

        // 验证方法调用
        verify(shareRecordsService, times(1)).getShareAccessLogs(shareId, current, size, startDate, endDate, userId);
    }

    /**
     * 测试获取分享访问日志 - 分享不存在
     */
    @Test
    void testGetShareAccessLogs_ShareNotFound() {
        // 准备测试数据
        Long shareId = 999L;
        Integer current = 1;
        Integer size = 10;
        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        Long userId = 100L;

        // Mock方法调用抛出异常
        when(shareRecordsService.getShareAccessLogs(shareId, current, size, startDate, endDate, userId))
            .thenThrow(new BusinessException("分享记录不存在"));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            shareRecordsService.getShareAccessLogs(shareId, current, size, startDate, endDate, userId);
        });

        assertEquals("分享记录不存在", exception.getMessage());

        // 验证方法调用
        verify(shareRecordsService, times(1)).getShareAccessLogs(shareId, current, size, startDate, endDate, userId);
    }

    /**
     * 测试边界条件 - 空密码预览
     */
    @Test
    void testPreviewShare_EmptyPassword() {
        // 准备测试数据
        String shareCode = "TEST12345678";
        String password = "";
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "isValid", true,
            "requirePassword", true,
            "passwordCorrect", false,
            "resourceType", "resume"
        );

        // Mock方法调用
        when(shareRecordsService.previewShare(shareCode, password)).thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.previewShare(shareCode, password);

        // 验证结果
        assertNotNull(result);
        assertEquals(true, result.get("isValid"));
        assertEquals(true, result.get("requirePassword"));
        assertEquals(false, result.get("passwordCorrect"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).previewShare(shareCode, password);
    }

    /**
     * 测试边界条件 - 空分享码
     */
    @Test
    void testVerifyShareAccess_EmptyShareCode() {
        // 准备测试数据
        String shareCode = "";
        String password = "123456";
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "verified", false,
            "message", "分享链接不存在或已失效"
        );

        // Mock方法调用
        when(shareRecordsService.verifyShareAccess(shareCode, password)).thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.verifyShareAccess(shareCode, password);

        // 验证结果
        assertNotNull(result);
        assertEquals(false, result.get("verified"));
        assertEquals("分享链接不存在或已失效", result.get("message"));

                 // 验证方法调用
         verify(shareRecordsService, times(1)).verifyShareAccess(shareCode, password);
     }

    /**
     * 测试批量操作分享状态 - 成功场景
     */
    @Test
    void testBatchToggleShareStatus_Success() {
        // 准备测试数据
        List<Long> shareIds = List.of(1L, 2L, 3L);
        Boolean isActive = true;
        Long userId = 100L;
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "successCount", 3,
            "failedCount", 0,
            "failedItems", List.of()
        );

        // Mock方法调用
        when(shareRecordsService.batchToggleShareStatus(shareIds, isActive, userId))
            .thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.batchToggleShareStatus(shareIds, isActive, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.get("successCount"));
        assertEquals(0, result.get("failedCount"));
        assertEquals(List.of(), result.get("failedItems"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).batchToggleShareStatus(shareIds, isActive, userId);
    }

    /**
     * 测试批量操作分享状态 - 部分失败场景
     */
    @Test
    void testBatchToggleShareStatus_PartialFailure() {
        // 准备测试数据
        List<Long> shareIds = List.of(1L, 999L, 3L);
        Boolean isActive = false;
        Long userId = 100L;
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "successCount", 2,
            "failedCount", 1,
            "failedItems", List.of(
                Map.of("shareId", 999L, "reason", "分享记录不存在")
            )
        );

        // Mock方法调用
        when(shareRecordsService.batchToggleShareStatus(shareIds, isActive, userId))
            .thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.batchToggleShareStatus(shareIds, isActive, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.get("successCount"));
        assertEquals(1, result.get("failedCount"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> failedItems = (List<Map<String, Object>>) result.get("failedItems");
        assertEquals(1, failedItems.size());
        assertEquals(999L, failedItems.get(0).get("shareId"));
        assertEquals("分享记录不存在", failedItems.get(0).get("reason"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).batchToggleShareStatus(shareIds, isActive, userId);
    }

    /**
     * 测试批量设置过期时间 - 成功场景
     */
    @Test
    void testBatchSetExpireTime_Success() {
        // 准备测试数据
        List<Long> shareIds = List.of(1L, 2L, 3L);
        LocalDateTime expireTime = LocalDateTime.now().plusDays(30);
        Long userId = 100L;
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "successCount", 3,
            "failedCount", 0,
            "failedItems", List.of()
        );

        // Mock方法调用
        when(shareRecordsService.batchSetExpireTime(shareIds, expireTime, userId))
            .thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.batchSetExpireTime(shareIds, expireTime, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.get("successCount"));
        assertEquals(0, result.get("failedCount"));
        assertEquals(List.of(), result.get("failedItems"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).batchSetExpireTime(shareIds, expireTime, userId);
    }

    /**
     * 测试批量设置过期时间 - 过期时间无效
     */
    @Test
    void testBatchSetExpireTime_InvalidExpireTime() {
        // 准备测试数据
        List<Long> shareIds = List.of(1L, 2L);
        LocalDateTime expireTime = LocalDateTime.now().minusDays(1); // 过去的时间
        Long userId = 100L;
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "successCount", 0,
            "failedCount", 2,
            "failedItems", List.of(
                Map.of("shareId", 1L, "reason", "过期时间不能早于当前时间"),
                Map.of("shareId", 2L, "reason", "过期时间不能早于当前时间")
            )
        );

        // Mock方法调用
        when(shareRecordsService.batchSetExpireTime(shareIds, expireTime, userId))
            .thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.batchSetExpireTime(shareIds, expireTime, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.get("successCount"));
        assertEquals(2, result.get("failedCount"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> failedItems = (List<Map<String, Object>>) result.get("failedItems");
        assertEquals(2, failedItems.size());

        // 验证方法调用
        verify(shareRecordsService, times(1)).batchSetExpireTime(shareIds, expireTime, userId);
    }

    /**
     * 测试获取热门分享 - 成功场景
     */
    @Test
    void testGetTrendingShares_Success() {
        // 准备测试数据
        String resourceType = "resume";
        String period = "30d";
        Integer limit = 10;
        Long userId = 100L;
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "period", "30d",
            "updateTime", LocalDateTime.now(),
            "trending", List.of(
                Map.of(
                    "shareId", 1L,
                    "resourceType", "resume",
                    "resourceName", "前端开发工程师简历",
                    "shareCode", "TEST12345678",
                    "viewCount", 1520,
                    "downloadCount", 234,
                    "shareTime", LocalDateTime.now().minusDays(10),
                    "growth", "+23.5%"
                ),
                Map.of(
                    "shareId", 2L,
                    "resourceType", "resume",
                    "resourceName", "Java开发工程师简历",
                    "shareCode", "TEST87654321",
                    "viewCount", 892,
                    "downloadCount", 156,
                    "shareTime", LocalDateTime.now().minusDays(5),
                    "growth", "+18.2%"
                )
            )
        );

        // Mock方法调用
        when(shareRecordsService.getTrendingShares(resourceType, period, limit, userId))
            .thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.getTrendingShares(resourceType, period, limit, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals("30d", result.get("period"));
        assertNotNull(result.get("updateTime"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> trending = (List<Map<String, Object>>) result.get("trending");
        assertEquals(2, trending.size());
        
        // 验证第一个热门分享
        Map<String, Object> firstTrending = trending.get(0);
        assertEquals(1L, firstTrending.get("shareId"));
        assertEquals("resume", firstTrending.get("resourceType"));
        assertEquals("前端开发工程师简历", firstTrending.get("resourceName"));
        assertEquals(1520, firstTrending.get("viewCount"));
        assertEquals("+23.5%", firstTrending.get("growth"));

        // 验证方法调用
        verify(shareRecordsService, times(1)).getTrendingShares(resourceType, period, limit, userId);
    }

    /**
     * 测试获取热门分享 - 空结果
     */
    @Test
    void testGetTrendingShares_EmptyResult() {
        // 准备测试数据
        String resourceType = "template";
        String period = "7d";
        Integer limit = 5;
        Long userId = 100L;
        
        // 模拟返回结果
        Map<String, Object> expectedResult = Map.of(
            "period", "7d",
            "updateTime", LocalDateTime.now(),
            "trending", List.of()
        );

        // Mock方法调用
        when(shareRecordsService.getTrendingShares(resourceType, period, limit, userId))
            .thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.getTrendingShares(resourceType, period, limit, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals("7d", result.get("period"));
        assertNotNull(result.get("updateTime"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> trending = (List<Map<String, Object>>) result.get("trending");
        assertEquals(0, trending.size());

        // 验证方法调用
        verify(shareRecordsService, times(1)).getTrendingShares(resourceType, period, limit, userId);
    }

    /**
     * 测试获取热门分享 - 默认参数
     */
    @Test
    void testGetTrendingShares_DefaultParameters() {
        // 准备测试数据
        String resourceType = null;
        String period = null;
        Integer limit = null;
        Long userId = 100L;
        
        // 模拟返回结果（应该使用默认值）
        Map<String, Object> expectedResult = Map.of(
            "period", "30d",
            "updateTime", LocalDateTime.now(),
            "trending", List.of(
                Map.of(
                    "shareId", 1L,
                    "resourceType", "resume",
                    "resourceName", "测试简历",
                    "viewCount", 100,
                    "growth", "+15.0%"
                )
            )
        );

        // Mock方法调用
        when(shareRecordsService.getTrendingShares(resourceType, period, limit, userId))
            .thenReturn(expectedResult);

        // 执行测试
        Map<String, Object> result = shareRecordsService.getTrendingShares(resourceType, period, limit, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals("30d", result.get("period")); // 应该使用默认值
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> trending = (List<Map<String, Object>>) result.get("trending");
        assertEquals(1, trending.size());

        // 验证方法调用
        verify(shareRecordsService, times(1)).getTrendingShares(resourceType, period, limit, userId);
    }
} 