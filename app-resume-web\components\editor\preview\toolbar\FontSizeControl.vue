<template>
  <div class="font-size-control">
    <div class="size-control-group">
      <button 
        class="size-btn decrease"
        @click="decreaseSize"
        :disabled="currentSize <= minSize"
        title="减小字号"
      >
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      </button>
      
      <input 
        v-model="sizeInput"
        type="number"
        :min="minSize"
        :max="maxSize"
        class="size-input"
        @blur="handleSizeChange"
        @keydown.enter="handleSizeChange"
        title="字体大小"
      />
      
      <button 
        class="size-btn increase"
        @click="increaseSize"
        :disabled="currentSize >= maxSize"
        title="增大字号"
      >
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
/**
 * 字号控制组件
 * @description 提供字号调整功能，支持按钮增减和直接输入
 * <AUTHOR>
 * @since 2.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, watch } from 'vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 当前字号值（如 '14px'）
   */
  modelValue: {
    type: String,
    default: '14px'
  },
  
  /**
   * 最小字号
   */
  minSize: {
    type: Number,
    default: 8
  },
  
  /**
   * 最大字号
   */
  maxSize: {
    type: Number,
    default: 72
  },
  
  /**
   * 调整步长
   */
  step: {
    type: Number,
    default: 1
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['update:modelValue', 'change'])

// ================================
// 响应式数据
// ================================

/**
 * 字号输入框值
 */
const sizeInput = ref('')

// ================================
// 计算属性
// ================================

/**
 * 当前字号数值
 */
const currentSize = computed(() => {
  const match = props.modelValue.match(/\d+/)
  return match ? parseInt(match[0]) : 14
})

// ================================
// 监听器
// ================================
watch(() => props.modelValue, (newValue) => {
  sizeInput.value = currentSize.value.toString()
}, { immediate: true })

// ================================
// 事件处理方法
// ================================

/**
 * 减小字号
 */
const decreaseSize = () => {
  const newSize = Math.max(currentSize.value - props.step, props.minSize)
  updateSize(newSize)
}

/**
 * 增大字号
 */
const increaseSize = () => {
  const newSize = Math.min(currentSize.value + props.step, props.maxSize)
  updateSize(newSize)
}

/**
 * 处理字号输入变化
 */
const handleSizeChange = () => {
  const value = parseInt(sizeInput.value)
  
  // 验证输入值
  if (isNaN(value)) {
    sizeInput.value = currentSize.value.toString()
    return
  }
  
  // 限制范围
  const clampedValue = Math.max(props.minSize, Math.min(value, props.maxSize))
  
  if (clampedValue !== value) {
    sizeInput.value = clampedValue.toString()
  }
  
  updateSize(clampedValue)
}

/**
 * 更新字号
 * @param {number} size - 新字号
 */
const updateSize = (size) => {
  const newValue = `${size}px`
  emit('update:modelValue', newValue)
  emit('change', newValue)
}
</script>

<style scoped>
/* ================================
 * 字号控制容器
 * ================================ */
.font-size-control {
  @apply flex items-center;
}

/* ================================
 * 字号控制组
 * ================================ */
.size-control-group {
  @apply flex items-center;
  @apply bg-white border border-gray-200 rounded-lg overflow-hidden;
  @apply transition-all duration-200;
}

.size-control-group:hover {
  @apply border-gray-300 shadow-sm;
}

.size-control-group:focus-within {
  @apply border-primary-500 ring-2 ring-primary-500 ring-opacity-20;
}

/* ================================
 * 字号按钮
 * ================================ */
.size-btn {
  @apply flex items-center justify-center;
  @apply w-7 h-7 p-0;
  @apply text-gray-600 hover:text-gray-900;
  @apply hover:bg-gray-50;
  @apply transition-colors duration-200;
  @apply disabled:opacity-40 disabled:cursor-not-allowed;
  @apply focus:outline-none focus:ring-0;
}

.size-btn.decrease {
  @apply border-r border-gray-200;
}

.size-btn.increase {
  @apply border-l border-gray-200;
}

.size-btn:disabled:hover {
  @apply text-gray-600 bg-transparent;
}

/* ================================
 * 字号输入框
 * ================================ */
.size-input {
  @apply w-12 h-7 px-1;
  @apply text-sm text-center;
  @apply text-gray-900;
  @apply border-0 bg-transparent;
  @apply focus:outline-none focus:ring-0;
  @apply appearance-none;
}

.size-input::-webkit-outer-spin-button,
.size-input::-webkit-inner-spin-button {
  @apply appearance-none m-0;
}

.size-input[type=number] {
  -moz-appearance: textfield;
}

/* ================================
 * 响应式设计
 * ================================ */
@media (max-width: 768px) {
  .size-control-group {
    @apply scale-95;
  }
}
</style> 