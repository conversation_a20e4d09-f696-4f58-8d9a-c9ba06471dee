import{B as n}from"./CG_vNifS.js";import{_ as l}from"./DlAUqK2U.js";import{r,c as i,a as s,b as c,t as d,o as p}from"./CURHyiUL.js";import"./D7AOVZt6.js";const m={class:"test-page"},_={class:"test-container"},f={class:"form-section"},u={class:"debug-section"},v={__name:"basic-info-test",setup(g){const e=r({name:"测试用户",phone:"13800138000",email:"<EMAIL>"}),a=o=>{console.log("收到更新:",o),e.value={...o}};return(o,t)=>(p(),i("div",m,[t[1]||(t[1]=s("h1",null,"基本信息表单测试",-1)),s("div",_,[s("div",f,[c(n,{data:e.value,onUpdate:a},null,8,["data"])]),s("div",u,[t[0]||(t[0]=s("h3",null,"调试信息",-1)),s("pre",null,d(JSON.stringify(e.value,null,2)),1)])])]))}},I=l(v,[["__scopeId","data-v-5d5ea098"]]);export{I as default};
