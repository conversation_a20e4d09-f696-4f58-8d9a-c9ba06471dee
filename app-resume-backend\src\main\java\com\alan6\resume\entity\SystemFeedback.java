package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("system_feedback")
public class SystemFeedback implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 反馈ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 反馈编号（FB+时间戳）
     */
    @TableField("feedback_id")
    private String feedbackId;

    /**
     * 用户ID（匿名反馈时可为空）
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 反馈类型（bug:错误报告,feature:功能建议,complaint:投诉,suggestion:建议）
     */
    @TableField("type")
    private String type;

    /**
     * 反馈标题
     */
    @TableField("title")
    private String title;

    /**
     * 反馈内容
     */
    @TableField("content")
    private String content;

    /**
     * 联系邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 优先级（low:低,medium:中,high:高,urgent:紧急）
     */
    @TableField("priority")
    private String priority;

    /**
     * 处理状态（pending:待处理,processing:处理中,resolved:已解决,closed:已关闭）
     */
    @TableField("status")
    private String status;

    /**
     * 处理人ID
     */
    @TableField("handler_id")
    private Long handlerId;

    /**
     * 处理回复
     */
    @TableField("handler_reply")
    private String handlerReply;

    /**
     * 处理时间
     */
    @TableField("handle_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleTime;

    /**
     * 客户端信息（浏览器、设备等）
     */
    @TableField("client_info")
    private String clientInfo;

    /**
     * 是否删除（0:未删除,1:已删除）
     */
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 