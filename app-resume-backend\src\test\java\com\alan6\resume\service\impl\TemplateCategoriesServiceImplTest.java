package com.alan6.resume.service.impl;

import com.alan6.resume.dto.template.TemplateCategoryResponse;
import com.alan6.resume.entity.TemplateCategories;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.mapper.TemplateCategoriesMapper;
import com.alan6.resume.mapper.ResumeTemplatesMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TemplateCategoriesServiceImpl测试类
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@ExtendWith(MockitoExtension.class)
class TemplateCategoriesServiceImplTest {

    @Spy
    @InjectMocks
    private TemplateCategoriesServiceImpl templateCategoriesService;

    @Mock
    private ResumeTemplatesMapper resumeTemplatesMapper;

    @Mock
    private TemplateCategoriesMapper templateCategoriesMapper;

    private TemplateCategories mockCategory1;
    private TemplateCategories mockCategory2;
    private List<TemplateCategories> mockCategories;

    @BeforeEach
    void setUp() {
        // 准备测试数据 - 分类1
        mockCategory1 = new TemplateCategories();
        mockCategory1.setId(1L);
        mockCategory1.setName("商务简约");
        mockCategory1.setDescription("适合商务场合的简约风格模板");
        mockCategory1.setIconUrl("business-icon.png");
        mockCategory1.setSortOrder(1);
        mockCategory1.setStatus((byte) 1);
        mockCategory1.setIsDeleted((byte) 0);
        mockCategory1.setCreateTime(LocalDateTime.now());
        mockCategory1.setUpdateTime(LocalDateTime.now());

        // 准备测试数据 - 分类2
        mockCategory2 = new TemplateCategories();
        mockCategory2.setId(2L);
        mockCategory2.setName("创意设计");
        mockCategory2.setDescription("适合设计师的创意风格模板");
        mockCategory2.setIconUrl("creative-icon.png");
        mockCategory2.setSortOrder(2);
        mockCategory2.setStatus((byte) 1);
        mockCategory2.setIsDeleted((byte) 0);
        mockCategory2.setCreateTime(LocalDateTime.now());
        mockCategory2.setUpdateTime(LocalDateTime.now());

        mockCategories = Arrays.asList(mockCategory1, mockCategory2);
    }

    /**
     * 测试获取所有分类 - 成功场景
     */
    @Test
    void testGetAllCategories_Success() {
        // Mock list方法返回分类列表
        doReturn(mockCategories).when(templateCategoriesService).list(any(LambdaQueryWrapper.class));
        
        // Mock模板数量查询
        when(resumeTemplatesMapper.selectCount(any(LambdaQueryWrapper.class)))
            .thenReturn(5L)  // 第一个分类有5个模板
            .thenReturn(3L); // 第二个分类有3个模板

        // 执行测试
        List<TemplateCategoryResponse> result = templateCategoriesService.getAllCategories();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证第一个分类
        TemplateCategoryResponse category1 = result.get(0);
        assertEquals(1L, category1.getId());
        assertEquals("商务简约", category1.getName());
        assertEquals("适合商务场合的简约风格模板", category1.getDescription());
        assertEquals("business-icon.png", category1.getIconUrl());
        assertEquals(1, category1.getSortOrder());
        assertEquals(5, category1.getTemplateCount());
        
        // 验证第二个分类
        TemplateCategoryResponse category2 = result.get(1);
        assertEquals(2L, category2.getId());
        assertEquals("创意设计", category2.getName());
        assertEquals(3, category2.getTemplateCount());

        // 验证方法调用
        verify(templateCategoriesService, times(1)).list(any(LambdaQueryWrapper.class));
        verify(resumeTemplatesMapper, times(2)).selectCount(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试获取所有分类 - 空列表
     */
    @Test
    void testGetAllCategories_EmptyList() {
        // Mock返回空列表
        doReturn(Collections.emptyList()).when(templateCategoriesService).list(any(LambdaQueryWrapper.class));

        // 执行测试
        List<TemplateCategoryResponse> result = templateCategoriesService.getAllCategories();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证方法调用
        verify(templateCategoriesService, times(1)).list(any(LambdaQueryWrapper.class));
        verify(resumeTemplatesMapper, never()).selectCount(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试获取所有分类 - 数据库异常
     */
    @Test
    void testGetAllCategories_DatabaseException() {
        // Mock抛出异常
        doThrow(new RuntimeException("数据库连接异常")).when(templateCategoriesService).list(any(LambdaQueryWrapper.class));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> templateCategoriesService.getAllCategories());
        
        assertEquals("获取模板分类列表失败", exception.getMessage());
        assertNotNull(exception.getCause());
        assertEquals("数据库连接异常", exception.getCause().getMessage());
    }

    /**
     * 测试根据分类ID获取模板数量 - 成功场景
     */
    @Test
    void testGetTemplateCountByCategoryId_Success() {
        Long categoryId = 1L;
        Long expectedCount = 10L;
        
        // Mock查询结果
        when(resumeTemplatesMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(expectedCount);

        // 执行测试
        Integer result = templateCategoriesService.getTemplateCountByCategoryId(categoryId);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCount.intValue(), result);
        
        // 验证方法调用
        verify(resumeTemplatesMapper, times(1)).selectCount(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试根据分类ID获取模板数量 - 分类不存在
     */
    @Test
    void testGetTemplateCountByCategoryId_CategoryNotExists() {
        Long categoryId = 999L;
        
        // Mock查询结果为0
        when(resumeTemplatesMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0L);

        // 执行测试
        Integer result = templateCategoriesService.getTemplateCountByCategoryId(categoryId);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result);
    }

    /**
     * 测试根据分类ID获取模板数量 - 数据库异常
     */
    @Test
    void testGetTemplateCountByCategoryId_DatabaseException() {
        Long categoryId = 1L;
        
        // Mock抛出异常
        when(resumeTemplatesMapper.selectCount(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库查询异常"));

        // 执行测试
        Integer result = templateCategoriesService.getTemplateCountByCategoryId(categoryId);

        // 验证结果 - 异常时应返回0
        assertNotNull(result);
        assertEquals(0, result);
        
        // 验证方法调用
        verify(resumeTemplatesMapper, times(1)).selectCount(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试根据分类ID获取模板数量 - null参数
     */
    @Test
    void testGetTemplateCountByCategoryId_NullParameter() {
        // 执行测试 - 传入null参数
        Integer result = templateCategoriesService.getTemplateCountByCategoryId(null);

        // 验证结果 - 应该返回0或抛出异常，这里假设返回0
        assertNotNull(result);
        assertEquals(0, result);
    }

    /**
     * 测试查询条件构建是否正确
     */
    @Test
    void testQueryWrapperConditions() {
        // 使用真实的service方法来验证查询条件
        doAnswer(invocation -> {
            LambdaQueryWrapper<TemplateCategories> wrapper = invocation.getArgument(0);
            // 这里可以验证wrapper的条件是否正确设置
            // 由于wrapper的内部状态不易直接验证，我们通过Mock验证调用即可
            return mockCategories;
        }).when(templateCategoriesService).list(any(LambdaQueryWrapper.class));
        
        when(resumeTemplatesMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(5L);

        // 执行测试
        List<TemplateCategoryResponse> result = templateCategoriesService.getAllCategories();

        // 验证结果不为空
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    /**
     * 测试模板数量查询条件构建是否正确
     */
    @Test
    void testTemplateCountQueryWrapperConditions() {
        Long categoryId = 1L;
        
        // 使用Answer来验证查询条件
        doAnswer(invocation -> {
            LambdaQueryWrapper<ResumeTemplates> wrapper = invocation.getArgument(0);
            // 验证查询条件设置正确
            return 5L;
        }).when(resumeTemplatesMapper).selectCount(any(LambdaQueryWrapper.class));

        // 执行测试
        Integer result = templateCategoriesService.getTemplateCountByCategoryId(categoryId);

        // 验证结果
        assertEquals(5, result);
        verify(resumeTemplatesMapper, times(1)).selectCount(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试大数据量场景
     */
    @Test
    void testGetAllCategories_LargeDataSet() {
        // 创建大量分类数据
        List<TemplateCategories> largeCategories = Arrays.asList(
            createMockCategory(1L, "分类1", 1),
            createMockCategory(2L, "分类2", 2),
            createMockCategory(3L, "分类3", 3),
            createMockCategory(4L, "分类4", 4),
            createMockCategory(5L, "分类5", 5)
        );
        
        doReturn(largeCategories).when(templateCategoriesService).list(any(LambdaQueryWrapper.class));
        
        // Mock每个分类的模板数量
        when(resumeTemplatesMapper.selectCount(any(LambdaQueryWrapper.class)))
            .thenReturn(10L, 15L, 8L, 20L, 5L);

        // 执行测试
        List<TemplateCategoryResponse> result = templateCategoriesService.getAllCategories();

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.size());
        
        // 验证每个分类的数据
        assertEquals(10, result.get(0).getTemplateCount());
        assertEquals(15, result.get(1).getTemplateCount());
        assertEquals(8, result.get(2).getTemplateCount());
        assertEquals(20, result.get(3).getTemplateCount());
        assertEquals(5, result.get(4).getTemplateCount());
    }

    /**
     * 创建Mock分类对象的辅助方法
     */
    private TemplateCategories createMockCategory(Long id, String name, Integer sortOrder) {
        TemplateCategories category = new TemplateCategories();
        category.setId(id);
        category.setName(name);
        category.setDescription("测试分类描述");
        category.setIconUrl("test-icon.png");
        category.setSortOrder(sortOrder);
        category.setStatus((byte) 1);
        category.setIsDeleted((byte) 0);
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());
        return category;
    }
} 