<template>
  <div class="space-y-6">
    <div class="text-center">
      <!-- 微信扫一扫标题 -->
      <h3 class="font-semibold text-lg text-secondary-900 mb-6">微信扫一扫</h3>
      
      <!-- 二维码区域 -->
      <div class="relative w-48 h-48 mx-auto mb-6 bg-white border-2 border-secondary-200 rounded-2xl flex items-center justify-center">
        <div v-if="!qrCodeLoaded" class="text-center">
          <div class="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-secondary-600 text-sm">正在生成二维码...</p>
        </div>
        
        <!-- 模拟二维码 -->
        <div v-else class="w-40 h-40 bg-black rounded-lg relative overflow-hidden">
          <div class="absolute inset-2 bg-white rounded">
            <div class="w-full h-full relative">
              <!-- 二维码图案 -->
              <div class="absolute top-1 left-1 w-6 h-6 border-2 border-black"></div>
              <div class="absolute top-1 right-1 w-6 h-6 border-2 border-black"></div>
              <div class="absolute bottom-1 left-1 w-6 h-6 border-2 border-black"></div>
              
              <!-- 中间logo -->
              <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-primary-600 rounded flex items-center justify-center">
                <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.146 4.203 2.943 5.558-.687 1.717-1.562 3.525-1.562 3.525s3.528-.612 5.125-1.36c.687.145 1.395.217 2.185.217 4.8 0 8.691-3.287 8.691-7.34 0-4.052-3.891-7.341-8.691-7.341z"/>
                </svg>
              </div>
              
              <!-- 随机点阵 -->
              <div class="absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px p-8">
                <div v-for="i in 64" :key="i" :class="Math.random() > 0.5 ? 'bg-black' : 'bg-white'" class="w-full h-full"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 刷新按钮 -->
        <button 
          @click="refreshQRCode"
          class="absolute top-2 right-2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-secondary-50 transition-colors duration-200"
        >
          <svg class="w-4 h-4 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
      </div>
      
      <div class="space-y-3">
        <p class="text-sm text-secondary-600">微信扫码，关注公众号授权登录</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 响应式数据
const qrCodeLoaded = ref(false)

// 生命周期
onMounted(() => {
  // 模拟二维码加载
  setTimeout(() => {
    qrCodeLoaded.value = true
  }, 1000)
})

// 刷新二维码
const refreshQRCode = () => {
  qrCodeLoaded.value = false
  setTimeout(() => {
    qrCodeLoaded.value = true
  }, 500)
}
</script> 