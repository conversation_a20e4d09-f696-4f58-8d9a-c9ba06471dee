# 预览图上传和访问功能实现说明

## 前端修改完成

### 1. 模板上传功能增强

#### 修改文件: `composables/admin/useTemplateUploadService.js`
- ✅ 增强了uploadTemplate方法，确保预览图和模板文件一起上传
- ✅ 添加了`savePreviewImageUrl`标记，指示后端将预览图路径写入数据库
- ✅ 增加了详细的日志记录，便于调试

#### 关键修改点:
```javascript
// 添加预览图 - 确保预览图和模板文件存储在同一目录
if (previewImage) {
  console.log('📸 添加预览图到上传数据:', previewImage.name)
  uploadFormData.append('previewImage', previewImage)
  // 标记需要将预览图路径写入数据库的previewImageUrl字段
  uploadFormData.append('savePreviewImageUrl', 'true')
}
```

### 2. 模板列表显示功能增强

#### 修改文件: `composables/admin/useTemplateListService.js`
- ✅ 在fetchTemplates方法中增加了预览图URL处理逻辑
- ✅ 确保预览图URL通过API代理访问MinIO

#### 关键修改点:
```javascript
// 处理预览图URL，确保能正确访问MinIO中的图片
templatesList = templatesList.map(template => {
  if (template.previewImageUrl) {
    // 如果预览图URL不是完整路径，通过API代理访问
    if (!template.previewImageUrl.startsWith('http') && !template.previewImageUrl.startsWith('/api/')) {
      template.previewImageUrl = `/api/admin/template/preview-image/${template.templateCode}`
    }
  }
  return template
})
```

#### 修改文件: `pages/admin/template/list.vue`
- ✅ 增强了图片错误处理逻辑
- ✅ 添加了备用URL机制
- ✅ 增加了图片加载成功的处理

## 需要后端配置的内容

### 1. 模板上传API增强 (`/api/admin/template/upload`)

**需要处理的参数:**
- `previewImage`: 预览图文件
- `savePreviewImageUrl`: 布尔值，指示是否将预览图URL保存到数据库

**存储要求:**
1. 预览图应与模板文件存储在同一个桶中
2. 预览图存储路径建议: `/{bucketName}/{templateCode}/preview.{ext}`
3. 预览图URL应保存到 `resume_templates` 表的 `preview_image_url` 字段

**示例存储结构:**
```
resume-templates/
├── business-simple/
│   ├── BusinessSimple.vue
│   ├── preview.jpg  ← 预览图
│   └── other-files...
└── creative-modern/
    ├── CreativeModern.vue
    ├── preview.png  ← 预览图
    └── other-files...
```

### 2. 预览图访问API (`/api/admin/template/preview-image/{templateCode}`)

**新增API接口:**
- **路径**: `/api/admin/template/preview-image/{templateCode}`
- **方法**: GET
- **参数**: `templateCode` (路径参数)
- **功能**: 作为代理，从MinIO获取预览图并返回给前端
- **返回**: 直接返回图片二进制数据，设置正确的Content-Type

**实现逻辑:**
1. 根据templateCode查询数据库获取预览图路径
2. 从MinIO读取预览图文件
3. 设置正确的HTTP响应头
4. 返回图片二进制数据

**参考实现:**
```java
@GetMapping("/preview-image/{templateCode}")
public ResponseEntity<byte[]> getPreviewImage(@PathVariable String templateCode) {
    try {
        // 1. 查询模板信息
        ResumeTemplate template = templateService.getByTemplateCode(templateCode);
        if (template == null || StringUtils.isEmpty(template.getPreviewImageUrl())) {
            return ResponseEntity.notFound().build();
        }
        
        // 2. 从MinIO获取预览图
        byte[] imageData = minioService.getObject(bucketName, template.getPreviewImageUrl());
        
        // 3. 设置响应头
        HttpHeaders headers = new HttpHeaders();
        String contentType = getContentType(template.getPreviewImageUrl());
        headers.setContentType(MediaType.parseMediaType(contentType));
        headers.setCacheControl(CacheControl.maxAge(Duration.ofHours(1)));
        
        return ResponseEntity.ok().headers(headers).body(imageData);
    } catch (Exception e) {
        log.error("获取预览图失败: {}", e.getMessage());
        return ResponseEntity.status(500).build();
    }
}
```

### 3. 数据库字段确认

**表**: `resume_templates`
**字段**: `preview_image_url` (VARCHAR)
**用途**: 存储预览图在MinIO中的路径

**示例数据:**
```sql
-- 预览图URL存储示例
UPDATE resume_templates SET preview_image_url = 'business-simple/preview.jpg' WHERE template_code = 'business-simple';
UPDATE resume_templates SET preview_image_url = 'creative-modern/preview.png' WHERE template_code = 'creative-modern';
```

### 4. 错误处理

**上传失败处理:**
- 如果预览图上传失败，但模板文件上传成功，应该记录日志但不阻止模板创建
- 返回成功响应，但在响应中标注预览图上传失败

**访问失败处理:**
- 预览图API应返回适当的HTTP状态码
- 404: 模板不存在或预览图不存在
- 500: MinIO访问失败等服务器错误

## 测试验证

### 前端测试
1. ✅ 模板上传页面能正常选择和预览图片
2. ✅ 上传时能正确传递预览图数据
3. ✅ 模板列表能正常显示预览图
4. ✅ 图片加载失败时有备用机制

### 后端测试需求
1. 验证预览图能正确保存到MinIO
2. 验证数据库中preview_image_url字段正确更新
3. 验证预览图API能正常返回图片
4. 验证错误情况的处理

## 完成状态

### ✅ 前端已完成
- [x] 上传服务增强
- [x] 列表显示增强
- [x] 错误处理增强
- [x] URL处理逻辑

### ⏳ 后端待实现
- [ ] 上传API增强 (处理previewImage参数)
- [ ] 预览图访问API (新增接口)
- [ ] 数据库字段更新逻辑
- [ ] 错误处理机制

---

**注意事项:**
1. 确保MinIO的访问权限配置正确
2. 预览图API需要适当的缓存策略
3. 考虑图片格式验证和大小限制
4. 建议添加图片压缩功能以提高加载速度
