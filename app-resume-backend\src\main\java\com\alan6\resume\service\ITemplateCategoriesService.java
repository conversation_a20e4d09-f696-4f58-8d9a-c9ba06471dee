package com.alan6.resume.service;

import com.alan6.resume.dto.template.TemplateCategoryResponse;
import com.alan6.resume.entity.TemplateCategories;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 模板分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface ITemplateCategoriesService extends IService<TemplateCategories> {

    /**
     * 获取所有可用的模板分类列表
     * 包含每个分类的模板数量统计
     *
     * @return 模板分类响应列表
     */
    List<TemplateCategoryResponse> getAllCategories();

    /**
     * 管理员获取所有模板分类列表（包含所有状态的分类）
     * 包含每个分类的模板数量统计
     *
     * @return 模板分类响应列表
     */
    List<TemplateCategoryResponse> getAdminCategories();

    /**
     * 根据分类ID获取模板数量
     *
     * @param categoryId 分类ID
     * @return 模板数量
     */
    Integer getTemplateCountByCategoryId(Long categoryId);

}
