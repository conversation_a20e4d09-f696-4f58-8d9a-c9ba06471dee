/**
 * 模块操作组合式函数
 * @description 处理简历模块的移动、删除、排序等操作
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref } from 'vue'

/**
 * 模块操作组合式函数
 * @param {Object} props - 组件属性
 * @param {Function} emit - 事件触发函数
 * @returns {Object} 模块操作相关的方法和状态
 */
export function useModuleOperations(props, emit) {
  
  // ================================
  // 响应式状态
  // ================================
  
  /**
   * 悬停状态管理
   * @description 记录当前悬停的项目索引，用于显示操作按钮
   */
  const hoveredSkill = ref(-1)
  const hoveredExperience = ref(-1)
  const hoveredEducation = ref(-1)
  const hoveredProject = ref(-1)
  
  // ================================
  // 模块级操作
  // ================================
  
  /**
   * 获取模块标题
   * @param {string} moduleType - 模块类型
   * @returns {string} 模块标题
   */
  const getModuleTitle = (moduleType) => {
    const titleMap = {
      basic_info: '基本信息',
      education: '教育经历',
      work_experience: '工作经历',
      project: '项目经验',
      skills: '技能标签',
      language: '语言能力',
      award: '获奖荣誉',
      hobbies: '兴趣爱好',
      self_evaluation: '自我评价',
      internship: '实习经历',
      training: '培训经历',
      research_experience: '研究经历',
      publication: '论文专利',
      certificate: '证书资质',
      volunteer_experience: '志愿服务',
      portfolio: '作品集',
      cover_letter: '自荐信',
      custom: '自定义'
    }
    return titleMap[moduleType] || '未知模块'
  }
  
  /**
   * 模块上移
   * @param {string} moduleType - 模块类型
   * @description 将指定模块向上移动一个位置
   */
  const moveModuleUp = (moduleType) => {
    console.log(`🔼 模块上移: ${moduleType}`)
    emit('module-move-up', moduleType)
  }
  
  /**
   * 模块下移
   * @param {string} moduleType - 模块类型
   * @description 将指定模块向下移动一个位置
   */
  const moveModuleDown = (moduleType) => {
    console.log(`🔽 模块下移: ${moduleType}`)
    emit('module-move-down', moduleType)
  }
  
  /**
   * 删除模块
   * @param {string} moduleType - 模块类型
   * @description 删除指定模块，需要用户确认
   */
  const deleteModule = (moduleType) => {
    const moduleTitle = getModuleTitle(moduleType)
    if (confirm(`确定要删除${moduleTitle}模块吗？`)) {
      console.log(`🗑️ 删除模块: ${moduleType}`)
      emit('module-delete', moduleType)
    }
  }
  
  // ================================
  // 技能操作
  // ================================
  
  /**
   * 技能上移
   * @param {number} index - 技能索引
   * @param {Array} skills - 技能列表
   * @description 将指定技能向上移动一个位置
   */
  const moveSkillUp = (index, skills) => {
    if (index > 0) {
      console.log(`🔼 技能上移: 索引 ${index}`)
      const newSkills = [...skills]
      ;[newSkills[index], newSkills[index - 1]] = [newSkills[index - 1], newSkills[index]]
      emit('skill-update', newSkills)
    }
  }
  
  /**
   * 技能下移
   * @param {number} index - 技能索引
   * @param {Array} skills - 技能列表
   * @description 将指定技能向下移动一个位置
   */
  const moveSkillDown = (index, skills) => {
    if (index < skills.length - 1) {
      console.log(`🔽 技能下移: 索引 ${index}`)
      const newSkills = [...skills]
      ;[newSkills[index], newSkills[index + 1]] = [newSkills[index + 1], newSkills[index]]
      emit('skill-update', newSkills)
    }
  }
  
  /**
   * 删除技能
   * @param {number} index - 技能索引
   * @param {Array} skills - 技能列表
   * @description 删除指定技能，需要用户确认
   */
  const deleteSkill = (index, skills) => {
    if (confirm('确定要删除这个技能吗？')) {
      console.log(`🗑️ 删除技能: 索引 ${index}`)
      const newSkills = skills.filter((_, i) => i !== index)
      emit('skill-update', newSkills)
    }
  }
  
  // ================================
  // 工作经历操作
  // ================================
  
  /**
   * 工作经历上移
   * @param {number} index - 经历索引
   * @param {Array} experiences - 工作经历列表
   * @description 将指定工作经历向上移动一个位置
   */
  const moveExperienceUp = (index, experiences) => {
    if (index > 0) {
      console.log(`🔼 工作经历上移: 索引 ${index}`)
      const newExperiences = [...experiences]
      ;[newExperiences[index], newExperiences[index - 1]] = [newExperiences[index - 1], newExperiences[index]]
      emit('experience-update', newExperiences)
    }
  }
  
  /**
   * 工作经历下移
   * @param {number} index - 经历索引
   * @param {Array} experiences - 工作经历列表
   * @description 将指定工作经历向下移动一个位置
   */
  const moveExperienceDown = (index, experiences) => {
    if (index < experiences.length - 1) {
      console.log(`🔽 工作经历下移: 索引 ${index}`)
      const newExperiences = [...experiences]
      ;[newExperiences[index], newExperiences[index + 1]] = [newExperiences[index + 1], newExperiences[index]]
      emit('experience-update', newExperiences)
    }
  }
  
  /**
   * 删除工作经历
   * @param {number} index - 经历索引
   * @param {Array} experiences - 工作经历列表
   * @description 删除指定工作经历，需要用户确认
   */
  const deleteExperience = (index, experiences) => {
    if (confirm('确定要删除这段工作经历吗？')) {
      console.log(`🗑️ 删除工作经历: 索引 ${index}`)
      const newExperiences = experiences.filter((_, i) => i !== index)
      emit('experience-update', newExperiences)
    }
  }
  
  // ================================
  // 教育经历操作
  // ================================
  
  /**
   * 教育经历上移
   * @param {number} index - 经历索引
   * @param {Array} educations - 教育经历列表
   * @description 将指定教育经历向上移动一个位置
   */
  const moveEducationUp = (index, educations) => {
    if (index > 0) {
      console.log(`🔼 教育经历上移: 索引 ${index}`)
      const newEducations = [...educations]
      ;[newEducations[index], newEducations[index - 1]] = [newEducations[index - 1], newEducations[index]]
      emit('education-update', newEducations)
    }
  }
  
  /**
   * 教育经历下移
   * @param {number} index - 经历索引
   * @param {Array} educations - 教育经历列表
   * @description 将指定教育经历向下移动一个位置
   */
  const moveEducationDown = (index, educations) => {
    if (index < educations.length - 1) {
      console.log(`🔽 教育经历下移: 索引 ${index}`)
      const newEducations = [...educations]
      ;[newEducations[index], newEducations[index + 1]] = [newEducations[index + 1], newEducations[index]]
      emit('education-update', newEducations)
    }
  }
  
  /**
   * 删除教育经历
   * @param {number} index - 经历索引
   * @param {Array} educations - 教育经历列表
   * @description 删除指定教育经历，需要用户确认
   */
  const deleteEducation = (index, educations) => {
    if (confirm('确定要删除这段教育经历吗？')) {
      console.log(`🗑️ 删除教育经历: 索引 ${index}`)
      const newEducations = educations.filter((_, i) => i !== index)
      emit('education-update', newEducations)
    }
  }
  
  // ================================
  // 项目经历操作
  // ================================
  
  /**
   * 项目经历上移
   * @param {number} index - 项目索引
   * @param {Array} projects - 项目经历列表
   * @description 将指定项目经历向上移动一个位置
   */
  const moveProjectUp = (index, projects) => {
    if (index > 0) {
      console.log(`🔼 项目经历上移: 索引 ${index}`)
      const newProjects = [...projects]
      ;[newProjects[index], newProjects[index - 1]] = [newProjects[index - 1], newProjects[index]]
      emit('project-update', newProjects)
    }
  }
  
  /**
   * 项目经历下移
   * @param {number} index - 项目索引
   * @param {Array} projects - 项目经历列表
   * @description 将指定项目经历向下移动一个位置
   */
  const moveProjectDown = (index, projects) => {
    if (index < projects.length - 1) {
      console.log(`🔽 项目经历下移: 索引 ${index}`)
      const newProjects = [...projects]
      ;[newProjects[index], newProjects[index + 1]] = [newProjects[index + 1], newProjects[index]]
      emit('project-update', newProjects)
    }
  }
  
  /**
   * 删除项目经历
   * @param {number} index - 项目索引
   * @param {Array} projects - 项目经历列表
   * @description 删除指定项目经历，需要用户确认
   */
  const deleteProject = (index, projects) => {
    if (confirm('确定要删除这个项目经历吗？')) {
      console.log(`🗑️ 删除项目经历: 索引 ${index}`)
      const newProjects = projects.filter((_, i) => i !== index)
      emit('project-update', newProjects)
    }
  }
  
  // ================================
  // 基本信息操作
  // ================================
  
  /**
   * 处理基本信息更新
   * @param {Object} basicInfo - 新的基本信息
   * @description 更新基本信息数据
   */
  const handleBasicInfoUpdate = (basicInfo) => {
    console.log('📝 基本信息更新:', basicInfo)
    emit('basic-info-update', basicInfo)
  }
  
  // ================================
  // 通用操作工具
  // ================================
  
  /**
   * 批量操作确认
   * @param {string} operation - 操作类型
   * @param {number} count - 操作数量
   * @returns {boolean} 是否确认操作
   */
  const confirmBatchOperation = (operation, count) => {
    return confirm(`确定要${operation} ${count} 个项目吗？`)
  }
  
  /**
   * 检查是否可以移动
   * @param {number} index - 当前索引
   * @param {number} length - 列表长度
   * @param {string} direction - 移动方向 ('up' | 'down')
   * @returns {boolean} 是否可以移动
   */
  const canMove = (index, length, direction) => {
    if (direction === 'up') {
      return index > 0
    } else if (direction === 'down') {
      return index < length - 1
    }
    return false
  }
  
  /**
   * 获取操作按钮样式
   * @param {boolean} disabled - 是否禁用
   * @param {string} type - 按钮类型 ('move' | 'delete')
   * @returns {Object} 样式对象
   */
  const getOperationButtonStyle = (disabled, type) => {
    const baseStyle = {
      opacity: disabled ? 0.5 : 1,
      cursor: disabled ? 'not-allowed' : 'pointer',
      transition: 'all 0.2s ease'
    }
    
    if (type === 'delete') {
      return {
        ...baseStyle,
        backgroundColor: disabled ? '#fca5a5' : '#ef4444',
        color: 'white'
      }
    }
    
    return {
      ...baseStyle,
      backgroundColor: disabled ? '#d1d5db' : '#3b82f6',
      color: 'white'
    }
  }
  
  // ================================
  // 返回对象
  // ================================
  
  return {
    // 悬停状态
    hoveredSkill,
    hoveredExperience,
    hoveredEducation,
    hoveredProject,
    
    // 模块操作
    moveModuleUp,
    moveModuleDown,
    deleteModule,
    getModuleTitle,
    
    // 技能操作
    moveSkillUp,
    moveSkillDown,
    deleteSkill,
    
    // 工作经历操作
    moveExperienceUp,
    moveExperienceDown,
    deleteExperience,
    
    // 教育经历操作
    moveEducationUp,
    moveEducationDown,
    deleteEducation,
    
    // 项目经历操作
    moveProjectUp,
    moveProjectDown,
    deleteProject,
    
    // 基本信息操作
    handleBasicInfoUpdate,
    
    // 工具方法
    confirmBatchOperation,
    canMove,
    getOperationButtonStyle
  }
} 