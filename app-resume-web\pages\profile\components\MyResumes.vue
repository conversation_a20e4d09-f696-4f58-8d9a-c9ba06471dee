<template>
  <div>
    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-secondary-900">我的简历</h2>
      <p class="mt-1 text-sm text-secondary-600">管理您创建的所有简历</p>
    </div>

    <!-- 简历列表 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p class="text-secondary-600">加载中...</p>
      </div>
    </div>

    <div v-else-if="resumes.length === 0" class="text-center py-12">
      <div class="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-medium text-secondary-900 mb-2">还没有创建简历</h3>
      <p class="text-secondary-600 mb-6">开始创建您的第一份专业简历吧</p>
      <button
        @click="handleCreateResume"
        class="px-6 py-3 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200"
      >
        创建简历
      </button>
    </div>

    <div v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <div
        v-for="resume in resumes"
        :key="resume.id"
        class="group relative bg-white border border-secondary-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300"
      >
        <!-- 简历预览图 -->
        <div class="aspect-[3/4] bg-secondary-100 relative overflow-hidden">
          <img 
            v-if="resume.thumbnail" 
            :src="resume.thumbnail" 
            :alt="resume.title"
            class="w-full h-full object-cover"
          >
          <div v-else class="w-full h-full flex items-center justify-center">
            <svg class="w-16 h-16 text-secondary-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>

          <!-- 操作按钮（悬停显示） -->
          <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
            <button
              @click="handleEditResume(resume)"
              class="px-4 py-2 bg-white text-secondary-900 rounded-lg font-medium hover:bg-secondary-100 transition-colors duration-200"
            >
              编辑
            </button>
            <button
              @click="handlePreviewResume(resume)"
              class="px-4 py-2 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200"
            >
              预览
            </button>
          </div>
        </div>

        <!-- 简历信息 -->
        <div class="p-4">
          <h3 class="font-medium text-secondary-900 mb-1 truncate">{{ resume.title || '未命名简历' }}</h3>
          <p class="text-sm text-secondary-500">
            更新于 {{ formatDate(resume.updatedAt) }}
          </p>
        </div>

        <!-- 更多操作菜单 -->
        <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="relative">
            <button
              @click="toggleMenu(resume.id)"
              class="w-8 h-8 bg-white/90 backdrop-blur rounded-lg flex items-center justify-center hover:bg-white transition-colors duration-200"
            >
              <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>

            <!-- 下拉菜单 -->
            <Transition
              enter-active-class="transition ease-out duration-100"
              enter-from-class="opacity-0 scale-95"
              enter-to-class="opacity-100 scale-100"
              leave-active-class="transition ease-in duration-75"
              leave-from-class="opacity-100 scale-100"
              leave-to-class="opacity-0 scale-95"
            >
              <div
                v-if="activeMenu === resume.id"
                class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-100 overflow-hidden z-10"
              >
                <button
                  @click="handleDuplicateResume(resume)"
                  class="w-full px-4 py-2 text-left text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-150"
                >
                  <span class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <span>复制简历</span>
                  </span>
                </button>
                <button
                  @click="handleRenameResume(resume)"
                  class="w-full px-4 py-2 text-left text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-150"
                >
                  <span class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <span>重命名</span>
                  </span>
                </button>
                <button
                  @click="handleDeleteResume(resume)"
                  class="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"
                >
                  <span class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    <span>删除</span>
                  </span>
                </button>
              </div>
            </Transition>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { $message } from '~/composables/shared/useGlobalMessage'

// 状态
const loading = ref(true)
const resumes = ref([])
const activeMenu = ref(null)

// 模拟数据（后续替换为真实API）
const mockResumes = [
  {
    id: '1',
    title: '前端开发工程师简历',
    thumbnail: null,
    updatedAt: new Date('2024-01-15T10:30:00'),
    templateId: 'template-1'
  },
  {
    id: '2',
    title: 'UI设计师简历',
    thumbnail: null,
    updatedAt: new Date('2024-01-10T14:20:00'),
    templateId: 'template-2'
  },
  {
    id: '3',
    title: '产品经理简历',
    thumbnail: null,
    updatedAt: new Date('2024-01-05T09:15:00'),
    templateId: 'template-3'
  }
]

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  const d = new Date(date)
  const now = new Date()
  const diff = now - d
  
  // 小于1小时
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000)
    return `${minutes}分钟前`
  }
  
  // 小于24小时
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000)
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000)
    return `${days}天前`
  }
  
  // 其他情况显示日期
  return d.toLocaleDateString('zh-CN')
}

// 切换菜单
const toggleMenu = (resumeId) => {
  activeMenu.value = activeMenu.value === resumeId ? null : resumeId
}

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    activeMenu.value = null
  }
}

// 创建简历
const handleCreateResume = () => {
  navigateTo('/templates')
}

// 编辑简历
const handleEditResume = (resume) => {
  navigateTo(`/editor/${resume.id}`)
}

// 预览简历
const handlePreviewResume = (resume) => {
  // TODO: 实现预览功能
  $message.info('预览功能开发中...')
}

// 复制简历
const handleDuplicateResume = async (resume) => {
  activeMenu.value = null
  try {
    // TODO: 调用API复制简历
    $message.success('简历复制成功')
    // 重新加载列表
    await loadResumes()
  } catch (error) {
    $message.error('复制失败，请重试')
  }
}

// 重命名简历
const handleRenameResume = (resume) => {
  activeMenu.value = null
  // TODO: 实现重命名功能（弹窗输入新名称）
  $message.info('重命名功能开发中...')
}

// 删除简历
const handleDeleteResume = async (resume) => {
  activeMenu.value = null
  if (confirm(`确定要删除"${resume.title}"吗？此操作不可恢复。`)) {
    try {
      // TODO: 调用API删除简历
      $message.success('简历已删除')
      // 重新加载列表
      await loadResumes()
    } catch (error) {
      $message.error('删除失败，请重试')
    }
  }
}

// 加载简历列表
const loadResumes = async () => {
  loading.value = true
  try {
    // TODO: 调用真实API获取简历列表
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    resumes.value = mockResumes
  } catch (error) {
    $message.error('加载简历列表失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadResumes()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 添加一些自定义样式 */
</style> 