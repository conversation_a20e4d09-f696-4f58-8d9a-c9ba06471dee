<template>
  <div class="route-test">
    <h1>路由测试页面</h1>
    
    <div class="current-route">
      <h2>当前路由信息</h2>
      <ul>
        <li>路径: {{ $route.path }}</li>
        <li>名称: {{ $route.name }}</li>
        <li>参数: {{ JSON.stringify($route.params) }}</li>
        <li>查询: {{ JSON.stringify($route.query) }}</li>
      </ul>
    </div>
    
    <div class="navigation-test">
      <h2>导航测试</h2>
      <div class="nav-buttons">
        <button @click="navigateTo('/admin')" class="btn">管理首页</button>
        <button @click="navigateTo('/admin/template/list')" class="btn">模板列表</button>
        <button @click="navigateTo('/admin/template/converter')" class="btn">模板转换</button>
        <button @click="navigateTo('/admin/template/upload')" class="btn">模板上传</button>
      </div>
    </div>
    
    <div class="direct-links">
      <h2>直接链接测试</h2>
      <ul>
        <li><NuxtLink to="/admin">管理首页</NuxtLink></li>
        <li><NuxtLink to="/admin/template/list">模板列表</NuxtLink></li>
        <li><NuxtLink to="/admin/template/converter">模板转换</NuxtLink></li>
        <li><NuxtLink to="/admin/template/upload">模板上传</NuxtLink></li>
        <li><NuxtLink to="/admin/template/categories">分类管理</NuxtLink></li>
      </ul>
    </div>
  </div>
</template>

<script setup>
const router = useRouter()

const navigateTo = async (path) => {
  console.log('导航到:', path)
  try {
    await router.push(path)
  } catch (error) {
    console.error('导航失败:', error)
  }
}
</script>

<style scoped>
.route-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.current-route, .navigation-test, .direct-links {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.nav-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn:hover {
  background: #2563eb;
}

ul {
  list-style-type: disc;
  padding-left: 20px;
}

li {
  margin: 5px 0;
}

a {
  color: #3b82f6;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style> 