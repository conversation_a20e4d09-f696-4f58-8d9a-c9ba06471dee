<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简历 - 王明远</title>
  <style>
    :root {
      --resume-font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      --resume-font-size: 14px;
      --resume-line-height: 1.6;
      --resume-primary-color: #007b81;
      --resume-text-color: #333;
      --resume-secondary-color: #666;
      --resume-margin-top: 60px;
      --resume-margin-bottom: 60px;
      --resume-margin-left: 60px;
      --resume-margin-right: 60px;
      --resume-module-spacing: 24px;
      --resume-item-spacing: 16px;
      --resume-paragraph-spacing: 12px;
      --resume-column-gap: 40px;
      --resume-module-padding: 16px;
      --resume-item-padding: 12px;
    }
    body {
      font-family: var(--resume-font-family);
      font-size: var(--resume-font-size);
      line-height: var(--resume-line-height);
      margin: 0;
      padding: 0;
      color: var(--resume-text-color);
      background: #ffffff;
    }
    .resume-template {
      width: 794px;
      margin: 0 auto;
      border: 1px solid #eee;
      box-sizing: border-box;
    }
    .header {
      background: var(--resume-primary-color);
      color: #fff;
      padding: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .header-left h1 {
      font-size: 28px;
      margin: 0;
      font-weight: bold;
    }
    .header-left p {
      margin: 10px 0 0;
      font-size: 14px;
    }
    .header-right {
      width: 96px;
      height: 96px;
      border-radius: 10px;
      background: #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666;
    }

    .resume-section {
      padding: 20px 40px;
      border-bottom: 1px solid #ddd;
    }

    .section-title {
      display: inline-block;
      background-color: var(--resume-primary-color);
      color: #fff;
      padding: 5px 15px;
      font-size: 16px;
      font-weight: bold;
      position: relative;
      margin-bottom: 15px;
    }
    .section-title::after {
      content: "";
      position: absolute;
      right: -12px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-top: 12px solid transparent;
      border-bottom: 12px solid transparent;
      border-left: 12px solid var(--resume-primary-color);
    }

    .resume-item {
      margin-bottom: var(--resume-item-spacing);
    }
    .item-title {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
    }
    .item-desc {
      margin-top: 5px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="resume-template">
    <div class="header">
      <div class="header-left basic-info">
        <h1><span >{{ mergedResumeData.basicInfo.name }}</span>/span></h1>
        <p>
          <span v-if="mergedResumeData.basicInfo.gender" >{{ mergedResumeData.basicInfo.gender }}</span>/span>
          <span v-if="mergedResumeData.basicInfo.gender">｜</span>
          <span v-if="mergedResumeData.basicInfo.age" >{{ mergedResumeData.basicInfo.age }}</span>/span>
          <span v-if="mergedResumeData.basicInfo.age">｜</span>
          <span >{{ mergedResumeData.basicInfo.currentCity }}</span>/span>｜📞
          <span >{{ mergedResumeData.basicInfo.phone }}</span>/span>｜✉️
          <span >{{ mergedResumeData.basicInfo.email }}</span>/span>
        </p>
        <p>
          当前状态：<span >{{ mergedResumeData.basicInfo.currentStatus }}</span>/span>｜
          期望岗位：<span >{{ mergedResumeData.basicInfo.expectedPosition }}</span>/span>｜
          期望薪资：<span >{{ mergedResumeData.basicInfo.expectedSalary }}</span>/span>
        </p>
      </div>
      <div class="header-right" v-if="mergedResumeData.basicInfo.photo">
        <img :src="mergedResumeData.basicInfo.photo" width="96" height="96" style="border-radius: 10px; object-fit: cover;" />
      </div>
      <div class="header-right" v-if="!mergedResumeData.basicInfo.photo">
        头像
      </div>
    </div>

    <section class="resume-section" v-if="mergedResumeData.educations && mergedResumeData.educations.length > 0">
      <div class="section-title">教育经历</div>
      <div v-for="education in mergedResumeData.educations" :key="education.id" class="resume-item">
        <div class="item-title">
          <span>
            <span >{{ education.school }}</span>/span>｜
            <span >{{ education.major }}</span>/span>
            <span >{{ education.degree }}</span>/span>
          </span>
          <span>
            <span >{{ education.start_date }}</span>/span> - <span >{{ education.end_date }}</span>/span>
          </span>
        </div>
        <div class="item-desc" v-if="education.description" >{{ education.description }}</span>/div>
      </div>
    </section>

    <section class="resume-section" v-if="mergedResumeData.workExperiences && mergedResumeData.workExperiences.length > 0">
      <div class="section-title">工作经历</div>
      <div v-for="work in mergedResumeData.workExperiences" :key="work.id" class="resume-item">
        <div class="item-title">
          <span>
            <span >{{ work.company }}</span>/span>｜<span >{{ work.position }}</span>/span>
          </span>
          <span>
            <span >{{ work.start_date }}</span>/span> - <span >{{ work.end_date }}</span>/span>
          </span>
        </div>
        <div class="item-desc" v-if="work.description" >{{ work.description }}</span>/div>
      </div>
    </section>

    <section class="resume-section" v-if="mergedResumeData.projects && mergedResumeData.projects.length > 0">
      <div class="section-title">项目经历</div>
      <div v-for="project in mergedResumeData.projects" :key="project.id" class="resume-item">
        <div class="item-title">
          <span >{{ project.name }}</span>/span>｜<span >{{ project.role }}</span>/div>
        <div class="item-desc">
          <div v-if="project.technologies">技术栈：<span >{{ project.technologies }}</span>/div>
          <div v-if="project.description" >{{ project.description }}</span>/div>
        </div>
      </div>
    </section>

    <section class="resume-section" v-if="mergedResumeData.skills && mergedResumeData.skills.length > 0">
      <div class="section-title">技能特长</div>
      <div v-for="skill in mergedResumeData.skills" :key="skill.id" class="resume-item">
        <div class="item-title">
          <span >{{ skill.name }}</span>/span>
          <span v-if="skill.level" >{{ skill.level }}</span>/div>
      </div>
    </section>

    <section class="resume-section" v-if="mergedResumeData.awards && mergedResumeData.awards.length > 0">
      <div class="section-title">获奖荣誉</div>
      <div v-for="award in mergedResumeData.awards" :key="award.id" class="resume-item">
        <div class="item-title">
          <span >{{ award.name }}</span>/span>
          <span v-if="award.organization">｜<span >{{ award.organization }}</span>/span></span>
          <span v-if="award.level">｜<span >{{ award.level }}</span>/span></span>
          <span v-if="award.date">
            <span >{{ award.date }}</span>/span>
          </span>
        </div>
        <div class="item-desc" v-if="award.description" >{{ award.description }}</span>/div>
      </div>
    </section>

    <section class="resume-section" v-if="mergedResumeData.selfEvaluation && mergedResumeData.selfEvaluation.content">
      <div class="section-title">自我评价</div>
      <div class="item-desc" >{{ mergedResumeData.selfEvaluation.content }}</span>/div>
    </section>
  </div>
</body>
</html>