package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员套餐响应DTO
 * 
 * 主要功能：
 * 1. 返回会员套餐的详细信息
 * 2. 包含套餐特性和权益描述
 * 3. 支持排序和推荐标识
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "会员套餐响应", description = "会员套餐详细信息")
public class MembershipPackageResponse {

    /**
     * 套餐ID
     */
    @Schema(description = "套餐的唯一标识ID", example = "1")
    private Long id;

    /**
     * 套餐名称
     */
    @Schema(description = "套餐名称", example = "月度会员")
    private String packageName;

    /**
     * 套餐代码
     */
    @Schema(description = "套餐代码，用于系统识别", example = "monthly")
    private String packageCode;

    /**
     * 套餐描述
     */
    @Schema(description = "套餐的详细描述", example = "享受高级模板、无限导出等特权")
    private String description;

    /**
     * 时长类型
     * 1-天，2-月，3-季，4-年，5-永久
     */
    @Schema(description = "时长类型：1-天，2-月，3-季，4-年，5-永久", example = "2")
    private Byte durationType;

    /**
     * 时长数值
     */
    @Schema(description = "时长数值，配合时长类型使用", example = "1")
    private Integer durationValue;

    /**
     * 原价
     */
    @Schema(description = "套餐原价", example = "29.90")
    private BigDecimal originalPrice;

    /**
     * 现价
     */
    @Schema(description = "套餐现价（优惠后价格）", example = "19.90")
    private BigDecimal currentPrice;

    /**
     * 最大简历数量
     * -1表示无限制
     */
    @Schema(description = "最大简历数量，-1表示无限制", example = "-1")
    private Integer maxResumeCount;

    /**
     * 每月导出次数
     * -1表示无限制
     */
    @Schema(description = "每月导出次数，-1表示无限制", example = "-1")
    private Integer maxExportCount;

    /**
     * 是否可用付费模板
     */
    @Schema(description = "是否可以使用付费模板", example = "true")
    private Boolean premiumTemplates;

    /**
     * 是否推荐
     */
    @Schema(description = "是否为推荐套餐", example = "false")
    private Boolean isRecommended;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重，数值越大排序越靠前", example = "1")
    private Integer sortOrder;

    /**
     * 特性列表
     */
    @Schema(description = "套餐特性列表", example = "[\"无限制创建简历\", \"使用所有付费模板\"]")
    private List<String> features;

    /**
     * 获取时长描述
     * 
     * @return 时长的可读描述
     */
    public String getDurationDescription() {
        if (durationValue == null || durationType == null) {
            return "未知";
        }
        
        String unit;
        switch (durationType) {
            case 1:
                unit = "天";
                break;
            case 2:
                unit = "月";
                break;
            case 3:
                unit = "季";
                break;
            case 4:
                unit = "年";
                break;
            case 5:
                return "永久";
            default:
                unit = "未知";
        }
        
        return durationValue + unit;
    }

    /**
     * 是否有折扣
     * 
     * @return true-有折扣，false-无折扣
     */
    public Boolean hasDiscount() {
        if (originalPrice == null || currentPrice == null) {
            return false;
        }
        return originalPrice.compareTo(currentPrice) > 0;
    }

    /**
     * 获取折扣率
     * 
     * @return 折扣率（保留两位小数）
     */
    public BigDecimal getDiscountRate() {
        if (!hasDiscount()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal discount = originalPrice.subtract(currentPrice);
        return discount.divide(originalPrice, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .setScale(2, BigDecimal.ROUND_HALF_UP);
    }
} 