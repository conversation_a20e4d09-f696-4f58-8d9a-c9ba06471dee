package com.alan6.resume.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统指标响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "SystemMetricsResponse", description = "系统指标响应")
public class SystemMetricsResponse {

    /**
     * 性能指标
     */
    @Schema(description = "性能指标")
    private PerformanceMetrics performance;

    /**
     * 业务指标
     */
    @Schema(description = "业务指标")
    private BusinessMetrics business;

    /**
     * 错误统计
     */
    @Schema(description = "错误统计")
    private ErrorMetrics errors;

    /**
     * 检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检查时间", example = "2024-12-22 17:00:00")
    private LocalDateTime checkTime;

    /**
     * 性能指标内部类
     */
    @Data
    @Schema(name = "PerformanceMetrics", description = "性能指标")
    public static class PerformanceMetrics {
        @Schema(description = "平均响应时间(ms)", example = "120")
        private Double avgResponseTime;
        
        @Schema(description = "QPS", example = "150")
        private Double qps;
        
        @Schema(description = "CPU使用率(%)", example = "45.5")
        private Double cpuUsage;
        
        @Schema(description = "内存使用率(%)", example = "68.2")
        private Double memoryUsage;
        
        @Schema(description = "磁盘使用率(%)", example = "35.8")
        private Double diskUsage;
        
        @Schema(description = "网络IO(KB/s)", example = "1024")
        private Double networkIo;
        
        @Schema(description = "数据库连接池使用率(%)", example = "25.0")
        private Double dbPoolUsage;
        
        @Schema(description = "Redis连接数", example = "10")
        private Integer redisConnections;
    }

    /**
     * 业务指标内部类
     */
    @Data
    @Schema(name = "BusinessMetrics", description = "业务指标")
    public static class BusinessMetrics {
        @Schema(description = "在线用户数", example = "156")
        private Integer onlineUsers;
        
        @Schema(description = "今日新用户", example = "45")
        private Integer todayNewUsers;
        
        @Schema(description = "今日活跃用户", example = "520")
        private Integer todayActiveUsers;
        
        @Schema(description = "今日简历创建数", example = "156")
        private Integer todayResumeCreated;
        
        @Schema(description = "今日简历导出数", example = "89")
        private Integer todayResumeExported;
        
        @Schema(description = "今日订单数", example = "15")
        private Integer todayOrders;
        
        @Schema(description = "今日收入", example = "1500.00")
        private Double todayRevenue;
        
        @Schema(description = "待处理订单数", example = "8")
        private Integer pendingOrders;
    }

    /**
     * 错误指标内部类
     */
    @Data
    @Schema(name = "ErrorMetrics", description = "错误指标")
    public static class ErrorMetrics {
        @Schema(description = "今日错误总数", example = "12")
        private Integer todayErrorCount;
        
        @Schema(description = "错误率(%)", example = "0.8")
        private Double errorRate;
        
        @Schema(description = "最近错误列表")
        private List<RecentError> recentErrors;
    }

    /**
     * 最近错误内部类
     */
    @Data
    @Schema(name = "RecentError", description = "最近错误")
    public static class RecentError {
        @Schema(description = "错误类型", example = "BusinessException")
        private String errorType;
        
        @Schema(description = "错误消息", example = "文件上传失败")
        private String message;
        
        @Schema(description = "发生次数", example = "3")
        private Integer count;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "最后发生时间", example = "2024-12-22 16:55:00")
        private LocalDateTime lastOccurred;
    }
} 