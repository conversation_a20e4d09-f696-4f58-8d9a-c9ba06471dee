<template>
  <div class="language-form">
    <div class="form-header">
      <h4 class="form-title">语言能力</h4>
      <p class="form-desc">展示您的语言水平和相关证书</p>
    </div>
    
    <div v-if="formData.languages && formData.languages.length > 0" class="languages-list">
      <div v-for="(language, index) in formData.languages" :key="index" class="language-item">
        <div class="language-controls">
          <input
            v-model="language.name"
            type="text"
            class="language-name-input"
            placeholder="语言名称（如：英语、日语）"
            @input="handleLanguageChange"
          />
          <select
            v-model="language.level"
            class="language-level-select"
            @change="handleLanguageChange"
          >
            <option value="">选择水平</option>
            <option value="初级">初级</option>
            <option value="中级">中级</option>
            <option value="高级">高级</option>
            <option value="母语">母语</option>
            <option value="流利">流利</option>
          </select>
          <button 
            class="delete-btn"
            @click="removeLanguage(index)"
            title="删除语言"
          >
            ×
          </button>
        </div>
        
        <div class="language-details">
          <input
            v-model="language.certificate"
            type="text"
            class="certificate-input"
            placeholder="相关证书（如：CET-6、JLPT N2）"
            @input="handleLanguageChange"
          />
                    <RichTextEditor
            v-model="language.description"
            placeholder="详细描述（如：能够进行商务交流、阅读技术文档等）"
            min-height="120px"
            @update:modelValue="handleLanguageChange"
          />
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无语言能力信息</p>
      <p class="empty-hint">点击下方按钮添加您的语言能力</p>
    </div>
    
    <div class="form-actions">
      <button class="add-btn" @click="addLanguage">
        <Icon name="plus" size="sm" />
        <span>添加语言</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ languages: [] })
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = reactive({
  languages: []
})

// 防抖定时器
let debounceTimer = null

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.languages) {
    formData.languages = [...newData.languages]
  } else {
    formData.languages = []
  }
}, { immediate: true, deep: true })

/**
 * 处理语言变化 - 实时更新
 */
const handleLanguageChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的定时器，300ms后触发更新
  debounceTimer = setTimeout(() => {
    emit('update', { languages: [...formData.languages] })
  }, 300)
}

/**
 * 添加语言
 */
const addLanguage = () => {
  formData.languages.push({
    name: '',
    level: '',
    certificate: '',
    description: ''
  })
  handleLanguageChange()
}

/**
 * 删除语言
 */
const removeLanguage = (index) => {
  formData.languages.splice(index, 1)
  handleLanguageChange()
}
</script>

<style scoped>
.language-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.languages-list {
  @apply space-y-4 mb-6;
}

.language-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-3;
}

.language-controls {
  @apply flex items-center gap-2;
}

.language-name-input {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.language-level-select {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
  min-width: 100px;
}

.delete-btn {
  @apply w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold;
}

.language-details {
  @apply space-y-2;
}

.certificate-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.description-textarea {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-xs text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}
</style> 