# Vue+JSON模板系统实现方案

## 概述

本方案实现了将简历模板从单一Vue文件扩展为Vue+JSON文件组合的架构，支持模板与内容的分离，提供更灵活的模板管理和内容定制能力。

## 架构设计

### 1. 核心思想

- **职责分离**：Vue文件负责样式和交互，JSON文件负责默认内容数据
- **灵活组合**：一个Vue模板可以配合多个JSON内容，支持按行业/职位定制
- **降级兼容**：如果模板没有JSON文件，系统会从全局默认内容库获取
- **用户优先**：用户编写的内容始终优先于模板默认内容

### 2. 文件结构

```
后端存储（MinIO/本地）：
templates/
├── modern/                    # 现代简约模板
│   ├── template.vue          # Vue模板文件
│   └── content.json          # 默认内容数据
├── classic/                   # 经典模板
│   ├── template.vue          # Vue模板文件
│   └── content.json          # 默认内容数据
└── content/                   # 全局内容库
    ├── template-content.json  # 全局默认内容
    ├── industries/            # 行业特定内容
    │   ├── tech.json
    │   ├── finance.json
    │   └── design.json
    └── positions/             # 职位特定内容
        ├── frontend.json
        └── backend.json
```

## 后端实现

### 1. DTO类更新

#### TemplateLoadResponse.java
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateLoadResponse {
    private Long templateId;
    private String templateCode;
    private String templateName;
    private String templateContent;      // Vue文件内容
    private String defaultContent;       // JSON默认内容
    private Boolean hasDefaultContent;   // 是否包含默认内容
    private String dataSource;
    private Long loadTime;
    // ... 其他字段
}
```

#### DefaultContentRequest.java
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DefaultContentRequest {
    @NotBlank(message = "模板类别不能为空")
    private String templateCategory;     // 必需
    private String industry;             // 可选
    private String position;             // 可选
    private String language;             // 可选，默认zh-CN
}
```

#### DefaultContentResponse.java
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DefaultContentResponse {
    private String defaultContent;      // JSON格式的默认内容
    private String contentSource;       // 内容来源
    private String templateCategory;
    private String industry;
    private String position;
    private String language;
    private LocalDateTime generateTime;
}
```

### 2. 服务层实现

#### IDefaultContentService.java
```java
public interface IDefaultContentService {
    DefaultContentResponse getDefaultContent(DefaultContentRequest request);
    boolean hasDefaultContent(String templateCategory, String industry, String position);
    List<String> getSupportedCategories();
    List<String> getSupportedIndustries();
    List<String> getSupportedPositions(String industry);
    boolean refreshContentCache();
}
```

#### DefaultContentServiceImpl.java
- 实现多级优先级匹配：模板专用 → 行业+职位 → 行业 → 类别 → 默认
- 支持内容缓存和动态刷新
- 提供丰富的内容管理功能

#### TemplateLoaderServiceImpl.java 更新
- 支持同时加载Vue文件和JSON文件
- 三层存储架构保持不变：Redis → MinIO → 本地
- 分别缓存Vue内容和JSON内容

### 3. 控制器更新

#### TemplateLoaderController.java
新增接口：
- `POST /template-loader/default-content` - 获取默认内容
- `GET /template-loader/categories` - 获取支持的模板类别
- `GET /template-loader/industries` - 获取支持的行业类型
- `GET /template-loader/positions` - 获取支持的职位类型
- `POST /template-loader/refresh-content-cache` - 刷新默认内容缓存
- `GET /template-loader/check-content` - 检查默认内容是否存在

## 前端实现

### 1. useEditorStore.js 简化

**重要变更：**
- 移除 `DEFAULT_MODULES` 中的 `data` 字段
- 简历数据初始化为空，由模板加载时填充
- 新增 `setResumeData()` 方法用于设置从后端加载的数据
- 新增 `loadResumeData()` 方法用于加载简历数据

**核心方法：**
```javascript
// 设置简历数据（用于模板加载后的数据填充）
const setResumeData = (resumeData, templateInfo = null) => {
  currentResumeData.value = resumeData
  if (templateInfo) {
    currentTemplate.value = templateInfo
  }
  // 保存到历史记录
  saveState({ resumeData, title: title.value, settings: settings.value })
}

// 加载简历数据（从后端加载）
const loadResumeData = async (resumeId, templateId = null) => {
  const resumeData = await fetchResume(resumeId)
  if (resumeData) {
    currentResumeData.value = resumeData
    title.value = resumeData.title || '我的简历'
  }
}
```

### 2. useTemplateLoader.js 增强

**新增功能：**
- 支持处理Vue+JSON组合响应
- 新增默认内容获取功能
- 支持完整的模板+内容加载流程

**核心方法：**
```javascript
// 加载模板（支持Vue+JSON组合）
const loadTemplate = async (options) => {
  const result = await callTemplateLoaderAPI(requestData)
  if (result.success && result.data) {
    const templateData = result.data
    setActiveTemplate(templateData)
    
    // 如果模板包含默认内容，设置默认内容
    if (templateData.hasDefaultContent && templateData.defaultContent) {
      currentDefaultContent.value = {
        defaultContent: templateData.defaultContent,
        contentSource: 'template_specific'
      }
    }
    
    return { template: templateData, defaultContent: currentDefaultContent.value }
  }
}

// 获取默认内容
const getDefaultContent = async (options) => {
  const result = await callDefaultContentAPI(requestData)
  if (result.success && result.data) {
    currentDefaultContent.value = result.data
    return result.data
  }
}

// 完整的模板+内容加载流程
const loadTemplateWithContent = async (templateOptions, contentOptions = null) => {
  // 首先加载模板
  const templateResult = await loadTemplate(templateOptions)
  
  // 如果模板已包含默认内容，直接返回
  if (templateResult.defaultContent) {
    return templateResult
  }
  
  // 如果模板没有默认内容且提供了内容选项，获取默认内容
  if (contentOptions) {
    const defaultContent = await getDefaultContent(contentOptions)
    return { template: templateResult.template, defaultContent }
  }
  
  return templateResult
}
```

## 数据流程

### 1. 首次加载模板

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant M as MinIO/存储

    U->>F: 选择模板
    F->>B: POST /template-loader/load
    B->>M: 加载Vue文件
    B->>M: 加载JSON文件（如果存在）
    M-->>B: 返回文件内容
    B-->>F: 返回{vue文件, json内容, hasDefaultContent}
    
    alt 模板有默认内容
        F->>F: 使用模板的JSON内容
    else 模板无默认内容
        F->>B: POST /template-loader/default-content
        B-->>F: 返回默认内容
    end
    
    F->>F: 渲染Vue模板 + 填充JSON内容
    F-->>U: 显示简历
```

### 2. 切换模板

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant E as EditorStore

    U->>F: 切换模板
    F->>U: 询问是否保留当前内容
    
    alt 保留当前内容
        F->>F: 加载新Vue模板
        F->>E: 使用当前用户数据
    else 不保留内容
        F->>F: 加载新Vue模板 + 新JSON内容
        F->>E: 使用新模板的默认数据
    end
    
    F-->>U: 显示新模板简历
```

## 优势分析

### 1. 架构优势

- **职责清晰**：Vue专注样式，JSON专注内容，各司其职
- **灵活扩展**：支持按行业/职位定制内容，满足不同需求
- **维护简单**：内容编辑无需代码知识，开发人员专注功能
- **性能优化**：支持分别缓存Vue和JSON，提高加载效率

### 2. 用户体验

- **个性化**：根据行业职位提供匹配的默认内容
- **选择自由**：切换模板时可选择保留或替换内容
- **降级保障**：即使模板没有专用内容，也能提供基础默认内容

### 3. 开发效率

- **内容管理**：非技术人员可直接编辑JSON内容
- **模板复用**：一个Vue模板可配合多个内容方案
- **版本控制**：Vue和JSON分离，便于独立维护和更新

## 示例文件

### 1. Vue模板文件示例

```vue
<template>
  <div class="resume-template modern-template">
    <div class="resume-header">
      <h1 class="name">{{ resumeData.basicInfo?.name || '姓名' }}</h1>
      <h2 class="title">{{ resumeData.basicInfo?.title || '职位标题' }}</h2>
      <!-- 其他内容... -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModernTemplate',
  props: {
    resumeData: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>

<style scoped>
/* 样式定义... */
</style>
```

### 2. JSON内容文件示例

```json
{
  "templateCategory": "modern",
  "templateName": "现代简约模板",
  "defaultContent": {
    "basicInfo": {
      "name": "张三",
      "title": "前端开发工程师",
      "phone": "13800138000",
      "email": "<EMAIL>"
    },
    "selfEvaluation": {
      "content": "专业的前端开发工程师..."
    },
    "workExperiences": [...],
    "educations": [...],
    "skills": [...]
  },
  "metadata": {
    "targetIndustries": ["互联网", "科技"],
    "targetPositions": ["前端开发", "全栈开发"],
    "language": "zh-CN"
  }
}
```

## 部署和配置

### 1. 后端配置

1. 确保MinIO或本地存储目录已配置
2. 创建模板目录结构
3. 上传示例模板文件
4. 配置缓存策略

### 2. 前端配置

1. 更新API配置，确保新接口路径正确
2. 测试模板加载和内容获取功能
3. 验证模板切换和内容保留逻辑

### 3. 测试验证

1. **模板加载测试**：验证Vue+JSON组合加载
2. **内容获取测试**：测试默认内容的多级匹配
3. **切换模板测试**：验证内容保留/替换逻辑
4. **缓存测试**：验证Redis缓存的正确性
5. **降级测试**：测试API失败时的模拟数据降级

## 总结

本方案成功实现了Vue+JSON模板系统，具有以下特点：

1. **架构清晰**：前后端职责分明，代码结构合理
2. **功能完整**：支持模板加载、内容获取、缓存管理等完整功能
3. **扩展性强**：支持多种内容定制方案，易于扩展新功能
4. **兼容性好**：保持现有API兼容，平滑升级
5. **用户友好**：提供丰富的个性化选项和良好的用户体验

该方案为简历编辑器提供了更强大和灵活的模板系统，为后续功能扩展奠定了坚实基础。 