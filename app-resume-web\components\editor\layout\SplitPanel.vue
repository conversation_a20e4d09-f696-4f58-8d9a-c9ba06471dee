<template>
  <div class="split-panel" ref="splitPanelRef">
    <!-- 左侧面板 -->
    <div 
      class="split-left"
      :style="{ width: `${leftWidth}%` }"
    >
      <slot name="left" />
    </div>

    <!-- 拖拽分割线 -->
    <div 
      class="split-divider"
      @mousedown="handleMouseDown"
      @touchstart="handleTouchStart"
    >
      <div class="divider-line">
        <div class="divider-handle">
          <div class="handle-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧面板 -->
    <div 
      class="split-right"
      :style="{ width: `${rightWidth}%` }"
    >
      <slot name="right" />
    </div>
  </div>
</template>

<script setup>
/**
 * 可拖拽分割面板组件
 * @description 提供左右分屏布局，支持拖拽调整面板大小
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, onMounted, onUnmounted } from 'vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 左侧面板宽度百分比
   */
  leftWidth: {
    type: Number,
    default: 50,
    validator: (value) => value >= 20 && value <= 80
  },

  /**
   * 最小左侧宽度百分比
   */
  minLeftWidth: {
    type: Number,
    default: 25
  },

  /**
   * 最大左侧宽度百分比
   */
  maxLeftWidth: {
    type: Number,
    default: 75
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['resize'])

// ================================
// 响应式数据
// ================================
const splitPanelRef = ref(null)
const isDragging = ref(false)
const startX = ref(0)
const startLeftWidth = ref(0)

// ================================
// 计算属性
// ================================

/**
 * 右侧面板宽度
 */
const rightWidth = computed(() => {
  return 100 - props.leftWidth
})

// ================================
// 生命周期
// ================================
onMounted(() => {
  // 添加全局事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.addEventListener('touchmove', handleTouchMove)
  document.addEventListener('touchend', handleTouchEnd)
})

onUnmounted(() => {
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
})

// ================================
// 方法定义
// ================================

/**
 * 处理鼠标按下事件
 * @param {MouseEvent} event - 鼠标事件
 */
const handleMouseDown = (event) => {
  event.preventDefault()
  startDrag(event.clientX)
}

/**
 * 处理触摸开始事件
 * @param {TouchEvent} event - 触摸事件
 */
const handleTouchStart = (event) => {
  event.preventDefault()
  const touch = event.touches[0]
  startDrag(touch.clientX)
}

/**
 * 开始拖拽
 * @param {number} clientX - 客户端X坐标
 */
const startDrag = (clientX) => {
  if (!splitPanelRef.value) return

  isDragging.value = true
  startX.value = clientX
  startLeftWidth.value = props.leftWidth

  // 添加拖拽样式
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

/**
 * 处理鼠标移动事件
 * @param {MouseEvent} event - 鼠标事件
 */
const handleMouseMove = (event) => {
  if (!isDragging.value) return
  handleDrag(event.clientX)
}

/**
 * 处理触摸移动事件
 * @param {TouchEvent} event - 触摸事件
 */
const handleTouchMove = (event) => {
  if (!isDragging.value) return
  const touch = event.touches[0]
  handleDrag(touch.clientX)
}

/**
 * 处理拖拽移动
 * @param {number} clientX - 客户端X坐标
 */
const handleDrag = (clientX) => {
  if (!splitPanelRef.value || !isDragging.value) return

  const containerRect = splitPanelRef.value.getBoundingClientRect()
  const containerWidth = containerRect.width
  const deltaX = clientX - startX.value
  const deltaPercent = (deltaX / containerWidth) * 100

  let newLeftWidth = startLeftWidth.value + deltaPercent

  // 限制在最小和最大宽度之间
  newLeftWidth = Math.max(props.minLeftWidth, Math.min(props.maxLeftWidth, newLeftWidth))

  // 发射调整事件
  emit('resize', newLeftWidth)
}

/**
 * 处理鼠标释放事件
 */
const handleMouseUp = () => {
  endDrag()
}

/**
 * 处理触摸结束事件
 */
const handleTouchEnd = () => {
  endDrag()
}

/**
 * 结束拖拽
 */
const endDrag = () => {
  if (!isDragging.value) return

  isDragging.value = false

  // 移除拖拽样式
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

/**
 * 重置面板大小
 */
const resetSize = () => {
  emit('resize', 50)
}

/**
 * 设置左侧面板为主要
 */
const setLeftPrimary = () => {
  emit('resize', 70)
}

/**
 * 设置右侧面板为主要
 */
const setRightPrimary = () => {
  emit('resize', 30)
}

// ================================
// 暴露方法给父组件
// ================================
defineExpose({
  resetSize,
  setLeftPrimary,
  setRightPrimary
})
</script>

<style scoped>
.split-panel {
  @apply flex h-full overflow-hidden;
}

.split-left,
.split-right {
  @apply overflow-hidden;
  transition: width 0.1s ease-out;
}

.split-divider {
  @apply relative flex-shrink-0 bg-gray-200 cursor-col-resize;
  width: 6px;
  transition: background-color 0.2s ease;
}

.split-divider:hover {
  @apply bg-gray-300;
}

.split-divider:active {
  @apply bg-primary-400;
}

.divider-line {
  @apply absolute inset-0 flex items-center justify-center;
}

.divider-handle {
  @apply w-1 h-8 bg-gray-400 rounded-full flex items-center justify-center opacity-0 transition-opacity duration-200;
}

.split-divider:hover .divider-handle {
  @apply opacity-100;
}

.handle-dots {
  @apply flex flex-col space-y-1;
}

.dot {
  @apply w-1 h-1 bg-white rounded-full;
}

/* 拖拽时的全局样式 */
.split-panel:global(.dragging) {
  @apply select-none;
}

.split-panel:global(.dragging) * {
  @apply pointer-events-none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .split-divider {
    width: 8px;
  }
  
  .divider-handle {
    @apply w-1.5 h-10;
  }
}

/* 动画效果 */
@keyframes divider-pulse {
  0%, 100% {
    @apply bg-gray-200;
  }
  50% {
    @apply bg-primary-300;
  }
}

.split-divider.active {
  animation: divider-pulse 1s ease-in-out infinite;
}
</style> 