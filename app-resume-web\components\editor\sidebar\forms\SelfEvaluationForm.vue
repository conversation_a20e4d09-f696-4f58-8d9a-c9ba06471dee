<template>
  <div class="self-evaluation-form">
    <div class="form-header">
      <h4 class="form-title">自我评价</h4>
      <p class="form-desc">简洁地描述您的个人特点和职业优势</p>
    </div>
    
    <div class="form-content">
      <RichTextEditor
        v-model="formData.content"
        placeholder="在这里写下您的自我评价..."
        min-height="200px"
        @update:modelValue="handleContentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ content: '' })
  }
})

const emit = defineEmits(['update'])

const formData = reactive({
  content: ''
})

let debounceTimer = null

watch(() => props.data, (newData) => {
  if (newData && newData.content) {
    formData.content = newData.content
  } else {
    formData.content = ''
  }
}, { immediate: true, deep: true })

const handleContentChange = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  debounceTimer = setTimeout(() => {
    emit('update', { content: formData.content })
  }, 300)
}
</script>

<style scoped>
.self-evaluation-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.form-content {
  @apply space-y-6;
}

.content-textarea {
  @apply w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none;
  min-height: 200px;
}

.tips-section {
  @apply bg-purple-50 rounded-lg p-4 border border-purple-200;
}

.tips-title {
  @apply text-sm font-medium text-purple-900 mb-3;
}

.tips-list {
  @apply space-y-2;
}

.tips-list li {
  @apply text-sm text-purple-800 flex items-start;
}

.tips-list li::before {
  @apply text-purple-600 mr-2 mt-0.5;
  content: '•';
}
</style> 