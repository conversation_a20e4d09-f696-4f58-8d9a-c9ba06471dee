package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 简历基本信息更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "简历基本信息更新请求")
public class ResumeBasicInfoRequest {

    /**
     * 头像URL
     */
    @Schema(description = "头像URL", example = "https://cdn.example.com/avatar.jpg")
    private String avatarUrl;

    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名", example = "张三")
    @Size(max = 50, message = "姓名长度不能超过50字符")
    private String realName;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "13800138000")
    @Size(max = 20, message = "电话号码长度不能超过20字符")
    private String phone;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100字符")
    private String email;

    /**
     * 居住地址
     */
    @Schema(description = "居住地址", example = "北京市朝阳区")
    @Size(max = 200, message = "地址长度不能超过200字符")
    private String address;

    /**
     * 出生日期
     */
    @Schema(description = "出生日期", example = "1995-01-01")
    private LocalDate birthday;

    /**
     * 性别（0:未知,1:男,2:女）
     */
    @Schema(description = "性别", example = "1")
    private Byte gender;

    /**
     * 求职意向
     */
    @Schema(description = "求职意向", example = "前端开发工程师")
    @Size(max = 100, message = "求职意向长度不能超过100字符")
    private String jobIntention;

    /**
     * 期望薪资
     */
    @Schema(description = "期望薪资", example = "20K-30K")
    @Size(max = 50, message = "期望薪资长度不能超过50字符")
    private String expectedSalary;

    /**
     * 工作年限
     */
    @Schema(description = "工作年限", example = "5")
    private Byte workYears;

    /**
     * 个人网站
     */
    @Schema(description = "个人网站", example = "https://zhangsan.dev")
    @Size(max = 200, message = "个人网站长度不能超过200字符")
    private String personalWebsite;

    /**
     * GitHub地址
     */
    @Schema(description = "GitHub地址", example = "https://github.com/zhangsan")
    @Size(max = 200, message = "GitHub地址长度不能超过200字符")
    private String githubUrl;
} 