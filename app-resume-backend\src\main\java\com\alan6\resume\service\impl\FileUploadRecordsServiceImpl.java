package com.alan6.resume.service.impl;

import com.alan6.resume.config.MinioConfig;
import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.file.*;
import com.alan6.resume.entity.FileUploadRecords;
import com.alan6.resume.mapper.FileUploadRecordsMapper;
import com.alan6.resume.service.IFileUploadRecordsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件上传记录表服务实现类
 * 
 * 主要功能：
 * 1. 实现文件上传和下载服务
 * 2. 集成MinIO对象存储
 * 3. 管理文件版本控制和预签名URL
 * 4. 处理存储桶管理功能
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadRecordsServiceImpl extends ServiceImpl<FileUploadRecordsMapper, FileUploadRecords> implements IFileUploadRecordsService {

    private final MinioClient minioClient;
    private final MinioConfig.MinioProperties minioProperties;

    /**
     * 文件类型对应的大小限制（字节）
     */
    private static final Map<String, Long> FILE_SIZE_LIMITS = Map.of(
            "avatar", 5L * 1024 * 1024,     // 5MB
            "resume", 10L * 1024 * 1024,    // 10MB
            "template", 20L * 1024 * 1024,  // 20MB
            "document", 50L * 1024 * 1024   // 50MB
    );

    /**
     * 文件类型对应的允许的MIME类型
     */
    private static final Map<String, Set<String>> ALLOWED_MIME_TYPES = Map.of(
            "avatar", Set.of("image/jpeg", "image/png", "image/gif"),
            "resume", Set.of("application/pdf", "application/msword", 
                           "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
            "template", Set.of("text/html", "application/json", "text/plain"),
            "document", Set.of("application/pdf", "application/msword", 
                             "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                             "text/plain", "image/jpeg", "image/png")
    );

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadResponse uploadFile(MultipartFile file, FileUploadRequest request, Long userId) {
        log.info("开始上传文件，用户ID: {}, 文件名: {}, 大小: {}", userId, file.getOriginalFilename(), file.getSize());
        
        try {
            // 参数验证
            if (!request.isValid()) {
                throw new BusinessException("请求参数无效");
            }
            
            if (!validateFile(file, request.getType())) {
                throw new BusinessException("文件验证失败");
            }
            
            // 生成文件名和路径
            String fileName = generateUniqueFileName(file.getOriginalFilename(), request.getType());
            String filePath = buildFilePath(request.getType(), fileName);
            String bucketName = request.getBucketName() != null ? request.getBucketName() : getBucketNameByFileType(request.getType());
            
            // 确保存储桶存在
            ensureBucketExists(bucketName);
            
            // 上传文件到MinIO
            ObjectWriteResponse response = minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(filePath)
                            .stream(file.getInputStream(), file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build()
            );
            
            // 生成文件URL
            String fileUrl = String.format("%s/%s/%s", minioProperties.getEndpoint(), bucketName, filePath);
            
            // 保存文件记录
            FileUploadRecords record = new FileUploadRecords();
            record.setId(System.currentTimeMillis()); // 使用时间戳作为ID
            record.setUserId(userId);
            record.setFileName(fileName);
            record.setOriginalName(file.getOriginalFilename());
            record.setFilePath(filePath);
            record.setFileUrl(fileUrl);
            record.setBucketName(bucketName);
            record.setObjectKey(filePath);
            record.setEtag(response.etag());
            record.setVersionId(response.versionId());
            record.setFileSize(file.getSize());
            record.setFileType(file.getContentType());
            record.setStorageClass(request.getDefaultStorageClass());
            record.setAccessPolicy(request.getDefaultAccessPolicy());
            record.setEncryptionType(request.getDefaultEncryptionType());
            record.setUploadPlatform("web");
            record.setIsDeleted((byte) 0);
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            
            boolean saveResult = save(record);
            if (!saveResult) {
                throw new BusinessException("文件记录保存失败");
            }
            
            log.info("文件上传成功，文件ID: {}, 文件名: {}", record.getId(), fileName);
            
            return FileUploadResponse.create(
                    String.valueOf(record.getId()),
                    fileName,
                    file.getOriginalFilename(),
                    filePath,
                    fileUrl,
                    bucketName,
                    filePath,
                    response.etag(),
                    response.versionId(),
                    file.getSize(),
                    file.getContentType(),
                    request.getDefaultStorageClass(),
                    request.getDefaultAccessPolicy(),
                    request.getDefaultEncryptionType(),
                    LocalDateTime.now()
            );
            
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public FileInfoResponse getFileInfo(String fileId, Long userId) {
        log.debug("获取文件信息，文件ID: {}, 用户ID: {}", fileId, userId);
        
        FileUploadRecords record = getById(fileId);
        if (record == null) {
            throw new BusinessException("文件不存在");
        }
        
        if (!checkFileAccess(fileId, userId)) {
            throw new BusinessException("无权限访问该文件");
        }
        
        // 构建响应
        return FileInfoResponse.builder()
                .fileId(String.valueOf(record.getId()))
                .fileName(record.getFileName())
                .originalName(record.getOriginalName())
                .filePath(record.getFilePath())
                .fileUrl(record.getFileUrl())
                .bucketName(record.getBucketName())
                .objectKey(record.getObjectKey())
                .etag(record.getEtag())
                .versionId(record.getVersionId())
                .fileSize(record.getFileSize())
                .fileType(record.getFileType())
                .storageClass(record.getStorageClass())
                .accessPolicy(record.getAccessPolicy())
                .contentEncoding(record.getContentEncoding())
                .cacheControl(record.getCacheControl())
                .expiresAt(record.getExpiresAt())
                .encryptionType(record.getEncryptionType())
                .uploadPlatform(record.getUploadPlatform())
                .createTime(record.getCreateTime())
                .updateTime(record.getUpdateTime())
                .build();
    }

    @Override
    public FileListResponse getFileList(String type, String bucketName, String fileType, 
                                       LocalDateTime startTime, LocalDateTime endTime, String keyword,
                                       Integer page, Integer size, Long userId) {
        log.debug("获取文件列表，用户ID: {}, 类型: {}, 页码: {}, 大小: {}", userId, type, page, size);
        
        // 构建查询条件
        LambdaQueryWrapper<FileUploadRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecords::getUserId, userId)
                    .eq(FileUploadRecords::getIsDeleted, 0);
        
        if (bucketName != null && !bucketName.trim().isEmpty()) {
            queryWrapper.eq(FileUploadRecords::getBucketName, bucketName);
        }
        
        if (fileType != null && !fileType.trim().isEmpty()) {
            queryWrapper.eq(FileUploadRecords::getFileType, fileType);
        }
        
        if (startTime != null) {
            queryWrapper.ge(FileUploadRecords::getCreateTime, startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le(FileUploadRecords::getCreateTime, endTime);
        }
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like(FileUploadRecords::getFileName, keyword)
                    .or()
                    .like(FileUploadRecords::getOriginalName, keyword));
        }
        
        queryWrapper.orderByDesc(FileUploadRecords::getCreateTime);
        
        // 执行分页查询
        Page<FileUploadRecords> pageRequest = new Page<>(page, size);
        IPage<FileUploadRecords> pageResult = page(pageRequest, queryWrapper);
        
        // 转换为响应对象
        List<FileListResponse.FileItem> fileItems = pageResult.getRecords().stream()
                .map(this::convertToFileItem)
                .collect(Collectors.toList());
        
        return FileListResponse.builder()
                .total(pageResult.getTotal())
                .page((int) pageResult.getCurrent())
                .size((int) pageResult.getSize())
                .pages((int) pageResult.getPages())
                .files(fileItems)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFile(String fileId, Boolean permanent, Long userId) {
        log.info("删除文件，文件ID: {}, 永久删除: {}, 用户ID: {}", fileId, permanent, userId);
        
        FileUploadRecords record = getById(fileId);
        if (record == null) {
            throw new BusinessException("文件不存在");
        }
        
        if (!checkFileAccess(fileId, userId)) {
            throw new BusinessException("无权限删除该文件");
        }
        
        try {
            if (permanent != null && permanent) {
                // 永久删除：从MinIO和数据库中删除
                minioClient.removeObject(
                        RemoveObjectArgs.builder()
                                .bucket(record.getBucketName())
                                .object(record.getObjectKey())
                                .build()
                );
                
                boolean deleteResult = removeById(fileId);
                log.info("文件永久删除完成，文件ID: {}, 结果: {}", fileId, deleteResult);
                return deleteResult;
            } else {
                // 逻辑删除：只标记为已删除
                record.setIsDeleted((byte) 1);
                record.setUpdateTime(LocalDateTime.now());
                
                boolean updateResult = updateById(record);
                log.info("文件逻辑删除完成，文件ID: {}, 结果: {}", fileId, updateResult);
                return updateResult;
            }
        } catch (Exception e) {
            log.error("文件删除失败，文件ID: {}", fileId, e);
            throw new BusinessException("文件删除失败: " + e.getMessage());
        }
    }

    @Override
    public PresignedUrlResponse generatePresignedUrl(PresignedUrlRequest request, Long userId) {
        log.debug("生成预签名URL，文件ID: {}, 用户ID: {}", request.getFileId(), userId);
        
        if (!request.isValid()) {
            throw new BusinessException("请求参数无效");
        }
        
        if (!checkFileAccess(request.getFileId(), userId)) {
            throw new BusinessException("无权限访问该文件");
        }
        
        FileUploadRecords record = getById(request.getFileId());
        if (record == null) {
            throw new BusinessException("文件不存在");
        }
        
        try {
            // 生成预签名URL
            String presignedUrl = minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.valueOf(request.getDefaultMethod()))
                            .bucket(record.getBucketName())
                            .object(record.getObjectKey())
                            .expiry(request.getDefaultExpiresIn())
                            .build()
            );
            
            LocalDateTime expiresAt = LocalDateTime.now().plusSeconds(request.getDefaultExpiresIn());
            
            log.debug("预签名URL生成成功，文件ID: {}, 过期时间: {}", request.getFileId(), expiresAt);
            
            return PresignedUrlResponse.create(
                    presignedUrl,
                    request.getDefaultMethod(),
                    expiresAt,
                    request.getDefaultExpiresIn(),
                    request.getFileId(),
                    record.getFileName()
            );
            
        } catch (Exception e) {
            log.error("预签名URL生成失败，文件ID: {}", request.getFileId(), e);
            throw new BusinessException("预签名URL生成失败: " + e.getMessage());
        }
    }

    @Override
    public FileVersionListResponse getFileVersions(String fileId, Integer page, Integer size, Long userId) {
        log.debug("获取文件版本列表，文件ID: {}, 用户ID: {}", fileId, userId);
        
        if (!checkFileAccess(fileId, userId)) {
            throw new BusinessException("无权限访问该文件");
        }
        
        FileUploadRecords record = getById(fileId);
        if (record == null) {
            throw new BusinessException("文件不存在");
        }
        
        // 简化实现：只返回当前版本
        // 实际场景中需要实现完整的版本管理
        FileVersionListResponse.FileVersion currentVersion = FileVersionListResponse.FileVersion.builder()
                .versionId(record.getVersionId())
                .etag(record.getEtag())
                .fileSize(record.getFileSize())
                .isLatest(true)
                .storageClass(record.getStorageClass())
                .createTime(record.getCreateTime())
                .lastModified(record.getUpdateTime())
                .versionTag("current")
                .description("当前版本")
                .build();
        
        return FileVersionListResponse.builder()
                .total(1L)
                .page(page)
                .size(size)
                .pages(1)
                .fileId(fileId)
                .versions(List.of(currentVersion))
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileVersionRestoreResponse restoreFileVersion(String fileId, String versionId, Long userId) {
        log.info("恢复文件版本，文件ID: {}, 版本ID: {}, 用户ID: {}", fileId, versionId, userId);
        
        if (!checkFileAccess(fileId, userId)) {
            throw new BusinessException("无权限操作该文件");
        }
        
        FileUploadRecords record = getById(fileId);
        if (record == null) {
            throw new BusinessException("文件不存在");
        }
        
        // 简化实现：返回成功响应
        // 实际场景中需要实现完整的版本恢复逻辑
        return FileVersionRestoreResponse.success(
                fileId,
                versionId,
                record.getVersionId(),
                record.getVersionId(),
                userId,
                "系统用户"
        );
    }

    @Override
    public BucketListResponse getBucketList(Long userId) {
        log.debug("获取存储桶列表，用户ID: {}", userId);
        
        try {
            List<Bucket> buckets = minioClient.listBuckets();
            
            List<BucketListResponse.BucketInfo> bucketInfos = buckets.stream()
                    .map(this::convertToBucketInfo)
                    .collect(Collectors.toList());
            
            return BucketListResponse.builder()
                    .buckets(bucketInfos)
                    .build();
            
        } catch (Exception e) {
            log.error("获取存储桶列表失败", e);
            throw new BusinessException("获取存储桶列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BucketCreateResponse createBucket(BucketCreateRequest request, Long userId) {
        log.info("创建存储桶，名称: {}, 用户ID: {}", request.getName(), userId);
        
        if (!request.isValid()) {
            throw new BusinessException("请求参数无效");
        }
        
        String bucketName = request.getNormalizedName();
        if (bucketName == null) {
            throw new BusinessException("存储桶名称格式不正确");
        }
        
        try {
            // 检查存储桶是否已存在
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (exists) {
                throw new BusinessException("存储桶已存在");
            }
            
            // 创建存储桶
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            
            String bucketUrl = String.format("%s/%s", minioProperties.getEndpoint(), bucketName);
            
            log.info("存储桶创建成功，名称: {}, URL: {}", bucketName, bucketUrl);
            
            return BucketCreateResponse.success(request, bucketUrl, userId);
            
        } catch (Exception e) {
            log.error("存储桶创建失败，名称: {}", bucketName, e);
            return BucketCreateResponse.failure(request, "存储桶创建失败: " + e.getMessage(), userId);
        }
    }

    @Override
    public byte[] downloadFile(String fileId, Boolean download, String filename, Long userId) {
        log.debug("下载文件，文件ID: {}, 用户ID: {}", fileId, userId);
        
        if (!checkFileAccess(fileId, userId)) {
            throw new BusinessException("无权限下载该文件");
        }
        
        FileUploadRecords record = getById(fileId);
        if (record == null) {
            throw new BusinessException("文件不存在");
        }
        
        try {
            GetObjectResponse response = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(record.getBucketName())
                            .object(record.getObjectKey())
                            .build()
            );
            
            return response.readAllBytes();
            
        } catch (Exception e) {
            log.error("文件下载失败，文件ID: {}", fileId, e);
            throw new BusinessException("文件下载失败: " + e.getMessage());
        }
    }

    @Override
    public boolean checkFileAccess(String fileId, Long userId) {
        FileUploadRecords record = getById(fileId);
        if (record == null) {
            return false;
        }
        
        // 检查文件归属权
        return Objects.equals(record.getUserId(), userId);
    }

    @Override
    public String generateUniqueFileName(String originalFileName, String fileType) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String randomSuffix = UUID.randomUUID().toString().substring(0, 8);
        
        String extension = "";
        if (originalFileName != null && originalFileName.contains(".")) {
            extension = originalFileName.substring(originalFileName.lastIndexOf("."));
        }
        
        return String.format("%s_%s_%s%s", fileType, timestamp, randomSuffix, extension);
    }

    @Override
    public boolean validateFile(MultipartFile file, String fileType) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        // 检查文件大小
        Long sizeLimit = FILE_SIZE_LIMITS.get(fileType);
        if (sizeLimit != null && file.getSize() > sizeLimit) {
            return false;
        }
        
        // 检查文件类型
        Set<String> allowedTypes = ALLOWED_MIME_TYPES.get(fileType);
        if (allowedTypes != null && !allowedTypes.contains(file.getContentType())) {
            return false;
        }
        
        return true;
    }

    @Override
    public String getBucketNameByFileType(String fileType) {
        if (fileType == null) {
            return minioProperties.getBucketName();
        }
        
        return switch (fileType) {
            case "avatar" -> "avatar-bucket";
            case "resume" -> "resume-bucket";
            case "template" -> "template-bucket";
            case "document" -> "document-bucket";
            default -> minioProperties.getBucketName();
        };
    }

    @Override
    public String buildFilePath(String fileType, String fileName) {
        LocalDateTime now = LocalDateTime.now();
        String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return String.format("%s/%s/%s", fileType, datePath, fileName);
    }

    /**
     * 确保存储桶存在
     * 
     * @param bucketName 存储桶名称
     */
    private void ensureBucketExists(String bucketName) {
        try {
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!exists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("自动创建存储桶: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("检查或创建存储桶失败: {}", bucketName, e);
            throw new BusinessException("存储桶操作失败: " + e.getMessage());
        }
    }

    /**
     * 转换为文件项
     * 
     * @param record 文件记录
     * @return 文件项
     */
    private FileListResponse.FileItem convertToFileItem(FileUploadRecords record) {
        return FileListResponse.FileItem.builder()
                .fileId(String.valueOf(record.getId()))
                .fileName(record.getFileName())
                .originalName(record.getOriginalName())
                .fileUrl(record.getFileUrl())
                .fileSize(record.getFileSize())
                .fileType(record.getFileType())
                .bucketName(record.getBucketName())
                .accessPolicy(record.getAccessPolicy())
                .storageClass(record.getStorageClass())
                .createTime(record.getCreateTime())
                .build();
    }

    /**
     * 转换为存储桶信息
     * 
     * @param bucket MinIO存储桶
     * @return 存储桶信息
     */
    private BucketListResponse.BucketInfo convertToBucketInfo(Bucket bucket) {
        return BucketListResponse.BucketInfo.builder()
                .name(bucket.name())
                .description("MinIO存储桶")
                .region("us-east-1")
                .versioning(true)
                .encryption("SSE-S3")
                .storageClass("STANDARD")
                .accessPolicy("private")
                .objectCount(0L)
                .totalSize(0L)
                .createTime(bucket.creationDate() != null ? 
                           bucket.creationDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() :
                           LocalDateTime.now())
                .lastModified(LocalDateTime.now())
                .build();
    }
}
