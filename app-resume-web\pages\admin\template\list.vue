<template>
  <div class="template-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">模板列表</h1>
          <p class="page-description">管理所有已上传的简历模板</p>
        </div>
        <div class="header-right">
          <NuxtLink to="/admin/template/upload" class="btn-primary">
            ➕ 上传模板
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-filters">
        <!-- 搜索框 -->
        <div class="search-box">
          <input
            v-model="searchForm.keyword"
            type="text"
            placeholder="搜索模板名称、代码、描述..."
            class="search-input"
            @keyup.enter="handleSearch"
          />
          <button @click="handleSearch" class="search-btn">🔍</button>
        </div>

        <!-- 筛选器 -->
        <div class="filter-controls">
          <!-- 分类筛选 -->
          <select v-model="searchForm.categoryId" @change="handleSearch" class="filter-select">
            <option value="">全部分类</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>

          <!-- 类型筛选 -->
          <select v-model="searchForm.isPremium" @change="handleSearch" class="filter-select">
            <option value="">全部类型</option>
            <option value="0">免费模板</option>
            <option value="1">付费模板</option>
          </select>

          <!-- 状态筛选 -->
          <select v-model="searchForm.isDeleted" @change="handleSearch" class="filter-select">
            <option value="">全部状态</option>
            <option value="0">正常模板</option>
            <option value="1">已删除</option>
          </select>

          <!-- 排序 -->
          <select v-model="searchForm.sortBy" @change="handleSearch" class="filter-select">
            <option value="default">默认排序</option>
            <option value="latest">最新创建</option>
            <option value="popular">使用次数</option>
            <option value="rating">评分排序</option>
          </select>

          <!-- 重置按钮 -->
          <button @click="resetFilters" class="reset-btn">🔄 重置</button>
        </div>
      </div>
    </div>

    <!-- 模板列表内容 -->
    <div class="list-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载模板列表...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">⚠️</div>
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button @click="loadTemplates" class="retry-btn">重试</button>
      </div>

      <!-- 空状态 -->
      <div v-else-if="isEmpty" class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>暂无模板</h3>
        <p>还没有上传任何模板，点击上传按钮开始添加模板</p>
        <NuxtLink to="/admin/template/upload" class="btn-primary">
          ➕ 上传模板
        </NuxtLink>
      </div>

      <!-- 模板列表 -->
      <div v-else class="template-list">
        <!-- 列表头部信息 -->
        <div class="list-header">
          <div class="list-info">
            <span class="total-count">共 {{ total }} 个模板</span>
            <span class="current-page">第 {{ currentPage }} / {{ totalPages }} 页</span>
          </div>
          <div class="view-controls">
            <button
              :class="['view-btn', { active: viewMode === 'list' }]"
              @click="viewMode = 'list'"
            >
              📋 列表
            </button>
            <button
              :class="['view-btn', { active: viewMode === 'grid' }]"
              @click="viewMode = 'grid'"
            >
              🔲 网格
            </button>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="list-view">
          <div class="list-table">
            <div class="table-header">
              <div class="col-preview">预览</div>
              <div class="col-name">模板名称</div>
              <div class="col-code">模板代码</div>
              <div class="col-category">分类</div>
              <div class="col-type">类型</div>
              <div class="col-stats">统计</div>
              <div class="col-status">状态</div>
              <div class="col-time">创建时间</div>
              <div class="col-actions">操作</div>
            </div>

            <div class="table-body">
              <div
                v-for="template in templates"
                :key="template.id"
                class="table-row"
              >
                <div class="col-preview">
                  <div v-if="!template.previewImageUrl" class="preview-placeholder-small">
                    📄
                  </div>
                  <img
                    v-else
                    :src="getPreviewImageUrl(template)"
                    :alt="template.name"
                    class="preview-thumb"
                    @error="handleImageError($event, template)"
                    @load="handleImageLoad($event, template)"
                  />
                </div>
                <div class="col-name">
                  <div class="template-name">{{ template.name }}</div>
                  <div class="template-desc">{{ template.description }}</div>
                </div>
                <div class="col-code">
                  <code class="template-code">{{ template.templateCode }}</code>
                </div>
                <div class="col-category">
                  {{ template.categoryName || '未分类' }}
                </div>
                <div class="col-type">
                  <span :class="['type-badge', template.isPremium ? 'premium' : 'free']">
                    {{ template.isPremium ? '付费' : '免费' }}
                  </span>
                  <span v-if="template.isPremium" class="price">¥{{ template.price }}</span>
                </div>
                <div class="col-stats">
                  <div class="stat-line">👁️ {{ template.useCount || 0 }}</div>
                  <div class="stat-line">⭐ {{ template.rating || 0 }}</div>
                </div>
                <div class="col-status">
                  <span v-if="template.isDeleted === 1" class="status-indicator deleted">
                    已删除
                  </span>
                  <span v-else :class="['status-indicator', template.status === 1 ? 'enabled' : 'disabled']">
                    {{ template.status === 1 ? '启用' : '禁用' }}
                  </span>
                </div>
                <div class="col-time">
                  {{ formatDateTime(template.createTime) }}
                </div>
                <div class="col-actions">
                  <div class="action-buttons">
                    <button @click="previewTemplate(template)" class="action-btn preview-btn" title="预览">
                      👁️
                    </button>
                    <button @click="editTemplate(template)" class="action-btn edit-btn" title="编辑">
                      ✏️
                    </button>
                    <button
                      v-if="template.isDeleted !== 1"
                      @click="toggleTemplateStatus(template)"
                      :class="['action-btn', 'status-btn', template.status === 1 ? 'disable' : 'enable']"
                      :title="template.status === 1 ? '禁用' : '启用'"
                    >
                      {{ template.status === 1 ? '⏸️' : '▶️' }}
                    </button>
                    <button 
                      v-if="template.isDeleted !== 1"
                      @click="deleteTemplate(template)" 
                      class="action-btn delete-btn" 
                      title="删除"
                    >
                      🗑️
                    </button>
                    <button 
                      v-if="template.isDeleted === 1"
                      @click="restoreTemplate(template)" 
                      class="action-btn restore-btn" 
                      title="恢复"
                    >
                      ↩️
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-else-if="viewMode === 'grid'" class="grid-view">
          <div
            v-for="template in templates"
            :key="template.id"
            class="template-card"
          >
            <!-- 预览图 -->
            <div class="template-preview">
              <div v-if="!template.previewImageUrl" class="preview-placeholder">
                <div class="placeholder-icon">📄</div>
                <div class="placeholder-text">暂无预览</div>
              </div>
              <img
                v-else
                :src="getPreviewImageUrl(template)"
                :alt="template.name"
                class="preview-image"
                @error="handleImageError($event, template)"
                @load="handleImageLoad($event, template)"
              />
              <div class="template-overlay">
                <div class="overlay-actions">
                  <button @click="previewTemplate(template)" class="action-btn preview-btn">
                    👁️ 预览
                  </button>
                  <button @click="editTemplate(template)" class="action-btn edit-btn">
                    ✏️ 编辑
                  </button>
                </div>
              </div>
            </div>

            <!-- 模板信息 -->
            <div class="template-info">
              <div class="template-header">
                <h3 class="template-name">{{ template.name }}</h3>
                <div class="template-badges">
                  <span v-if="template.isDeleted === 1" class="badge deleted">已删除</span>
                  <span v-if="template.isPremium" class="badge premium">付费</span>
                  <span v-else class="badge free">免费</span>
                  <span v-if="template.isHot" class="badge hot">热门</span>
                </div>
              </div>

              <div class="template-meta">
                <span class="template-code">{{ template.templateCode }}</span>
                <span class="template-category">{{ template.categoryName || '未分类' }}</span>
              </div>

              <div class="template-stats">
                <div class="stat-item">
                  <span>👁️ {{ template.useCount || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span>⭐ {{ template.rating || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span>🕒 {{ formatDateTime(template.createTime) }}</span>
                </div>
              </div>

              <div class="template-actions">
                <button
                  v-if="template.isDeleted !== 1"
                  @click="toggleTemplateStatus(template)"
                  :class="['status-btn', template.status === 1 ? 'enabled' : 'disabled']"
                >
                  {{ template.status === 1 ? '✅ 已启用' : '⏸️ 已禁用' }}
                </button>
                <button 
                  v-if="template.isDeleted !== 1"
                  @click="deleteTemplate(template)" 
                  class="delete-btn"
                >
                  🗑️ 删除
                </button>
                <button 
                  v-if="template.isDeleted === 1"
                  @click="restoreTemplate(template)" 
                  class="restore-btn"
                >
                  ↩️ 恢复
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页器 -->
        <div class="pagination">
          <button
            @click="loadPage(currentPage - 1)"
            :disabled="currentPage <= 1"
            class="page-btn"
          >
            ⬅️ 上一页
          </button>

          <div class="page-numbers">
            <button
              v-for="page in visiblePages"
              :key="page"
              @click="loadPage(page)"
              :class="['page-number', { active: page === currentPage }]"
            >
              {{ page }}
            </button>
          </div>

          <button
            @click="loadPage(currentPage + 1)"
            :disabled="currentPage >= totalPages"
            class="page-btn"
          >
            下一页 ➡️
          </button>

          <div class="page-info">
            <span>每页</span>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option :value="10">10</option>
              <option :value="20">20</option>
              <option :value="50">50</option>
            </select>
            <span>条</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认删除弹窗 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="cancelDelete">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button @click="cancelDelete" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <p>确定要删除模板 <strong>{{ templateToDelete?.name }}</strong> 吗？</p>
          <p class="warning-text">此操作不可恢复，请谨慎操作。</p>
        </div>
        <div class="modal-actions">
          <button @click="cancelDelete" class="btn-secondary">取消</button>
          <button @click="confirmDelete" class="btn-danger" :disabled="deleting">
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 模板预览弹窗 -->
    <div v-if="showPreviewModal" class="modal-overlay" @click="closePreview">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h3>模板预览 - {{ previewTemplate?.name }}</h3>
          <button @click="closePreview" class="close-btn">×</button>
        </div>
        <div class="modal-body preview-body">
          <div v-if="previewLoading" class="preview-loading">
            <div class="loading-spinner"></div>
            <p>正在加载预览...</p>
          </div>
          <div v-else-if="previewError" class="preview-error">
            <div class="error-icon">⚠️</div>
            <p>预览加载失败: {{ previewError }}</p>
            <button @click="retryPreview" class="retry-btn">重试</button>
          </div>
          <div v-else class="preview-container">
            <div class="preview-info">
              <div class="info-row">
                <span class="info-label">模板代码:</span>
                <code>{{ currentPreviewTemplate?.templateCode }}</code>
              </div>
              <div class="info-row">
                <span class="info-label">模板描述:</span>
                <span>{{ currentPreviewTemplate?.description || '暂无描述' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">分类:</span>
                <span>{{ currentPreviewTemplate?.categoryName || '未分类' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">类型:</span>
                <span :class="['type-badge', currentPreviewTemplate?.isPremium ? 'premium' : 'free']">
                  {{ currentPreviewTemplate?.isPremium ? '付费' : '免费' }}
                  <span v-if="currentPreviewTemplate?.isPremium" class="price">¥{{ currentPreviewTemplate?.price }}</span>
                </span>
              </div>
            </div>
            <div class="preview-image-container">
              <img 
                v-if="previewUrl"
                :src="previewUrl"
                class="preview-image"
                @error="handlePreviewImageError"
                @load="handlePreviewImageLoad"
                alt="模板预览图"
              />
              <div v-else class="no-preview">
                <div class="no-preview-icon">📄</div>
                <p>暂无预览</p>
              </div>
              <div v-if="imageLoading" class="image-loading">
                <div class="loading-spinner"></div>
                <p>加载中...</p>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-actions">
          <button @click="editTemplate(currentPreviewTemplate)" class="btn-primary">
            ✏️ 编辑模板
          </button>
          <button @click="closePreview" class="btn-secondary">关闭</button>
        </div>
      </div>
    </div>

    <!-- 模板编辑弹窗 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEdit">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h3>编辑模板 - {{ editingTemplate?.name }}</h3>
          <button @click="closeEdit" class="close-btn">×</button>
        </div>
        <div class="modal-body edit-body">
          <form @submit.prevent="saveTemplate" class="edit-form">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label required">模板名称</label>
                <input 
                  v-model="editForm.name"
                  type="text"
                  class="form-input"
                  placeholder="请输入模板名称"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label required">模板代码</label>
                <input 
                  v-model="editForm.templateCode"
                  type="text"
                  class="form-input"
                  placeholder="请输入模板代码"
                  :disabled="true"
                  title="模板代码不可修改"
                />
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">模板描述</label>
              <textarea 
                v-model="editForm.description"
                class="form-textarea"
                rows="3"
                placeholder="请输入模板描述"
              ></textarea>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">模板类型</label>
                <select v-model="editForm.isPremium" class="form-select">
                  <option :value="0">免费模板</option>
                  <option :value="1">付费模板</option>
                </select>
              </div>
              <div v-if="editForm.isPremium === 1" class="form-group">
                <label class="form-label required">模板价格</label>
                <input 
                  v-model="editForm.price"
                  type="number"
                  step="0.01"
                  min="0"
                  class="form-input"
                  placeholder="请输入价格（元）"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">排序权重</label>
                <input 
                  v-model="editForm.sortOrder"
                  type="number"
                  min="0"
                  class="form-input"
                  placeholder="数值越大排序越靠前"
                />
              </div>
              <div class="form-group">
                <label class="form-label">状态</label>
                <select v-model="editForm.status" class="form-select">
                  <option :value="1">启用</option>
                  <option :value="0">禁用</option>
                </select>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-actions">
          <button @click="saveTemplate" class="btn-primary" :disabled="saving">
            {{ saving ? '保存中...' : '保存' }}
          </button>
          <button @click="closeEdit" class="btn-secondary">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useTemplateListService } from '~/composables/admin/useTemplateListService'

// 页面元数据
definePageMeta({
  title: '模板列表 - 管理后台',
  layout: 'admin'
})

// ================================
// Composables 和服务
// ================================

const templateListService = useTemplateListService()
const {
  loading,
  error,
  templates,
  categories,
  pagination,
  filters,
  hasMore,
  isEmpty,
  fetchTemplates,
  fetchCategories,
  deleteTemplate: deleteTemplateService,
  updateTemplateStatus,
  filterTemplates,
  searchTemplates,
  loadPage
} = templateListService

// ================================
// 响应式数据
// ================================

// 搜索表单
const searchForm = ref({
  keyword: '',
  categoryId: '',
  isPremium: '',
  isDeleted: '',
  sortBy: 'default'
})

// 视图模式
const viewMode = ref('list') // 'grid' | 'list'

// 删除确认弹窗
const showDeleteModal = ref(false)
const templateToDelete = ref(null)
const deleting = ref(false)

// 预览弹窗
const showPreviewModal = ref(false)
const currentPreviewTemplate = ref(null)
const previewLoading = ref(false)
const previewError = ref('')
const previewUrl = ref('')
const imageLoading = ref(false)

// 编辑弹窗
const showEditModal = ref(false)
const editingTemplate = ref(null)
const saving = ref(false)
const editForm = ref({
  id: null,
  name: '',
  templateCode: '',
  description: '',
  isPremium: 0,
  price: null,
  sortOrder: 0,
  status: 1
})

// ================================
// 计算属性
// ================================

// 总数
const total = computed(() => pagination.total)

// 当前页
const currentPage = computed(() => pagination.current)

// 每页大小
const pageSize = ref(10)

// 总页数
const totalPages = computed(() => pagination.pages)

// 可见页码
const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// ================================
// 方法定义
// ================================



/**
 * 加载模板列表
 * @description 根据当前搜索条件加载模板数据
 */
const loadTemplates = async () => {
  try {
    // 同步搜索表单到filters
    Object.assign(filters, searchForm.value)
    await fetchTemplates()
    

  } catch (err) {
    console.error('加载模板列表失败:', err)
  }
}

/**
 * 加载分类列表
 * @description 获取所有模板分类
 */
const loadCategories = async () => {
  try {
    const result = await fetchCategories()
    if (!result.success) {
      throw new Error(result.error)
    }
  } catch (err) {
    console.error('加载分类列表失败:', err)
  }
}

/**
 * 处理搜索
 * @description 执行搜索并重置到第一页
 */
const handleSearch = () => {
  // 同步所有筛选条件到filters
  Object.assign(filters, searchForm.value)
  pagination.current = 1
  loadTemplates()
}

/**
 * 重置筛选条件
 * @description 清空所有筛选条件并重新加载
 */
const resetFilters = () => {
  searchForm.value = {
    keyword: '',
    categoryId: '',
    isPremium: '',
    isDeleted: '',
    sortBy: 'default'
  }
  // 同步重置filters
  Object.assign(filters, searchForm.value)
  pagination.current = 1
  loadTemplates()
}

/**
 * 处理页面大小变化
 * @description 改变每页显示数量
 */
const handlePageSizeChange = () => {
  pagination.size = parseInt(pageSize.value)
  pagination.current = 1
  loadTemplates()
}

/**
 * 预览模板
 * @description 在弹窗中显示模板预览
 * @param {Object} template - 模板对象
 */
const previewTemplate = async (template) => {
  if (!template) {
    showMessage('模板数据不完整', 'error')
    return
  }

  try {
    currentPreviewTemplate.value = template
    showPreviewModal.value = true
    await loadTemplatePreview(template)
  } catch (error) {
    console.error('预览模板失败:', error)
    showMessage('打开预览失败', 'error')
  }
}

/**
 * 加载模板预览
 * @description 获取模板预览URL
 * @param {Object} template - 模板对象
 */
const loadTemplatePreview = async (template) => {
  if (!template) {
    previewError.value = '模板数据不完整'
    return
  }

  try {
    previewLoading.value = true
    previewError.value = ''
    imageLoading.value = true
    
    // 调用后端API获取模板预览URL
    const response = await $fetch(`/api/admin/template/${template.id}/preview`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.code === 200 && response.data) {
      previewUrl.value = response.data.previewUrl
    } else {
      throw new Error(response.msg || '获取预览失败')
    }
  } catch (error) {
    console.error('加载模板预览失败:', error)
    
    let errorMessage = '加载预览失败'
    if (error.statusCode === 404) {
      errorMessage = '模板文件不存在'
    } else if (error.statusCode === 401) {
      errorMessage = '登录已过期，请重新登录'
    } else if (error.statusCode === 403) {
      errorMessage = '没有权限查看此模板'
    } else if (error.statusCode >= 500) {
      errorMessage = '服务器错误，请稍后重试'
    } else if (error.message) {
      errorMessage = error.message
    }
    
    previewError.value = errorMessage
  } finally {
    previewLoading.value = false
  }
}

/**
 * 重试预览
 */
const retryPreview = () => {
  if (currentPreviewTemplate.value) {
    loadTemplatePreview(currentPreviewTemplate.value)
  }
}

/**
 * 关闭预览弹窗
 */
const closePreview = () => {
  showPreviewModal.value = false
  currentPreviewTemplate.value = null
  previewUrl.value = ''
  previewError.value = ''
  imageLoading.value = false
}

/**
 * 处理预览模态框图片加载成功
 */
const handlePreviewImageLoad = () => {
  imageLoading.value = false
  console.log('预览图片加载成功')
}

/**
 * 处理预览模态框图片加载错误
 */
const handlePreviewImageError = () => {
  imageLoading.value = false
  console.error('预览图片加载失败')
  previewError.value = '预览图片加载失败'
}

/**
 * 编辑模板
 * @description 在弹窗中编辑模板信息
 * @param {Object} template - 模板对象
 */
const editTemplate = (template) => {
  editingTemplate.value = template
  
  // 填充编辑表单
  Object.assign(editForm.value, {
    id: template.id,
    name: template.name,
    templateCode: template.templateCode,
    description: template.description || '',
    isPremium: template.isPremium,
    price: template.price,
    sortOrder: template.sortOrder || 0,
    status: template.status
  })
  
  showEditModal.value = true
  
  // 如果是从预览弹窗打开的编辑，先关闭预览
  if (showPreviewModal.value) {
    closePreview()
  }
}

/**
 * 保存模板
 * @description 保存编辑后的模板信息
 */
const saveTemplate = async () => {
  // 表单验证
  if (!editForm.value.name?.trim()) {
    showMessage('模板名称不能为空', 'error')
    return
  }

  if (!editForm.value.templateCode?.trim()) {
    showMessage('模板代码不能为空', 'error')
    return
  }

  if (editForm.value.isPremium === 1 && (!editForm.value.price || editForm.value.price <= 0)) {
    showMessage('付费模板必须设置有效价格', 'error')
    return
  }

  try {
    saving.value = true
    
    const response = await $fetch(`/api/admin/template/${editForm.value.id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      },
      body: {
        name: editForm.value.name.trim(),
        templateCode: editForm.value.templateCode.trim(),
        description: editForm.value.description?.trim() || '',
        isPremium: editForm.value.isPremium,
        price: editForm.value.isPremium === 1 ? editForm.value.price : null,
        sortOrder: editForm.value.sortOrder || 0,
        status: editForm.value.status
      }
    })

    if (response.code === 200) {
      // 更新本地模板数据
      const templateIndex = templates.value.findIndex(t => t.id === editForm.value.id)
      if (templateIndex !== -1) {
        Object.assign(templates.value[templateIndex], {
          name: editForm.value.name,
          templateCode: editForm.value.templateCode,
          description: editForm.value.description,
          isPremium: editForm.value.isPremium,
          price: editForm.value.price,
          sortOrder: editForm.value.sortOrder,
          status: editForm.value.status,
          updateTime: new Date().toISOString()
        })
      }
      
      closeEdit()
      showMessage('模板更新成功', 'success')
    } else {
      throw new Error(response.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存模板失败:', error)
    
    let errorMessage = '保存失败'
    if (error.message) {
      errorMessage = error.message
    } else if (error.data?.msg) {
      errorMessage = error.data.msg
    } else if (error.statusCode === 401) {
      errorMessage = '登录已过期，请重新登录'
    } else if (error.statusCode === 403) {
      errorMessage = '没有权限执行此操作'
    } else if (error.statusCode >= 500) {
      errorMessage = '服务器错误，请稍后重试'
    }
    
    showMessage(errorMessage, 'error')
  } finally {
    saving.value = false
  }
}

/**
 * 关闭编辑弹窗
 */
const closeEdit = () => {
  showEditModal.value = false
  editingTemplate.value = null
  
  // 重置表单
  Object.assign(editForm.value, {
    id: null,
    name: '',
    templateCode: '',
    description: '',
    isPremium: 0,
    price: null,
    sortOrder: 0,
    status: 1
  })
}

/**
 * 显示消息提示
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型: success, error, warning, info
 */
const showMessage = (message, type = 'info') => {
  // 这里可以使用element-plus的message组件或自定义消息提示
  console.log(`[${type.toUpperCase()}] ${message}`)
  // 如果项目中有全局消息组件，可以在这里调用
}

/**
 * 切换模板状态
 * @description 启用或禁用模板
 * @param {Object} template - 模板对象
 */
const toggleTemplateStatus = async (template) => {
  try {
    const newStatus = template.status === 1 ? 0 : 1
    const result = await updateTemplateStatus(template.id, newStatus)
    
    if (result.success) {
      console.log(`模板 ${template.name} 状态已更新为: ${newStatus === 1 ? '启用' : '禁用'}`)
    } else {
      throw new Error(result.error)
    }
  } catch (err) {
    console.error('更新模板状态失败:', err)
    alert('更新模板状态失败: ' + err.message)
  }
}

/**
 * 删除模板
 * @description 显示删除确认弹窗
 * @param {Object} template - 模板对象
 */
const deleteTemplate = (template) => {
  templateToDelete.value = template
  showDeleteModal.value = true
}

/**
 * 确认删除
 * @description 执行删除操作
 */
const confirmDelete = async () => {
  if (!templateToDelete.value) return
  
  deleting.value = true
  try {
    console.log('🗑️ 准备删除模板:', {
      id: templateToDelete.value.id,
      name: templateToDelete.value.name,
      templateCode: templateToDelete.value.templateCode,
      isDeleted: templateToDelete.value.isDeleted
    })
    
    const result = await deleteTemplateService(templateToDelete.value.id)
    
    if (result.success) {
      console.log(`模板 ${templateToDelete.value.name} 删除成功`)
      showDeleteModal.value = false
      templateToDelete.value = null
      
      // 如果当前页没有数据了，回到上一页
      if (templates.value.length === 1 && currentPage.value > 1) {
        loadPage(currentPage.value - 1)
      } else {
        loadTemplates()
      }
    } else {
      throw new Error(result.error)
    }
  } catch (err) {
    console.error('删除模板失败:', err)
    alert('删除模板失败: ' + err.message)
  } finally {
    deleting.value = false
  }
}

/**
 * 取消删除
 * @description 关闭删除确认弹窗
 */
const cancelDelete = () => {
  showDeleteModal.value = false
  templateToDelete.value = null
}

/**
 * 恢复模板
 * @description 恢复已删除的模板
 * @param {Object} template - 模板对象
 */
const restoreTemplate = async (template) => {
  try {
    // TODO: 实现恢复模板的API调用
    console.log('恢复模板:', template)
    alert(`恢复模板功能开发中: ${template.name}`)
  } catch (err) {
    console.error('恢复模板失败:', err)
    alert('恢复模板失败: ' + err.message)
  }
}

/**
 * 处理图片加载成功
 * @description 图片加载成功时的处理
 */
const handleImageLoad = (event, template) => {
  const actualUrl = getPreviewImageUrl(template)
  console.log('预览图加载成功:')
  console.log('- 模板代码:', template.templateCode)
  console.log('- 原始URL:', template.previewImageUrl)
  console.log('- 实际使用URL:', actualUrl)
}

/**
 * 处理图片加载错误
 * @description 图片加载失败时的处理
 */
const handleImageError = (event, template) => {
  const actualUrl = getPreviewImageUrl(template)
  console.log('预览图加载失败:')
  console.log('- 模板代码:', template.templateCode)
  console.log('- 原始URL:', template.previewImageUrl)
  console.log('- 实际使用URL:', actualUrl)
  console.log('- 错误事件:', event)
  
  // 如果有原始路径，尝试加载备用路径
  if (template.previewImageUrl && !template.previewImageUrl.includes('/api/admin/template/preview-image/')) {
    // 尝试通过API代理访问
    const backupUrl = `/api/admin/template/preview-image/${template.templateCode}`
    console.log('使用备用预览图路径:', backupUrl)
    template.previewImageUrl = backupUrl
  } else {
    // 清除图片URL，显示占位符
    template.previewImageUrl = null
  }
}

/**
 * 获取预览图URL
 * @description 处理预览图URL，确保能正确访问MinIO中的图片
 * @param {Object} template - 模板对象
 * @returns {string|null} 预览图URL
 */
const getPreviewImageUrl = (template) => {
  if (!template.previewImageUrl) {
    return null
  }
  
  // 如果已经是完整的URL，直接返回
  if (template.previewImageUrl.startsWith('http')) {
    return template.previewImageUrl
  }
  
  // 如果已经是API路径，直接返回
  if (template.previewImageUrl.startsWith('/api/admin/template/preview-image/')) {
    return template.previewImageUrl
  }
  
  // 如果是相对路径，通过API代理访问
  if (template.previewImageUrl.startsWith('/')) {
    return template.previewImageUrl
  }
  
  // 如果是存储路径，通过API代理访问
  return `/api/admin/template/preview-image/${template.templateCode}`
}

/**
 * 加载模板列表时处理预览图URL
 */
const processTemplatePreviewUrls = (templates) => {
  return templates.map(template => {
    if (template.previewImageUrl) {
      // 确保预览图URL是可访问的
      template.previewImageUrl = getPreviewImageUrl(template)
    }
    return template
  })
}

/**
 * 格式化时间
 * @description 格式化时间显示
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// ================================
// 生命周期
// ================================

onMounted(async () => {
  // 同步初始分页大小
  pagination.size = pageSize.value
  

  
  // 加载分类列表和模板列表
  await Promise.all([
    loadCategories(),
    loadTemplates()
  ])
})

// ================================
// 监听器
// ================================

// 监听错误状态，自动清除
watch(error, (newError) => {
  if (newError) {
    setTimeout(() => {
      // clearError() // 移除旧的clearError
    }, 5000)
  }
})
</script>

<style scoped>
/* 页面整体样式 */
.template-list-page {
  /* 移除padding，使用布局提供的padding */
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  margin-left: 24px;
}

.btn-primary {
  display: inline-block;
  padding: 12px 24px;
  background: #409eff;
  color: white;
  text-decoration: none;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-primary:hover {
  background: #66b1ff;
}

/* 搜索和筛选区域 */
.search-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-box {
  display: flex;
  align-items: center;
  max-width: 400px;
}

.search-input {
  flex: 1;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px 0 0 4px;
  font-size: 14px;
  outline: none;
}

.search-input:focus {
  border-color: #409eff;
}

.search-btn {
  padding: 12px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-size: 16px;
}

.search-btn:hover {
  background: #66b1ff;
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #409eff;
}

.reset-btn {
  padding: 8px 16px;
  background: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.reset-btn:hover {
  background: #ecf5ff;
  color: #409eff;
  border-color: #409eff;
}

/* 列表内容 */
.list-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 48px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 48px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-state h3 {
  font-size: 18px;
  color: #f56c6c;
  margin: 0 0 8px 0;
}

.error-state p {
  color: #909399;
  margin: 0 0 24px 0;
}

.retry-btn {
  padding: 12px 24px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-btn:hover {
  background: #66b1ff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: #909399;
  margin: 0 0 24px 0;
}

/* 模板列表 */
.template-list {
  /* 列表容器样式 */
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.list-info {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #606266;
}

.view-controls {
  display: flex;
  gap: 8px;
}

.view-btn {
  padding: 8px 12px;
  background: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.view-btn:hover {
  background: #ecf5ff;
  color: #409eff;
  border-color: #409eff;
}

.view-btn.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

/* 网格视图 */
.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.template-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.template-preview {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 14px;
}

.template-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.template-card:hover .template-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.9);
  color: #303133;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.action-btn:hover {
  background: white;
}

.template-info {
  padding: 16px;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.template-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  flex: 1;
  line-height: 1.4;
  min-height: 22px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-badges {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.badge.free {
  background: #e1f3d8;
  color: #67c23a;
}

.badge.premium {
  background: #fdf6ec;
  color: #e6a23c;
}

.badge.hot {
  background: #fef0f0;
  color: #f56c6c;
}

.badge.deleted {
  background: #f4f4f5;
  color: #909399;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  color: #909399;
}

.template-code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
}

.template-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 12px;
  color: #909399;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.template-actions {
  display: flex;
  gap: 8px;
}

.status-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.status-btn.enabled {
  background: #e1f3d8;
  color: #67c23a;
}

.status-btn.disabled {
  background: #f4f4f5;
  color: #909399;
}

.delete-btn {
  padding: 8px 12px;
  background: #fef0f0;
  color: #f56c6c;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.delete-btn:hover {
  background: #f56c6c;
  color: white;
}

.restore-btn {
  padding: 8px 12px;
  background: #e1f3d8;
  color: #67c23a;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.restore-btn:hover {
  background: #67c23a;
  color: white;
}

/* 列表视图 */
.list-view {
  /* 列表视图样式 */
}

.list-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 80px 2fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #303133;
}

.table-header > div {
  padding: 12px 8px;
  font-size: 14px;
}

.table-body {
  /* 表格主体样式 */
}

.table-row {
  display: grid;
  grid-template-columns: 80px 2fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid #ebeef5;
  transition: background 0.3s;
}

.table-row:hover {
  background: #f5f7fa;
}

.table-row > div {
  padding: 12px 8px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.col-preview {
  justify-content: center;
}

.preview-thumb {
  width: 48px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
  flex-shrink: 0;
  display: block;
}

.preview-placeholder-small {
  width: 48px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 16px;
  color: #909399;
  flex-shrink: 0;
}

.col-name {
  flex-direction: column;
  align-items: flex-start;
}

.template-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.type-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.type-badge.free {
  background: #e1f3d8;
  color: #67c23a;
}

.type-badge.premium {
  background: #fdf6ec;
  color: #e6a23c;
}

.price {
  margin-left: 8px;
  font-size: 12px;
  color: #e6a23c;
  font-weight: 500;
}

.col-stats {
  flex-direction: column;
  align-items: flex-start;
}

.stat-line {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.status-indicator {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.enabled {
  background: #e1f3d8;
  color: #67c23a;
}

.status-indicator.disabled {
  background: #f4f4f5;
  color: #909399;
}

.status-indicator.deleted {
  background: #f4f4f5;
  color: #909399;
  border: 1px solid #dcdfe6;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-btn {
  background: #e6f7ff;
  color: #1890ff;
}

.edit-btn {
  background: #f6ffed;
  color: #52c41a;
}

.status-btn.enable {
  background: #f6ffed;
  color: #52c41a;
}

.status-btn.disable {
  background: #fff7e6;
  color: #fa8c16;
}

.delete-btn {
  background: #fff2f0;
  color: #ff4d4f;
}

.restore-btn {
  background: #f6ffed;
  color: #52c41a;
}

.action-btn:hover {
  opacity: 0.8;
}

/* 分页器 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.page-btn {
  padding: 8px 16px;
  background: white;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-btn:hover:not(:disabled) {
  background: #ecf5ff;
  color: #409eff;
  border-color: #409eff;
}

.page-btn:disabled {
  background: #f5f7fa;
  color: #c0c4cc;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 32px;
  height: 32px;
  background: white;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number:hover {
  background: #ecf5ff;
  color: #409eff;
  border-color: #409eff;
}

.page-number.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.page-size-select {
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

/* 确认删除弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 通用模态框样式 */
.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-content.large {
  max-width: 900px;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #909399;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f5f7fa;
  color: #303133;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.modal-body p {
  margin: 0 0 12px 0;
  color: #606266;
}

.warning-text {
  color: #f56c6c;
  font-size: 14px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #e4e7ed;
  background: #f8f9fa;
}

/* 预览弹窗专用样式 */
.preview-body {
  padding: 0;
}

.preview-loading, .preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

.preview-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preview-error .error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.preview-container {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.preview-info {
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 80px;
  color: #606266;
  font-weight: 500;
}

.preview-image-container {
  flex: 1;
  position: relative;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border: none;
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #606266;
  z-index: 1;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e4e7ed;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.no-preview-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 编辑弹窗专用样式 */
.edit-body {
  max-height: 500px;
  overflow-y: auto;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.form-label.required::after {
  content: ' *';
  color: #f56c6c;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #409eff;
}

.form-input:disabled {
  background: #f5f7fa;
  color: #c0c4cc;
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 按钮样式 */
.btn-primary {
  padding: 10px 20px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.btn-primary:hover {
  background: #66b1ff;
}

.btn-primary:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 10px 20px;
  background: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.btn-danger {
  padding: 10px 20px;
  background: #f56c6c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.btn-danger:hover:not(:disabled) {
  background: #f78989;
}

.btn-danger:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.retry-btn {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-list-page {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .search-filters {
    flex-direction: column;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .grid-view {
    grid-template-columns: 1fr;
  }

  .list-view {
    overflow-x: auto;
  }

  .table-header,
  .table-row {
    min-width: 800px;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .modal-content.large {
    max-width: none;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .preview-container {
    height: 400px;
  }

  .modal-actions {
    flex-direction: column-reverse;
  }

  .btn-primary,
  .btn-secondary,
  .btn-danger {
    width: 100%;
  }
}
</style> 