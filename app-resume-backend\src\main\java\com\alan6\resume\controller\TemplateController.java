package com.alan6.resume.controller;

import com.alan6.resume.common.ratelimit.RateLimit;
import com.alan6.resume.common.utils.BusinessLogUtils;
import com.alan6.resume.core.R;
import com.alan6.resume.dto.template.*;
import com.alan6.resume.security.UserPrincipal;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.ITemplateCategoriesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <p>
 * 简历模板 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/template")
@RequiredArgsConstructor
@Validated
@Tag(name = "模板管理", description = "模板相关接口")
public class TemplateController {

    private final ITemplateCategoriesService templateCategoriesService;
    private final IResumeTemplatesService resumeTemplatesService;

    /**
     * 获取模板分类列表
     */
    @GetMapping("/categories")
    @Operation(summary = "获取模板分类", description = "获取所有可用的模板分类列表，包含每个分类的模板数量")
    @RateLimit(key = "template:categories", rate = 100, rateInterval = 60)
    public R<List<TemplateCategoryResponse>> getCategories() {
        log.info("开始获取模板分类列表");
        
        try {
            List<TemplateCategoryResponse> categories = templateCategoriesService.getAllCategories();
            
            BusinessLogUtils.logBusiness("获取模板分类成功", 
                    "categoryCount", categories.size());
            
            log.info("成功获取模板分类列表，共 {} 个分类", categories.size());
            return R.ok(categories);
            
        } catch (Exception e) {
            log.error("获取模板分类列表失败", e);
            BusinessLogUtils.logError("获取模板分类失败", e);
            return R.fail("获取模板分类失败");
        }
    }

    /**
     * 获取模板列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取模板列表", description = "获取模板列表，支持多维度筛选和分页")
    @RateLimit(key = "template:list", rate = 60, rateInterval = 60)
    public R<TemplateListResponse> getTemplateList(
            @Parameter(description = "列表请求参数") TemplateListRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("开始获取模板列表，用户ID: {}, 请求参数: {}", userId, request);
        
        try {
            TemplateListResponse response = resumeTemplatesService.getTemplateList(request, userId);
            
            BusinessLogUtils.logBusiness("获取模板列表成功", 
                    "userId", userId, "templateCount", response.getRecords().size());
            
            log.info("成功获取模板列表，用户ID: {}, 返回 {} 个模板", userId, response.getRecords().size());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("获取模板列表失败，用户ID: {}", userId, e);
            BusinessLogUtils.logError("获取模板列表失败", e, "userId", userId);
            return R.fail("获取模板列表失败");
        }
    }

    /**
     * 获取模板详情
     */
    @GetMapping("/detail/{id}")
    @Operation(summary = "获取模板详情", description = "根据模板ID获取详细信息")
    @RateLimit(key = "template:detail", rate = 100, rateInterval = 60)
    public R<TemplateDetailResponse> getTemplateDetail(
            @Parameter(description = "模板ID") @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("开始获取模板详情，模板ID: {}, 用户ID: {}", id, userId);
        
        try {
            TemplateDetailResponse response = resumeTemplatesService.getTemplateDetail(id, userId);
            
            BusinessLogUtils.logBusiness("获取模板详情成功", 
                    "templateId", id, "userId", userId);
            
            log.info("成功获取模板详情，模板ID: {}, 用户ID: {}", id, userId);
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("获取模板详情失败，模板ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("获取模板详情失败", e, "templateId", id, "userId", userId);
            return R.fail("获取模板详情失败");
        }
    }

    /**
     * 使用模板创建简历
     */
    @PostMapping("/use/{id}")
    @Operation(summary = "使用模板创建简历", description = "基于指定模板创建新简历")
    @RateLimit(key = "template:use", rate = 20, rateInterval = 60)
    public R<TemplateUseResponse> useTemplate(
            @Parameter(description = "模板ID") @PathVariable Long id,
            @Parameter(description = "使用模板请求") @Valid @RequestBody TemplateUseRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始使用模板创建简历，模板ID: {}, 用户ID: {}, 简历名称: {}", id, userId, request.getResumeName());
        
        try {
            TemplateUseResponse response = resumeTemplatesService.useTemplate(id, request, userId);
            
            BusinessLogUtils.logBusiness("使用模板创建简历成功", 
                    "templateId", id, "userId", userId, "resumeName", request.getResumeName());
            
            log.info("成功使用模板创建简历，模板ID: {}, 用户ID: {}, 简历ID: {}", id, userId, response.getResumeId());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("使用模板创建简历失败，模板ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("使用模板创建简历失败", e, "templateId", id, "userId", userId);
            return R.fail("使用模板创建简历失败");
        }
    }

    /**
     * 搜索模板
     */
    @GetMapping("/search")
    @Operation(summary = "搜索模板", description = "根据关键词搜索模板，支持多维度筛选")
    @RateLimit(key = "template:search", rate = 60, rateInterval = 60)
    public R<TemplateListResponse> searchTemplates(
            @Parameter(description = "搜索请求参数") @Valid TemplateSearchRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("开始搜索模板，用户ID: {}, 搜索关键词: {}", userId, request.getKeyword());
        
        try {
            TemplateListResponse response = resumeTemplatesService.searchTemplates(request, userId);
            
            BusinessLogUtils.logBusiness("搜索模板成功", 
                    "keyword", request.getKeyword(), "userId", userId, "resultCount", response.getRecords().size());
            
            log.info("搜索模板完成，用户ID: {}, 关键词: {}, 结果数量: {}", userId, request.getKeyword(), response.getRecords().size());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("搜索模板失败，用户ID: {}, 关键词: {}", userId, request.getKeyword(), e);
            BusinessLogUtils.logError("搜索模板失败", e, "keyword", request.getKeyword(), "userId", userId);
            return R.fail("搜索模板失败");
        }
    }

    /**
     * 推荐模板
     */
    @GetMapping("/recommend")
    @Operation(summary = "推荐模板", description = "基于用户偏好和历史行为推荐模板")
    @RateLimit(key = "template:recommend", rate = 60, rateInterval = 60)
    public R<List<TemplateResponse>> recommendTemplates(
            @Parameter(description = "推荐请求参数") TemplateRecommendRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("开始推荐模板，用户ID: {}, 推荐数量: {}", userId, request.getLimit());
        
        try {
            List<TemplateResponse> response = resumeTemplatesService.recommendTemplates(request, userId);
            
            BusinessLogUtils.logBusiness("推荐模板成功", 
                    "userId", userId, "recommendCount", response.size());
            
            log.info("推荐模板完成，用户ID: {}, 推荐数量: {}", userId, response.size());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("推荐模板失败，用户ID: {}", userId, e);
            BusinessLogUtils.logError("推荐模板失败", e, "userId", userId);
            return R.fail("推荐模板失败");
        }
    }

    /**
     * 获取热门模板
     */
    @GetMapping("/hot")
    @Operation(summary = "获取热门模板", description = "获取热门模板列表，支持时间范围和分类筛选")
    @RateLimit(key = "template:hot", rate = 60, rateInterval = 60)
    public R<List<TemplateResponse>> getHotTemplates(
            @Parameter(description = "热门模板请求参数") TemplateHotRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("开始获取热门模板，用户ID: {}, 数量: {}, 时间范围: {}", userId, request.getLimit(), request.getTimeRange());
        
        try {
            List<TemplateResponse> response = resumeTemplatesService.getHotTemplates(request, userId);
            
            BusinessLogUtils.logBusiness("获取热门模板成功", 
                    "userId", userId, "timeRange", request.getTimeRange(), "hotCount", response.size());
            
            log.info("获取热门模板完成，用户ID: {}, 返回数量: {}", userId, response.size());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("获取热门模板失败，用户ID: {}", userId, e);
            BusinessLogUtils.logError("获取热门模板失败", e, "userId", userId);
            return R.fail("获取热门模板失败");
        }
    }

    /**
     * 模板预览
     */
    @GetMapping("/preview/{id}")
    @Operation(summary = "模板预览", description = "生成模板预览图片或HTML，支持多种尺寸和质量设置")
    @RateLimit(key = "template:preview", rate = 30, rateInterval = 60)
    public R<TemplatePreviewResponse> previewTemplate(
            @Parameter(description = "模板ID") @PathVariable Long id,
            @Parameter(description = "预览请求参数") @Valid TemplatePreviewRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("开始预览模板，模板ID: {}, 用户ID: {}, 预览格式: {}", id, userId, request.getFormat());
        
        try {
            TemplatePreviewResponse response = resumeTemplatesService.previewTemplate(id, request, userId);
            
            BusinessLogUtils.logBusiness("模板预览生成成功", 
                    "templateId", id, "userId", userId, "format", request.getFormat(), 
                    "width", request.getWidth(), "quality", request.getQuality());
            
            log.info("模板预览生成成功，模板ID: {}, 用户ID: {}, 预览URL: {}", id, userId, response.getPreviewUrl());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("模板预览生成失败，模板ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("模板预览生成失败", e, "templateId", id, "userId", userId);
            return R.fail("模板预览生成失败: " + e.getMessage());
        }
    }

    /**
     * 模板预览（带数据）
     */
    @PostMapping("/preview/{id}/with-data")
    @Operation(summary = "模板预览（带数据）", description = "使用提供的简历数据生成模板预览，适用于用户自定义数据的预览")
    @RateLimit(key = "template:preview:data", rate = 20, rateInterval = 60)
    public R<TemplatePreviewResponse> previewTemplateWithData(
            @Parameter(description = "模板ID") @PathVariable Long id,
            @Parameter(description = "预览请求参数（包含简历数据）") @Valid @RequestBody TemplatePreviewWithDataRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("开始预览模板（带数据），模板ID: {}, 用户ID: {}, 预览格式: {}", id, userId, request.getFormat());
        
        try {
            TemplatePreviewResponse response = resumeTemplatesService.previewTemplateWithData(id, request, userId);
            
            BusinessLogUtils.logBusiness("模板预览（带数据）生成成功", 
                    "templateId", id, "userId", userId, "format", request.getFormat(), 
                    "width", request.getWidth(), "quality", request.getQuality(),
                    "hasResumeData", request.getResumeData() != null);
            
            log.info("模板预览（带数据）生成成功，模板ID: {}, 用户ID: {}, 预览URL: {}", id, userId, response.getPreviewUrl());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("模板预览（带数据）生成失败，模板ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("模板预览（带数据）生成失败", e, "templateId", id, "userId", userId);
            return R.fail("模板预览生成失败: " + e.getMessage());
        }
    }

}
