package com.alan6.resume.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 登录响应DTO
 * 
 * 主要功能：
 * 1. 返回登录成功后的用户信息
 * 2. 包含访问Token和用户基本信息
 * 3. 提供给前端进行后续请求认证
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "登录响应", description = "用户登录成功后的响应数据")
public class LoginResponse {

    /**
     * 访问Token
     * 用于后续API请求的身份认证
     */
    @Schema(name = "访问Token", description = "用于API请求认证的令牌")
    private String token;

    /**
     * Token过期时间
     * 前端可根据此时间提醒用户重新登录
     */
    @Schema(name = "Token过期时间", description = "Token的过期时间戳")
    private LocalDateTime tokenExpireTime;

    /**
     * 用户ID
     * 用户的唯一标识
     */
    @Schema(name = "用户ID", description = "用户的唯一标识")
    private Long userId;

    /**
     * 用户名
     * 用户的登录用户名
     */
    @Schema(name = "用户名", description = "用户的登录用户名")
    private String username;

    /**
     * 昵称
     * 用户的显示昵称
     */
    @Schema(name = "昵称", description = "用户的显示昵称")
    private String nickname;

    /**
     * 头像URL
     * 用户的头像地址
     */
    @Schema(name = "头像URL", description = "用户的头像地址")
    private String avatarUrl;

    /**
     * 手机号
     * 用户绑定的手机号（脱敏显示）
     */
    @Schema(name = "手机号", description = "用户绑定的手机号（脱敏显示）")
    private String phone;

    /**
     * 邮箱
     * 用户绑定的邮箱地址（脱敏显示）
     */
    @Schema(name = "邮箱", description = "用户绑定的邮箱地址（脱敏显示）")
    private String email;

    /**
     * 性别
     * 0-未知，1-男，2-女
     */
    @Schema(name = "性别", description = "0-未知，1-男，2-女")
    private Byte gender;

    /**
     * 注册平台
     * 用户首次注册的平台
     */
    @Schema(name = "注册平台", description = "用户首次注册的平台")
    private String registerPlatform;

    /**
     * 用户状态
     * 0-禁用，1-正常
     */
    @Schema(name = "用户状态", description = "0-禁用，1-正常")
    private Byte status;

    /**
     * 手机号是否已验证
     * 0-否，1-是
     */
    @Schema(name = "手机号是否已验证", description = "0-否，1-是")
    private Byte isPhoneVerified;

    /**
     * 界面语言偏好
     * 用户选择的界面语言
     */
    @Schema(name = "界面语言偏好", description = "用户选择的界面语言")
    private String preferredLanguage;

    /**
     * 是否为新用户
     * 用于前端判断是否需要引导新用户
     */
    @Schema(name = "是否为新用户", description = "true-新用户，false-老用户")
    private Boolean isNewUser;

    /**
     * 最后登录时间
     * 用户上次登录的时间
     */
    @Schema(name = "最后登录时间", description = "用户上次登录的时间")
    private LocalDateTime lastLoginTime;

    /**
     * 会员信息
     * 用户的会员等级和到期时间等信息
     */
    @Schema(name = "会员信息", description = "用户的会员等级和到期时间等信息")
    private MembershipInfo membershipInfo;

    /**
     * 用户权限列表
     * 用户拥有的功能权限
     */
    @Schema(name = "用户权限列表", description = "用户拥有的功能权限")
    private java.util.List<String> permissions;

    /**
     * 会员信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "会员信息", description = "用户会员相关信息")
    public static class MembershipInfo {

        /**
         * 是否为会员
         */
        @Schema(description = "true-会员，false-普通用户")
        private Boolean isMember;

        /**
         * 会员等级
         */
        @Schema(description = "会员等级名称")
        private String memberLevel;

        /**
         * 会员到期时间
         */
        @Schema(description = "会员服务到期时间")
        private LocalDateTime expireTime;

        /**
         * 剩余天数
         */
        @Schema(description = "会员服务剩余天数")
        private Integer remainDays;
    }

    /**
     * 创建新用户的登录响应
     * 
     * 用于首次登录自动注册的用户
     * 
     * @param token 访问Token
     * @param user 新注册的用户信息
     * @return 登录响应对象
     */
    public static LoginResponse createForNewUser(String token, Object user) {
        return LoginResponse.builder()
                .token(token)
                .tokenExpireTime(LocalDateTime.now().plusSeconds(7 * 24 * 60 * 60)) // 7天后过期
                .isNewUser(true)
                .build();
    }

    /**
     * 创建老用户的登录响应
     * 
     * 用于已存在用户的登录
     * 
     * @param token 访问Token
     * @param user 用户信息
     * @return 登录响应对象
     */
    public static LoginResponse createForExistingUser(String token, Object user) {
        return LoginResponse.builder()
                .token(token)
                .tokenExpireTime(LocalDateTime.now().plusSeconds(7 * 24 * 60 * 60)) // 7天后过期
                .isNewUser(false)
                .build();
    }

    /**
     * 对手机号进行脱敏处理
     * 
     * @param phone 原始手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 对邮箱进行脱敏处理
     * 
     * @param email 原始邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 2) {
            return email;
        }
        
        String maskedUsername = username.substring(0, 1) + "***" + username.substring(username.length() - 1);
        return maskedUsername + "@" + domain;
    }
} 