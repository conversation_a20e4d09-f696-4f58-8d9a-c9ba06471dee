# 管理后台模板上传问题修复总结

## 问题描述
用户报告管理后台模板上传页面中两个问题：
1. 模板代码字段总是提示"检查代码可用性失败"
2. Minio桶名称总是提示"桶不可用"

## 问题分析

### 1. 初步分析
- 前端调用API：`/api/admin/template/check-code` 和 `/api/admin/template/check-bucket`
- 后端接口：`/admin/template/check-code` 和 `/admin/template/check-bucket`
- Nuxt代理配置正确：`/api` → `http://localhost:9311`

### 2. 错误信息分析
**前端错误：**
```
XHR GET http://localhost:3000/api/admin/template/check-bucket?bucketName=resume-template
[HTTP/1.1 401 Unauthorized 9ms]
❌ 检查存储桶失败: Error: HTTP error! status: 401
```

**后端错误：**
```
DEBUG com.alan6.resume.common.utils.TokenUtils - Authorization请求头为空
DEBUG c.a.r.security.filter.TokenAuthenticationFilter - 请求头中未找到有效Token
WARN c.a.r.s.handler.AuthenticationEntryPointImpl - 用户认证失败
```

### 3. 根本原因
**认证令牌键不匹配问题：**
- 管理员登录服务使用：`localStorage.getItem('admin_token')`
- 模板上传服务使用：`localStorage.getItem('auth_token')`
- 调试页面使用：`localStorage.getItem('auth_token')`

这导致即使管理员成功登录，模板上传相关的API调用也无法获取到正确的认证令牌。

## 解决方案

### 1. 修复PowerShell命令语法
```powershell
# 错误的语法
cd app-resume-web && npm run dev

# 正确的语法
cd app-resume-web; npm run dev
```

### 2. 统一认证令牌键名
将所有相关文件中的令牌键统一为 `admin_token`：

**修复文件：**
- `app-resume-web/composables/useTemplateUploadService.js`
- `app-resume-web/pages/admin-auth-debug.vue`

**修复内容：**
```javascript
// 修复前
const token = localStorage.getItem('auth_token')

// 修复后
const token = localStorage.getItem('admin_token')
```

### 3. 增强调试功能
更新了认证调试页面 `admin-auth-debug.vue`，添加了：
- 详细的网络请求日志
- 请求头和响应头的完整信息
- 401错误的具体处理建议
- 自动化的接口测试流程

## 测试步骤

### 1. 启动服务
```powershell
# 启动后端服务
cd app-resume-backend
mvn spring-boot:run -Dspring-boot.run.profiles=test

# 启动前端服务
cd app-resume-web
npm run dev
```

### 2. 登录管理后台
1. 访问：`http://localhost:3000/admin/login`
2. 使用快速登录：
   - 管理员：手机号 `13800138001`，密码 `123456`
   - 超级管理员：手机号 `13800138002`，密码 `123456`

### 3. 测试认证调试页面
1. 访问：`http://localhost:3000/admin-auth-debug`
2. 检查认证令牌是否正确显示
3. 点击"直接测试API"按钮
4. 查看详细的调试日志

### 4. 测试模板上传页面
1. 访问：`http://localhost:3000/admin/template/upload`
2. 输入模板代码，观察是否还提示"检查代码可用性失败"
3. 检查桶名称，观察是否还提示"桶不可用"

## 技术细节

### 1. API接口路径
- 前端请求：`/api/admin/template/check-code`
- 代理转发：`http://localhost:9311/admin/template/check-code`
- 后端接口：`@GetMapping("/check-code")` in `@RequestMapping("/admin/template")`

### 2. 认证流程
1. 管理员登录 → 获取JWT令牌
2. 令牌存储在 `localStorage.getItem('admin_token')`
3. API调用时添加头：`Authorization: Bearer ${token}`
4. 后端验证令牌并检查ADMIN/SUPER_ADMIN权限

### 3. 代理配置
```typescript
// nuxt.config.ts
nitro: {
  devProxy: {
    '/api': {
      target: 'http://localhost:9311',
      changeOrigin: true,
      pathRewrite: {
        '^/api': ''
      }
    }
  }
}
```

## 修复验证

### 1. 网络请求测试
```powershell
# 测试后端直接访问（应返回401）
Invoke-WebRequest -Uri "http://localhost:9311/admin/template/check-code?templateCode=test" -Method GET -Headers @{"Accept"="application/json"}

# 测试前端代理（应返回401）
Invoke-WebRequest -Uri "http://localhost:3000/api/admin/template/check-code?templateCode=test" -Method GET -Headers @{"Accept"="application/json"}
```

### 2. 认证令牌验证
登录后检查浏览器localStorage：
```javascript
// 应该存在
localStorage.getItem('admin_token')
localStorage.getItem('admin_info')
```

## 预期结果
修复后，管理员登录成功后：
1. 模板代码检查功能正常工作
2. MinIO桶状态检查功能正常工作
3. 不再出现401认证错误
4. 调试页面显示正确的认证状态

## 注意事项
1. 确保后端服务正常运行在端口9311
2. 确保前端服务正常运行在端口3000
3. 确保Redis和MinIO服务正常运行
4. 如果仍有问题，检查数据库中的管理员用户权限设置 