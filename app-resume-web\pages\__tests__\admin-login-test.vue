<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">管理员登录测试</h1>
        <p class="text-gray-600">测试管理员账户登录功能</p>
      </div>

      <form @submit.prevent="handleLogin">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            手机号
          </label>
          <input
            v-model="loginForm.phone"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请输入手机号"
          />
        </div>

        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            密码
          </label>
          <input
            v-model="loginForm.password"
            type="password"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请输入密码"
          />
        </div>

        <button
          type="submit"
          :disabled="loading"
          class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </form>

      <!-- 登录结果 -->
      <div v-if="loginResult" class="mt-4 p-4 rounded-md" :class="loginResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'">
        <h4 class="font-medium mb-2">{{ loginResult.success ? '登录成功' : '登录失败' }}</h4>
        <div class="text-xs overflow-auto">
          <div v-if="loginResult.success">
            <p><strong>Token:</strong> {{ loginResult.data?.token?.substring(0, 50) }}...</p>
            <p><strong>用户ID:</strong> {{ loginResult.data?.userId }}</p>
            <p><strong>昵称:</strong> {{ loginResult.data?.nickname }}</p>
            <p><strong>手机号:</strong> {{ loginResult.data?.phone }}</p>
          </div>
          <div v-else>
            <p><strong>错误信息:</strong> {{ loginResult.error }}</p>
            <p><strong>状态码:</strong> {{ loginResult.status }}</p>
            <div v-if="loginResult.details">
              <p><strong>详细信息:</strong></p>
              <pre class="mt-2 text-xs bg-gray-100 p-2 rounded">{{ JSON.stringify(loginResult.details, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试说明 -->
      <div class="mt-6 p-4 bg-blue-50 rounded-md">
        <h4 class="font-medium text-blue-800 mb-2">测试说明</h4>
        <div class="text-sm text-blue-700">
          <p><strong>测试账户：</strong></p>
          <p>• 管理员: *********** / 123456</p>
          <p>• 超级管理员: *********** / 123456</p>
          <p class="mt-2"><strong>注意：</strong>需要先在数据库中执行 test-admin-data.sql 初始化数据</p>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="mt-6 flex space-x-4">
        <button
          @click="quickLogin('admin')"
          class="flex-1 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 text-sm"
        >
          快速登录管理员
        </button>
        <button
          @click="quickLogin('superadmin')"
          class="flex-1 bg-purple-500 text-white py-2 px-4 rounded-md hover:bg-purple-600 text-sm"
        >
          快速登录超管
        </button>
      </div>

      <!-- 测试后台管理 -->
      <div v-if="isLoggedIn" class="mt-6">
        <NuxtLink
          to="/admin-test-ui"
          class="w-full bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 text-center block"
        >
          进入管理后台UI测试
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置页面标题
useHead({
  title: '管理员登录测试'
})

// 响应式数据
const loading = ref(false)
const loginResult = ref(null)
const isLoggedIn = ref(false)

const loginForm = reactive({
  phone: '',
  password: ''
})

// 测试账户数据
const testAccounts = {
  admin: {
    phone: '***********',
    password: '123456'
  },
  superadmin: {
    phone: '***********',
    password: '123456'
  }
}

// 登录处理
const handleLogin = async () => {
  if (!loginForm.phone || !loginForm.password) {
    loginResult.value = {
      success: false,
      data: { message: '请输入手机号和密码' }
    }
    return
  }

  loading.value = true
  loginResult.value = null

  try {
    const response = await $fetch('/auth/password-login', {
      method: 'POST',
      baseURL: 'http://localhost:9311',
      body: {
        phone: loginForm.phone,
        password: loginForm.password,
        platform: 'web',
        loginType: 1
      }
    })

    loginResult.value = {
      success: true,
      data: response
    }
    isLoggedIn.value = true

    // 这里可以保存token到localStorage或者cookie
    if (response.data && response.data.token) {
      localStorage.setItem('auth_token', response.data.token)
      localStorage.setItem('user_info', JSON.stringify(response.data))
    }

  } catch (error) {
    loginResult.value = {
      success: false,
      data: error.data || { message: error.message }
    }
    isLoggedIn.value = false
  } finally {
    loading.value = false
  }
}

// 快速登录
const quickLogin = (type) => {
  const account = testAccounts[type]
  if (account) {
    loginForm.phone = account.phone
    loginForm.password = account.password
    handleLogin()
  }
}

// 检查是否已登录
onMounted(() => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    isLoggedIn.value = true
  }
})
</script> 