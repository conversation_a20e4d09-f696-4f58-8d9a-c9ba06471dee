/**
 * 模板内容管理服务
 * 提供模板内容的增删改查、启用禁用、复制等功能
 * 
 * @description 对应后端AdminTemplateController的内容管理接口
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, reactive } from 'vue'

export const useTemplateContentService = () => {
  // 响应式数据
  const loading = ref(false)
  const error = ref(null)
  const contentList = ref([])
  const currentContent = ref(null)
  
  // 筛选条件
  const filters = reactive({
    industry: '',
    position: '',
    language: ''
  })

  /**
   * 获取认证头部
   */
  const getAuthHeaders = () => {
    const token = localStorage.getItem('admin_token')
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }

  /**
   * 获取模板内容列表
   * 
   * @param {Object} filterParams 筛选参数
   * @returns {Promise<Array>} 内容列表
   */
  const getContentList = async (filterParams = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const params = new URLSearchParams()
      
      // 添加筛选参数
      if (filterParams.industry) params.append('industry', filterParams.industry)
      if (filterParams.position) params.append('position', filterParams.position)
      if (filterParams.language) params.append('language', filterParams.language)
      
      const response = await $fetch(`/api/admin/template/content/list?${params.toString()}`, {
        method: 'GET',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        contentList.value = response.data || []
        return contentList.value
      } else {
        throw new Error(response.msg || '获取内容列表失败')
      }
      
    } catch (err) {
      console.error('获取内容列表失败:', err)
      error.value = err.message || '获取内容列表失败'
      contentList.value = []
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据ID获取模板内容详情
   * 
   * @param {number} contentId 内容ID
   * @returns {Promise<Object>} 内容详情
   */
  const getContentById = async (contentId) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch(`/api/admin/template/content/${contentId}`, {
        method: 'GET',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        currentContent.value = response.data
        return response.data
      } else {
        throw new Error(response.msg || '获取内容详情失败')
      }
      
    } catch (err) {
      console.error('获取内容详情失败:', err)
      error.value = err.message || '获取内容详情失败'
      currentContent.value = null
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建模板内容
   * 
   * @param {Object} contentData 内容数据
   * @returns {Promise<Object>} 创建结果
   */
  const createContent = async (contentData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch('/api/admin/template/content', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(contentData)
      })
      
      if (response.code === 200) {
        // 创建成功后刷新列表
        await getContentList(filters)
        return response.data
      } else {
        throw new Error(response.msg || '创建内容失败')
      }
      
    } catch (err) {
      console.error('创建内容失败:', err)
      error.value = err.message || '创建内容失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新模板内容
   * 
   * @param {number} contentId 内容ID
   * @param {Object} contentData 更新的内容数据
   * @returns {Promise<Object>} 更新结果
   */
  const updateContent = async (contentId, contentData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch(`/api/admin/template/content/${contentId}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(contentData)
      })
      
      if (response.code === 200) {
        // 更新成功后刷新列表
        await getContentList(filters)
        return response.data
      } else {
        throw new Error(response.msg || '更新内容失败')
      }
      
    } catch (err) {
      console.error('更新内容失败:', err)
      error.value = err.message || '更新内容失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除模板内容
   * 
   * @param {number} contentId 内容ID
   * @returns {Promise<boolean>} 删除结果
   */
  const deleteContent = async (contentId) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch(`/api/admin/template/content/${contentId}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        // 删除成功后刷新列表
        await getContentList(filters)
        return true
      } else {
        throw new Error(response.msg || '删除内容失败')
      }
      
    } catch (err) {
      console.error('删除内容失败:', err)
      error.value = err.message || '删除内容失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 启用模板内容
   * 
   * @param {number} contentId 内容ID
   * @returns {Promise<boolean>} 操作结果
   */
  const enableContent = async (contentId) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch(`/api/admin/template/content/${contentId}/enable`, {
        method: 'PUT',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        // 操作成功后刷新列表
        await getContentList(filters)
        return true
      } else {
        throw new Error(response.msg || '启用内容失败')
      }
      
    } catch (err) {
      console.error('启用内容失败:', err)
      error.value = err.message || '启用内容失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 禁用模板内容
   * 
   * @param {number} contentId 内容ID
   * @returns {Promise<boolean>} 操作结果
   */
  const disableContent = async (contentId) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch(`/api/admin/template/content/${contentId}/disable`, {
        method: 'PUT',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        // 操作成功后刷新列表
        await getContentList(filters)
        return true
      } else {
        throw new Error(response.msg || '禁用内容失败')
      }
      
    } catch (err) {
      console.error('禁用内容失败:', err)
      error.value = err.message || '禁用内容失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 复制模板内容
   * 
   * @param {number} contentId 源内容ID
   * @param {string} newCode 新内容代码
   * @param {string} newName 新内容名称
   * @returns {Promise<Object>} 复制结果
   */
  const copyContent = async (contentId, newCode, newName) => {
    loading.value = true
    error.value = null
    
    try {
      const params = new URLSearchParams()
      params.append('newCode', newCode)
      params.append('newName', newName)
      
      const response = await $fetch(`/api/admin/template/content/${contentId}/copy?${params.toString()}`, {
        method: 'POST',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        // 复制成功后刷新列表
        await getContentList(filters)
        return response.data
      } else {
        throw new Error(response.msg || '复制内容失败')
      }
      
    } catch (err) {
      console.error('复制内容失败:', err)
      error.value = err.message || '复制内容失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取所有行业列表
   * 
   * @returns {Promise<Array>} 行业列表
   */
  const getAllIndustries = async () => {
    try {
      const response = await $fetch('/api/admin/template/content/industries', {
        method: 'GET',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        return response.data || []
      } else {
        throw new Error(response.msg || '获取行业列表失败')
      }
      
    } catch (err) {
      console.error('获取行业列表失败:', err)
      error.value = err.message || '获取行业列表失败'
      return []
    }
  }

  /**
   * 获取所有职位列表
   * 
   * @returns {Promise<Array>} 职位列表
   */
  const getAllPositions = async () => {
    try {
      const response = await $fetch('/api/admin/template/content/positions', {
        method: 'GET',
        headers: getAuthHeaders()
      })
      
      if (response.code === 200) {
        return response.data || []
      } else {
        throw new Error(response.msg || '获取职位列表失败')
      }
      
    } catch (err) {
      console.error('获取职位列表失败:', err)
      error.value = err.message || '获取职位列表失败'
      return []
    }
  }

  /**
   * 批量操作内容状态
   * 
   * @param {Array} contentIds 内容ID数组
   * @param {string} action 操作类型：'enable' | 'disable' | 'delete'
   * @returns {Promise<Object>} 操作结果
   */
  const batchOperation = async (contentIds, action) => {
    loading.value = true
    error.value = null
    
    try {
      const promises = contentIds.map(contentId => {
        switch (action) {
          case 'enable':
            return enableContent(contentId)
          case 'disable':
            return disableContent(contentId)
          case 'delete':
            return deleteContent(contentId)
          default:
            throw new Error(`不支持的操作类型: ${action}`)
        }
      })
      
      const results = await Promise.allSettled(promises)
      
      const successCount = results.filter(r => r.status === 'fulfilled').length
      const failureCount = results.filter(r => r.status === 'rejected').length
      
      return {
        successCount,
        failureCount,
        total: contentIds.length
      }
      
    } catch (err) {
      console.error('批量操作失败:', err)
      error.value = err.message || '批量操作失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 搜索内容
   * 
   * @param {string} keyword 搜索关键词
   * @param {Object} searchFilters 搜索筛选条件
   * @returns {Promise<Array>} 搜索结果
   */
  const searchContent = async (keyword, searchFilters = {}) => {
    const searchParams = {
      ...searchFilters,
      keyword
    }
    
    return await getContentList(searchParams)
  }

  /**
   * 重置筛选条件
   */
  const resetFilters = () => {
    filters.industry = ''
    filters.position = ''
    filters.language = ''
  }

  /**
   * 清理错误状态
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // 响应式数据
    loading,
    error,
    contentList,
    currentContent,
    filters,
    
    // 基本操作
    getContentList,
    getContentById,
    createContent,
    updateContent,
    deleteContent,
    
    // 状态管理
    enableContent,
    disableContent,
    copyContent,
    
    // 数据获取
    getAllIndustries,
    getAllPositions,
    
    // 批量操作
    batchOperation,
    
    // 搜索功能
    searchContent,
    
    // 辅助方法
    resetFilters,
    clearError
  }
} 