package com.alan6.resume.service;

import com.alan6.resume.dto.order.OrderDetailResponse;
import com.alan6.resume.dto.order.OrderListResponse;
import com.alan6.resume.entity.Orders;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IOrdersService extends IService<Orders> {

    /**
     * 创建会员购买订单
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @param packageName 套餐名称
     * @param amount 订单金额
     * @param paymentMethod 支付方式
     * @return 创建的订单
     */
    Orders createMembershipOrder(Long userId, Long packageId, String packageName, 
                                BigDecimal amount, String paymentMethod);

    /**
     * 根据订单号获取订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    Orders getByOrderNo(String orderNo);

    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param status 新状态
     * @param tradeNo 第三方交易号（可选）
     * @return 更新是否成功
     */
    Boolean updateOrderStatus(Long orderId, Byte status, String tradeNo);

    /**
     * 更新订单状态（通过订单号）
     * 
     * @param orderNo 订单号
     * @param status 新状态
     * @param tradeNo 第三方交易号
     * @param remark 备注
     * @return 更新是否成功
     */
    Boolean updateOrderStatusByOrderNo(String orderNo, Byte status, String tradeNo, String remark);

    /**
     * 获取用户订单列表（分页）
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页数量
     * @param status 订单状态（可选）
     * @param paymentMethod 支付方式（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param orderType 订单类型（可选）
     * @return 订单列表
     */
    OrderListResponse getUserOrderList(Long userId, Integer page, Integer size, Byte status, 
                                     Byte paymentMethod, LocalDateTime startTime, LocalDateTime endTime, Byte orderType);

    /**
     * 获取订单详情
     * 
     * @param orderNo 订单号
     * @param userId 用户ID（用于权限验证）
     * @return 订单详情
     */
    OrderDetailResponse getOrderDetail(String orderNo, Long userId);

    /**
     * 取消订单
     * 
     * @param orderNo 订单号
     * @param userId 用户ID（用于权限验证）
     * @param cancelReason 取消原因
     * @param cancelReasonCode 取消原因代码
     * @return 取消结果
     */
    Boolean cancelOrder(String orderNo, Long userId, String cancelReason, String cancelReasonCode);

}
