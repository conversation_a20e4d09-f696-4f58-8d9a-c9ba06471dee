<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简历 - 王明远</title>
  <style>
    :root {
      --resume-font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      --resume-font-size: 14px;
      --resume-line-height: 1.6;
      --resume-primary-color: #007b81;
      --resume-text-color: #333;
      --resume-secondary-color: #666;
      --resume-margin-top: 60px;
      --resume-margin-bottom: 60px;
      --resume-margin-left: 60px;
      --resume-margin-right: 60px;
      --resume-module-spacing: 24px;
      --resume-item-spacing: 16px;
      --resume-paragraph-spacing: 12px;
      --resume-column-gap: 40px;
      --resume-module-padding: 16px;
      --resume-item-padding: 12px;
    }
    body {
      font-family: var(--resume-font-family);
      font-size: var(--resume-font-size);
      line-height: var(--resume-line-height);
      margin: 0;
      padding: 0;
      color: var(--resume-text-color);
      background: #ffffff;
    }
    .resume-template {
      width: 794px;
      margin: 0 auto;
      border: 1px solid #eee;
      box-sizing: border-box;
    }
    .header {
      background: var(--resume-primary-color);
      color: #fff;
      padding: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .header-left h1 {
      font-size: 28px;
      margin: 0;
      font-weight: bold;
    }
    .header-left p {
      margin: 10px 0 0;
      font-size: 14px;
    }
    .header-right {
      width: 96px;
      height: 96px;
      border-radius: 10px;
      background: #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666;
    }

    .resume-section {
      padding: 20px 40px;
      border-bottom: 1px solid #ddd;
    }

    .section-title {
      display: inline-block;
      background-color: var(--resume-primary-color);
      color: #fff;
      padding: 5px 15px;
      font-size: 16px;
      font-weight: bold;
      position: relative;
      margin-bottom: 15px;
    }
    .section-title::after {
      content: "";
      position: absolute;
      right: -12px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-top: 12px solid transparent;
      border-bottom: 12px solid transparent;
      border-left: 12px solid var(--resume-primary-color);
    }

    .resume-item {
      margin-bottom: var(--resume-item-spacing);
    }
    .item-title {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
    }
    .item-desc {
      margin-top: 5px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="resume-template" data-module-root="true">
    <div class="header" data-module="basic_info">
      <div class="header-left basic-info">
        <h1><span data-vue-text="resumeData.basic_info.name">王明远</span></h1>
        <p>
          <span data-vue-if="resumeData.basic_info.gender" data-vue-text="resumeData.basic_info.gender">男</span>
          <span data-vue-if="resumeData.basic_info.gender">｜</span>
          <span data-vue-if="resumeData.basic_info.age" data-vue-text="resumeData.basic_info.age">1992年生</span>
          <span data-vue-if="resumeData.basic_info.age">｜</span>
          <span data-vue-text="resumeData.basic_info.currentCity">上海</span>｜📞
          <span data-vue-text="resumeData.basic_info.phone">13888888888</span>｜✉️
          <span data-vue-text="resumeData.basic_info.email"><EMAIL></span>
        </p>
        <p>
          当前状态：<span data-vue-text="resumeData.basic_info.currentStatus">在职，考虑新机会</span>｜
          期望岗位：<span data-vue-text="resumeData.basic_info.expectedPosition">高级产品经理</span>｜
          期望薪资：<span data-vue-text="resumeData.basic_info.expectedSalary">30-40k</span>
        </p>
      </div>
      <div class="header-right" data-vue-if="resumeData.basic_info.photo">
        <img data-vue-src="resumeData.basic_info.photo" width="96" height="96" style="border-radius: 10px; object-fit: cover;" />
      </div>
      <div class="header-right" data-vue-if="!resumeData.basic_info.photo">
        头像
      </div>
    </div>

    <section class="resume-section" data-module="education" data-vue-if="resumeData.education && resumeData.education.length > 0">
      <div class="section-title">教育经历</div>
      <div data-vue-for="education in resumeData.education" data-vue-key="education.id" class="resume-item">
        <div class="item-title">
          <span>
            <span data-vue-text="education.school"></span>｜
            <span data-vue-text="education.major"></span>
            <span data-vue-text="education.degree"></span>
          </span>
          <span>
            <span data-vue-text="education.start_date"></span> - <span data-vue-text="education.end_date"></span>
          </span>
        </div>
        <div class="item-desc" data-vue-if="education.description" data-vue-text="education.description"></div>
      </div>
    </section>

    <section class="resume-section" data-module="work_experience" data-vue-if="resumeData.work_experience && resumeData.work_experience.length > 0">
      <div class="section-title">工作经历</div>
      <div data-vue-for="work in resumeData.work_experience" data-vue-key="work.id" class="resume-item">
        <div class="item-title">
          <span>
            <span data-vue-text="work.company"></span>｜<span data-vue-text="work.position"></span>
          </span>
          <span>
            <span data-vue-text="work.start_date"></span> - <span data-vue-text="work.end_date"></span>
          </span>
        </div>
        <div class="item-desc" data-vue-if="work.description" data-vue-text="work.description"></div>
      </div>
    </section>

    <section class="resume-section" data-module="project" data-vue-if="resumeData.project && resumeData.project.length > 0">
      <div class="section-title">项目经历</div>
      <div data-vue-for="project in resumeData.project" data-vue-key="project.id" class="resume-item">
        <div class="item-title">
          <span data-vue-text="project.name"></span>｜<span data-vue-text="project.role"></span>
        </div>
        <div class="item-desc">
          <div data-vue-if="project.technologies">技术栈：<span data-vue-text="project.technologies"></span></div>
          <div data-vue-if="project.description" data-vue-text="project.description"></div>
        </div>
      </div>
    </section>

    <section class="resume-section" data-module="skills" data-vue-if="resumeData.skills && resumeData.skills.length > 0">
      <div class="section-title">技能特长</div>
      <div data-vue-for="skill in resumeData.skills" data-vue-key="skill.id" class="resume-item">
        <div class="item-title">
          <span data-vue-text="skill.name"></span>
          <span data-vue-if="skill.level" data-vue-text="skill.level"></span>
        </div>
      </div>
    </section>

    <section class="resume-section" data-module="award" data-vue-if="resumeData.award && resumeData.award.length > 0">
      <div class="section-title">获奖荣誉</div>
      <div data-vue-for="award in resumeData.award" data-vue-key="award.id" class="resume-item">
        <div class="item-title">
          <span data-vue-text="award.name"></span>
          <span data-vue-if="award.organization">｜<span data-vue-text="award.organization"></span></span>
          <span data-vue-if="award.level">｜<span data-vue-text="award.level"></span></span>
          <span data-vue-if="award.date">
            <span data-vue-text="award.date"></span>
          </span>
        </div>
        <div class="item-desc" data-vue-if="award.description" data-vue-text="award.description"></div>
      </div>
    </section>

    <section class="resume-section" data-module="self_evaluation" data-vue-if="resumeData.self_evaluation && resumeData.self_evaluation.content">
      <div class="section-title">自我评价</div>
      <div class="item-desc" data-vue-text="resumeData.self_evaluation.content"></div>
    </section>
  </div>
</body>
</html>