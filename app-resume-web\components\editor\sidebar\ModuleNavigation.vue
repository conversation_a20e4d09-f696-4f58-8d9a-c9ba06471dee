<template>
  <div class="module-navigation">
    <!-- 模块导航头部 -->
    <div class="nav-header">
      <h3 class="nav-title">简历模块</h3>
      <button 
        class="manage-btn"
        @click="showModuleManager"
        title="管理模块"
      >
        <Icon name="plus" size="sm" />
        <span class="manage-text">管理模块</span>
      </button>
    </div>

    <!-- 模块列表 -->
    <div ref="navListRef" class="nav-list">
      <!-- 空状态提示 -->
      <div v-if="visibleModules.length === 0" class="empty-state">
        <p class="empty-text">正在加载模块...</p>
      </div>
      
      <div
        v-for="(module, index) in visibleModules"
        :key="module.id"
        :class="[
          'nav-item',
          { 'active': currentModuleId === module.id },
          { 'disabled': !module.enabled }
        ]"
        @click="selectModule(module, index)"
      >
        <div class="module-content">
          <div class="module-header">
            <Icon :name="module.icon" size="sm" class="module-icon" />
            <div class="module-name-wrapper">
              <span v-if="module.required" class="required-mark">*</span>
              <span class="module-name">{{ module.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 模块管理弹窗 -->
    <ModuleManager 
      v-if="showManager"
      :modules="allModules"
      @close="hideModuleManager"
      @update="handleModuleUpdate"
    />
  </div>
</template>

<script setup>
/**
 * 模块导航组件
 * @description 提供简历模块的导航切换功能，支持模块状态显示和管理
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, nextTick } from 'vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'
import Icon from '~/components/common/Icon.vue'
import ModuleManager from './ModuleManager.vue'

// ================================
// 响应式数据
// ================================
const showManager = ref(false)
const navListRef = ref(null)
let scrollTimeout = null

// ================================
// 状态管理
// ================================
const editorStore = useEditorStore()

// ================================
// 计算属性
// ================================

/**
 * 当前选中的模块ID
 */
const currentModuleId = computed(() => {
  const moduleId = editorStore.currentModuleId.value
  console.log('ModuleNavigation - 当前模块ID:', moduleId)
  return moduleId
})

/**
 * 所有模块配置
 */
const allModules = computed(() => {
  console.log('ModuleNavigation - 计算 allModules')
  const modules = editorStore.moduleConfigs.value
  console.log('ModuleNavigation - moduleConfigs.value 结果:', modules)
  return modules || []
})

/**
 * 可见的模块（已启用的模块，按顺序排列）
 */
const visibleModules = computed(() => {
  const modules = allModules.value
  console.log('ModuleNavigation - allModules.value:', modules)
  
  if (!Array.isArray(modules)) {
    console.warn('ModuleNavigation - modules 不是数组:', modules)
    return []
  }
  
  // 获取已启用的普通模块
  const enabledModules = modules.filter(module => module && module.enabled)
  
  // 获取已启用的自定义模块
  const customModules = editorStore.customModules?.value || []
  const enabledCustomModules = customModules.filter(module => module && module.enabled)
  
  // 合并所有已启用的模块
  const allEnabledModules = [...enabledModules, ...enabledCustomModules]
  
  // 按 order 字段排序
  const sorted = allEnabledModules.sort((a, b) => (a.order || 0) - (b.order || 0))
  console.log('ModuleNavigation - 启用的模块数量:', sorted.length)
  console.log('ModuleNavigation - 启用的模块:', sorted.map(m => `${m.name}(${m.order})`))
  return sorted
})

// ================================
// 方法定义
// ================================

/**
 * 选择模块
 * @param {Object} module - 模块对象
 * @param {Number} index - 模块索引
 */
const selectModule = (module, index) => {
  console.log('ModuleNavigation - selectModule 被调用:', module)
  
  if (!module) {
    console.warn('ModuleNavigation - 模块对象为空')
    return
  }
  
  if (!module.enabled) {
    console.warn('ModuleNavigation - 尝试选择已禁用的模块:', module.name)
    return
  }
  
  console.log('ModuleNavigation - 选择模块:', module.id, module.name)
  console.log('ModuleNavigation - 模块类型:', module.type)
  console.log('ModuleNavigation - 是否为自定义模块:', module.id.startsWith('custom_'))
  
  editorStore.setCurrentModule(module.id)
  
  // 自动滚动功能
  handleAutoScroll(index)
  
  // 验证设置是否成功
  setTimeout(() => {
    console.log('ModuleNavigation - 设置后的当前模块ID:', editorStore.currentModuleId.value)
  }, 100)
}

/**
 * 处理自动滚动
 * @param {Number} clickedIndex - 点击的模块索引
 */
const handleAutoScroll = async (clickedIndex) => {
  // 清除之前的滚动timeout
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  
  scrollTimeout = setTimeout(async () => {
    await nextTick()
    
    if (!navListRef.value) return
    
    const container = navListRef.value
    const moduleItems = container.querySelectorAll('.nav-item')
    
    if (!moduleItems || moduleItems.length === 0) return
    
    const clickedItem = moduleItems[clickedIndex]
    if (!clickedItem) return
    
    const containerRect = container.getBoundingClientRect()
    const itemRect = clickedItem.getBoundingClientRect()
    
    // 计算模块相对于容器的位置
    const itemLeft = itemRect.left - containerRect.left + container.scrollLeft
    const itemRight = itemRect.right - containerRect.left + container.scrollLeft
    const containerWidth = container.clientWidth
    
    // 设置边界检测阈值（距离边界多少像素内触发滚动）
    const threshold = 120 // 增加滚动距离，提供更大的滚动范围
    
    // 检测是否需要向左滚动
    if (itemLeft - container.scrollLeft < threshold) {
      const targetScrollLeft = Math.max(0, itemLeft - threshold)
      smoothScrollTo(container, targetScrollLeft)
    }
    // 检测是否需要向右滚动
    else if (itemRight - container.scrollLeft > containerWidth - threshold) {
      const targetScrollLeft = itemRight - containerWidth + threshold
      smoothScrollTo(container, targetScrollLeft)
    }
  }, 50) // 50ms的防抖延迟
}

/**
 * 平滑滚动到指定位置
 * @param {Element} container - 容器元素
 * @param {Number} targetScrollLeft - 目标滚动位置
 */
const smoothScrollTo = (container, targetScrollLeft) => {
  container.scrollTo({
    left: targetScrollLeft,
    behavior: 'smooth'
  })
}

/**
 * 检查基本信息模块状态
 * @param {Object} data - 模块数据
 * @returns {String} 状态
 */
const checkBasicInfoStatus = (data) => {
  const requiredFields = ['name', 'phone', 'email']
  const filledCount = requiredFields.filter(field => data[field]?.trim()).length
  
  if (filledCount === requiredFields.length) return 'completed'
  if (filledCount > 0) return 'partial'
  return 'empty'
}

/**
 * 检查列表类型模块状态
 * @param {Array} data - 模块数据
 * @returns {String} 状态
 */
const checkListModuleStatus = (data) => {
  if (!Array.isArray(data) || data.length === 0) return 'empty'
  
  const validItems = data.filter(item => {
    return Object.values(item).some(value => 
      value && typeof value === 'string' && value.trim()
    )
  })
  
  if (validItems.length === data.length) return 'completed'
  if (validItems.length > 0) return 'partial'
  return 'empty'
}

/**
 * 检查技能模块状态
 * @param {Object} data - 模块数据
 * @returns {String} 状态
 */
const checkSkillsStatus = (data) => {
  if (!data || !data.skills || !Array.isArray(data.skills) || data.skills.length === 0) return 'empty'
  
  const validSkills = data.skills.filter(skill => skill.name?.trim())
  
  if (validSkills.length === data.skills.length) return 'completed'
  if (validSkills.length > 0) return 'partial'
  return 'empty'
}

/**
 * 检查对象数组模块状态
 * @param {Object} data - 模块数据
 * @param {String} arrayKey - 数组键名
 * @returns {String} 状态
 */
const checkObjectArrayStatus = (data, arrayKey) => {
  if (!data || !data[arrayKey] || !Array.isArray(data[arrayKey]) || data[arrayKey].length === 0) return 'empty'
  
  const validItems = data[arrayKey].filter(item => {
    return Object.values(item).some(value => 
      value && typeof value === 'string' && value.trim()
    )
  })
  
  if (validItems.length === data[arrayKey].length) return 'completed'
  if (validItems.length > 0) return 'partial'
  return 'empty'
}

/**
 * 检查文本模块状态
 * @param {Object} data - 模块数据
 * @returns {String} 状态
 */
const checkTextModuleStatus = (data) => {
  if (!data || !data.content || !data.content.trim()) return 'empty'
  
  const wordCount = data.content.trim().split(/\s+/).length
  if (wordCount >= 10) return 'completed'
  if (wordCount > 0) return 'partial'
  return 'empty'
}

/**
 * 获取模块完成状态
 * @param {Object} module - 模块对象
 * @returns {String} 状态：completed/partial/empty
 */
const getModuleStatus = (module) => {
  const moduleData = editorStore.getModuleData(module.id)
  
  if (!moduleData) return 'empty'
  
  // 根据不同模块类型检查完成状态
  switch (module.type) {
    case 'basic_info':
      return checkBasicInfoStatus(moduleData)
    case 'education':
    case 'work_experience':
    case 'project':
    case 'internship':
    case 'training':
    case 'research_experience':
    case 'volunteer_experience':
      return checkListModuleStatus(moduleData)
    case 'skills':
      return checkSkillsStatus(moduleData)
    case 'language':
      return checkObjectArrayStatus(moduleData, 'languages')
    case 'award':
      return checkObjectArrayStatus(moduleData, 'awards')
    case 'hobbies':
      return checkObjectArrayStatus(moduleData, 'hobbies')
    case 'publication':
      return checkObjectArrayStatus(moduleData, 'publications')
    case 'certificate':
      return checkObjectArrayStatus(moduleData, 'certificates')
    case 'portfolio':
      return checkObjectArrayStatus(moduleData, 'items')
    case 'self_evaluation':
    case 'cover_letter':
    case 'custom':
      return checkTextModuleStatus(moduleData)
    default:
      return 'empty'
  }
}

/**
 * 显示模块管理器
 */
const showModuleManager = () => {
  showManager.value = true
}

/**
 * 隐藏模块管理器
 */
const hideModuleManager = () => {
  showManager.value = false
}

/**
 * 处理模块更新
 * @param {Array} updatedModules - 更新后的模块配置
 */
const handleModuleUpdate = (updatedModules) => {
  editorStore.moduleConfigs.value = updatedModules
  hideModuleManager()
}
</script>

<style scoped>
.module-navigation {
  @apply flex flex-col bg-white border-b border-gray-200;
  /* 计算固定高度：头部高度 + 导航列表高度 */
  height: 156px; /* 头部52px + 导航列表94px + 间距10px */
  min-height: 156px;
  max-height: 156px;
  flex-shrink: 0; /* 不允许收缩 */
}

/* 导航头部 */
.nav-header {
  @apply flex items-center justify-between px-4 py-3 border-b border-gray-100;
  height: 52px; /* 固定头部高度 */
  flex-shrink: 0;
}

.nav-title {
  @apply text-sm font-medium text-gray-900;
}

.manage-btn {
  @apply px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200;
  @apply flex items-center space-x-2;
}

.manage-text {
  @apply text-sm font-medium;
}

/* 模块列表 */
.nav-list {
  @apply overflow-x-auto overflow-y-hidden;
  @apply flex flex-row gap-2 px-4 py-3;
  height: 94px; /* 固定高度：padding(12px*2) + 模块高度(70px) */
  min-height: 94px;
  max-height: 94px;
  flex-shrink: 0; /* 不允许收缩 */
}

/* 空状态 */
.empty-state {
  @apply text-center py-8 w-full;
}

.empty-text {
  @apply text-sm text-gray-500;
}

.nav-item {
  @apply flex-shrink-0 min-w-[105px] max-w-[123px] h-auto;
  @apply bg-white border border-gray-200 rounded-lg;
  @apply cursor-pointer transition-all duration-200;
  @apply hover:border-gray-300 hover:shadow-sm;
}

.nav-item:hover {
  @apply bg-gray-50;
}

.nav-item.active {
  @apply bg-primary-50 border-primary-400 shadow-md;
}

.nav-item.active .module-name {
  @apply text-primary-800 font-semibold;
}

.nav-item.active .module-icon {
  @apply text-primary-700;
}

.nav-item.disabled {
  @apply opacity-50 cursor-not-allowed;
}

.nav-item.disabled:hover {
  @apply bg-white border-gray-200 shadow-none;
}

/* 模块内容 */
.module-content {
  @apply px-2 py-2 flex flex-col items-center justify-center text-center;
  height: 70px; /* 固定高度 */
}

.module-header {
  @apply flex flex-col items-center justify-center h-full space-y-1;
}

.module-icon {
  @apply text-gray-500;
}

.module-name-wrapper {
  @apply flex items-center space-x-1;
}

.module-name {
  @apply text-xs text-gray-700 leading-tight;
  @apply break-words hyphens-auto;
}

.required-mark {
  @apply text-red-500 text-xs;
}





/* 响应式设计 */
@media (max-width: 768px) {
  .nav-item {
    @apply px-3 py-2;
  }
  
  .module-name {
    @apply text-xs;
  }
  
  .add-module-btn {
    @apply text-xs px-3 py-1.5;
  }
}

/* 横向滚动条样式 */
.nav-list {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.nav-list::-webkit-scrollbar {
  @apply h-2;
}

.nav-list::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.nav-list::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.nav-list::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
</style> 