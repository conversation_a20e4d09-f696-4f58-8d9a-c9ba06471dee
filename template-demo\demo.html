<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简历 - {{姓名}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', <PERSON>l, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .resume-container {
            max-width: 210mm; /* A4纸宽度 */
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 297mm; /* A4纸高度 */
        }

        /* 头部信息区域 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 20px;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #666;
        }

        .name {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .title {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            font-size: 0.9em;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            min-height: calc(297mm - 200px);
        }

        .left-column {
            width: 35%;
            background: #f8f9fa;
            padding: 30px;
        }

        .right-column {
            width: 65%;
            padding: 30px;
        }

        /* 模块标题 */
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: #764ba2;
        }

        /* 技能模块 */
        .skill-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }

        .skill-name {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .skill-level {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        /* 工作经历 */
        .experience-item {
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 1px solid #eee;
            position: relative;
        }

        .experience-item:last-child {
            border-bottom: none;
        }

        .experience-item::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 5px;
            width: 10px;
            height: 10px;
            background: #667eea;
            border-radius: 50%;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .job-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
        }

        .company-name {
            color: #667eea;
            font-weight: 500;
        }

        .date-range {
            color: #666;
            font-size: 0.9em;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }

        /* 响应式设计 */
        @media print {
            body {
                background: white;
            }
            
            .resume-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 头部信息 -->
        <header class="header">
            <div class="avatar">👤</div>
            <h1 class="name">张三</h1>
            <div class="title">高级前端开发工程师</div>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📱</span>
                    <span>138-0013-8001</span>
                </div>
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📍</span>
                    <span>北京市朝阳区</span>
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- 左侧栏 -->
            <aside class="left-column">
                <!-- 专业技能 -->
                <section class="skills-section">
                    <h2 class="section-title">专业技能</h2>
                    <div class="skill-item">
                        <div class="skill-name">JavaScript</div>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 90%"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Vue.js</div>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 85%"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">React</div>
                        <div class="skill-level">
                            <div class="skill-progress" style="width: 80%"></div>
                        </div>
                    </div>
                </section>
            </aside>

            <!-- 右侧主要内容 -->
            <main class="right-column">
                <!-- 工作经历 -->
                <section class="experience-section">
                    <h2 class="section-title">工作经历</h2>
                    
                    <div class="experience-item">
                        <div class="experience-header">
                            <div>
                                <div class="job-title">高级前端开发工程师</div>
                                <div class="company-name">ABC科技有限公司</div>
                            </div>
                            <div class="date-range">2022-03 至 现在</div>
                        </div>
                        <div class="job-description">
                            负责公司核心产品的前端架构设计和开发，带领团队完成多个重要项目的技术攻关。
                        </div>
                    </div>

                    <div class="experience-item">
                        <div class="experience-header">
                            <div>
                                <div class="job-title">前端开发工程师</div>
                                <div class="company-name">XYZ互联网公司</div>
                            </div>
                            <div class="date-range">2020-06 至 2022-02</div>
                        </div>
                        <div class="job-description">
                            负责电商平台前端功能开发和维护，优化页面性能，提升用户体验。
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>
</body>
</html> 