<template>
  <div class="admin-memberships">
    <div class="page-header">
      <h1 class="page-title">会员管理</h1>
      <p class="page-description">管理会员套餐、会员用户和会员权益</p>
    </div>

    <div class="content-area">
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <h3>会员总数</h3>
            <p class="stat-number">456</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⭐</div>
          <div class="stat-content">
            <h3>高级会员</h3>
            <p class="stat-number">89</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💎</div>
          <div class="stat-content">
            <h3>VIP会员</h3>
            <p class="stat-number">23</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-content">
            <h3>会员收入</h3>
            <p class="stat-number">¥45,678</p>
          </div>
        </div>
      </div>

      <div class="tabs-container">
        <div class="tabs">
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'members' }"
            @click="activeTab = 'members'"
          >
            会员用户
          </button>
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'packages' }"
            @click="activeTab = 'packages'"
          >
            会员套餐
          </button>
        </div>

        <div class="tab-content">
          <!-- 会员用户列表 -->
          <div v-if="activeTab === 'members'" class="members-section">
            <div class="section-header">
              <h2>会员用户</h2>
              <div class="section-actions">
                <input type="text" placeholder="搜索用户..." class="search-input">
                <select class="filter-select">
                  <option value="">全部等级</option>
                  <option value="basic">基础会员</option>
                  <option value="premium">高级会员</option>
                  <option value="vip">VIP会员</option>
                </select>
              </div>
            </div>

            <div class="table-content">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>用户ID</th>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>会员等级</th>
                    <th>到期时间</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="member in mockMembers" :key="member.id">
                    <td>{{ member.id }}</td>
                    <td>{{ member.username }}</td>
                    <td>{{ member.email }}</td>
                    <td>
                      <span class="level-badge" :class="member.level">
                        {{ getLevelText(member.level) }}
                      </span>
                    </td>
                    <td>{{ member.expireTime }}</td>
                    <td>
                      <span class="status-badge" :class="member.status">
                        {{ getStatusText(member.status) }}
                      </span>
                    </td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn-view">查看</button>
                        <button class="btn-edit">编辑</button>
                        <button class="btn-extend">续期</button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 会员套餐管理 -->
          <div v-if="activeTab === 'packages'" class="packages-section">
            <div class="section-header">
              <h2>会员套餐</h2>
              <div class="section-actions">
                <button class="btn-add">+ 添加套餐</button>
              </div>
            </div>

            <div class="packages-grid">
              <div v-for="pkg in mockPackages" :key="pkg.id" class="package-card">
                <div class="package-header">
                  <h3>{{ pkg.name }}</h3>
                  <span class="package-price">¥{{ pkg.price }}/{{ pkg.duration }}</span>
                </div>
                
                <div class="package-features">
                  <ul>
                    <li v-for="feature in pkg.features" :key="feature">{{ feature }}</li>
                  </ul>
                </div>
                
                <div class="package-stats">
                  <div class="stat">
                    <span class="label">购买数量:</span>
                    <span class="value">{{ pkg.purchaseCount }}</span>
                  </div>
                  <div class="stat">
                    <span class="label">状态:</span>
                    <span class="status-badge" :class="pkg.status">
                      {{ getStatusText(pkg.status) }}
                    </span>
                  </div>
                </div>
                
                <div class="package-actions">
                  <button class="btn-edit">编辑</button>
                  <button class="btn-toggle" :class="pkg.status">
                    {{ pkg.status === 'active' ? '禁用' : '启用' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin',
  middleware: 'admin-simple'
})

const activeTab = ref('members')

// 模拟会员用户数据
const mockMembers = ref([
  {
    id: 'U001',
    username: '张三',
    email: '<EMAIL>',
    level: 'premium',
    expireTime: '2024-12-31',
    status: 'active'
  },
  {
    id: 'U002',
    username: '李四',
    email: '<EMAIL>',
    level: 'vip',
    expireTime: '2024-06-30',
    status: 'active'
  },
  {
    id: 'U003',
    username: '王五',
    email: '<EMAIL>',
    level: 'basic',
    expireTime: '2024-03-15',
    status: 'expired'
  }
])

// 模拟会员套餐数据
const mockPackages = ref([
  {
    id: 'PKG001',
    name: '基础会员',
    price: 29,
    duration: '月',
    features: ['下载5个模板', '基础客服支持', '简历导出PDF'],
    purchaseCount: 234,
    status: 'active'
  },
  {
    id: 'PKG002',
    name: '高级会员',
    price: 99,
    duration: '月',
    features: ['下载20个模板', '优先客服支持', '简历导出多种格式', '简历在线分享'],
    purchaseCount: 89,
    status: 'active'
  },
  {
    id: 'PKG003',
    name: 'VIP会员',
    price: 299,
    duration: '年',
    features: ['无限下载模板', '专属客服', '所有高级功能', '定制模板服务'],
    purchaseCount: 23,
    status: 'active'
  }
])

const getLevelText = (level) => {
  const levelMap = {
    basic: '基础会员',
    premium: '高级会员',
    vip: 'VIP会员'
  }
  return levelMap[level] || level
}

const getStatusText = (status) => {
  const statusMap = {
    active: '正常',
    expired: '已过期',
    suspended: '已暂停',
    inactive: '未激活'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.admin-memberships {
  padding: 24px;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 12px;
}

.stat-content h3 {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.tabs-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
  padding: 16px 24px;
  border: none;
  background: none;
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-btn.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.tab-btn:hover {
  color: #3b82f6;
}

.tab-content {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 12px;
}

.search-input, .filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.search-input {
  width: 200px;
}

.filter-select {
  width: 120px;
}

.btn-add {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.btn-add:hover {
  background: #2563eb;
}

.table-content {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.data-table td {
  color: #1f2937;
  font-size: 14px;
}

.level-badge, .status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.level-badge.basic {
  background: #f3f4f6;
  color: #374151;
}

.level-badge.premium {
  background: #dbeafe;
  color: #2563eb;
}

.level-badge.vip {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.active {
  background: #d1fae5;
  color: #059669;
}

.status-badge.expired {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge.suspended {
  background: #fef3c7;
  color: #d97706;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-view, .btn-edit, .btn-extend {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.btn-view {
  background: #f3f4f6;
  color: #374151;
}

.btn-view:hover {
  background: #e5e7eb;
}

.btn-edit {
  background: #3b82f6;
  color: white;
}

.btn-edit:hover {
  background: #2563eb;
}

.btn-extend {
  background: #059669;
  color: white;
}

.btn-extend:hover {
  background: #047857;
}

.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.package-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  background: #fafafa;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.package-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.package-price {
  font-size: 16px;
  font-weight: 600;
  color: #3b82f6;
}

.package-features ul {
  list-style: none;
  padding: 0;
  margin: 0 0 16px 0;
}

.package-features li {
  padding: 4px 0;
  color: #6b7280;
  font-size: 14px;
}

.package-features li:before {
  content: '✓';
  color: #059669;
  font-weight: bold;
  margin-right: 8px;
}

.package-stats {
  margin-bottom: 16px;
}

.stat {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat .label {
  color: #6b7280;
  font-size: 14px;
}

.stat .value {
  color: #1f2937;
  font-weight: 500;
  font-size: 14px;
}

.package-actions {
  display: flex;
  gap: 8px;
}

.btn-toggle {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.btn-toggle.active {
  background: #fee2e2;
  color: #dc2626;
}

.btn-toggle.inactive {
  background: #d1fae5;
  color: #059669;
}
</style> 