package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分享更新请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareUpdateRequest", description = "分享更新请求")
public class ShareUpdateRequest {

    /**
     * 分享类型（可选）
     */
    @Schema(description = "分享类型（1:公开，2:密码保护，3:仅链接访问，4:指定用户）", example = "2")
    @Min(value = 1, message = "分享类型必须在1-4之间")
    @Max(value = 4, message = "分享类型必须在1-4之间")
    private Byte shareType;

    /**
     * 过期时间（可选）
     */
    @Schema(description = "过期时间", example = "2024-12-31 23:59:59")
    private LocalDateTime expireTime;

    /**
     * 访问密码（可选）
     */
    @Schema(description = "访问密码", example = "newpassword")
    private String password;

    /**
     * 是否允许下载（可选）
     */
    @Schema(description = "是否允许下载", example = "false")
    private Boolean allowDownload;

    /**
     * 访问次数限制（可选）
     */
    @Schema(description = "访问次数限制", example = "50")
    private Integer viewLimit;

    /**
     * 分享范围（可选）
     */
    @Schema(description = "分享范围（1:公开，2:仅链接，3:指定域名）", example = "2")
    @Min(value = 1, message = "分享范围必须在1-3之间")
    @Max(value = 3, message = "分享范围必须在1-3之间")
    private Byte shareScope;

    /**
     * 允许访问的域名白名单（可选）
     */
    @Schema(description = "允许访问的域名白名单", example = "[\"example.com\", \"*.company.com\"]")
    private List<String> allowedDomains;

    /**
     * 分享描述（可选）
     */
    @Schema(description = "分享描述", example = "更新后的分享描述")
    private String description;
} 