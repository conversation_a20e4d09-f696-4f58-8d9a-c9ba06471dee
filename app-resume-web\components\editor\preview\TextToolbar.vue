<template>
  <div 
    class="text-toolbar"
  >
    <!-- 折叠按钮 -->
    <div class="toolbar-header">
      <div class="header-left">
        <button 
          class="collapse-btn"
          @click="toggleCollapse"
          :class="{ 'collapsed': isCollapsed }"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="6,9 12,15 18,9"></polyline>
          </svg>
          <span class="collapse-text">样式工具</span>
        </button>

        <label for="color-mode-toggle" class="color-mode-switch">
          <input 
            type="checkbox"
            id="color-mode-toggle"
            :checked="isGrayscale"
            @change="toggleColorMode"
          >
          <span class="slider"></span>
          <span class="label-text">{{ isGrayscale ? '黑白' : '原色' }}</span>
        </label>
      </div>

      <div class="header-right">
        <!-- 右侧预留空间 -->
      </div>
    </div>

    <!-- 工具栏内容 -->
    <Transition name="toolbar-collapse">
      <div v-show="!isCollapsed" class="toolbar-content">
        <div class="toolbar-sections">
          <!-- 字体工具 -->
          <FontSelector 
            v-model="localSettings.fontFamily"
            @change="handleSettingsChange"
          />
          <FontSizeControl 
            v-model="localSettings.fontSize"
            @change="handleSettingsChange"
          />
          <ColorSelector
            v-model="localSettings.textColor"
            @change="handleSettingsChange"
          />

          <!-- 分割线 -->
          <div class="toolbar-divider"></div>

          <!-- 布局工具 -->
          <SpacingControl
            v-model="localSettings.spacings"
            @change="handleSettingsChange"
          />
          <MarginControl
            v-model="localSettings.margins"
            @change="handleSettingsChange"
          />
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
/**
 * 文字工具栏组件
 * @description 提供现代化的文字格式化工具，支持折叠、弹出式子菜单
 * <AUTHOR>
 * @since 2.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'
import FontSelector from './toolbar/FontSelector.vue'
import FontSizeControl from './toolbar/FontSizeControl.vue'
import ColorSelector from './toolbar/ColorSelector.vue'
import MarginControl from './toolbar/MarginControl.vue'
import SpacingControl from './toolbar/SpacingControl.vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 样式设置对象
   */
  settings: {
    type: Object,
    default: () => ({})
  },
  /**
   * 模板默认值
   */
  templateDefaults: {
    type: Object,
    default: () => null
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['settings-change', 'color-mode-change'])

// ================================
// 响应式数据
// ================================

/**
 * 黑白模式状态
 */
const isGrayscale = ref(false)

/**
 * 折叠状态（默认展开）
 */
const isCollapsed = ref(false)

/**
 * 获取默认设置 - 优先使用模板默认值
 */
const getDefaultSettings = () => ({
  fontFamily: 'system-ui',
  fontSize: '14px',
  textColor: '#333333',
  margins: {
    top: props.templateDefaults?.marginTop || 60,
    bottom: props.templateDefaults?.marginBottom || 60,
    side: props.templateDefaults?.marginSide || 60
  },
  spacings: {
    module: props.templateDefaults?.moduleSpacing || 24,
    modulePadding: props.templateDefaults?.modulePadding || 16,
    item: props.templateDefaults?.itemSpacing || 16,
    itemPadding: props.templateDefaults?.itemPadding || 12,
    line: props.templateDefaults?.lineHeight || 1.6,
    paragraph: props.templateDefaults?.paragraphSpacing || 12,
    columnGap: 40
  }
})

/**
 * 本地设置状态
 */
const localSettings = reactive({
  ...getDefaultSettings(),
  ...props.settings
})

// ================================
// 生命周期钩子
// ================================
onMounted(() => {
  // 监听外部设置变化
  watch(() => props.settings, (newSettings) => {
    Object.assign(localSettings, {
      ...getDefaultSettings(),
      ...newSettings
    })
  }, { deep: true })
  
  // 监听模板默认值变化
  watch(() => props.templateDefaults, (newDefaults) => {
    if (newDefaults) {
      console.log('🎨 TextToolbar 接收到模板默认值变化:', newDefaults)
      Object.assign(localSettings, {
        ...getDefaultSettings(),
        ...props.settings
      })
    }
  }, { deep: true })
})

onUnmounted(() => {
  // 组件清理逻辑
})

// ================================
// 事件处理方法
// ================================

/**
 * 切换折叠状态
 */
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

/**
 * 切换色彩模式
 */
const toggleColorMode = () => {
  isGrayscale.value = !isGrayscale.value
  emit('color-mode-change', isGrayscale.value)
}

/**
 * 处理设置变化
 */
const handleSettingsChange = () => {
  emit('settings-change', { ...localSettings })
}
</script>

<style scoped>
/* ================================
 * 工具栏主容器
 * ================================ */
.text-toolbar {
  @apply bg-white border-b border-gray-100;
  @apply shadow-sm;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ================================
 * 工具栏头部
 * ================================ */
.toolbar-header {
  @apply flex items-center justify-between;
  @apply px-6 py-2 border-b border-gray-50;
}

.header-left, .header-right {
  @apply flex items-center;
}

.header-left {
  @apply gap-4;
}

.collapse-btn {
  @apply flex items-center gap-2;
  @apply text-sm font-medium text-gray-700;
  @apply hover:text-gray-900;
  @apply transition-colors duration-200;
  @apply focus:outline-none;
  @apply rounded-lg px-2 py-1;
}

.collapse-btn svg {
  @apply transition-transform duration-300;
}

.collapse-btn.collapsed svg {
  @apply rotate-180;
}

.collapse-text {
  @apply select-none;
}

/* ================================
 * 色彩模式开关
 * ================================ */
.color-mode-switch {
  @apply flex items-center cursor-pointer;
  @apply text-sm text-gray-600;
}

.color-mode-switch .label-text {
  @apply ml-2 font-medium;
  @apply select-none;
}

.color-mode-switch input {
  @apply hidden;
}

.color-mode-switch .slider {
  @apply relative w-10 h-5;
  @apply bg-gray-300 rounded-full;
  @apply transition-colors duration-200;
}

.color-mode-switch .slider::before {
  content: '';
  @apply absolute top-0.5 left-0.5;
  @apply w-4 h-4;
  @apply bg-white rounded-full;
  @apply shadow;
  @apply transition-transform duration-200;
}

.color-mode-switch input:checked + .slider {
  @apply bg-primary-500;
}

.color-mode-switch input:checked + .slider::before {
  @apply transform translate-x-5;
}

/* ================================
 * 工具栏内容
 * ================================ */
.toolbar-content {
  @apply px-6 py-3;
}

.toolbar-sections {
  @apply flex items-center gap-4;
}

.toolbar-divider {
  @apply h-6 w-px bg-gray-200;
  @apply mx-2;
}

/* ================================
 * 折叠动画
 * ================================ */
.toolbar-collapse-enter-active,
.toolbar-collapse-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.toolbar-collapse-enter-from,
.toolbar-collapse-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.toolbar-collapse-enter-to,
.toolbar-collapse-leave-from {
  opacity: 1;
  max-height: 150px;
}

/* ================================
 * 响应式设计
 * ================================ */
@media (max-width: 1024px) {
  .toolbar-sections {
    @apply flex-wrap gap-4;
  }
  
  .font-selector-container {
    @apply w-40;
  }
  
  .font-tools,
  .layout-tools {
    @apply flex-wrap gap-3;
  }
  
  .layout-tools {
    @apply flex-row;
  }
  
  .toolbar-divider {
    @apply h-12;
  }
}

@media (max-width: 768px) {
  .toolbar-content {
    @apply px-4 py-2;
  }
  
  .toolbar-header {
    @apply px-4 py-2;
  }
  
  .toolbar-sections {
    @apply gap-3;
  }
  
  .font-selector-container {
    @apply w-36;
  }
  
  .toolbar-divider {
    @apply h-10;
  }
}
</style> 