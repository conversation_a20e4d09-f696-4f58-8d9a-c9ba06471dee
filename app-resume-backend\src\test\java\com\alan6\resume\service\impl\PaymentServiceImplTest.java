package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.payment.*;
import com.alan6.resume.entity.Orders;
import com.alan6.resume.service.IOrdersService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PaymentServiceImpl测试类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@ExtendWith(MockitoExtension.class)
public class PaymentServiceImplTest {

    @Mock
    private IOrdersService ordersService;

    @InjectMocks
    private PaymentServiceImpl paymentService;

    private Orders testOrder;
    private PaymentCreateRequest createRequest;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testOrder = new Orders();
        testOrder.setId(1L);
        testOrder.setOrderNo("ORD20241222123456789");
        testOrder.setUserId(1001L);
        testOrder.setOrderType((byte) 1);
        testOrder.setProductId(2L);
        testOrder.setProductName("年度会员");
        testOrder.setOriginalAmount(new BigDecimal("299.90"));
        testOrder.setDiscountAmount(BigDecimal.ZERO);
        testOrder.setFinalAmount(new BigDecimal("199.90"));
        testOrder.setPaymentMethod((byte) 1);
        testOrder.setPaymentPlatform("wechat");
        testOrder.setOrderStatus((byte) 1);
        testOrder.setCreateTime(LocalDateTime.now());
        testOrder.setUpdateTime(LocalDateTime.now());

        createRequest = new PaymentCreateRequest();
        createRequest.setOrderNo("ORD20241222123456789");
        createRequest.setPaymentMethod("wechat");
        createRequest.setPaymentType("qrcode");
        createRequest.setClientIp("*************");
        createRequest.setNotifyUrl("https://api.example.com/payment/callback/wechat");
    }

    @Test
    void testGetPaymentMethods() {
        // 执行测试
        PaymentMethodResponse response = paymentService.getPaymentMethods();

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getMethods());
        assertEquals(3, response.getMethods().size());

        // 验证微信支付
        PaymentMethodResponse.PaymentMethod wechat = response.getMethods().get(0);
        assertEquals("wechat", wechat.getCode());
        assertEquals("微信支付", wechat.getName());
        assertTrue(wechat.getEnabled());

        // 验证支付宝
        PaymentMethodResponse.PaymentMethod alipay = response.getMethods().get(1);
        assertEquals("alipay", alipay.getCode());
        assertEquals("支付宝", alipay.getName());
        assertTrue(alipay.getEnabled());

        // 验证银联支付（禁用状态）
        PaymentMethodResponse.PaymentMethod unionpay = response.getMethods().get(2);
        assertEquals("unionpay", unionpay.getCode());
        assertEquals("银联支付", unionpay.getName());
        assertFalse(unionpay.getEnabled());
    }

    @Test
    void testCreatePayment_Success() {
        // 准备Mock数据
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(testOrder);

        // 执行测试
        PaymentCreateResponse response = paymentService.createPayment(createRequest);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getPaymentId());
        assertEquals("ORD20241222123456789", response.getOrderNo());
        assertEquals("wechat", response.getPaymentMethod());
        assertEquals("qrcode", response.getPaymentType());
        assertEquals(30, response.getTimeoutMinutes());
        assertNotNull(response.getCreateTime());
        assertNotNull(response.getPaymentInfo());
        assertNotNull(response.getPaymentInfo().getQrcodeUrl());
        assertEquals(new BigDecimal("199.90"), response.getPaymentInfo().getAmount());
        assertEquals("CNY", response.getPaymentInfo().getCurrency());

        // 验证Mock调用
        verify(ordersService).getByOrderNo("ORD20241222123456789");
    }

    @Test
    void testCreatePayment_OrderNotFound() {
        // 准备Mock数据
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            paymentService.createPayment(createRequest);
        });

        assertEquals(4001, exception.getErrorCode());
        assertEquals("订单不存在", exception.getMessage());
    }

    @Test
    void testCreatePayment_OrderStatusInvalid() {
        // 准备Mock数据 - 订单状态不是待支付
        testOrder.setOrderStatus((byte) 2); // 已支付状态
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(testOrder);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            paymentService.createPayment(createRequest);
        });

        assertEquals(4004, exception.getErrorCode());
        assertEquals("订单状态异常，当前状态不支持支付", exception.getMessage());
    }

    @Test
    void testCreatePayment_UnsupportedPaymentMethod() {
        // 准备测试数据 - 不支持的支付方式
        createRequest.setPaymentMethod("bitcoin");
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(testOrder);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            paymentService.createPayment(createRequest);
        });

        assertEquals(4005, exception.getErrorCode());
        assertEquals("不支持的支付方式：bitcoin", exception.getMessage());
    }

    @Test
    void testCreatePayment_InvalidAmount() {
        // 准备Mock数据 - 订单金额为0
        testOrder.setFinalAmount(BigDecimal.ZERO);
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(testOrder);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            paymentService.createPayment(createRequest);
        });

        assertEquals(4006, exception.getErrorCode());
        assertEquals("订单金额异常", exception.getMessage());
    }

    @Test
    void testGetPaymentStatus_Success() {
        // 准备Mock数据
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(testOrder);

        // 执行测试
        PaymentStatusResponse response = paymentService.getPaymentStatus("ORD20241222123456789");

        // 验证结果
        assertNotNull(response);
        assertEquals("ORD20241222123456789", response.getOrderNo());
        assertNotNull(response.getPaymentId());
        assertEquals("unpaid", response.getPaymentStatus());
        assertEquals("pending", response.getOrderStatus());
        assertEquals("wechat", response.getPaymentMethod());
        assertEquals(new BigDecimal("199.90"), response.getAmount());
        assertEquals(BigDecimal.ZERO, response.getPaidAmount());
        assertEquals("CNY", response.getCurrency());

        // 验证Mock调用
        verify(ordersService).getByOrderNo("ORD20241222123456789");
    }

    @Test
    void testGetPaymentStatus_OrderNotFound() {
        // 准备Mock数据
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            paymentService.getPaymentStatus("ORD20241222123456789");
        });

        assertEquals(4001, exception.getErrorCode());
        assertEquals("订单不存在", exception.getMessage());
    }

    @Test
    void testHandleWechatCallback_Success() {
        // 准备Mock数据
        when(ordersService.updateOrderStatusByOrderNo(anyString(), eq((byte) 2), anyString(), anyString()))
                .thenReturn(true);

        // 执行测试
        Boolean result = paymentService.handleWechatCallback(
                "{\"orderNo\":\"ORD20241222123456789\"}", 
                "signature123", 
                "*************", 
                "nonce123", 
                "serial123");

        // 验证结果
        assertTrue(result);

        // 验证Mock调用
        verify(ordersService).updateOrderStatusByOrderNo(anyString(), eq((byte) 2), anyString(), eq("微信支付成功"));
    }

    @Test
    void testHandleAlipayCallback_Success() {
        // 准备Mock数据
        Map<String, String> params = new HashMap<>();
        params.put("sign", "signature123");
        params.put("out_trade_no", "ORD20241222123456789");
        params.put("trade_no", "ALIPAY_TXN_123456");
        params.put("trade_status", "TRADE_SUCCESS");

        when(ordersService.updateOrderStatusByOrderNo("ORD20241222123456789", (byte) 2, "ALIPAY_TXN_123456", "支付宝支付成功"))
                .thenReturn(true);

        // 执行测试
        Boolean result = paymentService.handleAlipayCallback(params);

        // 验证结果
        assertTrue(result);

        // 验证Mock调用
        verify(ordersService).updateOrderStatusByOrderNo("ORD20241222123456789", (byte) 2, "ALIPAY_TXN_123456", "支付宝支付成功");
    }

    @Test
    void testHandleAlipayCallback_TradeNotSuccess() {
        // 准备Mock数据 - 交易状态非成功
        Map<String, String> params = new HashMap<>();
        params.put("sign", "signature123");
        params.put("out_trade_no", "ORD20241222123456789");
        params.put("trade_no", "ALIPAY_TXN_123456");
        params.put("trade_status", "TRADE_CLOSED");

        // 执行测试
        Boolean result = paymentService.handleAlipayCallback(params);

        // 验证结果
        assertFalse(result);

        // 验证没有调用更新订单状态
        verify(ordersService, never()).updateOrderStatusByOrderNo(anyString(), any(), anyString(), anyString());
    }

    @Test
    void testApplyRefund_Success() {
        // 准备测试数据
        testOrder.setOrderStatus((byte) 2); // 已支付状态
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(testOrder);

        RefundRequest request = new RefundRequest();
        request.setRefundAmount(new BigDecimal("199.90"));
        request.setRefundReason("用户主动退款");
        request.setRefundReasonCode("USER_CANCEL");

        // 执行测试
        RefundResponse response = paymentService.applyRefund("ORD20241222123456789", request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getRefundId());
        assertEquals("ORD20241222123456789", response.getOrderNo());
        assertEquals(new BigDecimal("199.90"), response.getRefundAmount());
        assertEquals("processing", response.getRefundStatus());
        assertEquals("用户主动退款", response.getRefundReason());
        assertEquals("1-3个工作日", response.getEstimatedTime());
        assertNotNull(response.getCreateTime());

        // 验证Mock调用
        verify(ordersService).getByOrderNo("ORD20241222123456789");
    }

    @Test
    void testApplyRefund_OrderStatusInvalid() {
        // 准备测试数据 - 订单状态不支持退款
        testOrder.setOrderStatus((byte) 1); // 待支付状态
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(testOrder);

        RefundRequest request = new RefundRequest();
        request.setRefundAmount(new BigDecimal("199.90"));
        request.setRefundReason("用户主动退款");
        request.setRefundReasonCode("USER_CANCEL");

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            paymentService.applyRefund("ORD20241222123456789", request);
        });

        assertEquals(4008, exception.getErrorCode());
        assertEquals("订单状态不支持退款", exception.getMessage());
    }

    @Test
    void testApplyRefund_AmountExceedsOrderAmount() {
        // 准备测试数据 - 退款金额超过订单金额
        testOrder.setOrderStatus((byte) 2); // 已支付状态
        when(ordersService.getByOrderNo("ORD20241222123456789")).thenReturn(testOrder);

        RefundRequest request = new RefundRequest();
        request.setRefundAmount(new BigDecimal("299.90")); // 超过订单金额199.90
        request.setRefundReason("用户主动退款");
        request.setRefundReasonCode("USER_CANCEL");

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            paymentService.applyRefund("ORD20241222123456789", request);
        });

        assertEquals(4009, exception.getErrorCode());
        assertEquals("退款金额不能超过订单金额", exception.getMessage());
    }

    @Test
    void testGetRefundStatus_Success() {
        // 执行测试
        RefundStatusResponse response = paymentService.getRefundStatus("REF20241222123456789");

        // 验证结果
        assertNotNull(response);
        assertEquals("REF20241222123456789", response.getRefundId());
        assertNotNull(response.getOrderNo());
        assertEquals(new BigDecimal("199.90"), response.getRefundAmount());
        assertEquals("success", response.getRefundStatus());
        assertEquals("用户主动退款", response.getRefundReason());
        assertNotNull(response.getRefundTime());
        assertNotNull(response.getCreateTime());
        assertNotNull(response.getTransactionId());
        assertEquals("ORIGINAL", response.getRefundChannel());
        assertEquals("原支付账户", response.getRefundAccount());
    }

    @Test
    void testVerifySignature_WechatSuccess() {
        // 执行测试
        SignatureVerifyResponse response = paymentService.verifySignature(
                "wechat", "signature123", "*************", "nonce123", "body", "serial123");

        // 验证结果
        assertNotNull(response);
        assertEquals("wechat", response.getPaymentMethod());
        assertTrue(response.getValid());
        assertEquals("微信支付签名验证通过", response.getMessage());
        assertNotNull(response.getVerifyTime());
    }

    @Test
    void testVerifySignature_AlipaySuccess() {
        // 执行测试
        SignatureVerifyResponse response = paymentService.verifySignature(
                "alipay", "signature123", "*************", "nonce123", "body", null);

        // 验证结果
        assertNotNull(response);
        assertEquals("alipay", response.getPaymentMethod());
        assertTrue(response.getValid());
        assertEquals("支付宝签名验证通过", response.getMessage());
        assertNotNull(response.getVerifyTime());
    }

    @Test
    void testVerifySignature_UnsupportedPaymentMethod() {
        // 执行测试
        SignatureVerifyResponse response = paymentService.verifySignature(
                "bitcoin", "signature123", "*************", "nonce123", "body", null);

        // 验证结果
        assertNotNull(response);
        assertEquals("bitcoin", response.getPaymentMethod());
        assertFalse(response.getValid());
        assertEquals("不支持的支付方式", response.getMessage());
        assertNotNull(response.getVerifyTime());
    }
} 