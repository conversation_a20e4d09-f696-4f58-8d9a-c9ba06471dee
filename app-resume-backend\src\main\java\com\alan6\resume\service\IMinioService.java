package com.alan6.resume.service;

/**
 * MinIO对象存储服务接口
 * 
 * @description 定义MinIO文件上传、下载、删除等操作
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IMinioService {

    /**
     * 上传文件
     * 
     * @param fileData 文件数据
     * @param fileName 文件名
     * @param contentType 文件类型
     * @return 文件访问URL
     */
    String uploadFile(byte[] fileData, String fileName, String contentType);

    /**
     * 下载文件
     * 
     * @param fileName 文件名
     * @return 文件数据
     */
    byte[] downloadFile(String fileName);

    /**
     * 删除文件
     * 
     * @param fileName 文件名
     * @return 是否删除成功
     */
    boolean deleteFile(String fileName);

    /**
     * 检查文件是否存在
     * 
     * @param fileName 文件名
     * @return 是否存在
     */
    boolean fileExists(String fileName);

    /**
     * 获取文件URL
     * 
     * @param fileName 文件名
     * @return 文件访问URL
     */
    String getFileUrl(String fileName);

    /**
     * 获取预签名上传URL
     * 
     * @param fileName 文件名
     * @param expiry 过期时间（秒）
     * @return 预签名URL
     */
    String getPresignedUploadUrl(String fileName, int expiry);

    /**
     * 获取预签名下载URL
     * 
     * @param fileName 文件名
     * @param expiry 过期时间（秒）
     * @return 预签名URL
     */
    String getPresignedDownloadUrl(String fileName, int expiry);
} 