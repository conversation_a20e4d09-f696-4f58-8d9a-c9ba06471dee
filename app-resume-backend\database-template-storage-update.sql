-- ================================
-- 模板存储结构更新脚本
-- ================================
-- 更新时间：2025-07-09
-- 说明：支持按模板分组的目录结构 resume-templates/{template-code}/

-- 1. 为 resume_templates 表添加新的文件路径字段
ALTER TABLE `resume_templates` 
ADD COLUMN `vue_file_path` VARCHAR(500) DEFAULT NULL COMMENT 'Vue模板文件在MinIO中的路径' AFTER `local_file_path`,
ADD COLUMN `json_file_path` VARCHAR(500) DEFAULT NULL COMMENT 'JSON默认内容文件在MinIO中的路径' AFTER `vue_file_path`,
ADD COLUMN `html_file_path` VARCHAR(500) DEFAULT NULL COMMENT 'HTML预览文件在MinIO中的路径（可选）' AFTER `json_file_path`,
ADD COLUMN `storage_type` TINYINT DEFAULT 1 COMMENT '存储类型（1:MinIO对象存储,2:本地文件系统,3:混合存储）' AFTER `html_file_path`,
ADD COLUMN `file_version` VARCHAR(20) DEFAULT '1.0.0' COMMENT '文件版本号' AFTER `storage_type`,
ADD COLUMN `file_hash` VARCHAR(64) DEFAULT NULL COMMENT '文件内容MD5哈希值，用于验证完整性' AFTER `file_version`,
ADD COLUMN `file_size` BIGINT DEFAULT 0 COMMENT '文件总大小（字节）' AFTER `file_hash`,
ADD COLUMN `last_sync_time` DATETIME DEFAULT NULL COMMENT '最后同步时间（MinIO与本地之间）' AFTER `file_size`;

-- 2. 添加索引优化查询性能
ALTER TABLE `resume_templates` 
ADD INDEX `idx_storage_type` (`storage_type`),
ADD INDEX `idx_file_version` (`file_version`),
ADD INDEX `idx_last_sync_time` (`last_sync_time`);

-- 3. 更新现有数据的存储路径（如果有数据的话）
-- 注意：这个脚本假设现有的 template_code 字段已经存在
UPDATE `resume_templates` 
SET 
    `vue_file_path` = CONCAT('resume-templates/', `template_code`, '/', `template_code`, '.vue'),
    `json_file_path` = CONCAT('resume-templates/', `template_code`, '/', `template_code`, '.json'),
    `html_file_path` = CONCAT('resume-templates/', `template_code`, '/', `template_code`, '.html'),
    `storage_type` = 1,
    `file_version` = '1.0.0'
WHERE `template_code` IS NOT NULL AND `template_code` != '';

-- 4. 创建模板文件存储配置表（可选，用于管理不同存储策略）
CREATE TABLE `template_storage_configs` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` VARCHAR(100) NOT NULL COMMENT '配置名称',
  `storage_type` TINYINT NOT NULL COMMENT '存储类型（1:MinIO,2:本地,3:混合）',
  `bucket_name` VARCHAR(100) DEFAULT NULL COMMENT 'MinIO桶名称',
  `base_path` VARCHAR(500) DEFAULT NULL COMMENT '基础路径',
  `max_file_size` BIGINT DEFAULT 10485760 COMMENT '最大文件大小（字节，默认10MB）',
  `allowed_extensions` JSON DEFAULT NULL COMMENT '允许的文件扩展名',
  `is_default` TINYINT DEFAULT 0 COMMENT '是否为默认配置',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_name` (`config_name`),
  KEY `idx_storage_type` (`storage_type`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板文件存储配置表';

-- 5. 插入默认存储配置
INSERT INTO `template_storage_configs` 
(`config_name`, `storage_type`, `bucket_name`, `base_path`, `max_file_size`, `allowed_extensions`, `is_default`, `status`) 
VALUES 
('MinIO模板存储', 1, 'resume-templates', '', 10485760, '["vue", "json", "html"]', 1, 1),
('本地文件存储', 2, NULL, '/opt/resume-templates', 10485760, '["vue", "json", "html"]', 0, 1),
('混合存储', 3, 'resume-templates', '', 10485760, '["vue", "json", "html"]', 0, 1);

-- 6. 创建模板文件同步日志表（用于记录文件同步操作）
CREATE TABLE `template_sync_logs` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `template_id` BIGINT NOT NULL COMMENT '模板ID',
  `template_code` VARCHAR(50) NOT NULL COMMENT '模板代码',
  `sync_type` TINYINT NOT NULL COMMENT '同步类型（1:上传,2:下载,3:删除,4:更新）',
  `source_type` TINYINT NOT NULL COMMENT '源类型（1:MinIO,2:本地）',
  `target_type` TINYINT NOT NULL COMMENT '目标类型（1:MinIO,2:本地）',
  `file_type` VARCHAR(10) NOT NULL COMMENT '文件类型（vue,json,html）',
  `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `file_size` BIGINT DEFAULT 0 COMMENT '文件大小',
  `file_hash` VARCHAR(64) DEFAULT NULL COMMENT '文件哈希值',
  `sync_status` TINYINT DEFAULT 0 COMMENT '同步状态（0:进行中,1:成功,2:失败）',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息（如果同步失败）',
  `sync_duration` INT DEFAULT 0 COMMENT '同步耗时（毫秒）',
  `operator_id` BIGINT DEFAULT NULL COMMENT '操作员ID',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_template_code` (`template_code`),
  KEY `idx_sync_type` (`sync_type`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板文件同步日志表';

-- 7. 创建视图，方便查询模板的完整信息
CREATE VIEW `v_template_files` AS
SELECT 
    rt.`id`,
    rt.`name`,
    rt.`template_code`,
    rt.`description`,
    rt.`preview_image_url`,
    rt.`vue_file_path`,
    rt.`json_file_path`,
    rt.`html_file_path`,
    rt.`storage_type`,
    rt.`file_version`,
    rt.`file_hash`,
    rt.`file_size`,
    rt.`last_sync_time`,
    rt.`category_id`,
    tc.`name` AS `category_name`,
    rt.`industry`,
    rt.`style`,
    rt.`color_scheme`,
    rt.`is_premium`,
    rt.`price`,
    rt.`use_count`,
    rt.`status`,
    rt.`create_time`,
    rt.`update_time`,
    CASE 
        WHEN rt.`storage_type` = 1 THEN 'MinIO对象存储'
        WHEN rt.`storage_type` = 2 THEN '本地文件系统'
        WHEN rt.`storage_type` = 3 THEN '混合存储'
        ELSE '未知'
    END AS `storage_type_name`,
    CASE 
        WHEN rt.`vue_file_path` IS NOT NULL AND rt.`json_file_path` IS NOT NULL THEN 1
        ELSE 0
    END AS `files_complete`
FROM `resume_templates` rt
LEFT JOIN `template_categories` tc ON rt.`category_id` = tc.`id`
WHERE rt.`is_deleted` = 0;

-- 8. 添加触发器，自动更新文件路径（当template_code变更时）
DELIMITER $$

CREATE TRIGGER `tr_update_template_file_paths`
BEFORE UPDATE ON `resume_templates`
FOR EACH ROW
BEGIN
    -- 如果template_code发生变化，自动更新文件路径
    IF NEW.`template_code` != OLD.`template_code` AND NEW.`template_code` IS NOT NULL THEN
        SET NEW.`vue_file_path` = CONCAT('resume-templates/', NEW.`template_code`, '/', NEW.`template_code`, '.vue');
        SET NEW.`json_file_path` = CONCAT('resume-templates/', NEW.`template_code`, '/', NEW.`template_code`, '.json');
        SET NEW.`html_file_path` = CONCAT('resume-templates/', NEW.`template_code`, '/', NEW.`template_code`, '.html');
        SET NEW.`last_sync_time` = NULL; -- 重置同步时间，表示需要重新同步
    END IF;
END$$

DELIMITER ;

-- 9. 创建存储过程，批量更新模板文件路径
DELIMITER $$

CREATE PROCEDURE `sp_batch_update_template_paths`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id BIGINT;
    DECLARE v_template_code VARCHAR(50);
    
    DECLARE cur CURSOR FOR 
        SELECT `id`, `template_code` 
        FROM `resume_templates` 
        WHERE `template_code` IS NOT NULL 
        AND `template_code` != ''
        AND (`vue_file_path` IS NULL OR `json_file_path` IS NULL);
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_id, v_template_code;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE `resume_templates` 
        SET 
            `vue_file_path` = CONCAT('resume-templates/', v_template_code, '/', v_template_code, '.vue'),
            `json_file_path` = CONCAT('resume-templates/', v_template_code, '/', v_template_code, '.json'),
            `html_file_path` = CONCAT('resume-templates/', v_template_code, '/', v_template_code, '.html'),
            `storage_type` = 1,
            `file_version` = '1.0.0'
        WHERE `id` = v_id;
    END LOOP;
    
    CLOSE cur;
    
    SELECT CONCAT('批量更新完成，影响行数：', ROW_COUNT()) AS result;
END$$

DELIMITER ;

-- 10. 创建函数，根据模板代码生成文件路径
DELIMITER $$

CREATE FUNCTION `fn_get_template_file_path`(template_code VARCHAR(50), file_type VARCHAR(10))
RETURNS VARCHAR(500)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE file_path VARCHAR(500);
    
    IF template_code IS NULL OR template_code = '' OR file_type IS NULL THEN
        RETURN NULL;
    END IF;
    
    SET file_path = CONCAT('resume-templates/', template_code, '/', template_code, '.', LOWER(file_type));
    
    RETURN file_path;
END$$

DELIMITER ;

-- 11. 执行数据完整性检查
-- 检查是否有重复的template_code
SELECT 
    `template_code`, 
    COUNT(*) as count 
FROM `resume_templates` 
WHERE `is_deleted` = 0 
GROUP BY `template_code` 
HAVING COUNT(*) > 1;

-- 检查文件路径是否正确设置
SELECT 
    `id`,
    `template_code`,
    `vue_file_path`,
    `json_file_path`,
    `html_file_path`,
    CASE 
        WHEN `vue_file_path` IS NULL THEN '缺少Vue文件路径'
        WHEN `json_file_path` IS NULL THEN '缺少JSON文件路径'
        ELSE '路径完整'
    END AS path_status
FROM `resume_templates` 
WHERE `is_deleted` = 0 
ORDER BY `id`;

-- 12. 清理和优化
-- 分析表以优化查询性能
ANALYZE TABLE `resume_templates`;
ANALYZE TABLE `template_storage_configs`;
ANALYZE TABLE `template_sync_logs`;

-- 更新完成提示
SELECT 
    '模板存储结构更新完成！' AS message,
    '新的存储结构：resume-templates/{template-code}/' AS storage_structure,
    '支持文件类型：Vue, JSON, HTML' AS supported_files,
    NOW() AS update_time; 