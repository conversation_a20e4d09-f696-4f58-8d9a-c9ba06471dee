HTML简历模板开发规范

一、简历模块及字段规范

1. 基本信息（data-module="basic_info"）
字段	绑定值（data-vue-text）
姓名	resumeData.basic_info.name
性别	resumeData.basic_info.gender
年龄	resumeData.basic_info.age
所在城市	resumeData.basic_info.currentCity
意向城市	resumeData.basic_info.intendedCity
电话	resumeData.basic_info.phone
邮箱	resumeData.basic_info.email
居住地址	resumeData.basic_info.address
当前状态	resumeData.basic_info.currentStatus
期望岗位	resumeData.basic_info.expectedPosition
期望薪资	resumeData.basic_info.expectedSalary
头像	resumeData.basic_info.photo
微信号	resumeData.basic_info.wechat
个人网站	resumeData.basic_info.website
GitHub	resumeData.basic_info.github
身高	resumeData.basic_info.height
体重	resumeData.basic_info.weight
政治面貌	resumeData.basic_info.politicalStatus
婚姻状况	resumeData.basic_info.maritalStatus
籍贯	resumeData.basic_info.hometown
民族	resumeData.basic_info.ethnicity

2. 教育经历（data-module="education"）
字段和绑定值如下：
学校	education.school
专业	education.major
学历	education.degree
开始时间	education.start_date
结束时间	education.end_date
描述	education.description

3. 工作经历（data-module="work_experience"）
字段和绑定值如下：
公司	work.company
职位	work.position
开始时间	work.start_date
结束时间	work.end_date
描述	work.description

4. 项目经历（data-module="project"）
字段和绑定值如下：
项目名称	project.name
角色	project.role
技术栈	project.technologies
描述	project.description

5. 技能特长（data-module="skills"）
字段和绑定值如下：
技能名	skill.name
熟练度	skill.level

6. 获奖荣誉（data-module="award"）
字段和绑定值如下：
奖项名称	award.name
获奖时间	award.date
颁发机构	award.organization
奖项级别	award.level
详细描述	award.description

7. 自我评价（data-module="self_evaluation"）
字段和绑定值如下：
自我评价	resumeData.self_evaluation.content

二、HTML结构开发规范

1. 页面基础结构
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简历标题</title>
  <style>/* CSS样式 */</style>
</head>
<body>
  <div class="resume-template" data-module-root="true">
    <!-- 简历模块 -->
  </div>
</body>
</html>

必须包含：
<!DOCTYPE html>
<meta charset="UTF-8">
<div class="resume-template" data-module-root="true"> 作为根容器

2. 模块标识（必须添加 data-module）
模块	属性值
基本信息	data-module="basic_info"
教育经历	data-module="education"
工作经历	data-module="work_experience"
项目经历	data-module="project"
技能特长	data-module="skills"
获奖荣誉	data-module="award"
自我评价	data-module="self_evaluation"

三、数据绑定规范

1. 普通字段绑定
使用 data-vue-text="..."
示例：
<span data-vue-text="resumeData.basic_info.name"></span>

2. 列表数据绑定（数组类型）
<!-- 教育经历示例 -->
<div data-vue-if="resumeData.education && resumeData.education.length > 0">
  <div data-vue-for="education in resumeData.education" data-vue-key="education.id">
    <div data-vue-text="education.school"></div>
    <!-- 其他字段 -->
  </div>
</div>

<!-- 工作经历示例 -->
<div data-vue-if="resumeData.work_experience && resumeData.work_experience.length > 0">
  <div data-vue-for="work in resumeData.work_experience" data-vue-key="work.id">
    <div data-vue-text="work.company"></div>
    <!-- 其他字段 -->
  </div>
</div>

<!-- 项目经历示例 -->
<div data-vue-if="resumeData.project && resumeData.project.length > 0">
  <div data-vue-for="project in resumeData.project" data-vue-key="project.id">
    <div data-vue-text="project.name"></div>
    <!-- 其他字段 -->
  </div>
</div>

<!-- 技能示例 -->
<div data-vue-if="resumeData.skills && resumeData.skills.length > 0">
  <div data-vue-for="skill in resumeData.skills" data-vue-key="skill.id">
    <div data-vue-text="skill.name"></div>
    <!-- 其他字段 -->
  </div>
</div>

<!-- 获奖荣誉示例 -->
<div data-vue-if="resumeData.award && resumeData.award.length > 0">
  <div data-vue-for="award in resumeData.award" data-vue-key="award.id">
    <div data-vue-text="award.name"></div>
    <!-- 其他字段 -->
  </div>
</div>

3. 条件渲染（可选字段或模块）
<!-- 基本信息可选字段 -->
<div data-vue-if="resumeData.basic_info.photo">
  <img :src="resumeData.basic_info.photo" />
</div>

<div data-vue-if="resumeData.basic_info.age">
  <span data-vue-text="resumeData.basic_info.age"></span>
</div>

<!-- 模块级条件渲染 -->
<section data-module="education" data-vue-if="resumeData.education && resumeData.education.length > 0">
  <h2>教育经历</h2>
  <!-- 模块内容 -->
</section>

四、CSS样式类命名规范

模块类名
.resume-section { }        /* 所有模块容器 */
.resume-item { }           /* 模块中每一项 */
.item-title { }            /* 标题项 */
.item-date { }             /* 日期项 */
.item-desc { }             /* 描述项 */

特定模块
.basic-info { }
.basic-info .name { }
.basic-info .contact { }

.skills { }
.skill-tag { }

五、CSS变量规范（推荐使用）
用于统一控制主题色、间距、字号等样式参数：

:root {
  /* 字体设置 */
  --resume-font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --resume-font-size: 14px;
  --resume-line-height: 1.6;
  
  /* 颜色设置 */
  --resume-primary-color: #3b82f6;
  --resume-text-color: #333333;
  --resume-secondary-color: #666666;
  
  /* 页面边距 */
  --resume-margin-top: 60px;
  --resume-margin-bottom: 60px;
  --resume-margin-left: 60px;
  --resume-margin-right: 60px;
  
  /* 模块间距 */
  --resume-module-spacing: 24px;
  --resume-item-spacing: 16px;
  --resume-paragraph-spacing: 12px;
  --resume-column-gap: 40px;
  
  /* 内边距 */
  --resume-module-padding: 16px;
  --resume-item-padding: 12px;
} 