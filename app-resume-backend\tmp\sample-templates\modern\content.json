{"templateCategory": "modern", "templateName": "现代简约模板", "description": "现代简约风格的简历模板，适合各类职业人士", "version": "1.0.0", "defaultContent": {"basicInfo": {"name": "张三", "title": "前端开发工程师", "phone": "13800138000", "email": "<EMAIL>", "address": "北京市朝阳区", "website": "https://zhangsan.dev", "photo": ""}, "selfEvaluation": {"content": "我是一名专业的前端开发工程师，拥有5年的Web开发经验。熟练掌握主流前端技术栈，包括Vue.js、React、TypeScript等。具有良好的代码规范和团队协作能力，热爱学习新技术，善于解决复杂的技术问题。在项目开发中注重用户体验和代码质量，能够独立完成从需求分析到项目上线的完整开发流程。"}, "workExperiences": [{"id": "work_1", "company": "字节跳动", "position": "高级前端开发工程师", "startDate": "2022-07", "endDate": "至今", "location": "北京", "description": "负责公司核心产品的前端开发工作，使用Vue.js、React等技术栈开发高质量的Web应用。参与架构设计和技术选型，优化项目性能和用户体验。", "achievements": ["主导重构核心业务模块，页面加载速度提升40%", "建立前端代码规范和自动化测试流程，减少线上bug率60%", "指导新人开发，团队整体开发效率提升30%"]}, {"id": "work_2", "company": "腾讯科技", "position": "前端开发工程师", "startDate": "2020-03", "endDate": "2022-06", "location": "深圳", "description": "参与多个ToB产品的前端开发，负责组件库建设和维护。熟练使用Vue生态系统进行大型项目开发。", "achievements": ["开发通用组件库，被10+项目复用，提升开发效率50%", "优化打包构建流程，构建时间减少70%", "参与微前端架构设计，支持多团队协作开发"]}], "projects": [{"id": "project_1", "name": "在线简历编辑器", "role": "前端负责人", "startDate": "2023-01", "endDate": "2023-12", "technology": "Vue.js 3, Nuxt.js, TypeScript, Tailwind CSS", "description": "设计并开发了一个功能完善的在线简历编辑器，支持多种模板、实时预览、PDF导出等功能。项目采用现代化的前端技术栈，具有良好的用户体验和性能表现。", "url": "https://resume-editor.example.com"}, {"id": "project_2", "name": "企业级管理后台", "role": "核心开发者", "startDate": "2022-03", "endDate": "2022-10", "technology": "React, Ant Design, Redux, TypeScript", "description": "开发企业级管理后台系统，包含用户管理、权限控制、数据统计等功能。采用微服务架构，支持多租户模式。", "url": "https://admin.example.com"}], "educations": [{"id": "edu_1", "school": "北京大学", "degree": "本科", "major": "计算机科学与技术", "startDate": "2018-09", "endDate": "2022-06", "gpa": "3.8/4.0", "description": "主修课程：数据结构与算法、操作系统、计算机网络、数据库系统、软件工程等。获得校级优秀学生奖学金，参与多项科研项目。"}], "skills": [{"name": "JavaScript", "level": 95}, {"name": "Vue.js", "level": 90}, {"name": "React", "level": 85}, {"name": "TypeScript", "level": 85}, {"name": "Node.js", "level": 80}, {"name": "Webpack", "level": 75}, {"name": "CSS/Sass", "level": 90}, {"name": "Git", "level": 85}], "awards": [{"id": "award_1", "name": "优秀员工奖", "date": "2023-12", "issuer": "字节跳动", "description": "因在项目开发中的突出贡献和技术创新获得年度优秀员工奖"}, {"id": "award_2", "name": "校级优秀学生奖学金", "date": "2021-06", "issuer": "北京大学", "description": "学习成绩优异，综合素质突出，获得校级优秀学生奖学金"}]}, "metadata": {"createdAt": "2025-01-09T12:00:00Z", "updatedAt": "2025-01-09T12:00:00Z", "author": "火花简历团队", "tags": ["现代", "简约", "技术", "商务"], "targetIndustries": ["互联网", "科技", "软件"], "targetPositions": ["前端开发", "全栈开发", "软件工程师"], "language": "zh-CN"}}