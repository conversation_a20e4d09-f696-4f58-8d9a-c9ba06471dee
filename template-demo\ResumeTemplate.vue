<template>
  <div ref="templateRef" class="resume-template" :class="{ 'preview-mode': !isDraggable }">
    
    <!-- 占位页面内容 -->
    <div v-if="resumeData.isPlaceholderPage" class="placeholder-page">
      <div class="placeholder-content">
        <div class="placeholder-icon">📄</div>
        <h3>第 {{ resumeData.pageNumber }} 页</h3>
        <p class="placeholder-text">此页将显示超出第一页的内容</p>
        <p class="placeholder-note">（内容分页功能开发中）</p>
      </div>
    </div>
    
    <!-- 正常简历内容 -->
    <template v-else>
      <!-- 头部信息区域 - 只在基本信息可见时显示 -->
      <ResumeHeader
        v-if="!isPaginationMode || visibleModules.includes('basic')"
        :basic-info="mergedResumeData.basicInfo"
        :text-style="textStyle"
        :is-draggable="isDraggable"
        @update-basic-info="handleBasicInfoUpdate"
      />

    <!-- 主体内容区域 -->
    <div class="main-content">
      
      <!-- 左侧栏 -->
      <aside class="left-column">
        <!-- 动态渲染左侧模块 -->
        <template v-for="module in leftColumnModules" :key="module.id">
          
          <!-- 专业技能模块 -->
          <section v-if="module.id === 'skills'" class="skills-section module-container">
          <!-- 模块操作按钮 -->
          <div v-if="isDraggable" class="module-actions">
            <button @click="moveModuleUp('skills')" class="module-action-btn" title="模块上移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="moveModuleDown('skills')" class="module-action-btn" title="模块下移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="deleteModule('skills')" class="module-action-btn delete" title="删除模块">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>

          <h2 class="section-title" :style="titleStyle">专业技能</h2>
          <div class="skills-list">
            <div 
              v-for="(skill, index) in mergedResumeData.skills" 
              :key="skill.id || index"
              class="skill-item"
              :class="{ 'draggable': isDraggable }"
              @mouseenter="hoveredSkill = index"
              @mouseleave="hoveredSkill = -1"
            >
              <!-- 技能项操作按钮 -->
              <div v-if="isDraggable && hoveredSkill === index" class="skill-actions">
                <button 
                  @click="moveSkillUp(index)" 
                  :disabled="index === 0"
                  class="skill-action-btn"
                  title="上移"
                >
                  ↑
                </button>
                <button 
                  @click="moveSkillDown(index)" 
                  :disabled="index === mergedResumeData.skills.length - 1"
                  class="skill-action-btn"
                  title="下移"
                >
                  ↓
                </button>
                <button 
                  @click="deleteSkill(index)"
                  class="skill-action-btn delete"
                  title="删除"
                >
                  ×
                </button>
              </div>
              
              <!-- 技能名称 -->
              <div class="skill-name">
                {{ skill.name }}
              </div>
              
              <!-- 技能等级进度条 -->
              <div class="skill-progress-container">
                <div class="skill-progress-track">
                  <div 
                    class="skill-progress-bar" 
                    :style="getSkillProgressStyle(skill.level)"
                  ></div>
                </div>
                <div class="skill-level-text">{{ skill.level }}%</div>
              </div>

              <!-- 技能等级只读显示 -->
            </div>

            <!-- 添加技能功能已移至左侧编辑区 -->
          </div>
        </section>

        </template>
      </aside>

      <!-- 右侧主要内容 -->
      <main class="right-column">
        <!-- 动态渲染右侧模块 -->
        <template v-for="module in rightColumnModules" :key="module.id">
        
          <!-- 工作经历模块 -->
          <section v-if="module.id === 'work'" class="work-section module-container">
          <!-- 模块操作按钮 -->
          <div v-if="isDraggable" class="module-actions">
            <button @click="moveModuleUp('work')" class="module-action-btn" title="模块上移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="moveModuleDown('work')" class="module-action-btn" title="模块下移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="deleteModule('work')" class="module-action-btn delete" title="删除模块">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>

          <h2 class="section-title" :style="titleStyle">工作经历</h2>
          <div class="work-timeline">
            <div 
              v-for="(experience, index) in mergedResumeData.workExperiences" 
              :key="experience.id || index"
              class="work-item"
              :class="{ 'draggable': isDraggable }"
              @mouseenter="hoveredExperience = index"
              @mouseleave="hoveredExperience = -1"
            >
              <!-- 时间线节点 -->
              <div class="timeline-node"></div>
              
              <!-- 工作经历项操作按钮 -->
              <div v-if="isDraggable && hoveredExperience === index" class="work-actions">
                <button 
                  @click="moveExperienceUp(index)" 
                  :disabled="index === 0"
                  class="work-action-btn"
                  title="上移"
                >
                  ↑
                </button>
                <button 
                  @click="moveExperienceDown(index)" 
                  :disabled="index === mergedResumeData.workExperiences.length - 1"
                  class="work-action-btn"
                  title="下移"
                >
                  ↓
                </button>
                <button 
                  @click="deleteExperience(index)"
                  class="work-action-btn delete"
                  title="删除"
                >
                  ×
                </button>
              </div>

              <!-- 工作内容 -->
              <div class="work-content">
                <div class="work-header">
                  <h3 class="job-title">
                    {{ experience.position }}
                  </h3>
                  <div class="company-name">
                    {{ experience.company }}
                  </div>
                  <div class="work-period">
                    {{ formatWorkPeriod(experience.startDate, experience.endDate) }}
                  </div>
                </div>
                <div class="work-description">
                  {{ experience.description }}
                </div>
              </div>
            </div>

            <!-- 添加工作经历功能已移至左侧编辑区 -->
          </div>
        </section>

          <!-- 教育经历模块 -->
          <section v-if="module.id === 'education'" class="education-section module-container">
          <!-- 模块操作按钮 -->
          <div v-if="isDraggable" class="module-actions">
            <button @click="moveModuleUp('education')" class="module-action-btn" title="模块上移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="moveModuleDown('education')" class="module-action-btn" title="模块下移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="deleteModule('education')" class="module-action-btn delete" title="删除模块">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>

          <h2 class="section-title" :style="titleStyle">教育经历</h2>
          <div class="education-list">
            <div 
              v-for="(education, index) in mergedResumeData.educations" 
              :key="education.id || index"
              class="education-item"
              :class="{ 'draggable': isDraggable }"
            >
              <!-- 教育经历操作按钮 -->
              <div v-if="isDraggable" class="education-actions">
                <button 
                  @click="moveEducationUp(index)" 
                  :disabled="index === 0"
                  class="education-action-btn"
                  title="上移"
                >
                  ↑
                </button>
                <button 
                  @click="moveEducationDown(index)" 
                  :disabled="index === mergedResumeData.educations.length - 1"
                  class="education-action-btn"
                  title="下移"
                >
                  ↓
                </button>
                <button 
                  @click="deleteEducation(index)"
                  class="education-action-btn delete"
                  title="删除"
                >
                  ×
                </button>
              </div>

              <div class="education-content">
                <div class="education-header">
                  <h3 class="school-name">{{ education.school }}</h3>
                  <div class="degree-info">
                    <span class="degree">{{ education.degree }}</span>
                    <span class="major">{{ education.major }}</span>
                  </div>
                  <div class="education-period">
                    {{ formatWorkPeriod(education.startDate, education.endDate) }}
                  </div>
                </div>
                <div v-if="education.description" class="education-description">
                  {{ education.description }}
                </div>
              </div>
            </div>
          </div>
        </section>

          <!-- 项目经历模块 -->
          <section v-if="module.id === 'project'" class="project-section module-container">
          <!-- 模块操作按钮 -->
          <div v-if="isDraggable" class="module-actions">
            <button @click="moveModuleUp('project')" class="module-action-btn" title="模块上移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="moveModuleDown('project')" class="module-action-btn" title="模块下移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="deleteModule('project')" class="module-action-btn delete" title="删除模块">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>

          <h2 class="section-title" :style="titleStyle">项目经历</h2>
          <div class="project-list">
            <div 
              v-for="(project, index) in mergedResumeData.projects" 
              :key="project.id || index"
              class="project-item"
              :class="{ 'draggable': isDraggable }"
            >
              <!-- 项目经历操作按钮 -->
              <div v-if="isDraggable" class="project-actions">
                <button 
                  @click="moveProjectUp(index)" 
                  :disabled="index === 0"
                  class="project-action-btn"
                  title="上移"
                >
                  ↑
                </button>
                <button 
                  @click="moveProjectDown(index)" 
                  :disabled="index === mergedResumeData.projects.length - 1"
                  class="project-action-btn"
                  title="下移"
                >
                  ↓
                </button>
                <button 
                  @click="deleteProject(index)"
                  class="project-action-btn delete"
                  title="删除"
                >
                  ×
                </button>
              </div>

              <div class="project-content">
                <div class="project-header">
                  <h3 class="project-name">{{ project.name }}</h3>
                  <div class="project-role">{{ project.role }}</div>
                  <div class="project-period">
                    {{ formatWorkPeriod(project.startDate, project.endDate) }}
                  </div>
                  <div v-if="project.technology" class="project-tech">
                    技术栈：{{ project.technology }}
                  </div>
                </div>
                <div v-if="project.description" class="project-description">
                  {{ project.description }}
                </div>
              </div>
            </div>
          </div>
        </section>

          <!-- 自我评价模块 -->
          <section v-if="module.id === 'self-evaluation'" class="self-evaluation-section module-container">
          <!-- 模块操作按钮 -->
          <div v-if="isDraggable" class="module-actions">
            <button @click="moveModuleUp('self-evaluation')" class="module-action-btn" title="模块上移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="moveModuleDown('self-evaluation')" class="module-action-btn" title="模块下移">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
              </svg>
            </button>
            <button @click="deleteModule('self-evaluation')" class="module-action-btn delete" title="删除模块">
              <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>

          <h2 class="section-title" :style="titleStyle">自我评价</h2>
          <div class="self-evaluation-content">
            <p>{{ mergedResumeData.selfEvaluation }}</p>
          </div>
        </section>

        </template>
      </main>
    </div>
    </template>
  </div>
</template>

<script setup>
/**
 * 简历模板组件
 * @description 现代化的简历预览模板，支持交互和动画效果
 * <AUTHOR>
 */

// ================================
// 导入依赖
// ================================
// 导入依赖和组件
// ================================
import { ref } from 'vue'
import { useResumeTemplateBase, TEMPLATE_STANDARD_PROPS, TEMPLATE_STANDARD_EVENTS } from '~/composables/template/useResumeTemplateBase'

// 导入子组件
import ResumeHeader from './components/ResumeHeader.vue'

// ================================
// 组件属性
// ================================
const props = defineProps(TEMPLATE_STANDARD_PROPS)

// ================================
// 组件事件
// ================================
const emit = defineEmits(TEMPLATE_STANDARD_EVENTS)

// ================================
// 模板引用
// ================================
const templateRef = ref(null)

// ================================
// 使用基础模板功能
// ================================
/**
 * 基础模板功能
 * @description 通过useResumeTemplateBase获取所有标准功能
 */
const templateBase = useResumeTemplateBase(props, emit)

// 解构所有需要的功能，保持与原来的接口一致
const {
  // 数据相关
  mergedResumeData,
  sortedModules,
  leftColumnModules,
  rightColumnModules,
  formatWorkPeriod,
  
  // 操作相关
  hoveredSkill,
  hoveredExperience,
  hoveredEducation,
  hoveredProject,
  moveModuleUp,
  moveModuleDown,
  deleteModule,
  moveSkillUp,
  moveSkillDown,
  deleteSkill,
  moveExperienceUp,
  moveExperienceDown,
  deleteExperience,
  moveEducationUp,
  moveEducationDown,
  deleteEducation,
  moveProjectUp,
  moveProjectDown,
  deleteProject,
  handleBasicInfoUpdate,
  
  // 样式相关
  titleStyle,
  getSkillProgressStyle,
  
  // 工具方法
  debugInfo
} = templateBase

// ================================
// 模板特有逻辑（如果有的话）
// ================================

/**
 * 模板特有的计算属性或方法可以在这里定义
 * 大部分通用功能已经通过useResumeTemplateBase提供
 */

// 暴露模板引用和调试信息供父组件使用
defineExpose({
  templateRef,
  debugInfo
})

// ================================
// 高度监听和自然流动支持
// ================================

import { watch, nextTick, onMounted } from 'vue'

/**
 * 监听内容变化并报告高度
 */
const reportHeight = () => {
  if (props.isNaturalFlow && templateRef.value) {
    nextTick(() => {
      const height = templateRef.value.scrollHeight
      console.log('📐 ResumeTemplate 内容高度变化:', height)
      emit('content-height-change', height)
    })
  }
}

// 监听数据变化
watch(() => mergedResumeData.value, reportHeight, { deep: true, immediate: true })

// 组件挂载后报告初始高度
onMounted(() => {
  if (props.isNaturalFlow) {
    setTimeout(reportHeight, 100) // 等待DOM完全渲染
  }
})
</script>

<style scoped>
/* ================================
 * 模板默认样式变量定义
 * ================================ */
:root {
  /* 模板默认样式变量 - 以 --template-default- 为前缀 */
  --template-default-module-spacing: 24px;    /* 模块间距 */
  --template-default-module-padding: 16px;    /* 模块内边距 */
  --template-default-item-spacing: 16px;      /* 项目间距 */
  --template-default-item-padding: 12px;      /* 项目内边距 */
  --template-default-margin-top: 60px;        /* 上边距 */
  --template-default-margin-bottom: 60px;     /* 下边距 */
  --template-default-margin-side: 60px;       /* 左右边距 */
  --template-default-line-height: 1.6;        /* 行距 */
  --template-default-paragraph-spacing: 12px; /* 段落间距 */
}

/* ================================
 * 标准CSS变量定义 - 所有简历模板必须使用相同的变量名
 * 使用双重fallback：用户设置 -> 模板默认值
 * ================================ */

/* ================================
 * 主容器样式
 * ================================ */
.resume-template {
  @apply w-full transition-all duration-300;
  
  /* 设置整个简历的边距 - 这是A4纸边界距离 */
  padding: var(--resume-margin-top, var(--template-default-margin-top)) var(--resume-margin-right, var(--template-default-margin-side)) var(--resume-margin-bottom, var(--template-default-margin-bottom)) var(--resume-margin-left, var(--template-default-margin-side));
  
  /* 在自然流动模式下，让外层容器控制尺寸和背景 */
  width: 100%;
  min-height: auto;
  margin: 0;
  line-height: var(--resume-line-height, var(--template-default-line-height));
}

.resume-template.preview-mode {
  @apply cursor-default;
}

/* 移除悬停阴影效果，因为外层容器已经有阴影 */

/* ================================
 * 主体内容布局
 * ================================ */
.main-content {
  @apply flex min-h-0 flex-1;
  gap: var(--resume-column-gap, 40px);
}

.left-column {
  @apply w-2/5 bg-gradient-to-b from-gray-50 to-gray-100/50;
  @apply flex flex-col;
  gap: var(--resume-module-spacing, var(--template-default-module-spacing));
  /* 移除padding，边距由整个模板控制 */
  padding: 0;
  
  /* 渐变背景增强质感 */
  background: linear-gradient(
    135deg,
    #f8fafc 0%,
    #f1f5f9 50%,
    #e2e8f0 100%
  );
}

.right-column {
  @apply w-3/5 bg-white;
  @apply flex flex-col;
  gap: var(--resume-module-spacing, var(--template-default-module-spacing));
  /* 移除padding，边距由整个模板控制 */
  padding: 0;
}

/* ================================
 * 响应式设计
 * ================================ */
@media (max-width: 1024px) {
  .resume-template {
    max-width: 100%;
    aspect-ratio: auto;
    min-height: auto;
  }
  
  .main-content {
    @apply flex-col;
  }
  
  .left-column,
  .right-column {
    @apply w-full p-6;
  }
}

/* ================================
 * 打印样式
 * ================================ */
@media print {
  .resume-template {
    width: 100%;
    margin: 0;
    min-height: auto;
  }
  
  .left-column,
  .right-column {
    @apply p-4;
  }
}

/* ================================
 * 动画增强
 * ================================ */
.resume-template * {
  @apply transition-all duration-200 ease-out;
}

/* 进入动画 */
.resume-template {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 交互反馈 */
.resume-template [class*="draggable"]:hover {
  transform: scale(1.02);
}

/* ================================
 * 分页相关样式
 * ================================ */
.page-break {
  @apply w-full my-6 relative;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ef4444, transparent);
  page-break-before: always;
}

.page-break-info {
  @apply flex items-center justify-center relative;
  margin: 16px 0;
}

.page-break-line {
  @apply flex-1 h-px bg-gradient-to-r from-transparent via-red-400 to-transparent;
}

.page-break-text {
  @apply px-4 py-1 bg-white text-xs font-medium text-red-500 border border-red-200 rounded-full;
  margin: 0 16px;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
}

/* 打印时隐藏分页线视觉效果，但保留分页功能 */
@media print {
  .page-break {
    background: none;
    height: 0;
    margin: 0;
  }
  
  .page-break-info {
    display: none;
  }
}

/* ================================
 * 模块容器样式
 * ================================ */
.module-container {
  @apply relative rounded-lg;
  @apply transition-all duration-200;
  /* margin-bottom 已被父级 .left-column/.right-column 的 gap 替代，移除以避免重复间距 */
  padding: var(--resume-module-padding, var(--template-default-module-padding));
}

.module-container:hover {
  @apply bg-gray-50/50;
}

/* 模块操作按钮 */
.module-actions {
  @apply absolute top-2 right-2 flex gap-1 opacity-0;
  @apply transition-opacity duration-200 z-10;
}

.module-container:hover .module-actions {
  @apply opacity-100;
}

.module-action-btn {
  @apply w-8 h-8 rounded-full border-none cursor-pointer;
  @apply flex items-center justify-center;
  @apply bg-blue-500 text-white hover:bg-blue-600;
  @apply transition-all duration-200;
}

.module-action-btn.delete {
  @apply bg-red-500 hover:bg-red-600;
}

.module-action-btn:hover {
  @apply shadow-md;
  transform: scale(1.1);
}

/* ================================
 * 技能模块样式
 * ================================ */
.skills-section {
  animation: slideInLeft 0.6s ease-out;
}

.section-title {
  @apply font-bold mb-5 pb-2 relative;
  @apply border-b-2 border-current;
  border-color: currentColor;
}

.section-title::after {
  @apply absolute bottom-0 left-0;
  @apply w-8 h-0.5 bg-purple-500;
  content: '';
  bottom: -2px;
}

.skills-list {
  gap: var(--resume-item-spacing, var(--template-default-item-spacing));
  @apply flex flex-col;
}

.skill-item {
  @apply relative bg-white rounded-lg shadow-sm;
  @apply border border-gray-200;
  @apply transition-all duration-200 ease-out;
  padding: var(--resume-item-padding, var(--template-default-item-padding));
}

.skill-item.draggable {
  @apply cursor-move;
}

.skill-item:hover {
  @apply shadow-md border-blue-200;
  transform: translateY(-1px);
}

.skill-item.draggable:hover {
  @apply shadow-lg;
  transform: translateY(-2px) scale(1.02);
}

/* 技能项操作按钮 */
.skill-actions {
  @apply absolute top-2 right-2 flex gap-1 z-10;
}

.skill-action-btn {
  @apply w-6 h-6 rounded border-none cursor-pointer;
  @apply flex items-center justify-center text-xs font-bold;
  @apply bg-blue-500 text-white hover:bg-blue-600;
  @apply transition-all duration-200;
}

.skill-action-btn.delete {
  @apply bg-red-500 hover:bg-red-600;
}

.skill-action-btn:hover {
  @apply shadow-md;
  transform: scale(1.1);
}

.skill-action-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 技能名称 */
.skill-name {
  @apply font-semibold text-gray-800 mb-3;
  @apply transition-colors duration-200;
}

.skill-name.editable {
  @apply cursor-pointer hover:text-blue-600;
}

.skill-name.editable:hover {
  @apply underline decoration-blue-400 underline-offset-2;
}

/* 技能进度条 */
.skill-progress-container {
  @apply flex items-center gap-3;
}

.skill-progress-track {
  @apply flex-1 h-2 bg-gray-200 rounded-full overflow-hidden;
  @apply relative;
}

.skill-progress-bar {
  @apply h-full rounded-full;
  @apply transition-all duration-500 ease-out;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.skill-level-text {
  @apply text-sm font-medium text-gray-600 min-w-[3rem] text-right;
}

/* 技能等级调节器 */
.skill-level-adjuster {
  @apply mt-3 opacity-0 transition-opacity duration-200;
}

.skill-item:hover .skill-level-adjuster {
  @apply opacity-100;
}

.skill-level-slider {
  @apply w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer;
}

.skill-level-slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 rounded-full bg-blue-500 cursor-pointer;
  @apply hover:bg-blue-600 transition-colors duration-200;
}

.skill-level-slider::-moz-range-thumb {
  @apply w-4 h-4 rounded-full bg-blue-500 cursor-pointer border-none;
}

/* 添加技能按钮 */
.add-skill-btn {
  @apply flex items-center justify-center gap-2 mt-4 p-3;
  @apply border-2 border-dashed border-gray-300 rounded-lg;
  @apply text-gray-500 cursor-pointer;
  @apply transition-all duration-200;
  @apply hover:border-blue-400 hover:text-blue-500;
  @apply hover:bg-blue-50;
}

.add-skill-btn:hover {
  transform: scale(1.05);
}

.add-icon {
  @apply w-5 h-5;
}

/* ================================
 * 工作经历模块样式
 * ================================ */
.work-section {
  animation: slideInRight 0.6s ease-out;
}

.work-timeline {
  @apply relative pl-6;
  @apply flex flex-col;
  gap: var(--resume-item-spacing, var(--template-default-item-spacing));
}

.work-timeline::before {
  @apply absolute left-2 top-0 bottom-0 w-0.5 bg-gray-300;
  content: '';
}

.work-item {
  @apply relative bg-white rounded-lg shadow-sm;
  @apply border border-gray-200;
  @apply transition-all duration-200;
  /* margin-bottom 已被父级 .work-timeline 的 gap 替代，移除以避免重复间距 */
  padding: var(--resume-item-padding, var(--template-default-item-padding));
}

.work-item.draggable {
  @apply cursor-move;
}

.work-item:hover {
  @apply shadow-md border-blue-200;
  transform: translateY(-1px);
}

.work-item.draggable:hover {
  @apply shadow-lg;
  transform: translateY(-2px) scale(1.02);
}

/* 时间线节点 */
.timeline-node {
  @apply absolute left-[-1.75rem] top-6 w-3 h-3;
  @apply bg-blue-500 rounded-full border-2 border-white;
  @apply shadow-md;
}

/* 工作经历操作按钮 */
.work-actions {
  @apply absolute top-2 right-2 flex gap-1 z-10;
}

.work-action-btn {
  @apply w-6 h-6 rounded border-none cursor-pointer;
  @apply flex items-center justify-center text-xs font-bold;
  @apply bg-blue-500 text-white hover:bg-blue-600;
  @apply transition-all duration-200;
}

.work-action-btn.delete {
  @apply bg-red-500 hover:bg-red-600;
}

.work-action-btn:hover {
  @apply shadow-md;
  transform: scale(1.1);
}

.work-action-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 工作内容 */
.work-content {
  @apply space-y-3;
}

.work-header {
  @apply space-y-2;
}

.job-title {
  @apply text-lg font-bold text-gray-900;
  @apply transition-colors duration-200;
}

.job-title.editable {
  @apply cursor-pointer hover:text-blue-600;
}

.job-title.editable:hover {
  @apply underline decoration-blue-400 underline-offset-2;
}

.company-name {
  @apply text-base font-semibold text-gray-700;
  @apply transition-colors duration-200;
}

.company-name.editable {
  @apply cursor-pointer hover:text-blue-600;
}

.company-name.editable:hover {
  @apply underline decoration-blue-400 underline-offset-2;
}

.work-period {
  @apply text-sm text-gray-500 font-medium;
}

.work-description {
  @apply text-gray-600;
  line-height: var(--resume-line-height, 1.6);
  @apply transition-colors duration-200;
}

.work-description.editable {
  @apply cursor-pointer hover:text-gray-800;
}

.work-description.editable:hover {
  @apply bg-gray-50 p-2 rounded;
}

/* 添加工作经历按钮 */
.add-experience-btn {
  @apply flex items-center justify-center gap-2 mt-6 p-4;
  @apply border-2 border-dashed border-gray-300 rounded-lg;
  @apply text-gray-500 cursor-pointer;
  @apply transition-all duration-200;
  @apply hover:border-blue-400 hover:text-blue-500;
  @apply hover:bg-blue-50;
}

.add-experience-btn:hover {
  transform: scale(1.05);
}

/* ================================
 * 教育经历模块样式
 * ================================ */
.education-section {
  animation: slideInRight 0.7s ease-out;
}

.education-list {
  @apply flex flex-col;
  gap: var(--resume-item-spacing, 16px);
}

.education-item {
  @apply bg-white p-4 rounded-lg shadow-sm border border-gray-200;
  @apply transition-all duration-200;
  @apply relative;
}

.education-item.draggable {
  @apply cursor-move;
}

.education-item:hover {
  @apply shadow-md border-blue-200;
  transform: translateY(-1px);
}

.education-actions {
  @apply absolute top-2 right-2 flex gap-1 z-10;
}

.education-action-btn {
  @apply w-6 h-6 rounded border-none cursor-pointer;
  @apply flex items-center justify-center text-xs font-bold;
  @apply bg-green-500 text-white hover:bg-green-600;
  @apply transition-all duration-200;
}

.education-action-btn.delete {
  @apply bg-red-500 hover:bg-red-600;
}

.education-action-btn:hover {
  @apply shadow-md;
  transform: scale(1.1);
}

.education-content {
  @apply space-y-2;
}

.education-header {
  @apply space-y-1;
}

.school-name {
  @apply text-lg font-bold text-gray-900;
}

.degree-info {
  @apply flex gap-2 text-gray-700;
}

.degree {
  @apply font-semibold;
}

.major {
  @apply text-gray-600;
}

.education-period {
  @apply text-sm text-gray-500 font-medium;
}

.education-description {
  @apply text-gray-600 mt-2;
  line-height: var(--resume-line-height, 1.6);
}

/* ================================
 * 项目经历模块样式
 * ================================ */
.project-section {
  animation: slideInRight 0.8s ease-out;
}

.project-list {
  @apply flex flex-col;
  gap: var(--resume-item-spacing, 16px);
}

.project-item {
  @apply bg-white p-4 rounded-lg shadow-sm border border-gray-200;
  @apply transition-all duration-200;
  @apply relative;
}

.project-item.draggable {
  @apply cursor-move;
}

.project-item:hover {
  @apply shadow-md border-purple-200;
  transform: translateY(-1px);
}

.project-actions {
  @apply absolute top-2 right-2 flex gap-1 z-10;
}

.project-action-btn {
  @apply w-6 h-6 rounded border-none cursor-pointer;
  @apply flex items-center justify-center text-xs font-bold;
  @apply bg-purple-500 text-white hover:bg-purple-600;
  @apply transition-all duration-200;
}

.project-action-btn.delete {
  @apply bg-red-500 hover:bg-red-600;
}

.project-action-btn:hover {
  @apply shadow-md;
  transform: scale(1.1);
}

.project-content {
  @apply space-y-2;
}

.project-header {
  @apply space-y-1;
}

.project-name {
  @apply text-lg font-bold text-gray-900;
}

.project-role {
  @apply text-gray-700 font-semibold;
}

.project-period {
  @apply text-sm text-gray-500 font-medium;
}

.project-tech {
  @apply text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded;
  @apply inline-block mt-1;
}

.project-description {
  @apply text-gray-600 mt-2;
  line-height: var(--resume-line-height, 1.6);
}

/* ================================
 * 自我评价模块样式
 * ================================ */
.self-evaluation-section {
  animation: slideInRight 0.9s ease-out;
}

.self-evaluation-content {
  @apply bg-white p-4 rounded-lg shadow-sm border border-gray-200;
  @apply transition-all duration-200;
}

.self-evaluation-content:hover {
  @apply shadow-md border-indigo-200;
}

.self-evaluation-content p {
  @apply text-gray-700;
  line-height: var(--resume-line-height, 1.6);
  @apply text-justify;
}

/* ================================
 * 动画定义
 * ================================ */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ================================
 * 占位页面样式
 * ================================ */
.placeholder-page {
  @apply h-full flex items-center justify-center;
}

.placeholder-content {
  @apply text-center space-y-4 p-8;
}

.placeholder-icon {
  @apply text-6xl mb-4;
}

.placeholder-content h3 {
  @apply text-2xl font-bold text-gray-700;
}

.placeholder-text {
  @apply text-lg text-gray-600;
}

.placeholder-note {
  @apply text-sm text-gray-500 italic;
}
</style> 