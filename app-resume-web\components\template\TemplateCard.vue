<template>
  <div class="group cursor-pointer">
    <!-- 模板预览区域 -->
    <div class="relative aspect-[3/4] bg-white rounded-2xl shadow-card border border-secondary-100 overflow-hidden mb-4 transition-all duration-300 group-hover:shadow-soft group-hover:-translate-y-1">
      <!-- 模板缩略图 -->
      <div class="absolute inset-0 bg-gradient-to-br from-secondary-50 to-secondary-100">
        <img 
          v-if="template.thumbnail"
          :src="template.thumbnail"
          :alt="template.name"
          class="w-full h-full object-cover"
          loading="lazy"
        />
        <!-- 默认预览内容 -->
        <div v-else class="p-6 h-full flex flex-col">
          <div class="space-y-3">
            <!-- 头部信息 -->
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-primary-200 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <div class="flex-1">
                <div class="h-3 bg-secondary-300 rounded w-20 mb-1"></div>
                <div class="h-2 bg-secondary-200 rounded w-16"></div>
              </div>
            </div>
            
            <!-- 内容行 -->
            <div class="space-y-2">
              <div class="h-2 bg-secondary-300 rounded w-full"></div>
              <div class="h-2 bg-secondary-200 rounded w-4/5"></div>
              <div class="h-2 bg-secondary-200 rounded w-3/4"></div>
              <div class="h-2 bg-secondary-200 rounded w-5/6"></div>
            </div>
            
            <!-- 技能部分 -->
            <div class="grid grid-cols-2 gap-3 mt-4">
              <div>
                <div class="h-2 bg-primary-200 rounded w-full mb-1"></div>
                <div class="h-1.5 bg-secondary-200 rounded w-3/4"></div>
              </div>
              <div>
                <div class="h-2 bg-success-200 rounded w-full mb-1"></div>
                <div class="h-1.5 bg-secondary-200 rounded w-2/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 使用人数标签 -->
      <div class="absolute bottom-4 left-4">
        <span class="inline-flex items-center px-2 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-danger-600">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          {{ template.usageCount || 0 }}人使用
        </span>
      </div>
      
      <!-- 悬浮操作按钮 -->
      <div class="absolute inset-0 bg-primary-600/90 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
        <div class="flex space-x-3">
          <button 
            @click.stop="handleUseTemplate"
            class="px-4 py-2 bg-white text-primary-600 rounded-lg hover:bg-secondary-50 transition-colors duration-200 text-sm font-medium"
          >
            使用模板
          </button>
          <button 
            @click.stop="handlePreview"
            class="px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors duration-200 text-sm font-medium"
          >
            预览
          </button>
        </div>
      </div>
    </div>
    
    <!-- 模板信息 -->
    <div>
      <h3 class="font-semibold text-lg text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
        {{ template.name }}
      </h3>
      
      <div class="flex items-center justify-between">
        <!-- 分类标签 -->
        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
          {{ template.category }}
        </span>
        
        <!-- 评分 -->
        <div class="flex items-center text-sm text-secondary-500">
          <svg class="w-4 h-4 mr-1 text-warning-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          {{ template.rating || 0 }}
        </div>
      </div>
      
      <!-- 支持功能 -->
      <div v-if="template.features?.length > 0" class="flex items-center space-x-4 mt-3 text-xs text-secondary-600">
        <span class="font-medium">支持功能：</span>
        <div class="flex space-x-2">
          <span 
            v-for="feature in template.features.slice(0, 3)" 
            :key="feature"
            class="text-primary-600 hover:text-primary-700 cursor-pointer"
          >
            {{ feature }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  template: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value === 'object' && value.id && value.name
    }
  }
})

// Emits
const emit = defineEmits(['preview', 'use-template'])

// Methods
const handlePreview = () => {
  emit('preview', props.template)
}

const handleUseTemplate = () => {
  emit('use-template', props.template)
}
</script> 