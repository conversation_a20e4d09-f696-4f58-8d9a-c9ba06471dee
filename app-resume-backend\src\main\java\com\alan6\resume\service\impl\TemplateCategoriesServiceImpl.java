package com.alan6.resume.service.impl;

import com.alan6.resume.dto.template.TemplateCategoryResponse;
import com.alan6.resume.entity.TemplateCategories;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.mapper.TemplateCategoriesMapper;
import com.alan6.resume.mapper.ResumeTemplatesMapper;
import com.alan6.resume.service.ITemplateCategoriesService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 模板分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateCategoriesServiceImpl extends ServiceImpl<TemplateCategoriesMapper, TemplateCategories> implements ITemplateCategoriesService {

    private final ResumeTemplatesMapper resumeTemplatesMapper;

    /**
     * 获取所有可用的模板分类列表
     * 包含每个分类的模板数量统计
     *
     * @return 模板分类响应列表
     */
    @Override
    public List<TemplateCategoryResponse> getAllCategories() {
        log.info("开始获取所有模板分类列表");
        
        try {
            // 查询所有启用的分类，按排序权重排序
            LambdaQueryWrapper<TemplateCategories> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TemplateCategories::getStatus, 1)  // 状态：启用
                   .eq(TemplateCategories::getIsDeleted, 0)  // 未删除
                   .orderByAsc(TemplateCategories::getSortOrder)  // 按排序权重升序
                   .orderByAsc(TemplateCategories::getId);  // 相同权重按ID升序
            
            List<TemplateCategories> categories = list(wrapper);
            log.info("查询到 {} 个可用分类", categories.size());
            
            // 转换为响应DTO并统计每个分类的模板数量
            List<TemplateCategoryResponse> responses = categories.stream()
                    .map(category -> {
                        TemplateCategoryResponse response = new TemplateCategoryResponse();
                        BeanUtils.copyProperties(category, response);
                        
                        // 统计该分类下的模板数量
                        Integer templateCount = getTemplateCountByCategoryId(category.getId());
                        response.setTemplateCount(templateCount);
                        
                        return response;
                    })
                    .collect(Collectors.toList());
            
            log.info("成功获取模板分类列表，共 {} 个分类", responses.size());
            return responses;
            
        } catch (Exception e) {
            log.error("获取模板分类列表失败", e);
            throw new RuntimeException("获取模板分类列表失败", e);
        }
    }

    /**
     * 管理员获取所有模板分类列表（包含所有状态的分类）
     * 包含每个分类的模板数量统计
     *
     * @return 模板分类响应列表
     */
    @Override
    public List<TemplateCategoryResponse> getAdminCategories() {
        log.info("管理员开始获取所有模板分类列表");
        
        try {
            // 查询所有未删除的分类，按排序权重排序
            LambdaQueryWrapper<TemplateCategories> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TemplateCategories::getIsDeleted, 0)  // 未删除
                   .orderByAsc(TemplateCategories::getSortOrder)  // 按排序权重升序
                   .orderByAsc(TemplateCategories::getId);  // 相同权重按ID升序
            
            List<TemplateCategories> categories = list(wrapper);
            log.info("管理员查询到 {} 个分类", categories.size());
            
            // 转换为响应DTO并统计每个分类的模板数量
            List<TemplateCategoryResponse> responses = categories.stream()
                    .map(category -> {
                        TemplateCategoryResponse response = new TemplateCategoryResponse();
                        BeanUtils.copyProperties(category, response);
                        
                        // 统计该分类下的模板数量（管理员看所有状态的模板）
                        Integer templateCount = getAdminTemplateCountByCategoryId(category.getId());
                        response.setTemplateCount(templateCount);
                        
                        return response;
                    })
                    .collect(Collectors.toList());
            
            log.info("管理员成功获取模板分类列表，共 {} 个分类", responses.size());
            return responses;
            
        } catch (Exception e) {
            log.error("管理员获取模板分类列表失败", e);
            throw new RuntimeException("管理员获取模板分类列表失败", e);
        }
    }

    /**
     * 根据分类ID获取模板数量
     *
     * @param categoryId 分类ID
     * @return 模板数量
     */
    @Override
    public Integer getTemplateCountByCategoryId(Long categoryId) {
        log.debug("开始统计分类ID为 {} 的模板数量", categoryId);
        
        try {
            // 查询该分类下上架且未删除的模板数量
            LambdaQueryWrapper<ResumeTemplates> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResumeTemplates::getCategoryId, categoryId)
                   .eq(ResumeTemplates::getStatus, 1)  // 状态：上架
                   .eq(ResumeTemplates::getIsDeleted, 0);  // 未删除
            
            Long count = resumeTemplatesMapper.selectCount(wrapper);
            Integer templateCount = count.intValue();
            
            log.debug("分类ID为 {} 的模板数量为 {}", categoryId, templateCount);
            return templateCount;
            
        } catch (Exception e) {
            log.error("统计分类ID为 {} 的模板数量失败", categoryId, e);
            return 0;  // 出错时返回0
        }
    }

    /**
     * 管理员根据分类ID获取模板数量（包含所有状态的模板）
     *
     * @param categoryId 分类ID
     * @return 模板数量
     */
    private Integer getAdminTemplateCountByCategoryId(Long categoryId) {
        log.debug("管理员开始统计分类ID为 {} 的模板数量", categoryId);
        
        try {
            // 查询该分类下未删除的模板数量（所有状态）
            LambdaQueryWrapper<ResumeTemplates> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResumeTemplates::getCategoryId, categoryId)
                   .eq(ResumeTemplates::getIsDeleted, 0);  // 未删除
            
            Long count = resumeTemplatesMapper.selectCount(wrapper);
            Integer templateCount = count.intValue();
            
            log.debug("管理员统计分类ID为 {} 的模板数量为 {}", categoryId, templateCount);
            return templateCount;
            
        } catch (Exception e) {
            log.error("管理员统计分类ID为 {} 的模板数量失败", categoryId, e);
            return 0;  // 出错时返回0
        }
    }
}
