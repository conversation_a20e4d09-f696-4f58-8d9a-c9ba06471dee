package com.alan6.resume.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@Schema(name = "Users对象", description = "用户表")
public class Users implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID（主键）")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "密码（加密）")
    private String password;

    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "性别（0:未知,1:男,2:女）")
    private Byte gender;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "注册类型（1:邮箱,2:手机,3:第三方授权）")
    private Byte registerType;

    @Schema(description = "注册平台（web,wechat_mp,douyin_mp,baidu_mp,alipay_mp,app_ios,app_android）")
    private String registerPlatform;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "最后登录平台")
    private String lastLoginPlatform;

    @Schema(description = "手机号是否已验证（0:否,1:是）")
    private Byte isPhoneVerified;

    @Schema(description = "界面语言偏好")
    private String preferredLanguage;

    @Schema(description = "账号状态（0:禁用,1:正常）")
    private Byte status;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
