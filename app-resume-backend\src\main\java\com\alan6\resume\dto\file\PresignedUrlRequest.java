package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 预签名URL请求DTO
 * 
 * 主要功能：
 * 1. 接收预签名URL生成请求参数
 * 2. 支持自定义HTTP方法和有效期
 * 3. 提供响应头设置功能
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "预签名URL请求", description = "生成预签名URL的请求参数")
public class PresignedUrlRequest {

    /**
     * 文件ID
     * 必填参数，指定要生成预签名URL的文件
     */
    @Schema(description = "文件ID", example = "F20241222123456789", required = true)
    private String fileId;

    /**
     * HTTP方法
     * 预签名URL支持的HTTP方法
     */
    @Schema(description = "HTTP方法", example = "GET", allowableValues = {"GET", "PUT", "POST", "DELETE"})
    private String method;

    /**
     * 有效期（秒）
     * 预签名URL的有效期，默认3600秒（1小时）
     */
    @Schema(description = "有效期（秒）", example = "3600", minimum = "60", maximum = "86400")
    private Integer expiresIn;

    /**
     * 响应头设置
     * 可选参数，用于设置响应时的HTTP头
     */
    @Schema(description = "响应头设置")
    private Map<String, String> responseHeaders;

    /**
     * 是否强制下载
     * 设置为true时，会添加Content-Disposition头强制下载
     */
    @Schema(description = "是否强制下载", example = "false")
    private Boolean forceDownload;

    /**
     * 自定义文件名
     * 下载时使用的文件名，仅在forceDownload为true时有效
     */
    @Schema(description = "自定义文件名", example = "download.jpg")
    private String customFileName;

    /**
     * 验证请求参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return fileId != null && !fileId.trim().isEmpty();
    }

    /**
     * 验证HTTP方法是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValidMethod() {
        return method == null || 
               method.equals("GET") || 
               method.equals("PUT") || 
               method.equals("POST") || 
               method.equals("DELETE");
    }

    /**
     * 验证有效期是否在合理范围内
     * 
     * @return true-有效，false-无效
     */
    public boolean isValidExpiresIn() {
        return expiresIn == null || (expiresIn >= 60 && expiresIn <= 86400);
    }

    /**
     * 获取默认HTTP方法
     * 
     * @return 默认HTTP方法
     */
    public String getDefaultMethod() {
        return method != null ? method : "GET";
    }

    /**
     * 获取默认有效期
     * 
     * @return 默认有效期（秒）
     */
    public Integer getDefaultExpiresIn() {
        return expiresIn != null ? expiresIn : 3600;
    }

    /**
     * 获取默认强制下载设置
     * 
     * @return 默认强制下载设置
     */
    public Boolean getDefaultForceDownload() {
        return forceDownload != null ? forceDownload : false;
    }

    /**
     * 构建响应头Map
     * 根据配置构建完整的响应头设置
     * 
     * @param originalFileName 原始文件名
     * @return 响应头Map
     */
    public Map<String, String> buildResponseHeaders(String originalFileName) {
        Map<String, String> headers = responseHeaders != null ? 
                                     Map.copyOf(responseHeaders) : 
                                     Map.of();
        
        // 如果设置了强制下载，添加Content-Disposition头
        if (getDefaultForceDownload()) {
            String fileName = customFileName != null ? customFileName : originalFileName;
            if (fileName != null) {
                headers = Map.copyOf(headers);
                headers.put("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            }
        }
        
        return headers;
    }

    /**
     * 检查是否设置了自定义响应头
     * 
     * @return true-有自定义响应头，false-无自定义响应头
     */
    public boolean hasCustomResponseHeaders() {
        return responseHeaders != null && !responseHeaders.isEmpty();
    }
} 