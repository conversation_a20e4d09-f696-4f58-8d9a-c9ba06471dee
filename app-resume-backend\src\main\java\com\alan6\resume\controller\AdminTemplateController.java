package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.dto.template.*;
import com.alan6.resume.entity.ResumeTemplateContent;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.entity.ResumeCategories;
import com.alan6.resume.config.MinioConfig;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.IResumeTemplateContentService;
import com.alan6.resume.service.ITemplateCategoriesService;
import com.alan6.resume.service.ITemplateUploadService;
import com.alan6.resume.service.IResumeCategoriesService;
import com.alan6.resume.dto.category.CategoryRequest;
import com.alan6.resume.dto.category.CategoryResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import com.alan6.resume.security.UserPrincipal;
import java.util.HashMap;
import com.alan6.resume.entity.ResumeCategories;

/**
 * 后台管理-模板管理控制器
 * 
 * @description 处理后台管理-模板管理功能
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/admin/template")
@RequiredArgsConstructor
@Tag(name = "后台管理-模板管理", description = "后台管理-模板管理")
public class AdminTemplateController {

    /**
     * 模板上传服务
     */
    private final ITemplateUploadService templateUploadService;

    private final IResumeTemplatesService resumeTemplatesService;
    private final IResumeTemplateContentService templateContentService;
    private final ITemplateCategoriesService templateCategoriesService;
    private final IResumeCategoriesService resumeCategoriesService;
    
    /**
     * MinIO配置属性
     */
    private final MinioConfig.MinioProperties minioProperties;

    /**
     * 上传简历模板
     * 
     * @description 管理员上传简历模板文件的主要接口
     *              支持多文件上传，自动创建模板记录和文件记录
     *              包含完整的参数校验、文件校验、存储处理等功能
     *              现在支持预览图上传功能
     * 
     * @param files 上传的文件数组，支持多种文件类型
     * @param previewImage 预览图文件（可选）
     * @param request 模板上传请求参数，包含模板基本信息
     * @param httpRequest HTTP请求对象，用于获取用户信息
     * @return 上传结果，包含成功文件、失败文件、模板信息等
     * 
     * <AUTHOR>
     * @since 1.0.0
     */
    @PostMapping("/upload")
    @Operation(summary = "上传简历模板", 
               description = "支持多文件上传，包括Vue组件、CSS样式、预览图等文件类型。" +
                           "自动创建模板记录和文件上传记录，支持指定MinIO桶和存储路径。" +
                           "支持预览图上传，预览图将与模板文件存储在同一目录。")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<TemplateUploadResponse> uploadTemplate(
            @Parameter(description = "上传的文件列表", required = true)
            @RequestParam("files") MultipartFile[] files,
            
            @Parameter(description = "预览图文件（可选）")
            @RequestParam(value = "previewImage", required = false) MultipartFile previewImage,
            
            @Parameter(description = "模板上传配置参数", required = true)
            @ModelAttribute @Valid TemplateUploadRequest request,
            
            HttpServletRequest httpRequest) {
        
        // 记录上传开始日志
        log.info("开始上传模板: 模板代码={}, 文件数量={}, 预览图={}, 操作用户IP={}", 
                request.getTemplateCode(), files.length, 
                previewImage != null ? previewImage.getOriginalFilename() : "无", 
                getClientIpAddress(httpRequest));

        try {
            // 1. 基础参数校验
            boolean validationResult = validateUploadParameters(files, request);
            if (!validationResult) {
                return R.fail("参数错误: 请检查文件数量或请求参数");
            }

            // 2. 预览图格式校验
            if (previewImage != null && !validatePreviewImage(previewImage)) {
                return R.fail("预览图格式错误: 仅支持JPG、PNG格式，大小不超过5MB");
            }

            // 3. 获取当前操作用户ID
            Long adminUserId = extractAdminUserIdFromRequest(httpRequest);
            if (adminUserId == null) {
                log.warn("无法获取管理员用户ID，拒绝上传请求");
                return R.fail("用户身份验证失败，请重新登录");
            }

            // 4. 业务逻辑校验
            boolean businessValidationResult = validateBusinessRules(request);
            if (!businessValidationResult) {
                return R.fail("业务规则校验失败: 模板代码已存在或价格设置无效");
            }

            // 5. 执行文件上传（包含预览图）
            TemplateUploadResponse uploadResponse = executeFileUpload(files, previewImage, request, adminUserId);

            // 6. 处理上传结果
            return handleUploadResult(uploadResponse, request);

        } catch (IllegalArgumentException e) {
            log.warn("模板上传参数错误: {}, 模板代码: {}", e.getMessage(), request.getTemplateCode());
            return R.fail("参数错误: " + e.getMessage());
            
        } catch (SecurityException e) {
            log.error("模板上传安全检查失败: {}, 模板代码: {}", e.getMessage(), request.getTemplateCode());
            return R.fail("安全检查失败: " + e.getMessage());
            
        } catch (Exception e) {
            log.error("模板上传异常: 模板代码={}, 错误信息={}", 
                    request.getTemplateCode(), e.getMessage(), e);
            return R.fail("上传失败: " + e.getMessage());
        }
    }

    /**
     * 校验上传基础参数
     * 
     * @description 验证文件数组和请求参数的基本有效性
     * @param files 上传文件数组
     * @param request 上传请求参数
     * @return 校验结果，true表示校验通过
     */
    private boolean validateUploadParameters(MultipartFile[] files, TemplateUploadRequest request) {
        // 检查文件是否为空
        if (files == null || files.length == 0) {
            return false;
        }

        // 检查文件数量限制
        if (files.length > MAX_FILE_COUNT) {
            return false;
        }

        // 检查请求参数
        if (request == null) {
            return false;
        }

        // 检查模板代码
        if (request.getTemplateCode() == null || request.getTemplateCode().trim().isEmpty()) {
            return false;
        }

        // 检查模板名称
        if (request.getTemplateName() == null || request.getTemplateName().trim().isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 校验业务规则
     * 
     * @description 验证模板代码唯一性等业务规则
     * @param request 上传请求参数
     * @return 校验结果，true表示校验通过
     */
    private boolean validateBusinessRules(TemplateUploadRequest request) {
        try {
            // 检查模板代码是否已存在
            boolean codeAvailable = templateUploadService.isTemplateCodeAvailable(request.getTemplateCode());
            if (!codeAvailable) {
                return false;
            }

            // 检查付费模板的价格设置
            if (request.getIsPremium() != null && request.getIsPremium() == 1) {
                if (request.getPrice() == null || request.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            log.error("业务规则校验失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证预览图文件
     * 
     * @description 验证预览图的格式、大小等参数
     * @param previewImage 预览图文件
     * @return 验证结果，true表示验证通过
     */
    private boolean validatePreviewImage(MultipartFile previewImage) {
        if (previewImage == null || previewImage.isEmpty()) {
            return true; // 预览图是可选的，空文件也认为是有效的
        }

        // 检查文件大小（5MB限制）
        long maxSize = 5 * 1024 * 1024; // 5MB
        if (previewImage.getSize() > maxSize) {
            log.warn("预览图文件过大: {} bytes，超过限制 {} bytes", previewImage.getSize(), maxSize);
            return false;
        }

        // 检查文件类型
        String contentType = previewImage.getContentType();
        if (contentType == null) {
            log.warn("预览图文件类型为空");
            return false;
        }

        // 允许的图片类型
        if (!contentType.equals("image/jpeg") && 
            !contentType.equals("image/jpg") && 
            !contentType.equals("image/png")) {
            log.warn("不支持的预览图文件类型: {}", contentType);
            return false;
        }

        // 检查文件扩展名
        String originalFilename = previewImage.getOriginalFilename();
        if (originalFilename != null) {
            String extension = originalFilename.toLowerCase();
            if (!extension.endsWith(".jpg") && 
                !extension.endsWith(".jpeg") && 
                !extension.endsWith(".png")) {
                log.warn("不支持的预览图文件扩展名: {}", extension);
                return false;
            }
        }

        return true;
    }

    /**
     * 执行文件上传
     * 
     * @description 调用服务层执行实际的文件上传和模板创建操作
     * @param files 上传文件数组
     * @param previewImage 预览图文件（可选）
     * @param request 上传请求参数
     * @param adminUserId 管理员用户ID
     * @return 上传响应结果
     * @throws Exception 上传过程中的异常
     */
    private TemplateUploadResponse executeFileUpload(MultipartFile[] files, 
                                                   MultipartFile previewImage,
                                                   TemplateUploadRequest request, 
                                                   Long adminUserId) throws Exception {
        // 转换文件数组为List
        List<MultipartFile> fileList = Arrays.asList(files);
        
        // 调用服务层执行上传（包含预览图）
        return templateUploadService.uploadTemplateFiles(fileList, previewImage, request, adminUserId);
    }

    /**
     * 处理上传结果
     * 
     * @description 根据上传结果生成相应的响应信息
     * @param uploadResponse 上传响应结果
     * @param request 原始请求参数
     * @return 最终的API响应
     */
    private R<TemplateUploadResponse> handleUploadResult(TemplateUploadResponse uploadResponse, 
                                                       TemplateUploadRequest request) {
        // 记录上传结果日志
        log.info("模板上传完成: 模板代码={}, 成功文件={}, 失败文件={}", 
                request.getTemplateCode(), 
                uploadResponse.getSuccessCount(), 
                uploadResponse.getFailureCount());

        // 根据结果生成响应消息
        String resultMessage = buildUploadResultMessage(uploadResponse);

        // 判断上传是否成功
        if (uploadResponse.getFailureCount() == 0) {
            // 全部成功
            return R.ok(uploadResponse, resultMessage);
        } else if (uploadResponse.getSuccessCount() == 0) {
            // 全部失败
            return R.fail(resultMessage);
        } else {
            // 部分成功
            return R.ok(uploadResponse, resultMessage);
        }
    }

    /**
     * 从HTTP请求中提取管理员用户ID
     * 
     * @description 从Spring Security上下文或JWT token中获取当前登录用户ID
     * @param request HTTP请求对象
     * @return 管理员用户ID，获取失败返回null
     */
    private Long extractAdminUserIdFromRequest(HttpServletRequest request) {
        try {
            // 方法1: 从Spring Security上下文获取
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                return userPrincipal.getUserId();
            }

            // 方法2: 从请求头获取（备用方案）
            String userIdHeader = request.getHeader("X-User-Id");
            if (userIdHeader != null && !userIdHeader.trim().isEmpty()) {
                return Long.parseLong(userIdHeader.trim());
            }

            // 方法3: 从请求属性获取（AdminInterceptor设置）
            Object userIdAttr = request.getAttribute("currentAdminUserId");
            if (userIdAttr instanceof Long) {
                return (Long) userIdAttr;
            }

            log.warn("无法从任何来源获取管理员用户ID");
            return null;

        } catch (NumberFormatException e) {
            log.error("解析用户ID失败: {}", e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("提取管理员用户ID时发生异常", e);
            return null;
        }
    }

    /**
     * 获取客户端IP地址
     * 
     * @description 从HTTP请求中获取真实的客户端IP地址，支持代理和负载均衡
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // 常见的代理头信息
        String[] ipHeaders = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        // 尝试从代理头获取IP
        for (String header : ipHeaders) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 如果有多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        // 如果没有代理，直接获取远程地址
        return request.getRemoteAddr();
    }

    /**
     * 上传文件数量限制常量
     */
    private static final int MAX_FILE_COUNT = 50;

    /**
     * 检查模板代码是否可用
     * 
     * @description 验证模板代码的唯一性，用于前端实时校验
     * @param templateCode 要检查的模板代码
     * @return 检查结果
     */
    @GetMapping("/check-code")
    @Operation(summary = "检查模板代码可用性", 
               description = "验证模板代码是否已存在，用于前端表单实时校验")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> checkTemplateCode(
            @Parameter(description = "模板代码", required = true, example = "business-simple")
            @RequestParam("templateCode") String templateCode) {
        
        log.debug("检查模板代码可用性: {}", templateCode);

        try {
            // 验证模板代码格式
            if (templateCode == null || templateCode.trim().isEmpty()) {
                return R.fail("模板代码不能为空");
            }

            if (!templateCode.matches("^[a-zA-Z0-9_-]+$")) {
                return R.fail("模板代码只能包含字母、数字、下划线和连字符");
            }

            // 检查唯一性
            boolean available = templateUploadService.isTemplateCodeAvailable(templateCode.trim());
            
            String message = available ? "模板代码可用" : "模板代码已存在";
            return R.ok(available, message);

        } catch (Exception e) {
            log.error("检查模板代码失败: {}, 错误: {}", templateCode, e.getMessage());
            return R.fail("检查失败: " + e.getMessage());
        }
    }

    /**
     * 检查模板名称可用性
     * 
     * @description 验证模板名称是否已存在，用于前端表单实时校验
     * @param templateName 模板名称
     * @return 是否可用
     */
    @GetMapping("/check-name")
    @Operation(summary = "检查模板名称可用性", 
               description = "验证模板名称是否已存在，用于前端表单实时校验")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> checkTemplateName(
            @Parameter(description = "模板名称", required = true, example = "商务简约模板")
            @RequestParam("templateName") String templateName) {
        
        log.debug("检查模板名称可用性: {}", templateName);

        try {
            // 验证模板名称
            if (templateName == null || templateName.trim().isEmpty()) {
                return R.fail("模板名称不能为空");
            }

            if (templateName.length() > 100) {
                return R.fail("模板名称长度不能超过100个字符");
            }

            // 检查唯一性
            boolean available = templateUploadService.isTemplateNameAvailable(templateName.trim());
            
            String message = available ? "模板名称可用" : "模板名称已存在";
            return R.ok(available, message);

        } catch (Exception e) {
            log.error("检查模板名称失败: {}, 错误: {}", templateName, e.getMessage());
            return R.fail("检查失败: " + e.getMessage());
        }
    }

    /**
     * 验证文件类型支持情况
     * 
     * @description 检查上传的文件类型是否被支持，用于前端文件选择校验
     * @param file 要验证的文件
     * @return 验证结果
     */
    @PostMapping("/validate-file")
    @Operation(summary = "验证文件类型", 
               description = "检查文件类型是否被模板上传功能支持")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> validateFileType(
            @Parameter(description = "要验证的文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        log.debug("验证文件类型: {}", file.getOriginalFilename());

        try {
            if (file == null || file.isEmpty()) {
                return R.fail("文件不能为空");
            }

            boolean supported = templateUploadService.isFileTypeSupported(file);
            
            String message = supported ? 
                    "文件类型支持" : 
                    "不支持的文件类型: " + file.getContentType();
            
            return R.ok(supported, message);

        } catch (Exception e) {
            log.error("验证文件类型失败: {}, 错误: {}", file.getOriginalFilename(), e.getMessage());
            return R.fail("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的文件类型列表
     * 
     * @description 返回模板上传支持的所有文件类型，用于前端显示和校验
     * @return 支持的文件类型列表
     */
    @GetMapping("/supported-file-types")
    @Operation(summary = "获取支持的文件类型", 
               description = "返回模板上传功能支持的所有文件类型列表")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<List<String>> getSupportedFileTypes() {
        
        log.debug("获取支持的文件类型列表");

        try {
            // 定义支持的文件类型和扩展名
            List<String> supportedTypes = Arrays.asList(
                    "HTML文件 (.html)",
                    "CSS样式文件 (.css)",
                    "JavaScript文件 (.js)",
                    "Vue组件文件 (.vue)",
                    "TypeScript文件 (.ts)",
                    "JSON配置文件 (.json)",
                    "纯文本文件 (.txt)",
                    "JPEG图片 (.jpg, .jpeg)",
                    "PNG图片 (.png)",
                    "GIF图片 (.gif)",
                    "SVG图片 (.svg)"
            );

            return R.ok(supportedTypes, "获取支持的文件类型成功");

        } catch (Exception e) {
            log.error("获取支持的文件类型失败: {}", e.getMessage());
            return R.fail("获取失败: " + e.getMessage());
        }
    }

    /**
     * 检查存储桶状态
     * 
     * @description 验证指定的MinIO存储桶是否存在且可用
     * @param bucketName 存储桶名称
     * @return 检查结果
     */
    @GetMapping("/check-bucket")
    @Operation(summary = "检查存储桶状态", 
               description = "验证MinIO存储桶是否存在，不存在则尝试创建")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> checkBucketStatus(
            @Parameter(description = "存储桶名称", required = true, example = "resume-template")
            @RequestParam("bucketName") String bucketName) {
        
        log.debug("检查存储桶状态: {}", bucketName);

        try {
            if (bucketName == null || bucketName.trim().isEmpty()) {
                return R.fail("存储桶名称不能为空");
            }

            if (!bucketName.matches("^[a-z0-9-]{3,63}$")) {
                return R.fail("存储桶名称格式不正确");
            }

            boolean available = templateUploadService.ensureBucketExists(bucketName.trim());
            
            String message = available ? "存储桶可用" : "存储桶不可用";
            return R.ok(available, message);

        } catch (Exception e) {
            log.error("检查存储桶状态失败: {}, 错误: {}", bucketName, e.getMessage());
            return R.fail("检查失败: " + e.getMessage());
        }
    }

    /**
     * 更新模板状态
     * 
     * @description 启用或禁用模板
     * @param templateId 模板ID
     * @param request 状态更新请求
     * @return 更新结果
     */
    @PutMapping("/{templateId}/status")
    @Operation(summary = "更新模板状态", 
               description = "启用或禁用指定模板")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> updateTemplateStatus(
            @Parameter(description = "模板ID", required = true) @PathVariable Long templateId,
            @Parameter(description = "状态更新请求", required = true) @RequestBody TemplateStatusUpdateRequest request) {
        
        log.debug("更新模板状态: templateId={}, status={}", templateId, request.getStatus());

        try {
            // 验证参数
            if (templateId == null || templateId <= 0) {
                return R.fail("模板ID无效");
            }

            if (request.getStatus() == null || (request.getStatus() != 0 && request.getStatus() != 1)) {
                return R.fail("状态值无效，只能是0（禁用）或1（启用）");
            }

            // TODO: 实现状态更新逻辑
            // 这里暂时返回成功，需要在服务层实现具体逻辑
            String statusText = request.getStatus() == 1 ? "启用" : "禁用";
            log.info("模板状态更新成功: templateId={}, status={}", templateId, statusText);
            return R.ok(true, "模板" + statusText + "成功");

        } catch (Exception e) {
            log.error("更新模板状态失败: templateId={}, status={}, error={}", 
                    templateId, request.getStatus(), e.getMessage(), e);
            return R.fail("更新模板状态失败: " + e.getMessage());
        }
    }

    /**
     * 构建上传结果消息
     * 
     * @description 根据上传结果生成友好的提示消息
     * @param response 上传响应结果
     * @return 格式化的结果消息
     */
    private String buildUploadResultMessage(TemplateUploadResponse response) {
        if (response.getFailureCount() == 0) {
            return String.format("模板上传成功！共上传 %d 个文件", response.getSuccessCount());
        } else if (response.getSuccessCount() == 0) {
            return String.format("模板上传失败！%d 个文件均上传失败", response.getFailureCount());
        } else {
            return String.format("模板上传完成！成功 %d 个文件，失败 %d 个文件", 
                    response.getSuccessCount(), response.getFailureCount());
        }
    }

    /**
     * 调试接口：检查数据库中的模板数据
     */
    @GetMapping("/debug/count")
    @Operation(summary = "调试接口-检查模板数据", description = "检查数据库中的模板数据总数")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Map<String, Object>> debugTemplateCount() {
        try {
            // 查询所有模板数量（包括删除的）
            long totalCount = resumeTemplatesService.count();
            
            // 查询未删除的模板数量
            long activeCount = resumeTemplatesService.lambdaQuery()
                    .eq(ResumeTemplates::getIsDeleted, 0)
                    .count();
            
            // 查询已删除的模板数量
            long deletedCount = resumeTemplatesService.lambdaQuery()
                    .eq(ResumeTemplates::getIsDeleted, 1)
                    .count();
            
            // 查询上架的模板数量
            long onlineCount = resumeTemplatesService.lambdaQuery()
                    .eq(ResumeTemplates::getIsDeleted, 0)
                    .eq(ResumeTemplates::getStatus, 1)
                    .count();
            
            // 查询前几个模板的基本信息
            List<ResumeTemplates> sampleTemplates = resumeTemplatesService.lambdaQuery()
                    .select(ResumeTemplates::getId, ResumeTemplates::getName, 
                           ResumeTemplates::getTemplateCode, ResumeTemplates::getStatus, 
                           ResumeTemplates::getIsDeleted)
                    .last("LIMIT 5")
                    .list();
            
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("activeCount", activeCount);
            result.put("deletedCount", deletedCount);
            result.put("onlineCount", onlineCount);
            result.put("sampleTemplates", sampleTemplates.stream().map(t -> {
                Map<String, Object> template = new HashMap<>();
                template.put("id", t.getId());
                template.put("name", t.getName());
                template.put("templateCode", t.getTemplateCode());
                template.put("status", t.getStatus());
                template.put("isDeleted", t.getIsDeleted());
                return template;
            }).collect(java.util.stream.Collectors.toList()));
            
            log.info("模板数据统计: 总数={}, 活跃={}, 已删除={}, 上架={}", 
                    totalCount, activeCount, deletedCount, onlineCount);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("获取模板统计数据失败", e);
            return R.fail("获取模板统计数据失败");
        }
    }

    /**
     * 管理员获取模板列表
     */
    @GetMapping("/list")
    @Operation(summary = "管理员获取模板列表", description = "管理员专用的模板列表接口，包含所有状态的模板")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<TemplateListResponse> getAdminTemplateList(
            @Parameter(description = "列表请求参数") TemplateListRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取模板列表，管理员ID: {}, 请求参数: {}", adminUserId, request);

        try {
            // 管理员可以看到所有状态的模板，包括禁用的
            TemplateListResponse response = resumeTemplatesService.getAdminTemplateList(request, adminUserId);

            log.info("管理员成功获取模板列表，管理员ID: {}, 返回 {} 个模板", adminUserId, response.getRecords().size());
            return R.ok(response);

        } catch (Exception e) {
            log.error("管理员获取模板列表失败，管理员ID: {}", adminUserId, e);
            return R.fail("获取模板列表失败");
        }
    }

    /**
     * 管理员获取模板分类列表
     */
    @GetMapping("/categories")
    @Operation(summary = "管理员获取模板分类列表", description = "管理员专用的模板分类接口，包含所有状态的分类")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<List<TemplateCategoryResponse>> getAdminTemplateCategories(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取模板分类列表，管理员ID: {}", adminUserId);

        try {
            // 管理员可以看到所有状态的分类
            List<TemplateCategoryResponse> categories = templateCategoriesService.getAdminCategories();

            log.info("管理员成功获取模板分类列表，管理员ID: {}, 共 {} 个分类", adminUserId, categories.size());
            return R.ok(categories);

        } catch (Exception e) {
            log.error("管理员获取模板分类列表失败，管理员ID: {}", adminUserId, e);
            return R.fail("获取模板分类失败");
        }
    }

    /**
     * 删除单个模板
     */
    @DeleteMapping("/{templateId}")
    @Operation(summary = "删除单个模板", description = "管理员删除指定的模板，支持软删除")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> deleteTemplate(
            @Parameter(description = "模板ID", required = true) @PathVariable Long templateId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员删除模板，模板ID: {}, 管理员ID: {}", templateId, adminUserId);

        try {
            // 验证参数
            if (templateId == null || templateId <= 0) {
                return R.fail("模板ID无效");
            }

            // 检查模板是否存在
            boolean exists = resumeTemplatesService.existsById(templateId);
            if (!exists) {
                return R.fail("模板不存在");
            }

            // 执行软删除
            boolean success = resumeTemplatesService.deleteTemplate(templateId, adminUserId);
            
            if (success) {
                log.info("管理员成功删除模板，模板ID: {}, 管理员ID: {}", templateId, adminUserId);
                return R.ok(true, "模板删除成功");
            } else {
                log.warn("管理员删除模板失败，模板ID: {}, 管理员ID: {}", templateId, adminUserId);
                return R.fail("模板删除失败");
            }

        } catch (Exception e) {
            log.error("管理员删除模板异常，模板ID: {}, 管理员ID: {}", templateId, adminUserId, e);
            return R.fail("删除模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除模板
     */
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除模板", description = "管理员批量删除多个模板，支持软删除")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Map<String, Object>> batchDeleteTemplates(
            @Parameter(description = "批量删除请求", required = true) 
            @RequestBody TemplateBatchDeleteRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员批量删除模板，模板数量: {}, 管理员ID: {}", 
                request.getTemplateIds() != null ? request.getTemplateIds().size() : 0, adminUserId);

        try {
            // 验证参数
            if (request.getTemplateIds() == null || request.getTemplateIds().isEmpty()) {
                return R.fail("请选择要删除的模板");
            }

            if (request.getTemplateIds().size() > 50) {
                return R.fail("单次最多删除50个模板");
            }

            // 验证所有模板ID
            for (Long templateId : request.getTemplateIds()) {
                if (templateId == null || templateId <= 0) {
                    return R.fail("包含无效的模板ID");
                }
            }

            // 执行批量删除
            Map<String, Object> result = resumeTemplatesService.batchDeleteTemplates(
                    request.getTemplateIds(), adminUserId);
            
            Integer successCount = (Integer) result.get("successCount");
            Integer failureCount = (Integer) result.get("failureCount");
            
            log.info("管理员批量删除模板完成，成功: {}, 失败: {}, 管理员ID: {}", 
                    successCount, failureCount, adminUserId);

            if (failureCount == 0) {
                return R.ok(result, String.format("成功删除 %d 个模板", successCount));
            } else if (successCount == 0) {
                return R.fail(result, String.format("删除失败，%d 个模板删除失败", failureCount));
            } else {
                return R.ok(result, String.format("批量删除完成，成功 %d 个，失败 %d 个", 
                        successCount, failureCount));
            }

        } catch (Exception e) {
            log.error("管理员批量删除模板异常，管理员ID: {}", adminUserId, e);
            return R.fail("批量删除模板失败：" + e.getMessage());
        }
    }

    // ================================
    // 模板内容管理相关接口
    // ================================

    /**
     * 获取模板内容列表
     * 
     * @description 管理员获取所有模板内容的列表，支持分页和筛选
     * @param industry 行业筛选（可选）
     * @param position 职位筛选（可选）
     * @param language 语言筛选（可选）
     * @param userPrincipal 当前用户信息
     * @return 模板内容列表
     */
    @GetMapping("/content/list")
    @Operation(summary = "获取模板内容列表", description = "管理员获取所有模板内容的列表")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<List<ResumeTemplateContent>> getTemplateContentList(
            @Parameter(description = "行业筛选") @RequestParam(required = false) String industry,
            @Parameter(description = "职位筛选") @RequestParam(required = false) String position,
            @Parameter(description = "语言筛选") @RequestParam(required = false) String language,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取模板内容列表，管理员ID: {}, 筛选条件: industry={}, position={}, language={}", 
                adminUserId, industry, position, language);

        try {
            List<ResumeTemplateContent> contentList;

            // 根据筛选条件获取内容
            if (industry != null && position != null) {
                contentList = templateContentService.getBestMatchContent(industry, position);
            } else if (industry != null) {
                contentList = templateContentService.getRecommendedByIndustry(industry);
            } else if (position != null) {
                contentList = templateContentService.getRecommendedByPosition(position);
            } else if (language != null) {
                contentList = templateContentService.getByLanguage(language);
            } else {
                contentList = templateContentService.getAllAvailable();
            }

            log.info("管理员成功获取模板内容列表，管理员ID: {}, 返回 {} 个内容", adminUserId, contentList.size());
            return R.ok(contentList);

        } catch (Exception e) {
            log.error("管理员获取模板内容列表失败，管理员ID: {}", adminUserId, e);
            return R.fail("获取模板内容列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取模板内容详情
     * 
     * @description 管理员获取指定模板内容的详细信息
     * @param contentId 内容ID
     * @param userPrincipal 当前用户信息
     * @return 模板内容详情
     */
    @GetMapping("/content/{contentId}")
    @Operation(summary = "获取模板内容详情", description = "管理员获取指定模板内容的详细信息")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<ResumeTemplateContent> getTemplateContentById(
            @Parameter(description = "内容ID", required = true) @PathVariable Long contentId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取模板内容详情，内容ID: {}, 管理员ID: {}", contentId, adminUserId);

        try {
            if (contentId == null || contentId <= 0) {
                return R.fail("内容ID无效");
            }

            ResumeTemplateContent content = templateContentService.getById(contentId);
            if (content == null) {
                return R.fail("模板内容不存在");
            }

            log.info("管理员成功获取模板内容详情，内容ID: {}, 管理员ID: {}", contentId, adminUserId);
            return R.ok(content);

        } catch (Exception e) {
            log.error("管理员获取模板内容详情失败，内容ID: {}, 管理员ID: {}", contentId, adminUserId, e);
            return R.fail("获取模板内容详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建模板内容
     * 
     * @description 管理员创建新的模板内容
     * @param content 模板内容对象
     * @param userPrincipal 当前用户信息
     * @return 创建结果
     */
    @PostMapping("/content")
    @Operation(summary = "创建模板内容", description = "管理员创建新的模板内容")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<ResumeTemplateContent> createTemplateContent(
            @Parameter(description = "模板内容对象", required = true) @RequestBody ResumeTemplateContent content,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员创建模板内容，管理员ID: {}, 内容代码: {}", adminUserId, content.getCode());

        try {
            // 基本参数验证
            if (content == null) {
                return R.fail("模板内容不能为空");
            }

            if (!StringUtils.hasText(content.getName())) {
                return R.fail("内容名称不能为空");
            }

            if (!StringUtils.hasText(content.getCode())) {
                return R.fail("内容代码不能为空");
            }

            if (content.getContentData() == null) {
                return R.fail("内容数据不能为空");
            }

            // 检查代码唯一性
            if (!templateContentService.isCodeAvailable(content.getCode())) {
                return R.fail("内容代码已存在: " + content.getCode());
            }

            // 创建内容
            boolean success = templateContentService.createContent(content);
            if (success) {
                log.info("管理员成功创建模板内容，内容ID: {}, 内容代码: {}, 管理员ID: {}", 
                        content.getId(), content.getCode(), adminUserId);
                return R.ok(content, "模板内容创建成功");
            } else {
                return R.fail("模板内容创建失败");
            }

        } catch (Exception e) {
            log.error("管理员创建模板内容失败，管理员ID: {}, 内容代码: {}", 
                    adminUserId, content.getCode(), e);
            return R.fail("创建模板内容失败: " + e.getMessage());
        }
    }

    /**
     * 更新模板内容
     * 
     * @description 管理员更新现有的模板内容
     * @param contentId 内容ID
     * @param content 更新的模板内容对象
     * @param userPrincipal 当前用户信息
     * @return 更新结果
     */
    @PutMapping("/content/{contentId}")
    @Operation(summary = "更新模板内容", description = "管理员更新现有的模板内容")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<ResumeTemplateContent> updateTemplateContent(
            @Parameter(description = "内容ID", required = true) @PathVariable Long contentId,
            @Parameter(description = "更新的模板内容对象", required = true) @RequestBody ResumeTemplateContent content,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员更新模板内容，内容ID: {}, 管理员ID: {}", contentId, adminUserId);

        try {
            // 基本参数验证
            if (contentId == null || contentId <= 0) {
                return R.fail("内容ID无效");
            }

            if (content == null) {
                return R.fail("模板内容不能为空");
            }

            // 检查内容是否存在
            ResumeTemplateContent existingContent = templateContentService.getById(contentId);
            if (existingContent == null) {
                return R.fail("模板内容不存在");
            }

            // 设置ID确保更新正确的记录
            content.setId(contentId);

            // 更新内容
            boolean success = templateContentService.updateContent(content);
            if (success) {
                log.info("管理员成功更新模板内容，内容ID: {}, 管理员ID: {}", contentId, adminUserId);
                return R.ok(content, "模板内容更新成功");
            } else {
                return R.fail("模板内容更新失败");
            }

        } catch (Exception e) {
            log.error("管理员更新模板内容失败，内容ID: {}, 管理员ID: {}", contentId, adminUserId, e);
            return R.fail("更新模板内容失败: " + e.getMessage());
        }
    }

    /**
     * 删除模板内容
     * 
     * @description 管理员删除指定的模板内容
     * @param contentId 内容ID
     * @param userPrincipal 当前用户信息
     * @return 删除结果
     */
    @DeleteMapping("/content/{contentId}")
    @Operation(summary = "删除模板内容", description = "管理员删除指定的模板内容")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> deleteTemplateContent(
            @Parameter(description = "内容ID", required = true) @PathVariable Long contentId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员删除模板内容，内容ID: {}, 管理员ID: {}", contentId, adminUserId);

        try {
            if (contentId == null || contentId <= 0) {
                return R.fail("内容ID无效");
            }

            // 检查内容是否存在
            ResumeTemplateContent content = templateContentService.getById(contentId);
            if (content == null) {
                return R.fail("模板内容不存在");
            }

            // 检查是否有模板正在使用此内容
            long usingCount = resumeTemplatesService.lambdaQuery()
                    .eq(ResumeTemplates::getContentId, contentId)
                    .eq(ResumeTemplates::getIsDeleted, 0)
                    .count();

            if (usingCount > 0) {
                return R.fail("无法删除，有 " + usingCount + " 个模板正在使用此内容");
            }

            // 删除内容
            boolean success = templateContentService.deleteContent(contentId);
            if (success) {
                log.info("管理员成功删除模板内容，内容ID: {}, 内容代码: {}, 管理员ID: {}", 
                        contentId, content.getCode(), adminUserId);
                return R.ok(true, "模板内容删除成功");
            } else {
                return R.fail("模板内容删除失败");
            }

        } catch (Exception e) {
            log.error("管理员删除模板内容失败，内容ID: {}, 管理员ID: {}", contentId, adminUserId, e);
            return R.fail("删除模板内容失败: " + e.getMessage());
        }
    }

    /**
     * 启用模板内容
     * 
     * @description 管理员启用指定的模板内容
     * @param contentId 内容ID
     * @param userPrincipal 当前用户信息
     * @return 操作结果
     */
    @PutMapping("/content/{contentId}/enable")
    @Operation(summary = "启用模板内容", description = "管理员启用指定的模板内容")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> enableTemplateContent(
            @Parameter(description = "内容ID", required = true) @PathVariable Long contentId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员启用模板内容，内容ID: {}, 管理员ID: {}", contentId, adminUserId);

        try {
            if (contentId == null || contentId <= 0) {
                return R.fail("内容ID无效");
            }

            boolean success = templateContentService.enableContent(contentId);
            if (success) {
                log.info("管理员成功启用模板内容，内容ID: {}, 管理员ID: {}", contentId, adminUserId);
                return R.ok(true, "模板内容启用成功");
            } else {
                return R.fail("模板内容启用失败");
            }

        } catch (Exception e) {
            log.error("管理员启用模板内容失败，内容ID: {}, 管理员ID: {}", contentId, adminUserId, e);
            return R.fail("启用模板内容失败: " + e.getMessage());
        }
    }

    /**
     * 禁用模板内容
     * 
     * @description 管理员禁用指定的模板内容
     * @param contentId 内容ID
     * @param userPrincipal 当前用户信息
     * @return 操作结果
     */
    @PutMapping("/content/{contentId}/disable")
    @Operation(summary = "禁用模板内容", description = "管理员禁用指定的模板内容")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Boolean> disableTemplateContent(
            @Parameter(description = "内容ID", required = true) @PathVariable Long contentId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员禁用模板内容，内容ID: {}, 管理员ID: {}", contentId, adminUserId);

        try {
            if (contentId == null || contentId <= 0) {
                return R.fail("内容ID无效");
            }

            boolean success = templateContentService.disableContent(contentId);
            if (success) {
                log.info("管理员成功禁用模板内容，内容ID: {}, 管理员ID: {}", contentId, adminUserId);
                return R.ok(true, "模板内容禁用成功");
            } else {
                return R.fail("模板内容禁用失败");
            }

        } catch (Exception e) {
            log.error("管理员禁用模板内容失败，内容ID: {}, 管理员ID: {}", contentId, adminUserId, e);
            return R.fail("禁用模板内容失败: " + e.getMessage());
        }
    }

    /**
     * 复制模板内容
     * 
     * @description 管理员复制现有的模板内容创建新内容
     * @param contentId 源内容ID
     * @param newCode 新内容代码
     * @param newName 新内容名称
     * @param userPrincipal 当前用户信息
     * @return 复制结果
     */
    @PostMapping("/content/{contentId}/copy")
    @Operation(summary = "复制模板内容", description = "管理员复制现有的模板内容创建新内容")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<ResumeTemplateContent> copyTemplateContent(
            @Parameter(description = "源内容ID", required = true) @PathVariable Long contentId,
            @Parameter(description = "新内容代码", required = true) @RequestParam String newCode,
            @Parameter(description = "新内容名称", required = true) @RequestParam String newName,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员复制模板内容，源内容ID: {}, 新代码: {}, 新名称: {}, 管理员ID: {}", 
                contentId, newCode, newName, adminUserId);

        try {
            if (contentId == null || contentId <= 0) {
                return R.fail("源内容ID无效");
            }

            if (!StringUtils.hasText(newCode)) {
                return R.fail("新内容代码不能为空");
            }

            if (!StringUtils.hasText(newName)) {
                return R.fail("新内容名称不能为空");
            }

            // 检查新代码是否可用
            if (!templateContentService.isCodeAvailable(newCode)) {
                return R.fail("新内容代码已存在: " + newCode);
            }

            // 复制内容
            ResumeTemplateContent newContent = templateContentService.copyContent(contentId, newCode, newName);
            if (newContent != null) {
                log.info("管理员成功复制模板内容，源内容ID: {}, 新内容ID: {}, 新代码: {}, 管理员ID: {}", 
                        contentId, newContent.getId(), newCode, adminUserId);
                return R.ok(newContent, "模板内容复制成功");
            } else {
                return R.fail("模板内容复制失败");
            }

        } catch (Exception e) {
            log.error("管理员复制模板内容失败，源内容ID: {}, 新代码: {}, 管理员ID: {}", 
                    contentId, newCode, adminUserId, e);
            return R.fail("复制模板内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有行业列表
     * 
     * @description 获取系统中所有已配置的行业类型
     * @param userPrincipal 当前用户信息
     * @return 行业列表
     */
    @GetMapping("/content/industries")
    @Operation(summary = "获取所有行业列表", description = "获取系统中所有已配置的行业类型")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<List<String>> getAllIndustries(@AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取所有行业列表，管理员ID: {}", adminUserId);

        try {
            List<String> industries = templateContentService.getAllIndustries();
            log.info("管理员成功获取所有行业列表，管理员ID: {}, 行业数量: {}", adminUserId, industries.size());
            return R.ok(industries);

        } catch (Exception e) {
            log.error("管理员获取所有行业列表失败，管理员ID: {}", adminUserId, e);
            return R.fail("获取行业列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有职位列表
     * 
     * @description 获取系统中所有已配置的职位类型
     * @param userPrincipal 当前用户信息
     * @return 职位列表
     */
    @GetMapping("/content/positions")
    @Operation(summary = "获取所有职位列表", description = "获取系统中所有已配置的职位类型")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<List<String>> getAllPositions(@AuthenticationPrincipal UserPrincipal userPrincipal) {

        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取所有职位列表，管理员ID: {}", adminUserId);

        try {
            List<String> positions = templateContentService.getAllPositions();
            log.info("管理员成功获取所有职位列表，管理员ID: {}, 职位数量: {}", adminUserId, positions.size());
            return R.ok(positions);

        } catch (Exception e) {
            log.error("管理员获取所有职位列表失败，管理员ID: {}", adminUserId, e);
            return R.fail("获取职位列表失败: " + e.getMessage());
        }
    }

    // ================================
    // 分类管理接口
    // ================================

    /**
     * 获取分类树结构
     */
    @GetMapping("/category/tree")
    @Operation(summary = "获取分类树结构", description = "获取完整的分类树结构，包含父级和子级分类")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<List<CategoryResponse>> getCategoryTree(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取分类树结构，管理员ID: {}", adminUserId);

        try {
            List<CategoryResponse> categoryTree = resumeCategoriesService.getCategoryTree();
            log.info("管理员成功获取分类树结构，管理员ID: {}, 分类数量: {}", adminUserId, categoryTree.size());
            return R.ok(categoryTree);

        } catch (Exception e) {
            log.error("管理员获取分类树结构失败，管理员ID: {}", adminUserId, e);
            return R.fail("获取分类树结构失败");
        }
    }

    /**
     * 获取指定类型的分类列表
     */
    @GetMapping("/category/type/{categoryType}")
    @Operation(summary = "获取指定类型的分类列表", description = "获取指定类型的分类列表")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<List<CategoryResponse>> getCategoriesByType(
            @PathVariable String categoryType,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取分类类型为 {} 的分类列表，管理员ID: {}", categoryType, adminUserId);

        try {
            List<CategoryResponse> categories = resumeCategoriesService.getCategoriesByType(categoryType);
            log.info("管理员成功获取分类列表，管理员ID: {}, 分类数量: {}", adminUserId, categories.size());
            return R.ok(categories);

        } catch (Exception e) {
            log.error("管理员获取分类列表失败，管理员ID: {}", adminUserId, e);
            return R.fail("获取分类列表失败");
        }
    }

    /**
     * 创建分类
     */
    @PostMapping("/category")
    @Operation(summary = "创建分类", description = "创建新的分类")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<String> createCategory(
            @RequestBody @Valid CategoryRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员创建分类，管理员ID: {}, 分类名称: {}", adminUserId, request.getName());

        try {
            boolean result = resumeCategoriesService.createCategory(request);
            if (result) {
                log.info("管理员成功创建分类，管理员ID: {}, 分类名称: {}", adminUserId, request.getName());
                return R.ok("创建分类成功");
            } else {
                return R.fail("创建分类失败");
            }

        } catch (Exception e) {
            log.error("管理员创建分类失败，管理员ID: {}, 错误信息: {}", adminUserId, e.getMessage(), e);
            return R.fail("创建分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新分类
     */
    @PutMapping("/category")
    @Operation(summary = "更新分类", description = "更新现有分类")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<String> updateCategory(
            @RequestBody @Valid CategoryRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员更新分类，管理员ID: {}, 分类ID: {}", adminUserId, request.getId());

        try {
            boolean result = resumeCategoriesService.updateCategory(request);
            if (result) {
                log.info("管理员成功更新分类，管理员ID: {}, 分类ID: {}", adminUserId, request.getId());
                return R.ok("更新分类成功");
            } else {
                return R.fail("更新分类失败");
            }

        } catch (Exception e) {
            log.error("管理员更新分类失败，管理员ID: {}, 错误信息: {}", adminUserId, e.getMessage(), e);
            return R.fail("更新分类失败: " + e.getMessage());
        }
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/category/{id}")
    @Operation(summary = "删除分类", description = "删除指定分类")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<String> deleteCategory(
            @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员删除分类，管理员ID: {}, 分类ID: {}", adminUserId, id);

        try {
            boolean result = resumeCategoriesService.deleteCategory(id);
            if (result) {
                log.info("管理员成功删除分类，管理员ID: {}, 分类ID: {}", adminUserId, id);
                return R.ok("删除分类成功");
            } else {
                return R.fail("删除分类失败");
            }

        } catch (Exception e) {
            log.error("管理员删除分类失败，管理员ID: {}, 错误信息: {}", adminUserId, e.getMessage(), e);
            return R.fail("删除分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取子分类列表
     */
    @GetMapping("/category/children/{parentId}")
    @Operation(summary = "获取子分类列表", description = "获取指定父分类的子分类列表")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<List<CategoryResponse>> getChildCategories(
            @PathVariable Long parentId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员获取子分类列表，管理员ID: {}, 父分类ID: {}", adminUserId, parentId);

        try {
            List<CategoryResponse> children = resumeCategoriesService.getChildCategories(parentId);
            log.info("管理员成功获取子分类列表，管理员ID: {}, 子分类数量: {}", adminUserId, children.size());
            return R.ok(children);

        } catch (Exception e) {
            log.error("管理员获取子分类列表失败，管理员ID: {}", adminUserId, e);
            return R.fail("获取子分类列表失败");
        }
    }

    /**
     * 初始化分类数据（开发测试用）
     */
    @PostMapping("/category/init")
    @Operation(summary = "初始化分类数据", description = "创建默认的分类数据（仅开发测试使用）")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<String> initCategories(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        Long adminUserId = userPrincipal != null ? userPrincipal.getUserId() : null;
        log.info("管理员初始化分类数据，管理员ID: {}", adminUserId);

        try {
            // 检查是否已存在分类数据
            long existingCount = resumeCategoriesService.count();
            if (existingCount > 0) {
                return R.fail("分类数据已存在，无需重复初始化");
            }

            // 创建父级分类
            createParentCategory(1L, "设计风格", "style", "style", 1);
            createParentCategory(2L, "适用行业", "industry", "industry", 2);
            createParentCategory(3L, "高校专业", "major", "major", 3);

            // 创建设计风格子分类
            createChildCategory(11L, "经典传统", "classic", "style", 1L, 1);
            createChildCategory(12L, "简约商务", "business", "style", 1L, 2);
            createChildCategory(13L, "创意设计", "creative", "style", 1L, 3);
            createChildCategory(14L, "现代时尚", "modern", "style", 1L, 4);
            createChildCategory(15L, "技术极客", "tech-geek", "style", 1L, 5);
            createChildCategory(16L, "清新文艺", "fresh-literary", "style", 1L, 6);
            createChildCategory(17L, "极简风格", "minimal", "style", 1L, 7);

            // 创建适用行业子分类
            createChildCategory(21L, "技术研发", "tech-dev", "industry", 2L, 1);
            createChildCategory(22L, "产品设计", "product-design", "industry", 2L, 2);
            createChildCategory(23L, "市场营销", "marketing", "industry", 2L, 3);
            createChildCategory(24L, "金融财务", "finance", "industry", 2L, 4);
            createChildCategory(25L, "教育培训", "education", "industry", 2L, 5);
            createChildCategory(26L, "医疗健康", "healthcare", "industry", 2L, 6);
            createChildCategory(27L, "法律服务", "legal", "industry", 2L, 7);
            createChildCategory(28L, "咨询顾问", "consulting", "industry", 2L, 8);
            createChildCategory(29L, "媒体传播", "media", "industry", 2L, 9);
            createChildCategory(30L, "制造业", "manufacturing", "industry", 2L, 10);
            createChildCategory(31L, "其他", "industry-other", "industry", 2L, 11);

            // 创建高校专业子分类
            createChildCategory(41L, "计算机科学", "computer-science", "major", 3L, 1);
            createChildCategory(42L, "软件工程", "software-engineering", "major", 3L, 2);
            createChildCategory(43L, "电子信息", "electronics", "major", 3L, 3);
            createChildCategory(44L, "机械工程", "mechanical", "major", 3L, 4);
            createChildCategory(45L, "工商管理", "business-admin", "major", 3L, 5);
            createChildCategory(46L, "市场营销", "marketing-major", "major", 3L, 6);
            createChildCategory(47L, "会计学", "accounting", "major", 3L, 7);
            createChildCategory(48L, "金融学", "finance-major", "major", 3L, 8);
            createChildCategory(49L, "法学", "law", "major", 3L, 9);
            createChildCategory(50L, "新闻传播", "journalism", "major", 3L, 10);
            createChildCategory(51L, "英语", "english", "major", 3L, 11);
            createChildCategory(52L, "汉语言文学", "chinese-literature", "major", 3L, 12);
            createChildCategory(53L, "心理学", "psychology", "major", 3L, 13);
            createChildCategory(54L, "教育学", "education-major", "major", 3L, 14);
            createChildCategory(55L, "医学", "medicine", "major", 3L, 15);
            createChildCategory(56L, "护理学", "nursing", "major", 3L, 16);
            createChildCategory(57L, "建筑学", "architecture", "major", 3L, 17);
            createChildCategory(58L, "土木工程", "civil-engineering", "major", 3L, 18);
            createChildCategory(59L, "艺术设计", "art-design", "major", 3L, 19);
            createChildCategory(60L, "音乐表演", "music", "major", 3L, 20);
            createChildCategory(61L, "其他", "major-other", "major", 3L, 21);

            log.info("管理员成功初始化分类数据，管理员ID: {}", adminUserId);
            return R.ok("分类数据初始化成功");

        } catch (Exception e) {
            log.error("管理员初始化分类数据失败，管理员ID: {}", adminUserId, e);
            return R.fail("初始化分类数据失败: " + e.getMessage());
        }
    }

    /**
     * 创建父级分类的辅助方法
     */
    private void createParentCategory(Long id, String name, String code, String categoryType, int sortOrder) {
        ResumeCategories category = new ResumeCategories();
        category.setId(id);
        category.setName(name);
        category.setCode(code);
        category.setCategoryType(categoryType);
        category.setParentId(0L);
        category.setDescription(name + "相关的模板分类");
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());
        category.setSortOrder(sortOrder);
        category.setStatus((byte) 1);
        resumeCategoriesService.save(category);
    }

    /**
     * 获取模板预览图
     * 
     * @description 根据模板代码获取预览图，从MinIO读取并返回二进制数据
     * @param templateCode 模板代码
     * @return 预览图的二进制数据
     */
    @GetMapping("/preview-image/{templateCode}")
    @Operation(summary = "获取模板预览图", description = "根据模板代码获取预览图")
    public ResponseEntity<byte[]> getPreviewImage(@PathVariable String templateCode) {
        log.info("获取模板预览图: {}", templateCode);

        try {
            // 1. 根据模板代码查询模板信息
            ResumeTemplates template = resumeTemplatesService.lambdaQuery()
                    .eq(ResumeTemplates::getTemplateCode, templateCode)
                    .one();
            if (template == null) {
                log.warn("模板不存在: {}", templateCode);
                return ResponseEntity.notFound().build();
            }

            // 2. 检查是否有预览图URL
            String previewImageUrl = template.getPreviewImageUrl();
            if (previewImageUrl == null || previewImageUrl.trim().isEmpty()) {
                log.warn("模板 {} 没有预览图", templateCode);
                return ResponseEntity.notFound().build();
            }

            // 3. 从MinIO获取预览图数据
            String bucketName = minioProperties.getTemplateBucketName(); // 使用配置的模板桶名
            byte[] imageData = templateUploadService.getPreviewImageData(bucketName, previewImageUrl);
            if (imageData == null || imageData.length == 0) {
                log.warn("预览图数据为空: {}", previewImageUrl);
                return ResponseEntity.notFound().build();
            }

            // 4. 设置响应头
            HttpHeaders headers = new HttpHeaders();
            
            // 根据文件扩展名设置Content-Type
            String contentType = determineImageContentType(previewImageUrl);
            headers.setContentType(MediaType.parseMediaType(contentType));
            
            // 设置缓存控制
            headers.setCacheControl("public, max-age=3600"); // 缓存1小时
            
            // 设置文件名
            String filename = templateCode + "_preview" + getFileExtension(previewImageUrl);
            headers.setContentDispositionFormData("inline", filename);

            log.info("成功获取模板预览图: {}, 大小: {} bytes", templateCode, imageData.length);
            return ResponseEntity.ok().headers(headers).body(imageData);

        } catch (Exception e) {
            log.error("获取模板预览图失败: {}", templateCode, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 根据文件路径确定图片Content-Type
     */
    private String determineImageContentType(String filePath) {
        if (filePath == null) return "image/jpeg";
        
        String lowerPath = filePath.toLowerCase();
        if (lowerPath.endsWith(".png")) {
            return "image/png";
        } else if (lowerPath.endsWith(".jpg") || lowerPath.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerPath.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerPath.endsWith(".webp")) {
            return "image/webp";
        } else {
            return "image/jpeg"; // 默认
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filePath) {
        if (filePath == null || !filePath.contains(".")) {
            return ".jpg";
        }
        
        int lastDotIndex = filePath.lastIndexOf(".");
        return filePath.substring(lastDotIndex);
    }

    /**
     * 获取模板预览内容
     * 
     * @description 获取模板的Vue组件内容用于预览
     * @param templateId 模板ID
     * @return 模板预览内容
     */
    @GetMapping("/{templateId}/preview")
    @Operation(summary = "获取模板预览内容", description = "获取模板的Vue组件内容用于预览")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public R<Map<String, Object>> getTemplatePreview(@PathVariable Long templateId) {
        log.info("获取模板预览内容: templateId={}", templateId);

        try {
            // 1. 查询模板信息
            ResumeTemplates template = resumeTemplatesService.getById(templateId);
            if (template == null) {
                log.warn("模板不存在: templateId={}", templateId);
                return R.fail("模板不存在");
            }

            // 2. 构建预览数据
            Map<String, Object> previewData = new HashMap<>();
            previewData.put("templateId", template.getId());
            previewData.put("templateCode", template.getTemplateCode());
            previewData.put("templateName", template.getName());
            previewData.put("description", template.getDescription());
            
            // 3. 构建预览图片URL（优先使用预览图片）
            String previewUrl = null;
            if (template.getPreviewImageUrl() != null && !template.getPreviewImageUrl().isEmpty()) {
                // 如果有预览图片，返回完整的后端API URL
                previewUrl = "http://localhost:9311/admin/template/preview-image/" + template.getTemplateCode();
                log.info("使用预览图片API URL: {}", previewUrl);
            } else if (template.getVueFilePath() != null) {
                // 如果没有预览图片但有Vue文件，返回Vue组件预览URL
                previewUrl = "/templates/" + template.getTemplateCode() + "/preview";
                log.info("使用Vue组件预览URL: {}", previewUrl);
            } else {
                // 都没有则提供默认预览
                previewUrl = "/templates/default/preview";
                previewData.put("message", "该模板暂无预览文件");
                log.warn("模板无预览内容，使用默认预览: templateId={}", templateId);
            }
            
            previewData.put("previewUrl", previewUrl);

            log.info("成功获取模板预览内容: templateId={}, templateCode={}", 
                     templateId, template.getTemplateCode());
            
            return R.ok(previewData, "获取预览内容成功");

        } catch (Exception e) {
            log.error("获取模板预览内容失败: templateId={}", templateId, e);
            return R.fail("获取预览内容失败: " + e.getMessage());
        }
    }

    private void createChildCategory(Long id, String name, String code, String categoryType, Long parentId, int sortOrder) {
        ResumeCategories category = new ResumeCategories();
        category.setId(id);
        category.setName(name);
        category.setCode(code);
        category.setCategoryType(categoryType);
        category.setParentId(parentId);
        category.setSortOrder(sortOrder);
        category.setStatus((byte) 1);
        resumeCategoriesService.save(category);
    }
} 