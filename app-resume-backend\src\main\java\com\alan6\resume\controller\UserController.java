package com.alan6.resume.controller;

import com.alan6.resume.common.ratelimit.RateLimit;
import com.alan6.resume.common.utils.BusinessLogUtils;
import com.alan6.resume.core.R;
import com.alan6.resume.dto.user.*;
import com.alan6.resume.security.UserPrincipal;
import com.alan6.resume.service.IUsersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户管理控制器
 * 
 * 主要功能：
 * 1. 用户基本信息管理（查看、更新）
 * 2. 用户头像上传和管理
 * 3. 用户密码修改功能
 * 4. 用户设置管理
 * 5. 手机号和邮箱绑定管理
 * 6. 账号注销功能
 * 
 * 安全措施：
 * 1. 所有接口需要用户登录认证
 * 2. 关键操作增加限流保护
 * 3. 敏感操作需要二次验证
 * 4. 完整的操作日志记录
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Tag(name = "用户管理", description = "用户个人信息和设置管理相关接口")
@Validated
public class UserController {

    /**
     * 用户服务
     */
    @Autowired
    private IUsersService usersService;

    /**
     * 获取用户信息
     * 
     * 返回当前登录用户的详细信息，包括：
     * 1. 基本信息（昵称、头像、性别等）
     * 2. 联系方式（手机号、邮箱，脱敏显示）
     * 3. 账号状态和会员信息
     * 4. 注册和登录记录
     * 
     * @param userPrincipal 当前登录用户信息
     * @return 用户详细信息响应
     */
    @GetMapping("/profile")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    public R<UserProfileResponse> getUserProfile(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户查看个人信息", "userId", userId);
        
        try {
            UserProfileResponse response = usersService.getUserProfile(userId);
            
            BusinessLogUtils.logBusiness("用户信息查询成功", 
                    "userId", userId,
                    "username", response.getUsername());
            
            return R.ok(response);
            
        } catch (Exception e) {
            BusinessLogUtils.logError("用户信息查询失败", e, "userId", userId);
            return R.fail("获取用户信息失败");
        }
    }

    /**
     * 更新用户基本信息
     * 
     * 支持更新的字段：
     * 1. 昵称（2-50个字符）
     * 2. 性别（0-未知，1-男，2-女）
     * 3. 生日（不能是未来日期）
     * 4. 语言偏好（zh-CN、en、ja、ko）
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 用户信息更新请求
     * @return 更新结果
     */
    @PutMapping("/profile")
    @Operation(summary = "更新用户信息", description = "更新用户的基本信息")
    @RateLimit(key = "user:update:#{#userPrincipal.userId}", rate = 10, rateInterval = 300)
    public R<Void> updateUserProfile(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated UserProfileRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户更新个人信息", 
                "userId", userId,
                "nickname", request.getNickname(),
                "gender", request.getGender(),
                "preferredLanguage", request.getPreferredLanguage());
        
        try {
            // 参数校验
            if (!request.hasFieldsToUpdate()) {
                return R.fail("没有需要更新的字段");
            }
            
            if (!request.isGenderValid()) {
                return R.fail("性别参数无效");
            }
            
            if (!request.isBirthdayValid()) {
                return R.fail("生日不能是未来日期");
            }
            
            boolean success = usersService.updateUserProfile(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("用户信息更新成功", "userId", userId);
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("用户信息更新失败", "userId", userId);
                return R.fail("更新失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("用户信息更新异常", e, "userId", userId);
            return R.fail("更新失败：" + e.getMessage());
        }
    }

    /**
     * 上传用户头像
     * 
     * 文件要求：
     * 1. 支持格式：jpg、png、gif
     * 2. 文件大小：不超过5MB
     * 3. 建议尺寸：200x200像素以上
     * 
     * @param userPrincipal 当前登录用户信息
     * @param file 头像文件
     * @return 上传结果，包含新头像URL
     */
    @PostMapping("/avatar")
    @Operation(summary = "上传头像", description = "上传用户头像，支持jpg、png、gif格式")
    @RateLimit(key = "user:avatar:#{#userPrincipal.userId}", rate = 5, rateInterval = 300)
    public R<AvatarUploadResponse> uploadAvatar(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam("file") @Parameter(description = "头像文件", required = true) MultipartFile file) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户上传头像", 
                "userId", userId,
                "fileName", file.getOriginalFilename(),
                "fileSize", file.getSize());
        
        try {
            // 文件基础校验
            if (file.isEmpty()) {
                return R.fail("文件不能为空");
            }
            
            if (file.getSize() > 5 * 1024 * 1024) {
                return R.fail("文件大小不能超过5MB");
            }
            
            AvatarUploadResponse response = usersService.uploadAvatar(userId, file);
            
            BusinessLogUtils.logBusiness("头像上传成功", 
                    "userId", userId,
                    "avatarUrl", response.getAvatarUrl(),
                    "fileSize", response.getFileSize());
            
            return R.ok(response);
            
        } catch (Exception e) {
            BusinessLogUtils.logError("头像上传失败", e, 
                    "userId", userId,
                    "fileName", file.getOriginalFilename());
            return R.fail("上传失败：" + e.getMessage());
        }
    }

    /**
     * 修改用户密码
     * 
     * 支持两种验证方式：
     * 1. 原密码验证：输入当前密码进行验证
     * 2. 短信验证码验证：通过绑定手机号接收验证码
     * 
     * 密码要求：
     * 1. 长度6-20位
     * 2. 必须包含字母和数字
     * 3. 可包含特殊字符@$!%*?&
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 密码修改请求
     * @return 修改结果
     */
    @PutMapping("/password")
    @Operation(summary = "修改密码", description = "修改用户登录密码，支持原密码验证和短信验证码验证")
    @RateLimit(key = "user:password:#{#userPrincipal.userId}", rate = 3, rateInterval = 300)
    public R<Void> changePassword(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated PasswordChangeRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户修改密码", 
                "userId", userId,
                "verificationType", request.getVerificationType());
        
        try {
            // 参数校验
            if (!request.isValid()) {
                return R.fail("请求参数无效");
            }
            
            if (!request.isPasswordConfirmed()) {
                return R.fail("两次密码输入不一致");
            }
            
            boolean success = usersService.changePassword(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("密码修改成功", "userId", userId);
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("密码修改失败", "userId", userId);
                return R.fail("密码修改失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("密码修改异常", e, "userId", userId);
            return R.fail("修改失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户设置
     * 
     * 返回用户的个性化设置信息，目前包括：
     * 1. 界面语言偏好
     * 
     * @param userPrincipal 当前登录用户信息
     * @return 用户设置信息
     */
    @GetMapping("/settings")
    @Operation(summary = "获取用户设置", description = "获取用户的个性化设置信息")
    public R<UserSettingsResponse> getUserSettings(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户查看设置", "userId", userId);
        
        try {
            UserSettingsResponse response = usersService.getUserSettings(userId);
            
            BusinessLogUtils.logBusiness("用户设置查询成功", "userId", userId);
            return R.ok(response);
            
        } catch (Exception e) {
            BusinessLogUtils.logError("用户设置查询失败", e, "userId", userId);
            return R.fail("获取设置失败");
        }
    }

    /**
     * 更新用户设置
     * 
     * 支持更新的设置项：
     * 1. 界面语言偏好（zh-CN、en、ja、ko）
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 用户设置更新请求
     * @return 更新结果
     */
    @PutMapping("/settings")
    @Operation(summary = "更新用户设置", description = "更新用户的个性化设置")
    @RateLimit(key = "user:settings:#{#userPrincipal.userId}", rate = 10, rateInterval = 300)
    public R<Void> updateUserSettings(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated UserSettingsRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户更新设置", 
                "userId", userId,
                "preferredLanguage", request.getPreferredLanguage());
        
        try {
            if (!request.hasFieldsToUpdate()) {
                return R.fail("没有需要更新的设置");
            }
            
            boolean success = usersService.updateUserSettings(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("用户设置更新成功", "userId", userId);
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("用户设置更新失败", "userId", userId);
                return R.fail("设置更新失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("用户设置更新异常", e, "userId", userId);
            return R.fail("更新失败：" + e.getMessage());
        }
    }

    /**
     * 绑定手机号
     * 
     * 绑定新手机号到当前账号，需要验证短信验证码
     * 
     * 注意事项：
     * 1. 手机号不能已被其他用户使用
     * 2. 必须验证短信验证码
     * 3. 绑定成功后自动设置为已验证状态
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 手机号绑定请求
     * @return 绑定结果
     */
    @PostMapping("/phone/bind")
    @Operation(summary = "绑定手机号", description = "绑定新手机号到当前账号")
    @RateLimit(key = "user:phone:bind:#{#userPrincipal.userId}", rate = 3, rateInterval = 300)
    public R<Void> bindPhone(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated PhoneBindRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户绑定手机号", 
                "userId", userId,
                "phone", request.getPhone());
        
        try {
            if (!request.isBindValid()) {
                return R.fail("请求参数无效");
            }
            
            boolean success = usersService.bindPhone(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("手机号绑定成功", 
                        "userId", userId,
                        "phone", request.getPhone());
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("手机号绑定失败", 
                        "userId", userId,
                        "phone", request.getPhone());
                return R.fail("绑定失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("手机号绑定异常", e, 
                    "userId", userId,
                    "phone", request.getPhone());
            return R.fail("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解绑手机号
     * 
     * 解绑当前账号的手机号，需要验证短信验证码
     * 
     * 注意事项：
     * 1. 必须验证当前绑定手机号的短信验证码
     * 2. 解绑后账号可能无法通过手机号登录
     * 3. 建议解绑前确保有其他登录方式
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 手机号解绑请求
     * @return 解绑结果
     */
    @PostMapping("/phone/unbind")
    @Operation(summary = "解绑手机号", description = "解绑当前账号的手机号")
    @RateLimit(key = "user:phone:unbind:#{#userPrincipal.userId}", rate = 3, rateInterval = 300)
    public R<Void> unbindPhone(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated PhoneBindRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户解绑手机号", "userId", userId);
        
        try {
            if (!request.isUnbindValid()) {
                return R.fail("请求参数无效");
            }
            
            boolean success = usersService.unbindPhone(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("手机号解绑成功", "userId", userId);
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("手机号解绑失败", "userId", userId);
                return R.fail("解绑失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("手机号解绑异常", e, "userId", userId);
            return R.fail("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 绑定邮箱
     * 
     * 绑定新邮箱到当前账号，需要验证邮箱验证码
     * 
     * 注意事项：
     * 1. 邮箱不能已被其他用户使用
     * 2. 必须验证邮箱验证码
     * 3. 绑定成功后可用于密码找回等功能
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 邮箱绑定请求
     * @return 绑定结果
     */
    @PostMapping("/email/bind")
    @Operation(summary = "绑定邮箱", description = "绑定新邮箱到当前账号")
    @RateLimit(key = "user:email:bind:#{#userPrincipal.userId}", rate = 3, rateInterval = 300)
    public R<Void> bindEmail(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated EmailBindRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户绑定邮箱", 
                "userId", userId,
                "email", request.getEmail());
        
        try {
            if (!request.isBindValid()) {
                return R.fail("请求参数无效");
            }
            
            boolean success = usersService.bindEmail(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("邮箱绑定成功", 
                        "userId", userId,
                        "email", request.getEmail());
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("邮箱绑定失败", 
                        "userId", userId,
                        "email", request.getEmail());
                return R.fail("绑定失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("邮箱绑定异常", e, 
                    "userId", userId,
                    "email", request.getEmail());
            return R.fail("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解绑邮箱
     * 
     * 解绑当前账号的邮箱，需要验证邮箱验证码
     * 
     * 注意事项：
     * 1. 必须验证当前绑定邮箱的验证码
     * 2. 解绑后将无法通过邮箱找回密码
     * 3. 建议解绑前确保记住当前密码
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 邮箱解绑请求
     * @return 解绑结果
     */
    @PostMapping("/email/unbind")
    @Operation(summary = "解绑邮箱", description = "解绑当前账号的邮箱")
    @RateLimit(key = "user:email:unbind:#{#userPrincipal.userId}", rate = 3, rateInterval = 300)
    public R<Void> unbindEmail(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated EmailBindRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户解绑邮箱", "userId", userId);
        
        try {
            if (!request.isUnbindValid()) {
                return R.fail("请求参数无效");
            }
            
            boolean success = usersService.unbindEmail(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("邮箱解绑成功", "userId", userId);
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("邮箱解绑失败", "userId", userId);
                return R.fail("解绑失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("邮箱解绑异常", e, "userId", userId);
            return R.fail("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 获取第三方授权列表
     * 
     * 返回当前用户已绑定的第三方平台授权信息
     * 
     * @param userPrincipal 当前登录用户信息
     * @return 第三方授权列表
     */
    @GetMapping("/third-party")
    @Operation(summary = "获取第三方授权", description = "获取用户绑定的第三方平台授权信息")
    public R<Object> getThirdPartyAuths(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户查看第三方授权", "userId", userId);
        
        try {
            // TODO: 实现获取第三方授权逻辑
            // 这里暂时返回空列表
            return R.ok(java.util.Collections.emptyList());
            
        } catch (Exception e) {
            BusinessLogUtils.logError("获取第三方授权失败", e, "userId", userId);
            return R.fail("获取授权信息失败");
        }
    }

    /**
     * 解绑第三方授权
     * 
     * 解绑指定的第三方平台授权
     * 
     * @param userPrincipal 当前登录用户信息
     * @param platform 平台类型
     * @return 解绑结果
     */
    @DeleteMapping("/third-party/{platform}")
    @Operation(summary = "解绑第三方授权", description = "解绑指定的第三方平台授权")
    @RateLimit(key = "user:third-party:unbind:#{#userPrincipal.userId}", rate = 5, rateInterval = 300)
    public R<Void> unbindThirdParty(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable @Parameter(description = "平台类型", required = true) String platform) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户解绑第三方授权", 
                "userId", userId,
                "platform", platform);
        
        try {
            // TODO: 实现解绑第三方授权逻辑
            BusinessLogUtils.logBusiness("第三方授权解绑成功", 
                    "userId", userId,
                    "platform", platform);
            return R.ok();
            
        } catch (Exception e) {
            BusinessLogUtils.logError("第三方授权解绑异常", e, 
                    "userId", userId,
                    "platform", platform);
            return R.fail("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 注销用户账号
     * 
     * 永久注销当前用户账号，此操作不可逆
     * 
     * 安全要求：
     * 1. 必须通过密码验证或短信验证码验证
     * 2. 注销后所有数据将被标记为删除状态
     * 3. 注销后无法恢复，请谨慎操作
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 账号注销请求
     * @return 注销结果
     */
    @PostMapping("/deactivate")
    @Operation(summary = "注销账号", description = "永久注销用户账号，此操作不可逆")
    @RateLimit(key = "user:deactivate:#{#userPrincipal.userId}", rate = 1, rateInterval = 3600)
    public R<Void> deactivateAccount(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated AccountDeactivateRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户申请注销账号", 
                "userId", userId,
                "verificationType", request.getVerificationType(),
                "reason", request.getReason());
        
        try {
            if (!request.isValid()) {
                return R.fail("请求参数无效");
            }
            
            boolean success = usersService.deactivateAccount(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("账号注销成功", 
                        "userId", userId,
                        "reason", request.getReason());
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("账号注销失败", 
                        "userId", userId,
                        "reason", "验证失败");
                return R.fail("注销失败，请检查验证信息");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("账号注销异常", e, 
                    "userId", userId,
                    "verificationType", request.getVerificationType());
            return R.fail("注销失败：" + e.getMessage());
        }
    }

    // ======================== 收藏管理相关接口 ========================

    /**
     * 获取用户收藏列表
     * 
     * 支持按收藏类型筛选，分页查询用户收藏的模板和简历。
     * 返回收藏的详细信息，包括目标对象的基本信息。
     * 
     * @param userPrincipal 当前登录用户信息
     * @param targetType 收藏类型（1:模板, 2:简历, 不传则查询全部）
     * @param current 当前页码（默认1）
     * @param size 每页大小（默认10）
     * @return 收藏列表分页响应
     */
    @GetMapping("/favorites")
    @Operation(summary = "获取用户收藏", description = "获取用户收藏的模板和简历列表，支持分页和类型筛选")
    public R<com.baomidou.mybatisplus.extension.plugins.pagination.Page<UserFavoriteResponse>> getUserFavorites(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam(value = "targetType", required = false) 
            @Parameter(description = "收藏类型（1:模板, 2:简历）", example = "1") Byte targetType,
            @RequestParam(value = "current", defaultValue = "1") 
            @Parameter(description = "当前页码", example = "1") Long current,
            @RequestParam(value = "size", defaultValue = "10") 
            @Parameter(description = "每页大小", example = "10") Long size) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户查看收藏列表", 
                "userId", userId,
                "targetType", targetType,
                "current", current,
                "size", size);
        
        try {
            // 参数校验
            if (current < 1) {
                return R.fail("页码必须大于0");
            }
            if (size < 1 || size > 100) {
                return R.fail("每页大小必须在1-100之间");
            }
            if (targetType != null && targetType != 1 && targetType != 2) {
                return R.fail("收藏类型参数无效");
            }
            
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<UserFavoriteResponse> result = 
                    usersService.getUserFavorites(userId, targetType, current, size);
            
            BusinessLogUtils.logBusiness("收藏列表查询成功", 
                    "userId", userId,
                    "totalCount", result.getTotal(),
                    "currentPage", result.getCurrent());
            
            return R.ok(result);
            
        } catch (Exception e) {
            BusinessLogUtils.logError("收藏列表查询失败", e, "userId", userId);
            return R.fail("获取收藏列表失败：" + e.getMessage());
        }
    }

    /**
     * 添加收藏
     * 
     * 将指定的模板或简历添加到用户收藏列表。
     * 支持收藏模板和简历，防止重复收藏。
     * 
     * @param userPrincipal 当前登录用户信息
     * @param request 收藏请求，包含收藏类型和目标ID
     * @return 添加结果
     */
    @PostMapping("/favorites")
    @Operation(summary = "添加收藏", description = "添加模板或简历到收藏列表")
    @RateLimit(key = "user:favorite:add:#{#userPrincipal.userId}", rate = 20, rateInterval = 300)
    public R<Void> addFavorite(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody @Validated UserFavoriteRequest request) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户添加收藏", 
                "userId", userId,
                "targetType", request.getTargetType(),
                "targetId", request.getTargetId());
        
        try {
            // 参数校验
            if (!request.isTargetTypeValid()) {
                return R.fail("收藏类型无效");
            }
            
            boolean success = usersService.addFavorite(userId, request);
            
            if (success) {
                BusinessLogUtils.logBusiness("收藏添加成功", 
                        "userId", userId,
                        "targetType", request.getTargetType(),
                        "targetId", request.getTargetId());
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("收藏添加失败", 
                        "userId", userId,
                        "targetType", request.getTargetType(),
                        "targetId", request.getTargetId());
                return R.fail("添加收藏失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("收藏添加异常", e, 
                    "userId", userId,
                    "targetType", request.getTargetType(),
                    "targetId", request.getTargetId());
            return R.fail("添加收藏失败：" + e.getMessage());
        }
    }

    /**
     * 取消收藏
     * 
     * 根据收藏ID删除用户的收藏记录。
     * 只能删除当前用户自己的收藏。
     * 
     * @param userPrincipal 当前登录用户信息
     * @param favoriteId 收藏ID
     * @return 取消结果
     */
    @DeleteMapping("/favorites/{favoriteId}")
    @Operation(summary = "取消收藏", description = "根据收藏ID取消收藏")
    @RateLimit(key = "user:favorite:remove:#{#userPrincipal.userId}", rate = 30, rateInterval = 300)
    public R<Void> removeFavorite(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable @Parameter(description = "收藏ID", required = true) Long favoriteId) {
        
        Long userId = userPrincipal.getUserId();
        BusinessLogUtils.logBusiness("用户取消收藏", 
                "userId", userId,
                "favoriteId", favoriteId);
        
        try {
            // 参数校验
            if (favoriteId == null || favoriteId <= 0) {
                return R.fail("收藏ID无效");
            }
            
            boolean success = usersService.removeFavorite(userId, favoriteId);
            
            if (success) {
                BusinessLogUtils.logBusiness("收藏取消成功", 
                        "userId", userId,
                        "favoriteId", favoriteId);
                return R.ok();
            } else {
                BusinessLogUtils.logWarning("收藏取消失败", 
                        "userId", userId,
                        "favoriteId", favoriteId);
                return R.fail("取消收藏失败");
            }
            
        } catch (Exception e) {
            BusinessLogUtils.logError("收藏取消异常", e, 
                    "userId", userId,
                    "favoriteId", favoriteId);
            return R.fail("取消收藏失败：" + e.getMessage());
        }
    }
}
