import{_ as v}from"./DlAUqK2U.js";import{r as u,c as l,a as t,f as i,F as p,g as r,h as b,o as n,t as d,s as m}from"./CURHyiUL.js";const h={class:"admin-orders"},_={class:"content-area"},g={class:"orders-table"},N={class:"table-content"},f={class:"data-table"},x={__name:"orders",setup(D){const o=u([{id:"ORD001",userName:"张三",productName:"高级简历模板",amount:29.9,status:"paid",createTime:"2024-01-15 10:30"},{id:"ORD002",userName:"李四",productName:"会员套餐",amount:99,status:"completed",createTime:"2024-01-14 15:20"},{id:"ORD003",userName:"王五",productName:"简历模板包",amount:49.9,status:"pending",createTime:"2024-01-13 09:15"}]),c=e=>({pending:"待支付",paid:"已支付",completed:"已完成",cancelled:"已取消"})[e]||e;return(e,a)=>(n(),l("div",h,[a[4]||(a[4]=t("div",{class:"page-header"},[t("h1",{class:"page-title"},"订单管理"),t("p",{class:"page-description"},"管理用户订单、付款状态和订单详情")],-1)),t("div",_,[a[3]||(a[3]=i('<div class="stats-cards" data-v-ed69881b><div class="stat-card" data-v-ed69881b><div class="stat-icon" data-v-ed69881b>📊</div><div class="stat-content" data-v-ed69881b><h3 data-v-ed69881b>总订单数</h3><p class="stat-number" data-v-ed69881b>1,234</p></div></div><div class="stat-card" data-v-ed69881b><div class="stat-icon" data-v-ed69881b>💰</div><div class="stat-content" data-v-ed69881b><h3 data-v-ed69881b>总收入</h3><p class="stat-number" data-v-ed69881b>¥12,345</p></div></div><div class="stat-card" data-v-ed69881b><div class="stat-icon" data-v-ed69881b>✅</div><div class="stat-content" data-v-ed69881b><h3 data-v-ed69881b>已完成</h3><p class="stat-number" data-v-ed69881b>1,100</p></div></div><div class="stat-card" data-v-ed69881b><div class="stat-icon" data-v-ed69881b>⏳</div><div class="stat-content" data-v-ed69881b><h3 data-v-ed69881b>待处理</h3><p class="stat-number" data-v-ed69881b>134</p></div></div></div>',1)),t("div",g,[a[2]||(a[2]=i('<div class="table-header" data-v-ed69881b><h2 data-v-ed69881b>订单列表</h2><div class="table-actions" data-v-ed69881b><input type="text" placeholder="搜索订单..." class="search-input" data-v-ed69881b><select class="filter-select" data-v-ed69881b><option value="" data-v-ed69881b>全部状态</option><option value="pending" data-v-ed69881b>待支付</option><option value="paid" data-v-ed69881b>已支付</option><option value="completed" data-v-ed69881b>已完成</option><option value="cancelled" data-v-ed69881b>已取消</option></select></div></div>',1)),t("div",N,[t("table",f,[a[1]||(a[1]=t("thead",null,[t("tr",null,[t("th",null,"订单ID"),t("th",null,"用户"),t("th",null,"商品"),t("th",null,"金额"),t("th",null,"状态"),t("th",null,"创建时间"),t("th",null,"操作")])],-1)),t("tbody",null,[(n(!0),l(p,null,r(b(o),s=>(n(),l("tr",{key:s.id},[t("td",null,d(s.id),1),t("td",null,d(s.userName),1),t("td",null,d(s.productName),1),t("td",null,"¥"+d(s.amount),1),t("td",null,[t("span",{class:m(["status-badge",s.status])},d(c(s.status)),3)]),t("td",null,d(s.createTime),1),a[0]||(a[0]=t("td",null,[t("div",{class:"action-buttons"},[t("button",{class:"btn-view"},"查看"),t("button",{class:"btn-edit"},"编辑")])],-1))]))),128))])])])])])]))}},y=v(x,[["__scopeId","data-v-ed69881b"]]);export{y as default};
