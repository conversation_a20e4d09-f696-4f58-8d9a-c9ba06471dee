<template>
  <div class="training-form">
    <div class="form-header">
      <h4 class="form-title">培训经历</h4>
      <p class="form-desc">添加您参加的培训课程和学习经历</p>
    </div>
    
    <div v-if="formData.training && formData.training.length > 0" class="training-list">
      <div v-for="(training, index) in formData.training" :key="index" class="training-item">
        <div class="training-header">
          <div class="form-group">
            <label class="form-label">培训项目/课程</label>
            <input
              v-model="training.course"
              type="text"
              class="form-input"
              placeholder="如：职场沟通技巧"
              @input="handleTrainingChange"
            />
          </div>
          <div class="form-group">
            <label class="form-label">所在公司</label>
            <input
              v-model="training.company"
              type="text"
              class="form-input"
              placeholder="可选，方便面试官对照经历"
              @input="handleTrainingChange"
            />
          </div>
          <button 
            class="delete-btn"
            @click="removeTraining(index)"
            title="删除培训经历"
          >
            ×
          </button>
        </div>
        
        <div class="training-details">
          <div class="form-group">
            <label class="form-label">培训时间</label>
            <div class="date-range-group">
              <div class="date-picker-wrapper">
                <input
                  v-model="training.startDate"
                  type="text"
                  class="date-input"
                  placeholder="2023-01"
                  readonly
                  @click="showDatePicker(`${index}_start`)"
                />
                <div 
                  v-if="activeDatePicker === `${index}_start`" 
                  class="date-picker-dropdown"
                >
                  <div class="date-picker-header">
                    <button type="button" @click="changeYear(-1)">‹</button>
                    <span>{{ currentYear }}</span>
                    <button type="button" @click="changeYear(1)">›</button>
                  </div>
                  <div class="month-grid">
                    <button
                      v-for="month in 12"
                      :key="month"
                      type="button"
                      class="month-btn"
                      :class="{ active: isSelectedMonth(`${index}_start`, month) }"
                      @click="selectDate(`${index}_start`, currentYear, month)"
                    >
                      {{ month }}月
                    </button>
                  </div>
                </div>
              </div>
              <span class="date-separator">至</span>
              <div class="date-picker-wrapper">
                <input
                  v-model="training.endDate"
                  type="text"
                  class="date-input"
                  placeholder="2023-03 或 至今"
                  readonly
                  @click="showDatePicker(`${index}_end`)"
                />
                <div 
                  v-if="activeDatePicker === `${index}_end`" 
                  class="date-picker-dropdown"
                >
                  <div class="date-picker-header">
                    <button type="button" @click="changeYear(-1)">‹</button>
                    <span>{{ currentYear }}</span>
                    <button type="button" @click="changeYear(1)">›</button>
                  </div>
                  <div class="month-grid">
                    <button
                      v-for="month in 12"
                      :key="month"
                      type="button"
                      class="month-btn"
                      :class="{ active: isSelectedMonth(`${index}_end`, month) }"
                      @click="selectDate(`${index}_end`, currentYear, month)"
                    >
                      {{ month }}月
                    </button>
                    <button
                      type="button"
                      class="month-btn present-btn"
                      @click="selectPresent(`${index}_end`)"
                    >
                      至今
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">培训描述</label>
            <RichTextEditor
              v-model="training.description"
              placeholder="请输入培训内容、学习收获、获得证书等..."
              min-height="120px"
              @update:modelValue="handleTrainingChange"
            />
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无培训经历信息</p>
      <p class="empty-hint">点击下方按钮添加培训经历</p>
    </div>
    
    <div class="form-actions">
      <button 
        class="add-btn" 
        @click="addTraining"
        :disabled="formData.training.length >= 10"
      >
        <Icon name="plus" size="sm" />
        <span>添加培训经历</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, ref, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ training: [] })
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = reactive({
  training: []
})

// 防抖定时器
let debounceTimer = null

// 日期选择器状态
const activeDatePicker = ref('')
const currentYear = ref(new Date().getFullYear())

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.training) {
    formData.training = [...newData.training]
  } else {
    formData.training = []
  }
}, { immediate: true, deep: true })

/**
 * 处理培训经历变化 - 实时更新
 */
const handleTrainingChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的定时器，300ms后触发更新
  debounceTimer = setTimeout(() => {
    emit('update', { training: [...formData.training] })
  }, 300)
}



/**
 * 添加培训经历
 */
const addTraining = () => {
  if (formData.training.length >= 10) {
    return
  }
  
  formData.training.push({
    course: '',
    company: '',
    startDate: '',
    endDate: '',
    isOngoing: false,
    description: ''
  })
  handleTrainingChange()
}

/**
 * 显示日期选择器
 */
const showDatePicker = (pickerId) => {
  activeDatePicker.value = pickerId
  // 根据当前值设置年份
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.training[index].startDate 
    : formData.training[index].endDate
  
  if (currentValue && currentValue !== '至今') {
    const year = parseInt(currentValue.split('-')[0])
    if (year) {
      currentYear.value = year
    }
  }
}

/**
 * 改变年份
 */
const changeYear = (delta) => {
  currentYear.value += delta
}

/**
 * 选择日期
 */
const selectDate = (pickerId, year, month) => {
  const dateStr = `${year}-${String(month).padStart(2, '0')}`
  const [index, type] = pickerId.split('_')
  
  if (type === 'start') {
    formData.training[index].startDate = dateStr
  } else {
    formData.training[index].endDate = dateStr
    formData.training[index].isOngoing = false
  }
  
  activeDatePicker.value = ''
  handleTrainingChange()
}

/**
 * 选择"至今"
 */
const selectPresent = (pickerId) => {
  const [index] = pickerId.split('_')
  formData.training[index].endDate = '至今'
  formData.training[index].isOngoing = true
  activeDatePicker.value = ''
  handleTrainingChange()
}

/**
 * 判断是否为选中的月份
 */
const isSelectedMonth = (pickerId, month) => {
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.training[index].startDate 
    : formData.training[index].endDate
    
  if (!currentValue || currentValue === '至今') return false
  
  const [year, selectedMonth] = currentValue.split('-')
  return parseInt(year) === currentYear.value && parseInt(selectedMonth) === month
}

/**
 * 点击外部关闭日期选择器
 */
const handleClickOutside = (event) => {
  if (!event.target.closest('.date-picker-wrapper')) {
    activeDatePicker.value = ''
  }
}

// 挂载时添加全局点击监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 卸载时移除监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

/**
 * 删除培训经历
 */
const removeTraining = (index) => {
  formData.training.splice(index, 1)
  handleTrainingChange()
}
</script>

<style scoped>
.training-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.training-list {
  @apply space-y-4 mb-6;
}

.training-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-4;
}

.training-header {
  @apply flex items-start gap-4;
}

.form-group {
  @apply flex-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.delete-btn {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold mt-6;
}

.training-details {
  @apply space-y-4;
}

.date-range-group {
  @apply flex items-center gap-3;
}

.date-picker-wrapper {
  @apply relative flex-1;
}

.date-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer;
}

.date-picker-dropdown {
  @apply absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10;
  min-width: 240px;
}

.date-picker-header {
  @apply flex items-center justify-between px-4 py-3 border-b border-gray-200;
}

.date-picker-header button {
  @apply w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors duration-200;
}

.month-grid {
  @apply grid grid-cols-4 gap-2 p-3;
}

.month-btn {
  @apply px-3 py-2 text-sm text-gray-700 hover:bg-blue-100 hover:text-blue-800 rounded transition-colors duration-200;
  white-space: nowrap;
  min-width: 48px;
}

.month-btn.active {
  @apply bg-blue-500 text-white;
}

.month-btn.present-btn {
  @apply col-span-4 bg-green-100 text-green-800 hover:bg-green-200;
}

.date-separator {
  @apply text-sm text-gray-500 flex-shrink-0;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-sm text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}

.limit-hint {
  @apply text-xs text-gray-500;
}
</style> 