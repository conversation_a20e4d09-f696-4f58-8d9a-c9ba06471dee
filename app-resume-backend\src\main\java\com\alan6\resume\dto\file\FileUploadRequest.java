package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件上传请求DTO
 * 
 * 主要功能：
 * 1. 接收文件上传请求参数
 * 2. 支持MinIO存储配置
 * 3. 提供文件类型和访问策略设置
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "文件上传请求", description = "文件上传时的请求参数")
public class FileUploadRequest {

    /**
     * 文件类型
     * avatar: 头像文件
     * resume: 简历附件
     * template: 模板文件
     * document: 文档文件
     */
    @Schema(description = "文件类型", example = "avatar", allowableValues = {"avatar", "resume", "template", "document"})
    private String type;

    /**
     * 存储桶名称
     * 可选参数，默认根据type自动选择
     */
    @Schema(description = "存储桶名称", example = "avatar-bucket")
    private String bucketName;

    /**
     * 访问策略
     * private: 私有访问
     * public-read: 公开读取
     * public-read-write: 公开读写
     */
    @Schema(description = "访问策略", example = "private", allowableValues = {"private", "public-read", "public-read-write"})
    private String accessPolicy;

    /**
     * 文件描述
     * 可选参数，用于添加文件说明
     */
    @Schema(description = "文件描述", example = "用户头像")
    private String description;

    /**
     * 是否启用版本控制
     * 默认为true
     */
    @Schema(description = "是否启用版本控制", example = "true")
    private Boolean versioning;

    /**
     * 加密类型
     * SSE-S3: 服务端加密
     * SSE-KMS: KMS加密
     * SSE-C: 客户端加密
     */
    @Schema(description = "加密类型", example = "SSE-S3", allowableValues = {"SSE-S3", "SSE-KMS", "SSE-C"})
    private String encryptionType;

    /**
     * 存储类别
     * STANDARD: 标准存储
     * REDUCED_REDUNDANCY: 低冗余存储
     * COLD: 冷存储
     */
    @Schema(description = "存储类别", example = "STANDARD", allowableValues = {"STANDARD", "REDUCED_REDUNDANCY", "COLD"})
    private String storageClass;

    /**
     * 验证文件类型是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValidType() {
        return type != null && 
               (type.equals("avatar") || type.equals("resume") || 
                type.equals("template") || type.equals("document"));
    }

    /**
     * 验证访问策略是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValidAccessPolicy() {
        return accessPolicy == null || 
               accessPolicy.equals("private") || 
               accessPolicy.equals("public-read") || 
               accessPolicy.equals("public-read-write");
    }

    /**
     * 获取默认存储桶名称
     * 根据文件类型返回对应的默认存储桶
     * 
     * @return 默认存储桶名称
     */
    public String getDefaultBucketName() {
        if (type == null) {
            return "default-bucket";
        }
        
        switch (type) {
            case "avatar":
                return "avatar-bucket";
            case "resume":
                return "resume-bucket";
            case "template":
                return "template-bucket";
            case "document":
                return "document-bucket";
            default:
                return "default-bucket";
        }
    }

    /**
     * 获取默认访问策略
     * 
     * @return 默认访问策略
     */
    public String getDefaultAccessPolicy() {
        return accessPolicy != null ? accessPolicy : "private";
    }

    /**
     * 获取默认加密类型
     * 
     * @return 默认加密类型
     */
    public String getDefaultEncryptionType() {
        return encryptionType != null ? encryptionType : "SSE-S3";
    }

    /**
     * 获取默认存储类别
     * 
     * @return 默认存储类别
     */
    public String getDefaultStorageClass() {
        return storageClass != null ? storageClass : "STANDARD";
    }

    /**
     * 验证请求参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return isValidType() && isValidAccessPolicy();
    }
} 