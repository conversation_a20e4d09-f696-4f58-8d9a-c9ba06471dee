package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件列表响应DTO
 * 
 * 主要功能：
 * 1. 返回文件列表查询结果
 * 2. 支持分页信息
 * 3. 提供文件基本信息列表
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "文件列表响应", description = "文件列表查询结果")
public class FileListResponse {

    /**
     * 总记录数
     */
    @Schema(description = "总记录数", example = "25")
    private Long total;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码", example = "1")
    private Integer page;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer size;

    /**
     * 总页数
     */
    @Schema(description = "总页数", example = "3")
    private Integer pages;

    /**
     * 文件列表
     */
    @Schema(description = "文件列表")
    private List<FileItem> files;

    /**
     * 文件项内部类
     * 包含文件的基本信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "文件项", description = "文件基本信息")
    public static class FileItem {

        /**
         * 文件ID
         */
        @Schema(description = "文件ID", example = "F20241222123456789")
        private String fileId;

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "avatar_20241222123456.jpg")
        private String fileName;

        /**
         * 原始文件名
         */
        @Schema(description = "原始文件名", example = "我的头像.jpg")
        private String originalName;

        /**
         * 文件URL
         */
        @Schema(description = "文件URL", example = "https://minio.example.com/avatar/2024/12/22/avatar_20241222123456.jpg")
        private String fileUrl;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小", example = "102400")
        private Long fileSize;

        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "image/jpeg")
        private String fileType;

        /**
         * 存储桶名称
         */
        @Schema(description = "存储桶名称", example = "avatar-bucket")
        private String bucketName;

        /**
         * 访问策略
         */
        @Schema(description = "访问策略", example = "private")
        private String accessPolicy;

        /**
         * 存储类别
         */
        @Schema(description = "存储类别", example = "STANDARD")
        private String storageClass;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间", example = "2024-12-22 15:30:00")
        private LocalDateTime createTime;

        /**
         * 格式化文件大小为可读格式
         * 
         * @return 格式化后的文件大小字符串
         */
        public String getFormattedFileSize() {
            if (fileSize == null) {
                return "未知";
            }
            
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else if (fileSize < 1024 * 1024 * 1024) {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            } else {
                return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
            }
        }

        /**
         * 检查文件是否为图片类型
         * 
         * @return true-图片文件，false-非图片文件
         */
        public boolean isImageFile() {
            return fileType != null && fileType.startsWith("image/");
        }

        /**
         * 检查文件是否为文档类型
         * 
         * @return true-文档文件，false-非文档文件
         */
        public boolean isDocumentFile() {
            return fileType != null && 
                   (fileType.equals("application/pdf") || 
                    fileType.equals("application/msword") || 
                    fileType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                    fileType.equals("text/plain"));
        }

        /**
         * 检查文件是否为公开访问
         * 
         * @return true-公开访问，false-私有访问
         */
        public boolean isPublicAccess() {
            return accessPolicy != null && 
                   (accessPolicy.equals("public-read") || accessPolicy.equals("public-read-write"));
        }

        /**
         * 获取文件扩展名
         * 
         * @return 文件扩展名
         */
        public String getFileExtension() {
            if (fileName == null) {
                return "";
            }
            
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
                return fileName.substring(lastDotIndex + 1).toLowerCase();
            }
            
            return "";
        }
    }

    /**
     * 检查是否有数据
     * 
     * @return true-有数据，false-无数据
     */
    public boolean hasData() {
        return files != null && !files.isEmpty();
    }

    /**
     * 检查是否有下一页
     * 
     * @return true-有下一页，false-无下一页
     */
    public boolean hasNext() {
        return page != null && pages != null && page < pages;
    }

    /**
     * 检查是否有上一页
     * 
     * @return true-有上一页，false-无上一页
     */
    public boolean hasPrevious() {
        return page != null && page > 1;
    }

    /**
     * 获取当前页的记录数
     * 
     * @return 当前页记录数
     */
    public int getCurrentPageSize() {
        return files != null ? files.size() : 0;
    }

    /**
     * 创建空的文件列表响应
     * 
     * @param page 页码
     * @param size 每页大小
     * @return 空的文件列表响应
     */
    public static FileListResponse empty(Integer page, Integer size) {
        return FileListResponse.builder()
                .total(0L)
                .page(page)
                .size(size)
                .pages(0)
                .files(List.of())
                .build();
    }
} 