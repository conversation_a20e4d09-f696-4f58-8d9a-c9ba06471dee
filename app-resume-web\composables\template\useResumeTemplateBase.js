/**
 * 简历模板基础组合式函数
 * @description 整合所有简历模板的公共功能，提供统一的接口
 * <AUTHOR>
 * @since 1.0.0
 */

import { useResumeDataProcessor } from './useResumeDataProcessor'
import { useModuleOperations } from './useModuleOperations'
import { useTemplateStyles } from './useTemplateStyles'
import { useResumeTemplateLogic } from '../../components/editor/preview/composables/useResumeTemplateLogic'
import { useResumeAnimations } from '../../components/editor/preview/composables/useResumeAnimations'

/**
 * 简历模板基础组合式函数
 * @description 这是所有简历模板的基础函数，提供标准化的功能接口
 * @param {Object} props - 组件属性
 * @param {Function} emit - 事件触发函数
 * @returns {Object} 模板所需的所有功能和数据
 */
export function useResumeTemplateBase(props, emit) {
  
  // ================================
  // 功能模块组合
  // ================================
  
  /**
   * 数据处理功能
   * @description 处理简历数据的合并、格式化、默认值等
   */
  const dataProcessor = useResumeDataProcessor(props)
  
  /**
   * 模块操作功能
   * @description 处理模块和项目的移动、删除、排序等操作
   */
  const moduleOperations = useModuleOperations(props, emit)
  
  /**
   * 样式计算功能
   * @description 处理样式计算、主题色彩、字体设置等
   */
  const styleCalculator = useTemplateStyles(props)
  
  /**
   * 模板逻辑功能
   * @description 处理模块显示逻辑、数据合并等
   */
  const templateLogic = useResumeTemplateLogic(props)
  
  /**
   * 动画效果功能
   * @description 处理模板的动画效果
   */
  const animations = useResumeAnimations()
  
  // ================================
  // 统一接口封装
  // ================================
  
  /**
   * 数据相关接口
   * @description 统一的数据处理接口
   */
  const dataInterface = {
    // 核心数据
    mergedResumeData: dataProcessor.mergedResumeData,
    sortedModules: dataProcessor.sortedModules,
    leftColumnModules: dataProcessor.leftColumnModules,
    rightColumnModules: dataProcessor.rightColumnModules,
    
    // 数据处理方法
    getModuleData: dataProcessor.getModuleData,
    mergeModuleData: dataProcessor.mergeModuleData,
    
    // 格式化工具
    formatWorkPeriod: dataProcessor.formatWorkPeriod,
    formatEducationPeriod: dataProcessor.formatEducationPeriod,
    formatProjectPeriod: dataProcessor.formatProjectPeriod,
    
    // 默认数据
    defaultResumeData: dataProcessor.defaultResumeData
  }
  
  /**
   * 操作相关接口
   * @description 统一的操作处理接口
   */
  const operationInterface = {
    // 悬停状态
    hoveredSkill: moduleOperations.hoveredSkill,
    hoveredExperience: moduleOperations.hoveredExperience,
    hoveredEducation: moduleOperations.hoveredEducation,
    hoveredProject: moduleOperations.hoveredProject,
    
    // 模块操作
    moveModuleUp: moduleOperations.moveModuleUp,
    moveModuleDown: moduleOperations.moveModuleDown,
    deleteModule: moduleOperations.deleteModule,
    getModuleTitle: moduleOperations.getModuleTitle,
    
    // 技能操作
    moveSkillUp: (index) => moduleOperations.moveSkillUp(index, dataInterface.mergedResumeData.value.skills),
    moveSkillDown: (index) => moduleOperations.moveSkillDown(index, dataInterface.mergedResumeData.value.skills),
    deleteSkill: (index) => moduleOperations.deleteSkill(index, dataInterface.mergedResumeData.value.skills),
    
    // 工作经历操作
    moveExperienceUp: (index) => moduleOperations.moveExperienceUp(index, dataInterface.mergedResumeData.value.workExperiences),
    moveExperienceDown: (index) => moduleOperations.moveExperienceDown(index, dataInterface.mergedResumeData.value.workExperiences),
    deleteExperience: (index) => moduleOperations.deleteExperience(index, dataInterface.mergedResumeData.value.workExperiences),
    
    // 教育经历操作
    moveEducationUp: (index) => moduleOperations.moveEducationUp(index, dataInterface.mergedResumeData.value.educations),
    moveEducationDown: (index) => moduleOperations.moveEducationDown(index, dataInterface.mergedResumeData.value.educations),
    deleteEducation: (index) => moduleOperations.deleteEducation(index, dataInterface.mergedResumeData.value.educations),
    
    // 项目经历操作
    moveProjectUp: (index) => moduleOperations.moveProjectUp(index, dataInterface.mergedResumeData.value.projects),
    moveProjectDown: (index) => moduleOperations.moveProjectDown(index, dataInterface.mergedResumeData.value.projects),
    deleteProject: (index) => moduleOperations.deleteProject(index, dataInterface.mergedResumeData.value.projects),
    
    // 基本信息操作
    handleBasicInfoUpdate: moduleOperations.handleBasicInfoUpdate,
    
    // 工具方法
    confirmBatchOperation: moduleOperations.confirmBatchOperation,
    canMove: moduleOperations.canMove,
    getOperationButtonStyle: moduleOperations.getOperationButtonStyle
  }
  
  /**
   * 样式相关接口
   * @description 统一的样式计算接口
   */
  const styleInterface = {
    // 核心样式
    mergedTextStyle: styleCalculator.mergedTextStyle,
    titleStyle: styleCalculator.titleStyle,
    subtitleStyle: styleCalculator.subtitleStyle,
    bodyStyle: styleCalculator.bodyStyle,
    pageStyle: styleCalculator.pageStyle,
    
    // 组件样式
    moduleContainerStyle: styleCalculator.moduleContainerStyle,
    itemContainerStyle: styleCalculator.itemContainerStyle,
    hoverStyle: styleCalculator.hoverStyle,
    operationButtonStyle: styleCalculator.operationButtonStyle,
    
    // 特殊样式
    skillProgressStyle: styleCalculator.skillProgressStyle,
    getSkillProgressStyle: styleCalculator.getSkillProgressStyle,
    
    // 工具方法
    mergeStyles: styleCalculator.mergeStyles,
    parseColor: styleCalculator.parseColor,
    getContrastColor: styleCalculator.getContrastColor,
    generateGradient: styleCalculator.generateGradient,
    getResponsiveFontSize: styleCalculator.getResponsiveFontSize,
    getResponsiveSpacing: styleCalculator.getResponsiveSpacing,
    applyTheme: styleCalculator.applyTheme,
    
    // 常量
    defaultTextStyle: styleCalculator.defaultTextStyle,
    defaultSpacing: styleCalculator.defaultSpacing,
    defaultMargins: styleCalculator.defaultMargins,
    presetThemes: styleCalculator.presetThemes
  }
  
  /**
   * 逻辑相关接口
   * @description 统一的逻辑处理接口
   */
  const logicInterface = {
    shouldShowModule: templateLogic.shouldShowModule,
    getModuleData: templateLogic.getModuleData,
    getModuleTitle: templateLogic.getModuleTitle
  }
  
  /**
   * 动画相关接口
   * @description 统一的动画效果接口
   */
  const animationInterface = {
    addFadeInAnimation: animations.addFadeInAnimation,
    addHighlightAnimation: animations.addHighlightAnimation
  }
  
  // ================================
  // 模板验证功能
  // ================================
  
  /**
   * 验证模板必需属性
   * @description 确保模板组件传入了必需的属性
   * @returns {boolean} 验证结果
   */
  const validateTemplateProps = () => {
    const requiredProps = ['resumeData', 'textStyle', 'isDraggable', 'visibleModules']
    const missingProps = requiredProps.filter(prop => !(prop in props))
    
    if (missingProps.length > 0) {
      console.warn('⚠️ 模板缺少必需属性:', missingProps)
      return false
    }
    
    return true
  }
  
  /**
   * 验证模板必需事件
   * @description 确保模板组件定义了必需的事件
   * @returns {boolean} 验证结果
   */
  const validateTemplateEvents = () => {
    const requiredEvents = [
      'update:resume-data',
      'basic-info-update',
      'module-move-up',
      'module-move-down',
      'module-delete',
      'skill-update',
      'experience-update',
      'education-update',
      'project-update'
    ]
    
    // 这里可以添加事件验证逻辑
    // 由于Vue 3的限制，我们暂时只记录日志
    console.log('📋 模板应该支持的事件:', requiredEvents)
    
    return true
  }
  
  /**
   * 初始化模板
   * @description 执行模板初始化检查和设置
   */
  const initializeTemplate = () => {
    console.log('🚀 初始化简历模板...')
    
    // 验证属性和事件
    const propsValid = validateTemplateProps()
    const eventsValid = validateTemplateEvents()
    
    if (propsValid && eventsValid) {
      console.log('✅ 模板初始化成功')
    } else {
      console.error('❌ 模板初始化失败')
    }
    
    // 记录模板信息
    console.log('📊 模板信息:', {
      modules: dataInterface.sortedModules.value.length,
      leftModules: dataInterface.leftColumnModules.value.length,
      rightModules: dataInterface.rightColumnModules.value.length,
      isDraggable: props.isDraggable,
      isPaginationMode: props.isPaginationMode
    })
  }
  
  // ================================
  // 调试工具
  // ================================
  
  /**
   * 调试信息
   * @description 提供模板调试信息
   */
  const debugInfo = {
    /**
     * 打印所有数据
     */
    printAllData: () => {
      console.log('🔍 模板调试信息:')
      console.log('📝 简历数据:', dataInterface.mergedResumeData.value)
      console.log('🎨 样式配置:', styleInterface.mergedTextStyle.value)
      console.log('📋 模块列表:', dataInterface.sortedModules.value)
    },
    
    /**
     * 打印性能信息
     */
    printPerformanceInfo: () => {
      console.log('⚡ 性能信息:')
      console.log('模块数量:', dataInterface.sortedModules.value.length)
      console.log('技能数量:', dataInterface.mergedResumeData.value.skills?.length || 0)
      console.log('工作经历数量:', dataInterface.mergedResumeData.value.workExperiences?.length || 0)
    },
    
    /**
     * 验证数据完整性
     */
    validateDataIntegrity: () => {
      const data = dataInterface.mergedResumeData.value
      const issues = []
      
      if (!data.basicInfo?.name) issues.push('缺少姓名')
      if (!data.skills?.length) issues.push('缺少技能')
      if (!data.workExperiences?.length) issues.push('缺少工作经历')
      
      if (issues.length > 0) {
        console.warn('⚠️ 数据完整性问题:', issues)
      } else {
        console.log('✅ 数据完整性检查通过')
      }
      
      return issues.length === 0
    }
  }
  
  // 执行初始化
  initializeTemplate()
  
  // ================================
  // 返回统一接口
  // ================================
  
  return {
    // 数据接口
    ...dataInterface,
    
    // 操作接口
    ...operationInterface,
    
    // 样式接口
    ...styleInterface,
    
    // 逻辑接口
    ...logicInterface,
    
    // 动画接口
    ...animationInterface,
    
    // 工具方法
    validateTemplateProps,
    validateTemplateEvents,
    initializeTemplate,
    debugInfo
  }
}

/**
 * 模板标准属性定义
 * @description 所有模板都应该遵循的属性规范
 */
export const TEMPLATE_STANDARD_PROPS = {
  /** 简历数据 */
  resumeData: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value === 'object'
    }
  },
  
  /** 文字样式配置 */
  textStyle: {
    type: Object,
    default: () => ({
      fontSize: '14px',
      lineHeight: '1.6',
      textColor: '#1f2937',
      primaryColor: '#3b82f6',
      secondaryColor: '#6366f1'
    })
  },
  
  /** 是否启用拖拽交互 */
  isDraggable: {
    type: Boolean,
    default: false
  },
  
  /** 可见模块列表 */
  visibleModules: {
    type: Array,
    default: () => ['basic_info', 'education', 'work_experience', 'project', 'skills', 'language', 'award', 'hobbies', 'self_evaluation', 'internship', 'training', 'research_experience', 'publication', 'certificate', 'volunteer_experience', 'portfolio', 'cover_letter', 'custom']
  },
  
  /** 页面索引（分页模式下使用） */
  pageIndex: {
    type: Number,
    default: 0
  },
  
  /** 是否为分页模式 */
  isPaginationMode: {
    type: Boolean,
    default: false
  },
  
  /** 是否为自然流动模式 */
  isNaturalFlow: {
    type: Boolean,
    default: false
  }
}

/**
 * 模板标准事件定义
 * @description 所有模板都应该支持的事件规范
 */
export const TEMPLATE_STANDARD_EVENTS = [
  'update:resume-data',
  'basic-info-update',
  'module-move-up',
  'module-move-down',
  'module-delete',
  'skill-update',
  'experience-update',
  'education-update',
  'project-update',
  'content-height-change'
] 