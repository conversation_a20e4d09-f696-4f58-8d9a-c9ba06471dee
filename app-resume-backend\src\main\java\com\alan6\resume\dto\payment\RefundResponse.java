package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款申请响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "RefundResponse", description = "退款申请响应")
public class RefundResponse {

    /**
     * 退款ID
     */
    @Schema(description = "退款ID", example = "REF20241222123456789")
    private String refundId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "ORD20241222123456789")
    private String orderNo;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额", example = "199.90")
    private BigDecimal refundAmount;

    /**
     * 退款状态
     */
    @Schema(description = "退款状态", example = "processing", 
            allowableValues = {"processing", "success", "failed", "rejected"})
    private String refundStatus;

    /**
     * 退款原因
     */
    @Schema(description = "退款原因", example = "用户主动退款")
    private String refundReason;

    /**
     * 预计到账时间
     */
    @Schema(description = "预计到账时间", example = "1-3个工作日")
    private String estimatedTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-22 16:00:00")
    private LocalDateTime createTime;
} 