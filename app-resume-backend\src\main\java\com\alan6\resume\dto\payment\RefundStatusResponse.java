package com.alan6.resume.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款状态查询响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "RefundStatusResponse", description = "退款状态查询响应")
public class RefundStatusResponse {

    /**
     * 退款ID
     */
    @Schema(description = "退款ID", example = "REF20241222123456789")
    private String refundId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "ORD20241222123456789")
    private String orderNo;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额", example = "199.90")
    private BigDecimal refundAmount;

    /**
     * 退款状态
     */
    @Schema(description = "退款状态", example = "success", 
            allowableValues = {"processing", "success", "failed", "rejected"})
    private String refundStatus;

    /**
     * 退款原因
     */
    @Schema(description = "退款原因", example = "用户主动退款")
    private String refundReason;

    /**
     * 退款时间
     */
    @Schema(description = "退款时间", example = "2024-12-23 10:30:00")
    private LocalDateTime refundTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-22 16:00:00")
    private LocalDateTime createTime;

    /**
     * 第三方平台退款流水号
     */
    @Schema(description = "第三方平台退款流水号", example = "50000000000000000000000000000000")
    private String transactionId;

    /**
     * 退款渠道
     */
    @Schema(description = "退款渠道", example = "ORIGINAL", allowableValues = {"ORIGINAL", "BALANCE"})
    private String refundChannel;

    /**
     * 退款账户
     */
    @Schema(description = "退款账户", example = "原支付账户")
    private String refundAccount;
} 