/**
 * 简历下载服务
 * @description 提供简历的PDF、Word、图片格式下载功能
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, readonly } from 'vue'

/**
 * 下载服务组合式函数
 * @returns {Object} 下载服务对象
 */
export const useDownloadService = () => {
  // ================================
  // 响应式状态
  // ================================
  
  /**
   * 下载状态
   */
  const isDownloading = ref(false)
  
  /**
   * 下载进度
   */
  const downloadProgress = ref(0)
  
  /**
   * 当前下载任务ID
   */
  const currentJobId = ref(null)
  
  // ================================
  // 私有方法
  // ================================
  
  /**
   * 生成导出任务ID
   * @returns {string} 任务ID
   */
  const generateJobId = () => {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    return `export_${timestamp}_${random}`
  }
  
  /**
   * 轮询检查导出状态
   * @param {string} jobId - 任务ID
   * @returns {Promise<Object>} 导出结果
   */
  const pollExportStatus = async (jobId) => {
    const maxAttempts = 15 // 最大轮询次数，合理的轮询次数
    const pollInterval = 2000 // 轮询间隔（毫秒）
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        // 获取认证信息
        let token = null
        try {
          // 动态导入认证服务，避免循环依赖
          const { useAuthService } = await import('~/composables/auth/useAuthService')
          const authService = useAuthService()
          token = authService.getToken()
        } catch (error) {
          console.warn('无法获取认证服务，将以匿名模式请求:', error)
        }
        
        // 构造请求头
        const headers = {}
        
        // 如果有token，添加到请求头
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }
        
        const response = await $fetch(`/api/resume/export/status/${jobId}`, {
          method: 'GET',
          headers: headers
        })
        
        if (response.code === 200) {
          const data = response.data
          
          // 更新进度
          downloadProgress.value = data.progress || 0
          
          // 检查状态
          if (data.status === 'completed') {
            return data
          } else if (data.status === 'failed') {
            throw new Error(data.errorMessage || '导出失败')
          }
          
          // 继续轮询
          await new Promise(resolve => setTimeout(resolve, pollInterval))
        } else {
          throw new Error(response.message || '获取导出状态失败')
        }
      } catch (error) {
        console.error(`轮询导出状态失败 (尝试 ${attempt + 1}/${maxAttempts}):`, error)
        
        if (attempt === maxAttempts - 1) {
          throw error
        }
        
        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }
    
    throw new Error('导出超时，请稍后重试')
  }
  
  /**
   * 触发文件下载
   * @param {string} downloadUrl - 下载链接
   * @param {string} fileName - 文件名
   */
  const triggerDownload = (downloadUrl, fileName) => {
    // 创建隐藏的下载链接
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = fileName
    link.style.display = 'none'
    
    // 添加到文档并触发点击
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
  }
  
  // ================================
  // 公共方法
  // ================================
  
  /**
   * 下载简历
   * @param {Object} options - 下载选项
   * @param {string} options.resumeId - 简历ID
   * @param {string} options.format - 下载格式 (pdf/word/image)
   * @param {Object} options.resumeData - 简历数据
   * @param {Object} options.settings - 预览设置
   * @returns {Promise<void>}
   */
  const downloadResume = async (options) => {
    const { resumeId, format, resumeData, settings } = options
    
    // 参数验证
    if (!resumeId) {
      throw new Error('简历ID不能为空')
    }
    
    if (!format) {
      throw new Error('下载格式不能为空')
    }
    
    if (!['pdf', 'word', 'image'].includes(format)) {
      throw new Error('不支持的下载格式')
    }
    
    try {
      // 设置下载状态
      isDownloading.value = true
      downloadProgress.value = 0
      
      // 生成任务ID
      const jobId = generateJobId()
      currentJobId.value = jobId
      
      console.log(`开始下载任务，任务ID: ${jobId}，格式: ${format}`)
      
      // 构建导出请求
      const exportRequest = {
        resumeId: parseInt(resumeId),
        userId: 1, // TODO: 从用户状态获取
        format: format,
        priority: 5,
        quality: 'high',
        watermark: false,
        exportData: {
          resumeData: resumeData,
          settings: settings,
          jobId: jobId
        },
        options: {
          pageSize: 'A4',
          orientation: 'portrait'
        }
      }
      
      // 获取认证信息
      let token = null
      try {
        // 动态导入认证服务，避免循环依赖
        const { useAuthService } = await import('~/composables/auth/useAuthService')
        const authService = useAuthService()
        token = authService.getToken()
      } catch (error) {
        console.warn('无法获取认证服务，将以匿名模式请求:', error)
      }
      
      // 构造请求头
      const headers = {
        'Content-Type': 'application/json'
      }
      
      // 如果有token，添加到请求头
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      // 提交导出任务
      const response = await $fetch('/api/resume/export', {
        method: 'POST',
        headers: headers,
        body: exportRequest
      })
      
      if (response.code !== 200) {
        throw new Error(response.message || '提交导出任务失败')
      }
      
      const exportData = response.data
      console.log('导出任务提交成功:', exportData)
      
      // 轮询检查导出状态
      const result = await pollExportStatus(exportData.jobId)
      
      // 检查结果
      if (result.downloadUrl) {
        // 触发下载
        triggerDownload(result.downloadUrl, result.fileName)
        
        console.log(`${format.toUpperCase()}下载完成:`, result.fileName)
      } else {
        throw new Error('未获取到下载链接')
      }
      
    } catch (error) {
      console.error('下载失败:', error)
      
      // 如果是认证错误，提供更友好的错误信息
      if (error.statusCode === 401) {
        throw new Error('请先登录后再进行下载操作')
      }
      
      throw error
    } finally {
      // 重置状态
      isDownloading.value = false
      downloadProgress.value = 0
      currentJobId.value = null
    }
  }
  
  /**
   * 下载PDF格式
   * @param {Object} options - 下载选项
   * @returns {Promise<void>}
   */
  const downloadPDF = async (options) => {
    return downloadResume({ ...options, format: 'pdf' })
  }
  
  /**
   * 下载Word格式
   * @param {Object} options - 下载选项
   * @returns {Promise<void>}
   */
  const downloadWord = async (options) => {
    return downloadResume({ ...options, format: 'word' })
  }
  
  /**
   * 下载图片格式
   * @param {Object} options - 下载选项
   * @returns {Promise<void>}
   */
  const downloadImage = async (options) => {
    return downloadResume({ ...options, format: 'image' })
  }
  
  /**
   * 取消当前下载任务
   * @returns {Promise<void>}
   */
  const cancelDownload = async () => {
    if (currentJobId.value) {
      try {
        // 这里可以调用后端API取消任务
        // await $fetch(`/api/resume/export/cancel/${currentJobId.value}`, {
        //   method: 'POST'
        // })
        
        console.log(`取消下载任务: ${currentJobId.value}`)
      } catch (error) {
        console.error('取消下载任务失败:', error)
      } finally {
        // 重置状态
        isDownloading.value = false
        downloadProgress.value = 0
        currentJobId.value = null
      }
    }
  }
  
  // ================================
  // 返回服务对象
  // ================================
  return {
    // 状态
    isDownloading: readonly(isDownloading),
    downloadProgress: readonly(downloadProgress),
    currentJobId: readonly(currentJobId),
    
    // 方法
    downloadResume,
    downloadPDF,
    downloadWord,
    downloadImage,
    cancelDownload
  }
} 