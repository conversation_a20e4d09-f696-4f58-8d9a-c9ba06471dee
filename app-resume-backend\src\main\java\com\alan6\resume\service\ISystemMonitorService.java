package com.alan6.resume.service;

import com.alan6.resume.dto.system.SystemHealthResponse;
import com.alan6.resume.dto.system.SystemMetricsResponse;
import com.alan6.resume.dto.system.SystemStatusResponse;

/**
 * 系统监控服务接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ISystemMonitorService {

    /**
     * 系统健康检查
     *
     * @return 健康检查结果
     */
    SystemHealthResponse healthCheck();

    /**
     * 获取系统状态
     *
     * @return 系统状态信息
     */
    SystemStatusResponse getSystemStatus();

    /**
     * 获取系统指标
     *
     * @return 系统指标信息
     */
    SystemMetricsResponse getSystemMetrics();
} 