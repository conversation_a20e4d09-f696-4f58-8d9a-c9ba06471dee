# 简历模板库组件

本目录包含简历模板库页面的所有组件，按照阿里巴巴编码规范设计，结构清晰，职责分明。

## 组件结构

```
components/template/
├── HeroBanner.vue          # 顶部横条幅组件
├── SearchFilter.vue        # 搜索筛选组件
├── TemplateGrid.vue        # 模板网格容器组件
├── TemplateCard.vue        # 单个模板卡片组件
├── TemplatePagination.vue  # 分页组件
├── index.js               # 统一导出文件
└── README.md              # 文档说明
```

## 主要功能

### 1. HeroBanner 组件
- 展示主标题和slogan
- 提供5个快速导航卡片
- 3秒推荐模板功能
- 装饰性背景动画

### 2. SearchFilter 组件
- 主搜索分类：热门模板、设计风格、行业职位、高校专业、实习生
- 副搜索分类：根据主分类动态显示
- 支持关键词搜索
- 面包屑导航（副搜索页）

### 3. TemplateGrid 组件
- 响应式网格布局（4列×5行）
- 分页处理（每页20个模板）
- 空状态展示
- 加载状态处理

### 4. TemplateCard 组件
- 模板预览图/默认占位符
- 使用人数标签
- 悬浮操作按钮
- 模板信息展示
- 评分显示

### 5. TemplatePagination 组件
- 分页控件
- 页码统计信息
- 智能省略号显示
- 响应式设计

## 数据服务

### useTemplateService Composable
提供模板数据管理和API调用功能：

```javascript
const templateService = useTemplateService()

// 响应式数据
templateService.templates.value      // 筛选后的模板列表
templateService.loading.value        // 加载状态
templateService.error.value          // 错误信息
templateService.currentFilters.value // 当前筛选条件

// API方法
await templateService.fetchTemplates()           // 获取模板列表
await templateService.fetchTemplateById(id)      // 获取模板详情
await templateService.recordTemplateUsage(id)    // 记录使用

// 筛选方法
templateService.setMainCategoryFilter(category)     // 设置主分类
templateService.setSubCategoryFilter(filterData)    // 设置副分类
templateService.setKeywordFilter(keyword)           // 设置关键词
templateService.clearFilters()                      // 清空筛选
```

## 页面类型

### 主搜索页 (main)
- 显示完整的HeroBanner
- 不显示搜索框
- 选择主分类时直接筛选模板

### 副搜索页 (sub)
- 隐藏HeroBanner
- 显示面包屑导航和搜索框
- 选择副分类时进入此页面

## 使用示例

### 基本用法

```vue
<template>
  <div>
    <!-- 主搜索页 -->
    <div v-if="currentPageType === 'main'">
      <HeroBanner 
        @quick-recommend="handleQuickRecommend"
        @nav-click="handleNavClick"
      />
      <SearchFilter 
        :show-search-input="false"
        @main-category-change="handleMainCategoryChange"
        @sub-category-change="handleSubCategoryChange"
      />
      <TemplateGrid
        :templates="templates"
        :loading="loading"
        @preview="handlePreview"
        @use-template="handleUseTemplate"
      />
    </div>
  </div>
</template>

<script setup>
import { useTemplateService } from '~/composables/useTemplateService'
import { HeroBanner, SearchFilter, TemplateGrid } from '~/components/template'

const templateService = useTemplateService()
</script>
```

## 数据结构

### 模板对象结构
```javascript
{
  id: 1,                           // 模板ID
  name: '经典商务简历',            // 模板名称
  category: '简约商务',            // 分类名称
  mainCategory: 'style',           // 主分类ID
  subCategory: 'business',         // 副分类ID
  rating: 4.8,                     // 评分
  usageCount: 5462,               // 使用人数
  thumbnail: null,                 // 缩略图URL
  features: ['自定义配色', '自定义字体'], // 支持功能
  tags: ['商务', '简约', '专业'],    // 标签
  description: '适合商务场合的专业简历模板' // 描述
}
```

### 筛选条件结构
```javascript
{
  mainCategory: {                  // 主分类对象
    id: 'style',
    name: '设计风格'
  },
  subCategory: {                   // 副分类对象
    id: 'business', 
    name: '简约商务'
  },
  keyword: '商务'                  // 搜索关键词
}
```

## 后端接口准备

当前使用模拟数据，需要对接的真实API接口：

```javascript
// 获取模板列表
GET /api/templates?mainCategory=style&subCategory=business&keyword=商务&page=1&size=20

// 获取模板详情  
GET /api/templates/{id}

// 记录模板使用
POST /api/templates/usage { templateId: 1 }

// 获取分类数据
GET /api/template-categories
```

## 样式约定

- 使用项目统一的色彩系统（primary, secondary等）
- 遵循TailwindCSS命名规范
- 响应式设计，适配移动端
- 统一的卡片阴影和圆角设计
- 一致的过渡动画效果

## 注意事项

1. 所有组件都支持响应式设计
2. 图标使用项目统一的SVG图标
3. 错误处理和加载状态需要妥善处理
4. 组件间通信使用事件机制，避免紧耦合
5. 数据获取使用Composable模式，便于测试和复用 