import{u as v}from"./DF3nxAfK.js";import{_ as p}from"./DlAUqK2U.js";import{r as a,i as g,c as i,a as t,t as n,q as k,o as c}from"./CURHyiUL.js";const h={class:"auth-debug-page"},f={class:"debug-section"},b={class:"debug-section"},y={class:"debug-section"},S=["disabled"],I={key:0},x={__name:"auth-debug",setup(C){const u=a({}),r=a({}),l=a(!1),o=a(null),d=v();g(()=>{u.value={admin_token:localStorage.getItem("admin_token")||"null",token:localStorage.getItem("token")||"null",user_info:localStorage.getItem("user_info")||"null"},r.value={admin_token:document.cookie.includes("admin_token")?"exists":"null",all_cookies:document.cookie}});const m=async()=>{l.value=!0,o.value=null;try{const s="<html><body><h1>Test</h1></body></html>",e=new File([s],"test.html",{type:"text/html"}),_=await d.uploadHtmlFiles([e]);o.value=_}catch(s){o.value={error:s.message}}finally{l.value=!1}};return(s,e)=>(c(),i("div",h,[e[3]||(e[3]=t("h1",null,"认证调试页面",-1)),t("div",f,[e[0]||(e[0]=t("h2",null,"LocalStorage 状态",-1)),t("pre",null,n(u.value),1)]),t("div",b,[e[1]||(e[1]=t("h2",null,"Cookie 状态",-1)),t("pre",null,n(r.value),1)]),t("div",y,[e[2]||(e[2]=t("h2",null,"API 测试",-1)),t("button",{onClick:m,disabled:l.value},n(l.value?"上传中...":"测试上传API"),9,S),o.value?(c(),i("pre",I,n(o.value),1)):k("",!0)])]))}},w=p(x,[["__scopeId","data-v-2e8e08de"]]);export{w as default};
