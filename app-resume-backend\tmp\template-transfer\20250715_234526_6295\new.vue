<template>
  <div class="resume-template" :class="{ 'preview-mode': !isDraggable }">

          <!-- 基本信息模块 -->
          <section class="basic-info">
              <div class="basic-info-content">
                  <div class="avatar-section">
                      <img src="https://via.placeholder.com/120x120/667eea/ffffff?text=头像" alt="头像" class="avatar" />
                  </div>
                  <div class="info-section">
                      <h1 class="name" >{{ mergedResumeData.basicInfo.name }}</h1>

                      <div class="basic-details">
                          <div class="detail-group">
                              <div><span >{{ mergedResumeData.basicInfo.gender }}</span> · <span >{{ mergedResumeData.basicInfo.age }}</span> · <span >{{ mergedResumeData.basicInfo.currentCity }}</span></div>
                              <div>意向城市：<span >{{ mergedResumeData.basicInfo.intendedCity }}</span></div>
                              <div>居住地址：<span >{{ mergedResumeData.basicInfo.address }}</span></div>
                          </div>
                          <div class="detail-group">
                              <div>当前状态：<span >{{ mergedResumeData.basicInfo.currentStatus }}</span></div>
                              <div>期望职位：<span >{{ mergedResumeData.basicInfo.expectedPosition }}</span></div>
                              <div>期望薪资：<span >{{ mergedResumeData.basicInfo.expectedSalary }}</span></div>
                          </div>
                          <div class="detail-group" style="grid-column: 1 / -1;">
                              <div>身高：<span >{{ mergedResumeData.basicInfo.height }}</span> · 
                                   体重：<span >{{ mergedResumeData.basicInfo.weight }}</span> · 
                                   政治面貌：<span >{{ mergedResumeData.basicInfo.politicalStatus }}</span></div>
                              <div>婚姻状况：<span >{{ mergedResumeData.basicInfo.maritalStatus }}</span> · 
                                   籍贯：<span >{{ mergedResumeData.basicInfo.hometown }}</span> · 
                                   民族：<span >{{ mergedResumeData.basicInfo.ethnicity }}</span></div>
                          </div>
                      </div>

                      <div class="contact-info">
                          <div class="contact-item">
                              <span class="icon">📱</span>
                              <span >{{ mergedResumeData.basicInfo.phone }}</span>
                          </div>
                          <div class="contact-item">
                              <span class="icon">📧</span>
                              <span >{{ mergedResumeData.basicInfo.email }}</span>
                          </div>
                          <div class="contact-item">
                              <span class="icon">💬</span>
                              <span >{{ mergedResumeData.basicInfo.wechat }}</span>
                          </div>
                          <div class="contact-item">
                              <span class="icon">🌐</span>
                              <span >{{ mergedResumeData.basicInfo.url }}</span>
                          </div>
                          <div class="contact-item">
                              <span class="icon">💻</span>
                              <span >{{ mergedResumeData.basicInfo.url }}</span>
                          </div>
                      </div>

                      <!-- 自定义社交信息 -->
                      <div v-for="(social, index) in mergedResumeData.basic_info.customSocials" :key="social.id" class="contact-item">
                          <span >{{ social.label }}</span>：
                          <span >{{ social.value }}</span>
                      </div>

                      <!-- 自定义信息 -->
                      <div v-for="(info, index) in mergedResumeData.basic_info.customInfos" :key="info.id" class="contact-item">
                          <span >{{ info.label }}</span>：
                          <span >{{ info.value }}</span>
                      </div>
                  </div>
              </div>
          </section>

          <!-- 工作经历模块 -->
          <section class="module-section"><div class="module-header">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('workExperiences')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('workExperiences')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('workExperiences')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">💼 工作经历</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.workExperiences" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredExperience = index" @mouseleave="hoveredExperience = -1" :key="item.id">
                <!-- workExperiences项操作按钮 -->
                <div v-if="isDraggable && hoveredExperience === index" class="item-actions">
                  <button 
                    @click="moveExperienceUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveExperienceDown(index)" 
                    :disabled="index === mergedResumeData.workExperiences.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteExperience(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.company }}</div>
                              <div class="card-subtitle" >{{ item.position }}</div>
                              <div style="font-size: 14px; color: #74b9ff; margin-top: 5px;" >{{ item.department }}</div>
                          </div>
                          <div class="card-date">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.work_experience || resumeData.work_experience">
                      暂无工作经历信息
                  </div>
              </div>
          </section>

          <!-- 教育经历模块 -->
          <section class="module-section"><div class="module-header blue">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('educations')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('educations')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('educations')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">🎓 教育经历</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.educations" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredEducation = index" @mouseleave="hoveredEducation = -1" :key="item.id">
                <!-- educations项操作按钮 -->
                <div v-if="isDraggable && hoveredEducation === index" class="item-actions">
                  <button 
                    @click="moveEducationUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveEducationDown(index)" 
                    :disabled="index === mergedResumeData.educations.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteEducation(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.school }}</div>
                              <div class="card-subtitle">
                                  <span >{{ item.major }}</span> · 
                                  <span >{{ item.degree }}</span>
                                  <span v-if="item.customDegree" >{{ item.customDegree }}</span>
                              </div>
                          </div>
                          <div class="card-date">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.education || resumeData.education">
                      暂无教育经历信息
                  </div>
              </div>
          </section>

          <!-- 项目经历模块 -->
          <section class="module-section"><div class="module-header green">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('projects')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('projects')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('projects')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">🚀 项目经历</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.projects" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredProject = index" @mouseleave="hoveredProject = -1" :key="item.id">
                <!-- projects项操作按钮 -->
                <div v-if="isDraggable && hoveredProject === index" class="item-actions">
                  <button 
                    @click="moveProjectUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveProjectDown(index)" 
                    :disabled="index === mergedResumeData.projects.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteProject(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.name }}</div>
                              <div class="card-subtitle">
                                  <span >{{ item.role }}</span> · 
                                  <span >{{ item.company }}</span>
                              </div>
                          </div>
                          <div class="card-date">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.project || resumeData.project">
                      暂无项目经历信息
                  </div>
              </div>
          </section>

          <!-- 技能特长模块 -->
          <section class="module-section"><div class="module-header purple">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('skills')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('skills')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('skills')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">⚡ 技能特长</h2>
              </div>
              <div class="module-content">
                  <div class="skills-grid">
                      <div class="skill-item" v-for="(skill, index) in mergedResumeData.skills" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredSkill = index" @mouseleave="hoveredSkill = -1" :key="skill.id">
                <!-- skills项操作按钮 -->
                <div v-if="isDraggable && hoveredSkill === index" class="item-actions">
                  <button 
                    @click="moveSkillUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveSkillDown(index)" 
                    :disabled="index === mergedResumeData.skills.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteSkill(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                          <div class="skill-name" >{{ skill.name }}</div>
                          <div class="skill-progress">
                              <div class="skill-progress-bar"  :style="{width: skill.level + '%'}"></div>
                          </div>
                          <div class="skill-level">
                              <span >{{ skill.level }}</span>
                              <span><span >{{ skill.level }}</span>%</span>
                          </div>
                          <div style="font-size: 12px; color: #999; margin-top: 5px;">
                              显示模式：<span >{{ skill.displayMode }}</span>
                          </div>
                      </div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.skills || resumeData.skills">
                      暂无技能信息
                  </div>
              </div>
          </section>

          <!-- 语言能力模块 -->
          <section class="module-section"><div class="module-header orange">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('languages')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('languages')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('languages')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">🌐 语言能力</h2>
              </div>
              <div class="module-content">
                  <div class="grid-container">
                      <div class="grid-item" v-for="(item, index) in mergedResumeData.languages" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredLanguage = index" @mouseleave="hoveredLanguage = -1" :key="item.id">
                <!-- languages项操作按钮 -->
                <div v-if="isDraggable && hoveredLanguage === index" class="item-actions">
                  <button 
                    @click="moveLanguageUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveLanguageDown(index)" 
                    :disabled="index === mergedResumeData.languages.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteLanguage(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                          <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;" >{{ item.name }}</div>
                          <div style="font-size: 14px; color: #667eea; margin-bottom: 8px;" >{{ item.level }}</div>
                          <div style="font-size: 13px; color: #999; margin-bottom: 8px;" >{{ item.name }}</div>
                          <div style="font-size: 14px; color: #636e72;" >{{ item.description }}</div>
                      </div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.language || resumeData.language">
                      暂无语言能力信息
                  </div>
              </div>
          </section>

          <!-- 获奖情况模块 -->
          <section class="module-section"><div class="module-header">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('awards')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('awards')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('awards')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">🏆 获奖情况</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.awards" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredAward = index" @mouseleave="hoveredAward = -1" :key="item.id">
                <!-- awards项操作按钮 -->
                <div v-if="isDraggable && hoveredAward === index" class="item-actions">
                  <button 
                    @click="moveAwardUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveAwardDown(index)" 
                    :disabled="index === mergedResumeData.awards.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteAward(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.name }}</div>
                              <div class="card-subtitle">
                                  <span >{{ item.issuer }}</span> · 
                                  <span >{{ item.level }}</span>
                              </div>
                          </div>
                          <div class="card-date" >{{ item.date }}</div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.award || resumeData.name">
                      暂无获奖情况信息
                  </div>
              </div>
          </section>

          <!-- 证书资质模块 -->
          <section class="module-section"><div class="module-header blue">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('certificates')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('certificates')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('certificates')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">📜 证书资质</h2>
              </div>
              <div class="module-content">
                  <div class="grid-container">
                      <div class="grid-item" v-for="(item, index) in mergedResumeData.certificates" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredCertificate = index" @mouseleave="hoveredCertificate = -1" :key="item.id">
                <!-- certificates项操作按钮 -->
                <div v-if="isDraggable && hoveredCertificate === index" class="item-actions">
                  <button 
                    @click="moveCertificateUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveCertificateDown(index)" 
                    :disabled="index === mergedResumeData.certificates.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteCertificate(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                          <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px;" >{{ item.name }}</div>
                          <div style="font-size: 14px; color: #667eea; margin-bottom: 5px;" >{{ item.issuer }}</div>
                          <div style="font-size: 13px; color: #999; margin-bottom: 5px;" >{{ item.date }}</div>
                          <div style="font-size: 12px; color: #999; margin-bottom: 8px;">证书编号：<span >{{ item.number }}</span></div>
                          <div style="font-size: 14px; color: #636e72;" >{{ item.description }}</div>
                      </div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.certificate || resumeData.name">
                      暂无证书资质信息
                  </div>
              </div>
          </section>

          <!-- 实习经历模块 -->
          <section class="module-section"><div class="module-header green">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('internships')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('internships')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('internships')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">🎯 实习经历</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.internships" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredInternship = index" @mouseleave="hoveredInternship = -1" :key="item.id">
                <!-- internships项操作按钮 -->
                <div v-if="isDraggable && hoveredInternship === index" class="item-actions">
                  <button 
                    @click="moveInternshipUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveInternshipDown(index)" 
                    :disabled="index === mergedResumeData.internships.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteInternship(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.company }}</div>
                              <div class="card-subtitle" >{{ item.position }}</div>
                          </div>
                          <div class="card-date">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.internship || resumeData.internship">
                      暂无实习经历信息
                  </div>
              </div>
          </section>

          <!-- 志愿经历模块 -->
          <section class="module-section"><div class="module-header purple">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('volunteerExperiences')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('volunteerExperiences')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('volunteerExperiences')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">❤️ 志愿经历</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.volunteer_experience" :key="item.id">
                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.issuer }}</div>
                              <div class="card-subtitle">
                                  <span >{{ item.role }}</span> · 
                                  <span >{{ item.location }}</span>
                              </div>
                          </div>
                          <div class="card-date">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.volunteer_experience || resumeData.volunteer_experience">
                      暂无志愿经历信息
                  </div>
              </div>
          </section>

          <!-- 论文发表模块 -->
          <section class="module-section"><div class="module-header orange">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('publications')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('publications')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('publications')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">📚 论文发表</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.publications" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredPublication = index" @mouseleave="hoveredPublication = -1" :key="item.id">
                <!-- publications项操作按钮 -->
                <div v-if="isDraggable && hoveredPublication === index" class="item-actions">
                  <button 
                    @click="movePublicationUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="movePublicationDown(index)" 
                    :disabled="index === mergedResumeData.publications.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deletePublication(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.name }}</div>
                              <div class="card-subtitle">
                                  <span >{{ item.type }}</span> · 
                                  <span >{{ item.journal }}</span>
                              </div>
                          </div>
                          <div class="card-date" >{{ item.date }}</div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.publication || resumeData.publication">
                      暂无论文发表信息
                  </div>
              </div>
          </section>

          <!-- 科研经历模块 -->
          <section class="module-section">
              <div class="module-header">
                  <h2 class="module-title">🔬 科研经历</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.research_experience" :key="item.id">
                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.topic }}</div>
                              <div class="card-subtitle">
                                  <span >{{ item.role }}</span> · 
                                  <span >{{ item.issuer }}</span>
                              </div>
                          </div>
                          <div class="card-date">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.research_experience || resumeData.research_experience">
                      暂无科研经历信息
                  </div>
              </div>
          </section>

          <!-- 培训经历模块 -->
          <section class="module-section"><div class="module-header blue">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('trainings')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('trainings')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('trainings')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">📖 培训经历</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.trainings" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredTraining = index" @mouseleave="hoveredTraining = -1" :key="item.id">
                <!-- trainings项操作按钮 -->
                <div v-if="isDraggable && hoveredTraining === index" class="item-actions">
                  <button 
                    @click="moveTrainingUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveTrainingDown(index)" 
                    :disabled="index === mergedResumeData.trainings.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteTraining(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.course }}</div>
                              <div class="card-subtitle" >{{ item.company }}</div>
                          </div>
                          <div class="card-date">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.training || resumeData.training">
                      暂无培训经历信息
                  </div>
              </div>
          </section>

          <!-- 作品集模块 -->
          <section class="module-section"><div class="module-header green">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('portfolios')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('portfolios')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('portfolios')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">🎨 作品集</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.portfolios" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredPortfolio = index" @mouseleave="hoveredPortfolio = -1" :key="item.id">
                <!-- portfolios项操作按钮 -->
                <div v-if="isDraggable && hoveredPortfolio === index" class="item-actions">
                  <button 
                    @click="movePortfolioUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="movePortfolioDown(index)" 
                    :disabled="index === mergedResumeData.portfolios.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deletePortfolio(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.name }}</div>
                              <div class="card-subtitle">
                                  <span >{{ item.type }}</span> · 
                                  <a href="#" style="color: #667eea;">{{ item.url }}</a>
                              </div>
                          </div>
                          <div class="card-date" >{{ item.date }}</div>
                      </div>
                      <div class="card-description" >{{ item.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.portfolio || resumeData.portfolio">
                      暂无作品集信息
                  </div>
              </div>
          </section>

          <!-- 兴趣爱好模块 -->
          <section class="module-section"><div class="module-header purple">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('hobbies')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('hobbies')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('hobbies')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">🎯 兴趣爱好</h2>
              </div>
              <div class="module-content">
                  <div class="tags-container">
                      <div class="tag" v-for="(hobby, index) in mergedResumeData.hobbies" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredHobby = index" @mouseleave="hoveredHobby = -1" :key="hobby.id">
                <!-- hobbies项操作按钮 -->
                <div v-if="isDraggable && hoveredHobby === index" class="item-actions">
                  <button 
                    @click="moveHobbyUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveHobbyDown(index)" 
                    :disabled="index === mergedResumeData.hobbies.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteHobby(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                          <span >{{ hobby.name }}</span>
                          <span style="margin-left: 5px; opacity: 0.8;" >{{ hobby.level }}</span>
                      </div>
                  </div>
                  <div style="margin-top: 20px;" v-for="(hobby, index) in mergedResumeData.hobbies" :class="{ 'draggable': isDraggable }"@mouseenter="hoveredHobby = index" @mouseleave="hoveredHobby = -1" :key="hobby.id">
                <!-- hobbies项操作按钮 -->
                <div v-if="isDraggable && hoveredHobby === index" class="item-actions">
                  <button 
                    @click="moveHobbyUp(index)" 
                    :disabled="index === 0"
                    class="item-action-btn"
                    title="上移"
                  >
                    ↑
                  </button>
                  <button 
                    @click="moveHobbyDown(index)" 
                    :disabled="index === mergedResumeData.hobbies.length - 1"
                    class="item-action-btn"
                    title="下移"
                  >
                    ↓
                  </button>
                  <button 
                    @click="deleteHobby(index)"
                    class="item-action-btn delete"
                    title="删除"
                  >
                    ×
                  </button>
                </div>

                      <div style="font-weight: 600; margin-bottom: 5px;" >{{ hobby.name }}</div>
                      <div style="font-size: 14px; color: #636e72;" >{{ hobby.description }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.hobbies || resumeData.hobbies">
                      暂无兴趣爱好信息
                  </div>
              </div>
          </section>

          <!-- 自我评价模块 -->
          <section class="module-section"><div class="module-header orange">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('selfEvaluation')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('selfEvaluation')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('selfEvaluation')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">💭 自我评价</h2>
              </div>
              <div class="module-content">
                  <div class="text-content" >{{ mergedResumeData.selfEvaluation.content }}</div>
              </div>
          </section>

          <!-- 自荐信模块 -->
          <section class="module-section"><div class="module-header">
            <!-- 模块操作按钮 -->
            <div v-if="isDraggable" class="module-actions">
              <button @click="moveModuleUp('coverLetters')" class="module-action-btn" title="模块上移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="moveModuleDown('coverLetters')" class="module-action-btn" title="模块下移">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteModule('coverLetters')" class="module-action-btn delete" title="删除模块">
                <svg viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
  <h2 class="module-title">✉️ 自荐信</h2>
              </div>
              <div class="module-content">
                  <div class="text-content" >{{ mergedResumeData.coverLetters.content }}</div>
              </div>
          </section>

          <!-- 自定义模块示例 -->
          <section class="module-section">
              <div class="module-header blue">
                  <h2 class="module-title">🌟 社团活动</h2>
              </div>
              <div class="module-content">
                  <div class="card-item" v-for="(item, index) in mergedResumeData.custom_1640000001.items" :key="item.id">
                      <div class="card-header">
                          <div>
                              <div class="card-title" >{{ item.name }}</div>
                              <div class="card-subtitle" >{{ item.role }}</div>
                          </div>
                          <div class="card-date">
                              <span >{{ item.startDate }}</span> ~ 
                              <span >{{ item.endDate }}</span>
                          </div>
                      </div>
                      <div class="card-description" >{{ item.content }}</div>
                  </div>
                  <div class="empty-state" v-if="!mergedResumeData.custom.custom_1640000001">
                      暂无社团活动信息
                  </div>
              </div>
          </section>

      </div></template>

<script setup>
import { useResumeTemplateBase } from '~/composables/template/useResumeTemplateBase'

// 定义组件属性
const props = defineProps({
  resumeData: {
    type: Object,
    default: () => ({})
  },
  textStyle: {
    type: Object,
    default: () => ({})
  },
  isDraggable: {
    type: Boolean,
    default: false
  },
  visibleModules: {
    type: Array,
    default: () => []
  }
})

// 定义事件
const emit = defineEmits([
  'update-resume-data',
  'update-basic-info',
  'module-operation'
])

// 使用基础组合式函数
const {
  // 数据相关
  mergedResumeData,
  sortedModules,
  leftColumnModules,
  rightColumnModules,
  
  // 样式相关
  mergedTextStyle,
  titleStyle,
  subtitleStyle,
  bodyStyle,
  
  // 操作相关
  hoveredSkill,
  hoveredExperience,
  hoveredEducation,
  hoveredProject,
  hoveredAward,
  hoveredCertificate,
  hoveredLanguage,
  hoveredHobby,
  hoveredInternship,
  hoveredTraining,
  hoveredVolunteer,
  hoveredResearch,
  hoveredPublication,
  hoveredPortfolio,
  moveModuleUp,
  moveModuleDown,
  deleteModule,
  handleBasicInfoUpdate,
  
  // 工具方法
  formatWorkPeriod,
  formatEducationPeriod,
  formatProjectPeriod,
  getSkillProgressStyle
} = useResumeTemplateBase(props, emit)
</script>

<style scoped>
.resume-template * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .resume-template {
            max-width: 900px;
            margin: 0 auto;
            padding: 40px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        /* 基本信息模块 */
        .basic-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .basic-info::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: rotate(45deg);
        }

        .basic-info-content {
            position: relative;
            z-index: 2;
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 30px;
            align-items: center;
        }

        .avatar-section {
            text-align: center;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid rgba(255,255,255,0.3);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }

        .info-section {
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .name {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .basic-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            font-size: 16px;
        }

        .detail-group {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
            font-size: 16px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .icon {
            width: 20px;
            height: 20px;
            opacity: 0.9;
        }

        /* 模块通用样式 */
        .module-section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .module-header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px 30px;
            position: relative;
        }

        .module-header.blue {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .module-header.green {
            background: linear-gradient(135deg, #00b894, #00a085);
        }

        .module-header.purple {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
        }

        .module-header.orange {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .module-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .module-content {
            padding: 30px;
        }

        /* 卡片样式 */
        .card-item {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .card-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-subtitle {
            font-size: 16px;
            color: #667eea;
            font-weight: 500;
        }

        .card-date {
            font-size: 14px;
            color: #74b9ff;
            background: rgba(116, 185, 255, 0.1);
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .card-description {
            font-size: 15px;
            color: #636e72;
            line-height: 1.7;
        }

        /* 技能网格 */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .skill-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .skill-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .skill-progress {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .skill-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .skill-level {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: #636e72;
        }

        /* 标签样式 */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        /* 网格布局 */
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .grid-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            text-align: center;
        }

        /* 文本内容 */
        .text-content {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            font-size: 16px;
            line-height: 1.8;
            color: #2c3e50;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #b2bec3;
            font-style: italic;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .resume-template {
                padding: 20px;
            }

            .basic-info-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 20px;
            }

            .name {
                font-size: 32px;
            }

            .basic-details {
                grid-template-columns: 1fr;
            }

            .contact-info {
                justify-content: center;
            }

            .module-content {
                padding: 20px;
            }

            .card-header {
                flex-direction: column;
                gap: 10px;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 打印样式 */
        @media print {
            .resume-template {
                box-shadow: none;
                padding: 0;
                font-size: 12px;
            }

            .basic-info {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
            }

            .module-header {
                -webkit-print-color-adjust: exact;
            }

            .card-item:hover {
                transform: none;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }
        }
</style>
