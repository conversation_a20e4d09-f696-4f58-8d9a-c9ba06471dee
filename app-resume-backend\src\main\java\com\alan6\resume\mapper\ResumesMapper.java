package com.alan6.resume.mapper;

import com.alan6.resume.entity.Resumes;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 简历主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface ResumesMapper extends BaseMapper<Resumes> {

    /**
     * 增加浏览次数
     * 
     * @param id 简历ID
     */
    @Update("UPDATE resumes SET view_count = view_count + 1 WHERE id = #{id}")
    void incrementViewCount(@Param("id") Long id);

    /**
     * 增加下载次数
     * 
     * @param id 简历ID
     */
    @Update("UPDATE resumes SET download_count = download_count + 1 WHERE id = #{id}")
    void incrementDownloadCount(@Param("id") Long id);

    /**
     * 增加分享次数
     * 
     * @param id 简历ID
     */
    @Update("UPDATE resumes SET share_count = share_count + 1 WHERE id = #{id}")
    void incrementShareCount(@Param("id") Long id);
}
