package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 简历列表响应DTO
 * 
 * @description 返回简历列表的基本信息
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "简历列表响应")
public class ResumeListResponse {

    /**
     * 简历ID
     */
    @Schema(description = "简历ID")
    private Long id;

    /**
     * 简历名称
     */
    @Schema(description = "简历名称")
    private String name;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    private Long templateId;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    private String templateName;

    /**
     * 模板缩略图
     */
    @Schema(description = "模板缩略图")
    private String templateThumbnail;

    /**
     * 简历语言
     */
    @Schema(description = "简历语言")
    private String language;

    /**
     * 简历状态
     */
    @Schema(description = "简历状态（0-草稿，1-完成）")
    private Integer status;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开（0-私有，1-公开）")
    private Integer isPublic;

    /**
     * 浏览次数
     */
    @Schema(description = "浏览次数")
    private Integer viewCount;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数")
    private Integer downloadCount;

    /**
     * 分享次数
     */
    @Schema(description = "分享次数")
    private Integer shareCount;

    /**
     * 最后保存时间
     */
    @Schema(description = "最后保存时间")
    private LocalDateTime lastSaved;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 简历预览图
     */
    @Schema(description = "简历预览图")
    private String previewImage;

    /**
     * 完成度百分比
     */
    @Schema(description = "完成度百分比（0-100）")
    private Integer completeness;
} 