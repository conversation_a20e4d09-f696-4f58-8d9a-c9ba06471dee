package com.alan6.resume.config;

import io.minio.MinioClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * MinIO配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class MinioConfig {

    @Bean
    public MinioClient minioClient(MinioProperties minioProperties) {
        log.info("初始化MinIO客户端，endpoint: {}, bucket: {}", 
                minioProperties.getEndpoint(), minioProperties.getBucketName());
        
        return MinioClient.builder()
                .endpoint(minioProperties.getEndpoint())
                .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
                .build();
    }

    /**
     * MinIO配置属性
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "minio")
    public static class MinioProperties {
        /**
         * MinIO服务地址
         */
        private String endpoint = "http://192.168.16.130:9000";

        /**
         * MinIO访问密钥
         */
        private String accessKey = "admin";

        /**
         * MinIO密钥
         */
        private String secretKey = "admin123";

        /**
         * 存储桶名称（默认桶，用于简历导出等）
         */
        private String bucketName = "resume-exports";

        /**
         * 模板文件专用存储桶名称
         */
        private String templateBucketName = "resume-templates";

        /**
         * 用户头像存储桶名称
         */
        private String avatarBucketName = "avatar-bucket";

        /**
         * 文档存储桶名称
         */
        private String documentBucketName = "document-bucket";
    }
} 