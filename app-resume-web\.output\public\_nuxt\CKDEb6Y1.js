import{a5 as _,P as d,r as h}from"./CURHyiUL.js";import{$ as y}from"./D1FrdRFX.js";var A={};const o={DEFAULT_TIMEOUT:1e4,MAX_RETRY_COUNT:3,API_VERSION:"v1",ENDPOINTS:{AUTH:{REGISTER:"/auth/register",LOGIN:"/auth/login-sms",PASSWORD_LOGIN:"/auth/password-login",LOGOUT:"/auth/logout",SEND_CODE:"/auth/send-code",REFRESH_TOKEN:"/auth/refresh-token",VALIDATE_TOKEN:"/auth/validate-token"},USER:{PROFILE:"/user/profile",UPDATE_PROFILE:"/user/update-profile",CHANGE_PASSWORD:"/user/change-password",BIND_EMAIL:"/user/bind-email",UPLOAD_AVATAR:"/user/upload-avatar"},RESUME:{LIST:"/resume/list",CREATE:"/resume/create",UPDATE:"/resume/update",DELETE:"/resume/delete",DETAIL:"/resume/detail"},TEMPLATE:{LIST:"/template/list",DETAIL:"/template/detail",CATEGORIES:"/template/categories"},ADMIN:{LOGIN:"/auth/login-admin",CHECK_AUTH:"/admin/check-auth",INFO:"/admin/info"}}};class w{static getApiBase(){var e;try{{const s=_();if((e=s==null?void 0:s.public)!=null&&e.apiBase)return console.log("🔧 使用运行时配置API地址:",s.public.apiBase),s.public.apiBase}return A.NUXT_PUBLIC_API_BASE?(console.log("🔧 使用环境变量API地址:",A.NUXT_PUBLIC_API_BASE),A.NUXT_PUBLIC_API_BASE):(console.log("🔧 使用默认API地址: http://localhost:9311"),"http://localhost:9311")}catch(s){return console.warn("获取API基础地址失败，使用默认值:",s.message),console.log("🔧 错误处理：使用默认地址: http://localhost:9311"),"http://localhost:9311"}}static buildApiUrl(e){const s=this.getApiBase(),r=e.startsWith("/")?e:`/${e}`;return`${s.endsWith("/")?s.slice(0,-1):s}${r}`}static getApiInfo(){return{baseUrl:this.getApiBase(),version:o.API_VERSION,timeout:o.DEFAULT_TIMEOUT,endpoints:o.ENDPOINTS}}}class m{static async request(e,s={}){try{const r=w.buildApiUrl(e),u=this.getAuthToken(),i={"Content-Type":"application/json",Accept:"application/json",...s.headers};u&&(i.Authorization=`Bearer ${u}`);const p={...s,headers:i,timeout:s.timeout||o.DEFAULT_TIMEOUT},O=await $fetch(r,p);return this.handleResponse(O)}catch(r){throw console.error("API请求失败:",{endpoint:e,error:r.message,status:r.status,data:r.data}),this.handleError(r)}}static async get(e,s={},r={}){return this.request(e,{method:"GET",params:s,...r})}static async post(e,s={},r={}){return this.request(e,{method:"POST",body:s,...r})}static async put(e,s={},r={}){return this.request(e,{method:"PUT",body:s,...r})}static async delete(e,s={}){return this.request(e,{method:"DELETE",...s})}static getAuthToken(){{const e=localStorage.getItem("auth_token");return console.log("🔑 获取到的Token:",e?`${e.substring(0,10)}...`:"null"),console.log("🔑 Token完整长度:",e?e.length:0),e}}static handleResponse(e){if(e&&typeof e=="object"){if(e.code!==void 0){if(e.code===200)return e.data||e;throw new Error(e.msg||e.message||"请求失败")}return e}return e}static handleError(e){var s;if(!e.status)return new Error("网络连接失败，请检查网络设置");switch(e.status){case 401:return localStorage.removeItem("auth_token"),localStorage.removeItem("user_info"),new Error("登录已过期，请重新登录");case 403:return new Error("没有权限访问此资源");case 404:return new Error("请求的资源不存在");case 429:return new Error("请求过于频繁，请稍后再试");case 500:return new Error("服务器内部错误，请稍后再试");default:return new Error(((s=e.data)==null?void 0:s.message)||e.message||"请求失败")}}}const a=h(!1),n=h(null),l=h(!1),c=h(null),g=h(!1),T=async(t,e={})=>{try{const s=e.method||"GET",r=e.body||e.data,u=e.headers||{};let i;switch(s.toUpperCase()){case"GET":i=await m.get(t,r,{headers:u});break;case"POST":i=await m.post(t,r,{headers:u});break;case"PUT":i=await m.put(t,r,{headers:u});break;case"DELETE":i=await m.delete(t,{headers:u});break;default:throw new Error(`不支持的HTTP方法: ${s}`)}return i}catch(s){throw console.error("API请求失败:",s),new Error(s.message||"网络请求失败")}},f=()=>{{console.log("🔍 从localStorage获取Token，当前URL:",window.location.href),console.log("🔍 localStorage所有项目:",Object.keys(localStorage));const t=localStorage.getItem("auth_token");return console.log("🔍 获取到的auth_token:",t?`${t.substring(0,20)}...`:"null"),t}},E=t=>{if(console.log("💾 存储Token到localStorage:",t?`${t.substring(0,20)}...`:"null"),t){localStorage.setItem("auth_token",t);const e=localStorage.getItem("auth_token");console.log("💾 存储验证 - 立即读取:",e?`${e.substring(0,20)}...`:"null")}},I=()=>{localStorage.removeItem("auth_token"),localStorage.removeItem("user_info")},S=t=>{localStorage.setItem("user_info",JSON.stringify(t))},P=()=>{{console.log("🔍 从localStorage获取用户信息");const t=localStorage.getItem("user_info");return console.log("🔍 获取到的user_info:",t?"exists":"null"),t?JSON.parse(t):null}},U=async t=>{try{l.value=!0,c.value=null;const e={loginType:t.loginType||1,platform:"web",phone:t.phone,smsCode:t.smsCode,deviceId:B(),userAgent:navigator.userAgent},s=await T(o.ENDPOINTS.AUTH.LOGIN,{method:"POST",body:e});return E(s.accessToken),S(s),a.value=!0,n.value=s,g.value=!0,{success:!0,data:s}}catch(e){return c.value=e.message,{success:!1,error:e.message}}finally{l.value=!1}},k=async t=>{try{l.value=!0,c.value=null;const e={phone:t.phone,password:t.password,loginType:t.loginType||2,platform:t.platform||"web"},s=await T(o.ENDPOINTS.AUTH.PASSWORD_LOGIN,{method:"POST",body:e});if(s&&s.token)return E(s.token),S(s),a.value=!0,n.value=s,g.value=!0,{success:!0,data:s};throw new Error((s==null?void 0:s.message)||"登录失败")}catch(e){return c.value=e.message,{success:!1,error:e.message}}finally{l.value=!1}},N=async t=>{try{return l.value=!0,c.value=null,await T(`${o.ENDPOINTS.AUTH.SEND_CODE}?phone=${t}&platform=web`,{method:"POST"}),{success:!0,message:"验证码发送成功"}}catch(e){return c.value=e.message,{success:!1,error:e.message}}finally{l.value=!1}},L=async t=>{try{l.value=!0,c.value=null;const e={registerType:1,phone:t.phone,password:t.password,verificationCode:t.verifyCode,agreeTerms:t.agreeTerms||!0,platform:"web"},s=await T(o.ENDPOINTS.AUTH.REGISTER,{method:"POST",body:e});return E(s.accessToken),S(s),a.value=!0,n.value=s,g.value=!0,{success:!0,data:s}}catch(e){return c.value=e.message,{success:!1,error:e.message}}finally{l.value=!1}},b=async()=>{try{f()&&await T(o.ENDPOINTS.AUTH.LOGOUT,{method:"POST"})}catch(t){console.error("登出请求失败:",t)}finally{I(),a.value=!1,n.value=null,g.value=!0,y.success("退出成功，期待您的再次使用")}return!0},v=async()=>{try{if(!f())return console.log("🔑 没有找到Token，跳过验证"),!1;console.log("🔑 开始验证Token，调用接口:",o.ENDPOINTS.AUTH.VALIDATE_TOKEN);const e=await T(o.ENDPOINTS.AUTH.VALIDATE_TOKEN,{method:"GET"});return console.log("🔑 Token验证响应:",e),e===!0}catch(t){return console.error("🔑 Token验证失败:",t),console.log("🧹 Token无效，清除本地认证信息"),I(),a.value=!1,n.value=null,!1}},R=async()=>{try{return{success:!0,data:await T(o.ENDPOINTS.USER.PROFILE,{method:"GET"})}}catch(t){return console.error("获取用户信息失败:",t),{success:!1,error:t.message}}},D=(t,e)=>{e&&E(e),t&&(S(t),n.value=t),a.value=!0,g.value=!0},C=async()=>{try{const t=f(),e=P();t&&e?(console.log("🔄 初始化认证状态，验证Token有效性"),await v()?(console.log("✅ Token验证通过，恢复登录状态"),a.value=!0,n.value=e):console.log("❌ Token验证失败，已清除认证信息")):(console.log("📭 没有找到存储的认证信息"),a.value=!1,n.value=null)}catch(t){console.error("初始化认证状态失败:",t),I(),a.value=!1,n.value=null}finally{g.value=!0,console.log("🏁 认证状态初始化完成，当前登录状态:",a.value)}},B=()=>{{let t=localStorage.getItem("device_id");return t||(t="web_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),localStorage.setItem("device_id",t)),t}},G=()=>({isLoggedIn:d(a),currentUser:d(n),loading:d(l),error:d(c),isInitialized:d(g),login:U,passwordLogin:k,register:L,logout:b,sendSmsCode:N,getUserInfo:R,validateToken:v,initAuth:C,updateLoginState:D,getToken:f,setToken:E,removeToken:I}),M=Object.freeze(Object.defineProperty({__proto__:null,useAuthService:G},Symbol.toStringTag,{value:"Module"}));export{o as A,m as H,M as a,G as u};
