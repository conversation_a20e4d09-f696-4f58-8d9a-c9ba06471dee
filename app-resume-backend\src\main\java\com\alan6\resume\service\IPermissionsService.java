package com.alan6.resume.service;

import com.alan6.resume.entity.Permissions;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
public interface IPermissionsService extends IService<Permissions> {

    /**
     * 根据用户ID获取权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<Permissions> getPermissionsByUserId(Long userId);

    /**
     * 根据角色ID获取权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permissions> getPermissionsByRoleId(Long roleId);

    /**
     * 根据权限代码获取权限
     *
     * @param permissionCode 权限代码
     * @return 权限信息
     */
    Permissions getPermissionByCode(String permissionCode);

    /**
     * 获取菜单权限树
     *
     * @return 权限树列表
     */
    List<Permissions> getMenuPermissions();

    /**
     * 根据用户ID获取菜单权限树
     *
     * @param userId 用户ID
     * @return 菜单权限树
     */
    List<Permissions> getMenuPermissionsByUserId(Long userId);

    /**
     * 创建权限
     *
     * @param permission 权限信息
     * @return 是否成功
     */
    boolean createPermission(Permissions permission);

    /**
     * 更新权限
     *
     * @param permission 权限信息
     * @return 是否成功
     */
    boolean updatePermission(Permissions permission);

    /**
     * 删除权限
     *
     * @param permissionId 权限ID
     * @return 是否成功
     */
    boolean deletePermission(Long permissionId);

    /**
     * 检查权限代码是否存在
     *
     * @param permissionCode 权限代码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsPermissionCode(String permissionCode, Long excludeId);
} 