package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 支持的语言表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("supported_languages")
@Schema(name = "SupportedLanguages对象", description = "支持的语言表")
public class SupportedLanguages implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "语言ID")
    private Long id;

    @Schema(description = "语言代码(ISO 639-1)")
    private String languageCode;

    @Schema(description = "语言名称")
    private String languageName;

    @Schema(description = "本地名称")
    private String nativeName;

    @Schema(description = "主要使用国家代码(逗号分隔)")
    private String countryCodes;

    @Schema(description = "是否从右到左书写")
    private Byte isRtl;

    @Schema(description = "是否启用")
    private Byte isActive;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否为默认语言")
    private Byte isDefault;

    @Schema(description = "配置内容")
    private String configContent;

    @Schema(description = "配置版本")
    private String configVersion;
}
