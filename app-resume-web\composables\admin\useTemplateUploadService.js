/**
 * 模板上传服务
 * 提供模板文件上传、预览、管理等功能
 */

import { ref, reactive } from 'vue'

export const useTemplateUploadService = () => {
  // 响应式数据
  const uploading = ref(false)
  const error = ref(null)
  const uploadProgress = ref(0)
  
  // 上传信息
  const uploadInfo = reactive({
    name: '',
    description: '',
    categoryId: '',
    isPremium: 0,
    tags: '',
    previewFile: null,
    templateFile: null
  })

  /**
   * 检查模板代码是否可用
   */
  const checkTemplateCode = async (templateCode) => {
    try {
      const response = await $fetch(`/api/admin/template/check-code`, {
        method: 'GET',
        params: {
          templateCode: templateCode
        },
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      })
      
      return response.code === 200 && response.data === true
    } catch (err) {
      console.error('检查模板代码失败:', err)
      return false
    }
  }

  /**
   * 检查存储桶状态
   */
  const checkBucketStatus = async (bucketName) => {
    try {
      const response = await $fetch(`/api/admin/template/check-bucket`, {
        method: 'GET',
        params: {
          bucketName: bucketName
        },
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      })
      
      return response.code === 200 && response.data === true
    } catch (err) {
      console.error('检查存储桶状态失败:', err)
      return false
    }
  }

  /**
   * 上传模板文件（新接口）
   */
  const uploadTemplate = async (files, formData, previewImage = null) => {
    uploading.value = true
    error.value = null
    uploadProgress.value = 0
    
    try {
      console.log('🚀 使用新接口上传模板文件...', { files: files.length, formData, hasPreviewImage: !!previewImage })
      
      // 构建FormData
      const uploadFormData = new FormData()
      
      // 添加基本信息
      uploadFormData.append('templateCode', formData.templateCode)
      uploadFormData.append('templateName', formData.name)
      uploadFormData.append('description', formData.description || '')
      uploadFormData.append('bucketName', formData.bucketName || 'resume-templates')
      uploadFormData.append('industry', formData.industry || '')
      uploadFormData.append('style', formData.style || '')
      uploadFormData.append('colorScheme', formData.colorScheme || '')
      uploadFormData.append('isPremium', formData.isPremium || 0)
      uploadFormData.append('price', formData.price || 0)
      uploadFormData.append('sortOrder', formData.sortOrder || 0)
      
      // 添加分类ID数组
      if (formData.categoryIds && formData.categoryIds.length > 0) {
        formData.categoryIds.forEach(categoryId => {
          uploadFormData.append('categoryIds', categoryId)
        })
      }
      
      // 添加预览图 - 确保预览图和模板文件存储在同一目录
      if (previewImage) {
        console.log('📸 添加预览图到上传数据:', previewImage.name)
        uploadFormData.append('previewImage', previewImage)
        // 标记需要将预览图路径写入数据库的previewImageUrl字段
        uploadFormData.append('savePreviewImageUrl', 'true')
      }
      
      // 添加文件
      files.forEach((file, index) => {
        console.log(`📁 添加文件 ${index + 1}:`, file.name)
        uploadFormData.append('files', file)
      })
      
      const response = await $fetch('/api/admin/template/upload', {
        method: 'POST',
        body: uploadFormData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        onUploadProgress: (progress) => {
          uploadProgress.value = Math.round((progress.loaded / progress.total) * 100)
        }
      })
      
      if (response.code === 200) {
        console.log('✅ 模板上传成功:', response)
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '模板上传失败')
      }
      
    } catch (err) {
      console.error('❌ 模板上传失败:', err)
      error.value = err.message || '模板上传失败'
      
      return {
        success: false,
        error: error.value
      }
    } finally {
      uploading.value = false
      uploadProgress.value = 0
    }
  }

  /**
   * 重置上传信息
   */
  const resetUploadInfo = () => {
    Object.assign(uploadInfo, {
      name: '',
      description: '',
      categoryId: '',
      isPremium: 0,
      tags: '',
      previewFile: null,
      templateFile: null
    })
    error.value = null
    uploadProgress.value = 0
  }

  /**
   * 检查模板名称可用性
   */
  const checkTemplateName = async (templateName) => {
    try {
      console.log('🔍 检查模板名称可用性:', templateName)
      
      const response = await $fetch('/api/admin/template/check-name', {
        method: 'GET',
        params: { templateName },
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      })
      
      console.log('📋 模板名称检查结果:', response)
      return response.data // true表示可用，false表示已存在
      
    } catch (err) {
      console.error('❌ 检查模板名称失败:', err)
      throw new Error(err.message || '检查模板名称失败')
    }
  }

  return {
    // 数据
    uploading,
    error,
    uploadProgress,
    uploadInfo,
    
    // 方法
    uploadTemplate,
    checkTemplateCode,
    checkTemplateName,
    checkBucketStatus,
    resetUploadInfo
  }
} 