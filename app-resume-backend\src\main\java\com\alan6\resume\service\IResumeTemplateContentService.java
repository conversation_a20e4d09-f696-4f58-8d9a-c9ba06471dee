package com.alan6.resume.service;

import com.alan6.resume.entity.ResumeTemplateContent;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 简历模板内容服务接口
 * 
 * @description 提供简历模板内容的业务逻辑处理
 *              包括内容的创建、查询、更新、删除等操作
 *              支持按行业、职位等维度进行内容推荐
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IResumeTemplateContentService extends IService<ResumeTemplateContent> {

    /**
     * 根据内容代码获取内容
     * 
     * @description 通过唯一的内容代码获取特定的模板内容
     * @param code 内容代码
     * @return 匹配的模板内容，如果不存在则返回null
     */
    ResumeTemplateContent getByCode(String code);

    /**
     * 根据行业获取推荐内容列表
     * 
     * @description 获取指定行业的所有可用模板内容
     * @param industry 行业名称
     * @return 推荐的内容列表
     */
    List<ResumeTemplateContent> getRecommendedByIndustry(String industry);

    /**
     * 根据职位获取推荐内容列表
     * 
     * @description 获取指定职位的所有可用模板内容
     * @param position 职位名称
     * @return 推荐的内容列表
     */
    List<ResumeTemplateContent> getRecommendedByPosition(String position);

    /**
     * 根据行业和职位获取最佳匹配内容
     * 
     * @description 智能匹配最适合的内容，优先级：
     *              1. 同时匹配行业和职位的内容
     *              2. 只匹配行业的内容
     *              3. 只匹配职位的内容
     * @param industry 行业名称
     * @param position 职位名称
     * @return 最佳匹配的内容列表
     */
    List<ResumeTemplateContent> getBestMatchContent(String industry, String position);

    /**
     * 根据语言获取内容列表
     * 
     * @description 获取指定语言的所有可用模板内容
     * @param language 语言代码
     * @return 匹配的内容列表
     */
    List<ResumeTemplateContent> getByLanguage(String language);

    /**
     * 获取所有可用内容
     * 
     * @description 获取系统中所有启用状态的模板内容
     * @return 所有可用的内容列表
     */
    List<ResumeTemplateContent> getAllAvailable();

    /**
     * 创建新的模板内容
     * 
     * @description 创建新的模板内容记录，包含完整的业务验证
     * @param content 要创建的内容对象
     * @return 创建成功返回true，否则返回false
     */
    boolean createContent(ResumeTemplateContent content);

    /**
     * 更新模板内容
     * 
     * @description 更新现有的模板内容，包含业务验证
     * @param content 要更新的内容对象
     * @return 更新成功返回true，否则返回false
     */
    boolean updateContent(ResumeTemplateContent content);

    /**
     * 删除模板内容
     * 
     * @description 删除指定的模板内容（软删除）
     * @param contentId 内容ID
     * @return 删除成功返回true，否则返回false
     */
    boolean deleteContent(Long contentId);

    /**
     * 启用模板内容
     * 
     * @description 启用指定的模板内容
     * @param contentId 内容ID
     * @return 操作成功返回true，否则返回false
     */
    boolean enableContent(Long contentId);

    /**
     * 禁用模板内容
     * 
     * @description 禁用指定的模板内容
     * @param contentId 内容ID
     * @return 操作成功返回true，否则返回false
     */
    boolean disableContent(Long contentId);

    /**
     * 验证内容代码是否可用
     * 
     * @description 检查内容代码是否已存在
     * @param code 内容代码
     * @return 如果可用返回true，否则返回false
     */
    boolean isCodeAvailable(String code);

    /**
     * 获取所有行业列表
     * 
     * @description 获取系统中所有已配置的行业类型
     * @return 行业列表
     */
    List<String> getAllIndustries();

    /**
     * 获取所有职位列表
     * 
     * @description 获取系统中所有已配置的职位类型
     * @return 职位列表
     */
    List<String> getAllPositions();

    /**
     * 统计行业内容数量
     * 
     * @description 统计指定行业的内容总数
     * @param industry 行业名称
     * @return 内容数量
     */
    int countByIndustry(String industry);

    /**
     * 统计职位内容数量
     * 
     * @description 统计指定职位的内容总数
     * @param position 职位名称
     * @return 内容数量
     */
    int countByPosition(String position);

    /**
     * 复制内容
     * 
     * @description 复制现有内容创建新的内容记录
     * @param sourceId 源内容ID
     * @param newCode 新内容代码
     * @param newName 新内容名称
     * @return 复制成功返回新内容对象，否则返回null
     */
    ResumeTemplateContent copyContent(Long sourceId, String newCode, String newName);

    /**
     * 批量导入内容
     * 
     * @description 批量导入多个模板内容
     * @param contentList 要导入的内容列表
     * @return 导入成功的数量
     */
    int batchImportContent(List<ResumeTemplateContent> contentList);
} 