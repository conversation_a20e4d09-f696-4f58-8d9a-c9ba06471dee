package com.alan6.resume.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 密码登录请求DTO
 * 
 * 主要功能：
 * 1. 接收前端密码登录请求参数
 * 2. 参数校验和格式化
 * 3. 专门用于已注册用户的密码登录
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@Schema(name = "密码登录请求", description = "用户密码登录请求参数")
public class PasswordLoginRequest {

    /**
     * 登录类型
     * 固定为1，表示密码登录
     */
    @Schema(description = "登录类型：1-密码登录", 
            requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "登录类型不能为空")
    private Integer loginType = 1;

    /**
     * 登录平台
     * web、wechat_mp、douyin_mp、baidu_mp、alipay_mp、app_ios、app_android
     */
    @Schema(description = "登录平台标识", 
            requiredMode = Schema.RequiredMode.REQUIRED, example = "web")
    @NotBlank(message = "登录平台不能为空")
    @Pattern(regexp = "^(web|wechat_mp|douyin_mp|baidu_mp|alipay_mp|app_ios|app_android)$", 
             message = "登录平台格式不正确")
    private String platform;

    /**
     * 手机号
     * 用户注册时的手机号
     */
    @Schema(description = "手机号", 
            requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 登录密码
     * 用户设置的登录密码
     */
    @Schema(description = "登录密码", 
            requiredMode = Schema.RequiredMode.REQUIRED, example = "password123")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    private String password;

    /**
     * 设备标识
     * 用于设备绑定和安全控制
     */
    @Schema(description = "设备标识")
    private String deviceId;

    /**
     * 用户代理信息
     * 记录用户的浏览器或应用信息
     */
    @Schema(description = "用户代理信息")
    private String userAgent;

    /**
     * 客户端IP地址
     * 系统自动获取，前端无需传递
     */
    @Schema(description = "客户端IP地址")
    private String clientIp;

    /**
     * 验证密码登录参数是否完整
     * 
     * @return true-参数完整，false-参数不完整
     */
    public boolean isPasswordLoginValid() {
        return phone != null && !phone.trim().isEmpty() 
               && password != null && !password.trim().isEmpty();
    }
} 