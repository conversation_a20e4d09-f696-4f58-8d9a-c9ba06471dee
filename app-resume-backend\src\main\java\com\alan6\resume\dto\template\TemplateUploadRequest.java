package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 模板上传请求DTO
 * 
 * @description 用于接收前端模板上传的参数配置
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(name = "TemplateUploadRequest", description = "模板上传请求参数")
public class TemplateUploadRequest {

    /**
     * 模板名称
     * 必填，用于展示和识别
     */
    @Schema(description = "模板名称", required = true, example = "商务简约模板")
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 100, message = "模板名称长度不能超过100个字符")
    private String templateName;

    /**
     * 模板代码
     * 用于系统内部识别，通常对应组件名
     */
    @Schema(description = "模板代码（对应组件名）", required = true, example = "business-simple")
    @NotBlank(message = "模板代码不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "模板代码只能包含字母、数字、下划线和连字符")
    @Size(max = 50, message = "模板代码长度不能超过50个字符")
    private String templateCode;

    /**
     * 模板描述
     * 详细描述模板的特点和适用场景
     */
    @Schema(description = "模板描述", example = "适用于商务场景的简约风格简历模板")
    @Size(max = 500, message = "模板描述长度不能超过500个字符")
    private String description;

    /**
     * MinIO存储桶名称
     * 默认为 resume-template
     */
    @Schema(description = "MinIO存储桶名称", example = "resume-template")
    @Pattern(regexp = "^[a-z0-9-]{3,63}$", message = "桶名称格式不正确")
    private String bucketName = "resume-template";

    /**
     * 本地目录路径
     * 用于指定文件在MinIO中的存储路径前缀
     */
    @Schema(description = "本地目录路径", example = "/templates")
    private String localPath = "/templates";

    /**
     * 分类ID
     * 关联模板分类（已废弃，使用categoryIds）
     */
    @Schema(description = "分类ID（已废弃）", example = "1")
    @Deprecated
    private Long categoryId;

    /**
     * 分类ID列表
     * 支持多对多关联
     */
    @Schema(description = "分类ID列表", example = "[11, 21, 31]")
    private List<Long> categoryIds;

    /**
     * 适用行业
     * 模板适用的行业类型
     */
    @Schema(description = "适用行业", example = "IT互联网,金融,教育")
    @Size(max = 200, message = "适用行业长度不能超过200个字符")
    private String industry;

    /**
     * 模板风格
     * 描述模板的设计风格
     */
    @Schema(description = "模板风格", example = "简约,商务,现代")
    @Size(max = 100, message = "模板风格长度不能超过100个字符")
    private String style;

    /**
     * 配色方案
     * 模板的主要配色信息
     */
    @Schema(description = "配色方案", example = "蓝色系,深色主题")
    @Size(max = 100, message = "配色方案长度不能超过100个字符")
    private String colorScheme;

    /**
     * 是否保存预览图URL
     * 当前端传递预览图文件时，用于指示是否需要将预览图URL保存到数据库
     */
    @Schema(description = "是否保存预览图URL到数据库", example = "true")
    private String savePreviewImageUrl;

    /**
     * 是否付费模板
     * 0: 免费, 1: 付费
     */
    @Schema(description = "是否付费模板（0:免费,1:付费）", example = "0")
    private Byte isPremium = 0;

    /**
     * 模板价格
     * 当isPremium为1时必填
     */
    @Schema(description = "模板价格（元）", example = "9.99")
    private BigDecimal price;

    /**
     * 排序权重
     * 用于控制模板在列表中的显示顺序
     */
    @Schema(description = "排序权重", example = "100")
    private Integer sortOrder = 0;

    /**
     * 模板标签
     * JSON格式的标签数组
     */
    @Schema(description = "模板标签（JSON格式）", example = "[\"简约\",\"商务\",\"现代\"]")
    @Size(max = 500, message = "标签数据长度不能超过500个字符")
    private String tags;

    /**
     * 功能特性
     * JSON格式的功能特性列表
     */
    @Schema(description = "支持的功能特性（JSON格式）", example = "[\"自定义配色\",\"多页布局\",\"响应式设计\"]")
    @Size(max = 1000, message = "功能特性数据长度不能超过1000个字符")
    private String features;

    /**
     * 模板配置数据
     * JSON格式的配置信息
     */
    @Schema(description = "模板配置数据（JSON格式）", example = "{\"theme\": \"light\", \"layout\": \"A4\"}")
    @Size(max = 2000, message = "配置数据长度不能超过2000个字符")
    private String configData;
} 