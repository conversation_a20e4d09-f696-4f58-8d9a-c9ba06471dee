package com.alan6.resume.service;

import com.alan6.resume.dto.membership.MembershipBenefitsResponse;
import com.alan6.resume.dto.membership.MembershipCheckResponse;
import com.alan6.resume.dto.membership.MembershipCurrentResponse;
import com.alan6.resume.dto.membership.MembershipHistoryResponse;
import com.alan6.resume.dto.membership.MembershipPurchaseRequest;
import com.alan6.resume.dto.membership.MembershipPurchaseResponse;
import com.alan6.resume.entity.UserMemberships;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 用户会员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IUserMembershipsService extends IService<UserMemberships> {

    /**
     * 获取用户当前会员信息
     * 
     * @param userId 用户ID
     * @return 当前会员信息
     */
    MembershipCurrentResponse getCurrentMembership(Long userId);

    /**
     * 获取用户会员权益
     * 
     * @param userId 用户ID
     * @return 会员权益信息
     */
    MembershipBenefitsResponse getMembershipBenefits(Long userId);

    /**
     * 检查简历创建权限
     * 
     * @param userId 用户ID
     * @return 权限检查结果
     */
    MembershipCheckResponse checkResumeLimit(Long userId);

    /**
     * 检查导出权限
     * 
     * @param userId 用户ID
     * @return 权限检查结果
     */
    MembershipCheckResponse checkExportLimit(Long userId);

    /**
     * 检查模板使用权限
     * 
     * @param userId 用户ID
     * @param templateId 模板ID
     * @return 权限检查结果
     */
    MembershipCheckResponse checkTemplatePermission(Long userId, Long templateId);

    /**
     * 获取用户当前有效会员记录
     * 
     * @param userId 用户ID
     * @return 有效会员记录，如果没有则返回null
     */
    UserMemberships getCurrentValidMembership(Long userId);

    /**
     * 检查用户是否为会员
     * 
     * @param userId 用户ID
     * @return true-是会员，false-不是会员
     */
    Boolean isMember(Long userId);

    /**
     * 检查用户会员是否过期
     * 
     * @param userId 用户ID
     * @return true-已过期，false-未过期
     */
    Boolean isMembershipExpired(Long userId);

    /**
     * 增加简历使用数量
     * 
     * @param userId 用户ID
     * @return 操作是否成功
     */
    Boolean incrementResumeCount(Long userId);

    /**
     * 增加导出使用数量
     * 
     * @param userId 用户ID
     * @return 操作是否成功
     */
    Boolean incrementExportCount(Long userId);

    /**
     * 重置导出次数（每月重置）
     * 
     * @param userId 用户ID
     * @return 操作是否成功
     */
    Boolean resetExportCount(Long userId);

    /**
     * 购买会员
     * 
     * @param userId 用户ID
     * @param request 购买请求
     * @return 购买响应信息
     */
    MembershipPurchaseResponse purchaseMembership(Long userId, MembershipPurchaseRequest request);

    /**
     * 续费会员
     * 
     * @param userId 用户ID
     * @param request 续费请求
     * @return 续费响应信息
     */
    MembershipPurchaseResponse renewMembership(Long userId, MembershipPurchaseRequest request);

    /**
     * 查询会员记录
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 会员记录列表
     */
    MembershipHistoryResponse getMembershipHistory(Long userId, Integer page, Integer size);

    /**
     * 取消会员
     * 
     * @param userId 用户ID
     * @param membershipId 会员记录ID
     * @return 操作是否成功
     */
    Boolean cancelMembership(Long userId, Long membershipId);

    /**
     * 激活会员（支付成功后调用）
     * 
     * @param membershipId 会员记录ID
     * @return 操作是否成功
     */
    Boolean activateMembership(Long membershipId);

}
