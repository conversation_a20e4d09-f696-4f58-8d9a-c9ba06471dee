package com.alan6.resume.common.exception;

/**
 * 限流异常类
 * 
 * 主要功能：
 * 1. 封装限流场景下的异常情况
 * 2. 提供限流键和相关信息
 * 3. 支持限流统计和监控
 * 
 * 使用场景：
 * - API请求频率超限
 * - 用户操作频率控制
 * - 系统资源保护
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
public class RateLimitException extends RuntimeException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 限流键
     */
    private String rateLimitKey;

    /**
     * 限流类型
     */
    private String limitType;

    /**
     * 限流阈值
     */
    private long limitThreshold;

    /**
     * 时间窗口（秒）
     */
    private long timeWindow;

    /**
     * 默认构造函数
     */
    public RateLimitException() {
        super("请求过于频繁，请稍后重试");
    }

    /**
     * 构造函数：仅包含错误消息
     * 
     * @param message 错误消息
     */
    public RateLimitException(String message) {
        super(message);
    }

    /**
     * 构造函数：包含限流键和错误消息
     * 
     * @param rateLimitKey 限流键
     * @param message 错误消息
     */
    public RateLimitException(String rateLimitKey, String message) {
        super(message);
        this.rateLimitKey = rateLimitKey;
    }

    /**
     * 构造函数：包含详细的限流信息
     * 
     * @param rateLimitKey 限流键
     * @param limitType 限流类型
     * @param limitThreshold 限流阈值
     * @param timeWindow 时间窗口（秒）
     * @param message 错误消息
     */
    public RateLimitException(String rateLimitKey, String limitType, 
                             long limitThreshold, long timeWindow, String message) {
        super(message);
        this.rateLimitKey = rateLimitKey;
        this.limitType = limitType;
        this.limitThreshold = limitThreshold;
        this.timeWindow = timeWindow;
    }

    /**
     * 构造函数：包含错误消息和原因异常
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public RateLimitException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 获取限流键
     * 
     * @return 限流键
     */
    public String getRateLimitKey() {
        return rateLimitKey;
    }

    /**
     * 设置限流键
     * 
     * @param rateLimitKey 限流键
     */
    public void setRateLimitKey(String rateLimitKey) {
        this.rateLimitKey = rateLimitKey;
    }

    /**
     * 获取限流类型
     * 
     * @return 限流类型
     */
    public String getLimitType() {
        return limitType;
    }

    /**
     * 设置限流类型
     * 
     * @param limitType 限流类型
     */
    public void setLimitType(String limitType) {
        this.limitType = limitType;
    }

    /**
     * 获取限流阈值
     * 
     * @return 限流阈值
     */
    public long getLimitThreshold() {
        return limitThreshold;
    }

    /**
     * 设置限流阈值
     * 
     * @param limitThreshold 限流阈值
     */
    public void setLimitThreshold(long limitThreshold) {
        this.limitThreshold = limitThreshold;
    }

    /**
     * 获取时间窗口
     * 
     * @return 时间窗口（秒）
     */
    public long getTimeWindow() {
        return timeWindow;
    }

    /**
     * 设置时间窗口
     * 
     * @param timeWindow 时间窗口（秒）
     */
    public void setTimeWindow(long timeWindow) {
        this.timeWindow = timeWindow;
    }

    /**
     * 重写toString方法，提供更详细的异常信息
     * 
     * @return 异常信息字符串
     */
    @Override
    public String toString() {
        return String.format("RateLimitException{rateLimitKey='%s', limitType='%s', " +
                           "limitThreshold=%d, timeWindow=%d, message='%s'}", 
                           rateLimitKey, limitType, limitThreshold, timeWindow, getMessage());
    }
} 