import{l as c,r as p,c as r,a as e,p as l,v as a,d as x,y as f,o as n}from"./CURHyiUL.js";import{$ as i}from"./D1FrdRFX.js";import{_ as y}from"./DlAUqK2U.js";const b={class:"space-y-6"},v={class:"bg-white rounded-lg border border-secondary-200 p-6"},g={class:"space-y-4"},w={class:"flex items-center space-x-4"},h={class:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center"},k=["src"],_={key:1,class:"w-10 h-10 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},j={class:"flex items-center space-x-2"},B=["value"],M={class:"flex items-center space-x-2"},V={key:0,class:"px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"},C={key:1,class:"text-sm text-green-600 flex items-center"},z={class:"bg-white rounded-lg border border-secondary-200 p-6"},E={class:"space-y-4"},A={class:"flex items-center justify-between py-3 border-b border-secondary-100"},F={class:"flex items-center justify-between py-3"},I={class:"relative inline-flex items-center cursor-pointer"},N={__name:"AccountSettings",setup(P){const s=c({avatar:null,username:"user_123456",phone:"138****8888",email:"",emailVerified:!1,twoFactorEnabled:!1}),d=p(!1),m=()=>{i.success("信息已保存")},u=()=>{confirm("确定要注销账户吗？此操作不可恢复！")&&i.info("账户注销功能开发中...")};return(S,t)=>(n(),r("div",null,[t[18]||(t[18]=e("div",{class:"mb-6"},[e("h2",{class:"text-2xl font-bold text-secondary-900"},"账户设置"),e("p",{class:"mt-1 text-sm text-secondary-600"},"管理您的个人信息和账户安全")],-1)),e("div",b,[e("div",v,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-secondary-900 mb-4"},"基本信息",-1)),e("div",g,[e("div",w,[e("div",h,[s.avatar?(n(),r("img",{key:0,src:s.avatar,alt:"用户头像",class:"w-20 h-20 rounded-full object-cover"},null,8,k)):(n(),r("svg",_,t[4]||(t[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),t[5]||(t[5]=e("div",null,[e("button",{class:"px-4 py-2 bg-primary-600 text-white rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors duration-200"}," 更换头像 "),e("p",{class:"mt-1 text-xs text-secondary-500"},"支持 JPG、PNG 格式，大小不超过 2MB")],-1))]),e("div",null,[t[6]||(t[6]=e("label",{class:"block text-sm font-medium text-secondary-700 mb-1"},"用户名",-1)),l(e("input",{"onUpdate:modelValue":t[0]||(t[0]=o=>s.username=o),type:"text",class:"w-full px-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200",placeholder:"请输入用户名"},null,512),[[a,s.username]])]),e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-secondary-700 mb-1"},"手机号",-1)),e("div",j,[e("input",{value:s.phone,type:"text",class:"flex-1 px-4 py-2 border border-secondary-300 rounded-lg bg-secondary-50 cursor-not-allowed",disabled:""},null,8,B),t[7]||(t[7]=e("button",{class:"px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"}," 更换手机号 ",-1))])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-secondary-700 mb-1"},"邮箱",-1)),e("div",M,[l(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>s.email=o),type:"email",class:"flex-1 px-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200",placeholder:"请输入邮箱"},null,512),[[a,s.email]]),s.emailVerified?(n(),r("span",C,t[9]||(t[9]=[e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})],-1),x(" 已验证 ")]))):(n(),r("button",V," 验证邮箱 "))])])]),e("div",{class:"mt-6 flex justify-end"},[e("button",{onClick:m,class:"px-6 py-2 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200"}," 保存修改 ")])]),e("div",z,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-secondary-900 mb-4"},"账户安全",-1)),e("div",E,[e("div",A,[t[12]||(t[12]=e("div",null,[e("h4",{class:"text-sm font-medium text-secondary-900"},"登录密码"),e("p",{class:"text-sm text-secondary-500 mt-1"},"定期更换密码可以提高账户安全性")],-1)),e("button",{onClick:t[2]||(t[2]=o=>d.value=!0),class:"px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"}," 修改密码 ")]),e("div",F,[t[14]||(t[14]=e("div",null,[e("h4",{class:"text-sm font-medium text-secondary-900"},"两步验证"),e("p",{class:"text-sm text-secondary-500 mt-1"},"开启后需要手机验证码才能登录")],-1)),e("label",I,[l(e("input",{"onUpdate:modelValue":t[3]||(t[3]=o=>s.twoFactorEnabled=o),type:"checkbox",class:"sr-only peer"},null,512),[[f,s.twoFactorEnabled]]),t[13]||(t[13]=e("div",{class:"w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"},null,-1))])])])]),e("div",{class:"bg-white rounded-lg border border-secondary-200 p-6"},[t[17]||(t[17]=e("h3",{class:"text-lg font-medium text-secondary-900 mb-4"},"账户操作",-1)),e("div",{class:"space-y-4"},[e("div",{class:"flex items-center justify-between py-3"},[t[16]||(t[16]=e("div",null,[e("h4",{class:"text-sm font-medium text-secondary-900"},"注销账户"),e("p",{class:"text-sm text-secondary-500 mt-1"},"注销后所有数据将被永久删除且无法恢复")],-1)),e("button",{onClick:u,class:"px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 transition-colors duration-200"}," 注销账户 ")])])])])]))}},G=y(N,[["__scopeId","data-v-cc2065e6"]]);export{G as default};
