package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.payment.*;
import com.alan6.resume.entity.Orders;
import com.alan6.resume.service.IOrdersService;
import com.alan6.resume.service.IPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 支付服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@Service
public class PaymentServiceImpl implements IPaymentService {

    @Autowired
    private IOrdersService ordersService;

    /**
     * 获取支付方式列表
     * 
     * @return 支付方式列表
     */
    @Override
    public PaymentMethodResponse getPaymentMethods() {
        log.info("获取支付方式列表");
        
        PaymentMethodResponse response = new PaymentMethodResponse();
        List<PaymentMethodResponse.PaymentMethod> methods = new ArrayList<>();
        
        // 微信支付
        PaymentMethodResponse.PaymentMethod wechat = new PaymentMethodResponse.PaymentMethod();
        wechat.setCode("wechat");
        wechat.setName("微信支付");
        wechat.setIcon("https://cdn.example.com/icons/wechat.png");
        wechat.setEnabled(true);
        wechat.setDescription("使用微信扫码支付");
        wechat.setSupportedTypes(Arrays.asList("qrcode", "h5", "app"));
        methods.add(wechat);
        
        // 支付宝
        PaymentMethodResponse.PaymentMethod alipay = new PaymentMethodResponse.PaymentMethod();
        alipay.setCode("alipay");
        alipay.setName("支付宝");
        alipay.setIcon("https://cdn.example.com/icons/alipay.png");
        alipay.setEnabled(true);
        alipay.setDescription("使用支付宝扫码支付");
        alipay.setSupportedTypes(Arrays.asList("qrcode", "h5", "app"));
        methods.add(alipay);
        
        // 银联支付（暂时禁用）
        PaymentMethodResponse.PaymentMethod unionpay = new PaymentMethodResponse.PaymentMethod();
        unionpay.setCode("unionpay");
        unionpay.setName("银联支付");
        unionpay.setIcon("https://cdn.example.com/icons/unionpay.png");
        unionpay.setEnabled(false);
        unionpay.setDescription("使用银联卡支付");
        unionpay.setSupportedTypes(Arrays.asList("web", "app"));
        methods.add(unionpay);
        
        response.setMethods(methods);
        
        log.info("获取支付方式列表成功，共{}种支付方式", methods.size());
        return response;
    }

    /**
     * 创建支付订单
     * 
     * @param request 创建支付订单请求
     * @return 支付订单信息
     */
    @Override
    public PaymentCreateResponse createPayment(PaymentCreateRequest request) {
        log.info("创建支付订单，订单号：{}，支付方式：{}，支付类型：{}", 
                request.getOrderNo(), request.getPaymentMethod(), request.getPaymentType());
        
        // 参数验证
        validatePaymentCreateRequest(request);
        
        // 查询订单
        Orders order = ordersService.getByOrderNo(request.getOrderNo());
        if (order == null) {
            throw new BusinessException(4001, "订单不存在");
        }
        
        // 验证订单状态
        if (order.getOrderStatus() != 1) { // 1:待支付
            throw new BusinessException(4004, "订单状态异常，当前状态不支持支付");
        }
        
        // 验证支付方式
        if (!isSupportedPaymentMethod(request.getPaymentMethod())) {
            throw new BusinessException(4005, "不支持的支付方式：" + request.getPaymentMethod());
        }
        
        // 验证订单金额
        if (order.getFinalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(4006, "订单金额异常");
        }
        
        // 生成支付ID
        String paymentId = generatePaymentId();
        
        // 构建响应
        PaymentCreateResponse response = new PaymentCreateResponse();
        response.setPaymentId(paymentId);
        response.setOrderNo(request.getOrderNo());
        response.setPaymentMethod(request.getPaymentMethod());
        response.setPaymentType(request.getPaymentType());
        response.setTimeoutMinutes(30);
        response.setCreateTime(LocalDateTime.now());
        
        // 构建支付信息
        PaymentCreateResponse.PaymentInfo paymentInfo = buildPaymentInfo(order, request);
        response.setPaymentInfo(paymentInfo);
        
        log.info("创建支付订单成功，支付ID：{}，订单号：{}", paymentId, request.getOrderNo());
        return response;
    }

    /**
     * 查询支付状态
     * 
     * @param orderNo 订单号
     * @return 支付状态信息
     */
    @Override
    public PaymentStatusResponse getPaymentStatus(String orderNo) {
        log.info("查询支付状态，订单号：{}", orderNo);
        
        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单号不能为空");
        }
        
        // 查询订单
        Orders order = ordersService.getByOrderNo(orderNo);
        if (order == null) {
            throw new BusinessException(4001, "订单不存在");
        }
        
        // 构建响应
        PaymentStatusResponse response = new PaymentStatusResponse();
        response.setOrderNo(orderNo);
        response.setPaymentId("PAY" + orderNo.substring(3)); // 模拟支付ID
        response.setPaymentStatus(convertToPaymentStatus(order.getOrderStatus()));
        response.setOrderStatus(convertToOrderStatus(order.getOrderStatus()));
        response.setPaymentMethod(convertPaymentMethodToString(order.getPaymentMethod()));
        response.setAmount(order.getFinalAmount());
        response.setPaidAmount(order.getOrderStatus() >= 2 ? order.getFinalAmount() : BigDecimal.ZERO);
        response.setCurrency("CNY");
        response.setTransactionId(order.getTradeNo());
        response.setPaymentTime(order.getPayTime());
        response.setCreateTime(order.getCreateTime());
        response.setExpireTime(order.getCreateTime().plusMinutes(30));
        
        // 构建支付详细信息（模拟）
        if (order.getOrderStatus() >= 2) { // 已支付
            PaymentStatusResponse.PaymentDetail paymentDetail = new PaymentStatusResponse.PaymentDetail();
            paymentDetail.setPayerAccount("user_" + order.getUserId());
            paymentDetail.setBankType("CFT");
            paymentDetail.setFeeType("CNY");
            response.setPaymentInfo(paymentDetail);
        }
        
        log.info("查询支付状态成功，订单号：{}，支付状态：{}", orderNo, response.getPaymentStatus());
        return response;
    }

    /**
     * 处理微信支付回调
     * 
     * @param callbackData 回调数据
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param serialNo 证书序列号
     * @return 处理结果
     */
    @Override
    public Boolean handleWechatCallback(String callbackData, String signature, String timestamp, String nonce, String serialNo) {
        log.info("处理微信支付回调，时间戳：{}，随机字符串：{}", timestamp, nonce);
        
        try {
            // 验证签名（这里简化处理，实际应该验证微信支付签名）
            if (!verifyWechatSignature(callbackData, signature, timestamp, nonce, serialNo)) {
                log.error("微信支付回调签名验证失败");
                throw new BusinessException(4015, "支付签名验证失败");
            }
            
            // 解析回调数据（这里简化处理，实际应该解密微信支付数据）
            String orderNo = parseOrderNoFromWechatCallback(callbackData);
            String tradeNo = parseTradeNoFromWechatCallback(callbackData);
            
            // 更新订单状态
            Boolean result = ordersService.updateOrderStatusByOrderNo(orderNo, (byte) 2, tradeNo, "微信支付成功");
            
            log.info("微信支付回调处理完成，订单号：{}，交易号：{}，结果：{}", orderNo, tradeNo, result);
            return result;
        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            throw new BusinessException(4016, "支付回调数据异常");
        }
    }

    /**
     * 处理支付宝支付回调
     * 
     * @param callbackParams 回调参数
     * @return 处理结果
     */
    @Override
    public Boolean handleAlipayCallback(Map<String, String> callbackParams) {
        log.info("处理支付宝支付回调，参数数量：{}", callbackParams.size());
        
        try {
            // 验证签名（这里简化处理，实际应该验证支付宝签名）
            if (!verifyAlipaySignature(callbackParams)) {
                log.error("支付宝支付回调签名验证失败");
                throw new BusinessException(4015, "支付签名验证失败");
            }
            
            // 获取关键参数
            String orderNo = callbackParams.get("out_trade_no");
            String tradeNo = callbackParams.get("trade_no");
            String tradeStatus = callbackParams.get("trade_status");
            
            // 验证交易状态
            if (!"TRADE_SUCCESS".equals(tradeStatus)) {
                log.warn("支付宝交易状态非成功，状态：{}，订单号：{}", tradeStatus, orderNo);
                return false;
            }
            
            // 更新订单状态
            Boolean result = ordersService.updateOrderStatusByOrderNo(orderNo, (byte) 2, tradeNo, "支付宝支付成功");
            
            log.info("支付宝支付回调处理完成，订单号：{}，交易号：{}，结果：{}", orderNo, tradeNo, result);
            return result;
        } catch (Exception e) {
            log.error("处理支付宝支付回调失败", e);
            throw new BusinessException(4016, "支付回调数据异常");
        }
    }

    /**
     * 申请退款
     * 
     * @param orderNo 订单号
     * @param request 退款请求
     * @return 退款响应
     */
    @Override
    public RefundResponse applyRefund(String orderNo, RefundRequest request) {
        log.info("申请退款，订单号：{}，退款金额：{}，退款原因：{}", orderNo, request.getRefundAmount(), request.getRefundReason());
        
        // 查询订单
        Orders order = ordersService.getByOrderNo(orderNo);
        if (order == null) {
            throw new BusinessException(4001, "订单不存在");
        }
        
        // 验证订单状态
        if (order.getOrderStatus() != 2 && order.getOrderStatus() != 3) { // 2:已支付, 3:已完成
            throw new BusinessException(4008, "订单状态不支持退款");
        }
        
        // 验证退款金额
        if (request.getRefundAmount().compareTo(order.getFinalAmount()) > 0) {
            throw new BusinessException(4009, "退款金额不能超过订单金额");
        }
        
        // 生成退款ID
        String refundId = generateRefundId();
        
        // 构建响应（模拟退款处理）
        RefundResponse response = new RefundResponse();
        response.setRefundId(refundId);
        response.setOrderNo(orderNo);
        response.setRefundAmount(request.getRefundAmount());
        response.setRefundStatus("processing");
        response.setRefundReason(request.getRefundReason());
        response.setEstimatedTime("1-3个工作日");
        response.setCreateTime(LocalDateTime.now());
        
        log.info("申请退款成功，退款ID：{}，订单号：{}", refundId, orderNo);
        return response;
    }

    /**
     * 查询退款状态
     * 
     * @param refundId 退款ID
     * @return 退款状态信息
     */
    @Override
    public RefundStatusResponse getRefundStatus(String refundId) {
        log.info("查询退款状态，退款ID：{}", refundId);
        
        if (!StringUtils.hasText(refundId)) {
            throw new BusinessException("退款ID不能为空");
        }
        
        // 模拟退款状态查询
        RefundStatusResponse response = new RefundStatusResponse();
        response.setRefundId(refundId);
        response.setOrderNo("ORD" + refundId.substring(3)); // 从退款ID推导订单号
        response.setRefundAmount(new BigDecimal("199.90")); // 模拟退款金额
        response.setRefundStatus("success"); // 模拟退款成功
        response.setRefundReason("用户主动退款");
        response.setRefundTime(LocalDateTime.now().minusHours(1)); // 模拟1小时前退款成功
        response.setCreateTime(LocalDateTime.now().minusDays(1)); // 模拟1天前申请退款
        response.setTransactionId("REF_TXN_" + System.currentTimeMillis());
        response.setRefundChannel("ORIGINAL");
        response.setRefundAccount("原支付账户");
        
        log.info("查询退款状态成功，退款ID：{}，状态：{}", refundId, response.getRefundStatus());
        return response;
    }

    /**
     * 验证支付签名
     * 
     * @param paymentMethod 支付方式
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 原始数据
     * @param serialNo 证书序列号（微信支付需要）
     * @return 验证结果
     */
    @Override
    public SignatureVerifyResponse verifySignature(String paymentMethod, String signature, String timestamp, 
                                                 String nonce, String body, String serialNo) {
        log.info("验证支付签名，支付方式：{}，时间戳：{}", paymentMethod, timestamp);
        
        SignatureVerifyResponse response = new SignatureVerifyResponse();
        response.setPaymentMethod(paymentMethod);
        response.setVerifyTime(LocalDateTime.now());
        
        try {
            boolean valid = false;
            String message = "";
            
            if ("wechat".equals(paymentMethod)) {
                valid = verifyWechatSignature(body, signature, timestamp, nonce, serialNo);
                message = valid ? "微信支付签名验证通过" : "微信支付签名验证失败";
            } else if ("alipay".equals(paymentMethod)) {
                valid = verifyAlipaySignatureForBody(body, signature);
                message = valid ? "支付宝签名验证通过" : "支付宝签名验证失败";
            } else {
                message = "不支持的支付方式";
            }
            
            response.setValid(valid);
            response.setMessage(message);
            
            log.info("验证支付签名完成，支付方式：{}，结果：{}", paymentMethod, valid);
            return response;
        } catch (Exception e) {
            log.error("验证支付签名失败", e);
            response.setValid(false);
            response.setMessage("签名验证异常：" + e.getMessage());
            return response;
        }
    }

    // ========== 私有方法 ==========

    /**
     * 验证创建支付订单请求参数
     */
    private void validatePaymentCreateRequest(PaymentCreateRequest request) {
        if (!StringUtils.hasText(request.getOrderNo())) {
            throw new BusinessException("订单号不能为空");
        }
        if (!StringUtils.hasText(request.getPaymentMethod())) {
            throw new BusinessException("支付方式不能为空");
        }
        if (!StringUtils.hasText(request.getPaymentType())) {
            throw new BusinessException("支付类型不能为空");
        }
        if (!StringUtils.hasText(request.getClientIp())) {
            throw new BusinessException("客户端IP不能为空");
        }
        if (!StringUtils.hasText(request.getNotifyUrl())) {
            throw new BusinessException("回调通知地址不能为空");
        }
    }

    /**
     * 检查是否支持的支付方式
     */
    private boolean isSupportedPaymentMethod(String paymentMethod) {
        return "wechat".equals(paymentMethod) || "alipay".equals(paymentMethod);
    }

    /**
     * 生成支付ID
     */
    private String generatePaymentId() {
        return "PAY" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }

    /**
     * 生成退款ID
     */
    private String generateRefundId() {
        return "REF" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }

    /**
     * 构建支付信息
     */
    private PaymentCreateResponse.PaymentInfo buildPaymentInfo(Orders order, PaymentCreateRequest request) {
        PaymentCreateResponse.PaymentInfo paymentInfo = new PaymentCreateResponse.PaymentInfo();
        
        // 根据支付方式生成不同的支付信息
        if ("wechat".equals(request.getPaymentMethod())) {
            paymentInfo.setQrcodeUrl("weixin://wxpay/bizpayurl?pr=" + order.getOrderNo());
            paymentInfo.setQrcodeImage("data:image/png;base64,iVBORw0KGgoAAAANSUhE..."); // 模拟二维码图片
        } else if ("alipay".equals(request.getPaymentMethod())) {
            paymentInfo.setQrcodeUrl("alipays://platformapi/startapp?saId=10000007&qrcode=" + order.getOrderNo());
            paymentInfo.setQrcodeImage("data:image/png;base64,iVBORw0KGgoAAAANSUhE..."); // 模拟二维码图片
        }
        
        paymentInfo.setExpireTime(LocalDateTime.now().plusMinutes(30));
        paymentInfo.setAmount(order.getFinalAmount());
        paymentInfo.setCurrency("CNY");
        
        return paymentInfo;
    }

    /**
     * 转换订单状态为支付状态
     */
    private String convertToPaymentStatus(Byte orderStatus) {
        switch (orderStatus) {
            case 1: return "unpaid";    // 待支付
            case 2: return "paid";      // 已支付
            case 3: return "paid";      // 已完成
            case 4: return "closed";    // 已取消
            case 5: return "refunded";  // 已退款
            default: return "unknown";
        }
    }

    /**
     * 转换订单状态为订单状态字符串
     */
    private String convertToOrderStatus(Byte orderStatus) {
        switch (orderStatus) {
            case 1: return "pending";     // 待支付
            case 2: return "paid";        // 已支付
            case 3: return "completed";   // 已完成
            case 4: return "cancelled";   // 已取消
            case 5: return "refunded";    // 已退款
            default: return "unknown";
        }
    }

    /**
     * 转换支付方式数字为字符串
     */
    private String convertPaymentMethodToString(Byte paymentMethod) {
        switch (paymentMethod) {
            case 1: return "wechat";
            case 2: return "alipay";
            default: return "unknown";
        }
    }

    /**
     * 验证微信支付签名（模拟实现）
     */
    private boolean verifyWechatSignature(String body, String signature, String timestamp, String nonce, String serialNo) {
        // 这里应该实现真正的微信支付签名验证逻辑
        // 为了演示，这里简化为检查参数是否齐全
        return StringUtils.hasText(body) && StringUtils.hasText(signature) && 
               StringUtils.hasText(timestamp) && StringUtils.hasText(nonce);
    }

    /**
     * 验证支付宝签名（模拟实现）
     */
    private boolean verifyAlipaySignature(Map<String, String> params) {
        // 这里应该实现真正的支付宝签名验证逻辑
        // 为了演示，这里简化为检查必要参数是否存在
        return params.containsKey("sign") && params.containsKey("out_trade_no") && 
               params.containsKey("trade_status");
    }

    /**
     * 验证支付宝签名（针对原始数据）
     */
    private boolean verifyAlipaySignatureForBody(String body, String signature) {
        // 这里应该实现真正的支付宝签名验证逻辑
        return StringUtils.hasText(body) && StringUtils.hasText(signature);
    }

    /**
     * 从微信回调数据中解析订单号（模拟实现）
     */
    private String parseOrderNoFromWechatCallback(String callbackData) {
        // 这里应该解析真正的微信回调数据
        // 为了演示，返回模拟订单号
        return "ORD" + System.currentTimeMillis();
    }

    /**
     * 从微信回调数据中解析交易号（模拟实现）
     */
    private String parseTradeNoFromWechatCallback(String callbackData) {
        // 这里应该解析真正的微信回调数据
        // 为了演示，返回模拟交易号
        return "WX_TXN_" + System.currentTimeMillis();
    }
} 