package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 头像上传响应DTO
 * 
 * 主要功能：
 * 1. 返回头像上传成功后的结果信息
 * 2. 包含新头像的URL和相关信息
 * 3. 提供文件大小和名称等详细信息
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "头像上传响应", description = "头像上传成功后的响应信息")
public class AvatarUploadResponse {

    /**
     * 头像URL
     * 上传成功后的头像访问地址
     */
    @Schema(description = "头像URL")
    private String avatarUrl;

    /**
     * 文件名
     * 服务器端存储的文件名
     */
    @Schema(description = "文件名")
    private String fileName;

    /**
     * 文件大小
     * 文件大小，单位：字节
     */
    @Schema(description = "文件大小")
    private Long fileSize;

    /**
     * 创建头像上传响应对象
     * 
     * @param avatarUrl 头像URL
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @return 头像上传响应对象
     */
    public static AvatarUploadResponse create(String avatarUrl, String fileName, Long fileSize) {
        return AvatarUploadResponse.builder()
                .avatarUrl(avatarUrl)
                .fileName(fileName)
                .fileSize(fileSize)
                .build();
    }

    /**
     * 格式化文件大小为可读格式
     * 
     * @return 格式化后的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "未知";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }
} 