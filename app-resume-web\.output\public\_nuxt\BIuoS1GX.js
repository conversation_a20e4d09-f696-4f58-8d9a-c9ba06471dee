import{D as h}from"./iG63TLyk.js";import{_ as w}from"./DlAUqK2U.js";import{r as p,i as _,c as v,a,f as y,b as x,F as D,g as b,o as u,s as S,t as g}from"./CURHyiUL.js";import"./D7AOVZt6.js";const U={class:"download-demo-page"},$={class:"demo-container"},C={class:"mock-editor-header"},L={class:"mock-actions"},N={class:"demo-logs"},T={class:"log-container"},j={class:"log-time"},B={class:"log-message"},E={__name:"download-demo",setup(F){const m=p(!1),d=p("准备就绪"),l=p([]),o=(n,e="info")=>{const t=new Date().toLocaleTimeString();l.value.unshift({time:t,message:n,type:e}),l.value.length>20&&(l.value=l.value.slice(0,20))},f=(n,e)=>{let s="",t="text/plain";switch(n){case"pdf":s=`%PDF-1.4
这是一个模拟的PDF文件内容`,t="application/pdf";break;case"word":s="这是一个模拟的Word文档内容",t="application/vnd.openxmlformats-officedocument.wordprocessingml.document";break;case"image":s="这是一个模拟的图片文件内容",t="image/png";break}const r=new Blob([s],{type:t}),c=URL.createObjectURL(r),i=document.createElement("a");i.href=c,i.download=e,i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(c)},k=async n=>{const{format:e}=n;o(`用户选择下载格式: ${e.toUpperCase()}`,"info");try{m.value=!0,d.value=`正在生成${e.toUpperCase()}文档...`,o(`开始生成${e.toUpperCase()}文档`,"info");const s=1500+Math.random()*2e3;if(await new Promise(r=>setTimeout(r,s)),Math.random()>.1){const c=`简历_张三_${new Date().toISOString().slice(0,10)}.${e==="image"?"png":e==="word"?"docx":"pdf"}`;f(e,c),d.value="下载完成",o(`${e.toUpperCase()}文档生成成功`,"success"),o(`文件已下载: ${c}`,"success")}else throw new Error("网络连接不稳定，请重试")}catch(s){d.value="下载失败",o(`下载失败: ${s.message}`,"error")}finally{setTimeout(()=>{m.value=!1,d.value="准备就绪"},2e3)}};return _(()=>{o("页面加载完成，可以开始测试下载功能","info")}),(n,e)=>(u(),v("div",U,[e[3]||(e[3]=a("div",{class:"demo-header"},[a("h1",{class:"demo-title"},"简历下载功能演示"),a("p",{class:"demo-description"}," 这是一个纯前端演示页面，展示下载下拉菜单的UI效果和交互体验 ")],-1)),a("div",$,[a("div",C,[e[0]||(e[0]=a("div",{class:"mock-editor-title"},[a("h2",null,"我的简历"),a("span",{class:"mock-save-status"},"已保存")],-1)),a("div",L,[x(h,{"is-exporting":m.value,"export-status":d.value,onDownload:k},null,8,["is-exporting","export-status"])])]),e[1]||(e[1]=y('<div class="mock-resume-preview" data-v-9e40679a><div class="mock-resume-content" data-v-9e40679a><div class="mock-resume-header" data-v-9e40679a><h3 data-v-9e40679a>张三</h3><p data-v-9e40679a>前端开发工程师</p></div><div class="mock-resume-section" data-v-9e40679a><h4 data-v-9e40679a>工作经历</h4><div class="mock-work-item" data-v-9e40679a><strong data-v-9e40679a>火花简历 - 高级前端工程师</strong><span data-v-9e40679a>2022-至今</span><p data-v-9e40679a>负责简历编辑器的开发和维护，实现了多种简历模板和导出功能</p></div></div><div class="mock-resume-section" data-v-9e40679a><h4 data-v-9e40679a>技能特长</h4><div class="mock-skills" data-v-9e40679a><span class="skill-tag" data-v-9e40679a>Vue.js</span><span class="skill-tag" data-v-9e40679a>JavaScript</span><span class="skill-tag" data-v-9e40679a>CSS3</span><span class="skill-tag" data-v-9e40679a>Node.js</span></div></div></div></div>',1))]),a("div",N,[e[2]||(e[2]=a("h3",null,"操作日志",-1)),a("div",T,[(u(!0),v(D,null,b(l.value,(s,t)=>(u(),v("div",{key:t,class:S(["log-item",`log-${s.type}`])},[a("span",j,g(s.time),1),a("span",B,g(s.message),1)],2))),128))])])]))}},O=w(E,[["__scopeId","data-v-9e40679a"]]);export{O as default};
