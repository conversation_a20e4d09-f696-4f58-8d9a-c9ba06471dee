package com.alan6.resume.service;

import com.alan6.resume.dto.system.LanguageConfigResponse;
import com.alan6.resume.dto.system.LanguageListResponse;

/**
 * 多语言支持服务接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ILanguageService {

    /**
     * 获取支持的语言列表
     *
     * @return 语言列表响应
     */
    LanguageListResponse getSupportedLanguages();

    /**
     * 获取指定语言的配置
     *
     * @param languageCode 语言代码
     * @return 语言配置响应
     */
    LanguageConfigResponse getLanguageConfig(String languageCode);

    /**
     * 更新语言配置
     *
     * @param languageCode 语言代码
     * @param config       配置内容
     */
    void updateLanguageConfig(String languageCode, String config);

    /**
     * 启用/禁用语言
     *
     * @param languageCode 语言代码
     * @param enabled      是否启用
     */
    void toggleLanguage(String languageCode, boolean enabled);
} 