<template>
  <header class="admin-header">
    <div class="header-left">
      <!-- 移动端菜单按钮 -->
      <button class="mobile-menu-btn md:hidden" @click="toggleMobileMenu">
        <Bars3Icon class="w-6 h-6" />
      </button>
      
      <!-- 面包屑导航 -->
      <nav class="breadcrumb">
        <ol class="breadcrumb-list">
          <li class="breadcrumb-item">
            <NuxtLink to="/admin" class="breadcrumb-link">
              <HomeIcon class="w-4 h-4" />
              <span class="hidden sm:inline">管理后台</span>
            </NuxtLink>
          </li>
          <li v-if="currentPageTitle" class="breadcrumb-item">
            <ChevronRightIcon class="w-4 h-4 text-gray-400" />
            <span class="breadcrumb-current">{{ currentPageTitle }}</span>
          </li>
        </ol>
      </nav>
    </div>

    <div class="header-right">
      <!-- 通知消息 -->
      <div class="header-action">
        <button class="action-btn" title="通知消息">
          <BellIcon class="w-5 h-5" />
          <span v-if="unreadCount > 0" class="notification-badge">{{ unreadCount }}</span>
        </button>
      </div>

      <!-- 用户信息下拉菜单 -->
      <div class="user-dropdown" ref="dropdownRef">
        <button 
          class="user-trigger" 
          @click="toggleUserDropdown"
          :class="{ 'active': showUserDropdown }"
        >
          <div class="user-avatar">
            <img 
              v-if="adminInfo?.avatarUrl" 
              :src="adminInfo.avatarUrl" 
              :alt="adminInfo.nickname || adminInfo.username"
              class="avatar-img"
            />
            <div v-else class="avatar-placeholder">
              <UserIcon class="w-5 h-5" />
            </div>
          </div>
          <div class="user-info">
            <span class="user-name">{{ adminInfo?.nickname || adminInfo?.username || '管理员' }}</span>
            <span class="user-role">{{ getUserRoleText() }}</span>
          </div>
          <ChevronDownIcon 
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': showUserDropdown }"
          />
        </button>

        <!-- 下拉菜单 -->
        <transition name="dropdown">
          <div v-show="showUserDropdown" class="dropdown-menu">
            <div class="dropdown-header">
              <div class="dropdown-user-info">
                <p class="dropdown-name">{{ adminInfo?.nickname || adminInfo?.username }}</p>
                <p class="dropdown-email">{{ adminInfo?.email || '未设置邮箱' }}</p>
              </div>
            </div>
            
            <div class="dropdown-divider"></div>
            
            <div class="dropdown-body">
              <NuxtLink to="/profile" class="dropdown-item">
                <UserCircleIcon class="w-4 h-4" />
                <span>个人资料</span>
              </NuxtLink>
              
              <button @click="handleLogout" class="dropdown-item">
                <ArrowRightOnRectangleIcon class="w-4 h-4" />
                <span>退出登录</span>
              </button>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onClickOutside } from '@vueuse/core'
import {
  Bars3Icon,
  HomeIcon,
  ChevronRightIcon,
  BellIcon,
  UserIcon,
  ChevronDownIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'
import { useAuthService } from '~/composables/auth/useAuthService'
import { useAdminLoginService } from '~/composables/admin/useAdminLoginService'
import { useAdminStore } from '~/composables/admin/useAdminStore'

const route = useRoute()
const authService = useAuthService()
const { adminInfo, toggleSidebar } = useAdminStore()

// 响应式数据
const showUserDropdown = ref(false)
const dropdownRef = ref(null)
const unreadCount = ref(3) // 模拟未读消息数量

// 计算当前页面标题
const currentPageTitle = computed(() => {
  const pathMap = {
    '/admin': '',
    '/admin/users': '用户管理',
    '/admin/resumes': '简历管理', 
    '/admin/templates': '模板管理',
    '/admin/orders': '订单管理',
    '/admin/memberships': '会员管理',
    '/admin/settings': '系统设置'
  }
  return pathMap[route.path] || ''
})

/**
 * 获取用户角色文本
 */
const getUserRoleText = () => {
  if (!adminInfo.value?.roles) return '管理员'
  
  if (adminInfo.value.roles.includes('SUPER_ADMIN')) {
    return '超级管理员'
  } else if (adminInfo.value.roles.includes('ADMIN')) {
    return '管理员'
  }
  return '管理员'
}

/**
 * 切换用户下拉菜单
 */
const toggleUserDropdown = () => {
  showUserDropdown.value = !showUserDropdown.value
}

/**
 * 切换移动端菜单
 */
const toggleMobileMenu = () => {
  toggleSidebar()
}

/**
 * 处理退出登录
 */
const handleLogout = async () => {
  try {
    // 使用管理员登录服务清除认证信息
    const adminLoginService = useAdminLoginService()
    adminLoginService.clearAdminAuth()
    
    // 清除管理员状态
    const { clearAdminInfo } = useAdminStore()
    clearAdminInfo()
    
    // 跳转到登录页面
    await navigateTo('/admin/login')
  } catch (error) {
    console.error('管理员退出登录失败:', error)
    // 即使出错也要跳转到登录页面
    await navigateTo('/admin/login')
  }
}

// 点击外部关闭下拉菜单
onClickOutside(dropdownRef, () => {
  showUserDropdown.value = false
})
</script>

<style scoped>
.admin-header {
  height: 64px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.mobile-menu-btn {
  padding: 8px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mobile-menu-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  gap: 8px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-link {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: #3b82f6;
}

.breadcrumb-current {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-action {
  position: relative;
}

.action-btn {
  position: relative;
  padding: 8px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.notification-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #ef4444;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

.user-dropdown {
  position: relative;
}

.user-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.user-trigger:hover,
.user-trigger.active {
  background: #f3f4f6;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  line-height: 1;
}

.user-role {
  font-size: 12px;
  color: #6b7280;
  line-height: 1;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  width: 240px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1000;
}

.dropdown-header {
  padding: 16px;
  background: #f9fafb;
}

.dropdown-user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dropdown-name {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.dropdown-email {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.dropdown-divider {
  height: 1px;
  background: #e5e7eb;
}

.dropdown-body {
  padding: 8px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 16px;
  border: none;
  background: none;
  color: #374151;
  text-decoration: none;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background: #f3f4f6;
  color: #3b82f6;
}

/* 下拉菜单动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .admin-header {
    padding: 0 16px;
  }
  
  .user-info {
    display: none;
  }
  
  .dropdown-menu {
    width: 200px;
  }
}
</style> 