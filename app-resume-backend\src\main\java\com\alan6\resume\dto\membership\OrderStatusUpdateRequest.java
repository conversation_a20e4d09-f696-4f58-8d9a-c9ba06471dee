package com.alan6.resume.dto.membership;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 订单状态更新请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "OrderStatusUpdateRequest", description = "订单状态更新请求")
public class OrderStatusUpdateRequest {

    /**
     * 新状态（1:待支付,2:已支付,3:已完成,4:已取消,5:已退款）
     */
    @NotNull(message = "订单状态不能为空")
    @Min(value = 1, message = "订单状态值无效")
    @Max(value = 5, message = "订单状态值无效")
    @Schema(description = "订单状态", required = true, example = "2", allowableValues = {"1", "2", "3", "4", "5"})
    private Byte status;

    /**
     * 第三方交易号（支付成功时必填）
     */
    @Schema(description = "第三方交易号", example = "TXN20241201123456")
    private String tradeNo;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "支付成功")
    private String remark;
} 