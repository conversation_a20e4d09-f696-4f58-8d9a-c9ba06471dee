<template>
  <div class="margin-control">
    <!-- 边距按钮 -->
    <button 
      class="control-btn"
      @click="togglePanel"
      :class="{ 'active': isPanelOpen }"
      title="页面边距设置"
    >
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="9" y1="9" x2="15" y2="9"></line>
        <line x1="9" y1="15" x2="15" y2="15"></line>
      </svg>
      <span class="btn-label">页面边距</span>
      <svg class="dropdown-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="6,9 12,15 18,9"></polyline>
      </svg>
    </button>
    
    <!-- 边距设置面板 -->
    <Transition name="margin-panel">
      <div v-show="isPanelOpen" class="margin-panel">
        <div class="panel-header">
          <h4 class="panel-title">页面边距</h4>
        </div>
        
        <div class="margin-sliders">
          <!-- 上边距 -->
          <div class="slider-group">
            <label class="slider-label">页面-上边距</label>
            <div class="slider-container">
              <input
                v-model="localMargins.top"
                type="range"
                :min="marginConfig.top.min"
                :max="marginConfig.top.max"
                :step="marginConfig.top.step"
                class="slider"
                @input="handleMarginChange"
              />
              <div class="slider-value">{{ localMargins.top }}px</div>
            </div>
          </div>
          
          <!-- 左右边距 -->
          <div class="slider-group">
            <label class="slider-label">页面-左右边距</label>
            <div class="slider-container">
              <input
                v-model="localMargins.side"
                type="range"
                :min="marginConfig.side.min"
                :max="marginConfig.side.max"
                :step="marginConfig.side.step"
                class="slider"
                @input="handleMarginChange"
              />
              <div class="slider-value">{{ localMargins.side }}px</div>
            </div>
          </div>
        </div>
        
        <!-- 重置按钮 -->
        <div class="panel-footer">
          <button class="reset-btn" @click="resetMargins">
            重置默认
          </button>
        </div>
      </div>
    </Transition>
    
    <!-- 点击遮罩层关闭 -->
    <div 
      v-if="isPanelOpen"
      class="margin-overlay"
      @click="closePanel"
    ></div>
  </div>
</template>

<script setup>
/**
 * 边距控制组件
 * @description 提供页面边距调整功能，支持弹出式滑块面板
 * <AUTHOR>
 * @since 2.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 边距设置对象
   */
  modelValue: {
    type: Object,
    default: () => ({
      top: 60,     // 上边距
      side: 60     // 左右边距
    })
  }
})

// ================================
// 组件事件
// ================================
const emit = defineEmits(['update:modelValue', 'change'])

// ================================
// 响应式数据
// ================================

/**
 * 面板开启状态
 */
const isPanelOpen = ref(false)

/**
 * 本地边距设置
 */
const localMargins = reactive({
  top: 60,
  side: 60
})

/**
 * 边距配置
 */
const marginConfig = {
  top: {
    min: 20,
    max: 120,
    step: 5,
    default: 60
  },
  side: {
    min: 20,
    max: 120,
    step: 5,
    default: 60
  }
}

// ================================
// 生命周期钩子
// ================================
onMounted(() => {
  // 初始化本地设置
  Object.assign(localMargins, props.modelValue)
  
  // 监听外部变化
  watch(() => props.modelValue, (newValue) => {
    Object.assign(localMargins, newValue)
  }, { deep: true })
  
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// ================================
// 事件处理方法
// ================================

/**
 * 切换面板
 */
const togglePanel = () => {
  isPanelOpen.value = !isPanelOpen.value
}

/**
 * 关闭面板
 */
const closePanel = () => {
  isPanelOpen.value = false
}

/**
 * 处理边距变化
 */
const handleMarginChange = () => {
  const newValue = { ...localMargins }
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

/**
 * 重置边距
 */
const resetMargins = () => {
  localMargins.top = marginConfig.top.default
  localMargins.side = marginConfig.side.default
  handleMarginChange()
}

/**
 * 处理点击外部区域
 * @param {Event} event - 点击事件
 */
const handleClickOutside = (event) => {
  const marginControl = event.target.closest('.margin-control')
  if (!marginControl && isPanelOpen.value) {
    closePanel()
  }
}
</script>

<style scoped>
/* ================================
 * 边距控制容器
 * ================================ */
.margin-control {
  @apply relative;
}

/* ================================
 * 控制按钮
 * ================================ */
.control-btn {
  @apply flex items-center gap-2;
  @apply px-3 py-1.5;
  @apply text-sm font-medium text-gray-700;
  @apply bg-white border border-gray-200 rounded-lg;
  @apply hover:bg-gray-50 hover:border-gray-300;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.control-btn.active {
  @apply bg-primary-50 border-primary-200 text-primary-700;
}

.btn-label {
  @apply text-sm;
}

.dropdown-icon {
  @apply text-gray-400 transition-transform duration-200;
}

.control-btn.active .dropdown-icon {
  @apply rotate-180 text-primary-500;
}

/* ================================
 * 边距设置面板
 * ================================ */
.margin-panel {
  @apply absolute top-full right-0 mt-2 z-50;
  @apply w-72 p-4;
  @apply bg-white rounded-lg shadow-2xl;
  @apply border border-gray-200;
  @apply transform transition-all duration-200;
}

.panel-header {
  @apply mb-3 pb-2 border-b border-gray-100;
}

.panel-title {
  @apply text-sm font-semibold text-gray-900;
}

/* ================================
 * 滑块组
 * ================================ */
.margin-sliders {
  @apply space-y-4;
}

.slider-group {
  @apply space-y-2;
}

.slider-label {
  @apply block text-xs font-medium text-gray-600;
}

.slider-container {
  @apply flex items-center gap-3;
}

/* ================================
 * 滑块样式
 * ================================ */
.slider {
  @apply flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-primary-500 rounded-full cursor-pointer;
  @apply hover:bg-primary-600 transition-colors duration-200;
  @apply shadow-sm;
}

.slider::-moz-range-thumb {
  @apply w-4 h-4 bg-primary-500 rounded-full cursor-pointer border-0;
  @apply hover:bg-primary-600 transition-colors duration-200;
  @apply shadow-sm;
}

.slider-value {
  @apply text-xs font-medium text-gray-600 min-w-12 text-center;
  @apply bg-gray-50 px-2 py-1 rounded;
}

/* ================================
 * 面板底部
 * ================================ */
.panel-footer {
  @apply mt-4 pt-3 border-t border-gray-100;
  @apply flex justify-center;
}

.reset-btn {
  @apply px-3 py-1.5 text-xs;
  @apply text-gray-600 hover:text-gray-900;
  @apply bg-gray-50 hover:bg-gray-100;
  @apply border border-gray-200 rounded;
  @apply transition-colors duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

/* ================================
 * 遮罩层
 * ================================ */
.margin-overlay {
  @apply fixed inset-0 z-10;
  @apply bg-transparent;
}

/* ================================
 * 面板动画
 * ================================ */
.margin-panel-enter-active,
.margin-panel-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.margin-panel-enter-from {
  @apply opacity-0 -translate-y-2 scale-95;
}

.margin-panel-leave-to {
  @apply opacity-0 translate-y-2 scale-95;
}

.margin-panel-enter-to,
.margin-panel-leave-from {
  @apply opacity-100 translate-y-0 scale-100;
}

/* ================================
 * 响应式设计
 * ================================ */
@media (max-width: 768px) {
  .margin-panel {
    @apply left-auto right-0 min-w-56;
  }
  
  .control-btn {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-label {
    @apply hidden;
  }
}
</style> 