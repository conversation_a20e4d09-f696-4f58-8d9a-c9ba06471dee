package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.dto.system.*;
import com.alan6.resume.service.ISystemConfigsService;
import com.alan6.resume.service.ISystemMonitorService;
import com.alan6.resume.service.ISupportedLanguagesService;
import com.alan6.resume.service.ILanguageService;
import com.alan6.resume.service.ISystemFeedbackService;
import com.alan6.resume.service.IHelpDocumentService;
import com.alan6.resume.service.ISystemMaintenanceService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/system")
@RequiredArgsConstructor
@Tag(name = "系统管理", description = "系统管理相关接口")
public class SystemController {

    private final ISystemConfigsService systemConfigsService;
    private final ISystemMonitorService systemMonitorService;
    private final ISupportedLanguagesService supportedLanguagesService;
    private final ILanguageService languageService;
    private final ISystemFeedbackService systemFeedbackService;
    private final IHelpDocumentService helpDocumentService;
    private final ISystemMaintenanceService systemMaintenanceService;

    // ==================== 系统配置管理 ====================

    /**
     * 获取系统配置列表
     */
    @GetMapping("/configs")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取系统配置列表", description = "获取系统配置列表（管理员权限）")
    public R<Page<SystemConfigResponse>> getConfigList(
            @Parameter(description = "配置分组") @RequestParam(required = false) String group,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        
        log.info("获取系统配置列表, group: {}, page: {}, size: {}", group, page, size);
        Page<SystemConfigResponse> result = systemConfigsService.getConfigList(group, page, size);
        return R.ok(result);
    }

    /**
     * 获取单个配置项
     */
    @GetMapping("/configs/{key}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取单个配置项", description = "根据配置键获取配置项（管理员权限）")
    public R<SystemConfigResponse> getConfigByKey(
            @Parameter(description = "配置键") @PathVariable String key) {
        
        log.info("获取单个配置项, key: {}", key);
        SystemConfigResponse result = systemConfigsService.getConfigByKey(key);
        return R.ok(result);
    }

    /**
     * 更新配置项
     */
    @PutMapping("/configs/{key}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "更新配置项", description = "更新指定配置项（管理员权限）")
    public R<Void> updateConfig(
            @Parameter(description = "配置键") @PathVariable String key,
            @Validated @RequestBody SystemConfigRequest request) {
        
        log.info("更新配置项, key: {}, request: {}", key, request);
        systemConfigsService.updateConfig(key, request);
        return R.ok();
    }

    /**
     * 创建配置项
     */
    @PostMapping("/configs")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "创建配置项", description = "创建新的配置项（管理员权限）")
    public R<SystemConfigResponse> createConfig(@Validated @RequestBody SystemConfigRequest request) {
        log.info("创建配置项, request: {}", request);
        SystemConfigResponse result = systemConfigsService.createConfig(request);
        return R.ok(result);
    }

    /**
     * 获取配置分组列表
     */
    @GetMapping("/configs/groups")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取配置分组列表", description = "获取所有配置分组（管理员权限）")
    public R<List<String>> getConfigGroups() {
        log.info("获取配置分组列表");
        List<String> result = systemConfigsService.getConfigGroups();
        return R.ok(result);
    }

    /**
     * 获取前端配置
     */
    @GetMapping("/config")
    @Operation(summary = "获取前端配置", description = "获取前端公开配置（公开接口）")
    public R<Map<String, Object>> getFrontendConfig() {
        log.info("获取前端配置");
        Map<String, Object> result = systemConfigsService.getFrontendConfig();
        return R.ok(result);
    }

    // ==================== 系统监控 ====================

    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "系统健康检查", description = "检查系统各组件健康状态（公开接口）")
    public R<SystemHealthResponse> healthCheck() {
        log.info("执行系统健康检查");
        SystemHealthResponse result = systemMonitorService.healthCheck();
        return R.ok(result);
    }

    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取系统状态", description = "获取详细的系统状态信息（管理员权限）")
    public R<SystemStatusResponse> getSystemStatus() {
        log.info("获取系统状态");
        SystemStatusResponse result = systemMonitorService.getSystemStatus();
        return R.ok(result);
    }

    /**
     * 获取系统指标
     */
    @GetMapping("/metrics")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取系统指标", description = "获取系统性能和业务指标（管理员权限）")
    public R<SystemMetricsResponse> getSystemMetrics() {
        log.info("获取系统指标");
        SystemMetricsResponse result = systemMonitorService.getSystemMetrics();
        return R.ok(result);
    }

    // ==================== 系统维护 ====================

    /**
     * 清理缓存
     */
    @PostMapping("/cache/clear")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "清理缓存", description = "清理系统缓存（管理员权限）")
    public R<CacheClearResponse> clearCache(@RequestBody(required = false) CacheClearRequest request) {
        log.info("清理缓存, request: {}", request);
        CacheClearResponse result = systemMaintenanceService.clearCache(request);
        return R.ok(result);
    }

    /**
     * 系统维护模式
     */
    @PostMapping("/maintenance")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "系统维护模式", description = "启用或禁用系统维护模式（管理员权限）")
    public R<MaintenanceResponse> setMaintenanceMode(@Validated @RequestBody MaintenanceRequest request) {
        log.info("设置系统维护模式, request: {}", request);
        MaintenanceResponse result = systemMaintenanceService.setMaintenanceMode(request);
        return R.ok(result);
    }

    /**
     * 获取维护状态
     */
    @GetMapping("/maintenance")
    @Operation(summary = "获取维护状态", description = "获取当前系统维护状态（公开接口）")
    public R<MaintenanceResponse> getMaintenanceStatus() {
        log.info("获取系统维护状态");
        MaintenanceResponse result = systemMaintenanceService.getMaintenanceStatus();
        return R.ok(result);
    }

    // ==================== 多语言支持 ====================

    /**
     * 获取支持的语言
     */
    @GetMapping("/languages")
    @Operation(summary = "获取支持的语言", description = "获取系统支持的语言列表（公开接口）")
    public R<LanguageListResponse> getSupportedLanguages() {
        log.info("获取支持的语言列表");
        LanguageListResponse result = languageService.getSupportedLanguages();
        return R.ok(result);
    }

    /**
     * 获取语言配置
     */
    @GetMapping("/languages/{code}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取语言配置", description = "获取指定语言的配置（管理员权限）")
    public R<LanguageConfigResponse> getLanguageConfig(
            @Parameter(description = "语言代码") @PathVariable String code) {
        
        log.info("获取语言配置, code: {}", code);
        LanguageConfigResponse result = languageService.getLanguageConfig(code);
        return R.ok(result);
    }

    /**
     * 更新语言配置
     */
    @PutMapping("/languages/{code}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "更新语言配置", description = "更新指定语言的配置（管理员权限）")
    public R<Void> updateLanguageConfig(
            @Parameter(description = "语言代码") @PathVariable String code,
            @RequestBody String configContent) {
        
        log.info("更新语言配置, code: {}, configContent: {}", code, configContent);
        languageService.updateLanguageConfig(code, configContent);
        return R.ok();
    }

    /**
     * 启用/禁用语言
     */
    @PatchMapping("/languages/{code}/toggle")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "启用/禁用语言", description = "启用或禁用指定语言（管理员权限）")
    public R<Void> toggleLanguage(
            @Parameter(description = "语言代码") @PathVariable String code,
            @Parameter(description = "是否启用") @RequestParam boolean enabled) {
        
        log.info("切换语言状态, code: {}, enabled: {}", code, enabled);
        languageService.toggleLanguage(code, enabled);
        return R.ok();
    }

    // ==================== 意见反馈 ====================

    /**
     * 提交意见反馈
     */
    @PostMapping("/feedback")
    @Operation(summary = "提交意见反馈", description = "提交用户意见反馈（登录用户）")
    public R<Map<String, Object>> submitFeedback(@Validated @RequestBody FeedbackRequest request) {
        log.info("提交意见反馈, request: {}", request);
        // TODO: 从安全上下文获取用户ID，这里暂时传null表示匿名反馈
        Map<String, Object> result = systemFeedbackService.submitFeedback(request, null);
        return R.ok(result);
    }

    /**
     * 获取反馈列表（管理员）
     */
    @GetMapping("/feedback")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取反馈列表", description = "获取用户反馈列表（管理员权限）")
    public R<Page<FeedbackResponse>> getFeedbackList(
            @Parameter(description = "反馈类型") @RequestParam(required = false) String type,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        
        log.info("获取反馈列表, type: {}, status: {}, page: {}, size: {}", type, status, page, size);
        Page<FeedbackResponse> result = systemFeedbackService.getFeedbackList(type, status, page, size);
        return R.ok(result);
    }

    /**
     * 获取反馈详情
     */
    @GetMapping("/feedback/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取反馈详情", description = "获取指定反馈的详细信息（管理员权限）")
    public R<FeedbackResponse> getFeedbackDetail(@Parameter(description = "反馈ID") @PathVariable Long id) {
        log.info("获取反馈详情, id: {}", id);
        FeedbackResponse result = systemFeedbackService.getFeedbackDetail(id);
        return R.ok(result);
    }

    /**
     * 处理反馈
     */
    @PutMapping("/feedback/{id}/process")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "处理反馈", description = "处理用户反馈（管理员权限）")
    public R<Boolean> processFeedback(
            @Parameter(description = "反馈ID") @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        log.info("处理反馈, id: {}, request: {}", id, request);
        String status = (String) request.get("status");
        String response = (String) request.get("response");
        // TODO: 从安全上下文获取管理员ID，这里暂时传null
        boolean result = systemFeedbackService.handleFeedback(id, null, response, status);
        return R.ok(result);
    }

    // ==================== 帮助文档 ====================

    /**
     * 获取帮助文档
     */
    @GetMapping("/help")
    @Operation(summary = "获取帮助文档", description = "获取帮助文档列表（公开接口）")
    public R<HelpDocumentResponse> getHelpDocuments(
            @Parameter(description = "分类") @RequestParam(required = false) String category,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        log.info("获取帮助文档, category: {}, keyword: {}", category, keyword);
        HelpDocumentResponse result = helpDocumentService.getHelpDocuments(category, keyword);
        return R.ok(result);
    }

    /**
     * 获取帮助文章详情
     */
    @GetMapping("/help/{id}")
    @Operation(summary = "获取帮助文章详情", description = "获取指定帮助文章的详细内容（公开接口）")
    public R<HelpArticleResponse> getHelpArticle(@Parameter(description = "文章ID") @PathVariable Long id) {
        log.info("获取帮助文章详情, id: {}", id);
        HelpArticleResponse result = helpDocumentService.getHelpArticle(id);
        return R.ok(result);
    }

    /**
     * 搜索帮助文章
     */
    @GetMapping("/help/search")
    @Operation(summary = "搜索帮助文章", description = "根据关键词搜索帮助文章（公开接口）")
    public R<HelpDocumentResponse> searchHelpArticles(
            @Parameter(description = "搜索关键词") @RequestParam String keyword) {
        
        log.info("搜索帮助文章, keyword: {}", keyword);
        HelpDocumentResponse result = helpDocumentService.searchArticles(keyword);
        return R.ok(result);
    }

    /**
     * 评价文章
     */
    @PostMapping("/help/{id}/rate")
    @Operation(summary = "评价文章", description = "评价帮助文章是否有用（公开接口）")
    public R<Void> rateHelpArticle(
            @Parameter(description = "文章ID") @PathVariable Long id,
            @Parameter(description = "是否有用") @RequestParam boolean helpful) {
        
        log.info("评价文章, id: {}, helpful: {}", id, helpful);
        helpDocumentService.rateArticle(id, helpful);
        return R.ok();
    }
}
