package com.alan6.resume.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单详情查询响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "OrderDetailResponse", description = "订单详情查询响应")
public class OrderDetailResponse {

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "ORD20241222123456789")
    private String orderNo;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型", example = "membership")
    private String orderType;

    /**
     * 产品信息
     */
    @Schema(description = "产品信息")
    private ProductInfo productInfo;

    /**
     * 订单金额
     */
    @Schema(description = "订单金额", example = "199.90")
    private BigDecimal amount;

    /**
     * 货币类型
     */
    @Schema(description = "货币类型", example = "CNY")
    private String currency;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态", example = "completed")
    private String status;

    /**
     * 支付信息
     */
    @Schema(description = "支付信息")
    private PaymentInfo paymentInfo;

    /**
     * 用户信息
     */
    @Schema(description = "用户信息")
    private UserInfo userInfo;

    /**
     * 订单时间线
     */
    @Schema(description = "订单时间线")
    private List<Timeline> timeline;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-22 15:00:00")
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间", example = "2024-12-22 15:30:00")
    private LocalDateTime expireTime;

    /**
     * 产品信息内部类
     */
    @Data
    @Schema(name = "ProductInfo", description = "产品信息")
    public static class ProductInfo {
        
        /**
         * 产品ID
         */
        @Schema(description = "产品ID", example = "MP001")
        private String productId;

        /**
         * 产品名称
         */
        @Schema(description = "产品名称", example = "年度会员套餐")
        private String productName;

        /**
         * 产品描述
         */
        @Schema(description = "产品描述", example = "12个月会员权益，包含所有高级功能")
        private String productDesc;

        /**
         * 原价
         */
        @Schema(description = "原价", example = "299.90")
        private BigDecimal originalPrice;

        /**
         * 优惠价
         */
        @Schema(description = "优惠价", example = "199.90")
        private BigDecimal discountPrice;

        /**
         * 优惠原因
         */
        @Schema(description = "优惠原因", example = "新用户优惠")
        private String discountReason;
    }

    /**
     * 支付信息内部类
     */
    @Data
    @Schema(name = "PaymentInfo", description = "支付信息")
    public static class PaymentInfo {
        
        /**
         * 支付ID
         */
        @Schema(description = "支付ID", example = "PAY20241222123456789")
        private String paymentId;

        /**
         * 支付方式
         */
        @Schema(description = "支付方式", example = "wechat")
        private String paymentMethod;

        /**
         * 支付状态
         */
        @Schema(description = "支付状态", example = "paid")
        private String paymentStatus;

        /**
         * 第三方交易号
         */
        @Schema(description = "第三方交易号", example = "4200001234567890123456789")
        private String transactionId;

        /**
         * 支付时间
         */
        @Schema(description = "支付时间", example = "2024-12-22 15:10:30")
        private LocalDateTime paymentTime;
    }

    /**
     * 用户信息内部类
     */
    @Data
    @Schema(name = "UserInfo", description = "用户信息")
    public static class UserInfo {
        
        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "U001")
        private String userId;

        /**
         * 用户邮箱
         */
        @Schema(description = "用户邮箱", example = "<EMAIL>")
        private String userEmail;

        /**
         * 用户姓名
         */
        @Schema(description = "用户姓名", example = "张三")
        private String userName;
    }

    /**
     * 时间线内部类
     */
    @Data
    @Schema(name = "Timeline", description = "时间线")
    public static class Timeline {
        
        /**
         * 状态
         */
        @Schema(description = "状态", example = "created")
        private String status;

        /**
         * 时间
         */
        @Schema(description = "时间", example = "2024-12-22 15:00:00")
        private LocalDateTime time;

        /**
         * 描述
         */
        @Schema(description = "描述", example = "订单创建成功")
        private String description;
    }
} 