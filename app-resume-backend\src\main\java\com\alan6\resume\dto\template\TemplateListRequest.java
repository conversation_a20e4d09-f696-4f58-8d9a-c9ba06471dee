package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模板列表请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板列表请求DTO")
public class TemplateListRequest {

    /**
     * 当前页码（默认: 1）
     */
    @Schema(description = "当前页码", example = "1")
    private Integer current = 1;

    /**
     * 每页大小（默认: 10，最大: 50）
     */
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;

    /**
     * 分类ID（可选）
     */
    @Schema(description = "分类ID")
    private Long categoryId;

    /**
     * 适用行业（可选）
     */
    @Schema(description = "适用行业")
    private String industry;

    /**
     * 模板风格（可选）
     */
    @Schema(description = "模板风格")
    private String style;

    /**
     * 配色方案（可选）
     */
    @Schema(description = "配色方案")
    private String colorScheme;

    /**
     * 是否付费（可选，0:免费, 1:付费, 2:全部）
     */
    @Schema(description = "是否付费", example = "0")
    private Integer isPremium;

    /**
     * 是否删除（可选，0:未删除, 1:已删除, null:全部）
     */
    @Schema(description = "是否删除", example = "0")
    private Integer isDeleted;

    /**
     * 搜索关键词（可选）
     */
    @Schema(description = "搜索关键词")
    private String keyword;

    /**
     * 排序方式（可选，default:默认, popular:热门, latest:最新, rating:评分, price_asc:价格升序, price_desc:价格降序）
     */
    @Schema(description = "排序方式", example = "default")
    private String sortBy = "default";

    /**
     * 价格区间（可选，格式: "0-10" 或 "10-50"）
     */
    @Schema(description = "价格区间", example = "0-10")
    private String priceRange;

    /**
     * 最低评分（可选，1-5）
     */
    @Schema(description = "最低评分", example = "4")
    private Double ratingMin;

    /**
     * 是否热门（可选，true/false）
     */
    @Schema(description = "是否热门")
    private Boolean isHot;

    /**
     * 标签筛选（可选，逗号分隔，如: "简约,商务,创意"）
     */
    @Schema(description = "标签筛选", example = "简约,商务")
    private String tags;
} 