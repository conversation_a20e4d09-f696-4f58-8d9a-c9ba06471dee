package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;

/**
 * 分享列表查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareListRequest", description = "分享列表查询请求")
public class ShareListRequest {

    /**
     * 页码
     */
    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 资源类型过滤
     */
    @Schema(description = "资源类型（resume:简历，template:模板）", example = "resume")
    private String resourceType;

    /**
     * 分享类型过滤
     */
    @Schema(description = "分享类型（1:公开，2:密码保护，3:仅链接访问，4:指定用户）", example = "1")
    @Min(value = 1, message = "分享类型必须在1-4之间")
    @Max(value = 4, message = "分享类型必须在1-4之间")
    private Byte shareType;

    /**
     * 开始时间过滤
     */
    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    private LocalDateTime startTime;

    /**
     * 结束时间过滤
     */
    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    private LocalDateTime endTime;

    /**
     * 状态过滤
     */
    @Schema(description = "分享状态（0:失效，1:有效）", example = "1")
    private Byte isActive;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段（create_time:创建时间，view_count:查看次数，download_count:下载次数）", example = "create_time")
    private String orderBy = "create_time";

    /**
     * 排序方式
     */
    @Schema(description = "排序方式（asc:升序，desc:降序）", example = "desc")
    private String orderDirection = "desc";
} 