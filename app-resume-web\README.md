# 简历制作工具 - Web端

基于 Nuxt.js 3 构建的现代化简历制作平台前端应用，采用混合渲染架构，兼顾 SEO 优化和用户体验。

## 🚀 技术栈

- **框架**: Nuxt.js 3
- **样式**: Tailwind CSS
- **字体**: Google Fonts (Inter, Plus Jakarta Sans)
- **图标**: Heroicons
- **状态管理**: Pinia
- **SEO优化**: Nuxt SEO 模块

## 📁 项目结构

```
app-resume-web/
├── assets/               # 静态资源
│   └── css/             # 样式文件
├── components/          # Vue 组件
│   └── common/          # 公共组件
├── layouts/             # 布局组件
├── pages/               # 页面路由
├── plugins/             # 插件
├── public/              # 公共静态文件
├── server/              # 服务端代码
└── types/               # TypeScript 类型定义
```

## 🛠️ 混合渲染策略

项目采用智能路由渲染策略：

- **SEO页面** (SSR/SSG): 首页、模板页面、帮助页面 - 服务端渲染，利于搜索引擎优化
- **应用功能页面** (SPA): 用户登录、简历编辑器、用户后台 - 单页应用模式，提供流畅交互体验

## 📦 安装依赖

```bash
# 使用 npm
npm install

# 使用 yarn
yarn install

# 使用 pnpm
pnpm install
```

## 🚀 开发环境

```bash
# 启动开发服务器
npm run dev

# 访问地址
http://localhost:3000
```

## 🏗️ 构建部署

```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 生成静态站点 (可选)
npm run generate
```

## 🎨 设计系统

### 色彩系统
- **主色调**: 蓝色系 (primary)
- **辅助色**: 灰色系 (secondary)
- **功能色**: 成功(success)、警告(warning)、危险(danger)

### 字体系统
- **标题字体**: Plus Jakarta Sans
- **正文字体**: Inter

### 组件库
项目内置了完整的设计系统组件：
- 按钮组件 (btn-primary, btn-secondary, btn-outline)
- 卡片组件 (card, card-hover)
- 动画效果 (fade-in, slide-up, float 等)

## 🔧 环境配置

创建 `.env` 文件：

```bash
# API 基础地址
NUXT_PUBLIC_API_BASE=http://localhost:8080

# 应用名称
NUXT_PUBLIC_APP_NAME=简历制作工具
```

## 📱 响应式设计

完全响应式设计，支持：
- 桌面端 (1200px+)
- 平板端 (768px - 1199px)
- 移动端 (<768px)

## 🔍 SEO 优化

- 动态 meta 标签
- 结构化数据 (JSON-LD)
- Open Graph 标签
- Twitter Cards
- 自动生成 sitemap.xml
- robots.txt 配置

## 🚦 性能优化

- 代码分割和懒加载
- 图片优化和懒加载
- CSS 和 JS 压缩
- 预渲染关键页面
- Core Web Vitals 优化

## 🧪 开发规范

### 代码规范
- 使用 TypeScript
- ESLint + Prettier
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### 提交规范
使用 Conventional Commits：
- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 样式调整
- `refactor:` 代码重构

## 📞 技术支持

如有问题，请联系开发团队或查看项目文档。

## 📄 许可证

Copyright © 2024 简历制作工具. All rights reserved. 