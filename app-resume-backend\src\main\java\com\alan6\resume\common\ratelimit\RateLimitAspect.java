package com.alan6.resume.common.ratelimit;

import com.alan6.resume.common.exception.RateLimitException;
import com.alan6.resume.common.utils.BusinessLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 限流切面类
 * 
 * 主要功能：
 * 1. 拦截标记@RateLimit注解的方法
 * 2. 基于Redisson实现分布式限流
 * 3. 支持多种限流键的解析策略
 * 4. 集成结构化日志记录
 * 
 * 限流策略：
 * - 支持令牌桶算法
 * - 支持滑动窗口限流
 * - 支持自定义限流键和参数
 * - 支持动态开关控制
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Aspect
@Component
public class RateLimitAspect {

    /**
     * Redisson客户端，用于分布式限流
     */
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 限流键前缀，区分不同应用的限流数据
     */
    private static final String RATE_LIMIT_KEY_PREFIX = "rate_limit:resume:";

    /**
     * 环绕通知：处理限流逻辑
     * 
     * 在方法执行前进行限流检查，如果超过限制则抛出异常
     * 
     * @param joinPoint 切点信息
     * @param rateLimit 限流注解
     * @return 方法执行结果
     * @throws Throwable 方法执行异常或限流异常
     */
    @Around("@annotation(rateLimit)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimit rateLimit) throws Throwable {
        // 检查限流是否启用
        if (!rateLimit.enabled()) {
            log.debug("限流已禁用，直接执行方法: {}", joinPoint.getSignature().getName());
            return joinPoint.proceed();
        }

        // 生成限流键
        String rateLimitKey = generateRateLimitKey(joinPoint, rateLimit);
        
        // 获取当前时间（用于性能监控）
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行限流检查
            boolean allowRequest = checkRateLimit(rateLimitKey, rateLimit);
            
            if (allowRequest) {
                // 限流检查通过，记录日志并执行方法
                BusinessLogUtils.logRateLimit(rateLimitKey, false, 
                    "limitType", rateLimit.limitType(),
                    "rate", rateLimit.rate(),
                    "rateInterval", rateLimit.rateInterval());
                
                return joinPoint.proceed();
            } else {
                // 触发限流，记录日志并抛出异常
                BusinessLogUtils.logRateLimit(rateLimitKey, true,
                    "limitType", rateLimit.limitType(),
                    "rate", rateLimit.rate(),
                    "rateInterval", rateLimit.rateInterval(),
                    "clientIp", getClientIp());
                
                throw new RateLimitException(rateLimitKey, rateLimit.limitType(), 
                                           rateLimit.rate(), rateLimit.rateInterval(), 
                                           rateLimit.message());
            }
            
        } catch (RateLimitException e) {
            // 重新抛出限流异常
            throw e;
        } catch (Exception e) {
            // 限流检查异常，记录错误日志但不影响业务执行
            BusinessLogUtils.logError("限流检查异常，允许请求通过", e,
                "rateLimitKey", rateLimitKey,
                "method", joinPoint.getSignature().getName());
            
            return joinPoint.proceed();
        } finally {
            // 记录限流检查耗时
            long duration = System.currentTimeMillis() - startTime;
            if (duration > 100) { // 超过100ms记录警告
                BusinessLogUtils.logWarning("限流检查耗时过长",
                    "rateLimitKey", rateLimitKey,
                    "duration", duration);
            }
        }
    }

    /**
     * 执行限流检查
     * 
     * 基于Redisson的RRateLimiter实现分布式限流
     * 
     * @param rateLimitKey 限流键
     * @param rateLimit 限流配置
     * @return true-允许请求，false-拒绝请求
     */
    private boolean checkRateLimit(String rateLimitKey, RateLimit rateLimit) {
        try {
            // 获取限流器实例
            RRateLimiter rateLimiter = redissonClient.getRateLimiter(rateLimitKey);
            
            // 尝试设置限流规则（如果之前没有设置）
            boolean ruleSet = rateLimiter.trySetRate(RateType.OVERALL, 
                                                     rateLimit.rate(), 
                                                     rateLimit.rateInterval(), 
                                                     RateIntervalUnit.SECONDS);
            
            if (!ruleSet) {
                log.debug("限流规则已存在: {}", rateLimitKey);
            }
            
            // 尝试获取许可
            return rateLimiter.tryAcquire();
            
        } catch (Exception e) {
            // 限流器异常，记录错误但允许请求通过
            log.error("限流器执行异常，允许请求通过: {}", rateLimitKey, e);
            return true;
        }
    }

    /**
     * 生成限流键
     * 
     * 支持多种键模式：
     * 1. 固定字符串
     * 2. 包含方法参数的动态键
     * 3. 包含请求信息的复合键
     * 
     * @param joinPoint 切点信息
     * @param rateLimit 限流配置
     * @return 生成的限流键
     */
    private String generateRateLimitKey(ProceedingJoinPoint joinPoint, RateLimit rateLimit) {
        String keyPattern = rateLimit.key();
        
        // 如果键模式不包含占位符，直接返回
        if (!keyPattern.contains("#")) {
            return RATE_LIMIT_KEY_PREFIX + keyPattern;
        }
        
        // 解析键模式中的参数占位符
        String resolvedKey = resolveKeyParameters(joinPoint, keyPattern);
        
        return RATE_LIMIT_KEY_PREFIX + resolvedKey;
    }

    /**
     * 解析键模式中的参数占位符
     * 
     * 支持的占位符格式：
     * - #paramName：方法参数名
     * - #index：参数索引（如#0、#1）
     * 
     * @param joinPoint 切点信息
     * @param keyPattern 键模式
     * @return 解析后的键
     */
    private String resolveKeyParameters(ProceedingJoinPoint joinPoint, String keyPattern) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();
        
        String resolvedKey = keyPattern;
        
        // 解析参数名占位符
        for (int i = 0; i < parameters.length; i++) {
            String paramName = parameters[i].getName();
            String placeholder = "#" + paramName;
            
            if (resolvedKey.contains(placeholder)) {
                String paramValue = args[i] != null ? args[i].toString() : "null";
                resolvedKey = resolvedKey.replace(placeholder, paramValue);
            }
            
            // 解析索引占位符
            String indexPlaceholder = "#" + i;
            if (resolvedKey.contains(indexPlaceholder)) {
                String paramValue = args[i] != null ? args[i].toString() : "null";
                resolvedKey = resolvedKey.replace(indexPlaceholder, paramValue);
            }
        }
        
        // 如果还有未解析的占位符，使用方法名替换
        if (resolvedKey.contains("#")) {
            String methodName = method.getName();
            resolvedKey = resolvedKey.replaceAll("#\\w+", methodName);
        }
        
        return resolvedKey;
    }

    /**
     * 获取客户端IP地址
     * 
     * @return 客户端IP地址
     */
    private String getClientIp() {
        try {
            ServletRequestAttributes attributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return getClientIpAddress(request);
            }
            
            return "unknown";
        } catch (Exception e) {
            log.debug("获取客户端IP失败", e);
            return "unknown";
        }
    }

    /**
     * 获取客户端真实IP地址
     * 
     * 考虑代理服务器和负载均衡器的情况
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }
        
        return ipAddress != null ? ipAddress : "unknown";
    }
} 