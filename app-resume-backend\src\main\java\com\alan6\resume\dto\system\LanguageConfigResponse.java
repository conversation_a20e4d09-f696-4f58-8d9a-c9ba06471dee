package com.alan6.resume.dto.system;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 语言配置响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
public class LanguageConfigResponse {

    /**
     * 语言代码
     */
    private String languageCode;

    /**
     * 语言名称
     */
    private String languageName;

    /**
     * 本地化名称
     */
    private String nativeName;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否为默认语言
     */
    private Boolean isDefault;

    /**
     * 配置内容（JSON格式的翻译文本）
     */
    private Map<String, Object> config;

    /**
     * 配置版本
     */
    private String version;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 配置文件大小（字节）
     */
    private Long configSize;

    /**
     * 翻译完成度（百分比）
     */
    private Double completeness;
} 