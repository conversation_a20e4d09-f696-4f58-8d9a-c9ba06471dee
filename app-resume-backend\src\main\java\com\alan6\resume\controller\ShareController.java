package com.alan6.resume.controller;

import com.alan6.resume.common.ratelimit.RateLimit;
import com.alan6.resume.common.utils.BusinessLogUtils;
import com.alan6.resume.core.R;
import com.alan6.resume.dto.share.*;
import com.alan6.resume.entity.ShareRecords;
import com.alan6.resume.security.UserPrincipal;
import com.alan6.resume.service.IShareRecordsService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 分享管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@RestController
@RequestMapping("/share")
@RequiredArgsConstructor
@Validated
@Tag(name = "分享管理", description = "分享相关接口")
public class ShareController {

    private final IShareRecordsService shareRecordsService;

    /**
     * 创建简历分享链接
     */
    @PostMapping("/resume/{id}")
    @Operation(summary = "创建简历分享链接", description = "为指定简历创建分享链接")
    @RateLimit(key = "share:create:resume", rate = 10, rateInterval = 60)
    public R<ShareResponse> createResumeShare(
            @Parameter(description = "简历ID") @PathVariable @NotNull Long id,
            @Parameter(description = "分享创建请求") @Valid @RequestBody ShareCreateRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始创建简历分享链接，简历ID: {}, 用户ID: {}", id, userId);
        
        try {
            ShareResponse response = shareRecordsService.createResumeShare(id, request, userId);
            
            BusinessLogUtils.logBusiness("创建简历分享链接成功", 
                    "resumeId", id, "userId", userId, "shareCode", response.getShareCode());
            
            log.info("成功创建简历分享链接，简历ID: {}, 分享码: {}", id, response.getShareCode());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("创建简历分享链接失败，简历ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("创建简历分享链接失败", e, "resumeId", id, "userId", userId);
            return R.fail("创建分享链接失败: " + e.getMessage());
        }
    }

    /**
     * 创建模板分享链接
     */
    @PostMapping("/template/{id}")
    @Operation(summary = "创建模板分享链接", description = "为指定模板创建分享链接")
    @RateLimit(key = "share:create:template", rate = 10, rateInterval = 60)
    public R<ShareResponse> createTemplateShare(
            @Parameter(description = "模板ID") @PathVariable @NotNull Long id,
            @Parameter(description = "分享创建请求") @Valid @RequestBody ShareCreateRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始创建模板分享链接，模板ID: {}, 用户ID: {}", id, userId);
        
        try {
            ShareResponse response = shareRecordsService.createTemplateShare(id, request, userId);
            
            BusinessLogUtils.logBusiness("创建模板分享链接成功", 
                    "templateId", id, "userId", userId, "shareCode", response.getShareCode());
            
            log.info("成功创建模板分享链接，模板ID: {}, 分享码: {}", id, response.getShareCode());
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("创建模板分享链接失败，模板ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("创建模板分享链接失败", e, "templateId", id, "userId", userId);
            return R.fail("创建分享链接失败: " + e.getMessage());
        }
    }

    /**
     * 访问分享内容
     */
    @PostMapping("/access/{code}")
    @Operation(summary = "访问分享内容", description = "通过分享码访问分享的内容")
    @RateLimit(key = "share:access", rate = 100, rateInterval = 60)
    public R<Map<String, Object>> accessShare(
            @Parameter(description = "分享码") @PathVariable String code,
            @Parameter(description = "访问请求") @Valid @RequestBody ShareAccessRequest request) {
        
        log.info("开始访问分享内容，分享码: {}", code);
        
        try {
            Map<String, Object> result = shareRecordsService.accessShare(code, request);
            
            BusinessLogUtils.logBusiness("访问分享内容成功", 
                    "shareCode", code, "resourceType", result.get("resourceType"));
            
            log.info("成功访问分享内容，分享码: {}", code);
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("访问分享内容失败，分享码: {}", code, e);
            BusinessLogUtils.logError("访问分享内容失败", e, "shareCode", code);
            return R.fail("访问失败: " + e.getMessage());
        }
    }

    /**
     * 获取分享记录列表
     */
    @GetMapping("/records")
    @Operation(summary = "获取分享记录列表", description = "获取当前用户的分享记录列表，支持分页和筛选")
    @RateLimit(key = "share:records", rate = 60, rateInterval = 60)
    public R<Page<ShareRecords>> getShareRecords(
            @Parameter(description = "资源类型") @RequestParam(required = false) String resourceType,
            @Parameter(description = "资源ID") @RequestParam(required = false) Long resourceId,
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始获取分享记录列表，用户ID: {}, 资源类型: {}", userId, resourceType);
        
        try {
            Page<ShareRecords> result = shareRecordsService.getShareRecords(resourceType, resourceId, current, size, userId);
            
            BusinessLogUtils.logBusiness("获取分享记录列表成功", 
                    "userId", userId, "recordCount", result.getRecords().size());
            
            log.info("成功获取分享记录列表，用户ID: {}, 记录数: {}", userId, result.getRecords().size());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("获取分享记录列表失败，用户ID: {}", userId, e);
            BusinessLogUtils.logError("获取分享记录列表失败", e, "userId", userId);
            return R.fail("获取分享记录失败");
        }
    }

    /**
     * 更新分享配置
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新分享配置", description = "更新指定分享的配置信息")
    @RateLimit(key = "share:update", rate = 20, rateInterval = 60)
    public R<Boolean> updateShareConfig(
            @Parameter(description = "分享ID") @PathVariable @NotNull Long id,
            @Parameter(description = "更新请求") @Valid @RequestBody ShareUpdateRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始更新分享配置，分享ID: {}, 用户ID: {}", id, userId);
        
        try {
            // 转换更新请求为创建请求（复用逻辑）
            ShareCreateRequest createRequest = new ShareCreateRequest();
            createRequest.setShareType(request.getShareType());
            createRequest.setExpireTime(request.getExpireTime());
            createRequest.setPassword(request.getPassword());
            createRequest.setAllowDownload(request.getAllowDownload());
            createRequest.setViewLimit(request.getViewLimit());
            createRequest.setShareScope(request.getShareScope());
            createRequest.setAllowedDomains(request.getAllowedDomains());
            createRequest.setDescription(request.getDescription());
            
            Boolean result = shareRecordsService.updateShareConfig(id, createRequest, userId);
            
            BusinessLogUtils.logBusiness("更新分享配置成功", 
                    "shareId", id, "userId", userId);
            
            log.info("成功更新分享配置，分享ID: {}", id);
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("更新分享配置失败，分享ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("更新分享配置失败", e, "shareId", id, "userId", userId);
            return R.fail("更新分享配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除分享链接
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除分享链接", description = "删除指定的分享链接")
    @RateLimit(key = "share:delete", rate = 20, rateInterval = 60)
    public R<Boolean> deleteShare(
            @Parameter(description = "分享ID") @PathVariable @NotNull Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始删除分享链接，分享ID: {}, 用户ID: {}", id, userId);
        
        try {
            Boolean result = shareRecordsService.deleteShare(id, userId);
            
            BusinessLogUtils.logBusiness("删除分享链接成功", 
                    "shareId", id, "userId", userId);
            
            log.info("成功删除分享链接，分享ID: {}", id);
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("删除分享链接失败，分享ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("删除分享链接失败", e, "shareId", id, "userId", userId);
            return R.fail("删除分享链接失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除分享链接
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除分享链接", description = "批量删除多个分享链接")
    @RateLimit(key = "share:batch:delete", rate = 10, rateInterval = 60)
    public R<Map<String, Object>> batchDeleteShares(
            @Parameter(description = "分享ID列表") @Valid @RequestBody ShareBatchDeleteRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始批量删除分享链接，用户ID: {}, ID列表: {}", userId, request.getShareIds());
        
        try {
            Map<String, Object> result = shareRecordsService.batchDeleteShares(request.getShareIds(), userId);
            
            BusinessLogUtils.logBusiness("批量删除分享链接成功", 
                    "userId", userId, "deleteCount", result.get("deletedCount"));
            
            log.info("成功批量删除分享链接，用户ID: {}, 删除数量: {}", userId, result.get("deletedCount"));
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("批量删除分享链接失败，用户ID: {}", userId, e);
            BusinessLogUtils.logError("批量删除分享链接失败", e, "userId", userId);
            return R.fail("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取分享统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取分享统计信息", description = "获取用户的分享统计数据")
    @RateLimit(key = "share:statistics", rate = 30, rateInterval = 60)
    public R<Map<String, Object>> getShareStatistics(
            @Parameter(description = "资源类型") @RequestParam(required = false) String resourceType,
            @Parameter(description = "开始日期") @RequestParam(required = false) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) LocalDateTime endDate,
            @Parameter(description = "时间粒度") @RequestParam(defaultValue = "day") String granularity,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始获取分享统计信息，用户ID: {}, 资源类型: {}", userId, resourceType);
        
        try {
            Map<String, Object> result = shareRecordsService.getShareStatistics(resourceType, startDate, endDate, granularity, userId);
            
            BusinessLogUtils.logBusiness("获取分享统计信息成功", 
                    "userId", userId, "totalShares", result.get("totalShares"));
            
            log.info("成功获取分享统计信息，用户ID: {}, 总分享数: {}", userId, result.get("totalShares"));
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("获取分享统计信息失败，用户ID: {}", userId, e);
            BusinessLogUtils.logError("获取分享统计信息失败", e, "userId", userId);
            return R.fail("获取统计信息失败");
        }
    }

    /**
     * 获取分享详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取分享详情", description = "获取指定分享的详细信息")
    @RateLimit(key = "share:detail", rate = 60, rateInterval = 60)
    public R<ShareRecords> getShareDetail(
            @Parameter(description = "分享ID") @PathVariable @NotNull Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始获取分享详情，分享ID: {}, 用户ID: {}", id, userId);
        
        try {
            ShareRecords result = shareRecordsService.getShareDetail(id, userId);
            
            BusinessLogUtils.logBusiness("获取分享详情成功", 
                    "shareId", id, "userId", userId);
            
            log.info("成功获取分享详情，分享ID: {}", id);
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("获取分享详情失败，分享ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("获取分享详情失败", e, "shareId", id, "userId", userId);
            return R.fail("获取分享详情失败: " + e.getMessage());
        }
    }

    /**
     * 切换分享状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "切换分享状态", description = "启用或禁用指定的分享链接")
    @RateLimit(key = "share:status", rate = 20, rateInterval = 60)
    public R<Boolean> toggleShareStatus(
            @Parameter(description = "分享ID") @PathVariable @NotNull Long id,
            @Parameter(description = "状态切换请求") @Valid @RequestBody ShareStatusRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始切换分享状态，分享ID: {}, 用户ID: {}, 状态: {}", id, userId, request.getIsActive());
        
        try {
            Boolean result = shareRecordsService.toggleShareStatus(id, request.getIsActive(), userId);
            
            BusinessLogUtils.logBusiness("切换分享状态成功", 
                    "shareId", id, "userId", userId, "isActive", request.getIsActive());
            
            log.info("成功切换分享状态，分享ID: {}, 状态: {}", id, request.getIsActive());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("切换分享状态失败，分享ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("切换分享状态失败", e, "shareId", id, "userId", userId);
            return R.fail("切换分享状态失败: " + e.getMessage());
        }
    }

    /**
     * 预览分享内容
     */
    @GetMapping("/preview/{code}")
    @Operation(summary = "预览分享内容", description = "预览分享内容，不计入访问次数")
    @RateLimit(key = "share:preview", rate = 60, rateInterval = 60)
    public R<Map<String, Object>> previewShare(
            @Parameter(description = "分享码") @PathVariable String code,
            @Parameter(description = "访问密码") @RequestParam(required = false) String password) {
        
        log.info("开始预览分享内容，分享码: {}", code);
        
        try {
            Map<String, Object> result = shareRecordsService.previewShare(code, password);
            
            BusinessLogUtils.logBusiness("预览分享内容成功", 
                    "shareCode", code, "isValid", result.get("isValid"));
            
            log.info("成功预览分享内容，分享码: {}, 有效性: {}", code, result.get("isValid"));
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("预览分享内容失败，分享码: {}", code, e);
            BusinessLogUtils.logError("预览分享内容失败", e, "shareCode", code);
            return R.fail("预览失败: " + e.getMessage());
        }
    }

    /**
     * 验证分享访问权限
     */
    @PostMapping("/verify/{code}")
    @Operation(summary = "验证分享访问权限", description = "验证分享码和密码是否正确")
    @RateLimit(key = "share:verify", rate = 30, rateInterval = 60)
    public R<Map<String, Object>> verifyShareAccess(
            @Parameter(description = "分享码") @PathVariable String code,
            @Parameter(description = "验证请求") @Valid @RequestBody ShareVerifyRequest request) {
        
        log.info("开始验证分享访问权限，分享码: {}", code);
        
        try {
            Map<String, Object> result = shareRecordsService.verifyShareAccess(code, request.getPassword());
            
            BusinessLogUtils.logBusiness("验证分享访问权限完成", 
                    "shareCode", code, "verified", result.get("verified"));
            
            log.info("完成验证分享访问权限，分享码: {}, 验证结果: {}", code, result.get("verified"));
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("验证分享访问权限失败，分享码: {}", code, e);
            BusinessLogUtils.logError("验证分享访问权限失败", e, "shareCode", code);
            return R.fail("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取分享访问日志
     */
    @GetMapping("/{id}/access-logs")
    @Operation(summary = "获取分享访问日志", description = "获取指定分享的访问日志记录")
    @RateLimit(key = "share:access-logs", rate = 30, rateInterval = 60)
    public R<Page<Map<String, Object>>> getShareAccessLogs(
            @Parameter(description = "分享ID") @PathVariable @NotNull Long id,
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "开始日期") @RequestParam(required = false) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) LocalDateTime endDate,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始获取分享访问日志，分享ID: {}, 用户ID: {}", id, userId);
        
        try {
            Page<Map<String, Object>> result = shareRecordsService.getShareAccessLogs(id, current, size, startDate, endDate, userId);
            
            BusinessLogUtils.logBusiness("获取分享访问日志成功", 
                    "shareId", id, "userId", userId, "logCount", result.getRecords().size());
            
            log.info("成功获取分享访问日志，分享ID: {}, 日志数量: {}", id, result.getRecords().size());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("获取分享访问日志失败，分享ID: {}, 用户ID: {}", id, userId, e);
            BusinessLogUtils.logError("获取分享访问日志失败", e, "shareId", id, "userId", userId);
            return R.fail("获取访问日志失败: " + e.getMessage());
        }
    }

    /**
     * 批量操作分享状态
     */
    @PutMapping("/batch/status")
    @Operation(summary = "批量操作分享状态", description = "批量启用或禁用分享链接")
    @RateLimit(key = "share:batch-status", rate = 10, rateInterval = 60)
    public R<Map<String, Object>> batchToggleShareStatus(
            @Parameter(description = "批量状态操作请求") @Valid @RequestBody ShareBatchStatusRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始批量操作分享状态，分享ID列表: {}, 目标状态: {}, 用户ID: {}", 
                request.getShareIds(), request.getIsActive(), userId);
        
        try {
            Map<String, Object> result = shareRecordsService.batchToggleShareStatus(
                    request.getShareIds(), request.getIsActive(), userId);
            
            BusinessLogUtils.logBusiness("批量操作分享状态完成", 
                    "shareIds", request.getShareIds(), "isActive", request.getIsActive(), 
                    "userId", userId, "successCount", result.get("successCount"));
            
            log.info("成功批量操作分享状态，成功: {}, 失败: {}", 
                    result.get("successCount"), result.get("failedCount"));
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("批量操作分享状态失败，分享ID列表: {}, 用户ID: {}", request.getShareIds(), userId, e);
            BusinessLogUtils.logError("批量操作分享状态失败", e, 
                    "shareIds", request.getShareIds(), "userId", userId);
            return R.fail("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量设置过期时间
     */
    @PutMapping("/batch/expire")
    @Operation(summary = "批量设置过期时间", description = "批量设置分享链接的过期时间")
    @RateLimit(key = "share:batch-expire", rate = 10, rateInterval = 60)
    public R<Map<String, Object>> batchSetExpireTime(
            @Parameter(description = "批量过期时间设置请求") @Valid @RequestBody ShareBatchExpireRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始批量设置过期时间，分享ID列表: {}, 过期时间: {}, 用户ID: {}", 
                request.getShareIds(), request.getExpireTime(), userId);
        
        try {
            Map<String, Object> result = shareRecordsService.batchSetExpireTime(
                    request.getShareIds(), request.getExpireTime(), userId);
            
            BusinessLogUtils.logBusiness("批量设置过期时间完成", 
                    "shareIds", request.getShareIds(), "expireTime", request.getExpireTime(), 
                    "userId", userId, "successCount", result.get("successCount"));
            
            log.info("成功批量设置过期时间，成功: {}, 失败: {}", 
                    result.get("successCount"), result.get("failedCount"));
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("批量设置过期时间失败，分享ID列表: {}, 用户ID: {}", request.getShareIds(), userId, e);
            BusinessLogUtils.logError("批量设置过期时间失败", e, 
                    "shareIds", request.getShareIds(), "userId", userId);
            return R.fail("批量设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门分享
     */
    @GetMapping("/trending")
    @Operation(summary = "获取热门分享", description = "获取用户的热门分享列表")
    @RateLimit(key = "share:trending", rate = 30, rateInterval = 60)
    public R<Map<String, Object>> getTrendingShares(
            @Parameter(description = "资源类型") @RequestParam(required = false) String resourceType,
            @Parameter(description = "统计周期") @RequestParam(defaultValue = "30d") String period,
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "10") Integer limit,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        if (userPrincipal == null) {
            return R.fail("请先登录");
        }
        
        Long userId = userPrincipal.getUserId();
        log.info("开始获取热门分享，资源类型: {}, 统计周期: {}, 限制数量: {}, 用户ID: {}", 
                resourceType, period, limit, userId);
        
        try {
            Map<String, Object> result = shareRecordsService.getTrendingShares(resourceType, period, limit, userId);
            
            BusinessLogUtils.logBusiness("获取热门分享成功", 
                    "resourceType", resourceType, "period", period, "limit", limit, 
                    "userId", userId, "resultCount", ((List<?>) result.get("trending")).size());
            
            log.info("成功获取热门分享，返回数量: {}", ((List<?>) result.get("trending")).size());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("获取热门分享失败，用户ID: {}", userId, e);
            BusinessLogUtils.logError("获取热门分享失败", e, "userId", userId);
            return R.fail("获取热门分享失败: " + e.getMessage());
        }
    }
}
