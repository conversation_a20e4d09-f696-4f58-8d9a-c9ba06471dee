package com.alan6.resume.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 业务日志工具类
 * 
 * 主要功能：
 * 1. 提供结构化日志记录功能
 * 2. 管理日志上下文信息（MDC）
 * 3. 为后期链路追踪预留扩展点
 * 4. 支持性能监控和业务埋点
 * 
 * 设计原则：
 * - 为链路追踪预留接口，后期可无缝升级
 * - 统一业务日志格式，便于日志分析
 * - 支持多种日志级别和场景
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Component
public class BusinessLogUtils {

    /**
     * MDC键名常量定义
     */
    private static final String REQUEST_ID = "requestId";
    private static final String USER_ID = "userId";
    private static final String OPERATION = "operation";
    private static final String CLIENT_IP = "clientIp";
    private static final String PLATFORM = "platform";
    private static final String DURATION = "duration";
    
    /**
     * 预留链路追踪键名，后期可直接使用
     */
    private static final String TRACE_ID = "traceId";
    private static final String SPAN_ID = "spanId";

    /**
     * 设置请求上下文信息
     * 
     * 用于在整个请求生命周期中传递关键信息
     * 注意：为后期链路追踪预留traceId位置
     * 
     * @param requestId 请求标识，后期可替换为真实的traceId
     * @param userId 用户标识
     * @param operation 操作名称
     */
    public static void setContext(String requestId, String userId, String operation) {
        MDC.put(REQUEST_ID, requestId);
        MDC.put(USER_ID, userId);
        MDC.put(OPERATION, operation);
        
        // 预留：后期可以在这里添加真实的链路追踪信息
        // MDC.put(TRACE_ID, TraceContext.current().traceId());
        // MDC.put(SPAN_ID, TraceContext.current().spanId());
    }

    /**
     * 设置客户端信息
     * 
     * @param clientIp 客户端IP地址
     * @param platform 客户端平台
     */
    public static void setClientInfo(String clientIp, String platform) {
        if (clientIp != null) {
            MDC.put(CLIENT_IP, clientIp);
        }
        if (platform != null) {
            MDC.put(PLATFORM, platform);
        }
    }

    /**
     * 生成请求ID
     * 
     * 后期链路追踪启用后，可以替换为真实的traceId
     * 
     * @return 请求标识
     */
    public static String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 记录业务操作日志
     * 
     * 使用INFO级别记录正常的业务操作
     * 
     * @param message 日志消息
     * @param keyValues 键值对参数（key1, value1, key2, value2...）
     */
    public static void logBusiness(String message, Object... keyValues) {
        String formattedMessage = buildMessage(message, keyValues);
        log.info(formattedMessage);
    }

    /**
     * 记录业务成功日志
     * 
     * @param operation 操作名称
     * @param keyValues 键值对参数
     */
    public static void logSuccess(String operation, Object... keyValues) {
        String message = String.format("业务操作成功: %s", operation);
        logBusiness(message, keyValues);
    }

    /**
     * 记录业务错误日志
     * 
     * @param message 错误消息
     * @param throwable 异常对象
     * @param keyValues 键值对参数
     */
    public static void logError(String message, Throwable throwable, Object... keyValues) {
        String formattedMessage = buildMessage(message, keyValues);
        log.error(formattedMessage, throwable);
    }

    /**
     * 记录业务警告日志
     * 
     * @param message 警告消息
     * @param keyValues 键值对参数
     */
    public static void logWarning(String message, Object... keyValues) {
        String formattedMessage = buildMessage(message, keyValues);
        log.warn(formattedMessage);
    }

    /**
     * 记录性能监控日志
     * 
     * @param operation 操作名称
     * @param duration 执行时长（毫秒）
     * @param keyValues 键值对参数
     */
    public static void logPerformance(String operation, long duration, Object... keyValues) {
        // 设置耗时到MDC，便于日志分析
        MDC.put(DURATION, String.valueOf(duration));
        
        try {
            String message = String.format("性能监控 - 操作: %s, 耗时: %dms", operation, duration);
            logBusiness(message, keyValues);
        } finally {
            // 清理临时MDC
            MDC.remove(DURATION);
        }
    }

    /**
     * 记录限流日志
     * 
     * @param key 限流键
     * @param isBlocked 是否被限流
     * @param keyValues 键值对参数
     */
    public static void logRateLimit(String key, boolean isBlocked, Object... keyValues) {
        if (isBlocked) {
            logWarning("触发限流: " + key, keyValues);
        } else {
            log.debug("限流检查通过: {}", key);
        }
    }

    /**
     * 记录用户行为日志
     * 
     * @param action 用户行为
     * @param result 行为结果
     * @param keyValues 键值对参数
     */
    public static void logUserAction(String action, String result, Object... keyValues) {
        String message = String.format("用户行为: %s, 结果: %s", action, result);
        logBusiness(message, keyValues);
    }

    /**
     * 清理日志上下文
     * 
     * 在请求结束时调用，避免内存泄漏
     */
    public static void clearContext() {
        MDC.clear();
    }

    /**
     * 获取当前请求ID
     * 
     * @return 当前请求ID，如果没有则返回null
     */
    public static String getCurrentRequestId() {
        return MDC.get(REQUEST_ID);
    }

    /**
     * 获取当前用户ID
     * 
     * @return 当前用户ID，如果没有则返回null
     */
    public static String getCurrentUserId() {
        return MDC.get(USER_ID);
    }

    /**
     * 构建格式化的日志消息
     * 
     * 将键值对参数转换为结构化的日志消息
     * 
     * @param message 基础消息
     * @param keyValues 键值对参数
     * @return 格式化后的消息
     */
    private static String buildMessage(String message, Object... keyValues) {
        StringBuilder sb = new StringBuilder();
        sb.append("[BUSINESS] ").append(message);
        
        // 处理键值对参数
        if (keyValues != null && keyValues.length > 0) {
            sb.append(" |");
            for (int i = 0; i < keyValues.length; i += 2) {
                if (i + 1 < keyValues.length) {
                    sb.append(" ").append(keyValues[i]).append("=").append(keyValues[i + 1]);
                }
            }
        }
        
        return sb.toString();
    }
} 