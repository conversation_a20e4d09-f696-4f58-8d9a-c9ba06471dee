package com.alan6.resume.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@Schema(name = "Orders对象", description = "订单表")
public class Orders implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单类型（1:会员充值,2:模板购买）")
    private Byte orderType;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "原价")
    private BigDecimal originalAmount;

    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    @Schema(description = "实付金额")
    private BigDecimal finalAmount;

    @Schema(description = "支付方式（1:微信,2:支付宝）")
    private Byte paymentMethod;

    @Schema(description = "支付平台")
    private String paymentPlatform;

    @Schema(description = "第三方交易号")
    private String tradeNo;

    @Schema(description = "订单状态（1:待支付,2:已支付,3:已完成,4:已取消,5:已退款）")
    private Byte orderStatus;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
