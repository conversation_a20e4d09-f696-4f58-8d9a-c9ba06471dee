package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 简历创建请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "简历创建请求")
public class ResumeCreateRequest {

    /**
     * 简历名称
     */
    @Schema(description = "简历名称", example = "我的简历", required = true)
    @NotBlank(message = "简历名称不能为空")
    @Size(min = 1, max = 100, message = "简历名称长度必须在1-100字符之间")
    private String name;

    /**
     * 使用的模板ID
     */
    @Schema(description = "使用的模板ID", example = "3001")
    private Long templateId;

    /**
     * 简历语言
     */
    @Schema(description = "简历语言", example = "zh-CN")
    private String language = "zh-CN";

    /**
     * 父简历ID（从已有简历复制时使用）
     */
    @Schema(description = "父简历ID", example = "2001")
    private Long parentResumeId;
} 