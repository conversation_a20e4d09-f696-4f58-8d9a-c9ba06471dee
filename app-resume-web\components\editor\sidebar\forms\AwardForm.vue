<template>
  <div class="award-form">
    <div class="form-header">
      <h4 class="form-title">获奖荣誉</h4>
      <p class="form-desc">展示您获得的奖项和荣誉</p>
    </div>
    
    <div v-if="formData.awards && formData.awards.length > 0" class="awards-list">
      <div v-for="(award, index) in formData.awards" :key="index" class="award-item">
        <div class="award-header">
          <input
            v-model="award.name"
            type="text"
            class="award-name-input"
            placeholder="奖项名称"
            @input="handleAwardChange"
          />
          <div class="date-picker-wrapper">
            <input
              v-model="award.date"
              type="text"
              class="award-date-input"
              placeholder="获奖时间"
              readonly
              @click="showDatePicker(index)"
            />
            <div 
              v-if="activeDatePicker === index" 
              class="date-picker-dropdown"
            >
              <div class="date-picker-header">
                <button type="button" @click="changeYear(-1)">‹</button>
                <span>{{ currentYear }}</span>
                <button type="button" @click="changeYear(1)">›</button>
              </div>
              <div class="month-grid">
                <button
                  v-for="month in 12"
                  :key="month"
                  type="button"
                  class="month-btn"
                  :class="{ active: isSelectedMonth(index, month) }"
                  @click="selectDate(index, currentYear, month)"
                >
                  {{ month }}月
                </button>
              </div>
            </div>
          </div>
          <button 
            class="delete-btn"
            @click="removeAward(index)"
            title="删除奖项"
          >
            ×
          </button>
        </div>
        
        <div class="award-details">
          <input
            v-model="award.organization"
            type="text"
            class="organization-input"
            placeholder="颁发机构"
            @input="handleAwardChange"
          />
          <select
            v-model="award.level"
            class="level-select"
            @change="handleAwardChange"
          >
            <option value="">选择级别</option>
            <option value="国际级">国际级</option>
            <option value="国家级">国家级</option>
            <option value="省级">省级</option>
            <option value="市级">市级</option>
            <option value="校级">校级</option>
            <option value="公司级">公司级</option>
            <option value="其他">其他</option>
          </select>
        </div>
        
        <RichTextEditor
          v-model="award.description"
          placeholder="详细描述（如：获奖原因、竞争情况等）"
          min-height="120px"
          @update:modelValue="handleAwardChange"
        />
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>暂无获奖荣誉信息</p>
      <p class="empty-hint">点击下方按钮添加您的获奖荣誉</p>
    </div>
    
    <div class="form-actions">
      <button class="add-btn" @click="addAward">
        <Icon name="plus" size="sm" />
        <span>添加奖项</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, ref, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ awards: [] })
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = reactive({
  awards: []
})

// 防抖定时器
let debounceTimer = null

// 日期选择器状态
const activeDatePicker = ref('')
const currentYear = ref(new Date().getFullYear())

// 监听传入数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.awards) {
    formData.awards = [...newData.awards]
  } else {
    formData.awards = []
  }
}, { immediate: true, deep: true })

/**
 * 处理奖项变化 - 实时更新
 */
const handleAwardChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的定时器，300ms后触发更新
  debounceTimer = setTimeout(() => {
    emit('update', { awards: [...formData.awards] })
  }, 300)
}

/**
 * 添加奖项
 */
const addAward = () => {
  formData.awards.push({
    name: '',
    date: '',
    organization: '',
    level: '',
    description: ''
  })
  handleAwardChange()
}

/**
 * 删除奖项
 */
const removeAward = (index) => {
  formData.awards.splice(index, 1)
  handleAwardChange()
}

/**
 * 显示日期选择器
 */
const showDatePicker = (index) => {
  activeDatePicker.value = index
  // 根据当前值设置年份
  const currentValue = formData.awards[index].date
  if (currentValue) {
    const year = parseInt(currentValue.split('-')[0])
    if (year) {
      currentYear.value = year
    }
  }
}

/**
 * 改变年份
 */
const changeYear = (delta) => {
  currentYear.value += delta
}

/**
 * 检查是否为选中的月份
 */
const isSelectedMonth = (index, month) => {
  const currentValue = formData.awards[index].date
  if (!currentValue) return false
  
  const [year, selectedMonth] = currentValue.split('-')
  return parseInt(year) === currentYear.value && parseInt(selectedMonth) === month
}

/**
 * 选择日期
 */
const selectDate = (index, year, month) => {
  const dateStr = `${year}-${month.toString().padStart(2, '0')}`
  formData.awards[index].date = dateStr
  
  // 关闭日期选择器
  activeDatePicker.value = ''
  
  // 触发更新
  handleAwardChange()
}

/**
 * 点击外部关闭日期选择器
 */
const handleClickOutside = (event) => {
  if (!event.target.closest('.date-picker-wrapper')) {
    activeDatePicker.value = ''
  }
}

// 挂载时添加全局点击监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 卸载时移除监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.award-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.awards-list {
  @apply space-y-4 mb-6;
}

.award-item {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-3;
}

.award-header {
  @apply flex items-center gap-2;
}

.award-name-input {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.date-picker-wrapper {
  @apply relative;
}

.award-date-input {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer;
  min-width: 140px;
}

.date-picker-dropdown {
  @apply absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 p-3;
  min-width: 240px;
}

.date-picker-header {
  @apply flex items-center justify-between mb-3;
}

.date-picker-header button {
  @apply w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-all duration-200 text-sm;
}

.date-picker-header span {
  @apply font-medium text-gray-900 text-base;
}

.month-grid {
  @apply grid grid-cols-4 gap-2;
}

.month-btn {
  @apply px-3 py-2 text-sm text-gray-700 bg-gray-50 rounded hover:bg-blue-50 hover:text-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-400 transition-all duration-200 text-center whitespace-nowrap;
  min-width: 50px;
}

.month-btn.active {
  @apply bg-blue-500 text-white;
}

.delete-btn {
  @apply w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold;
}

.award-details {
  @apply flex items-center gap-2;
}

.organization-input {
  @apply flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.level-select {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
  min-width: 100px;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.empty-hint {
  @apply text-xs text-gray-400 mt-1;
}

.form-actions {
  @apply pt-4 border-t border-gray-200;
}

.add-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}
</style> 