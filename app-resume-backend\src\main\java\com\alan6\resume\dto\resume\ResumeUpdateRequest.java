package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;

/**
 * 简历更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "简历更新请求")
public class ResumeUpdateRequest {

    /**
     * 简历名称
     */
    @Schema(description = "简历名称", example = "更新后的简历名称")
    @Size(min = 1, max = 100, message = "简历名称长度必须在1-100字符之间")
    private String name;

    /**
     * 简历语言
     */
    @Schema(description = "简历语言", example = "en")
    private String language;

    /**
     * 自定义样式配置
     */
    @Schema(description = "自定义样式配置", example = "{\"theme\":\"blue\"}")
    private String customStyles;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "1")
    private Byte isPublic;
} 