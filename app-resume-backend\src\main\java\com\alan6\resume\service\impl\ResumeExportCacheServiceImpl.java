package com.alan6.resume.service.impl;

import com.alan6.resume.common.utils.RedisUtils;
import com.alan6.resume.service.IResumeExportCacheService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 简历导出缓存服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeExportCacheServiceImpl implements IResumeExportCacheService {

    private final RedisUtils redisUtils;
    private final ObjectMapper objectMapper;

    // 缓存key前缀
    private static final String RESUME_DATA_PREFIX = "export:resume:";
    private static final String RETRY_COUNT_PREFIX = "export:retry:";
    private static final String EXPORT_OPTIONS_PREFIX = "export:options:";

    // 默认过期时间（秒）
    private static final int DEFAULT_EXPIRE_SECONDS = 3600; // 1小时

    @Override
    public String cacheResumeData(Long resumeId, JsonNode resumeData, int expireSeconds) {
        String key = generateResumeDataKey(resumeId);
        try {
            String jsonString = objectMapper.writeValueAsString(resumeData);
            redisUtils.set(key, jsonString, expireSeconds);
            log.info("缓存简历数据成功，resumeId: {}, key: {}", resumeId, key);
            return key;
        } catch (Exception e) {
            log.error("缓存简历数据失败，resumeId: {}", resumeId, e);
            throw new RuntimeException("缓存简历数据失败", e);
        }
    }

    @Override
    public JsonNode getCachedResumeData(Long resumeId) {
        String key = generateResumeDataKey(resumeId);
        try {
            String jsonString = (String) redisUtils.get(key);
            if (jsonString == null) {
                log.debug("简历数据缓存不存在，resumeId: {}", resumeId);
                return null;
            }
            return objectMapper.readTree(jsonString);
        } catch (Exception e) {
            log.error("获取缓存简历数据失败，resumeId: {}", resumeId, e);
            return null;
        }
    }

    @Override
    public boolean removeCachedResumeData(Long resumeId) {
        String key = generateResumeDataKey(resumeId);
        try {
            boolean result = redisUtils.delete(key);
            log.info("删除简历数据缓存成功，resumeId: {}", resumeId);
            return result;
        } catch (Exception e) {
            log.error("删除简历数据缓存失败，resumeId: {}", resumeId, e);
            return false;
        }
    }

    @Override
    public int getRetryCount(String jobId) {
        String key = generateRetryCountKey(jobId);
        try {
            Object count = redisUtils.get(key);
            if (count == null) {
                return 0;
            }
            return Integer.parseInt(count.toString());
        } catch (Exception e) {
            log.error("获取重试次数失败，jobId: {}", jobId, e);
            return 0;
        }
    }

    @Override
    public int incrementRetryCount(String jobId) {
        String key = generateRetryCountKey(jobId);
        try {
            Long count = redisUtils.incr(key);
            // 设置过期时间，防止内存泄漏
            redisUtils.expire(key, DEFAULT_EXPIRE_SECONDS);
            log.info("增加重试次数成功，jobId: {}, count: {}", jobId, count);
            return count.intValue();
        } catch (Exception e) {
            log.error("增加重试次数失败，jobId: {}", jobId, e);
            return 0;
        }
    }

    @Override
    public boolean resetRetryCount(String jobId) {
        String key = generateRetryCountKey(jobId);
        try {
            boolean result = redisUtils.delete(key);
            log.info("重置重试次数成功，jobId: {}", jobId);
            return result;
        } catch (Exception e) {
            log.error("重置重试次数失败，jobId: {}", jobId, e);
            return false;
        }
    }

    @Override
    public String cacheExportOptions(String jobId, JsonNode exportOptions, int expireSeconds) {
        String key = generateExportOptionsKey(jobId);
        try {
            String jsonString = objectMapper.writeValueAsString(exportOptions);
            redisUtils.set(key, jsonString, expireSeconds);
            log.info("缓存导出选项成功，jobId: {}, key: {}", jobId, key);
            return key;
        } catch (Exception e) {
            log.error("缓存导出选项失败，jobId: {}", jobId, e);
            throw new RuntimeException("缓存导出选项失败", e);
        }
    }

    @Override
    public JsonNode getCachedExportOptions(String jobId) {
        String key = generateExportOptionsKey(jobId);
        try {
            String jsonString = (String) redisUtils.get(key);
            if (jsonString == null) {
                log.debug("导出选项缓存不存在，jobId: {}", jobId);
                return null;
            }
            return objectMapper.readTree(jsonString);
        } catch (Exception e) {
            log.error("获取缓存导出选项失败，jobId: {}", jobId, e);
            return null;
        }
    }

    @Override
    public boolean removeCachedExportOptions(String jobId) {
        String key = generateExportOptionsKey(jobId);
        try {
            boolean result = redisUtils.delete(key);
            log.info("删除导出选项缓存成功，jobId: {}", jobId);
            return result;
        } catch (Exception e) {
            log.error("删除导出选项缓存失败，jobId: {}", jobId, e);
            return false;
        }
    }

    @Override
    public String generateResumeDataKey(Long resumeId) {
        return RESUME_DATA_PREFIX + resumeId;
    }

    @Override
    public String generateRetryCountKey(String jobId) {
        return RETRY_COUNT_PREFIX + jobId;
    }

    @Override
    public String generateExportOptionsKey(String jobId) {
        return EXPORT_OPTIONS_PREFIX + jobId;
    }
} 