import{r as f,l as se,A as ne,c as n,o as a,a as t,q as b,b as ae,p as r,v as d,t as v,F as g,g as k,m as $,s as B,d as z,x as A,H as ie}from"./CURHyiUL.js";import{I as ue}from"./D7AOVZt6.js";import{_ as re}from"./DlAUqK2U.js";const de={class:"basic-info-form"},ce={class:"form-content"},pe={class:"form-group photo-group"},me={class:"photo-upload"},ve=["src"],he={key:1,class:"photo-placeholder"},ye={class:"form-row"},be={class:"form-group"},fe={class:"form-group"},ge={class:"form-group"},ke={class:"form-group"},_e={class:"form-row"},Se={class:"form-group"},Ce={class:"form-group"},xe={class:"form-group"},Ie={class:"form-group"},Ve={class:"form-group"},Oe={class:"form-group"},Ue={class:"section-divider"},we={class:"section-actions"},Fe={key:0,class:"field-selector"},Pe={class:"field-options"},$e=["onClick"],Be={class:"option-icon"},ze={key:1,class:"optional-section"},Ae={key:0},Le={class:"form-group-horizontal"},Me={class:"form-label-horizontal"},Ne=["onUpdate:modelValue","type","placeholder"],Te=["onClick"],He={key:1,class:"custom-fields-section"},je={class:"form-group"},Ge=["onUpdate:modelValue"],Re={class:"form-group"},qe=["onUpdate:modelValue"],Ee=["onClick"],Je={class:"section-divider"},Ke={class:"section-actions"},Qe={key:2,class:"field-selector"},We={class:"field-options"},Xe=["onClick"],Ye={class:"option-icon"},Ze={key:3,class:"optional-section"},De={key:0},et={class:"form-group-horizontal"},tt={class:"form-label-horizontal"},lt=["onUpdate:modelValue"],ot=["value"],st=["onUpdate:modelValue","type","placeholder"],nt=["onClick"],at={key:1,class:"custom-fields-section"},it={class:"form-group"},ut=["onUpdate:modelValue"],rt={class:"form-group"},dt=["onUpdate:modelValue"],ct=["onClick"],pt={__name:"BasicInfoForm",props:{data:{type:Object,default:()=>({})}},emits:["update"],setup(L,{emit:M}){const N=L,T=M,O=f(null),_=f(!0),S=f(!0),c=f([]),p=f([]),h=f([]),y=f([]),I=[{key:"wechat",label:"微信号"},{key:"website",label:"个人网站"},{key:"github",label:"GitHub"},{key:"custom",label:"自定义"}],V=[{key:"gender",label:"性别"},{key:"height",label:"身高"},{key:"weight",label:"体重"},{key:"politicalStatus",label:"政治面貌"},{key:"maritalStatus",label:"婚姻状况"},{key:"hometown",label:"籍贯"},{key:"ethnicity",label:"民族"},{key:"custom",label:"自定义"}],s=se({photo:"",name:"",phone:"",email:"",age:"",currentCity:"",address:"",currentStatus:"",intendedCity:"",expectedPosition:"",expectedSalary:"",wechat:"",website:"",github:"",gender:"",height:"",weight:"",politicalStatus:"",maritalStatus:"",hometown:"",ethnicity:"",customSocials:[],customInfos:[]});ne(()=>N.data,o=>{o&&(Object.assign(s,{photo:"",name:"",phone:"",email:"",age:"",currentCity:"",address:"",currentStatus:"",intendedCity:"",expectedPosition:"",expectedSalary:"",wechat:"",website:"",github:"",gender:"",height:"",weight:"",politicalStatus:"",maritalStatus:"",hometown:"",ethnicity:"",customSocials:[],customInfos:[],...o}),o.customSocials&&(h.value=[...o.customSocials]),o.customInfos&&(y.value=[...o.customInfos]),c.value=I.filter(e=>e.key==="custom"?!1:o[e.key]&&o[e.key].trim()!==""||c.value.includes(e.key)).map(e=>e.key),p.value=V.filter(e=>e.key==="custom"?!1:o[e.key]&&o[e.key].trim()!==""||p.value.includes(e.key)).map(e=>e.key))},{immediate:!0,deep:!0});const i=()=>{console.log("📝 BasicInfoForm - 发射更新事件:",{...s}),T("update",{...s})},H=()=>{var o;(o=O.value)==null||o.click()},j=o=>{const e=o.target.files[0];if(e){const l=new FileReader;l.onload=u=>{s.photo=u.target.result,i()},l.readAsDataURL(e)}},U=(o,e)=>o==="custom"?"+":e==="social"?c.value.includes(o)?"✓":"+":p.value.includes(o)?"✓":"+",G=o=>{o==="custom"?Q():R(o)},R=o=>{const e=c.value.indexOf(o);e>-1?(c.value.splice(e,1),s[o]=""):(c.value.push(o),s[o]=s[o]||""),i()},q=o=>{const e=c.value.indexOf(o);e>-1&&(c.value.splice(e,1),s[o]="",i())},w=()=>c.value.filter(o=>o!=="custom"),E=o=>{var e;return((e=I.find(l=>l.key===o))==null?void 0:e.label)||o},J=o=>["website","github"].includes(o)?"url":"text",K=o=>({wechat:"请输入微信号",website:"请输入个人网站链接",github:"请输入GitHub链接"})[o]||"",Q=()=>{if(h.value.length>=3){alert("最多只能自定义3个社交信息");return}h.value.push({id:Date.now(),label:"",value:""}),C()},W=o=>{h.value.splice(o,1),C()},C=()=>{s.customSocials=[...h.value],i()},X=o=>{o==="custom"?le():Y(o)},Y=o=>{const e=p.value.indexOf(o);e>-1?(p.value.splice(e,1),s[o]=""):(p.value.push(o),s[o]=s[o]||""),i()},Z=o=>{const e=p.value.indexOf(o);e>-1&&(p.value.splice(e,1),s[o]="",i())},F=()=>p.value.filter(o=>o!=="custom"),D=o=>{var e;return((e=V.find(l=>l.key===o))==null?void 0:e.label)||o},P=o=>["gender","politicalStatus","maritalStatus"].includes(o)?"select":"text",ee=o=>({gender:["男","女"],politicalStatus:["中共党员","中共预备党员","共青团员","民主党派","群众"],maritalStatus:["未婚","已婚","离异","丧偶"]})[o]||[],te=o=>({height:"如：175cm",weight:"如：65kg",hometown:"如：山东省济南市",ethnicity:"如：汉族"})[o]||"",le=()=>{if(y.value.length>=3){alert("最多只能自定义3个其他信息");return}y.value.push({id:Date.now(),label:"",value:""}),x()},oe=o=>{y.value.splice(o,1),x()},x=()=>{s.customInfos=[...y.value],i()};return(o,e)=>(a(),n("div",de,[e[31]||(e[31]=t("div",{class:"form-header"},[t("h4",{class:"form-title"},"基本信息"),t("p",{class:"form-desc"},"完善您的个人基本信息")],-1)),t("div",ce,[t("div",pe,[e[13]||(e[13]=t("label",{class:"form-label"},"照片",-1)),t("div",me,[t("div",{class:"photo-preview",onClick:H},[s.photo?(a(),n("img",{key:0,src:s.photo,alt:"照片",class:"photo-img"},null,8,ve)):(a(),n("div",he,[ae(ue,{name:"image",size:"lg"}),e[12]||(e[12]=t("span",null,"点击上传照片",-1))]))]),t("input",{ref_key:"fileInput",ref:O,type:"file",accept:"image/*",style:{display:"none"},onChange:j},null,544)])]),t("div",ye,[t("div",be,[e[14]||(e[14]=t("label",{class:"form-label"},"姓名",-1)),r(t("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>s.name=l),type:"text",class:"form-input",placeholder:"请输入您的姓名",onInput:i},null,544),[[d,s.name]])]),t("div",fe,[e[15]||(e[15]=t("label",{class:"form-label"},"年龄",-1)),r(t("input",{"onUpdate:modelValue":e[1]||(e[1]=l=>s.age=l),type:"number",class:"form-input",placeholder:"请输入年龄",onInput:i},null,544),[[d,s.age]])])]),t("div",ge,[e[16]||(e[16]=t("label",{class:"form-label"},"手机号",-1)),r(t("input",{"onUpdate:modelValue":e[2]||(e[2]=l=>s.phone=l),type:"tel",class:"form-input",placeholder:"请输入手机号",onInput:i},null,544),[[d,s.phone]])]),t("div",ke,[e[17]||(e[17]=t("label",{class:"form-label"},"邮箱",-1)),r(t("input",{"onUpdate:modelValue":e[3]||(e[3]=l=>s.email=l),type:"email",class:"form-input",placeholder:"请输入邮箱地址",onInput:i},null,544),[[d,s.email]])]),t("div",_e,[t("div",Se,[e[18]||(e[18]=t("label",{class:"form-label"},"所在城市",-1)),r(t("input",{"onUpdate:modelValue":e[4]||(e[4]=l=>s.currentCity=l),type:"text",class:"form-input",placeholder:"如：北京",onInput:i},null,544),[[d,s.currentCity]])]),t("div",Ce,[e[19]||(e[19]=t("label",{class:"form-label"},"意向城市",-1)),r(t("input",{"onUpdate:modelValue":e[5]||(e[5]=l=>s.intendedCity=l),type:"text",class:"form-input",placeholder:"如：北京",onInput:i},null,544),[[d,s.intendedCity]])])]),t("div",xe,[e[20]||(e[20]=t("label",{class:"form-label"},"居住地址",-1)),r(t("input",{"onUpdate:modelValue":e[6]||(e[6]=l=>s.address=l),type:"text",class:"form-input",placeholder:"如：北京市朝阳区***路***号**小区",onInput:i},null,544),[[d,s.address]])]),t("div",Ie,[e[21]||(e[21]=t("label",{class:"form-label"},"当前状态",-1)),r(t("input",{"onUpdate:modelValue":e[7]||(e[7]=l=>s.currentStatus=l),type:"text",class:"form-input",placeholder:"如：已离职-随时到岗",onInput:i},null,544),[[d,s.currentStatus]])]),t("div",Ve,[e[22]||(e[22]=t("label",{class:"form-label"},"期望岗位",-1)),r(t("input",{"onUpdate:modelValue":e[8]||(e[8]=l=>s.expectedPosition=l),type:"text",class:"form-input",placeholder:"如：销售总监",onInput:i},null,544),[[d,s.expectedPosition]])]),t("div",Oe,[e[23]||(e[23]=t("label",{class:"form-label"},"期望薪资",-1)),r(t("input",{"onUpdate:modelValue":e[9]||(e[9]=l=>s.expectedSalary=l),type:"text",class:"form-input",placeholder:"如：面议",onInput:i},null,544),[[d,s.expectedSalary]])]),t("div",Ue,[e[24]||(e[24]=t("h5",{class:"section-title"},"社交信息",-1)),t("div",we,[t("button",{type:"button",class:"collapse-btn",onClick:e[10]||(e[10]=l=>_.value=!_.value)},v(_.value?"收起":"展开"),1)])]),_.value?(a(),n("div",Fe,[t("div",Pe,[(a(),n(g,null,k(I,l=>t("button",{key:l.key,type:"button",class:B(["option-btn",{active:c.value.includes(l.key)&&l.key!=="custom","custom-btn":l.key==="custom"}]),onClick:$(u=>G(l.key),["stop"])},[t("span",Be,v(U(l.key,"social")),1),z(" "+v(l.label),1)],10,$e)),64))])])):b("",!0),c.value.length>0||h.value.length>0?(a(),n("div",ze,[w().length>0?(a(),n("div",Ae,[(a(!0),n(g,null,k(w(),l=>(a(),n("div",{key:l,class:"form-row"},[t("div",Le,[t("label",Me,v(E(l)),1),r(t("input",{"onUpdate:modelValue":u=>s[l]=u,type:J(l),class:"form-input",placeholder:K(l),onInput:i},null,40,Ne),[[A,s[l]]])]),t("button",{type:"button",class:"delete-field-btn",onClick:u=>q(l),title:"删除此字段"}," × ",8,Te)]))),128))])):b("",!0),h.value.length>0?(a(),n("div",He,[(a(!0),n(g,null,k(h.value,(l,u)=>(a(),n("div",{class:"form-row",key:l.id},[t("div",je,[e[25]||(e[25]=t("label",{class:"form-label"},"标签名称",-1)),r(t("input",{"onUpdate:modelValue":m=>l.label=m,type:"text",class:"form-input",placeholder:"如：微博",onInput:C},null,40,Ge),[[d,l.label]])]),t("div",Re,[e[26]||(e[26]=t("label",{class:"form-label"},"内容",-1)),r(t("input",{"onUpdate:modelValue":m=>l.value=m,type:"text",class:"form-input",placeholder:"请输入内容",onInput:C},null,40,qe),[[d,l.value]])]),t("button",{type:"button",class:"delete-field-btn",onClick:m=>W(u),title:"删除此字段"}," × ",8,Ee)]))),128))])):b("",!0)])):b("",!0),t("div",Je,[e[27]||(e[27]=t("h5",{class:"section-title"},"其他信息",-1)),t("div",Ke,[t("button",{type:"button",class:"collapse-btn",onClick:e[11]||(e[11]=l=>S.value=!S.value)},v(S.value?"收起":"展开"),1)])]),S.value?(a(),n("div",Qe,[t("div",We,[(a(),n(g,null,k(V,l=>t("button",{key:l.key,type:"button",class:B(["option-btn",{active:p.value.includes(l.key)&&l.key!=="custom","custom-btn":l.key==="custom"}]),onClick:$(u=>X(l.key),["stop"])},[t("span",Ye,v(U(l.key,"other")),1),z(" "+v(l.label),1)],10,Xe)),64))])])):b("",!0),p.value.length>0||y.value.length>0?(a(),n("div",Ze,[F().length>0?(a(),n("div",De,[(a(!0),n(g,null,k(F(),l=>(a(),n("div",{key:l,class:"form-row"},[t("div",et,[t("label",tt,v(D(l)),1),P(l)==="select"?r((a(),n("select",{key:0,"onUpdate:modelValue":u=>s[l]=u,class:"form-select",onChange:i},[e[28]||(e[28]=t("option",{value:""},"请选择",-1)),(a(!0),n(g,null,k(ee(l),u=>(a(),n("option",{key:u,value:u},v(u),9,ot))),128))],40,lt)),[[ie,s[l]]]):r((a(),n("input",{key:1,"onUpdate:modelValue":u=>s[l]=u,type:P(l),class:"form-input",placeholder:te(l),onInput:i},null,40,st)),[[A,s[l]]])]),t("button",{type:"button",class:"delete-field-btn",onClick:u=>Z(l),title:"删除此字段"}," × ",8,nt)]))),128))])):b("",!0),y.value.length>0?(a(),n("div",at,[(a(!0),n(g,null,k(y.value,(l,u)=>(a(),n("div",{class:"form-row",key:l.id},[t("div",it,[e[29]||(e[29]=t("label",{class:"form-label"},"标签名称",-1)),r(t("input",{"onUpdate:modelValue":m=>l.label=m,type:"text",class:"form-input",placeholder:"如：特长",onInput:x},null,40,ut),[[d,l.label]])]),t("div",rt,[e[30]||(e[30]=t("label",{class:"form-label"},"内容",-1)),r(t("input",{"onUpdate:modelValue":m=>l.value=m,type:"text",class:"form-input",placeholder:"请输入内容",onInput:x},null,40,dt),[[d,l.value]])]),t("button",{type:"button",class:"delete-field-btn",onClick:m=>oe(u),title:"删除此字段"}," × ",8,ct)]))),128))])):b("",!0)])):b("",!0)])]))}},yt=re(pt,[["__scopeId","data-v-e2813410"]]);export{yt as B};
