package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.dto.file.*;
import com.alan6.resume.service.IFileUploadRecordsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

/**
 * 文件管理控制器
 * 
 * 主要功能：
 * 1. 文件上传和下载管理
 * 2. 文件信息查询和列表展示
 * 3. 文件删除和版本控制
 * 4. 预签名URL生成
 * 5. 存储桶管理
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/file")
@RequiredArgsConstructor
@Tag(name = "文件管理", description = "文件上传、下载、管理相关接口")
public class FileController {

    private final IFileUploadRecordsService fileUploadRecordsService;

    /**
     * 文件上传接口
     * 
     * @param file 上传的文件
     * @param request 上传请求参数
     * @param httpRequest HTTP请求对象
     * @return 文件上传响应
     */
    @PostMapping("/upload")
    @Operation(summary = "文件上传", description = "支持多种文件类型上传到MinIO存储")
    public R<FileUploadResponse> uploadFile(
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "上传参数") @ModelAttribute FileUploadRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("收到文件上传请求，文件名: {}, 大小: {}, 类型: {}", 
                file.getOriginalFilename(), file.getSize(), request.getType());
        
        // 从请求中获取用户ID（实际项目中通过JWT token获取）
        Long userId = getUserIdFromRequest(httpRequest);
        
        FileUploadResponse response = fileUploadRecordsService.uploadFile(file, request, userId);
        
        log.info("文件上传成功，文件ID: {}", response.getFileId());
        return R.ok(response, "文件上传成功");
    }

    /**
     * 文件下载接口
     * 
     * @param fileId 文件ID
     * @param download 是否强制下载
     * @param filename 自定义文件名
     * @param httpRequest HTTP请求对象
     * @return 文件流响应
     */
    @GetMapping("/download/{fileId}")
    @Operation(summary = "文件下载", description = "根据文件ID下载文件")
    public ResponseEntity<byte[]> downloadFile(
            @Parameter(description = "文件ID") @PathVariable String fileId,
            @Parameter(description = "是否强制下载") @RequestParam(defaultValue = "false") Boolean download,
            @Parameter(description = "自定义文件名") @RequestParam(required = false) String filename,
            HttpServletRequest httpRequest) {
        
        log.debug("收到文件下载请求，文件ID: {}, 强制下载: {}", fileId, download);
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        // 先获取文件信息
        FileInfoResponse fileInfo = fileUploadRecordsService.getFileInfo(fileId, userId);
        
        // 下载文件数据
        byte[] fileData = fileUploadRecordsService.downloadFile(fileId, download, filename, userId);
        
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        
        // 设置文件名
        String downloadFilename = filename != null ? filename : fileInfo.getOriginalName();
        if (download) {
            headers.setContentDispositionFormData("attachment", downloadFilename);
        } else {
            headers.setContentDispositionFormData("inline", downloadFilename);
        }
        
        // 设置内容类型
        headers.setContentType(MediaType.parseMediaType(fileInfo.getFileType()));
        headers.setContentLength(fileData.length);
        
        log.debug("文件下载成功，文件ID: {}, 大小: {}", fileId, fileData.length);
        return ResponseEntity.ok().headers(headers).body(fileData);
    }

    /**
     * 获取文件信息接口
     * 
     * @param fileId 文件ID
     * @param httpRequest HTTP请求对象
     * @return 文件信息响应
     */
    @GetMapping("/{fileId}")
    @Operation(summary = "获取文件信息", description = "根据文件ID获取详细信息")
    public R<FileInfoResponse> getFileInfo(
            @Parameter(description = "文件ID") @PathVariable String fileId,
            HttpServletRequest httpRequest) {
        
        log.debug("收到获取文件信息请求，文件ID: {}", fileId);
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        FileInfoResponse response = fileUploadRecordsService.getFileInfo(fileId, userId);
        
        return R.ok(response, "获取文件信息成功");
    }

    /**
     * 删除文件接口
     * 
     * @param fileId 文件ID
     * @param permanent 是否永久删除
     * @param httpRequest HTTP请求对象
     * @return 删除结果
     */
    @DeleteMapping("/{fileId}")
    @Operation(summary = "删除文件", description = "删除指定文件，支持软删除和永久删除")
    public R<Boolean> deleteFile(
            @Parameter(description = "文件ID") @PathVariable String fileId,
            @Parameter(description = "是否永久删除") @RequestParam(defaultValue = "false") Boolean permanent,
            HttpServletRequest httpRequest) {
        
        log.info("收到删除文件请求，文件ID: {}, 永久删除: {}", fileId, permanent);
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        boolean result = fileUploadRecordsService.deleteFile(fileId, permanent, userId);
        
        String message = permanent ? "文件永久删除成功" : "文件删除成功";
        log.info("文件删除完成，文件ID: {}, 结果: {}", fileId, result);
        
        return R.ok(result, message);
    }

    /**
     * 获取文件列表接口
     * 
     * @param page 页码
     * @param size 每页大小
     * @param type 文件类型筛选
     * @param bucketName 存储桶筛选
     * @param fileType MIME类型筛选
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param keyword 关键词搜索
     * @param httpRequest HTTP请求对象
     * @return 文件列表响应
     */
    @GetMapping("/list")
    @Operation(summary = "获取文件列表", description = "分页查询文件列表，支持多种筛选条件")
    public R<FileListResponse> getFileList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "文件类型") @RequestParam(required = false) String type,
            @Parameter(description = "存储桶名称") @RequestParam(required = false) String bucketName,
            @Parameter(description = "MIME类型") @RequestParam(required = false) String fileType,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword,
            HttpServletRequest httpRequest) {
        
        log.debug("收到获取文件列表请求，页码: {}, 大小: {}, 类型: {}", page, size, type);
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        FileListResponse response = fileUploadRecordsService.getFileList(
                type, bucketName, fileType, startTime, endTime, keyword, page, size, userId);
        
        return R.ok(response, "获取文件列表成功");
    }

    /**
     * 生成预签名URL接口
     * 
     * @param request 预签名URL请求
     * @param httpRequest HTTP请求对象
     * @return 预签名URL响应
     */
    @PostMapping("/presigned-url")
    @Operation(summary = "生成预签名URL", description = "为文件生成临时访问URL")
    public R<PresignedUrlResponse> generatePresignedUrl(
            @Parameter(description = "预签名URL请求") @RequestBody PresignedUrlRequest request,
            HttpServletRequest httpRequest) {
        
        log.debug("收到生成预签名URL请求，文件ID: {}, 方法: {}", request.getFileId(), request.getMethod());
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        PresignedUrlResponse response = fileUploadRecordsService.generatePresignedUrl(request, userId);
        
        return R.ok(response, "预签名URL生成成功");
    }

    /**
     * 获取文件版本列表接口
     * 
     * @param fileId 文件ID
     * @param page 页码
     * @param size 每页大小
     * @param httpRequest HTTP请求对象
     * @return 文件版本列表响应
     */
    @GetMapping("/{fileId}/versions")
    @Operation(summary = "获取文件版本列表", description = "获取文件的历史版本信息")
    public R<FileVersionListResponse> getFileVersions(
            @Parameter(description = "文件ID") @PathVariable String fileId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest httpRequest) {
        
        log.debug("收到获取文件版本列表请求，文件ID: {}", fileId);
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        FileVersionListResponse response = fileUploadRecordsService.getFileVersions(fileId, page, size, userId);
        
        return R.ok(response, "获取文件版本列表成功");
    }

    /**
     * 恢复文件版本接口
     * 
     * @param fileId 文件ID
     * @param versionId 版本ID
     * @param httpRequest HTTP请求对象
     * @return 恢复结果
     */
    @PostMapping("/{fileId}/versions/{versionId}/restore")
    @Operation(summary = "恢复文件版本", description = "将文件恢复到指定版本")
    public R<FileVersionRestoreResponse> restoreFileVersion(
            @Parameter(description = "文件ID") @PathVariable String fileId,
            @Parameter(description = "版本ID") @PathVariable String versionId,
            HttpServletRequest httpRequest) {
        
        log.info("收到恢复文件版本请求，文件ID: {}, 版本ID: {}", fileId, versionId);
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        FileVersionRestoreResponse response = fileUploadRecordsService.restoreFileVersion(fileId, versionId, userId);
        
        return R.ok(response, "文件版本恢复成功");
    }

    /**
     * 获取存储桶列表接口
     * 
     * @param httpRequest HTTP请求对象
     * @return 存储桶列表响应
     */
    @GetMapping("/buckets")
    @Operation(summary = "获取存储桶列表", description = "获取MinIO中的所有存储桶信息")
    public R<BucketListResponse> getBucketList(HttpServletRequest httpRequest) {
        
        log.debug("收到获取存储桶列表请求");
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        BucketListResponse response = fileUploadRecordsService.getBucketList(userId);
        
        return R.ok(response, "获取存储桶列表成功");
    }

    /**
     * 创建存储桶接口
     * 
     * @param request 创建存储桶请求
     * @param httpRequest HTTP请求对象
     * @return 创建结果
     */
    @PostMapping("/buckets")
    @Operation(summary = "创建存储桶", description = "在MinIO中创建新的存储桶")
    public R<BucketCreateResponse> createBucket(
            @Parameter(description = "创建存储桶请求") @RequestBody BucketCreateRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("收到创建存储桶请求，名称: {}", request.getName());
        
        Long userId = getUserIdFromRequest(httpRequest);
        
        BucketCreateResponse response = fileUploadRecordsService.createBucket(request, userId);
        
        return R.ok(response, "存储桶创建成功");
    }

    /**
     * 从HTTP请求中获取用户ID
     * 实际项目中应该从JWT token中解析
     * 
     * @param request HTTP请求对象
     * @return 用户ID
     */
    private Long getUserIdFromRequest(HttpServletRequest request) {
        // TODO: 实际项目中应该从JWT token中解析用户ID
        // 这里暂时使用模拟数据
        String userIdHeader = request.getHeader("X-User-Id");
        if (userIdHeader != null) {
            try {
                return Long.parseLong(userIdHeader);
            } catch (NumberFormatException e) {
                log.warn("无效的用户ID格式: {}", userIdHeader);
            }
        }
        
        // 默认返回测试用户ID
        return 1L;
    }
}
