<template>
  <div class="dialog-overlay" @click="$emit('cancel')">
    <div class="dialog-content" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">创建自定义模块</h3>
        <button class="close-btn" @click="$emit('cancel')">
          <Icon name="close" size="sm" />
        </button>
      </div>
      
      <div class="dialog-body">
        <div class="form-group">
          <label class="form-label">模块名称</label>
          <input
            ref="nameInput"
            v-model="moduleName"
            type="text"
            class="form-input"
            placeholder="请输入自定义模块名称"
            maxlength="20"
            @keyup.enter="handleConfirm"
            @keyup.escape="$emit('cancel')"
          />
          <p class="form-hint">最多20个字符</p>
        </div>
        
        <div class="module-info">
          <div class="info-item">
            <Icon name="info" size="sm" class="info-icon" />
            <span class="info-text">自定义模块包含：名称、角色、时间范围、详细内容</span>
          </div>
          <div class="info-item">
            <Icon name="info" size="sm" class="info-icon" />
            <span class="info-text">最多可创建3个自定义模块</span>
          </div>
        </div>
      </div>
      
      <div class="dialog-footer">
        <button class="cancel-btn" @click="$emit('cancel')">
          取消
        </button>
        <button 
          class="confirm-btn" 
          @click="handleConfirm"
          :disabled="!moduleName.trim()"
        >
          确定
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'
import Icon from '../../common/Icon.vue'

const emit = defineEmits(['confirm', 'cancel'])

const moduleName = ref('')
const nameInput = ref(null)

onMounted(() => {
  nextTick(() => {
    nameInput.value?.focus()
  })
})

const handleConfirm = () => {
  if (moduleName.value.trim()) {
    emit('confirm', moduleName.value.trim())
  }
}
</script>

<style scoped>
.dialog-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.dialog-content {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4;
}

.dialog-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.dialog-title {
  @apply text-lg font-semibold text-gray-900;
}

.close-btn {
  @apply p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200;
}

.dialog-body {
  @apply p-6 space-y-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.form-hint {
  @apply text-xs text-gray-500;
}

.module-info {
  @apply space-y-2 p-3 bg-blue-50 rounded-md;
}

.info-item {
  @apply flex items-center gap-2;
}

.info-icon {
  @apply text-blue-500 flex-shrink-0;
}

.info-text {
  @apply text-sm text-blue-700;
}

.dialog-footer {
  @apply flex justify-end gap-3 px-6 py-4 border-t border-gray-200;
}

.cancel-btn {
  @apply px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200;
}

.confirm-btn {
  @apply px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
}
</style> 