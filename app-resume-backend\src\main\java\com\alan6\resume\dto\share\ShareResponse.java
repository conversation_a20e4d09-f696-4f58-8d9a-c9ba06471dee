package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分享响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareResponse", description = "分享响应")
public class ShareResponse {

    /**
     * 分享记录ID
     */
    @Schema(description = "分享记录ID", example = "6001")
    private Long id;

    /**
     * 资源类型（resume:简历，template:模板）
     */
    @Schema(description = "资源类型", example = "resume")
    private String resourceType;

    /**
     * 资源ID
     */
    @Schema(description = "资源ID", example = "2001")
    private Long resourceId;

    /**
     * 分享码
     */
    @Schema(description = "分享码", example = "abc123def456")
    private String shareCode;

    /**
     * 分享链接
     */
    @Schema(description = "分享链接", example = "https://resume.com/share/abc123def456")
    private String shareUrl;

    /**
     * 二维码URL
     */
    @Schema(description = "二维码URL", example = "https://cdn.example.com/qr/abc123def456.png")
    private String qrCodeUrl;

    /**
     * 分享类型
     */
    @Schema(description = "分享类型", example = "1")
    private Byte shareType;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间", example = "2024-12-31 23:59:59")
    private LocalDateTime expireTime;

    /**
     * 是否允许下载
     */
    @Schema(description = "是否允许下载", example = "true")
    private Boolean allowDownload;

    /**
     * 访问次数限制
     */
    @Schema(description = "访问次数限制", example = "100")
    private Integer viewLimit;

    /**
     * 查看次数
     */
    @Schema(description = "查看次数", example = "15")
    private Integer viewCount;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数", example = "3")
    private Integer downloadCount;

    /**
     * 分享描述
     */
    @Schema(description = "分享描述", example = "我的简历分享")
    private String description;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-01 10:00:00")
    private LocalDateTime createTime;
} 