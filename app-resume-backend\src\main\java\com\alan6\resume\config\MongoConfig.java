package com.alan6.resume.config;

import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * MongoDB配置类
 * 
 * @description 配置MongoDB连接和相关设置
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableMongoRepositories(basePackages = "com.alan6.resume.repository")
public class MongoConfig extends AbstractMongoClientConfiguration {

    /**
     * 数据库IP地址
     */
    @Value("${spring.data.mongodb.host:**************}")
    private String host;

    /**
     * 数据库端口
     */
    @Value("${spring.data.mongodb.port:27017}")
    private int port;

    /**
     * 数据库名称
     */
    @Value("${spring.data.mongodb.database:resume}")
    private String databaseName;

    /**
     * 认证数据库名称
     */
    @Value("${spring.data.mongodb.authentication-database:admin}")
    private String authDatabase;

    /**
     * 用户名
     */
    @Value("${spring.data.mongodb.username:admin}")
    private String username;

    /**
     * 密码
     */
    @Value("${spring.data.mongodb.password:123456}")
    private String password;

    /**
     * 获取数据库名称
     * 
     * @return 数据库名称
     */
    @Override
    protected String getDatabaseName() {
        return databaseName;
    }

    /**
     * 创建MongoDB客户端
     * 
     * @return MongoDB客户端实例
     */
    @Override
    public MongoClient mongoClient() {
        MongoCredential credential = MongoCredential.createCredential(
                username, authDatabase, password.toCharArray());

        ServerAddress serverAddress = new ServerAddress(host, port);

        MongoClientSettings settings = MongoClientSettings.builder()
                .applyToConnectionPoolSettings(pool -> pool
                        .maxSize(50) // 最大连接数
                        .minSize(5)  // 最小连接数
                        .maxWaitTime(1000, TimeUnit.MILLISECONDS)
                )
                .applyToSocketSettings(socket -> socket
                        .connectTimeout(10, TimeUnit.SECONDS)
                        .readTimeout(10, TimeUnit.SECONDS)
                )
                .credential(credential)
                .applyToClusterSettings(cluster -> cluster
                        .hosts(Collections.singletonList(serverAddress))
                )
                .build();
        return MongoClients.create(settings);
    }

    /**
     * 创建MongoTemplate实例
     * 
     * @return MongoTemplate实例，用于MongoDB操作
     */
    @Bean
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoClient(), getDatabaseName());
    }

    /**
     * 自动创建索引配置
     * 
     * @return 是否自动创建索引
     */
    @Override
    protected boolean autoIndexCreation() {
        return true;
    }
} 