package com.alan6.resume.service;

import com.alan6.resume.dto.system.SystemConfigRequest;
import com.alan6.resume.dto.system.SystemConfigResponse;
import com.alan6.resume.entity.SystemConfigs;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface ISystemConfigsService extends IService<SystemConfigs> {

    /**
     * 获取系统配置列表（分页）
     *
     * @param group 配置分组（可选）
     * @param page  页码
     * @param size  每页数量
     * @return 分页结果
     */
    Page<SystemConfigResponse> getConfigList(String group, Integer page, Integer size);

    /**
     * 根据配置键获取配置
     *
     * @param configKey 配置键
     * @return 配置响应
     */
    SystemConfigResponse getConfigByKey(String configKey);

    /**
     * 更新配置项
     *
     * @param configKey 配置键
     * @param request   配置请求
     * @return 是否成功
     */
    boolean updateConfig(String configKey, SystemConfigRequest request);

    /**
     * 创建配置项
     *
     * @param request 配置请求
     * @return 配置响应
     */
    SystemConfigResponse createConfig(SystemConfigRequest request);

    /**
     * 获取配置分组列表
     *
     * @return 分组列表
     */
    List<String> getConfigGroups();

    /**
     * 获取前端配置（公开配置）
     *
     * @return 前端配置映射
     */
    Map<String, Object> getFrontendConfig();

    /**
     * 根据配置键获取配置值
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 根据配置键获取配置值并转换为指定类型
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @param type         目标类型
     * @param <T>          泛型类型
     * @return 转换后的值
     */
    <T> T getConfigValue(String configKey, T defaultValue, Class<T> type);
}
