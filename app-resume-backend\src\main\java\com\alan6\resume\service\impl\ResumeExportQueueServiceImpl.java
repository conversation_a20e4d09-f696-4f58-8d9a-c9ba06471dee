package com.alan6.resume.service.impl;

import com.alan6.resume.entity.ResumeExportQueue;
import com.alan6.resume.mapper.ResumeExportQueueMapper;
import com.alan6.resume.service.IResumeExportQueueService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 简历导出队列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Service
public class ResumeExportQueueServiceImpl extends ServiceImpl<ResumeExportQueueMapper, ResumeExportQueue> implements IResumeExportQueueService {

    @Autowired
    private ResumeExportQueueMapper resumeExportQueueMapper;

    @Override
    public String submitExportJob(Long userId, Long resumeId, String exportFormat) {
        // 生成唯一任务ID
        String jobId = "export_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        
        // 创建导出任务
        ResumeExportQueue exportJob = new ResumeExportQueue();
        exportJob.setJobId(jobId);
        exportJob.setUserId(userId);
        exportJob.setResumeId(resumeId);
        exportJob.setExportFormat(exportFormat);
        exportJob.setPriority(5); // 默认优先级
        exportJob.setStatus("queued");
        exportJob.setAttempts(0);
        exportJob.setMaxAttempts(3);
        exportJob.setCreateTime(LocalDateTime.now());
        exportJob.setUpdateTime(LocalDateTime.now());
        
        // 保存到数据库
        this.save(exportJob);
        
        log.info("提交导出任务成功，jobId: {}, userId: {}, resumeId: {}, format: {}", 
                jobId, userId, resumeId, exportFormat);
        
        return jobId;
    }

    @Override
    public ResumeExportQueue getByJobId(String jobId) {
        return resumeExportQueueMapper.selectByJobId(jobId);
    }

    @Override
    public void updateStatus(String jobId, String status) {
        int result = resumeExportQueueMapper.updateStatusByJobId(jobId, status);
        if (result > 0) {
            log.info("更新导出任务状态成功，jobId: {}, status: {}", jobId, status);
        } else {
            log.warn("更新导出任务状态失败，jobId: {}, status: {}", jobId, status);
        }
    }

    @Override
    public void updateDownloadInfo(String jobId, String downloadUrl, Long fileSize) {
        int result = resumeExportQueueMapper.updateDownloadInfoByJobId(jobId, downloadUrl, fileSize);
        if (result > 0) {
            log.info("更新导出任务下载信息成功，jobId: {}, downloadUrl: {}, fileSize: {}", 
                    jobId, downloadUrl, fileSize);
        } else {
            log.warn("更新导出任务下载信息失败，jobId: {}", jobId);
        }
    }

    @Override
    public void updateError(String jobId, String errorMessage) {
        int result = resumeExportQueueMapper.updateErrorByJobId(jobId, errorMessage);
        if (result > 0) {
            log.error("更新导出任务错误信息，jobId: {}, error: {}", jobId, errorMessage);
        } else {
            log.error("更新导出任务错误信息失败，jobId: {}", jobId);
        }
    }

    @Override
    public List<ResumeExportQueue> getPendingJobs(int limit) {
        return resumeExportQueueMapper.selectPendingJobs(limit);
    }

    @Override
    public List<ResumeExportQueue> getByUserId(Long userId) {
        return resumeExportQueueMapper.selectByUserId(userId);
    }

} 