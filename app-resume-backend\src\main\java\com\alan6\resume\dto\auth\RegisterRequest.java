package com.alan6.resume.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @description 用户注册请求的数据传输对象 (DTO)
 * @date 2024/06/29
 * <p>
 * 该类封装了用户注册时从客户端传递到后端的所有必要信息。
 * 每个字段都配备了详细的Swagger文档注解和JSR 303校验注解，
 * 以确保数据的规范性和安全性。
 * </p>
 */
@Data
@Schema(description = "用户注册请求参数模型")
public class RegisterRequest {

    /**
     * 注册类型
     * 1: 手机号+密码注册
     * 2: 邮箱+密码注册 (未来扩展)
     * 3: 手机号+验证码注册 (未来扩展)
     * etc.
     * <p>
     * 目前仅支持手机号+密码注册，但设计上为未来扩展留出了空间。
     * </p>
     */
    @Schema(description = "注册类型, 1:手机号+密码注册", example = "1", required = true)
    private Integer registerType = 1;

    /**
     * 用户手机号
     * <p>
     * 必须是符合中国大陆手机号格式的11位数字。
     * 使用正则表达式进行严格格式校验。
     * </p>
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "13800138000", required = true)
    private String phone;

    /**
     * 用户设置的密码
     * <p>
     * 密码长度要求在6到20个字符之间，以保证基本的密码强度。
     * 在传输和存储过程中，密码应始终被加密处理。
     * </p>
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    @Schema(description = "登录密码", example = "password123", required = true)
    private String password;

    /**
     * 短信或邮箱验证码
     * <p>
     * 用于验证用户手机号或邮箱的真实性。
     * 通常是6位数字。
     * </p>
     */
    @NotBlank(message = "验证码不能为空")
    @Size(min = 6, max = 6, message = "验证码必须是6位")
    @Schema(description = "短信验证码", example = "123456", required = true)
    private String verificationCode;

    /**
     * 是否同意用户协议和隐私条款
     * <p>
     * 这是用户注册的法律前提，必须为true才允许注册。
     * </p>
     */
    @AssertTrue(message = "必须同意用户协议和隐私条款")
    @Schema(description = "是否同意用户协议", example = "true", required = true)
    private Boolean agreeTerms;

    /**
     * 注册平台
     * <p>
     * 记录用户注册的来源平台，如 web, ios, android, wechat_mp, etc.
     * 便于进行数据统计和平台适配。
     * </p>
     */
    @NotBlank(message = "注册平台不能为空")
    @Schema(description = "注册平台", example = "web", required = true)
    private String platform;
} 