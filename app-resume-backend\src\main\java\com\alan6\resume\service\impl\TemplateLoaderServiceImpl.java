package com.alan6.resume.service.impl;

import com.alan6.resume.dto.template.TemplateLoadRequest;
import com.alan6.resume.dto.template.TemplateLoadResponse;
import com.alan6.resume.entity.ResumeTemplateContent;
import com.alan6.resume.entity.ResumeTemplates;
import com.alan6.resume.service.IResumeTemplateContentService;
import com.alan6.resume.service.IResumeTemplatesService;
import com.alan6.resume.service.ITemplateCacheService;
import com.alan6.resume.service.ITemplateFileService;
import com.alan6.resume.service.ITemplateLoaderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 模板加载服务实现类
 * 
 * @description 实现新的模板加载逻辑：
 *              1. 从数据库加载模板基本信息
 *              2. 从MinIO加载Vue文件内容
 *              3. 从数据库加载JSON默认内容
 *              4. 支持三层缓存：Redis缓存 -> MinIO对象存储 -> 本地文件系统
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateLoaderServiceImpl implements ITemplateLoaderService {

    /**
     * 模板缓存服务
     */
    private final ITemplateCacheService templateCacheService;
    
    /**
     * 模板文件服务
     */
    private final ITemplateFileService templateFileService;
    
    /**
     * 模板基本信息服务
     */
    private final IResumeTemplatesService resumeTemplatesService;
    
    /**
     * 模板内容服务
     */
    private final IResumeTemplateContentService templateContentService;

    /**
     * 加载模板
     * 
     * @description 实现新的模板加载逻辑：
     *              1. 验证请求参数
     *              2. 检查Redis缓存
     *              3. 从数据库加载模板基本信息
     *              4. 从MinIO加载Vue文件内容
     *              5. 从数据库加载JSON默认内容
     *              6. 组装完整响应并缓存
     * @param request 模板加载请求
     * @return 模板加载响应，包含Vue文件内容和JSON默认内容
     */
    @Override
    public TemplateLoadResponse loadTemplate(TemplateLoadRequest request) {
        log.info("开始加载模板，请求参数: {}", request);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 参数验证
            validateRequest(request);
            
            // 2. 尝试从Redis缓存加载
            if (!request.getForceRefresh()) {
                TemplateLoadResponse cachedResponse = loadFromCache(request);
                if (cachedResponse != null) {
                    log.info("从Redis缓存加载模板成功，模板ID: {}", request.getTemplateId());
                    cachedResponse.setLoadTime(System.currentTimeMillis() - startTime);
                    return cachedResponse;
                }
            }
            
            // 3. 从数据库和存储系统加载模板
            TemplateLoadResponse response = loadTemplateFromStorage(request);
            if (response != null) {
                // 4. 缓存到Redis
                cacheTemplate(request, response);
                
                response.setLoadTime(System.currentTimeMillis() - startTime);
                log.info("模板加载成功，模板ID: {}, 耗时: {}ms", 
                        request.getTemplateId(), response.getLoadTime());
                return response;
            }
            
            // 所有存储层都没有找到模板
            throw new RuntimeException("模板不存在，模板ID: " + request.getTemplateId());
            
        } catch (Exception e) {
            log.error("加载模板失败，模板ID: {}, 错误: {}", request.getTemplateId(), e.getMessage(), e);
            throw new RuntimeException("加载模板失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量预热模板
     * 
     * @description 批量加载模板到Redis缓存中，提升访问性能
     * @param templateIds 模板ID数组
     * @return 预热成功的模板数量
     */
    @Override
    public int preloadTemplates(Long[] templateIds) {
        log.info("开始批量预热模板，模板数量: {}", templateIds.length);
        
        int successCount = 0;
        
        for (Long templateId : templateIds) {
            try {
                TemplateLoadRequest request = new TemplateLoadRequest();
                request.setTemplateId(templateId);
                request.setForceRefresh(true);
                
                loadTemplate(request);
                successCount++;
                
            } catch (Exception e) {
                log.warn("预热模板失败，模板ID: {}, 错误: {}", templateId, e.getMessage());
            }
        }
        
        log.info("批量预热模板完成，成功: {}, 总数: {}", successCount, templateIds.length);
        return successCount;
    }

    /**
     * 刷新模板缓存
     * 
     * @description 清除指定模板的Redis缓存，强制下次访问时重新加载
     * @param templateId 模板ID
     * @return 是否刷新成功
     */
    @Override
    public boolean refreshTemplateCache(Long templateId) {
        log.info("开始刷新模板缓存，模板ID: {}", templateId);
        
        try {
            // 删除缓存
            boolean deleted = templateCacheService.deleteTemplateFromCache(templateId);
            
            if (deleted) {
                log.info("刷新模板缓存成功，模板ID: {}", templateId);
            } else {
                log.warn("刷新模板缓存失败，缓存可能不存在，模板ID: {}", templateId);
            }
            
            return deleted;
            
        } catch (Exception e) {
            log.error("刷新模板缓存异常，模板ID: {}, 错误: {}", templateId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查模板可用性
     * 
     * @description 检查模板在各个存储层的可用性状态
     * @param templateId 模板ID
     * @return 可用性报告
     */
    @Override
    public String checkTemplateAvailability(Long templateId) {
        log.info("开始检查模板可用性，模板ID: {}", templateId);
        
        try {
            StringBuilder report = new StringBuilder();
            report.append("模板ID: ").append(templateId).append("\n");
            
            // 检查Redis缓存
            boolean cachedAvailable = templateCacheService.isTemplateExistsInCache(templateId);
            report.append("Redis缓存: ").append(cachedAvailable ? "可用" : "不可用").append("\n");
            
            // 检查数据库模板记录
            boolean dbTemplateAvailable = checkDatabaseTemplateAvailability(templateId);
            report.append("数据库模板: ").append(dbTemplateAvailable ? "可用" : "不可用").append("\n");
            
            // 检查MinIO Vue文件
            boolean minioVueAvailable = checkMinIOVueFileAvailability(templateId);
            report.append("MinIO Vue文件: ").append(minioVueAvailable ? "可用" : "不可用").append("\n");
            
            // 检查数据库内容
            boolean dbContentAvailable = checkDatabaseContentAvailability(templateId);
            report.append("数据库内容: ").append(dbContentAvailable ? "可用" : "不可用").append("\n");
            
            // 整体可用性
            boolean overallAvailable = cachedAvailable || 
                    (dbTemplateAvailable && (minioVueAvailable || dbContentAvailable));
            report.append("整体可用性: ").append(overallAvailable ? "可用" : "不可用");
            
            log.info("模板可用性检查完成，模板ID: {}, 整体可用: {}", templateId, overallAvailable);
            return report.toString();
            
        } catch (Exception e) {
            log.error("检查模板可用性异常，模板ID: {}, 错误: {}", templateId, e.getMessage(), e);
            return "检查失败: " + e.getMessage();
        }
    }

    // ================================
    // 私有辅助方法
    // ================================

    /**
     * 验证请求参数
     * 
     * @description 验证模板加载请求的参数有效性
     * @param request 模板加载请求
     */
    private void validateRequest(TemplateLoadRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("模板加载请求不能为空");
        }
        
        if (request.getTemplateId() == null && !StringUtils.hasText(request.getTemplateCode())) {
            throw new IllegalArgumentException("模板ID或模板代码至少需要提供一个");
        }
        
        log.debug("请求参数验证通过，模板ID: {}, 模板代码: {}", 
                request.getTemplateId(), request.getTemplateCode());
    }

    /**
     * 从缓存加载模板
     * 
     * @description 尝试从Redis缓存中加载模板
     * @param request 模板加载请求
     * @return 缓存的模板响应，如果不存在则返回null
     */
    private TemplateLoadResponse loadFromCache(TemplateLoadRequest request) {
        try {
            Long templateId = request.getTemplateId();
            
            // 如果只有模板代码，需要先查询模板ID
            if (templateId == null && StringUtils.hasText(request.getTemplateCode())) {
                ResumeTemplates template = resumeTemplatesService.lambdaQuery()
                        .eq(ResumeTemplates::getTemplateCode, request.getTemplateCode())
                        .eq(ResumeTemplates::getIsDeleted, 0)
                        .one();
                
                if (template != null) {
                    templateId = template.getId();
                    request.setTemplateId(templateId); // 设置到请求中，便于后续使用
                }
            }
            
            if (templateId == null) {
                log.debug("无法确定模板ID，跳过缓存查询");
                return null;
            }
            
                         // 从缓存获取模板
             String cachedContent = templateCacheService.getTemplateFromCache(templateId);
             
             if (StringUtils.hasText(cachedContent)) {
                 log.debug("从缓存获取模板成功，模板ID: {}", templateId);
                 // 这里需要将缓存的字符串转换为TemplateLoadResponse对象
                 // 简化处理，返回null让其从数据库加载
                 return null;
             }
            
            log.debug("缓存中未找到模板，模板ID: {}", templateId);
            return null;
            
        } catch (Exception e) {
            log.warn("从缓存加载模板失败，错误: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从存储系统加载模板
     * 
     * @description 从数据库和MinIO加载完整的模板信息
     * @param request 模板加载请求
     * @return 模板加载响应
     */
    private TemplateLoadResponse loadTemplateFromStorage(TemplateLoadRequest request) {
        try {
            // 1. 从数据库加载模板基本信息
            ResumeTemplates template = loadTemplateFromDatabase(request);
            if (template == null) {
                log.warn("数据库中未找到模板，模板ID: {}, 模板代码: {}", 
                        request.getTemplateId(), request.getTemplateCode());
                return null;
            }
            
            // 2. 加载Vue文件内容
            String vueContent = loadVueFileContent(template);
            
            // 3. 加载JSON默认内容
            String jsonContent = loadJsonDefaultContent(template);
            
            // 4. 构建响应
            return createTemplateLoadResponse(template, vueContent, jsonContent);
            
        } catch (Exception e) {
            log.error("从存储系统加载模板失败，错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从数据库加载模板基本信息
     * 
     * @description 根据模板ID或模板代码从数据库加载模板记录
     * @param request 模板加载请求
     * @return 模板实体对象
     */
    private ResumeTemplates loadTemplateFromDatabase(TemplateLoadRequest request) {
        try {
            ResumeTemplates template = null;
            
            // 优先使用模板ID查询
            if (request.getTemplateId() != null) {
                template = resumeTemplatesService.lambdaQuery()
                        .eq(ResumeTemplates::getId, request.getTemplateId())
                        .eq(ResumeTemplates::getIsDeleted, 0)
                        .eq(ResumeTemplates::getStatus, 1)
                        .one();
            }
            
            // 如果ID查询失败，尝试使用模板代码查询
            if (template == null && StringUtils.hasText(request.getTemplateCode())) {
                template = resumeTemplatesService.lambdaQuery()
                        .eq(ResumeTemplates::getTemplateCode, request.getTemplateCode())
                        .eq(ResumeTemplates::getIsDeleted, 0)
                        .eq(ResumeTemplates::getStatus, 1)
                        .one();
            }
            
            if (template != null) {
                log.debug("从数据库加载模板成功，模板ID: {}, 模板代码: {}", 
                        template.getId(), template.getTemplateCode());
                
                // 更新请求中的模板ID（如果之前为空）
                if (request.getTemplateId() == null) {
                    request.setTemplateId(template.getId());
                }
            }
            
            return template;
            
        } catch (Exception e) {
            log.error("从数据库加载模板失败，错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 加载Vue文件内容
     * 
     * @description 从MinIO或本地文件系统加载Vue文件内容
     * @param template 模板实体对象
     * @return Vue文件内容字符串
     */
    private String loadVueFileContent(ResumeTemplates template) {
        try {
            String vueContent = null;
            
            // 优先从MinIO加载
            if (StringUtils.hasText(template.getVueFilePath())) {
                vueContent = templateFileService.loadFromMinIO(template);
                if (vueContent != null) {
                    log.debug("从MinIO加载Vue文件成功，模板ID: {}", template.getId());
                    return vueContent;
                }
            }
            
            // 如果MinIO加载失败，尝试从本地文件系统加载
            if (StringUtils.hasText(template.getLocalFilePath())) {
                vueContent = templateFileService.loadFromLocal(template);
                if (vueContent != null) {
                    log.debug("从本地文件系统加载Vue文件成功，模板ID: {}", template.getId());
                    return vueContent;
                }
            }
            
            log.warn("无法加载Vue文件内容，模板ID: {}", template.getId());
            return null;
            
        } catch (Exception e) {
            log.error("加载Vue文件内容失败，模板ID: {}, 错误: {}", template.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 加载JSON默认内容
     * 
     * @description 从数据库加载模板的默认JSON内容
     * @param template 模板实体对象
     * @return JSON内容字符串
     */
    private String loadJsonDefaultContent(ResumeTemplates template) {
        try {
            String jsonContent = null;
            
            // 如果模板关联了内容ID，从数据库加载
            if (template.getContentId() != null) {
                ResumeTemplateContent templateContent = templateContentService.getById(template.getContentId());
                if (templateContent != null && templateContent.isEnabled()) {
                    jsonContent = templateContent.getContentDataAsString();
                    log.debug("从数据库加载JSON内容成功，模板ID: {}, 内容ID: {}", 
                            template.getId(), template.getContentId());
                    return jsonContent;
                }
            }
            
            // 如果没有关联内容，尝试根据行业和职位推荐内容
            if (StringUtils.hasText(template.getIndustry())) {
                java.util.List<ResumeTemplateContent> recommendedContents = 
                        templateContentService.getBestMatchContent(template.getIndustry(), null);
                
                if (!recommendedContents.isEmpty()) {
                    ResumeTemplateContent bestMatch = recommendedContents.get(0);
                    jsonContent = bestMatch.getContentDataAsString();
                    log.debug("使用推荐内容，模板ID: {}, 推荐内容ID: {}", 
                            template.getId(), bestMatch.getId());
                    return jsonContent;
                }
            }
            
            log.warn("无法加载JSON默认内容，模板ID: {}", template.getId());
            return null;
            
        } catch (Exception e) {
            log.error("加载JSON默认内容失败，模板ID: {}, 错误: {}", template.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建模板加载响应
     * 
     * @description 根据模板信息和内容创建完整的加载响应
     * @param template 模板实体对象
     * @param vueContent Vue文件内容
     * @param jsonContent JSON默认内容
     * @return 模板加载响应
     */
    private TemplateLoadResponse createTemplateLoadResponse(ResumeTemplates template, 
                                                           String vueContent, 
                                                           String jsonContent) {
        TemplateLoadResponse response = new TemplateLoadResponse();
        
        // 设置模板基本信息
        response.setTemplateId(template.getId());
        response.setTemplateName(template.getName());
        response.setTemplateCode(template.getTemplateCode());
        response.setIsPremium(template.getIsPremium() != null && template.getIsPremium() == 1);
        
        // 设置文件内容
        response.setTemplateContent(vueContent);
        response.setDefaultContent(jsonContent);
        
        // 设置加载信息
        response.setDataSource("database+minio");
        response.setLoadTime(0L); // 将在外部设置
        response.setHasDefaultContent(StringUtils.hasText(jsonContent));
        response.setContentId(template.getContentId());
        response.setVersion("1.0.0");
        response.setCacheTime(LocalDateTime.now());
        
        // 设置文件大小
        if (StringUtils.hasText(vueContent)) {
            response.setFileSize((long) vueContent.getBytes().length);
        }
        
        log.debug("创建模板加载响应成功，模板ID: {}, 有Vue内容: {}, 有JSON内容: {}", 
                template.getId(), StringUtils.hasText(vueContent), StringUtils.hasText(jsonContent));
        
        return response;
    }

    /**
     * 缓存模板到Redis
     * 
     * @description 将加载的模板缓存到Redis中
     * @param request 模板加载请求
     * @param response 模板加载响应
     */
    private void cacheTemplate(TemplateLoadRequest request, TemplateLoadResponse response) {
        try {
            if (response.getTemplateId() != null && StringUtils.hasText(response.getTemplateContent())) {
                // 缓存Vue模板内容
                templateCacheService.setTemplateToCache(response.getTemplateId(), 
                    response.getTemplateContent(), java.time.Duration.ofHours(24));
                log.debug("模板缓存成功，模板ID: {}", response.getTemplateId());
            }
        } catch (Exception e) {
            log.warn("缓存模板异常，模板ID: {}, 错误: {}", response.getTemplateId(), e.getMessage());
        }
    }

    /**
     * 检查数据库模板可用性
     * 
     * @description 检查模板在数据库中是否存在且可用
     * @param templateId 模板ID
     * @return 是否可用
     */
    private boolean checkDatabaseTemplateAvailability(Long templateId) {
        try {
            ResumeTemplates template = resumeTemplatesService.lambdaQuery()
                    .eq(ResumeTemplates::getId, templateId)
                    .eq(ResumeTemplates::getIsDeleted, 0)
                    .eq(ResumeTemplates::getStatus, 1)
                    .one();
            
            return template != null;
        } catch (Exception e) {
            log.warn("检查数据库模板可用性失败，模板ID: {}, 错误: {}", templateId, e.getMessage());
            return false;
        }
    }

    /**
     * 检查MinIO Vue文件可用性
     * 
     * @description 检查Vue文件在MinIO中是否存在
     * @param templateId 模板ID
     * @return 是否可用
     */
    private boolean checkMinIOVueFileAvailability(Long templateId) {
        try {
            ResumeTemplates template = resumeTemplatesService.getById(templateId);
            if (template == null || !StringUtils.hasText(template.getVueFilePath())) {
                return false;
            }
            
            return templateFileService.existsInMinIO(template);
        } catch (Exception e) {
            log.warn("检查MinIO Vue文件可用性失败，模板ID: {}, 错误: {}", templateId, e.getMessage());
            return false;
        }
    }

    /**
     * 检查数据库内容可用性
     * 
     * @description 检查模板的默认内容在数据库中是否存在
     * @param templateId 模板ID
     * @return 是否可用
     */
    private boolean checkDatabaseContentAvailability(Long templateId) {
        try {
            ResumeTemplates template = resumeTemplatesService.getById(templateId);
            if (template == null || template.getContentId() == null) {
                return false;
            }
            
            ResumeTemplateContent content = templateContentService.getById(template.getContentId());
            return content != null && content.isEnabled();
        } catch (Exception e) {
            log.warn("检查数据库内容可用性失败，模板ID: {}, 错误: {}", templateId, e.getMessage());
            return false;
        }
    }
} 