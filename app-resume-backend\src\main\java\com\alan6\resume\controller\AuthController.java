package com.alan6.resume.controller;

import com.alan6.resume.core.R;
import com.alan6.resume.dto.auth.AdminLoginRequest;
import com.alan6.resume.dto.auth.AdminLoginResponse;
import com.alan6.resume.dto.auth.LoginRequest;
import com.alan6.resume.dto.auth.LoginResponse;
import com.alan6.resume.dto.auth.PasswordLoginRequest;
import com.alan6.resume.dto.auth.RegisterRequest;
import com.alan6.resume.dto.auth.RegisterResponse;
import com.alan6.resume.service.IAuthService;
import com.alan6.resume.common.utils.TokenUtils;
import com.alan6.resume.common.constants.AuthConstants;
import com.alan6.resume.common.utils.BusinessLogUtils;
import com.alan6.resume.common.ratelimit.RateLimit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 认证控制器
 * 
 * 主要功能：
 * 1. 处理用户登录请求
 * 2. 处理用户登出请求  
 * 3. 发送短信验证码
 * 4. Token相关操作
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Tag(name = "认证管理")
@RestController
@RequestMapping("/auth")
@Validated
public class AuthController {

    /**
     * 认证服务
     */
    @Autowired
    private IAuthService authService;

    /**
     * Token工具类
     */
    @Autowired
    private TokenUtils tokenUtils;

    /**
     * 用户注册
     * 
     * 处理用户注册的完整流程：
     * 1. 验证注册参数的有效性和完整性
     * 2. 检查手机号是否已被其他用户注册
     * 3. 验证短信验证码的真实性和有效性
     * 4. 创建新用户账户并设置初始信息
     * 5. 生成访问Token和刷新Token
     * 6. 返回注册成功响应，包含用户信息和Token
     * 
     * 注册成功后，用户可以直接使用返回的Token进行后续操作，
     * 无需再次登录，提升用户体验。
     * 
     * 安全特性：
     * - 手机号格式严格验证
     * - 短信验证码过期时间控制
     * - 密码强度要求（6-20位）
     * - 用户协议同意确认
     * - 详细的业务日志记录
     * 
     * @param registerRequest 用户注册请求参数
     * @param request HTTP请求对象
     * @return 注册结果，包含Token和用户基本信息
     */
    @Operation(summary = "用户注册", description = "新用户注册，成功后返回Token和用户信息")
    @PostMapping("/register")
    @RateLimit(key = "register:#phone", rate = 3, rateInterval = 300, 
               message = "注册尝试过于频繁，请5分钟后重试")
    public R<RegisterResponse> register(@Parameter(description = "用户注册请求参数", required = true)
                                       @Valid @RequestBody RegisterRequest registerRequest,
                                       HttpServletRequest request) {
        // 生成请求ID并设置日志上下文
        String requestId = BusinessLogUtils.generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取客户端信息
            String clientIp = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            
            // 设置日志上下文
            BusinessLogUtils.setContext(requestId, registerRequest.getPhone(), "user_register");
            BusinessLogUtils.setClientInfo(clientIp, registerRequest.getPlatform());
            
            // 记录注册请求开始
            BusinessLogUtils.logBusiness("用户注册请求开始", 
                "registerType", registerRequest.getRegisterType(),
                "platform", registerRequest.getPlatform(),
                "phone", registerRequest.getPhone(),
                "agreeTerms", registerRequest.getAgreeTerms());
            
            // 执行用户注册逻辑
            RegisterResponse response = authService.register(registerRequest);
            
            // 更新用户ID上下文
            BusinessLogUtils.setContext(requestId, String.valueOf(response.getUserId()), "user_register");
            
            // 记录注册成功
            BusinessLogUtils.logSuccess("用户注册成功",
                "userId", response.getUserId(),
                "platform", registerRequest.getPlatform(),
                "hasRefreshToken", response.getRefreshToken() != null);
            
            return R.ok(response, "注册成功");
            
        } catch (Exception e) {
            // 记录注册失败
            BusinessLogUtils.logError("用户注册失败", e,
                "registerType", registerRequest.getRegisterType(),
                "platform", registerRequest.getPlatform(),
                "phone", registerRequest.getPhone(),
                "agreeTerms", registerRequest.getAgreeTerms());
            
            return R.fail("注册失败：" + e.getMessage());
            
        } finally {
            // 记录性能日志
            long duration = System.currentTimeMillis() - startTime;
            BusinessLogUtils.logPerformance("user_register", duration);
            
            // 清理日志上下文
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 用户登录-基于密码
     * 
     * 专门用于已注册用户的密码登录：
     * 1. 验证手机号和密码格式
     * 2. 检查用户是否已注册
     * 3. 验证密码正确性
     * 4. 生成Token并返回用户信息
     * 
     * 注意：此接口仅用于已注册用户，不会创建新用户
     * 
     * @param passwordLoginRequest 密码登录请求参数
     * @param request HTTP请求对象
     * @return 登录结果，包含Token和用户信息
     */
    @Operation(summary = "用户登录-基于密码", description = "已注册用户使用手机号和密码登录")
    @PostMapping("/password-login")
    @RateLimit(key = "password_login:#phone", rate = 5, rateInterval = 300, 
               message = "密码登录尝试过于频繁，请5分钟后重试")
    public R<LoginResponse> passwordLogin(@Parameter(description = "密码登录请求参数", required = true)
                                         @Valid @RequestBody PasswordLoginRequest passwordLoginRequest,
                                         HttpServletRequest request) {
        // 生成请求ID并设置日志上下文
        String requestId = BusinessLogUtils.generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取客户端信息
            String clientIp = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            
            // 设置日志上下文
            BusinessLogUtils.setContext(requestId, passwordLoginRequest.getPhone(), "password_login");
            BusinessLogUtils.setClientInfo(clientIp, passwordLoginRequest.getPlatform());
            
            // 设置请求参数
            passwordLoginRequest.setClientIp(clientIp);
            passwordLoginRequest.setUserAgent(userAgent);
            
            // 记录密码登录请求开始
            BusinessLogUtils.logBusiness("用户密码登录请求开始", 
                "loginType", passwordLoginRequest.getLoginType(),
                "platform", passwordLoginRequest.getPlatform(),
                "phone", passwordLoginRequest.getPhone());
            
            // 执行密码登录逻辑
            LoginResponse response = authService.passwordLogin(passwordLoginRequest);
            
            // 更新用户ID上下文
            BusinessLogUtils.setContext(requestId, String.valueOf(response.getUserId()), "password_login");
            
            // 记录登录成功
            BusinessLogUtils.logSuccess("用户密码登录",
                "userId", response.getUserId(),
                "isMember", response.getMembershipInfo() != null ? response.getMembershipInfo().getIsMember() : false);
            
            return R.ok(response, "登录成功");
            
        } catch (Exception e) {
            // 记录登录失败
            BusinessLogUtils.logError("用户密码登录失败", e,
                "loginType", passwordLoginRequest.getLoginType(),
                "platform", passwordLoginRequest.getPlatform(),
                "phone", passwordLoginRequest.getPhone());
            
            return R.fail("登录失败：" + e.getMessage());
            
        } finally {
            // 记录性能日志
            long duration = System.currentTimeMillis() - startTime;
            BusinessLogUtils.logPerformance("password_login", duration);
            
            // 清理日志上下文
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 用户登录-基于短信验证码
     * 
     * 支持多种登录方式：
     * 1. 手机号+验证码登录（loginType=1）
     * 2. 微信授权登录（loginType=2）
     * 3. 小程序登录（loginType=3）
     * 
     * 登录成功后会返回Token，客户端需要在后续请求的Header中携带此Token
     * 
     * @param loginRequest 登录请求参数
     * @param request HTTP请求对象
     * @return 登录结果，包含Token和用户信息
     */
    @Operation(summary = "用户登录-基于短信验证码", description = "支持手机号、微信、小程序多种登录方式")
    @PostMapping("/login-sms")
    @RateLimit(key = "login:#phone", rate = 5, rateInterval = 300, 
               message = "登录尝试过于频繁，请5分钟后重试")
    public R<LoginResponse> login(@Parameter(description = "登录请求参数", required = true)
                                 @Valid @RequestBody LoginRequest loginRequest,
                                 HttpServletRequest request) {
        // 生成请求ID并设置日志上下文
        String requestId = BusinessLogUtils.generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取客户端信息
            String clientIp = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            
            // 设置日志上下文
            BusinessLogUtils.setContext(requestId, loginRequest.getPhone(), "login");
            BusinessLogUtils.setClientInfo(clientIp, loginRequest.getPlatform());
            
            // 设置请求参数
            loginRequest.setClientIp(clientIp);
            loginRequest.setUserAgent(userAgent);
            
            // 记录登录请求开始
            BusinessLogUtils.logBusiness("用户登录请求开始", 
                "loginType", loginRequest.getLoginType(),
                "platform", loginRequest.getPlatform(),
                "phone", loginRequest.getPhone());
            
            // 执行登录逻辑
            LoginResponse response = authService.login(loginRequest);
            
            // 更新用户ID上下文
            BusinessLogUtils.setContext(requestId, String.valueOf(response.getUserId()), "login");
            
            // 记录登录成功
            BusinessLogUtils.logSuccess("用户登录",
                "userId", response.getUserId(),
                "isMember", response.getMembershipInfo() != null ? response.getMembershipInfo().getIsMember() : false);
            
            return R.ok(response, "登录成功");
            
        } catch (Exception e) {
            // 记录登录失败
            BusinessLogUtils.logError("用户登录失败", e,
                "loginType", loginRequest.getLoginType(),
                "platform", loginRequest.getPlatform(),
                "phone", loginRequest.getPhone());
            
            return R.fail("登录失败：" + e.getMessage());
            
        } finally {
            // 记录性能日志
            long duration = System.currentTimeMillis() - startTime;
            BusinessLogUtils.logPerformance("login", duration);
            
            // 清理日志上下文
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 用户登出
     * 
     * 清除服务器端的Token缓存，使Token失效
     * 客户端调用此接口后应该清除本地存储的Token
     * 
     * @param request HTTP请求对象
     * @return 登出结果
     */
    @Operation(summary = "用户登出", description = "清除Token缓存，使Token失效")
    @PostMapping("/logout")
    public R<Void> logout(HttpServletRequest request) {
        try {
            // 从请求头中提取Token
            String authorizationHeader = request.getHeader(AuthConstants.Token.HEADER_NAME);
            String token = tokenUtils.extractTokenFromHeader(authorizationHeader);
            
            if (token == null) {
                log.warn("登出请求中未找到有效Token");
                return R.fail("未找到有效的登录信息");
            }
            
            // 执行登出逻辑
            boolean success = authService.logout(token);
            
            if (success) {
                log.info("用户登出成功，Token: {}", token);
                return R.ok(null, "登出成功");
            } else {
                log.warn("用户登出失败，Token: {}", token);
                return R.fail("登出失败");
            }
            
        } catch (Exception e) {
            log.error("用户登出异常", e);
            return R.fail("登出失败：" + e.getMessage());
        }
    }

    /**
     * 发送短信验证码
     * 
     * 用于手机号登录时发送验证码
     * 接口限制：同一手机号1分钟内只能发送1次，每天最多发送10次
     * 
     * @param phone 手机号
     * @param platform 平台标识
     * @return 发送结果
     */
    @Operation(summary = "发送短信验证码", description = "用于手机号登录，有频率限制")
    @PostMapping("/send-code")
    @RateLimit(key = "sms_code:#phone", rate = 1, rateInterval = 60, 
               message = "验证码发送过于频繁，请1分钟后重试")
    public R<Void> sendSmsCode(@Parameter(description = "手机号", required = true, example = "13800138000")
                               @NotBlank(message = "手机号不能为空")
                               @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
                               @RequestParam String phone,
                               
                               @Parameter(description = "平台标识", required = true, example = "web")
                               @NotBlank(message = "平台标识不能为空")
                               @Pattern(regexp = "^(web|wechat_mp|douyin_mp|baidu_mp|alipay_mp|app_ios|app_android)$", 
                                       message = "平台标识格式不正确")
                               @RequestParam String platform) {
        // 生成请求ID并设置日志上下文
        String requestId = BusinessLogUtils.generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            // 设置日志上下文
            BusinessLogUtils.setContext(requestId, phone, "send_sms_code");
            BusinessLogUtils.setClientInfo(null, platform);
            
            // 记录发送验证码请求开始
            BusinessLogUtils.logBusiness("发送短信验证码请求开始", 
                "phone", phone,
                "platform", platform);
            
            // 执行发送短信验证码逻辑
            boolean success = authService.sendSmsCode(phone, platform);
            
            if (success) {
                // 记录发送成功
                BusinessLogUtils.logSuccess("发送短信验证码",
                    "phone", phone,
                    "platform", platform);
                
                return R.ok(null, "验证码发送成功");
            } else {
                // 记录发送失败
                BusinessLogUtils.logWarning("短信验证码发送失败",
                    "phone", phone,
                    "platform", platform,
                    "reason", "服务提供商返回失败");
                
                return R.fail("验证码发送失败，请稍后重试");
            }
            
        } catch (Exception e) {
            // 记录异常
            BusinessLogUtils.logError("发送短信验证码异常", e,
                "phone", phone,
                "platform", platform);
            
            return R.fail("验证码发送失败：" + e.getMessage());
            
        } finally {
            // 记录性能日志
            long duration = System.currentTimeMillis() - startTime;
            BusinessLogUtils.logPerformance("send_sms_code", duration);
            
            // 清理日志上下文
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 刷新Token
     * 
     * 延长Token的有效期，避免用户频繁重新登录
     * 只有在Token即将过期时才允许刷新
     * 
     * @param request HTTP请求对象
     * @return 刷新结果
     */
    @Operation(summary = "刷新Token", description = "延长Token有效期")
    @PostMapping("/refresh-token")
    public R<Void> refreshToken(HttpServletRequest request) {
        try {
            // 从请求头中提取Token
            String authorizationHeader = request.getHeader(AuthConstants.Token.HEADER_NAME);
            String token = tokenUtils.extractTokenFromHeader(authorizationHeader);
            
            if (token == null) {
                log.warn("刷新Token请求中未找到有效Token");
                return R.fail("未找到有效的Token");
            }
            
            // 执行Token刷新逻辑
            boolean success = authService.refreshToken(token);
            
            if (success) {
                log.info("Token刷新成功，Token: {}", token);
                return R.ok(null, "Token刷新成功");
            } else {
                log.warn("Token刷新失败，Token: {}", token);
                return R.fail("Token刷新失败");
            }
            
        } catch (Exception e) {
            log.error("Token刷新异常", e);
            return R.fail("Token刷新失败：" + e.getMessage());
        }
    }

    /**
     * 验证Token有效性
     * 
     * 客户端可以调用此接口检查Token是否仍然有效
     * 主要用于前端判断是否需要重新登录
     * 
     * @param request HTTP请求对象
     * @return 验证结果
     */
    @Operation(summary = "验证Token", description = "检查Token是否有效")
    @GetMapping("/validate-token")
    public R<Boolean> validateToken(HttpServletRequest request) {
        try {
            // 从请求头中提取Token
            String authorizationHeader = request.getHeader(AuthConstants.Token.HEADER_NAME);
            String token = tokenUtils.extractTokenFromHeader(authorizationHeader);
            
            if (token == null) {
                log.debug("验证Token请求中未找到Token");
                return R.ok(false, "未找到Token");
            }
            
            // 执行Token验证逻辑
            boolean isValid = authService.validateToken(token);
            
            log.debug("Token验证结果，Token: {}, 有效性: {}", token, isValid);
            
            return R.ok(isValid, isValid ? "Token有效" : "Token无效");
            
        } catch (Exception e) {
            log.error("Token验证异常", e);
            return R.ok(false, "Token验证失败");
        }
    }

    /**
     * 管理员登录
     * 
     * 专门用于后台管理系统的登录接口
     * 只有具有管理员角色的用户才能登录成功
     * 
     * @param adminLoginRequest 管理员登录请求参数
     * @param request HTTP请求对象
     * @return 管理员登录结果，包含权限和菜单信息
     */
    @Operation(summary = "管理员登录", description = "后台管理系统专用登录接口，返回权限和菜单信息")
    @PostMapping("/login-admin")
    @RateLimit(key = "admin_login:#phone", rate = 3, rateInterval = 300, 
               message = "管理员登录尝试过于频繁，请5分钟后重试")
    public R<AdminLoginResponse> adminLogin(@Parameter(description = "管理员登录请求参数", required = true)
                                           @Valid @RequestBody AdminLoginRequest adminLoginRequest,
                                           HttpServletRequest request) {
        // 生成请求ID并设置日志上下文
        String requestId = BusinessLogUtils.generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            // 设置日志上下文
            BusinessLogUtils.setContext(requestId, adminLoginRequest.getPhone(), "admin_login");
            BusinessLogUtils.setClientInfo(getClientIpAddress(request), adminLoginRequest.getPlatform());
            
            // 记录管理员登录请求开始
            BusinessLogUtils.logBusiness("管理员登录请求开始", 
                "phone", adminLoginRequest.getPhone(),
                "platform", adminLoginRequest.getPlatform());
            
            // 执行管理员登录逻辑
            AdminLoginResponse response = authService.adminLogin(adminLoginRequest, getClientIpAddress(request));
            
            // 记录登录成功
            BusinessLogUtils.logSuccess("管理员登录成功",
                "userId", response.getUserId(),
                "username", response.getUsername(),
                "phone", response.getPhone(),
                "roles", response.getRoles(),
                "permissions", response.getPermissions());
            
            return R.ok(response, "管理员登录成功");
            
        } catch (Exception e) {
            // 记录登录失败
            BusinessLogUtils.logError("管理员登录失败", e,
                "phone", adminLoginRequest.getPhone(),
                "platform", adminLoginRequest.getPlatform());
            
            return R.fail("管理员登录失败：" + e.getMessage());
            
        } finally {
            // 记录性能日志
            long duration = System.currentTimeMillis() - startTime;
            BusinessLogUtils.logPerformance("admin_login", duration);
            
            // 清理日志上下文
            BusinessLogUtils.clearContext();
        }
    }

    /**
     * 获取客户端真实IP地址
     * 
     * 考虑代理服务器和负载均衡器的情况
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // 检查是否通过代理
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况（取第一个非unknown的IP）
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }
        
        return ipAddress;
    }
}
