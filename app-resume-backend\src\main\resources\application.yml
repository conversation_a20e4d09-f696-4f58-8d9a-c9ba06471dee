server:
  port: 9311

spring:
  application:
    name: app-resume
  
  # 环境配置
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**************:3306/resume?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: root
    password: 123456
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置连接超时时间
      connect-timeout: 30000
      # 配置网络超时时间
      socket-timeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000"
      # 配置web监控
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        reset-enable: false
        login-username: admin
        login-password: admin
        allow: ""
        deny: ""
  
  # Redis配置
  data:
    redis:
      host: **************
      port: 6379
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 8
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池中的最小空闲连接
          min-idle: 0

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 搜索指定包别名
  typeAliasesPackage: com.alan6.resume.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath:mapper/*.xml
  # 全局配置
  global-config:
    db-config:
      # 主键类型
      id-type: ASSIGN_ID
      # 字段策略
      field-strategy: NOT_EMPTY
      # 数据库类型
      db-type: MYSQL
      # 逻辑删除字段
      logic-delete-field: isDeleted
      # 逻辑删除全局值（1表示已删除）
      logic-delete-value: 1
      # 逻辑未删除全局值（0表示未删除）
      logic-not-delete-value: 0

# 应用认证配置
app:
  auth:
    # 是否允许多设备同时登录
    allow-multi-device: false
    # 单用户最大Token数量（多设备模式下的限制）
    max-tokens-per-user: 5
  
  # 前端域名配置
  frontend:
    domain: http://localhost:3001
  
  # 导出配置
  export:
    expire-hours: 24
    storage-path: tmp/resume-exports
    url-prefix: http://localhost:9311/api/resume/export

# MinIO配置
minio:
  endpoint: http://**************:9000
  access-key: admin
  secret-key: admin123
  bucket-name: resume-exports
  template-bucket-name: resume-templates

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
    try-it-out-enabled: true
    filter: true
    disable-swagger-default-url: true
  show-actuator: false
  default-consumes-media-type: application/json
  default-produces-media-type: application/json

# 日志配置
logging:
  level:
    com.alan6.resume: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
