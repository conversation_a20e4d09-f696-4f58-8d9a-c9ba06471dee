/**
 * 简历模板逻辑组合式函数
 * @description 处理模块显示、数据合并等逻辑
 */

import { computed } from 'vue'

export function useResumeTemplateLogic(props) {
    /**
   * 判断模块是否应该显示
   * @param {string} moduleType - 模块类型
   * @returns {boolean} 是否显示
   */
  const shouldShowModule = (moduleType) => {
    return computed(() => {
      // 检查模块是否在可见列表中
      if (!props.visibleModules.includes(moduleType)) {
        return false
      }
      
      // 检查模块是否存在于modules数组中且已启用
      const modules = props.resumeData?.modules || []
      const module = modules.find(m => m.id === moduleType)
      
      if (!module || !module.enabled) {
        return false
      }
      
      return true
    }).value
  }

  /**
   * 获取模块数据
   * @param {string} moduleType - 模块类型
   * @returns {any} 模块数据
   */
  const getModuleData = (moduleType) => {
    const modules = props.resumeData?.modules || []
    const module = modules.find(m => m.id === moduleType)
    return module?.data || null
  }
  
  /**
   * 获取模块标题
   * @param {string} moduleType - 模块类型
   * @returns {string} 模块标题
   */
  const getModuleTitle = (moduleType) => {
    const titleMap = {
      basic_info: '基本信息',
      education: '教育经历',
      work_experience: '工作经历',
      project: '项目经验',
      skills: '技能标签',
      language: '语言能力',
      award: '获奖荣誉',
      hobbies: '兴趣爱好',
      self_evaluation: '自我评价',
      internship: '实习经历',
      training: '培训经历',
      research_experience: '研究经历',
      publication: '论文专利',
      certificate: '证书资质',
      volunteer_experience: '志愿服务',
      portfolio: '作品集',
      cover_letter: '自荐信',
      custom: '自定义'
    }
    
    return titleMap[moduleType] || '未知模块'
  }
  
  return {
    shouldShowModule,
    getModuleData,
    getModuleTitle
  }
} 