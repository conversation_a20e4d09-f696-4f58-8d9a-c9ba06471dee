<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar">
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ active: isFormatActive('bold') }"
          @click="toggleFormat('bold')"
          title="粗体 (Ctrl+B)"
        >
          <strong>B</strong>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ active: isFormatActive('italic') }"
          @click="toggleFormat('italic')"
          title="斜体 (Ctrl+I)"
        >
          <em>I</em>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ active: isFormatActive('underline') }"
          @click="toggleFormat('underline')"
          title="下划线 (Ctrl+U)"
        >
          <u>U</u>
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          @click="createLink"
          title="超链接 (Ctrl+K)"
        >
          🔗
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ active: isFormatActive('insertUnorderedList') }"
          @click="toggleList('insertUnorderedList')"
          title="无序列表"
        >
          <svg viewBox="0 0 16 16" fill="currentColor" class="w-4 h-4">
            <path d="M2 4a1 1 0 100-2 1 1 0 000 2zM5 3h9v1H5V3zM2 8a1 1 0 100-2 1 1 0 000 2zM5 7h9v1H5V7zM2 12a1 1 0 100-2 1 1 0 000 2zM5 11h9v1H5v-1z"/>
          </svg>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ active: isFormatActive('insertOrderedList') }"
          @click="toggleList('insertOrderedList')"
          title="有序列表"
        >
          <svg viewBox="0 0 16 16" fill="currentColor" class="w-4 h-4">
            <path d="M2.003 2.5a.5.5 0 00-.723-.447l-1.003.5a.5.5 0 00.446.895l.28-.14V6H.5a.5.5 0 000 1h2.006a.5.5 0 100-1h-.503V2.5zM5 3.25a.75.75 0 01.75-.75h8.5a.75.75 0 010 1.5h-8.5A.75.75 0 015 3.25zM5 7.25a.75.75 0 01.75-.75h8.5a.75.75 0 010 1.5h-8.5A.75.75 0 015 7.25zM5 11.25a.75.75 0 01.75-.75h8.5a.75.75 0 010 1.5h-8.5a.75.75 0 01-.75-.75zM.924 10.32l.003-.004a.851.851 0 01.144-.153A.66.66 0 011.5 10c.195 0 .306.068.374.146a.57.57 0 01.128.376c0 .453-.269.682-.8 1.078l-.035.025C.692 11.98 0 12.495 0 13.5a.5.5 0 00.5.5h2.003a.5.5 0 000-1H1.146c.132-.197.351-.372.654-.597l.047-.035c.47-.35 1.156-.858 1.156-1.845 0-.365-.118-.744-.377-1.038-.268-.303-.658-.484-1.126-.484-.48 0-.84.202-1.068.392a1.858 1.858 0 00-.348.384l-.007.011-.002.004-.001.002-.001.001a.5.5 0 00.851.525zM.5 10.055l-.427-.26.427.26z"/>
          </svg>
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ active: isAlignActive('left') }"
          @click="setAlignment('left')"
          title="左对齐"
        >
          <svg viewBox="0 0 16 16" fill="currentColor" class="w-4 h-4">
            <path d="M2 12.5a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm0-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5zm0-3a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm0-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5z"/>
          </svg>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ active: isAlignActive('center') }"
          @click="setAlignment('center')"
          title="居中对齐"
        >
          <svg viewBox="0 0 16 16" fill="currentColor" class="w-4 h-4">
            <path d="M4 12.5a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm-2-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5zm2-3a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm-2-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5z"/>
          </svg>
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ active: isAlignActive('right') }"
          @click="setAlignment('right')"
          title="右对齐"
        >
          <svg viewBox="0 0 16 16" fill="currentColor" class="w-4 h-4">
            <path d="M6 12.5a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm-4-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5zm4-3a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zm-4-3a.5.5 0 01.5-.5h11a.5.5 0 010 1h-11a.5.5 0 01-.5-.5z"/>
          </svg>
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          @click="clearFormatting"
          title="清除样式"
        >
          🗑
        </button>
      </div>
    </div>
    
    <div
      ref="editorContent"
      class="editor-content"
      contenteditable="true"
      :placeholder="placeholder"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus"
      @keydown="handleKeydown"
      @paste="handlePaste"
    ></div>
  </div>
</template>

<script>
import { ref, watch, nextTick, onMounted } from 'vue'

export default {
  name: 'RichTextEditor',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    minHeight: {
      type: String,
      default: '100px'
    }
  },
  emits: ['update:modelValue', 'focus', 'blur'],
  setup(props, { emit }) {
    const editorContent = ref(null)
    const internalContent = ref('')
    const isFocused = ref(false)
    
    // 初始化内容
    onMounted(() => {
      if (props.modelValue) {
        internalContent.value = props.modelValue
        editorContent.value.innerHTML = props.modelValue
      }
    })
    
    // 监听外部值变化
    watch(() => props.modelValue, (newValue) => {
      if (newValue !== internalContent.value) {
        internalContent.value = newValue || ''
        if (editorContent.value) {
          editorContent.value.innerHTML = newValue || ''
        }
      }
    })
    
    // 处理输入
    const handleInput = () => {
      const content = editorContent.value.innerHTML
      internalContent.value = content
      emit('update:modelValue', content)
    }
    
    // 处理焦点
    const handleFocus = () => {
      isFocused.value = true
      emit('focus')
    }
    
    const handleBlur = () => {
      isFocused.value = false
      emit('blur')
    }
    
    // 处理粘贴事件
    const handlePaste = (e) => {
      e.preventDefault()
      const text = e.clipboardData.getData('text/plain')
      document.execCommand('insertText', false, text)
      handleInput()
    }
    
    // 处理键盘事件
    const handleKeydown = (e) => {
      // 处理Tab键
      if (e.key === 'Tab') {
        e.preventDefault()
        document.execCommand('insertText', false, '\t')
        return
      }
      
      // 处理快捷键
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'b':
            e.preventDefault()
            toggleFormat('bold')
            break
          case 'i':
            e.preventDefault()
            toggleFormat('italic')
            break
          case 'u':
            e.preventDefault()
            toggleFormat('underline')
            break
          case 'k':
            e.preventDefault()
            createLink()
            break
        }
      }
    }
    
    // 检查格式是否激活
    const isFormatActive = (command) => {
      try {
        return document.queryCommandState(command)
      } catch (e) {
        return false
      }
    }
    
    // 检查对齐方式是否激活
    const isAlignActive = (alignment) => {
      try {
        if (alignment === 'left') return document.queryCommandState('justifyLeft')
        if (alignment === 'center') return document.queryCommandState('justifyCenter')
        if (alignment === 'right') return document.queryCommandState('justifyRight')
        return false
      } catch (e) {
        return false
      }
    }
    
    // 切换格式
    const toggleFormat = (command) => {
      document.execCommand(command, false, null)
      editorContent.value.focus()
      handleInput()
    }
    
    // 切换列表
    const toggleList = (command) => {
      document.execCommand(command, false, null)
      editorContent.value.focus()
      handleInput()
    }
    
    // 设置对齐方式
    const setAlignment = (alignment) => {
      let command = 'justifyLeft'
      if (alignment === 'center') command = 'justifyCenter'
      if (alignment === 'right') command = 'justifyRight'
      
      document.execCommand(command, false, null)
      editorContent.value.focus()
      handleInput()
    }
    
    // 创建超链接
    const createLink = () => {
      const selection = window.getSelection()
      const selectedText = selection.toString()
      
      if (!selectedText) {
        alert('请先选择要添加链接的文本')
        return
      }
      
      const url = prompt('请输入链接地址:', 'https://')
      if (url && url.trim()) {
        // 验证URL格式
        let validUrl = url.trim()
        if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://') && !validUrl.startsWith('mailto:')) {
          validUrl = 'https://' + validUrl
        }
        
        document.execCommand('createLink', false, validUrl)
        editorContent.value.focus()
        handleInput()
      }
    }
    
    // 清除格式
    const clearFormatting = () => {
      document.execCommand('removeFormat', false, null)
      editorContent.value.focus()
      handleInput()
    }
    
    return {
      editorContent,
      internalContent,
      isFocused,
      handleInput,
      handleFocus,
      handleBlur,
      handlePaste,
      handleKeydown,
      isFormatActive,
      isAlignActive,
      toggleFormat,
      toggleList,
      setAlignment,
      createLink,
      clearFormatting
    }
  }
}
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
  gap: 4px;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 2px;
}

.toolbar-separator {
  width: 1px;
  height: 20px;
  background: #d1d5db;
  margin: 0 4px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #374151;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: #e5e7eb;
}

.toolbar-btn.active {
  background: #3b82f6;
  color: white;
}

.editor-content {
  min-height: v-bind(minHeight);
  padding: 12px;
  outline: none;
  line-height: 1.4;
  font-size: 14px;
  color: #374151;
}

.editor-content:empty:before {
  content: attr(placeholder);
  color: #9ca3af;
  pointer-events: none;
}

.editor-content:focus {
  outline: none;
}

/* 富文本内容样式 */
.editor-content :deep(strong),
.editor-content :deep(b) {
  font-weight: bold;
}

.editor-content :deep(em),
.editor-content :deep(i) {
  font-style: italic;
}

.editor-content :deep(u) {
  text-decoration: underline;
}

.editor-content :deep(a) {
  color: #3b82f6;
  text-decoration: underline;
}

.editor-content :deep(ul) {
  list-style-type: disc;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.editor-content :deep(ol) {
  list-style-type: decimal;
  margin-left: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.editor-content :deep(li) {
  margin-bottom: 4px;
}

.editor-content :deep(div[style*="text-align: center"]) {
  text-align: center;
}

.editor-content :deep(div[style*="text-align: right"]) {
  text-align: right;
}

.editor-content :deep(div[style*="text-align: left"]) {
  text-align: left;
}
</style> 