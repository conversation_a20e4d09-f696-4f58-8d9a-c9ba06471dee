package com.alan6.resume.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 模板预览请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "模板预览请求DTO")
public class TemplatePreviewRequest {

    /**
     * 预览格式（可选，image:图片预览, html:HTML预览，默认: image）
     */
    @Pattern(regexp = "^(image|html)$", message = "预览格式只能是image或html")
    @Schema(description = "预览格式", example = "image", allowableValues = {"image", "html"})
    private String format = "image";

    /**
     * 图片质量（可选，当format=image时有效，low/medium/high，默认: medium）
     */
    @Pattern(regexp = "^(low|medium|high)$", message = "图片质量只能是low、medium或high")
    @Schema(description = "图片质量", example = "medium", allowableValues = {"low", "medium", "high"})
    private String quality = "medium";

    /**
     * 预览宽度（可选，像素值，默认: 800）
     */
    @Min(value = 200, message = "预览宽度最小值为200")
    @Max(value = 2000, message = "预览宽度最大值为2000")
    @Schema(description = "预览宽度", example = "800")
    private Integer width = 800;

    /**
     * 预览高度（可选，像素值，0表示自动计算）
     */
    @Min(value = 0, message = "预览高度不能为负数")
    @Max(value = 3000, message = "预览高度最大值为3000")
    @Schema(description = "预览高度", example = "0")
    private Integer height = 0;

    /**
     * 缩放比例（可选，0.5-2.0，默认: 1.0）
     */
    @DecimalMin(value = "0.5", message = "缩放比例最小值为0.5")
    @DecimalMax(value = "2.0", message = "缩放比例最大值为2.0")
    @Schema(description = "缩放比例", example = "1.0")
    private Double scale = 1.0;
} 