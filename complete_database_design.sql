-- ================================
-- 简历应用数据库表设计（简化版）
-- ================================

-- ================================
-- 用户管理相关表
-- ================================

-- 用户表
CREATE TABLE `users` (
  `id` BIGINT NOT NULL COMMENT '用户ID（主键）',
  `username` VARCHAR(50) DEFAULT NULL COMMENT '用户名',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `password` VARCHAR(255) DEFAULT NULL COMMENT '密码（加密）',
  `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `nickname` VARCHAR(100) DEFAULT NULL COMMENT '昵称',
  `gender` TINYINT DEFAULT 0 COMMENT '性别（0:未知,1:男,2:女）',
  `birthday` DATE DEFAULT NULL COMMENT '生日',
  `register_type` TINYINT DEFAULT 1 COMMENT '注册类型（1:邮箱,2:手机,3:第三方授权）',
  `register_platform` VARCHAR(20) DEFAULT NULL COMMENT '注册平台（web,wechat_mp,douyin_mp,baidu_mp,alipay_mp,app_ios,app_android）',
  `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_platform` VARCHAR(20) DEFAULT NULL COMMENT '最后登录平台',
  `is_phone_verified` TINYINT DEFAULT 0 COMMENT '手机号是否已验证（0:否,1:是）',
  `preferred_language` VARCHAR(10) DEFAULT 'zh-CN' COMMENT '界面语言偏好' ,
  `status` TINYINT DEFAULT 1 COMMENT '账号状态（0:禁用,1:正常）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_register_platform` (`register_platform`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户第三方授权表
CREATE TABLE `user_third_party_auths` (
  `id` BIGINT NOT NULL COMMENT '授权记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `platform` VARCHAR(20) NOT NULL COMMENT '平台类型（wechat_mp:微信小程序,douyin_mp:抖音小程序,baidu_mp:百度小程序,alipay_mp:支付宝小程序,wechat_app:微信APP,qq:QQ等）',
  `openid` VARCHAR(100) NOT NULL COMMENT '平台OpenID',
  `unionid` VARCHAR(100) DEFAULT NULL COMMENT '平台UnionID（如果有）',
  `platform_nickname` VARCHAR(100) DEFAULT NULL COMMENT '平台昵称',
  `platform_avatar` VARCHAR(500) DEFAULT NULL COMMENT '平台头像',
  `access_token` VARCHAR(500) DEFAULT NULL COMMENT '访问令牌',
  `refresh_token` VARCHAR(500) DEFAULT NULL COMMENT '刷新令牌',
  `expires_in` INT DEFAULT NULL COMMENT 'token过期时间（秒）',
  `bind_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_auth_time` DATETIME DEFAULT NULL COMMENT '最后授权时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:解绑,1:已绑定）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_openid` (`platform`, `openid`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_unionid` (`unionid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户第三方授权表';

-- 账号合并记录表（简化版）
CREATE TABLE `user_merge_records` (
  `id` BIGINT NOT NULL COMMENT '合并记录ID',
  `master_user_id` BIGINT NOT NULL COMMENT '主账号ID（保留的账号）',
  `slave_user_id` BIGINT NOT NULL COMMENT '从账号ID（被合并的账号）',
  `merge_type` TINYINT NOT NULL COMMENT '合并类型（1:微信绑定手机号,2:手机号绑定微信,3:管理员操作）',
  `merge_reason` VARCHAR(200) DEFAULT NULL COMMENT '合并原因',
  `operator_id` BIGINT DEFAULT NULL COMMENT '操作员ID（如果是管理员操作）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_master_user_id` (`master_user_id`),
  KEY `idx_slave_user_id` (`slave_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账号合并记录表';

-- ================================
-- 会员体系相关表
-- ================================

-- 会员套餐表
CREATE TABLE `membership_packages` (
  `id` BIGINT NOT NULL COMMENT '套餐ID',
  `package_name` VARCHAR(100) NOT NULL COMMENT '套餐名称',
  `package_code` VARCHAR(50) NOT NULL COMMENT '套餐代码',
  `description` TEXT DEFAULT NULL COMMENT '套餐描述',
  `duration_type` TINYINT NOT NULL COMMENT '时长类型（1:天,2:月,3:季,4:年,5:永久）',
  `duration_value` INT NOT NULL COMMENT '时长数值',
  `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
  `current_price` DECIMAL(10,2) NOT NULL COMMENT '现价',
  `max_resume_count` INT DEFAULT -1 COMMENT '最大简历数量（-1表示无限制）',
  `max_export_count` INT DEFAULT -1 COMMENT '每月导出次数（-1表示无限制）',
  `premium_templates` TINYINT DEFAULT 0 COMMENT '是否可用付费模板（0:否,1:是）',
  `is_recommended` TINYINT DEFAULT 0 COMMENT '是否推荐（0:否,1:是）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:下架,1:上架）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_package_code` (`package_code`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员套餐表';

-- 用户会员表
CREATE TABLE `user_memberships` (
  `id` BIGINT NOT NULL COMMENT '会员记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `order_id` BIGINT DEFAULT NULL COMMENT '订单ID',
  `start_time` DATETIME NOT NULL COMMENT '会员开始时间',
  `end_time` DATETIME NOT NULL COMMENT '会员结束时间',
  `used_resume_count` INT DEFAULT 0 COMMENT '已使用简历数量',
  `used_export_count` INT DEFAULT 0 COMMENT '本月已导出次数',
  `last_export_reset_time` DATETIME DEFAULT NULL COMMENT '导出次数最后重置时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:失效,1:生效）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员表';

-- ================================
-- 简历模板相关表
-- ================================

-- 模板分类表
CREATE TABLE `template_categories` (
  `id` BIGINT NOT NULL COMMENT '分类ID',
  `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '分类描述',
  `icon_url` VARCHAR(500) DEFAULT NULL COMMENT '分类图标URL',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父分类ID（0为顶级分类）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板分类表';

-- 简历模板表
CREATE TABLE `resume_templates` (
  `id` BIGINT NOT NULL COMMENT '模板ID',
  `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `template_code` VARCHAR(50) NOT NULL COMMENT '模板代码（对应文件夹名或组件名）',
  `description` TEXT DEFAULT NULL COMMENT '模板描述',
  `preview_image_url` VARCHAR(500) DEFAULT NULL COMMENT '预览图URL',
  `template_file_id` BIGINT DEFAULT NULL COMMENT '关联的模板文件ID（file_upload_records表）',
  `content_id` BIGINT DEFAULT NULL COMMENT '关联的内容ID',
  `local_file_path` VARCHAR(500) DEFAULT NULL COMMENT '本地文件路径',
  `vue_file_path` VARCHAR(500) DEFAULT NULL COMMENT 'Vue模板文件路径',
  `config_data` JSON DEFAULT NULL COMMENT '模板配置数据（主题、样式等）',
  `features` JSON DEFAULT NULL COMMENT '支持的功能特性列表',
  `tags` JSON DEFAULT NULL COMMENT '模板标签',
  `category_id` BIGINT DEFAULT NULL COMMENT '分类ID',
  `industry` VARCHAR(50) DEFAULT NULL COMMENT '适用行业',
  `style` VARCHAR(50) DEFAULT NULL COMMENT '模板风格',
  `color_scheme` VARCHAR(50) DEFAULT NULL COMMENT '配色方案',
  `is_premium` TINYINT DEFAULT 0 COMMENT '是否付费模板（0:免费,1:付费）',
  `price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '模板价格',
  `use_count` INT DEFAULT 0 COMMENT '使用次数',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:下架,1:上架）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_template_file_id` (`template_file_id`),
  KEY `idx_is_premium` (`is_premium`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  CONSTRAINT `fk_template_file` FOREIGN KEY (`template_file_id`) REFERENCES `file_upload_records` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历模板表';

-- 简历模板内容表
CREATE TABLE `resume_template_content` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `name` VARCHAR(100) NOT NULL COMMENT '内容名称',
  `code` VARCHAR(50) NOT NULL COMMENT '内容代码',
  `description` TEXT COMMENT '内容描述',
  `content_data` JSON NOT NULL COMMENT '简历内容数据',
  `industry` VARCHAR(50) COMMENT '适用行业',
  `position` VARCHAR(50) COMMENT '适用职位',
  `language` VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言',
  `version` VARCHAR(20) DEFAULT '1.0.0' COMMENT '版本号',
  `status` TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_industry` (`industry`),
  KEY `idx_position` (`position`)
);

-- 简历模板分类表
CREATE TABLE `resume_categories` (
  `id` BIGINT NOT NULL COMMENT '分类ID',
  `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
  `code` VARCHAR(50) NOT NULL COMMENT '分类代码',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '分类描述',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父分类ID（0为顶级分类）',
  `category_type` VARCHAR(30) NOT NULL COMMENT '分类类型（style:风格,industry:行业,experience:经验,audience:受众等）',
  `icon_url` VARCHAR(500) DEFAULT NULL COMMENT '分类图标URL',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_type` (`category_type`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历分类表';

-- 模板分类关联表
CREATE TABLE `resume_template_categories` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `template_id` BIGINT NOT NULL COMMENT '模板ID',
  `category_id` BIGINT NOT NULL COMMENT '分类ID',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_category` (`template_id`, `category_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_category_id` (`category_id`),
  CONSTRAINT `fk_template_categories_template` FOREIGN KEY (`template_id`) REFERENCES `resume_templates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_template_categories_category` FOREIGN KEY (`category_id`) REFERENCES `resume_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板分类关联表';

-- ================================
-- 简历相关表
-- ================================

-- 简历主表
CREATE TABLE `resumes` (
  `id` BIGINT NOT NULL COMMENT '简历ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `template_id` BIGINT DEFAULT NULL COMMENT '使用的模板ID',
  `name` VARCHAR(100) NOT NULL COMMENT '简历名称',
  `language` VARCHAR(10) DEFAULT 'zh-CN' COMMENT '简历语言（zh-CN,en,ja,ko等）',
  `parent_resume_id` BIGINT DEFAULT NULL COMMENT '父简历ID（从哪个简历生成的）',
  `mongo_document_id` VARCHAR(50) DEFAULT NULL COMMENT 'MongoDB文档ID，存的完整简历json数据',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公开（0:私有,1:公开）',
  `view_count` INT DEFAULT 0 COMMENT '浏览次数',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `share_count` INT DEFAULT 0 COMMENT '分享次数',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:草稿,1:完成）',
  `last_save_time` DATETIME DEFAULT NULL COMMENT '最后保存时间',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_language` (`language`),
  KEY `idx_parent_resume_id` (`parent_resume_id`),
  KEY `idx_create_time` (`create_time`),
  UNIQUE KEY `uk_mongo_document_id` (`mongo_document_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历主表';

-- ================================
-- 用户行为相关表
-- ================================

-- 用户收藏表
CREATE TABLE `user_favorites` (
  `id` BIGINT NOT NULL COMMENT '收藏ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `target_type` TINYINT NOT NULL COMMENT '收藏类型（1:模板,2:简历）',
  `target_id` BIGINT NOT NULL COMMENT '目标ID（模板ID或简历ID）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`),
  KEY `idx_target` (`target_type`, `target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';

-- 分享记录表
CREATE TABLE `share_records` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '分享记录ID',
  `user_id` BIGINT NOT NULL COMMENT '分享用户ID',
  `resource_type` VARCHAR(20) NOT NULL COMMENT '资源类型（resume:简历,template:模板）',
  `resource_id` BIGINT NOT NULL COMMENT '资源ID（简历ID或模板ID）',
  `share_type` TINYINT NOT NULL COMMENT '分享类型（1:公开,2:密码保护,3:仅链接访问,4:指定用户）',
  `share_scope` TINYINT DEFAULT 1 COMMENT '分享范围（1:公开,2:仅链接,3:指定域名）',
  `share_code` VARCHAR(64) NOT NULL COMMENT '分享码（用于生成分享链接）',
  `share_url` VARCHAR(500) DEFAULT NULL COMMENT '分享链接',
  `qr_code_url` VARCHAR(500) DEFAULT NULL COMMENT '二维码URL',
  `password` VARCHAR(255) DEFAULT NULL COMMENT '访问密码',
  `allow_download` TINYINT DEFAULT 1 COMMENT '是否允许下载（0:不允许,1:允许）',
  `view_limit` INT DEFAULT -1 COMMENT '访问次数限制（-1表示无限制）',
  `view_count` INT DEFAULT 0 COMMENT '查看次数',
  `download_count` INT DEFAULT 0 COMMENT '下载次数',
  `allowed_domains` TEXT DEFAULT NULL COMMENT '允许访问的域名白名单（JSON数组）',
  `description` VARCHAR(500) DEFAULT NULL COMMENT '分享描述',
  `expire_time` DATETIME DEFAULT NULL COMMENT '过期时间',
  `is_active` TINYINT DEFAULT 1 COMMENT '是否有效（0:失效,1:有效）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_code` (`share_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_resource` (`resource_type`, `resource_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享记录表';

-- 简历导出记录表
CREATE TABLE `resume_export_records` (
  `id` BIGINT NOT NULL COMMENT '导出记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `resume_id` BIGINT NOT NULL COMMENT '简历ID',
  `job_id` VARCHAR(50) DEFAULT NULL COMMENT '关联的导出任务ID',
  `export_format` VARCHAR(20) NOT NULL COMMENT '导出格式（pdf,word,image）',
  `download_url` VARCHAR(500) DEFAULT NULL COMMENT '下载链接',
  `file_size` BIGINT DEFAULT NULL COMMENT '文件大小（字节）',
  `export_platform` VARCHAR(20) DEFAULT NULL COMMENT '导出平台',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历导出记录表';

-- 异步导出队列表
CREATE TABLE `resume_export_queue` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `job_id` VARCHAR(50) NOT NULL,
  `user_id` BIGINT NOT NULL,
  `resume_id` BIGINT NOT NULL,
  `export_format` VARCHAR(20) NOT NULL COMMENT 'pdf/word/png/jpg',
  `priority` INT DEFAULT 5 COMMENT '优先级1-10',
  `status` VARCHAR(20) DEFAULT 'queued' COMMENT 'queued/processing/completed/failed',
  `download_url` VARCHAR(500) DEFAULT NULL,
  `file_size` BIGINT DEFAULT NULL,
  `error_message` TEXT DEFAULT NULL,
  `attempts` INT DEFAULT 0,
  `max_attempts` INT DEFAULT 3,
  `started_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `completed_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_job_id` (`job_id`),
  KEY `idx_user_resume` (`user_id`, `resume_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='简历导出队列表';

-- 文件上传记录表（支持MinIO对象存储）
CREATE TABLE `file_upload_records` (
  `id` BIGINT NOT NULL COMMENT '文件ID',
  `user_id` BIGINT DEFAULT NULL COMMENT '上传用户ID',
  `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
  `original_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `bucket_name` VARCHAR(100) DEFAULT NULL COMMENT 'MinIO存储桶名称',
  `object_key` VARCHAR(1000) DEFAULT NULL COMMENT 'MinIO对象键（完整路径）',
  `etag` VARCHAR(255) DEFAULT NULL COMMENT 'MinIO对象ETag（用于完整性验证）',
  `version_id` VARCHAR(255) DEFAULT NULL COMMENT '对象版本ID（支持版本控制）',
  `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
  `file_type` VARCHAR(100) NOT NULL COMMENT '文件类型（MIME类型）',
  `storage_class` VARCHAR(50) DEFAULT 'STANDARD' COMMENT '存储类别（STANDARD/REDUCED_REDUNDANCY/COLD）',
  `content_encoding` VARCHAR(100) DEFAULT NULL COMMENT '内容编码（gzip/deflate等）',
  `cache_control` VARCHAR(255) DEFAULT NULL COMMENT '缓存控制指令',
  `expires_at` DATETIME DEFAULT NULL COMMENT '文件过期时间',
  `access_policy` VARCHAR(50) DEFAULT 'private' COMMENT '访问策略（private/public-read/public-read-write）',
  `presigned_url_expires` DATETIME DEFAULT NULL COMMENT '预签名URL过期时间',
  `encryption_type` VARCHAR(50) DEFAULT NULL COMMENT '加密类型（SSE-S3/SSE-KMS/SSE-C）',
  `metadata` JSON DEFAULT NULL COMMENT '自定义元数据（JSON格式）',
  `upload_platform` VARCHAR(20) DEFAULT NULL COMMENT '上传平台',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_bucket_object` (`bucket_name`, `object_key`(255)),
  KEY `idx_etag` (`etag`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_access_policy` (`access_policy`),
  KEY `idx_storage_class` (`storage_class`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传记录表（支持MinIO对象存储）'; 

-- 用户操作日志表
CREATE TABLE `user_operation_logs` (
  `id` BIGINT NOT NULL COMMENT '日志ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型',
  `operation_module` VARCHAR(50) NOT NULL COMMENT '操作模块',
  `target_type` VARCHAR(50) DEFAULT NULL COMMENT '目标对象类型',
  `target_id` BIGINT DEFAULT NULL COMMENT '目标对象ID',
  `operation_desc` VARCHAR(200) DEFAULT NULL COMMENT '操作描述',
  `operation_data` JSON DEFAULT NULL COMMENT '操作相关数据',
  `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
  `platform` VARCHAR(20) DEFAULT NULL COMMENT '操作平台',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作日志表';

-- 用户访问日志表（用于IP地理位置记录和语言检测）
CREATE TABLE `user_access_logs` (
  `id` BIGINT NOT NULL COMMENT '访问日志ID',
  `user_id` BIGINT DEFAULT NULL COMMENT '用户ID',
  `ip_address` VARCHAR(50) NOT NULL COMMENT 'IP地址',
  `country_code` VARCHAR(10) DEFAULT NULL COMMENT '国家代码',
  `country_name` VARCHAR(50) DEFAULT NULL COMMENT '国家名称',
  `region` VARCHAR(50) DEFAULT NULL COMMENT '地区',
  `city` VARCHAR(50) DEFAULT NULL COMMENT '城市',
  `detected_language` VARCHAR(10) DEFAULT NULL COMMENT '检测到的语言',
  `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
  `referer` VARCHAR(500) DEFAULT NULL COMMENT '来源页面',
  `access_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_country_code` (`country_code`),
  KEY `idx_access_time` (`access_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户访问日志表';

-- ================================
-- 角色权限相关表
-- ================================

-- 角色表
CREATE TABLE `roles` (
  `id` BIGINT NOT NULL COMMENT '角色ID',
  `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `role_code` VARCHAR(50) NOT NULL COMMENT '角色代码',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '角色描述',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
CREATE TABLE `permissions` (
  `id` BIGINT NOT NULL COMMENT '权限ID',
  `permission_name` VARCHAR(100) NOT NULL COMMENT '权限名称',
  `permission_code` VARCHAR(100) NOT NULL COMMENT '权限代码',
  `resource_type` VARCHAR(50) DEFAULT NULL COMMENT '资源类型（menu:菜单,button:按钮,api:接口）',
  `resource_url` VARCHAR(200) DEFAULT NULL COMMENT '资源URL',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父权限ID',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `status` TINYINT DEFAULT 1 COMMENT '状态（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE `user_roles` (
  `id` BIGINT NOT NULL COMMENT '关联ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE `role_permissions` (
  `id` BIGINT NOT NULL COMMENT '关联ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `permission_id` BIGINT NOT NULL COMMENT '权限ID',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 插入基础角色数据
INSERT INTO `roles` (`id`, `role_name`, `role_code`, `description`, `sort_order`) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1),
(2, '管理员', 'ADMIN', '系统管理员，拥有大部分管理权限', 2),
(3, '普通用户', 'USER', '普通用户，只能使用基础功能', 3);

-- 插入基础权限数据
INSERT INTO `permissions` (`id`, `permission_name`, `permission_code`, `resource_type`, `resource_url`, `parent_id`, `sort_order`) VALUES
(1, '后台管理', 'admin', 'menu', '/admin', 0, 1),
(2, '用户管理', 'admin:user', 'menu', '/admin/users', 1, 2),
(3, '模板管理', 'admin:template', 'menu', '/admin/templates', 1, 3),
(4, '订单管理', 'admin:order', 'menu', '/admin/orders', 1, 4),
(5, '系统设置', 'admin:system', 'menu', '/admin/system', 1, 5);

-- 给超级管理员分配所有权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5);

-- 给管理员分配部分权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(2, 1), (2, 2), (2, 3), (2, 4);

-- ================================
-- 支付订单相关表
-- ================================

-- 订单表
CREATE TABLE `orders` (
  `id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `order_type` TINYINT NOT NULL COMMENT '订单类型（1:会员充值,2:模板购买）',
  `product_id` BIGINT DEFAULT NULL COMMENT '产品ID',
  `product_name` VARCHAR(100) DEFAULT NULL COMMENT '产品名称',
  `original_amount` DECIMAL(10,2) NOT NULL COMMENT '原价',
  `discount_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
  `final_amount` DECIMAL(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` TINYINT DEFAULT NULL COMMENT '支付方式（1:微信,2:支付宝）',
  `payment_platform` VARCHAR(20) DEFAULT NULL COMMENT '支付平台',
  `trade_no` VARCHAR(100) DEFAULT NULL COMMENT '第三方交易号',
  `order_status` TINYINT DEFAULT 1 COMMENT '订单状态（1:待支付,2:已支付,3:已完成,4:已取消,5:已退款）',
  `pay_time` DATETIME DEFAULT NULL COMMENT '支付时间',
  `complete_time` DATETIME DEFAULT NULL COMMENT '完成时间',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- ================================
-- 系统管理模块相关表
-- ================================

-- 支持的语言配置表
CREATE TABLE `supported_languages` (
  `id` BIGINT NOT NULL COMMENT '语言ID',
  `language_code` VARCHAR(10) NOT NULL COMMENT '语言代码(ISO 639-1)',
  `language_name` VARCHAR(50) NOT NULL COMMENT '语言名称',
  `native_name` VARCHAR(50) NOT NULL COMMENT '本地名称',
  `country_codes` VARCHAR(200) DEFAULT NULL COMMENT '主要使用国家代码(逗号分隔)',
  `is_rtl` TINYINT DEFAULT 0 COMMENT '是否从右到左书写',
  `is_active` TINYINT DEFAULT 1 COMMENT '是否启用',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_language_code` (`language_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支持的语言表';

-- 初始数据
INSERT INTO supported_languages VALUES
(1, 'zh-CN', 'Chinese (Simplified)', '简体中文', 'CN,SG', 0, 1, 1, NOW(), NOW()),
(2, 'en', 'English', 'English', 'US,GB,AU,CA,NZ', 0, 1, 2, NOW(), NOW()),
(3, 'ja', 'Japanese', '日本語', 'JP', 0, 1, 3, NOW(), NOW()),
(4, 'ko', 'Korean', '한국어', 'KR', 0, 1, 4, NOW(), NOW());

-- 系统配置表
CREATE TABLE `system_configs` (
  `id` BIGINT NOT NULL COMMENT '配置ID',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT DEFAULT NULL COMMENT '配置值',
  `config_type` VARCHAR(20) DEFAULT 'string' COMMENT '配置类型（string,number,boolean,json）',
  `config_group` VARCHAR(50) DEFAULT NULL COMMENT '配置分组',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '配置描述',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 意见反馈表
CREATE TABLE `system_feedback` (
  `id` BIGINT NOT NULL COMMENT '反馈ID',
  `feedback_id` VARCHAR(50) NOT NULL COMMENT '反馈编号（FB+时间戳）',
  `user_id` BIGINT DEFAULT NULL COMMENT '用户ID（匿名反馈时可为空）',
  `type` VARCHAR(20) NOT NULL COMMENT '反馈类型（bug:错误报告,feature:功能建议,complaint:投诉,suggestion:建议）',
  `title` VARCHAR(200) NOT NULL COMMENT '反馈标题',
  `content` TEXT NOT NULL COMMENT '反馈内容',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '联系邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `priority` VARCHAR(20) DEFAULT 'medium' COMMENT '优先级（low:低,medium:中,high:高,urgent:紧急）',
  `status` VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态（pending:待处理,processing:处理中,resolved:已解决,closed:已关闭）',
  `handler_id` BIGINT DEFAULT NULL COMMENT '处理人ID',
  `handler_reply` TEXT DEFAULT NULL COMMENT '处理回复',
  `handle_time` DATETIME DEFAULT NULL COMMENT '处理时间',
  `client_info` JSON DEFAULT NULL COMMENT '客户端信息（浏览器、设备等）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_feedback_id` (`feedback_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统反馈表';

-- 反馈附件表
CREATE TABLE `system_feedback_attachments` (
  `id` BIGINT NOT NULL COMMENT '附件ID',
  `feedback_id` BIGINT NOT NULL COMMENT '反馈ID',
  `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `file_size` BIGINT DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` VARCHAR(50) DEFAULT NULL COMMENT '文件类型',
  `upload_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_feedback_id` (`feedback_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='反馈附件表';

-- 帮助文档分类表
CREATE TABLE `help_categories` (
  `id` BIGINT NOT NULL COMMENT '分类ID',
  `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
  `code` VARCHAR(50) NOT NULL COMMENT '分类代码（getting-started,user-guide,faq,api-doc等）',
  `title` VARCHAR(100) NOT NULL COMMENT '分类标题',
  `description` VARCHAR(500) DEFAULT NULL COMMENT '分类描述',
  `icon` VARCHAR(100) DEFAULT NULL COMMENT '分类图标',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父分类ID（0为顶级分类）',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_enabled` TINYINT DEFAULT 1 COMMENT '是否启用（0:禁用,1:启用）',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助文档分类表';

-- 帮助文章表
CREATE TABLE `help_articles` (
  `id` BIGINT NOT NULL COMMENT '文章ID',
  `category_id` BIGINT NOT NULL COMMENT '分类ID',
  `title` VARCHAR(200) NOT NULL COMMENT '文章标题',
  `summary` VARCHAR(500) DEFAULT NULL COMMENT '文章摘要',
  `content` LONGTEXT NOT NULL COMMENT '文章内容（支持Markdown）',
  `content_html` LONGTEXT DEFAULT NULL COMMENT '文章HTML内容（缓存）',
  `tags` VARCHAR(500) DEFAULT NULL COMMENT '标签（逗号分隔）',
  `author_id` BIGINT DEFAULT NULL COMMENT '作者ID',
  `view_count` INT DEFAULT 0 COMMENT '浏览次数',
  `helpful_count` INT DEFAULT 0 COMMENT '有用评价次数',
  `unhelpful_count` INT DEFAULT 0 COMMENT '无用评价次数',
  `sort_order` INT DEFAULT 0 COMMENT '排序权重',
  `is_published` TINYINT DEFAULT 0 COMMENT '是否发布（0:草稿,1:已发布）',
  `publish_time` DATETIME DEFAULT NULL COMMENT '发布时间',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_publish_time` (`publish_time`),
  FULLTEXT KEY `ft_title_content` (`title`, `content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助文章表';

-- 系统维护记录表
CREATE TABLE `system_maintenance_logs` (
  `id` BIGINT NOT NULL COMMENT '维护记录ID',
  `maintenance_type` VARCHAR(50) NOT NULL COMMENT '维护类型（cache_clear:缓存清理,maintenance_mode:维护模式,system_restart:系统重启）',
  `operator_id` BIGINT NOT NULL COMMENT '操作人ID',
  `operation_desc` VARCHAR(500) NOT NULL COMMENT '操作描述',
  `operation_params` JSON DEFAULT NULL COMMENT '操作参数',
  `operation_result` JSON DEFAULT NULL COMMENT '操作结果',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME DEFAULT NULL COMMENT '结束时间',
  `duration_ms` INT DEFAULT NULL COMMENT '执行时长（毫秒）',
  `status` VARCHAR(20) DEFAULT 'success' COMMENT '执行状态（success:成功,failed:失败,running:执行中）',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
  `client_ip` VARCHAR(50) DEFAULT NULL COMMENT '操作IP',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_maintenance_type` (`maintenance_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统维护记录表';


-- 初始化帮助文档分类数据
INSERT INTO `help_categories` (`id`, `name`, `code`, `title`, `description`, `sort_order`) VALUES
(1, 'getting-started', 'getting-started', '快速开始', '新手入门指南', 1),
(2, 'user-guide', 'user-guide', '用户指南', '详细的功能使用说明', 2),
(3, 'faq', 'faq', '常见问题', '用户常见问题解答', 3),
(4, 'api-doc', 'api-doc', 'API文档', '开发者API接口文档', 4);

-- 初始化示例帮助文章
INSERT INTO `help_articles` (`id`, `category_id`, `title`, `summary`, `content`, `tags`, `view_count`, `helpful_count`, `sort_order`, `is_published`, `publish_time`) VALUES
(1, 1, '如何注册账号', '详细介绍账号注册流程', '# 如何注册账号\n\n## 1. 访问注册页面\n\n请访问我们的注册页面...\n\n## 2. 填写注册信息\n\n请填写以下必要信息：\n- 邮箱地址\n- 密码\n- 确认密码\n\n## 3. 验证邮箱\n\n注册成功后，系统会发送验证邮件到您的邮箱...', '注册,账号,新手', 1250, 89, 1, 1, NOW()),
(2, 1, '创建第一份简历', '从零开始创建简历的完整指南', '# 创建第一份简历\n\n## 1. 选择模板\n\n登录后，点击"创建简历"按钮...\n\n## 2. 填写基本信息\n\n包括姓名、联系方式、求职意向等...\n\n## 3. 添加工作经历\n\n详细填写您的工作经验...', '简历,创建,新手,模板', 980, 76, 2, 1, NOW()),
(3, 2, '简历模板使用', '如何选择和使用简历模板', '# 简历模板使用指南\n\n## 模板分类\n\n我们提供多种类型的模板：\n- 商务风格\n- 创意设计\n- 简约风格\n- 技术专业\n\n## 如何选择模板\n\n根据您的行业和个人喜好选择合适的模板...', '模板,使用,指南', 756, 65, 1, 1, NOW()); 