import { ref, computed } from 'vue'

/**
 * 模板服务 Composable
 * 用于管理简历模板的数据获取、筛选和分页
 */
export const useTemplateService = () => {
  // ================================
  // 响应式数据
  // ================================
  const templates = ref([])
  const loading = ref(false)
  const error = ref(null)
  const pagination = ref({
    current: 1,
    size: 20,
    total: 0,
    pages: 0
  })
  const currentFilters = ref({
    keyword: '',
    categoryId: null,
    industry: '',
    style: '',
    colorScheme: '',
    isPremium: null,
    sortBy: 'default'
  })

  // ================================
  // 计算属性
  // ================================
  const filteredTemplates = computed(() => {
    return templates.value
  })

  // ================================
  // API 方法
  // ================================
  
  /**
   * 获取模板列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  const fetchTemplates = async (params = {}) => {
    try {
      loading.value = true
      error.value = null

      // 构建请求参数
      const requestParams = {
        current: pagination.value.current,
        size: pagination.value.size,
        sortBy: currentFilters.value.sortBy,
        ...params
      }

      // 只添加非空的筛选参数
      if (currentFilters.value.keyword && currentFilters.value.keyword.trim()) {
        requestParams.keyword = currentFilters.value.keyword.trim()
      }
      if (currentFilters.value.categoryId) {
        requestParams.categoryId = currentFilters.value.categoryId
      }
      if (currentFilters.value.industry && currentFilters.value.industry.trim()) {
        requestParams.industry = currentFilters.value.industry.trim()
      }
      if (currentFilters.value.style && currentFilters.value.style.trim()) {
        requestParams.style = currentFilters.value.style.trim()
      }
      if (currentFilters.value.colorScheme && currentFilters.value.colorScheme.trim()) {
        requestParams.colorScheme = currentFilters.value.colorScheme.trim()
      }
      if (currentFilters.value.isPremium !== null && currentFilters.value.isPremium !== '') {
        requestParams.isPremium = currentFilters.value.isPremium
      }

      // 调用真实API接口
      const response = await $fetch('/api/template/list', {
        method: 'GET',
        params: requestParams
      })

      if (response.code === 200) {
        const data = response.data
        templates.value = data.records || []
        pagination.value.total = parseInt(data.total) || 0
        pagination.value.current = data.current || 1
        pagination.value.size = data.size || 20
        pagination.value.pages = data.pages || Math.ceil(pagination.value.total / pagination.value.size)

        return {
          success: true,
          data: templates.value,
          total: pagination.value.total
        }
      } else {
        throw new Error(response.msg || '获取模板列表失败')
      }
    } catch (err) {
      console.error('获取模板列表失败:', err)
      error.value = err.message || '获取模板列表失败'
      return {
        success: false,
        error: error.value
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据ID获取模板详情
   * @param {number} templateId - 模板ID
   * @returns {Promise}
   */
  const fetchTemplateById = async (templateId) => {
    try {
      loading.value = true
      error.value = null

      // 调用真实API接口
      const response = await $fetch(`/api/template/detail/${templateId}`, {
        method: 'GET'
      })

      if (response.code === 200) {
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '获取模板详情失败')
      }
    } catch (err) {
      console.error('获取模板详情失败:', err)
      error.value = err.message || '获取模板详情失败'
      return {
        success: false,
        error: error.value
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * 使用模板创建简历
   * @param {number} templateId - 模板ID
   * @param {Object} useRequest - 使用请求参数
   * @returns {Promise}
   */
  const useTemplate = async (templateId, useRequest) => {
    try {
      loading.value = true
      error.value = null

      // 调用真实API接口 - 需要登录
      const response = await $fetch(`/api/template/use/${templateId}`, {
        method: 'POST',
        body: useRequest,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.code === 200) {
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error(response.msg || '使用模板失败')
      }
    } catch (err) {
      console.error('使用模板失败:', err)
      error.value = err.message || '使用模板失败'
      return {
        success: false,
        error: error.value
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * 记录模板使用（已集成到useTemplate接口中）
   * @param {number} templateId - 模板ID
   * @returns {Promise}
   */
  const recordTemplateUsage = async (templateId) => {
    // 现在使用记录已经集成到useTemplate接口中
    // 这里保留接口兼容性，实际可以移除或简化
    return { success: true }
  }

  /**
   * 获取模板分类列表
   * @returns {Promise}
   */
  const fetchCategories = async () => {
    try {
      const response = await $fetch('/api/template/categories', {
        method: 'GET'
      })

      if (response.code === 200) {
        return {
          success: true,
          data: response.data || []
        }
      } else {
        throw new Error(response.msg || '获取模板分类失败')
      }
    } catch (err) {
      console.error('获取模板分类失败:', err)
      return {
        success: false,
        error: err.message || '获取模板分类失败'
      }
    }
  }

  // ================================
  // 筛选和分页方法
  // ================================
  
  /**
   * 设置关键词筛选
   * @param {string} keyword - 搜索关键词
   */
  const setKeywordFilter = async (keyword) => {
    currentFilters.value.keyword = keyword
    pagination.value.current = 1 // 重置到第一页
    await fetchTemplates()
  }

  /**
   * 设置分类筛选
   * @param {number} categoryId - 分类ID
   */
  const setCategoryFilter = async (categoryId) => {
    currentFilters.value.categoryId = categoryId
    pagination.value.current = 1 // 重置到第一页
    await fetchTemplates()
  }

  /**
   * 设置行业筛选
   * @param {string} industry - 行业
   */
  const setIndustryFilter = async (industry) => {
    currentFilters.value.industry = industry
    pagination.value.current = 1 // 重置到第一页
    await fetchTemplates()
  }

  /**
   * 设置风格筛选
   * @param {string} style - 风格
   */
  const setStyleFilter = async (style) => {
    currentFilters.value.style = style
    pagination.value.current = 1 // 重置到第一页
    await fetchTemplates()
  }

  /**
   * 设置付费类型筛选
   * @param {number} isPremium - 是否付费 (0:免费, 1:付费, null:全部)
   */
  const setPremiumFilter = async (isPremium) => {
    currentFilters.value.isPremium = isPremium
    pagination.value.current = 1 // 重置到第一页
    await fetchTemplates()
  }

  /**
   * 设置排序方式
   * @param {string} sortBy - 排序方式
   */
  const setSortBy = async (sortBy) => {
    currentFilters.value.sortBy = sortBy
    pagination.value.current = 1 // 重置到第一页
    await fetchTemplates()
  }

  /**
   * 切换页面
   * @param {number} page - 页码
   */
  const changePage = async (page) => {
    if (page >= 1 && page <= pagination.value.pages) {
      pagination.value.current = page
      await fetchTemplates()
    }
  }

  /**
   * 设置每页大小
   * @param {number} size - 每页大小
   */
  const setPageSize = async (size) => {
    pagination.value.size = size
    pagination.value.current = 1 // 重置到第一页
    await fetchTemplates()
  }

  /**
   * 清空所有筛选条件
   */
  const clearFilters = async () => {
    currentFilters.value = {
      keyword: '',
      categoryId: null,
      industry: '',
      style: '',
      colorScheme: '',
      isPremium: null,
      sortBy: 'default'
    }
    pagination.value.current = 1
    await fetchTemplates()
  }

  // ================================
  // 兼容性方法（保持向后兼容）
  // ================================

  /**
   * 设置主分类筛选（兼容旧版本）
   * @param {Object} mainCategory - 主分类对象
   */
  const setMainCategoryFilter = async (mainCategory) => {
    if (mainCategory && mainCategory.id) {
      await setCategoryFilter(mainCategory.id)
    }
  }

  /**
   * 设置副分类筛选（兼容旧版本）
   * @param {Object} filterData - 包含主分类和副分类的对象
   */
  const setSubCategoryFilter = async (filterData) => {
    if (filterData && filterData.subCategory) {
      // 这里可以根据副分类设置更具体的筛选条件
      if (filterData.subCategory.industry) {
        await setIndustryFilter(filterData.subCategory.industry)
      } else if (filterData.subCategory.style) {
        await setStyleFilter(filterData.subCategory.style)
      }
    }
  }

  // ================================
  // 返回对象
  // ================================
  return {
    // 响应式数据
    templates: filteredTemplates,
    loading,
    error,
    pagination,
    currentFilters,

    // API方法
    fetchTemplates,
    fetchTemplateById,
    useTemplate,
    recordTemplateUsage,
    fetchCategories,

    // 筛选和分页方法
    setKeywordFilter,
    setCategoryFilter,
    setIndustryFilter,
    setStyleFilter,
    setPremiumFilter,
    setSortBy,
    changePage,
    setPageSize,
    clearFilters,

    // 兼容性方法
    setMainCategoryFilter,
    setSubCategoryFilter
  }
} 