/**
 * 认证服务
 * @description 处理用户登录、注册、登出等认证相关功能
 * <AUTHOR>
 * @since 1.0.0
 */

import { ref, readonly, computed } from 'vue'
import { HttpClient, API_CONFIG } from '~/utils/api-config'
import { $message } from '../shared/useGlobalMessage'

// ================================
// 响应式数据
// ================================
const isLoggedIn = ref(false)
const currentUser = ref(null)
const loading = ref(false)
const error = ref(null)
const isInitialized = ref(false) // 标记认证状态是否已初始化

// ================================
// HTTP 请求工具
// ================================
const request = async (endpoint, options = {}) => {
  try {
    const method = options.method || 'GET'
    const data = options.body || options.data
    const headers = options.headers || {}
    
    let response
    switch (method.toUpperCase()) {
      case 'GET':
        response = await HttpClient.get(endpoint, data, { headers })
        break
      case 'POST':
        response = await HttpClient.post(endpoint, data, { headers })
        break
      case 'PUT':
        response = await HttpClient.put(endpoint, data, { headers })
        break
      case 'DELETE':
        response = await HttpClient.delete(endpoint, { headers })
        break
      default:
        throw new Error(`不支持的HTTP方法: ${method}`)
    }

    return response
  } catch (err) {
    console.error('API请求失败:', err)
    throw new Error(err.message || '网络请求失败')
  }
}

// ================================
// Token 管理
// ================================
const getStoredToken = () => {
  if (process.client) {
    console.log('🔍 从localStorage获取Token，当前URL:', window.location.href)
    console.log('🔍 localStorage所有项目:', Object.keys(localStorage))
    const token = localStorage.getItem('auth_token')
    console.log('🔍 获取到的auth_token:', token ? `${token.substring(0, 20)}...` : 'null')
    return token
  }
  return null
}

const setStoredToken = (token) => {
  if (process.client) {
    console.log('💾 存储Token到localStorage:', token ? `${token.substring(0, 20)}...` : 'null')
    if (token) {
      localStorage.setItem('auth_token', token)
      
      // 立即验证存储是否成功
      const stored = localStorage.getItem('auth_token')
      console.log('💾 存储验证 - 立即读取:', stored ? `${stored.substring(0, 20)}...` : 'null')
    }
  }
}

const removeStoredToken = () => {
  if (process.client) {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
  }
}

const setStoredUser = (user) => {
  if (process.client) {
    localStorage.setItem('user_info', JSON.stringify(user))
  }
}

const getStoredUser = () => {
  if (process.client) {
    console.log('🔍 从localStorage获取用户信息')
    const userStr = localStorage.getItem('user_info')
    console.log('🔍 获取到的user_info:', userStr ? 'exists' : 'null')
    return userStr ? JSON.parse(userStr) : null
  }
  return null
}

// ================================
// 认证服务方法
// ================================

/**
 * 用户登录（短信验证码）
 * @param {Object} loginData - 登录数据
 * @returns {Promise<Object>} 登录结果
 */
const login = async (loginData) => {
  try {
    loading.value = true
    error.value = null

    // 构建登录请求参数
    const loginRequest = {
      loginType: loginData.loginType || 1, // 默认手机号登录
      platform: 'web',
      phone: loginData.phone,
      smsCode: loginData.smsCode,
      deviceId: generateDeviceId(),
      userAgent: navigator.userAgent
    }

    // 发送登录请求
    const response = await request(API_CONFIG.ENDPOINTS.AUTH.LOGIN, {
      method: 'POST',
      body: loginRequest
    })

    // 存储Token和用户信息（登录接口返回的是accessToken）
    setStoredToken(response.accessToken)
    setStoredUser(response)
    
    // 更新状态
    isLoggedIn.value = true
    currentUser.value = response
    isInitialized.value = true

    return {
      success: true,
      data: response
    }
  } catch (err) {
    error.value = err.message
    return {
      success: false,
      error: err.message
    }
  } finally {
    loading.value = false
  }
}

/**
 * 密码登录
 * @param {Object} loginData - 登录数据 {phone, password, loginType, platform}
 * @returns {Promise<Object>} 登录结果
 */
const passwordLogin = async (loginData) => {
  try {
    loading.value = true
    error.value = null

    // 构建密码登录请求参数
    const loginRequest = {
      phone: loginData.phone,
      password: loginData.password,
      loginType: loginData.loginType || 2, // 密码登录类型
      platform: loginData.platform || 'web'
    }

    // 发送密码登录请求 - 使用正确的API端点
    const response = await request(API_CONFIG.ENDPOINTS.AUTH.PASSWORD_LOGIN, {
      method: 'POST',
      body: loginRequest
    })

    // 处理登录响应 - 根据后端API返回的字段
    if (response && response.token) {
      // 存储Token和用户信息
      setStoredToken(response.token)
      setStoredUser(response)
      
      // 更新状态
      isLoggedIn.value = true
      currentUser.value = response
      isInitialized.value = true

      return {
        success: true,
        data: response
      }
    } else {
      throw new Error(response?.message || '登录失败')
    }
  } catch (err) {
    error.value = err.message
    return {
      success: false,
      error: err.message
    }
  } finally {
    loading.value = false
  }
}

/**
 * 发送验证码
 * @param {string} phone - 手机号
 * @returns {Promise<Object>} 发送结果
 */
const sendSmsCode = async (phone) => {
  try {
    loading.value = true
    error.value = null

    await request(`${API_CONFIG.ENDPOINTS.AUTH.SEND_CODE}?phone=${phone}&platform=web`, {
      method: 'POST'
    })

    return {
      success: true,
      message: '验证码发送成功'
    }
  } catch (err) {
    error.value = err.message
    return {
      success: false,
      error: err.message
    }
  } finally {
    loading.value = false
  }
}

/**
 * 用户注册
 * @param {Object} registerData - 注册数据
 * @returns {Promise<Object>} 注册结果
 */
const register = async (registerData) => {
  try {
    loading.value = true
    error.value = null

    // 构建注册请求参数
    const registerRequest = {
      registerType: 1, // 手机号+密码注册
      phone: registerData.phone,
      password: registerData.password,
      verificationCode: registerData.verifyCode,
      agreeTerms: registerData.agreeTerms || true,
      platform: 'web'
    }

    // 发送注册请求
    const response = await request(API_CONFIG.ENDPOINTS.AUTH.REGISTER, {
      method: 'POST',
      body: registerRequest
    })

    // 存储Token和用户信息（注册接口返回的是accessToken）
    setStoredToken(response.accessToken)
    setStoredUser(response)
    
    // 更新状态
    isLoggedIn.value = true
    currentUser.value = response
    isInitialized.value = true

    return {
      success: true,
      data: response
    }
  } catch (err) {
    error.value = err.message
    return {
      success: false,
      error: err.message
    }
  } finally {
    loading.value = false
  }
}

/**
 * 用户登出
 * @returns {Promise<boolean>} 登出结果
 */
const logout = async () => {
  try {
    const token = getStoredToken()
    if (token) {
      await request(API_CONFIG.ENDPOINTS.AUTH.LOGOUT, {
        method: 'POST'
      })
    }
  } catch (err) {
    console.error('登出请求失败:', err)
  } finally {
    // 无论请求是否成功，都清除本地存储
    removeStoredToken()
    isLoggedIn.value = false
    currentUser.value = null
    // 登出后保持初始化状态（不需要重新初始化）
    isInitialized.value = true
    
    // 显示退出成功消息
    $message.success('退出成功，期待您的再次使用')
  }
  
  return true
}

/**
 * 验证Token有效性
 * @returns {Promise<boolean>} 验证结果
 */
const validateToken = async () => {
  try {
    const token = getStoredToken()
    if (!token) {
      console.log('🔑 没有找到Token，跳过验证')
      return false
    }

    console.log('🔑 开始验证Token，调用接口:', API_CONFIG.ENDPOINTS.AUTH.VALIDATE_TOKEN)
    const response = await request(API_CONFIG.ENDPOINTS.AUTH.VALIDATE_TOKEN, {
      method: 'GET'
    })
    
    console.log('🔑 Token验证响应:', response)
    return response === true
  } catch (err) {
    console.error('🔑 Token验证失败:', err)
    
    // Token验证失败时，清除本地存储的无效认证信息
    console.log('🧹 Token无效，清除本地认证信息')
    removeStoredToken()
    isLoggedIn.value = false
    currentUser.value = null
    
    return false
  }
}

/**
 * 获取用户信息
 * @returns {Promise<Object>} 用户信息
 */
const getUserInfo = async () => {
  try {
    const response = await request(API_CONFIG.ENDPOINTS.USER.PROFILE, {
      method: 'GET'
    })

    return {
      success: true,
      data: response
    }
  } catch (err) {
    console.error('获取用户信息失败:', err)
    return {
      success: false,
      error: err.message
    }
  }
}

/**
 * 更新登录状态
 * @param {Object} userData - 用户数据
 * @param {string} token - 访问令牌
 */
const updateLoginState = (userData, token) => {
  if (token) {
    setStoredToken(token)
  }
  if (userData) {
    setStoredUser(userData)
    currentUser.value = userData
  }
  isLoggedIn.value = true
  // 登录成功也标记为已初始化
  isInitialized.value = true
}

/**
 * 初始化认证状态
 */
const initAuth = async () => {
  try {
    const token = getStoredToken()
    const user = getStoredUser()
    
    if (token && user) {
      console.log('🔄 初始化认证状态，验证Token有效性')
      // 验证Token是否仍然有效
      const isValid = await validateToken()
      if (isValid) {
        console.log('✅ Token验证通过，恢复登录状态')
        isLoggedIn.value = true
        currentUser.value = user
      } else {
        // Token无效时，validateToken方法已经清除了存储和状态
        console.log('❌ Token验证失败，已清除认证信息')
      }
    } else {
      // 没有存储的认证信息
      console.log('📭 没有找到存储的认证信息')
      isLoggedIn.value = false
      currentUser.value = null
    }
  } catch (error) {
    console.error('初始化认证状态失败:', error)
    // 出错时也清除可能的无效数据
    removeStoredToken()
    isLoggedIn.value = false
    currentUser.value = null
  } finally {
    // 标记初始化完成
    isInitialized.value = true
    console.log('🏁 认证状态初始化完成，当前登录状态:', isLoggedIn.value)
  }
}

/**
 * 生成设备ID
 * @returns {string} 设备ID
 */
const generateDeviceId = () => {
  if (process.client) {
    let deviceId = localStorage.getItem('device_id')
    if (!deviceId) {
      deviceId = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      localStorage.setItem('device_id', deviceId)
    }
    return deviceId
  }
  return 'web_' + Date.now()
}

/**
 * 认证服务Hook
 */
export const useAuthService = () => {
  return {
    // 状态
    isLoggedIn: readonly(isLoggedIn),
    currentUser: readonly(currentUser),
    loading: readonly(loading),
    error: readonly(error),
    isInitialized: readonly(isInitialized),

    // 方法
    login,
    passwordLogin,
    register,
    logout,
    sendSmsCode,
    getUserInfo,
    validateToken,
    initAuth,
    updateLoginState,
    
    // Token管理
    getToken: getStoredToken,
    setToken: setStoredToken,
    removeToken: removeStoredToken
  }
} 