package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.membership.MembershipPackageResponse;
import com.alan6.resume.entity.MembershipPackages;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 会员套餐服务简化测试类
 * 主要测试业务逻辑和数据转换，避免Mock框架的复杂性
 * 
 * <AUTHOR>
 * @since 2025-06-22
 */
class MembershipPackagesServiceImplSimpleTest {

    private MembershipPackages testPackage;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testPackage = new MembershipPackages();
        testPackage.setId(1L);
        testPackage.setPackageCode("monthly");
        testPackage.setPackageName("月度会员");
        testPackage.setDescription("月度会员套餐");
        testPackage.setCurrentPrice(new BigDecimal("29.90"));
        testPackage.setOriginalPrice(new BigDecimal("39.90"));
        testPackage.setDurationType((byte) 2); // 月
        testPackage.setDurationValue(1);
        testPackage.setMaxResumeCount(-1);
        testPackage.setMaxExportCount(-1);
        testPackage.setPremiumTemplates((byte) 1);
        testPackage.setIsRecommended((byte) 1);
        testPackage.setStatus((byte) 1);
        testPackage.setIsDeleted((byte) 0);
        testPackage.setCreateTime(LocalDateTime.now());
        testPackage.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 测试参数验证 - 套餐ID为空
     */
    @Test
    void testValidatePackageId_Null() {
        // 创建服务实例
        MembershipPackagesServiceImpl service = new MembershipPackagesServiceImpl();
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> service.getPackageDetail(null));
        
        assertEquals("套餐ID不能为空", exception.getMessage());
    }

    /**
     * 测试套餐代码验证 - 空字符串
     */
    @Test
    void testValidatePackageCode_Empty() {
        // 创建服务实例
        MembershipPackagesServiceImpl service = new MembershipPackagesServiceImpl();
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> service.getByPackageCode(""));
        
        assertEquals("套餐代码不能为空", exception.getMessage());
    }

    /**
     * 测试套餐代码验证 - null
     */
    @Test
    void testValidatePackageCode_Null() {
        // 创建服务实例
        MembershipPackagesServiceImpl service = new MembershipPackagesServiceImpl();
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> service.getByPackageCode(null));
        
        assertEquals("套餐代码不能为空", exception.getMessage());
    }

    /**
     * 测试套餐可用性检查 - null ID
     */
    @Test
    void testIsPackageAvailable_NullId() {
        // 创建服务实例
        MembershipPackagesServiceImpl service = new MembershipPackagesServiceImpl();
        
        // 执行测试
        Boolean result = service.isPackageAvailable(null);
        
        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试MembershipPackageResponse的Builder模式
     */
    @Test
    void testMembershipPackageResponse_Builder() {
        // 使用Builder创建响应对象
        MembershipPackageResponse response = MembershipPackageResponse.builder()
                .id(testPackage.getId())
                .packageName(testPackage.getPackageName())
                .packageCode(testPackage.getPackageCode())
                .description(testPackage.getDescription())
                .currentPrice(testPackage.getCurrentPrice())
                .originalPrice(testPackage.getOriginalPrice())
                .isRecommended(testPackage.getIsRecommended() == 1)
                .premiumTemplates(testPackage.getPremiumTemplates() == 1)
                .build();

        // 验证结果
        assertNotNull(response);
        assertEquals(testPackage.getId(), response.getId());
        assertEquals(testPackage.getPackageName(), response.getPackageName());
        assertEquals(testPackage.getPackageCode(), response.getPackageCode());
        assertEquals(testPackage.getDescription(), response.getDescription());
        assertEquals(testPackage.getCurrentPrice(), response.getCurrentPrice());
        assertEquals(testPackage.getOriginalPrice(), response.getOriginalPrice());
        assertTrue(response.getIsRecommended());
        assertTrue(response.getPremiumTemplates());
    }

    /**
     * 测试时长描述功能
     */
    @Test
    void testDurationDescription() {
        MembershipPackageResponse response = MembershipPackageResponse.builder()
                .durationType((byte) 2)  // 月
                .durationValue(1)
                .build();

        assertEquals("1月", response.getDurationDescription());

        // 测试年
        response.setDurationType((byte) 4);
        response.setDurationValue(1);
        assertEquals("1年", response.getDurationDescription());

        // 测试永久
        response.setDurationType((byte) 5);
        assertEquals("永久", response.getDurationDescription());
    }

    /**
     * 测试折扣检查功能
     */
    @Test
    void testDiscountCheck() {
        MembershipPackageResponse response = MembershipPackageResponse.builder()
                .originalPrice(new BigDecimal("39.90"))
                .currentPrice(new BigDecimal("29.90"))
                .build();

        // 验证有折扣
        assertTrue(response.hasDiscount());
        
        // 验证折扣率计算
        BigDecimal discountRate = response.getDiscountRate();
        assertNotNull(discountRate);
        assertTrue(discountRate.compareTo(BigDecimal.ZERO) > 0);

        // 测试无折扣情况
        response.setCurrentPrice(response.getOriginalPrice());
        assertFalse(response.hasDiscount());
        assertEquals(BigDecimal.ZERO, response.getDiscountRate());
    }

    /**
     * 测试业务逻辑 - 验证套餐状态
     */
    @Test
    void testPackageStatusValidation() {
        // 测试正常状态
        assertEquals((byte) 1, testPackage.getStatus());
        assertEquals((byte) 0, testPackage.getIsDeleted());
        
        // 测试下架状态
        testPackage.setStatus((byte) 0);
        assertEquals((byte) 0, testPackage.getStatus());
        
        // 测试删除状态
        testPackage.setIsDeleted((byte) 1);
        assertEquals((byte) 1, testPackage.getIsDeleted());
    }

    /**
     * 测试价格计算逻辑
     */
    @Test
    void testPriceCalculation() {
        // 验证价格设置
        assertEquals(new BigDecimal("29.90"), testPackage.getCurrentPrice());
        assertEquals(new BigDecimal("39.90"), testPackage.getOriginalPrice());
        
        // 验证价格比较
        assertTrue(testPackage.getOriginalPrice().compareTo(testPackage.getCurrentPrice()) > 0);
        
        // 计算折扣金额
        BigDecimal discount = testPackage.getOriginalPrice().subtract(testPackage.getCurrentPrice());
        assertEquals(new BigDecimal("10.00"), discount);
    }

    /**
     * 测试套餐权益设置
     */
    @Test
    void testPackagePrivileges() {
        // 验证无限制设置
        assertEquals(-1, testPackage.getMaxResumeCount().intValue());
        assertEquals(-1, testPackage.getMaxExportCount().intValue());
        
        // 验证付费模板权限
        assertEquals((byte) 1, testPackage.getPremiumTemplates());
        
        // 验证推荐标识
        assertEquals((byte) 1, testPackage.getIsRecommended());
    }
} 