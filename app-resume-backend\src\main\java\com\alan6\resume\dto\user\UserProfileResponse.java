package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息响应DTO
 * 
 * 主要功能：
 * 1. 返回用户基本信息和设置
 * 2. 包含会员信息和登录记录
 * 3. 对敏感信息进行脱敏处理
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "用户信息响应", description = "用户个人信息详情")
public class UserProfileResponse {

    /**
     * 用户ID
     */
    @Schema(description = "用户的唯一标识ID")
    private Long id;

    /**
     * 用户名
     */
    @Schema(description = "用户的登录用户名")
    private String username;

    /**
     * 邮箱（脱敏）
     */
    @Schema(description = "用户绑定的邮箱地址（脱敏显示）")
    private String email;

    /**
     * 手机号（脱敏）
     */
    @Schema(description = "用户绑定的手机号（脱敏显示）")
    private String phone;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 头像URL
     */
    @Schema(description = "用户的头像地址URL")
    private String avatarUrl;

    /**
     * 性别
     * 0-未知，1-男，2-女
     */
    @Schema(description = "性别 0-未知，1-男，2-女")
    private Byte gender;

    /**
     * 生日
     */
    @Schema(description = "生日")
    private LocalDate birthday;

    /**
     * 注册类型
     * 1-邮箱，2-手机，3-第三方授权
     */
    @Schema(description = "注册类型 1-邮箱，2-手机，3-第三方授权")
    private Byte registerType;

    /**
     * 注册平台
     */
    @Schema(description = "用户首次注册的平台")
    private String registerPlatform;

    /**
     * 界面语言偏好
     */
    @Schema(description = "界面语言偏好")
    private String preferredLanguage;

    /**
     * 账号状态
     * 0-禁用，1-正常
     */
    @Schema(description = "账号状态 0-禁用，1-正常")
    private Byte status;

    /**
     * 手机号是否已验证
     * 0-否，1-是
     */
    @Schema(description = "手机号是否已验证 0-否，1-是")
    private Byte isPhoneVerified;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    /**
     * 最后登录平台
     */
    @Schema(description = "最后登录平台")
    private String lastLoginPlatform;

    /**
     * 会员信息
     */
    @Schema(description = "会员信息")
    private MembershipInfo membership;

    /**
     * 会员信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "会员信息", description = "用户会员相关信息")
    public static class MembershipInfo {

        /**
         * 会员等级
         */
        @Schema(description = "会员等级：0-普通用户，1-基础会员，2-高级会员")
        private Integer level;

        /**
         * 会员到期时间
         */
        @Schema(description = "会员到期时间")
        private LocalDateTime expireTime;

        /**
         * 已使用简历数量
         */
        @Schema(description = "已使用简历数量")
        private Integer usedResumeCount;

        /**
         * 已使用导出次数
         */
        @Schema(description = "已使用导出次数")
        private Integer usedExportCount;
    }

    /**
     * 对手机号进行脱敏处理
     * 
     * @param phone 原始手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 对邮箱进行脱敏处理
     * 
     * @param email 原始邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 2) {
            return email;
        }
        
        String maskedUsername = username.substring(0, 1) + "***" + username.substring(username.length() - 1);
        return maskedUsername + "@" + domain;
    }
} 