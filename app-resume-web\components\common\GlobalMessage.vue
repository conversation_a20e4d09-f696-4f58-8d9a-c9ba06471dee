<template>
  <Teleport to="body">
    <Transition name="message-fade">
      <div 
        v-if="visible && message"
        class="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 min-w-[320px] max-w-[600px] px-4"
      >
        <div 
          :class="[
            'flex items-center px-6 py-4 rounded-lg shadow-lg backdrop-blur-md',
            'border transition-all duration-300',
            messageTypeClasses
          ]"
        >
          <!-- 图标 -->
          <div class="flex-shrink-0 mr-3">
            <svg 
              v-if="type === 'success'" 
              class="w-5 h-5 text-green-600" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <svg 
              v-else-if="type === 'error'" 
              class="w-5 h-5 text-red-600" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <svg 
              v-else-if="type === 'warning'" 
              class="w-5 h-5 text-yellow-600" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.963-.833-2.732 0L3.732 16.5C2.962 18.333 3.924 20 5.464 20z"></path>
            </svg>
            <svg 
              v-else 
              class="w-5 h-5 text-blue-600" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          
          <!-- 消息内容 -->
          <div class="flex-1">
            <p class="text-sm font-medium" :class="textColorClass">{{ message }}</p>
          </div>
          
          <!-- 关闭按钮 -->
          <button 
            @click="close"
            class="flex-shrink-0 ml-4 p-1 rounded-full hover:bg-black/10 transition-colors duration-200"
          >
            <svg class="w-4 h-4" :class="textColorClass" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

// ================================
// Props
// ================================
const props = defineProps({
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
  },
  message: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 4000
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// ================================
// Emits
// ================================
const emit = defineEmits(['close'])

// ================================
// 响应式数据
// ================================
let timer = null

// ================================
// 计算属性
// ================================
const messageTypeClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'bg-green-50/90 border-green-200 text-green-800'
    case 'error':
      return 'bg-red-50/90 border-red-200 text-red-800'
    case 'warning':
      return 'bg-yellow-50/90 border-yellow-200 text-yellow-800'
    default:
      return 'bg-blue-50/90 border-blue-200 text-blue-800'
  }
})

const textColorClass = computed(() => {
  switch (props.type) {
    case 'success':
      return 'text-green-800'
    case 'error':
      return 'text-red-800'
    case 'warning':
      return 'text-yellow-800'
    default:
      return 'text-blue-800'
  }
})

// ================================
// 方法
// ================================
const close = () => {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  emit('close')
}

const startTimer = () => {
  if (timer) {
    clearTimeout(timer)
  }
  
  if (props.duration > 0) {
    timer = setTimeout(() => {
      close()
    }, props.duration)
  }
}

// ================================
// 监听器
// ================================
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.message) {
    startTimer()
  } else {
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
  }
})

// ================================
// 生命周期
// ================================
onMounted(() => {
  if (props.visible && props.message) {
    startTimer()
  }
})
</script>

<style scoped>
.message-fade-enter-active,
.message-fade-leave-active {
  transition: all 0.3s ease;
}

.message-fade-enter-from {
  opacity: 0;
  transform: translate(-50%, -20px);
}

.message-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -20px);
}
</style> 