<template>
  <div class="auth-debug-page">
    <h1>认证调试页面</h1>
    
    <div class="debug-section">
      <h2>LocalStorage 状态</h2>
      <pre>{{ localStorageData }}</pre>
    </div>
    
    <div class="debug-section">
      <h2>Cookie 状态</h2>
      <pre>{{ cookieData }}</pre>
    </div>
    
    <div class="debug-section">
      <h2>API 测试</h2>
      <button @click="testUpload" :disabled="uploading">
        {{ uploading ? '上传中...' : '测试上传API' }}
      </button>
      <pre v-if="uploadResult">{{ uploadResult }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useHtmlConverterService } from '~/composables/admin/useHtmlConverterService'

const localStorageData = ref({})
const cookieData = ref({})
const uploading = ref(false)
const uploadResult = ref(null)

const converterService = useHtmlConverterService()

onMounted(() => {
  // 检查localStorage
  if (process.client) {
    localStorageData.value = {
      admin_token: localStorage.getItem('admin_token') || 'null',
      token: localStorage.getItem('token') || 'null',
      user_info: localStorage.getItem('user_info') || 'null'
    }
    
    // 检查cookies
    cookieData.value = {
      admin_token: document.cookie.includes('admin_token') ? 'exists' : 'null',
      all_cookies: document.cookie
    }
  }
})

const testUpload = async () => {
  uploading.value = true
  uploadResult.value = null
  
  try {
    // 创建一个假的文件进行测试
    const testContent = '<html><body><h1>Test</h1></body></html>'
    const testFile = new File([testContent], 'test.html', { type: 'text/html' })
    
    const result = await converterService.uploadHtmlFiles([testFile])
    uploadResult.value = result
  } catch (error) {
    uploadResult.value = { error: error.message }
  } finally {
    uploading.value = false
  }
}
</script>

<style scoped>
.auth-debug-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.debug-section h2 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
}

button {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style> 