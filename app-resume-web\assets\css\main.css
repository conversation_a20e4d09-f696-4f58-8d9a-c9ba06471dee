@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-secondary-900 bg-white;
  }
  
  * {
    @apply border-secondary-200;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply btn bg-white text-secondary-700 border border-secondary-300 hover:bg-secondary-50 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn bg-transparent text-primary-600 border border-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
  }
  
  .card {
    @apply bg-white rounded-2xl shadow-card border border-secondary-100 overflow-hidden;
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:shadow-soft hover:-translate-y-1 hover:border-primary-200;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }
  
  .section-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .container-width {
    @apply max-w-7xl mx-auto;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
} 