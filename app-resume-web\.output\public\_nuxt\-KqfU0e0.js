import{_ as m}from"./BXnw39BI.js";import{S as b,c as g,a as o,t as e,U as i,b as a,w as d,o as f,d as p}from"./CURHyiUL.js";import{_ as k}from"./DlAUqK2U.js";const $={class:"simple-route-test"},y={class:"current-route"},C={class:"navigation-test"},_={class:"nav-buttons"},N={class:"admin-navigation-test"},S={class:"nav-buttons"},w={class:"direct-links"},T={__name:"simple-route-test",setup(B){const v=b(),r=async n=>{console.log("导航到:",n);try{await v.push(n)}catch(t){console.error("导航失败:",t)}},l=async n=>{console.log("尝试导航到管理后台:",n);try{await v.push(n)}catch(t){console.error("管理后台导航失败:",t),console.log("这是正常的，因为需要登录认证")}};return(n,t)=>{const u=m;return f(),g("div",$,[t[14]||(t[14]=o("h1",null,"简单路由测试页面",-1)),o("div",y,[t[7]||(t[7]=o("h2",null,"当前路由信息",-1)),o("ul",null,[o("li",null,"路径: "+e((n._.provides[i]||n.$route).path),1),o("li",null,"名称: "+e((n._.provides[i]||n.$route).name),1),o("li",null,"参数: "+e(JSON.stringify((n._.provides[i]||n.$route).params)),1),o("li",null,"查询: "+e(JSON.stringify((n._.provides[i]||n.$route).query)),1)])]),o("div",C,[t[8]||(t[8]=o("h2",null,"导航测试（不需要认证）",-1)),o("div",_,[o("button",{onClick:t[0]||(t[0]=s=>r("/")),class:"btn"},"首页"),o("button",{onClick:t[1]||(t[1]=s=>r("/login")),class:"btn"},"登录页"),o("button",{onClick:t[2]||(t[2]=s=>r("/register")),class:"btn"},"注册页")])]),o("div",N,[t[9]||(t[9]=o("h2",null,"管理后台导航测试（需要认证）",-1)),o("div",S,[o("button",{onClick:t[3]||(t[3]=s=>l("/admin")),class:"btn"},"管理首页"),o("button",{onClick:t[4]||(t[4]=s=>l("/admin/template/list")),class:"btn"},"模板列表"),o("button",{onClick:t[5]||(t[5]=s=>l("/admin/template/converter")),class:"btn"},"模板转换"),o("button",{onClick:t[6]||(t[6]=s=>l("/admin/template/upload")),class:"btn"},"模板上传")])]),o("div",w,[t[13]||(t[13]=o("h2",null,"直接链接测试",-1)),o("ul",null,[o("li",null,[a(u,{to:"/"},{default:d(()=>t[10]||(t[10]=[p("首页")])),_:1,__:[10]})]),o("li",null,[a(u,{to:"/login"},{default:d(()=>t[11]||(t[11]=[p("登录页")])),_:1,__:[11]})]),o("li",null,[a(u,{to:"/register"},{default:d(()=>t[12]||(t[12]=[p("注册页")])),_:1,__:[12]})])])]),t[15]||(t[15]=o("div",{class:"route-info"},[o("h2",null,"路由信息"),o("p",null,"当前页面不需要认证，可以正常访问所有公开路由。"),o("p",null,"管理后台路由需要登录后才能访问。")],-1))])}}},O=k(T,[["__scopeId","data-v-ae4da6fb"]]);export{O as default};
