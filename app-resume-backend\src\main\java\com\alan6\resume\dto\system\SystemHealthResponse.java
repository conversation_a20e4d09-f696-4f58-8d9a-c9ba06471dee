package com.alan6.resume.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 系统健康检查响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "SystemHealthResponse", description = "系统健康检查响应")
public class SystemHealthResponse {

    /**
     * 系统状态
     */
    @Schema(description = "系统状态", example = "UP")
    private String status;

    /**
     * 检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检查时间", example = "2024-12-22 17:00:00")
    private LocalDateTime checkTime;

    /**
     * 各组件状态
     */
    @Schema(description = "各组件状态")
    private Map<String, ComponentStatus> components;

    /**
     * 组件状态内部类
     */
    @Data
    @Schema(name = "ComponentStatus", description = "组件状态")
    public static class ComponentStatus {
        
        /**
         * 组件状态
         */
        @Schema(description = "组件状态", example = "UP")
        private String status;
        
        /**
         * 响应时间（毫秒）
         */
        @Schema(description = "响应时间（毫秒）", example = "15")
        private Long responseTime;
        
        /**
         * 详细信息
         */
        @Schema(description = "详细信息")
        private Map<String, Object> details;
    }
} 