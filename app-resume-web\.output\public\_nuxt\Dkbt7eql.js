import{_ as N}from"./BXnw39BI.js";import{u as V,r as g,l as C,i as L,c as r,a as t,q as b,m as T,p as h,v as _,h as s,t as a,s as U,d,b as q,w as B,o as u}from"./CURHyiUL.js";const D={class:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center"},M={class:"bg-white p-8 rounded-lg shadow-lg w-full max-w-md"},O={class:"mb-4"},$={class:"mb-6"},J=["disabled"],R={class:"font-medium mb-2"},j={class:"text-xs overflow-auto"},z={key:0},A={key:1},E={key:0},F={class:"mt-2 text-xs bg-gray-100 p-2 rounded"},H={class:"mt-6 flex space-x-4"},P={key:1,class:"mt-6"},W={__name:"admin-login-test",setup(G){V({title:"管理员登录测试"});const p=g(!1),o=g(null),m=g(!1),l=C({phone:"",password:""}),S={admin:{phone:"13800138001",password:"123456"},superadmin:{phone:"13800138002",password:"123456"}},c=async()=>{if(!l.phone||!l.password){o.value={success:!1,data:{message:"请输入手机号和密码"}};return}p.value=!0,o.value=null;try{const n=await $fetch("/auth/password-login",{method:"POST",baseURL:"http://localhost:9311",body:{phone:l.phone,password:l.password,platform:"web",loginType:1}});o.value={success:!0,data:n},m.value=!0,n.data&&n.data.token&&(localStorage.setItem("auth_token",n.data.token),localStorage.setItem("user_info",JSON.stringify(n.data)))}catch(n){o.value={success:!1,data:n.data||{message:n.message}},m.value=!1}finally{p.value=!1}},f=n=>{const e=S[n];e&&(l.phone=e.phone,l.password=e.password,c())};return L(()=>{localStorage.getItem("auth_token")&&(m.value=!0)}),(n,e)=>{var x,v,y,w,k;const I=N;return u(),r("div",D,[t("div",M,[e[14]||(e[14]=t("div",{class:"text-center mb-8"},[t("h1",{class:"text-2xl font-bold text-gray-900 mb-2"},"管理员登录测试"),t("p",{class:"text-gray-600"},"测试管理员账户登录功能")],-1)),t("form",{onSubmit:T(c,["prevent"])},[t("div",O,[e[4]||(e[4]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 手机号 ",-1)),h(t("input",{"onUpdate:modelValue":e[0]||(e[0]=i=>s(l).phone=i),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"请输入手机号"},null,512),[[_,s(l).phone]])]),t("div",$,[e[5]||(e[5]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 密码 ",-1)),h(t("input",{"onUpdate:modelValue":e[1]||(e[1]=i=>s(l).password=i),type:"password",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"请输入密码"},null,512),[[_,s(l).password]])]),t("button",{type:"submit",disabled:s(p),class:"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"},a(s(p)?"登录中...":"登录"),9,J)],32),s(o)?(u(),r("div",{key:0,class:U(["mt-4 p-4 rounded-md",s(o).success?"bg-green-50 text-green-800":"bg-red-50 text-red-800"])},[t("h4",R,a(s(o).success?"登录成功":"登录失败"),1),t("div",j,[s(o).success?(u(),r("div",z,[t("p",null,[e[6]||(e[6]=t("strong",null,"Token:",-1)),d(" "+a((v=(x=s(o).data)==null?void 0:x.token)==null?void 0:v.substring(0,50))+"...",1)]),t("p",null,[e[7]||(e[7]=t("strong",null,"用户ID:",-1)),d(" "+a((y=s(o).data)==null?void 0:y.userId),1)]),t("p",null,[e[8]||(e[8]=t("strong",null,"昵称:",-1)),d(" "+a((w=s(o).data)==null?void 0:w.nickname),1)]),t("p",null,[e[9]||(e[9]=t("strong",null,"手机号:",-1)),d(" "+a((k=s(o).data)==null?void 0:k.phone),1)])])):(u(),r("div",A,[t("p",null,[e[10]||(e[10]=t("strong",null,"错误信息:",-1)),d(" "+a(s(o).error),1)]),t("p",null,[e[11]||(e[11]=t("strong",null,"状态码:",-1)),d(" "+a(s(o).status),1)]),s(o).details?(u(),r("div",E,[e[12]||(e[12]=t("p",null,[t("strong",null,"详细信息:")],-1)),t("pre",F,a(JSON.stringify(s(o).details,null,2)),1)])):b("",!0)]))])],2)):b("",!0),e[15]||(e[15]=t("div",{class:"mt-6 p-4 bg-blue-50 rounded-md"},[t("h4",{class:"font-medium text-blue-800 mb-2"},"测试说明"),t("div",{class:"text-sm text-blue-700"},[t("p",null,[t("strong",null,"测试账户：")]),t("p",null,"• 管理员: 13800138001 / 123456"),t("p",null,"• 超级管理员: 13800138002 / 123456"),t("p",{class:"mt-2"},[t("strong",null,"注意："),d("需要先在数据库中执行 test-admin-data.sql 初始化数据")])])],-1)),t("div",H,[t("button",{onClick:e[2]||(e[2]=i=>f("admin")),class:"flex-1 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 text-sm"}," 快速登录管理员 "),t("button",{onClick:e[3]||(e[3]=i=>f("superadmin")),class:"flex-1 bg-purple-500 text-white py-2 px-4 rounded-md hover:bg-purple-600 text-sm"}," 快速登录超管 ")]),s(m)?(u(),r("div",P,[q(I,{to:"/admin-test-ui",class:"w-full bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 text-center block"},{default:B(()=>e[13]||(e[13]=[d(" 进入管理后台UI测试 ")])),_:1,__:[13]})])):b("",!0)])])}}};export{W as default};
