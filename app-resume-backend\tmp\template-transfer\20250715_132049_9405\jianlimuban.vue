<template>
  <div class="resume-template" :class="{ 'preview-mode': !isDraggable }">

          <!-- 头部信息 -->
          <header class="header">
              <div class="avatar"  v-if="mergedResumeData.basicInfo.avatar">
                  <img :src="resumeData.basic_info.photo" alt="个人照片">
              </div>
              <div class="name" >{{ mergedResumeData.basicInfo.name }}</div>
              <div class="job-title" >{{ mergedResumeData.basicInfo.jobTitle }}</div>
              <div class="contact-info">
                  <div class="contact-item">
                      <span>📱</span>
                      <span >{{ mergedResumeData.basicInfo.phone }}</span>
                  </div>
                  <div class="contact-item">
                      <span>✉️</span>
                      <span >{{ mergedResumeData.basicInfo.email }}</span>
                  </div>
                  <div class="contact-item">
                      <span>📍</span>
                      <span >{{ mergedResumeData.basicInfo.currentCity }}</span>
                  </div>
                  <div class="contact-item"  v-if="mergedResumeData.basicInfo.wechat">
                      <span>💬</span>
                      <span>{{ mergedResumeData.basicInfo.wechat }}</span>
                  </div>
              </div>
          </header>

          <div class="main-content">
              <!-- 左列 -->
              <div class="left-column">

                  <!-- 技能特长 -->
                  <section>
                      <h2 class="section-title">技能特长</h2>
                      <div class="skills-grid">
                          <div class="skill-item" v-for="skill in mergedResumeData.skills" :key="skill.id">
                              <div class="skill-name" >{{ skill.name }}</div>
                              <div class="skill-level">
                                  <div class="skill-progress"></div>
                              </div>
                          </div>
                      </div>
                  </section>

                  <!-- 语言能力 -->
                  <section>
                      <h2 class="section-title">语言能力</h2>
                      <div class="language-item" v-for="item in mergedResumeData.languages" :key="item.id">
                          <span >{{ item.name }}</span>
                          <span class="language-level" >{{ item.level }}</span>
                      </div>
                  </section>

                  <!-- 证书资质 -->
                  <section>
                      <h2 class="section-title">证书资质</h2>
                      <div class="list-item" v-for="item in mergedResumeData.certificates" :key="item.id">
                          <div class="list-item-title" >{{ item.name }}</div>
                          <div class="list-item-meta" >{{ item.date }}</div>
                      </div>
                  </section>

                  <!-- 兴趣爱好 -->
                  <section>
                      <h2 class="section-title">兴趣爱好</h2>
                      <div class="hobbies-grid">
                          <div class="hobby-item" v-for="item in mergedResumeData.hobbies" :key="item.id">
                              <span >{{ item.name }}</span>
                          </div>
                      </div>
                  </section>

              </div>

              <!-- 右列 -->
              <div class="right-column">

                  <!-- 自我评价 -->
                  <section>
                      <h2 class="section-title">自我评价</h2>
                      <div class="text-content" >{{ mergedResumeData.selfEvaluations.content }}</div>
                  </section>

                  <!-- 工作经历 -->
                  <section>
                      <h2 class="section-title">工作经历</h2>
                      <div class="experience-item" v-for="item in mergedResumeData.workExperiences" :key="item.id">
                          <div class="experience-header">
                              <div>
                                  <div class="experience-title" >{{ item.position }}</div>
                                  <div class="experience-company" >{{ item.company }}</div>
                              </div>
                              <div class="experience-date">
                                  <span >{{ item.startDate }}</span> - 
                                  <span >{{ item.endDate }}</span>
                              </div>
                          </div>
                          <div class="experience-description" >{{ item.description }}</div>
                      </div>
                  </section>

                  <!-- 项目经验 -->
                  <section>
                      <h2 class="section-title">项目经验</h2>
                      <div class="experience-item" v-for="item in mergedResumeData.projects" :key="item.id">
                          <div class="experience-header">
                              <div>
                                  <div class="experience-title" >{{ item.name }}</div>
                                  <div class="experience-company" >{{ item.role }}</div>
                              </div>
                              <div class="experience-date">
                                  <span >{{ item.startDate }}</span> - 
                                  <span >{{ item.endDate }}</span>
                              </div>
                          </div>
                          <div class="experience-description" >{{ item.description }}</div>
                      </div>
                  </section>

                  <!-- 教育经历 -->
                  <section>
                      <h2 class="section-title">教育经历</h2>
                      <div class="experience-item" v-for="item in mergedResumeData.educations" :key="item.id">
                          <div class="experience-header">
                              <div>
                                  <div class="experience-title" >{{ item.school }}</div>
                                  <div class="experience-company">
                                      <span >{{ item.major }}</span> | 
                                      <span >{{ item.degree }}</span>
                                  </div>
                              </div>
                              <div class="experience-date">
                                  <span >{{ item.startDate }}</span> - 
                                  <span >{{ item.endDate }}</span>
                              </div>
                          </div>
                          <div class="experience-description" >{{ item.description }}</div>
                      </div>
                  </section>

                  <!-- 实习经历 -->
                  <section>
                      <h2 class="section-title">实习经历</h2>
                      <div class="experience-item" v-for="item in mergedResumeData.internships" :key="item.id">
                          <div class="experience-header">
                              <div>
                                  <div class="experience-title" >{{ item.position }}</div>
                                  <div class="experience-company" >{{ item.company }}</div>
                              </div>
                              <div class="experience-date">
                                  <span >{{ item.startDate }}</span> - 
                                  <span >{{ item.endDate }}</span>
                              </div>
                          </div>
                          <div class="experience-description" >{{ item.description }}</div>
                      </div>
                  </section>

                  <!-- 获奖荣誉 -->
                  <section>
                      <h2 class="section-title">获奖荣誉</h2>
                      <div class="list-item" v-for="item in mergedResumeData.awards" :key="item.id">
                          <div class="list-item-title" >{{ item.name }}</div>
                          <div class="list-item-meta" >{{ item.date }}</div>
                      </div>
                  </section>

                  <!-- 其他模块（可选）-->

                  <!-- 培训经历 -->
                  <section style="display: none;">
                      <h2 class="section-title">培训经历</h2>
                      <div class="list-item" v-for="item in mergedResumeData.trainings" :key="item.id">
                          <div class="list-item-title" >{{ item.course }}</div>
                          <div class="list-item-meta" >{{ item.date }}</div>
                      </div>
                  </section>

                  <!-- 志愿服务 -->
                  <section style="display: none;">
                      <h2 class="section-title">志愿服务</h2>
                      <div class="experience-item" v-for="item in mergedResumeData.volunteerExperiences" :key="item.id">
                          <div class="experience-header">
                              <div>
                                  <div class="experience-title" >{{ item.role }}</div>
                                  <div class="experience-company" >{{ item.issuer }}</div>
                              </div>
                              <div class="experience-date">
                                  <span >{{ item.startDate }}</span> - 
                                  <span >{{ item.endDate }}</span>
                              </div>
                          </div>
                          <div class="experience-description" >{{ item.description }}</div>
                      </div>
                  </section>

                  <!-- 研究经历 -->
                  <section style="display: none;">
                      <h2 class="section-title">研究经历</h2>
                      <div class="experience-item" v-for="item in mergedResumeData.researchExperiences" :key="item.id">
                          <div class="experience-header">
                              <div>
                                  <div class="experience-title" >{{ item.topic }}</div>
                                  <div class="experience-company" >{{ item.issuer }}</div>
                              </div>
                              <div class="experience-date">
                                  <span >{{ item.startDate }}</span> - 
                                  <span >{{ item.endDate }}</span>
                              </div>
                          </div>
                          <div class="experience-description" >{{ item.description }}</div>
                      </div>
                  </section>

                  <!-- 论文专利 -->
                  <section style="display: none;">
                      <h2 class="section-title">论文专利</h2>
                      <div class="list-item" v-for="item in mergedResumeData.publications" :key="item.id">
                          <div class="list-item-title" >{{ item.name }}</div>
                          <div class="list-item-meta" >{{ item.date }}</div>
                      </div>
                  </section>

                  <!-- 作品集 -->
                  <section style="display: none;">
                      <h2 class="section-title">作品集</h2>
                      <div class="portfolio-item" v-for="item in mergedResumeData.portfolios" :key="item.id">
                          <div class="portfolio-title" >{{ item.name }}</div>
                          <a class="portfolio-link"  href="#">{{ item.url }}</a>
                          <div class="experience-description" >{{ item.description }}</div>
                      </div>
                  </section>

                  <!-- 自荐信 -->
                  <section style="display: none;">
                      <h2 class="section-title">自荐信</h2>
                      <div class="text-content" >{{ mergedResumeData.coverLetters.content }}</div>
                  </section>

                  <!-- 自定义模块 -->
                  <section style="display: none;">
                      <h2 class="section-title">社交主页</h2>
                      <div class="list-item" v-for="item in mergedResumeData.custom" :key="item.id">
                          <div class="list-item-title" >{{ item.name }}</div>
                          <div class="list-item-meta" >{{ item.description }}</div>
                      </div>
                  </section>

              </div>
          </div>
      </div></template>

<script setup>
import { useResumeTemplateBase } from '~/composables/template/useResumeTemplateBase'

// 定义组件属性
const props = defineProps({
  resumeData: {
    type: Object,
    default: () => ({})
  },
  textStyle: {
    type: Object,
    default: () => ({})
  },
  isDraggable: {
    type: Boolean,
    default: false
  },
  visibleModules: {
    type: Array,
    default: () => []
  }
})

// 定义事件
const emit = defineEmits([
  'update-resume-data',
  'update-basic-info',
  'module-operation'
])

// 使用基础组合式函数
const {
  // 数据相关
  mergedResumeData,
  sortedModules,
  leftColumnModules,
  rightColumnModules,
  
  // 样式相关
  mergedTextStyle,
  titleStyle,
  subtitleStyle,
  bodyStyle,
  
  // 操作相关
  hoveredSkill,
  hoveredExperience,
  hoveredEducation,
  hoveredProject,
  moveModuleUp,
  moveModuleDown,
  deleteModule,
  handleBasicInfoUpdate,
  
  // 工具方法
  formatWorkPeriod,
  formatEducationPeriod,
  formatProjectPeriod,
  getSkillProgressStyle
} = useResumeTemplateBase(props, emit)
</script>

<style scoped>
.resume-template * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .resume-template {
            max-width: 210mm;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 297mm;
        }

        /* 头部信息区域 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 20px;
            overflow: hidden;
            background: #ddd;
        }

        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .name {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .job-title {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            font-size: 0.9em;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            min-height: calc(297mm - 200px);
        }

        .left-column {
            width: 35%;
            background: #f8f9fa;
            padding: 30px;
        }

        .right-column {
            width: 65%;
            padding: 30px;
        }

        /* 模块标题 */
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: #764ba2;
        }

        /* 技能模块 */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .skill-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .skill-name {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .skill-level {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            width: 85%;
        }

        /* 语言能力 */
        .language-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .language-item:last-child {
            border-bottom: none;
        }

        .language-level {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        /* 经历项目 */
        .experience-item {
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 1px solid #eee;
            position: relative;
        }

        .experience-item:last-child {
            border-bottom: none;
        }

        .experience-item::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 5px;
            width: 10px;
            height: 10px;
            background: #667eea;
            border-radius: 50%;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .experience-title {
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
        }

        .experience-company {
            color: #667eea;
            font-weight: 500;
        }

        .experience-date {
            color: #666;
            font-size: 0.9em;
        }

        .experience-description {
            margin-top: 10px;
            line-height: 1.6;
        }

        /* 自我评价和自荐信 */
        .text-content {
            line-height: 1.8;
            margin-bottom: 20px;
        }

        /* 获奖证书等列表项 */
        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-item-title {
            font-weight: 500;
            color: #333;
        }

        .list-item-meta {
            color: #666;
            font-size: 0.9em;
        }

        /* 作品集 */
        .portfolio-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .portfolio-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }

        .portfolio-link {
            color: #666;
            text-decoration: none;
            font-size: 0.9em;
        }

        /* 兴趣爱好 */
        .hobbies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
        }

        .hobby-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9em;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* 打印样式 */
        @media print {
            
            .resume-template {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
        }
</style>
