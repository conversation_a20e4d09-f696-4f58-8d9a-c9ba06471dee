package com.alan6.resume.service;

import com.alan6.resume.dto.membership.MembershipPackageResponse;
import com.alan6.resume.entity.MembershipPackages;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 会员套餐表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IMembershipPackagesService extends IService<MembershipPackages> {

    /**
     * 获取会员套餐列表
     * 
     * @param status 套餐状态（可选）
     * @param recommended 是否只返回推荐套餐（可选）
     * @return 套餐列表
     */
    List<MembershipPackageResponse> getPackageList(Byte status, Boolean recommended);

    /**
     * 获取单个套餐详情
     * 
     * @param packageId 套餐ID
     * @return 套餐详情
     */
    MembershipPackageResponse getPackageDetail(Long packageId);

    /**
     * 获取推荐套餐
     * 
     * @return 推荐套餐详情
     */
    MembershipPackageResponse getRecommendedPackage();

    /**
     * 根据套餐代码获取套餐信息
     * 
     * @param packageCode 套餐代码
     * @return 套餐信息
     */
    MembershipPackages getByPackageCode(String packageCode);

    /**
     * 检查套餐是否可用
     * 
     * @param packageId 套餐ID
     * @return true-可用，false-不可用
     */
    Boolean isPackageAvailable(Long packageId);

}
