/**
 * 占位符图片API路由
 * @description 生成SVG占位符图片
 * @route GET /api/admin/placeholder/[width]/[height]
 * <AUTHOR>
 * @since 1.0.0
 */

export default defineEventHandler(async (event) => {
  // 获取路径参数
  const params = getRouterParams(event)
  const pathSegments = params.params as string[]
  
  // 默认尺寸
  let width = 120
  let height = 80
  
  // 解析尺寸参数
  if (pathSegments && pathSegments.length >= 2) {
    width = parseInt(pathSegments[0]) || 120
    height = parseInt(pathSegments[1]) || 80
  }
  
  // 限制尺寸范围
  width = Math.max(50, Math.min(800, width))
  height = Math.max(50, Math.min(600, height))
  
  // 生成SVG占位符
  const svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="#f3f4f6"/>
    <text x="50%" y="50%" text-anchor="middle" dy="0.3em" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af">
      ${width} × ${height}
    </text>
  </svg>`
  
  // 设置响应头
  setHeader(event, 'Content-Type', 'image/svg+xml')
  setHeader(event, 'Cache-Control', 'public, max-age=31536000')
  
  return svg
}) 