package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 简历模块配置请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "简历模块配置请求")
public class ResumeModuleConfigRequest {

    /**
     * 模块配置列表
     */
    @Schema(description = "模块配置列表", required = true)
    @NotNull(message = "模块配置列表不能为空")
    private List<ModuleConfig> modules;

    /**
     * 模块配置项
     */
    @Data
    @Schema(description = "模块配置项")
    public static class ModuleConfig {
        
        /**
         * 模块类型
         */
        @Schema(description = "模块类型", example = "basic_info", required = true)
        @NotBlank(message = "模块类型不能为空")
        @Size(max = 50, message = "模块类型长度不能超过50字符")
        private String moduleType;

        /**
         * 模块名称
         */
        @Schema(description = "模块名称", example = "基本信息", required = true)
        @NotBlank(message = "模块名称不能为空")
        @Size(max = 100, message = "模块名称长度不能超过100字符")
        private String moduleName;

        /**
         * 是否显示
         */
        @Schema(description = "是否显示", example = "1", required = true)
        @NotNull(message = "是否显示不能为空")
        private Byte isVisible;

        /**
         * 排序权重
         */
        @Schema(description = "排序权重", example = "1", required = true)
        @NotNull(message = "排序权重不能为空")
        private Integer sortOrder;

        /**
         * 自定义标题
         */
        @Schema(description = "自定义标题", example = "个人基本信息")
        @Size(max = 100, message = "自定义标题长度不能超过100字符")
        private String customTitle;
    }
} 