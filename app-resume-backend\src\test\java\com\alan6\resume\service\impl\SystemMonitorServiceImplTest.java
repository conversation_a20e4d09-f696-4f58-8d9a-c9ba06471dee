package com.alan6.resume.service.impl;

import com.alan6.resume.dto.system.SystemHealthResponse;
import com.alan6.resume.dto.system.SystemMetricsResponse;
import com.alan6.resume.dto.system.SystemStatusResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Statement;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * SystemMonitorServiceImpl 测试类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SystemMonitorServiceImplTest {

    @Mock
    private DataSource dataSource;

    @Mock
    private Connection connection;

    @Mock
    private DatabaseMetaData metaData;

    @Mock
    private Statement statement;

    @Mock
    private ResultSet resultSet;

    @InjectMocks
    private SystemMonitorServiceImpl systemMonitorService;

    @BeforeEach
    void setUp() throws Exception {
        // 模拟数据库连接
        when(dataSource.getConnection()).thenReturn(connection);
        when(connection.getMetaData()).thenReturn(metaData);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);
        
        // 模拟数据库元数据
        when(metaData.getDatabaseProductName()).thenReturn("MySQL");
        when(metaData.getDatabaseProductVersion()).thenReturn("8.0.33");
    }

    /**
     * 测试系统健康检查 - 成功场景
     */
    @Test
    void testHealthCheck_Success() throws Exception {
        // 执行测试
        SystemHealthResponse result = systemMonitorService.healthCheck();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getCheckTime());
        assertEquals("UP", result.getStatus());
        assertNotNull(result.getComponents());
        
        // 验证各组件状态
        assertTrue(result.getComponents().containsKey("database"));
        assertTrue(result.getComponents().containsKey("redis"));
        assertTrue(result.getComponents().containsKey("minio"));
        assertTrue(result.getComponents().containsKey("disk"));
        
        // 验证数据库组件状态
        SystemHealthResponse.ComponentStatus dbStatus = result.getComponents().get("database");
        assertEquals("UP", dbStatus.getStatus());
        assertNotNull(dbStatus.getResponseTime());
        assertNotNull(dbStatus.getDetails());
        assertEquals("MySQL", dbStatus.getDetails().get("database"));
        assertEquals("8.0.33", dbStatus.getDetails().get("version"));

        // 验证方法调用
        verify(dataSource, times(1)).getConnection();
        verify(connection, times(1)).createStatement();
        verify(statement, times(1)).executeQuery("SELECT 1");
        verify(resultSet, times(1)).close();
    }

    /**
     * 测试系统健康检查 - 数据库连接失败
     */
    @Test
    void testHealthCheck_DatabaseFailure() throws Exception {
        // 模拟数据库连接失败
        when(dataSource.getConnection()).thenThrow(new RuntimeException("Database connection failed"));

        // 执行测试
        SystemHealthResponse result = systemMonitorService.healthCheck();

        // 验证结果
        assertNotNull(result);
        assertEquals("DOWN", result.getStatus()); // 由于数据库DOWN，整体状态应该是DOWN
        
        // 验证数据库组件状态
        SystemHealthResponse.ComponentStatus dbStatus = result.getComponents().get("database");
        assertEquals("DOWN", dbStatus.getStatus());
        assertNotNull(dbStatus.getDetails());
        assertEquals("Database connection failed", dbStatus.getDetails().get("error"));

        // 验证方法调用
        verify(dataSource, times(1)).getConnection();
    }

    /**
     * 测试获取系统状态 - 成功场景
     */
    @Test
    void testGetSystemStatus_Success() throws Exception {
        // 执行测试
        SystemStatusResponse result = systemMonitorService.getSystemStatus();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getCheckTime());
        
        // 验证系统信息
        assertNotNull(result.getSystem());
        assertNotNull(result.getSystem().getOsName());
        assertNotNull(result.getSystem().getOsArch());
        assertNotNull(result.getSystem().getJavaVersion());
        assertTrue(result.getSystem().getCpuCores() > 0);
        assertTrue(result.getSystem().getTotalMemory() > 0);
        assertTrue(result.getSystem().getMemoryUsage() >= 0);

        // 验证应用信息
        assertNotNull(result.getApplication());
        assertEquals("app-resume-backend", result.getApplication().getName());
        assertEquals("1.0.0", result.getApplication().getVersion());
        assertNotNull(result.getApplication().getStartTime());
        assertTrue(result.getApplication().getUptime() >= 0);

        // 验证数据库信息
        assertNotNull(result.getDatabase());
        assertEquals("MySQL", result.getDatabase().getType());
        assertEquals("8.0.33", result.getDatabase().getVersion());
        assertEquals("UP", result.getDatabase().getStatus());
        assertEquals(5, result.getDatabase().getActiveConnections());
        assertEquals(20, result.getDatabase().getMaxConnections());

        // 验证Redis信息
        assertNotNull(result.getRedis());
        assertEquals("7.0.5", result.getRedis().getVersion());
        assertEquals("UP", result.getRedis().getStatus());
        assertEquals(128L, result.getRedis().getUsedMemory());
        assertEquals(1500L, result.getRedis().getKeyCount());
        assertEquals(2L, result.getRedis().getResponseTime());

        // 验证MinIO信息
        assertNotNull(result.getMinio());
        assertEquals("UP", result.getMinio().getStatus());
        assertEquals(4, result.getMinio().getBucketCount());
        assertEquals(2500L, result.getMinio().getObjectCount());
        assertEquals(10240L, result.getMinio().getTotalSize());
        assertEquals(25L, result.getMinio().getResponseTime());

        // 验证方法调用
        verify(dataSource, times(1)).getConnection();
        verify(connection, times(1)).createStatement();
        verify(statement, times(1)).executeQuery("SELECT 1");
    }

    /**
     * 测试获取系统状态 - 数据库连接失败
     */
    @Test
    void testGetSystemStatus_DatabaseFailure() throws Exception {
        // 模拟数据库连接失败
        when(dataSource.getConnection()).thenThrow(new RuntimeException("Database connection failed"));

        // 执行测试
        SystemStatusResponse result = systemMonitorService.getSystemStatus();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDatabase());
        assertEquals("DOWN", result.getDatabase().getStatus());
        assertEquals(0L, result.getDatabase().getResponseTime());

        // 验证方法调用
        verify(dataSource, times(1)).getConnection();
    }

    /**
     * 测试获取系统指标 - 成功场景
     */
    @Test
    void testGetSystemMetrics_Success() {
        // 执行测试
        SystemMetricsResponse result = systemMonitorService.getSystemMetrics();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getCheckTime());

        // 验证性能指标
        assertNotNull(result.getPerformance());
        assertEquals(120.0, result.getPerformance().getAvgResponseTime());
        assertEquals(150.0, result.getPerformance().getQps());
        assertEquals(45.5, result.getPerformance().getCpuUsage());
        assertEquals(68.2, result.getPerformance().getMemoryUsage());
        assertEquals(35.8, result.getPerformance().getDiskUsage());
        assertEquals(1024.0, result.getPerformance().getNetworkIo());
        assertEquals(25.0, result.getPerformance().getDbPoolUsage());
        assertEquals(10, result.getPerformance().getRedisConnections());

        // 验证业务指标
        assertNotNull(result.getBusiness());
        assertEquals(156, result.getBusiness().getOnlineUsers());
        assertEquals(45, result.getBusiness().getTodayNewUsers());
        assertEquals(520, result.getBusiness().getTodayActiveUsers());
        assertEquals(156, result.getBusiness().getTodayResumeCreated());
        assertEquals(89, result.getBusiness().getTodayResumeExported());
        assertEquals(15, result.getBusiness().getTodayOrders());
        assertEquals(1500.00, result.getBusiness().getTodayRevenue());
        assertEquals(8, result.getBusiness().getPendingOrders());

        // 验证错误指标
        assertNotNull(result.getErrors());
        assertEquals(12, result.getErrors().getTodayErrorCount());
        assertEquals(0.8, result.getErrors().getErrorRate());
        assertNotNull(result.getErrors().getRecentErrors());
        assertEquals(2, result.getErrors().getRecentErrors().size());
        
        // 验证最近错误列表
        SystemMetricsResponse.RecentError firstError = result.getErrors().getRecentErrors().get(0);
        assertEquals("BusinessException", firstError.getErrorType());
        assertEquals("文件上传失败", firstError.getMessage());
        assertEquals(3, firstError.getCount());
        assertNotNull(firstError.getLastOccurred());

        SystemMetricsResponse.RecentError secondError = result.getErrors().getRecentErrors().get(1);
        assertEquals("ValidationException", secondError.getErrorType());
        assertEquals("参数验证失败", secondError.getMessage());
        assertEquals(2, secondError.getCount());
        assertNotNull(secondError.getLastOccurred());
    }

    /**
     * 测试系统信息获取
     */
    @Test
    void testGetSystemInfo() throws Exception {
        // 通过getSystemStatus间接测试
        SystemStatusResponse result = systemMonitorService.getSystemStatus();
        
        SystemStatusResponse.SystemInfo systemInfo = result.getSystem();
        assertNotNull(systemInfo);
        
        // 验证操作系统信息
        assertNotNull(systemInfo.getOsName());
        assertNotNull(systemInfo.getOsArch());
        assertNotNull(systemInfo.getJavaVersion());
        
        // 验证CPU和内存信息
        assertTrue(systemInfo.getCpuCores() > 0);
        assertTrue(systemInfo.getTotalMemory() >= 0);
        assertTrue(systemInfo.getUsedMemory() >= 0);
        assertTrue(systemInfo.getAvailableMemory() >= 0);
        assertTrue(systemInfo.getMemoryUsage() >= 0 && systemInfo.getMemoryUsage() <= 100);
    }

    /**
     * 测试应用信息获取
     */
    @Test
    void testGetApplicationInfo() throws Exception {
        // 通过getSystemStatus间接测试
        SystemStatusResponse result = systemMonitorService.getSystemStatus();
        
        SystemStatusResponse.ApplicationInfo appInfo = result.getApplication();
        assertNotNull(appInfo);
        
        // 验证应用基本信息
        assertEquals("app-resume-backend", appInfo.getName());
        assertEquals("1.0.0", appInfo.getVersion());
        assertNotNull(appInfo.getProfile());
        assertNotNull(appInfo.getStartTime());
        assertTrue(appInfo.getUptime() >= 0);
    }

    /**
     * 测试Redis信息获取（模拟）
     */
    @Test
    void testGetRedisInfo() throws Exception {
        // 通过getSystemStatus间接测试
        SystemStatusResponse result = systemMonitorService.getSystemStatus();
        
        SystemStatusResponse.RedisInfo redisInfo = result.getRedis();
        assertNotNull(redisInfo);
        
        // 验证Redis信息
        assertEquals("7.0.5", redisInfo.getVersion());
        assertEquals("UP", redisInfo.getStatus());
        assertEquals(128L, redisInfo.getUsedMemory());
        assertEquals(1500L, redisInfo.getKeyCount());
        assertEquals(2L, redisInfo.getResponseTime());
    }

    /**
     * 测试MinIO信息获取（模拟）
     */
    @Test
    void testGetMinioInfo() throws Exception {
        // 通过getSystemStatus间接测试
        SystemStatusResponse result = systemMonitorService.getSystemStatus();
        
        SystemStatusResponse.MinioInfo minioInfo = result.getMinio();
        assertNotNull(minioInfo);
        
        // 验证MinIO信息
        assertEquals("UP", minioInfo.getStatus());
        assertEquals(4, minioInfo.getBucketCount());
        assertEquals(2500L, minioInfo.getObjectCount());
        assertEquals(10240L, minioInfo.getTotalSize());
        assertEquals(25L, minioInfo.getResponseTime());
    }

    /**
     * 测试磁盘健康检查
     */
    @Test
    void testCheckDiskHealth() {
        // 通过healthCheck间接测试
        SystemHealthResponse result = systemMonitorService.healthCheck();
        
        SystemHealthResponse.ComponentStatus diskStatus = result.getComponents().get("disk");
        assertNotNull(diskStatus);
        
        // 验证磁盘状态（UP 或 WARN）
        assertTrue("UP".equals(diskStatus.getStatus()) || "WARN".equals(diskStatus.getStatus()));
        assertNotNull(diskStatus.getResponseTime());
        assertNotNull(diskStatus.getDetails());
        
        // 验证磁盘详细信息
        assertTrue(diskStatus.getDetails().containsKey("total"));
        assertTrue(diskStatus.getDetails().containsKey("free"));
        assertTrue(diskStatus.getDetails().containsKey("usage"));
    }

    /**
     * 测试性能指标获取
     */
    @Test
    void testGetPerformanceMetrics() {
        // 通过getSystemMetrics间接测试
        SystemMetricsResponse result = systemMonitorService.getSystemMetrics();
        
        SystemMetricsResponse.PerformanceMetrics performance = result.getPerformance();
        assertNotNull(performance);
        
        // 验证所有性能指标都有值
        assertNotNull(performance.getAvgResponseTime());
        assertNotNull(performance.getQps());
        assertNotNull(performance.getCpuUsage());
        assertNotNull(performance.getMemoryUsage());
        assertNotNull(performance.getDiskUsage());
        assertNotNull(performance.getNetworkIo());
        assertNotNull(performance.getDbPoolUsage());
        assertNotNull(performance.getRedisConnections());
        
        // 验证指标值的合理性
        assertTrue(performance.getAvgResponseTime() > 0);
        assertTrue(performance.getQps() > 0);
        assertTrue(performance.getCpuUsage() >= 0 && performance.getCpuUsage() <= 100);
        assertTrue(performance.getMemoryUsage() >= 0 && performance.getMemoryUsage() <= 100);
        assertTrue(performance.getDiskUsage() >= 0 && performance.getDiskUsage() <= 100);
        assertTrue(performance.getDbPoolUsage() >= 0 && performance.getDbPoolUsage() <= 100);
        assertTrue(performance.getRedisConnections() >= 0);
    }

    /**
     * 测试业务指标获取
     */
    @Test
    void testGetBusinessMetrics() {
        // 通过getSystemMetrics间接测试
        SystemMetricsResponse result = systemMonitorService.getSystemMetrics();
        
        SystemMetricsResponse.BusinessMetrics business = result.getBusiness();
        assertNotNull(business);
        
        // 验证所有业务指标都有值
        assertNotNull(business.getOnlineUsers());
        assertNotNull(business.getTodayNewUsers());
        assertNotNull(business.getTodayActiveUsers());
        assertNotNull(business.getTodayResumeCreated());
        assertNotNull(business.getTodayResumeExported());
        assertNotNull(business.getTodayOrders());
        assertNotNull(business.getTodayRevenue());
        assertNotNull(business.getPendingOrders());
        
        // 验证指标值的合理性
        assertTrue(business.getOnlineUsers() >= 0);
        assertTrue(business.getTodayNewUsers() >= 0);
        assertTrue(business.getTodayActiveUsers() >= 0);
        assertTrue(business.getTodayResumeCreated() >= 0);
        assertTrue(business.getTodayResumeExported() >= 0);
        assertTrue(business.getTodayOrders() >= 0);
        assertTrue(business.getTodayRevenue() >= 0);
        assertTrue(business.getPendingOrders() >= 0);
    }

    /**
     * 测试错误指标获取
     */
    @Test
    void testGetErrorMetrics() {
        // 通过getSystemMetrics间接测试
        SystemMetricsResponse result = systemMonitorService.getSystemMetrics();
        
        SystemMetricsResponse.ErrorMetrics errors = result.getErrors();
        assertNotNull(errors);
        
        // 验证错误指标
        assertNotNull(errors.getTodayErrorCount());
        assertNotNull(errors.getErrorRate());
        assertNotNull(errors.getRecentErrors());
        
        // 验证指标值的合理性
        assertTrue(errors.getTodayErrorCount() >= 0);
        assertTrue(errors.getErrorRate() >= 0 && errors.getErrorRate() <= 100);
        assertFalse(errors.getRecentErrors().isEmpty());
        
        // 验证最近错误的结构
        for (SystemMetricsResponse.RecentError error : errors.getRecentErrors()) {
            assertNotNull(error.getErrorType());
            assertNotNull(error.getMessage());
            assertNotNull(error.getCount());
            assertNotNull(error.getLastOccurred());
            assertTrue(error.getCount() > 0);
        }
    }
} 