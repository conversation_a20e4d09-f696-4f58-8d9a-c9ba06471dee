import{r as p,l as i}from"./CURHyiUL.js";const I=()=>{const s=p(!1),o=p(null),n=p(0),d=i({name:"",description:"",categoryId:"",isPremium:0,tags:"",previewFile:null,templateFile:null});return{uploading:s,error:o,uploadProgress:n,uploadInfo:d,uploadTemplate:async(t,e,l=null)=>{s.value=!0,o.value=null,n.value=0;try{console.log("🚀 使用新接口上传模板文件...",{files:t.length,formData:e,hasPreviewImage:!!l});const a=new FormData;a.append("templateCode",e.templateCode),a.append("templateName",e.name),a.append("description",e.description||""),a.append("bucketName",e.bucketName||"resume-templates"),a.append("industry",e.industry||""),a.append("style",e.style||""),a.append("colorScheme",e.colorScheme||""),a.append("isPremium",e.isPremium||0),a.append("price",e.price||0),a.append("sortOrder",e.sortOrder||0),e.categoryIds&&e.categoryIds.length>0&&e.categoryIds.forEach(r=>{a.append("categoryIds",r)}),l&&(console.log("📸 添加预览图到上传数据:",l.name),a.append("previewImage",l),a.append("savePreviewImageUrl","true")),t.forEach((r,u)=>{console.log(`📁 添加文件 ${u+1}:`,r.name),a.append("files",r)});const c=await $fetch("/api/admin/template/upload",{method:"POST",body:a,headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`},onUploadProgress:r=>{n.value=Math.round(r.loaded/r.total*100)}});if(c.code===200)return console.log("✅ 模板上传成功:",c),{success:!0,data:c.data};throw new Error(c.msg||"模板上传失败")}catch(a){return console.error("❌ 模板上传失败:",a),o.value=a.message||"模板上传失败",{success:!1,error:o.value}}finally{s.value=!1,n.value=0}},checkTemplateCode:async t=>{try{const e=await $fetch("/api/admin/template/check-code",{method:"GET",params:{templateCode:t},headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});return e.code===200&&e.data===!0}catch(e){return console.error("检查模板代码失败:",e),!1}},checkTemplateName:async t=>{try{console.log("🔍 检查模板名称可用性:",t);const e=await $fetch("/api/admin/template/check-name",{method:"GET",params:{templateName:t},headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});return console.log("📋 模板名称检查结果:",e),e.data}catch(e){throw console.error("❌ 检查模板名称失败:",e),new Error(e.message||"检查模板名称失败")}},checkBucketStatus:async t=>{try{const e=await $fetch("/api/admin/template/check-bucket",{method:"GET",params:{bucketName:t},headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});return e.code===200&&e.data===!0}catch(e){return console.error("检查存储桶状态失败:",e),!1}},resetUploadInfo:()=>{Object.assign(d,{name:"",description:"",categoryId:"",isPremium:0,tags:"",previewFile:null,templateFile:null}),o.value=null,n.value=0}}};export{I as u};
