package com.alan6.resume.dto.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统配置列表响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "SystemConfigListResponse", description = "系统配置列表响应")
public class SystemConfigListResponse {

    /**
     * 分页数据
     */
    @Schema(description = "分页数据")
    private Page<SystemConfigResponse> records;

    /**
     * 构造函数
     *
     * @param page 分页数据
     */
    public SystemConfigListResponse(Page<SystemConfigResponse> page) {
        this.records = page;
    }

    /**
     * 默认构造函数
     */
    public SystemConfigListResponse() {
    }
} 