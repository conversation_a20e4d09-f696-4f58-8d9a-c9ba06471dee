package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;

/**
 * 简历列表查询请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "简历列表查询请求")
public class ResumeListRequest {

    /**
     * 当前页码，默认为1
     */
    @Schema(description = "当前页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小，默认为10
     */
    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 搜索关键词
     */
    @Schema(description = "搜索关键词", example = "前端开发")
    private String keyword;

    /**
     * 简历状态 (0:草稿, 1:完成)
     */
    @Schema(description = "简历状态", example = "1")
    private Byte status;

    /**
     * 简历语言
     */
    @Schema(description = "简历语言", example = "zh-CN")
    private String language;
} 