import{_ as F}from"./BXnw39BI.js";import{j as G,r as O,k as U,A as N,c as b,o as h,a as e,b as t,w as p,p as C,J as y,h as s,T as S,q as W,s as k,z as Z,ab as Q,aa as X,ai as M,t as T,n as q,u as Y,E as ee}from"./CURHyiUL.js";import{u as R}from"./PsJl1LMp.js";import{I as $}from"./D7AOVZt6.js";import{_ as H}from"./DlAUqK2U.js";import{u as te}from"./CKDEb6Y1.js";import{u as se}from"./CwmtQiCm.js";import"./D1FrdRFX.js";const ne={class:"sidebar-header"},oe={class:"logo-container"},ae={class:"logo-icon"},ie={class:"logo-text"},re={class:"sidebar-menu"},le={class:"menu-nav"},ce={class:"menu-list"},de={class:"menu-item"},ue={class:"menu-text"},me={class:"menu-item"},pe={class:"menu-text"},_e={class:"menu-item"},fe={class:"menu-text"},ve={class:"menu-item has-submenu"},he={class:"menu-text"},we={class:"submenu"},ge={class:"submenu-item"},ke={class:"submenu-item"},be={class:"submenu-item"},xe={class:"submenu-item"},$e={class:"menu-item"},Ae={class:"menu-text"},Ce={class:"menu-item"},ye={class:"menu-text"},Se={key:0,class:"menu-item"},Me={class:"menu-text"},Ee={class:"sidebar-footer"},Ie=["title"],Te={__name:"AdminSidebar",setup(o){const r=G(),{sidebarCollapsed:n,isSuperAdmin:g,toggleSidebar:f,setActiveMenuItem:_}=R(),A=O(!0),l=v=>v==="/admin"?r.path==="/admin":r.path.startsWith(v),d=U(()=>r.path.startsWith("/admin/template")),w=()=>{n.value||(A.value=!A.value)};return N(r,v=>{v.path.startsWith("/admin/template")&&(A.value=!0)},{immediate:!0}),N(n,v=>{v&&(A.value=!1)}),(v,a)=>{const m=F;return h(),b("div",{class:k(["admin-sidebar",{collapsed:s(n)}])},[e("div",ne,[e("div",oe,[e("div",ae,[t($,{name:"star",class:"w-8 h-8 text-blue-500"})]),t(S,{name:"fade"},{default:p(()=>[C(e("span",ie,"火花简历",512),[[y,!s(n)]])]),_:1})])]),e("div",re,[e("nav",le,[e("ul",ce,[e("li",de,[t(m,{to:"/admin",class:k(["menu-link",{active:l("/admin")}]),onClick:a[0]||(a[0]=c=>s(_)("dashboard"))},{default:p(()=>[t($,{name:"chart-bar",class:"menu-icon"}),t(S,{name:"fade"},{default:p(()=>[C(e("span",ue,"仪表盘",512),[[y,!s(n)]])]),_:1})]),_:1},8,["class"])]),e("li",me,[t(m,{to:"/admin/users",class:k(["menu-link",{active:l("/admin/users")}]),onClick:a[1]||(a[1]=c=>s(_)("users"))},{default:p(()=>[t($,{name:"users",class:"menu-icon"}),t(S,{name:"fade"},{default:p(()=>[C(e("span",pe,"用户管理",512),[[y,!s(n)]])]),_:1})]),_:1},8,["class"])]),e("li",_e,[t(m,{to:"/admin/resumes",class:k(["menu-link",{active:l("/admin/resumes")}]),onClick:a[2]||(a[2]=c=>s(_)("resumes"))},{default:p(()=>[t($,{name:"document",class:"menu-icon"}),t(S,{name:"fade"},{default:p(()=>[C(e("span",fe,"简历管理",512),[[y,!s(n)]])]),_:1})]),_:1},8,["class"])]),e("li",ve,[e("div",{class:k(["menu-link submenu-trigger",{active:d.value}]),onClick:w},[t($,{name:"folder",class:"menu-icon"}),t(S,{name:"fade"},{default:p(()=>[C(e("span",he,"模板管理",512),[[y,!s(n)]])]),_:1}),t(S,{name:"fade"},{default:p(()=>[C(t($,{name:"arrow-down",class:k(["submenu-arrow",{"rotate-180":!A.value}])},null,8,["class"]),[[y,!s(n)]])]),_:1})],2),t(S,{name:"submenu"},{default:p(()=>[C(e("ul",we,[e("li",ge,[t(m,{to:"/admin/template/list",class:k(["submenu-link",{active:l("/admin/template/list")}]),onClick:a[3]||(a[3]=c=>s(_)("template-list"))},{default:p(()=>[t($,{name:"document",class:"submenu-icon"}),a[11]||(a[11]=e("span",{class:"submenu-text"},"模板列表",-1))]),_:1,__:[11]},8,["class"])]),e("li",ke,[t(m,{to:"/admin/template/upload",class:k(["submenu-link",{active:l("/admin/template/upload")}]),onClick:a[4]||(a[4]=c=>s(_)("template-upload"))},{default:p(()=>[t($,{name:"plus",class:"submenu-icon"}),a[12]||(a[12]=e("span",{class:"submenu-text"},"模板上传",-1))]),_:1,__:[12]},8,["class"])]),e("li",be,[t(m,{to:"/admin/template/categories",class:k(["submenu-link",{active:l("/admin/template/categories")}]),onClick:a[5]||(a[5]=c=>s(_)("template-categories"))},{default:p(()=>[t($,{name:"folder",class:"submenu-icon"}),a[13]||(a[13]=e("span",{class:"submenu-text"},"分类管理",-1))]),_:1,__:[13]},8,["class"])]),e("li",xe,[t(m,{to:"/admin/template/converter",class:k(["submenu-link",{active:l("/admin/template/converter")}]),onClick:a[6]||(a[6]=c=>s(_)("template-converter"))},{default:p(()=>[t($,{name:"settings",class:"submenu-icon"}),a[14]||(a[14]=e("span",{class:"submenu-text"},"模板转换",-1))]),_:1,__:[14]},8,["class"])])],512),[[y,A.value&&!s(n)]])]),_:1})]),e("li",$e,[t(m,{to:"/admin/orders",class:k(["menu-link",{active:l("/admin/orders")}]),onClick:a[7]||(a[7]=c=>s(_)("orders"))},{default:p(()=>[t($,{name:"shopping-cart",class:"menu-icon"}),t(S,{name:"fade"},{default:p(()=>[C(e("span",Ae,"订单管理",512),[[y,!s(n)]])]),_:1})]),_:1},8,["class"])]),e("li",Ce,[t(m,{to:"/admin/memberships",class:k(["menu-link",{active:l("/admin/memberships")}]),onClick:a[8]||(a[8]=c=>s(_)("memberships"))},{default:p(()=>[t($,{name:"star",class:"menu-icon"}),t(S,{name:"fade"},{default:p(()=>[C(e("span",ye,"会员管理",512),[[y,!s(n)]])]),_:1})]),_:1},8,["class"])]),s(g)?(h(),b("li",Se,[t(m,{to:"/admin/settings",class:k(["menu-link",{active:l("/admin/settings")}]),onClick:a[9]||(a[9]=c=>s(_)("settings"))},{default:p(()=>[t($,{name:"settings",class:"menu-icon"}),t(S,{name:"fade"},{default:p(()=>[C(e("span",Me,"系统设置",512),[[y,!s(n)]])]),_:1})]),_:1},8,["class"])])):W("",!0)])])]),e("div",Ee,[e("button",{onClick:a[10]||(a[10]=(...c)=>s(f)&&s(f)(...c)),class:"collapse-btn",title:s(n)?"展开侧边栏":"折叠侧边栏"},[s(n)?(h(),Z($,{key:1,name:"arrow-down",class:"w-5 h-5"})):(h(),Z($,{key:0,name:"arrow-up",class:"w-5 h-5"}))],8,Ie)])],2)}}},je=H(Te,[["__scopeId","data-v-a0c83941"]]);function Be(o){return Q()?(X(o),!0):!1}const J=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const Le=Object.prototype.toString,Pe=o=>Le.call(o)==="[object Object]",j=()=>{},Oe=Re();function Re(){var o,r;return J&&((o=window==null?void 0:window.navigator)==null?void 0:o.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((r=window==null?void 0:window.navigator)==null?void 0:r.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function V(o){return Array.isArray(o)?o:[o]}function Ve(o,r,n){return N(o,r,{...n,immediate:!0})}const K=J?window:void 0;function P(o){var r;const n=M(o);return(r=n==null?void 0:n.$el)!=null?r:n}function D(...o){const r=[],n=()=>{r.forEach(l=>l()),r.length=0},g=(l,d,w,v)=>(l.addEventListener(d,w,v),()=>l.removeEventListener(d,w,v)),f=U(()=>{const l=V(M(o[0])).filter(d=>d!=null);return l.every(d=>typeof d!="string")?l:void 0}),_=Ve(()=>{var l,d;return[(d=(l=f.value)==null?void 0:l.map(w=>P(w)))!=null?d:[K].filter(w=>w!=null),V(M(f.value?o[1]:o[0])),V(s(f.value?o[2]:o[1])),M(f.value?o[3]:o[2])]},([l,d,w,v])=>{if(n(),!(l!=null&&l.length)||!(d!=null&&d.length)||!(w!=null&&w.length))return;const a=Pe(v)?{...v}:v;r.push(...l.flatMap(m=>d.flatMap(c=>w.map(E=>g(m,c,E,a)))))},{flush:"post"}),A=()=>{_(),n()};return Be(n),A}let z=!1;function De(o,r,n={}){const{window:g=K,ignore:f=[],capture:_=!0,detectIframe:A=!1,controls:l=!1}=n;if(!g)return l?{stop:j,cancel:j,trigger:j}:j;if(Oe&&!z){z=!0;const i={passive:!0};Array.from(g.document.body.children).forEach(u=>u.addEventListener("click",j,i)),g.document.documentElement.addEventListener("click",j,i)}let d=!0;const w=i=>M(f).some(u=>{if(typeof u=="string")return Array.from(g.document.querySelectorAll(u)).some(x=>x===i.target||i.composedPath().includes(x));{const x=P(u);return x&&(i.target===x||i.composedPath().includes(x))}});function v(i){const u=M(i);return u&&u.$.subTree.shapeFlag===16}function a(i,u){const x=M(i),I=x.$.subTree&&x.$.subTree.children;return I==null||!Array.isArray(I)?!1:I.some(L=>L.el===u.target||u.composedPath().includes(L.el))}const m=i=>{const u=P(o);if(i.target!=null&&!(!(u instanceof Element)&&v(o)&&a(o,i))&&!(!u||u===i.target||i.composedPath().includes(u))){if("detail"in i&&i.detail===0&&(d=!w(i)),!d){d=!0;return}r(i)}};let c=!1;const E=[D(g,"click",i=>{c||(c=!0,setTimeout(()=>{c=!1},0),m(i))},{passive:!0,capture:_}),D(g,"pointerdown",i=>{const u=P(o);d=!w(i)&&!!(u&&!i.composedPath().includes(u))},{passive:!0}),A&&D(g,"blur",i=>{setTimeout(()=>{var u;const x=P(o);((u=g.document.activeElement)==null?void 0:u.tagName)==="IFRAME"&&!(x!=null&&x.contains(g.document.activeElement))&&r(i)},0)},{passive:!0})].filter(Boolean),B=()=>E.forEach(i=>i());return l?{stop:B,cancel:()=>{d=!1},trigger:i=>{d=!0,m(i),d=!1}}:B}function Ne(o,r){return h(),b("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"})])}function We(o,r){return h(),b("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"})])}function Ue(o,r){return h(),b("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"})])}function He(o,r){return h(),b("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"})])}function Ze(o,r){return h(),b("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"})])}function qe(o,r){return h(),b("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"})])}function ze(o,r){return h(),b("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function Fe(o,r){return h(),b("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}const Ge={class:"admin-header"},Je={class:"header-left"},Ke={class:"breadcrumb"},Qe={class:"breadcrumb-list"},Xe={class:"breadcrumb-item"},Ye={key:0,class:"breadcrumb-item"},et={class:"breadcrumb-current"},tt={class:"header-right"},st={class:"header-action"},nt={class:"action-btn",title:"通知消息"},ot={key:0,class:"notification-badge"},at={class:"user-avatar"},it=["src","alt"],rt={key:1,class:"avatar-placeholder"},lt={class:"user-info"},ct={class:"user-name"},dt={class:"user-role"},ut={class:"dropdown-menu"},mt={class:"dropdown-header"},pt={class:"dropdown-user-info"},_t={class:"dropdown-name"},ft={class:"dropdown-email"},vt={class:"dropdown-body"},ht={__name:"AdminHeader",setup(o){const r=G();te();const{adminInfo:n,toggleSidebar:g}=R(),f=O(!1),_=O(null),A=O(3),l=U(()=>({"/admin":"","/admin/users":"用户管理","/admin/resumes":"简历管理","/admin/templates":"模板管理","/admin/orders":"订单管理","/admin/memberships":"会员管理","/admin/settings":"系统设置"})[r.path]||""),d=()=>{var m;return(m=n.value)!=null&&m.roles?n.value.roles.includes("SUPER_ADMIN")?"超级管理员":(n.value.roles.includes("ADMIN"),"管理员"):"管理员"},w=()=>{f.value=!f.value},v=()=>{g()},a=async()=>{try{se().clearAdminAuth();const{clearAdminInfo:c}=R();c(),await q("/admin/login")}catch(m){console.error("管理员退出登录失败:",m),await q("/admin/login")}};return De(_,()=>{f.value=!1}),(m,c)=>{var B,i,u;const E=F;return h(),b("header",Ge,[e("div",Je,[e("button",{class:"mobile-menu-btn md:hidden",onClick:v},[t(s(We),{class:"w-6 h-6"})]),e("nav",Ke,[e("ol",Qe,[e("li",Xe,[t(E,{to:"/admin",class:"breadcrumb-link"},{default:p(()=>[t(s(qe),{class:"w-4 h-4"}),c[0]||(c[0]=e("span",{class:"hidden sm:inline"},"管理后台",-1))]),_:1,__:[0]})]),l.value?(h(),b("li",Ye,[t(s(Ze),{class:"w-4 h-4 text-gray-400"}),e("span",et,T(l.value),1)])):W("",!0)])])]),e("div",tt,[e("div",st,[e("button",nt,[t(s(Ue),{class:"w-5 h-5"}),A.value>0?(h(),b("span",ot,T(A.value),1)):W("",!0)])]),e("div",{class:"user-dropdown",ref_key:"dropdownRef",ref:_},[e("button",{class:k(["user-trigger",{active:f.value}]),onClick:w},[e("div",at,[(B=s(n))!=null&&B.avatarUrl?(h(),b("img",{key:0,src:s(n).avatarUrl,alt:s(n).nickname||s(n).username,class:"avatar-img"},null,8,it)):(h(),b("div",rt,[t(s(Fe),{class:"w-5 h-5"})]))]),e("div",lt,[e("span",ct,T(((i=s(n))==null?void 0:i.nickname)||((u=s(n))==null?void 0:u.username)||"管理员"),1),e("span",dt,T(d()),1)]),t(s(He),{class:k(["w-4 h-4 transition-transform duration-200",{"rotate-180":f.value}])},null,8,["class"])],2),t(S,{name:"dropdown"},{default:p(()=>{var x,I,L;return[C(e("div",ut,[e("div",mt,[e("div",pt,[e("p",_t,T(((x=s(n))==null?void 0:x.nickname)||((I=s(n))==null?void 0:I.username)),1),e("p",ft,T(((L=s(n))==null?void 0:L.email)||"未设置邮箱"),1)])]),c[3]||(c[3]=e("div",{class:"dropdown-divider"},null,-1)),e("div",vt,[t(E,{to:"/profile",class:"dropdown-item"},{default:p(()=>[t(s(ze),{class:"w-4 h-4"}),c[1]||(c[1]=e("span",null,"个人资料",-1))]),_:1,__:[1]}),e("button",{onClick:a,class:"dropdown-item"},[t(s(Ne),{class:"w-4 h-4"}),c[2]||(c[2]=e("span",null,"退出登录",-1))])])],512),[[y,f.value]])]}),_:1})],512)])])}}},wt=H(ht,[["__scopeId","data-v-a7606516"]]),gt={class:"admin-layout"},kt={class:"admin-content"},bt={__name:"admin",setup(o){const{sidebarCollapsed:r}=R();return Y({title:"后台管理 - 火花简历",meta:[{name:"description",content:"火花简历后台管理系统"}]}),(n,g)=>{const f=je,_=wt;return h(),b("div",gt,[t(f),e("div",{class:k(["admin-main",{"sidebar-collapsed":s(r)}])},[t(_),e("div",kt,[ee(n.$slots,"default",{},void 0,!0)])],2)])}}},It=H(bt,[["__scopeId","data-v-91530cde"]]);export{It as default};
