package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 简历预览请求DTO
 * 
 * @description 用于保存简历预览状态的请求参数
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "简历预览请求参数")
public class ResumePreviewRequest {

    /**
     * 简历ID
     */
    @NotNull(message = "简历ID不能为空")
    @Schema(description = "简历ID", required = true)
    private Long resumeId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", required = true)
    private Long userId;

    /**
     * 导出格式
     */
    @Schema(description = "导出格式（pdf/word/image）")
    private String format;

    /**
     * 简历数据
     * 包含完整的简历内容和样式设置
     */
    @Schema(description = "简历数据")
    private ResumeExportData resumeData;

    /**
     * 导出选项
     */
    @Schema(description = "导出选项")
    private ExportOptions exportOptions;

    /**
     * 简历导出数据
     */
    @Data
    @Schema(description = "简历导出数据")
    public static class ResumeExportData {
        /**
         * 基础信息
         */
        @Schema(description = "基础信息")
        private Map<String, Object> basicInfo;

        /**
         * 模块列表
         */
        @Schema(description = "模块列表")
        private List<ResumeSaveRequest.ModuleData> modules;

        /**
         * 模块配置
         */
        @Schema(description = "模块配置")
        private List<ModuleConfig> moduleConfigs;

        /**
         * 主题设置
         */
        @Schema(description = "主题设置")
        private ResumeSaveRequest.ThemeSettings theme;

        /**
         * 布局设置
         */
        @Schema(description = "布局设置")
        private ResumeSaveRequest.LayoutSettings layout;

        /**
         * 排版设置
         */
        @Schema(description = "排版设置")
        private ResumeSaveRequest.TypographySettings typography;

        /**
         * 页面设置
         */
        @Schema(description = "页面设置")
        private ResumeSaveRequest.PageSettings page;
    }

    /**
     * 模块配置
     */
    @Data
    @Schema(description = "模块配置")
    public static class ModuleConfig {
        @Schema(description = "模块ID")
        private String id;

        @Schema(description = "模块类型")
        private String type;

        @Schema(description = "模块名称")
        private String name;

        @Schema(description = "是否启用")
        private Boolean enabled;

        @Schema(description = "排序顺序")
        private Integer order;

        @Schema(description = "自定义标题")
        private String customTitle;
    }

    /**
     * 导出选项
     */
    @Data
    @Schema(description = "导出选项")
    public static class ExportOptions {
        /**
         * 是否包含水印
         */
        @Schema(description = "是否包含水印", defaultValue = "false")
        private Boolean includeWatermark = false;

        /**
         * 导出质量
         */
        @Schema(description = "导出质量（low/medium/high）", defaultValue = "high")
        private String quality = "high";

        /**
         * 页面大小
         */
        @Schema(description = "页面大小（A4/Letter等）", defaultValue = "A4")
        private String pageSize = "A4";

        /**
         * 页面方向
         */
        @Schema(description = "页面方向（portrait/landscape）", defaultValue = "portrait")
        private String orientation = "portrait";

        /**
         * 缩放比例
         */
        @Schema(description = "缩放比例", defaultValue = "1.0")
        private Double scale = 1.0;
    }
} 