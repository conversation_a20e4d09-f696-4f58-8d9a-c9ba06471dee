package com.alan6.resume.mapper;

import com.alan6.resume.entity.ResumeExportQueue;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 简历导出队列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ResumeExportQueueMapper extends BaseMapper<ResumeExportQueue> {

    /**
     * 根据jobId查询导出任务
     */
    @Select("SELECT * FROM resume_export_queue WHERE job_id = #{jobId}")
    ResumeExportQueue selectByJobId(@Param("jobId") String jobId);

    /**
     * 根据jobId更新任务状态
     */
    @Update("UPDATE resume_export_queue SET status = #{status}, update_time = NOW() WHERE job_id = #{jobId}")
    int updateStatusByJobId(@Param("jobId") String jobId, @Param("status") String status);

    /**
     * 根据jobId更新下载链接和文件大小
     */
    @Update("UPDATE resume_export_queue SET download_url = #{downloadUrl}, file_size = #{fileSize}, status = 'completed', completed_at = NOW(), update_time = NOW() WHERE job_id = #{jobId}")
    int updateDownloadInfoByJobId(@Param("jobId") String jobId, @Param("downloadUrl") String downloadUrl, @Param("fileSize") Long fileSize);

    /**
     * 根据jobId更新下载链接
     */
    @Update("UPDATE resume_export_queue SET download_url = #{downloadUrl}, status = 'completed', completed_at = NOW(), update_time = NOW() WHERE job_id = #{jobId}")
    int updateDownloadInfoByJobId(@Param("jobId") String jobId, @Param("downloadUrl") String downloadUrl);

    /**
     * 根据jobId更新错误信息
     */
    @Update("UPDATE resume_export_queue SET error_message = #{errorMessage}, status = 'failed', attempts = attempts + 1, update_time = NOW() WHERE job_id = #{jobId}")
    int updateErrorByJobId(@Param("jobId") String jobId, @Param("errorMessage") String errorMessage);

    /**
     * 获取待处理的任务（按优先级和创建时间排序）
     */
    @Select("SELECT * FROM resume_export_queue WHERE status = 'queued' AND attempts < max_attempts ORDER BY priority ASC, create_time ASC LIMIT #{limit}")
    List<ResumeExportQueue> selectPendingJobs(@Param("limit") int limit);

    /**
     * 根据用户ID查询导出记录
     */
    @Select("SELECT * FROM resume_export_queue WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<ResumeExportQueue> selectByUserId(@Param("userId") Long userId);

} 