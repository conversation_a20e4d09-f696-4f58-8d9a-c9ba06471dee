package com.alan6.resume.common.exception;

/**
 * 业务异常类
 * 
 * 主要功能：
 * 1. 封装业务逻辑中的异常情况
 * 2. 提供错误码和错误消息
 * 3. 支持业务场景的异常链传递
 * 
 * 使用场景：
 * - 业务规则验证失败
 * - 数据状态不符合预期
 * - 业务流程中断
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
public class BusinessException extends RuntimeException {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private int errorCode;

    /**
     * 默认构造函数
     */
    public BusinessException() {
        super();
        this.errorCode = 400;
    }

    /**
     * 构造函数：仅包含错误消息
     * 
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.errorCode = 400;
    }

    /**
     * 构造函数：包含错误码和错误消息
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     */
    public BusinessException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造函数：包含错误消息和原因异常
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = 400;
    }

    /**
     * 构造函数：包含错误码、错误消息和原因异常
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public BusinessException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public int getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误码
     * 
     * @param errorCode 错误码
     */
    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 重写toString方法，提供更详细的异常信息
     * 
     * @return 异常信息字符串
     */
    @Override
    public String toString() {
        return String.format("BusinessException{errorCode=%d, message='%s'}", 
                            errorCode, getMessage());
    }
} 