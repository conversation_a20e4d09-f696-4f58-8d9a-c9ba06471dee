package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 分享状态切换请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareStatusRequest", description = "分享状态切换请求")
public class ShareStatusRequest {

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    @NotNull(message = "激活状态不能为空")
    private Boolean isActive;
} 