package com.alan6.resume.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量操作分享状态请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "ShareBatchStatusRequest", description = "批量操作分享状态请求")
public class ShareBatchStatusRequest {

    /**
     * 分享记录ID数组
     */
    @Schema(description = "分享记录ID数组", example = "[6001, 6002, 6003]", required = true)
    @NotEmpty(message = "分享ID列表不能为空")
    private List<Long> shareIds;

    /**
     * 目标状态
     */
    @Schema(description = "目标状态（true:启用，false:禁用）", example = "true", required = true)
    @NotNull(message = "目标状态不能为空")
    private Boolean isActive;
} 