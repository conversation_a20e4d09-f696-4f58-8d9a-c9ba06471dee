package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 文件上传记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("file_upload_records")
@Schema(name = "FileUploadRecords对象", description = "文件上传记录表")
public class FileUploadRecords implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文件ID")
    private Long id;

    @Schema(description = "上传用户ID")
    private Long userId;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "文件类型（MIME类型）")
    private String fileType;

    @Schema(description = "MinIO存储桶名称")
    private String bucketName;

    @Schema(description = "MinIO对象键（完整路径）")
    private String objectKey;

    @Schema(description = "MinIO对象ETag（用于完整性验证）")
    private String etag;

    @Schema(description = "对象版本ID（支持版本控制）")
    private String versionId;

    @Schema(description = "存储类别（STANDARD/REDUCED_REDUNDANCY/COLD）")
    private String storageClass;

    @Schema(description = "内容编码（gzip/deflate等）")
    private String contentEncoding;

    @Schema(description = "缓存控制指令")
    private String cacheControl;

    @Schema(description = "文件过期时间")
    private LocalDateTime expiresAt;

    @Schema(description = "访问策略（private/public-read/public-read-write）")
    private String accessPolicy;

    @Schema(description = "预签名URL过期时间")
    private LocalDateTime presignedUrlExpires;

    @Schema(description = "加密类型（SSE-S3/SSE-KMS/SSE-C）")
    private String encryptionType;

    @Schema(description = "自定义元数据（JSON格式）")
    private String metadata;

    @Schema(description = "上传平台")
    private String uploadPlatform;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
