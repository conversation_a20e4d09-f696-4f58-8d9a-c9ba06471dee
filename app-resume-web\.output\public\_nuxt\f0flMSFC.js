import{e as m,r as p,c as s,f as _,a as t,F as n,g as c,h,o,s as d,t as i}from"./CURHyiUL.js";const u={class:"pt-16"},f={class:"py-20"},v={class:"container-width section-padding"},g={class:"space-y-12"},y={class:"flex items-center mb-6"},b={class:"w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg mr-4"},w={class:"font-display font-bold text-2xl text-secondary-900"},k={class:"text-lg text-secondary-600 mb-6"},M={class:"space-y-2"},z={class:"text-secondary-700"},B={class:"bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl p-8 h-64 flex items-center justify-center"},T={class:"text-center"},j={class:"w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4"},C={class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},H=["d"],S={class:"text-secondary-600"},D={__name:"tutorial",setup(V){m({title:"使用教程 - 火花简历",description:"学习如何使用火花简历制作专业简历"});const x=p([{id:1,title:"选择模板",description:"从150+精美模板中选择适合您行业和风格的模板。",points:["浏览不同分类的模板","预览模板效果","选择最适合的设计风格"],icon:"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z",imageText:"模板选择界面"},{id:2,title:"填写信息",description:"使用我们的智能编辑器，轻松填写您的个人信息、工作经历、教育背景等。",points:["智能表单引导填写","实时预览效果","AI智能推荐内容"],icon:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z",imageText:"信息编辑器"},{id:3,title:"预览调整",description:"实时预览简历效果，调整布局和内容，确保简历完美呈现。",points:["实时预览功能","拖拽调整布局","字体和颜色自定义"],icon:"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z",imageText:"预览界面"},{id:4,title:"导出分享",description:"一键导出为PDF、Word等格式，或生成在线分享链接。",points:["多格式导出支持","在线分享功能","下载管理"],icon:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4",imageText:"导出选项"}]);return(F,a)=>(o(),s("div",u,[a[1]||(a[1]=_('<section class="bg-gradient-to-br from-primary-50 to-orange-50 py-16"><div class="container-width section-padding"><div class="text-center max-w-3xl mx-auto"><h1 class="font-display font-bold text-4xl lg:text-5xl text-secondary-900 mb-6"> 使用教程 </h1><p class="text-xl text-secondary-600"> 快速上手火花简历，轻松制作专业简历 </p></div></div></section>',1)),t("section",f,[t("div",v,[t("div",g,[(o(!0),s(n,null,c(h(x),(e,r)=>(o(),s("div",{key:e.id,class:"flex flex-col lg:flex-row items-center gap-12"},[t("div",{class:d(["flex-1 order-2 lg:order-1",{"lg:order-2":r%2===1}])},[t("div",y,[t("div",b,i(r+1),1),t("h3",w,i(e.title),1)]),t("p",k,i(e.description),1),t("ul",M,[(o(!0),s(n,null,c(e.points,l=>(o(),s("li",{key:l,class:"flex items-start"},[a[0]||(a[0]=t("svg",{class:"w-5 h-5 text-success-500 mr-3 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)),t("span",z,i(l),1)]))),128))])],2),t("div",{class:d(["flex-1 order-1 lg:order-2",{"lg:order-1":r%2===1}])},[t("div",B,[t("div",T,[t("div",j,[(o(),s("svg",C,[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:e.icon},null,8,H)]))]),t("p",S,i(e.imageText),1)])])],2)]))),128))])])])]))}};export{D as default};
