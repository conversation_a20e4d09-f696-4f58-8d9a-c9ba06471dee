package com.alan6.resume;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;

import java.sql.Types;
import java.util.Collections;

public class CodeGenerator {
/*    public static void main(String[] args) {
        FastAutoGenerator.create("*********************************************************************************",
                        "root", "123456")
                .globalConfig(builder -> {
                    builder.author("Alan6") // 作者
                            .enableSwagger() // 开启 swagger 注解
                            .outputDir(System.getProperty("user.dir") + "/src/main/java"); // 输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.alan6.resume") // 包名
                            .moduleName(null) // 模块名（可为空）
                            .pathInfo(Collections.singletonMap(OutputFile.xml,
                                    System.getProperty("user.dir") + "/src/main/resources/mapper")); // mapper.xml 路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude("file_upload_records", "membership_packages", "orders", "resume_awards", "resume_basic_info", "resume_certificates", "resume_cover_letters", "resume_educations", "resume_export_records", "resume_hobbies", "resume_internships", "resume_languages", "resume_module_configs", "resume_portfolios", "resume_projects", "resume_publications", "resume_references", "resume_research_experiences", "resume_self_evaluations", "resume_skills", "resume_templates", "resume_trainings", "resume_volunteer_experiences", "resume_work_experiences", "resumes", "share_records", "supported_languages", "system_configs", "template_categories", "user_access_logs", "user_favorites", "user_memberships", "user_merge_records", "user_operation_logs", "user_third_party_auths", "users") // 生成的表名
                            .addTablePrefix("t_", "sys_") // 表名前缀过滤
                            .entityBuilder().enableLombok() // 实体类使用 Lombok
                            .controllerBuilder().enableRestStyle(); // Rest 风格
                })
                .templateEngine(new VelocityTemplateEngine()) // 使用默认模板引擎
                .execute();
    }*/
}

