package com.alan6.resume.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单列表查询响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@Schema(name = "OrderListResponse", description = "订单列表查询响应")
public class OrderListResponse {

    /**
     * 总数量
     */
    @Schema(description = "总数量", example = "25")
    private Long total;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码", example = "1")
    private Integer page;

    /**
     * 每页数量
     */
    @Schema(description = "每页数量", example = "10")
    private Integer size;

    /**
     * 总页数
     */
    @Schema(description = "总页数", example = "3")
    private Integer pages;

    /**
     * 订单列表
     */
    @Schema(description = "订单列表")
    private List<OrderItem> orders;

    /**
     * 订单项
     */
    @Data
    @Schema(name = "OrderItem", description = "订单项")
    public static class OrderItem {
        
        /**
         * 订单号
         */
        @Schema(description = "订单号", example = "ORD20241222123456789")
        private String orderNo;

        /**
         * 订单类型
         */
        @Schema(description = "订单类型", example = "membership")
        private String orderType;

        /**
         * 产品名称
         */
        @Schema(description = "产品名称", example = "年度会员套餐")
        private String productName;

        /**
         * 订单金额
         */
        @Schema(description = "订单金额", example = "199.90")
        private BigDecimal amount;

        /**
         * 货币类型
         */
        @Schema(description = "货币类型", example = "CNY")
        private String currency;

        /**
         * 订单状态
         */
        @Schema(description = "订单状态", example = "completed", 
                allowableValues = {"pending", "paid", "completed", "cancelled", "expired", "refunded"})
        private String status;

        /**
         * 支付方式
         */
        @Schema(description = "支付方式", example = "wechat")
        private String paymentMethod;

        /**
         * 支付状态
         */
        @Schema(description = "支付状态", example = "paid")
        private String paymentStatus;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间", example = "2024-12-22 15:00:00")
        private LocalDateTime createTime;

        /**
         * 支付时间
         */
        @Schema(description = "支付时间", example = "2024-12-22 15:10:30")
        private LocalDateTime paymentTime;

        /**
         * 过期时间
         */
        @Schema(description = "过期时间", example = "2024-12-22 15:30:00")
        private LocalDateTime expireTime;
    }
} 