package com.alan6.resume.dto.resume;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 工作经历请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "工作经历请求")
public class ResumeWorkExperienceRequest {

    /**
     * 公司名称
     */
    @Schema(description = "公司名称", example = "阿里巴巴集团", required = true)
    @NotBlank(message = "公司名称不能为空")
    @Size(min = 1, max = 100, message = "公司名称长度必须在1-100字符之间")
    private String companyName;

    /**
     * 职位
     */
    @Schema(description = "职位", example = "前端开发工程师", required = true)
    @NotBlank(message = "职位不能为空")
    @Size(min = 1, max = 100, message = "职位长度必须在1-100字符之间")
    private String position;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2020-07-01", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2023-12-31")
    private LocalDate endDate;

    /**
     * 是否在职
     */
    @Schema(description = "是否在职", example = "0", required = true)
    @NotNull(message = "是否在职不能为空")
    private Byte isCurrent;

    /**
     * 工作描述
     */
    @Schema(description = "工作描述", example = "负责电商平台前端开发和维护，参与多个重要项目的架构设计和开发工作")
    private String jobDescription;

    /**
     * 主要成就
     */
    @Schema(description = "主要成就", example = "完成了多个重要项目的前端开发工作，获得团队技术创新奖")
    private String achievements;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重", example = "1", required = true)
    @NotNull(message = "排序权重不能为空")
    private Integer sortOrder;
} 