/**
 * API配置管理工具
 * @description 统一管理API基础配置和请求工具
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// API配置常量
// ================================
const API_CONFIG = {
  // 默认超时时间（毫秒）
  DEFAULT_TIMEOUT: 10000,
  
  // 请求重试次数
  MAX_RETRY_COUNT: 3,
  
  // API版本
  API_VERSION: 'v1',
  
  // 接口端点定义
  ENDPOINTS: {
    // 认证相关接口
    AUTH: {
      REGISTER: '/auth/register',                    // 用户注册
      LOGIN: '/auth/login-sms',                     // 短信验证码登录
      PASSWORD_LOGIN: '/auth/password-login',        // 密码登录
      LOGOUT: '/auth/logout',                       // 用户登出
      SEND_CODE: '/auth/send-code',                 // 发送验证码
      REFRESH_TOKEN: '/auth/refresh-token',         // 刷新Token
      VALIDATE_TOKEN: '/auth/validate-token'        // 验证Token
    },
    
    // 用户相关接口
    USER: {
      PROFILE: '/user/profile',                     // 用户信息
      UPDATE_PROFILE: '/user/update-profile',       // 更新用户信息
      CHANGE_PASSWORD: '/user/change-password',     // 修改密码
      BIND_EMAIL: '/user/bind-email',              // 绑定邮箱
      UPLOAD_AVATAR: '/user/upload-avatar'          // 上传头像
    },
    
    // 简历相关接口
    RESUME: {
      LIST: '/resume/list',                         // 简历列表
      CREATE: '/resume/create',                     // 创建简历
      UPDATE: '/resume/update',                     // 更新简历
      DELETE: '/resume/delete',                     // 删除简历
      DETAIL: '/resume/detail'                      // 简历详情
    },
    
    // 模板相关接口
    TEMPLATE: {
      LIST: '/template/list',                       // 模板列表
      DETAIL: '/template/detail',                   // 模板详情
      CATEGORIES: '/template/categories'            // 模板分类
    },
    
    // 管理员相关接口
    ADMIN: {
      LOGIN: '/auth/login-admin',                   // 管理员登录
      CHECK_AUTH: '/admin/check-auth',              // 检查管理员权限
      INFO: '/admin/info'                           // 获取管理员信息
    }
  }
}

// ================================
// API基础地址管理
// ================================
class ApiBaseManager {
  /**
   * 获取API基础地址
   * 优先级：开发代理 > 环境变量 > 运行时配置 > 默认值
   * @returns {string} API基础地址
   */
  static getApiBase() {
    try {
      // 开发环境使用代理
      if (process.client && process.dev) {
        console.log('🔧 开发环境，使用代理地址: /api')
        return '/api'
      }
      
      // 优先使用运行时配置
      if (process.client) {
        const runtimeConfig = useRuntimeConfig()
        if (runtimeConfig?.public?.apiBase) {
          console.log('🔧 使用运行时配置API地址:', runtimeConfig.public.apiBase)
          return runtimeConfig.public.apiBase
        }
      }
      
      // 其次使用环境变量
      if (process.env.NUXT_PUBLIC_API_BASE) {
        console.log('🔧 使用环境变量API地址:', process.env.NUXT_PUBLIC_API_BASE)
        return process.env.NUXT_PUBLIC_API_BASE
      }
      
      // 最后使用默认值
      console.log('🔧 使用默认API地址: http://localhost:9311')
      return 'http://localhost:9311'
      
    } catch (error) {
      console.warn('获取API基础地址失败，使用默认值:', error.message)
      // 开发环境出错时也尝试使用代理
      if (process.client && process.dev) {
        console.log('🔧 错误处理：开发环境使用代理地址: /api')
        return '/api'
      }
      console.log('🔧 错误处理：使用默认地址: http://localhost:9311')
      return 'http://localhost:9311'
    }
  }

  /**
   * 构建完整的API地址
   * @param {string} endpoint - 接口端点
   * @returns {string} 完整的API地址
   */
  static buildApiUrl(endpoint) {
    const baseUrl = this.getApiBase()
    
    // 确保端点以/开头
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
    
    // 移除baseUrl末尾的斜杠（如果有）
    const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl
    
    return `${normalizedBaseUrl}${normalizedEndpoint}`
  }

  /**
   * 获取当前API配置信息
   * @returns {Object} API配置信息
   */
  static getApiInfo() {
    return {
      baseUrl: this.getApiBase(),
      version: API_CONFIG.API_VERSION,
      timeout: API_CONFIG.DEFAULT_TIMEOUT,
      endpoints: API_CONFIG.ENDPOINTS
    }
  }
}

// ================================
// HTTP请求工具类
// ================================
class HttpClient {
  /**
   * 统一请求方法
   * @param {string} endpoint - 接口端点
   * @param {Object} options - 请求配置
   * @returns {Promise} 请求结果
   */
  static async request(endpoint, options = {}) {
    try {
      const url = ApiBaseManager.buildApiUrl(endpoint)
      
      // 获取认证Token
      const token = this.getAuthToken()
      
      // 构建请求头
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      }
      
      // 添加认证头
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      // 构建请求配置
      const requestConfig = {
        ...options,
        headers,
        timeout: options.timeout || API_CONFIG.DEFAULT_TIMEOUT
      }
      
      // 发送请求
      const response = await $fetch(url, requestConfig)
      
      // 处理响应
      return this.handleResponse(response)
      
    } catch (error) {
      console.error('API请求失败:', {
        endpoint,
        error: error.message,
        status: error.status,
        data: error.data
      })
      
      // 统一错误处理
      throw this.handleError(error)
    }
  }

  /**
   * GET请求
   * @param {string} endpoint - 接口端点
   * @param {Object} params - 查询参数
   * @param {Object} options - 请求配置
   * @returns {Promise} 请求结果
   */
  static async get(endpoint, params = {}, options = {}) {
    return this.request(endpoint, {
      method: 'GET',
      params,
      ...options
    })
  }

  /**
   * POST请求
   * @param {string} endpoint - 接口端点
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求配置
   * @returns {Promise} 请求结果
   */
  static async post(endpoint, data = {}, options = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: data,
      ...options
    })
  }

  /**
   * PUT请求
   * @param {string} endpoint - 接口端点
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求配置
   * @returns {Promise} 请求结果
   */
  static async put(endpoint, data = {}, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: data,
      ...options
    })
  }

  /**
   * DELETE请求
   * @param {string} endpoint - 接口端点
   * @param {Object} options - 请求配置
   * @returns {Promise} 请求结果
   */
  static async delete(endpoint, options = {}) {
    return this.request(endpoint, {
      method: 'DELETE',
      ...options
    })
  }

  /**
   * 获取认证Token
   * @returns {string|null} 认证Token
   */
  static getAuthToken() {
    if (process.client) {
      const token = localStorage.getItem('auth_token')
      console.log('🔑 获取到的Token:', token ? `${token.substring(0, 10)}...` : 'null')
      console.log('🔑 Token完整长度:', token ? token.length : 0)
      return token
    }
    return null
  }

  /**
   * 处理响应数据
   * @param {Object} response - 响应对象
   * @returns {Object} 处理后的数据
   */
  static handleResponse(response) {
    // 检查响应格式
    if (response && typeof response === 'object') {
      // 统一响应格式：{ code, msg, data }
      if (response.code !== undefined) {
        if (response.code === 200) {
          return response.data || response
        } else {
          throw new Error(response.msg || response.message || '请求失败')
        }
      }
      
      // 直接返回数据
      return response
    }
    
    return response
  }

  /**
   * 统一错误处理
   * @param {Error} error - 错误对象
   * @returns {Error} 处理后的错误
   */
  static handleError(error) {
    // 网络错误
    if (!error.status) {
      return new Error('网络连接失败，请检查网络设置')
    }
    
    // HTTP状态码错误
    switch (error.status) {
      case 401:
        // 未授权，可能需要重新登录
        if (process.client) {
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user_info')
        }
        return new Error('登录已过期，请重新登录')
      
      case 403:
        return new Error('没有权限访问此资源')
      
      case 404:
        return new Error('请求的资源不存在')
      
      case 429:
        return new Error('请求过于频繁，请稍后再试')
      
      case 500:
        return new Error('服务器内部错误，请稍后再试')
      
      default:
        return new Error(error.data?.message || error.message || '请求失败')
    }
  }
}

// ================================
// 导出API配置和工具
// ================================
export {
  API_CONFIG,
  ApiBaseManager,
  HttpClient
}

// 默认导出HTTP客户端
export default HttpClient 