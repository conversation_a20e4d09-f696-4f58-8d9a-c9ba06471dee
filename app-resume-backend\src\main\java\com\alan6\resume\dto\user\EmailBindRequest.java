package com.alan6.resume.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 邮箱绑定/解绑请求DTO
 * 
 * 主要功能：
 * 1. 处理邮箱绑定和解绑的请求参数
 * 2. 验证邮箱格式和邮箱验证码
 * 3. 确保操作的安全性
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Schema(name = "邮箱绑定请求", description = "邮箱绑定/解绑的请求参数")
public class EmailBindRequest {

    /**
     * 邮箱地址
     * 绑定时必填，解绑时可选
     */
    @Schema(description = "邮箱地址")
    @Pattern(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式不正确")
    private String email;

    /**
     * 邮箱验证码
     * 6位数字验证码
     */
    @Schema(description = "邮箱验证码")
    @NotBlank(message = "邮箱验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确，应为6位数字")
    private String emailCode;

    /**
     * 验证绑定邮箱的参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isBindValid() {
        return email != null && !email.trim().isEmpty() 
               && emailCode != null && !emailCode.trim().isEmpty();
    }

    /**
     * 验证解绑邮箱的参数是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isUnbindValid() {
        return emailCode != null && !emailCode.trim().isEmpty();
    }
} 