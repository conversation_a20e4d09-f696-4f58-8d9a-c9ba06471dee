package com.alan6.resume.service.impl;

import com.alan6.resume.config.MinioConfig;
import com.alan6.resume.service.IMinioService;
import io.minio.*;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.concurrent.TimeUnit;

/**
 * MinIO对象存储服务实现类
 * 
 * @description 实现MinIO文件上传、下载、删除等操作
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MinioServiceImpl implements IMinioService {

    private final MinioClient minioClient;
    private final MinioConfig.MinioProperties minioProperties;

    /**
     * 上传文件
     */
    @Override
    public String uploadFile(byte[] fileData, String fileName, String contentType) {
        log.info("上传文件到MinIO，文件名：{}，类型：{}，大小：{} bytes", fileName, contentType, fileData.length);
        
        try {
            // 确保存储桶存在
            ensureBucketExists();
            
            // 上传文件
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .stream(new ByteArrayInputStream(fileData), fileData.length, -1)
                    .contentType(contentType)
                    .build();
            
            ObjectWriteResponse response = minioClient.putObject(putObjectArgs);
            
            // 生成文件访问URL
            String fileUrl = String.format("%s/%s/%s", 
                    minioProperties.getEndpoint(), 
                    minioProperties.getBucketName(), 
                    fileName);
            
            log.info("文件上传成功，URL：{}", fileUrl);
            return fileUrl;
            
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new RuntimeException("上传文件失败：" + e.getMessage(), e);
        }
    }

    /**
     * 下载文件
     */
    @Override
    public byte[] downloadFile(String fileName) {
        log.info("从MinIO下载文件，文件名：{}", fileName);
        
        try {
            GetObjectArgs getObjectArgs = GetObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .build();
            
            try (GetObjectResponse response = minioClient.getObject(getObjectArgs)) {
                byte[] fileData = response.readAllBytes();
                log.info("文件下载成功，大小：{} bytes", fileData.length);
                return fileData;
            }
            
        } catch (Exception e) {
            log.error("下载文件失败", e);
            throw new RuntimeException("下载文件失败：" + e.getMessage(), e);
        }
    }

    /**
     * 删除文件
     */
    @Override
    public boolean deleteFile(String fileName) {
        log.info("从MinIO删除文件，文件名：{}", fileName);
        
        try {
            RemoveObjectArgs removeObjectArgs = RemoveObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .build();
            
            minioClient.removeObject(removeObjectArgs);
            log.info("文件删除成功");
            return true;
            
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     */
    @Override
    public boolean fileExists(String fileName) {
        log.debug("检查文件是否存在，文件名：{}", fileName);
        
        try {
            StatObjectArgs statObjectArgs = StatObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .build();
            
            StatObjectResponse response = minioClient.statObject(statObjectArgs);
            return response != null;
            
        } catch (Exception e) {
            log.debug("文件不存在或检查失败：{}", fileName);
            return false;
        }
    }

    /**
     * 获取文件URL
     */
    @Override
    public String getFileUrl(String fileName) {
        return String.format("%s/%s/%s", 
                minioProperties.getEndpoint(), 
                minioProperties.getBucketName(), 
                fileName);
    }

    /**
     * 获取预签名上传URL
     */
    @Override
    public String getPresignedUploadUrl(String fileName, int expiry) {
        log.info("生成预签名上传URL，文件名：{}，过期时间：{}秒", fileName, expiry);
        
        try {
            GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                    .method(Method.PUT)
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .expiry(expiry, TimeUnit.SECONDS)
                    .build();
            
            String presignedUrl = minioClient.getPresignedObjectUrl(args);
            log.info("预签名上传URL生成成功：{}", presignedUrl);
            return presignedUrl;
            
        } catch (Exception e) {
            log.error("生成预签名上传URL失败", e);
            throw new RuntimeException("生成预签名上传URL失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取预签名下载URL
     */
    @Override
    public String getPresignedDownloadUrl(String fileName, int expiry) {
        log.info("生成预签名下载URL，文件名：{}，过期时间：{}秒", fileName, expiry);
        
        try {
            GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .expiry(expiry, TimeUnit.SECONDS)
                    .build();
            
            String presignedUrl = minioClient.getPresignedObjectUrl(args);
            log.info("预签名下载URL生成成功：{}", presignedUrl);
            return presignedUrl;
            
        } catch (Exception e) {
            log.error("生成预签名下载URL失败", e);
            throw new RuntimeException("生成预签名下载URL失败：" + e.getMessage(), e);
        }
    }

    /**
     * 确保存储桶存在
     */
    private void ensureBucketExists() {
        try {
            String bucketName = minioProperties.getBucketName();
            
            BucketExistsArgs bucketExistsArgs = BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build();
            
            boolean exists = minioClient.bucketExists(bucketExistsArgs);
            
            if (!exists) {
                log.info("存储桶不存在，创建存储桶：{}", bucketName);
                
                MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build();
                
                minioClient.makeBucket(makeBucketArgs);
                log.info("存储桶创建成功：{}", bucketName);
            } else {
                log.debug("存储桶已存在：{}", bucketName);
            }
            
        } catch (Exception e) {
            log.error("检查或创建存储桶失败", e);
            throw new RuntimeException("检查或创建存储桶失败：" + e.getMessage(), e);
        }
    }
} 