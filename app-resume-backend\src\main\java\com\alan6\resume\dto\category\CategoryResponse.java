package com.alan6.resume.dto.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 分类响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@Schema(description = "分类响应DTO")
public class CategoryResponse {

    @Schema(description = "分类ID")
    private Long id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "分类代码")
    private String code;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "父分类ID（0为顶级分类）")
    private Long parentId;

    @Schema(description = "分类类型")
    private String categoryType;

    @Schema(description = "分类图标URL")
    private String iconUrl;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "状态（0:禁用,1:启用）")
    private Byte status;

    @Schema(description = "模板数量")
    private Integer templateCount;

    @Schema(description = "子分类列表")
    private List<CategoryResponse> children;
} 