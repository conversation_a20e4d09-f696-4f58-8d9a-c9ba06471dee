package com.alan6.resume.common.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alan6.resume.common.constants.AuthConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Token生成和管理工具类
 * 
 * 主要功能：
 * 1. 生成安全的随机Token
 * 2. 解析和验证Token格式
 * 3. 提供Token相关的辅助方法
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Component
public class TokenUtils {

    /**
     * 生成随机Token
     * 
     * 生成策略：
     * - 使用当前时间戳（毫秒）作为前缀，确保时间唯一性
     * - 使用随机字符串作为后缀，增加随机性
     * - 总长度为32位，符合系统设计要求
     * 
     * @return 32位随机Token字符串
     */
    public String generateToken() {
        try {
            // 获取当前时间戳（毫秒），确保时间唯一性
            long timestamp = System.currentTimeMillis();
            
            // 将时间戳转换为16进制字符串，减少长度
            String timestampHex = Long.toHexString(timestamp);
            
            // 计算需要的随机字符串长度，确保总长度为32
            int randomLength = AuthConstants.Token.TOKEN_LENGTH - timestampHex.length();
            
            // 生成随机字符串，使用数字和字母（不区分大小写）
            String randomStr = RandomUtil.randomString("0123456789abcdef", randomLength);
            
            // 拼接时间戳和随机字符串
            String token = timestampHex + randomStr;
            
            log.debug("生成Token成功，长度: {}", token.length());
            
            return token;
            
        } catch (Exception e) {
            log.error("生成Token失败", e);
            
            // 如果生成失败，使用纯随机字符串作为备选方案
            return RandomUtil.randomString("0123456789abcdef", AuthConstants.Token.TOKEN_LENGTH);
        }
    }

    /**
     * 从HTTP请求头中提取Token
     * 
     * @param authorizationHeader HTTP请求头中的Authorization字段值
     * @return 提取出的Token字符串，如果无效则返回null
     */
    public String extractTokenFromHeader(String authorizationHeader) {
        // 检查请求头是否为空
        if (StrUtil.isBlank(authorizationHeader)) {
            log.debug("Authorization请求头为空");
            return null;
        }
        
        // 检查是否以Bearer开头
        if (!authorizationHeader.startsWith(AuthConstants.Token.TOKEN_PREFIX)) {
            log.debug("Authorization请求头格式错误，缺少Bearer前缀");
            return null;
        }
        
        // 提取Token字符串
        String token = authorizationHeader.substring(AuthConstants.Token.TOKEN_PREFIX.length()).trim();
        
        // 验证Token格式
        if (!isValidTokenFormat(token)) {
            log.debug("Token格式无效: {}", token);
            return null;
        }
        
        log.debug("从请求头提取Token成功");
        return token;
    }

    /**
     * 验证Token格式是否有效
     * 
     * @param token 待验证的Token字符串
     * @return true-格式有效，false-格式无效
     */
    public boolean isValidTokenFormat(String token) {
        // 检查Token是否为空
        if (StrUtil.isBlank(token)) {
            return false;
        }
        
        // 检查Token长度
        if (token.length() != AuthConstants.Token.TOKEN_LENGTH) {
            log.debug("Token长度错误，期望: {}, 实际: {}", AuthConstants.Token.TOKEN_LENGTH, token.length());
            return false;
        }
        
        // 检查Token字符组成（只允许数字和小写字母）
        if (!token.matches("^[0-9a-f]+$")) {
            log.debug("Token包含非法字符: {}", token);
            return false;
        }
        
        return true;
    }
}