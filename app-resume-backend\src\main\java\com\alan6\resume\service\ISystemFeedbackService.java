package com.alan6.resume.service;

import com.alan6.resume.dto.system.FeedbackRequest;
import com.alan6.resume.dto.system.FeedbackResponse;
import com.alan6.resume.entity.SystemFeedback;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 * 系统反馈表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ISystemFeedbackService extends IService<SystemFeedback> {

    /**
     * 提交反馈
     *
     * @param request 反馈请求
     * @param userId  用户ID（可为空，表示匿名反馈）
     * @return 反馈提交结果
     */
    Map<String, Object> submitFeedback(FeedbackRequest request, Long userId);

    /**
     * 获取反馈列表（分页）
     *
     * @param type   反馈类型（可选）
     * @param status 处理状态（可选）
     * @param page   页码
     * @param size   每页数量
     * @return 反馈列表
     */
    Page<FeedbackResponse> getFeedbackList(String type, String status, Integer page, Integer size);

    /**
     * 获取反馈详情
     *
     * @param feedbackId 反馈ID
     * @return 反馈详情
     */
    FeedbackResponse getFeedbackDetail(Long feedbackId);

    /**
     * 处理反馈
     *
     * @param feedbackId   反馈ID
     * @param handlerId    处理人ID
     * @param handlerReply 处理回复
     * @param status       新状态
     * @return 处理结果
     */
    boolean handleFeedback(Long feedbackId, Long handlerId, String handlerReply, String status);

    /**
     * 生成反馈编号
     *
     * @return 反馈编号
     */
    String generateFeedbackId();
} 