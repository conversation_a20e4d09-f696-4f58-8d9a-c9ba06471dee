<template>
  <div class="test-page">
    <h1>HTML规范页面测试</h1>
    
    <div class="test-section">
      <h2>页面链接测试</h2>
      <NuxtLink to="/admin/template/html-specification" class="test-link">
        访问HTML规范页面
      </NuxtLink>
    </div>
    
    <div class="test-section">
      <h2>转换页面链接测试</h2>
      <NuxtLink to="/admin/template/converter" class="test-link">
        访问转换页面（包含规范按钮）
      </NuxtLink>
    </div>
    
    <div class="test-section">
      <h2>功能验证清单</h2>
      <ul class="test-checklist">
        <li>✅ HTML规范页面创建完成</li>
        <li>✅ 转换页面添加规范按钮</li>
        <li>✅ 页面样式和布局正确</li>
        <li>✅ 包含完整的规范文档</li>
        <li>✅ 包含示例代码和验证清单</li>
      </ul>
    </div>
    
    <div class="test-section">
      <h2>规范内容概览</h2>
      <div class="content-overview">
        <div class="overview-item">
          <h3>1. 基本文档结构</h3>
          <p>DOCTYPE、HTML标签、字符编码等基本要求</p>
        </div>
        <div class="overview-item">
          <h3>2. 模块定义规范</h3>
          <p>data-module属性、支持的模块类型</p>
        </div>
        <div class="overview-item">
          <h3>3. 字段绑定规范</h3>
          <p>data-field、data-vue-text等属性使用</p>
        </div>
        <div class="overview-item">
          <h3>4. 各模块字段详解</h3>
          <p>基本信息、工作经历、教育经历等模块字段</p>
        </div>
        <div class="overview-item">
          <h3>5. 完整示例代码</h3>
          <p>可直接使用的HTML模板示例</p>
        </div>
        <div class="overview-item">
          <h3>6. 验证清单</h3>
          <p>转换前的检查项目</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'default'
})
</script>

<style scoped>
.test-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.test-link {
  display: inline-block;
  padding: 10px 20px;
  background: #3498db;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  transition: background 0.2s;
}

.test-link:hover {
  background: #2980b9;
}

.test-checklist {
  list-style: none;
  padding: 0;
}

.test-checklist li {
  padding: 8px 0;
  font-size: 16px;
}

.content-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.overview-item h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.overview-item p {
  margin: 0;
  color: #6c757d;
}
</style> 