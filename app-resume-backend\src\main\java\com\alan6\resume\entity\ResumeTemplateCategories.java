package com.alan6.resume.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 模板分类关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
@Setter
@TableName("resume_template_categories")
@Schema(name = "ResumeTemplateCategories对象", description = "模板分类关联表")
public class ResumeTemplateCategories implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "模板ID")
    private Long templateId;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "是否删除（0:未删除,1:已删除）")
    @TableLogic
    private Byte isDeleted;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 