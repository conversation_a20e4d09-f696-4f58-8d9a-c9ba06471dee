<template>
  <div class="custom-form">
    <div class="form-header">
      <h4 class="form-title">自定义内容</h4>
      <p class="form-desc">添加您的个性化简历内容</p>
    </div>
    
    <div class="form-content">
      <!-- 内容项列表 -->
      <div v-if="formData.items && formData.items.length > 0" class="items-list">
        <div
          v-for="(item, index) in formData.items"
          :key="index"
          class="item-card"
        >
          <div class="item-header">
            <!-- 名称和角色在同一行 -->
            <div class="form-group">
              <label class="form-label">名称</label>
              <input
                v-model="item.name"
                type="text"
                class="form-input"
                placeholder="请输入名称"
                @input="handleItemChange"
              />
            </div>
            <div class="form-group">
              <label class="form-label">角色</label>
              <input
                v-model="item.role"
                type="text"
                class="form-input"
                placeholder="请输入担任的角色"
                @input="handleItemChange"
              />
            </div>
            <button
              type="button"
              class="delete-btn"
              @click="removeItem(index)"
              title="删除此项"
            >
              ×
            </button>
          </div>
          
          <div class="item-details">
            
            <!-- 时间 -->
            <div class="form-group">
              <label class="form-label">时间</label>
              <div class="date-range-group">
                <div class="date-picker-wrapper">
                  <input
                    v-model="item.startDate"
                    type="text"
                    class="date-input"
                    placeholder="2023-01"
                    readonly
                    @click="showDatePicker(`${index}_start`)"
                  />
                  <div
                    v-if="activeDatePicker === `${index}_start`"
                    class="date-picker-dropdown"
                  >
                    <div class="date-picker-header">
                      <button type="button" @click="changeYear(-1)">‹</button>
                      <span>{{ currentYear }}</span>
                      <button type="button" @click="changeYear(1)">›</button>
                    </div>
                    <div class="month-grid">
                      <button
                        v-for="month in 12"
                        :key="month"
                        type="button"
                        class="month-btn"
                        :class="{ active: isSelectedMonth(`${index}_start`, month) }"
                        @click="selectDate(`${index}_start`, currentYear, month)"
                      >
                        {{ month }}月
                      </button>
                    </div>
                  </div>
                </div>
                
                <span class="date-separator">至</span>
                
                <div class="date-picker-wrapper">
                  <input
                    v-model="item.endDate"
                    type="text"
                    class="date-input"
                    placeholder="2024-06 或 至今"
                    readonly
                    @click="showDatePicker(`${index}_end`)"
                  />
                  <div
                    v-if="activeDatePicker === `${index}_end`"
                    class="date-picker-dropdown"
                  >
                    <div class="date-picker-header">
                      <button type="button" @click="changeYear(-1)">‹</button>
                      <span>{{ currentYear }}</span>
                      <button type="button" @click="changeYear(1)">›</button>
                    </div>
                    <div class="month-grid">
                      <button
                        v-for="month in 12"
                        :key="month"
                        type="button"
                        class="month-btn"
                        :class="{ active: isSelectedMonth(`${index}_end`, month) }"
                        @click="selectDate(`${index}_end`, currentYear, month)"
                      >
                        {{ month }}月
                      </button>
                      <button
                        type="button"
                        class="month-btn present-btn"
                        @click="selectPresent(`${index}_end`)"
                      >
                        至今
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 内容描述 -->
            <div class="form-group">
              <label class="form-label">内容描述</label>
              <RichTextEditor
                v-model="item.content"
                :placeholder="'请输入详细内容...'"
                @update="handleItemChange"
              />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <Icon name="plus" size="lg" class="empty-icon" />
        <p class="empty-text">暂无内容项</p>
        <p class="empty-desc">点击下方按钮添加您的第一个内容项</p>
      </div>
      
      <!-- 添加按钮 -->
      <button
        type="button"
        class="add-btn"
        @click="addItem"
      >
        <Icon name="plus" size="sm" />
        <span>添加内容项</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, computed, watch, onMounted, onUnmounted } from 'vue'
import Icon from '~/components/common/Icon.vue'
import RichTextEditor from '~/components/common/RichTextEditor.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ items: [] })
  }
})

const emit = defineEmits(['update'])

// 响应式数据
const formData = reactive({
  items: []
})

const activeDatePicker = ref('')
const currentYear = ref(new Date().getFullYear())

// 年份选项
const years = computed(() => {
  const currentY = new Date().getFullYear()
  const years = []
  for (let i = currentY + 2; i >= currentY - 50; i--) {
    years.push(i)
  }
  return years
})

let debounceTimer = null

// 监听props数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.items && Array.isArray(newData.items)) {
    formData.items = newData.items.map(item => ({
      name: item.name || '',
      role: item.role || '',
      startDate: item.startDate || '',
      endDate: item.endDate || '',
      isOngoing: item.isOngoing || false,
      content: item.content || ''
    }))
  } else {
    formData.items = []
  }
}, { immediate: true, deep: true })

// 方法定义
const addItem = () => {
  const newItem = {
    name: '',
    role: '',
    startDate: '',
    endDate: '',
    isOngoing: false,
    content: ''
  }
  formData.items.push(newItem)
  emitUpdate()
}

const removeItem = (index) => {
  if (confirm('确定要删除这个内容项吗？')) {
    formData.items.splice(index, 1)
    emitUpdate()
  }
}

const handleItemChange = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  debounceTimer = setTimeout(() => {
    emitUpdate()
  }, 300)
}

const emitUpdate = () => {
  emit('update', { items: formData.items })
}

// 时间选择器相关方法
const showDatePicker = (pickerId) => {
  activeDatePicker.value = pickerId
  // 根据当前值设置年份
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.items[index].startDate 
    : formData.items[index].endDate
  
  if (currentValue && currentValue !== '至今') {
    const year = parseInt(currentValue.split('-')[0])
    if (year) {
      currentYear.value = year
    }
  }
}

const changeYear = (delta) => {
  currentYear.value += delta
}

const selectDate = (pickerId, year, month) => {
  const dateStr = `${year}-${String(month).padStart(2, '0')}`
  const [index, type] = pickerId.split('_')
  
  if (type === 'start') {
    formData.items[index].startDate = dateStr
  } else {
    formData.items[index].endDate = dateStr
    formData.items[index].isOngoing = false
  }
  
  activeDatePicker.value = ''
  handleItemChange()
}

const selectPresent = (pickerId) => {
  const [index] = pickerId.split('_')
  formData.items[index].endDate = '至今'
  formData.items[index].isOngoing = true
  activeDatePicker.value = ''
  handleItemChange()
}

const isSelectedMonth = (pickerId, month) => {
  const [index, type] = pickerId.split('_')
  const currentValue = type === 'start' 
    ? formData.items[index].startDate 
    : formData.items[index].endDate
    
  if (!currentValue || currentValue === '至今') return false
  
  const [year, selectedMonth] = currentValue.split('-')
  return parseInt(year) === currentYear.value && parseInt(selectedMonth) === month
}

// 点击外部关闭日期选择器
const handleClickOutside = (event) => {
  if (!event.target.closest('.date-picker-wrapper')) {
    activeDatePicker.value = ''
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.custom-form {
  @apply p-6;
}

.form-header {
  @apply mb-6;
}

.form-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.form-desc {
  @apply text-sm text-gray-600;
}

.form-content {
  @apply space-y-6;
}

/* 内容项列表 */
.items-list {
  @apply space-y-6;
}

.item-card {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-4;
}

.item-header {
  @apply flex items-start gap-4;
}

.delete-btn {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200 text-lg font-bold mt-6;
}

.item-details {
  @apply space-y-4;
}

/* 表单组件 */
.form-group {
  @apply flex-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

/* 时间选择器 */
.date-range-group {
  @apply flex items-center gap-3;
}

.date-picker-wrapper {
  @apply relative flex-1;
}

.date-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer;
}

.date-picker-dropdown {
  @apply absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10;
  min-width: 240px;
}

.date-picker-header {
  @apply flex items-center justify-between px-4 py-3 border-b border-gray-200;
}

.date-picker-header button {
  @apply w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors duration-200;
}

.month-grid {
  @apply grid grid-cols-4 gap-2 p-3;
}

.month-btn {
  @apply px-3 py-2 text-sm text-gray-700 hover:bg-blue-100 hover:text-blue-800 rounded transition-colors duration-200;
  white-space: nowrap;
  min-width: 48px;
}

.month-btn.active {
  @apply bg-blue-500 text-white;
}

.month-btn.present-btn {
  @apply col-span-4 bg-green-100 text-green-800 hover:bg-green-200;
}

.date-separator {
  @apply text-sm text-gray-500 flex-shrink-0;
}

/* 空状态 */
.empty-state {
  @apply text-center py-12;
}

.empty-icon {
  @apply text-gray-400 mx-auto mb-3;
}

.empty-text {
  @apply text-base font-medium text-gray-900 mb-1;
}

.empty-desc {
  @apply text-sm text-gray-600;
}

/* 添加按钮 */
.add-btn {
  @apply w-full flex items-center justify-center space-x-2 px-4 py-3 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200;
}

.add-btn:hover {
  @apply bg-blue-100;
}
</style> 