<template>
  <div class="editor-container">
    <!-- 全局消息提示 -->
    <GlobalMessage
      :visible="globalMessage.messageState.visible"
      :type="globalMessage.messageState.type"
      :message="globalMessage.messageState.message"
      :duration="globalMessage.messageState.duration"
      @close="globalMessage.hideMessage"
    />
    
    <!-- 顶部导航栏 -->
    <EditorHeader 
      :resume-data="editorStore.resumeData.value"
      :is-dirty="editorStore.isDirty.value"
      :is-saving="editorStore.isLoading.value"
      @save="handleSave"
      @download="handleDownload"
      @send-email="handleSendEmail"
      @title-change="handleTitleChange"
      @request-login="handleRequestLogin"
    />
    
    <!-- 主体区域 -->
    <div class="editor-main">
      <SplitPanel
        :left-width="leftPanelWidth"
        @resize="handlePanelResize"
      >
        <!-- 左侧编辑区 -->
        <template #left>
          <div class="editor-sidebar">
            <ModuleNavigation />
            <ContentEditor />
          </div>
        </template>
        
        <!-- 右侧预览区 -->
        <template #right>
          <div 
            class="editor-preview"
            :class="{ 'grayscale-mode': isGrayscaleMode }"
          >
            <TextToolbar
              :settings="previewSettings"
              :template-defaults="templateDefaults"
              @settings-change="handleSettingsChange"
              @color-mode-change="handleColorModeChange"
            />
            <ResumePreview
              :resume-data="currentResumeData"
              :settings="previewSettings"
              @module-order-change="handleModuleOrderChange"
              @module-delete="handleModuleDelete"
              @template-defaults-loaded="handleTemplateDefaultsLoaded"
            />
          </div>
        </template>
      </SplitPanel>
    </div>

    <!-- 模块管理弹窗 -->
    <ModuleManager
      v-if="showModuleManager"
      :modules="Array.isArray(editorStore.moduleConfigs.value) ? editorStore.moduleConfigs.value : []"
      @close="() => showModuleManager = false"
      @update="handleModulesUpdate"
    />

    <!-- 登录弹窗 -->
    <LoginModal 
      v-if="showLoginModal"
      v-model="showLoginModal" 
    />
  </div>
</template>

<script setup>
/**
 * 编辑器主布局组件
 * @description 编辑器的主要布局容器，包含顶部导航、左右分屏等
 * <AUTHOR>
 * @since 1.0.0
 */

// ================================
// 导入依赖
// ================================
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useEditorStore } from '~/composables/editor/useEditorStore'
import { useAutoSave } from '~/composables/shared/useAutoSave'
import { useGlobalMessage } from '~/composables/shared/useGlobalMessage'
import { useDownloadService } from '~/composables/shared/useDownloadService'

// 导入布局组件
import EditorHeader from './EditorHeader.vue'
import SplitPanel from './SplitPanel.vue'

// 导入左侧组件
import ModuleNavigation from '../sidebar/ModuleNavigation.vue'
import ContentEditor from '../sidebar/ContentEditor.vue'
import ModuleManager from '../sidebar/ModuleManager.vue'

// 导入右侧组件
import TextToolbar from '../preview/TextToolbar.vue'
import ResumePreview from '../preview/ResumePreview.vue'
import LoginModal from '~/components/LoginModal.vue'
import GlobalMessage from '~/components/common/GlobalMessage.vue'

// ================================
// 组件属性
// ================================
const props = defineProps({
  /**
   * 简历ID（用于编辑已有简历）
   */
  resumeId: {
    type: String,
    default: null
  }
})

// ================================
// 响应式数据
// ================================
const editorStore = useEditorStore()
const autoSave = useAutoSave()
const globalMessage = useGlobalMessage()

// 黑白模式状态
const isGrayscaleMode = ref(false)

// 左侧面板宽度
const leftPanelWidth = ref(50) // 百分比

// 模块管理弹窗显示状态
const showModuleManager = ref(false)

// 登录弹窗显示状态
const showLoginModal = ref(false)

// 模板默认值
const templateDefaults = ref(null)

// 预览设置 - 初始值将被模板默认值覆盖
const previewSettings = ref({
  fontFamily: 'system-ui',
  fontSize: '14px',
  textColor: '#333333', // 默认选择第2个颜色
  margins: {
    top: 60,      // 上边距
    bottom: 60,   // 下边距
    side: 60      // 左右边距
  },
  spacings: {
    module: 24,         // 模块间距
    modulePadding: 16,  // 模块内边距
    item: 16,           // 项目间距
    itemPadding: 12,    // 项目内边距
    line: 1.6,          // 行距
    paragraph: 12       // 段落间距
  }
})

// ================================
// 计算属性
// ================================

/**
 * 当前简历数据
 */
const currentResumeData = computed(() => {
  // 确保正确访问响应式数据
  const data = editorStore.currentResumeData.value || {}
  console.log('🔍 EditorLayout - currentResumeData 计算:', data)
  return data
})

/**
 * 当前模块数据
 */
const currentModuleData = computed(() => {
  return editorStore.getModuleData(editorStore.currentModuleId.value) || {}
})

// ================================
// 生命周期
// ================================
onMounted(async () => {
  try {
    // 初始化编辑器
    await initializeEditor()
    
    // 启动自动保存
    // autoSave.start()
    
    // 添加键盘快捷键监听
    document.addEventListener('keydown', handleKeyboardShortcuts)
  } catch (error) {
    console.error('编辑器初始化失败:', error)
  }
})

onUnmounted(() => {
  // 停止自动保存
  // autoSave.stop()
  
  // 移除键盘监听
  document.removeEventListener('keydown', handleKeyboardShortcuts)
})

// ================================
// 初始化方法
// ================================

/**
 * 初始化编辑器
 */
const initializeEditor = async () => {
  console.log('开始初始化编辑器...')
  
  try {
    if (props.resumeId) {
      // 加载已有简历
      await editorStore.loadResume(props.resumeId)
      console.log('已加载简历:', props.resumeId)
    } else {
      // 创建新简历
      await editorStore.createNewResume()
      console.log('已创建新简历')
    }
    
    console.log('编辑器初始化完成，当前简历数据:', editorStore.resumeData)
  } catch (error) {
    console.error('编辑器初始化失败:', error)
    // 即使失败也要确保有默认数据
    editorStore.reset()
  }
}

// ================================
// 事件处理方法
// ================================

/**
 * 处理面板大小调整
 * @param {number} leftWidth - 左侧面板宽度百分比
 */
const handlePanelResize = (leftWidth) => {
  leftPanelWidth.value = leftWidth
}

/**
 * 处理模块切换
 * @param {string} moduleId - 模块ID
 */
const handleModuleChange = (moduleId) => {
  editorStore.setCurrentModule(moduleId)
}

/**
 * 处理模块管理
 */
const handleModuleManage = () => {
  showModuleManager.value = true
}

/**
 * 处理数据变更
 * @param {Object} data - 变更的数据
 */
const handleDataChange = (data) => {
  editorStore.updateModuleData(editorStore.currentModule.value, data)
}

/**
 * 处理设置变更
 * @param {Object} settings - 设置数据
 */
const handleSettingsChange = (settings) => {
  previewSettings.value = { ...previewSettings.value, ...settings }
  editorStore.updateSettings(settings)
}

/**
 * 处理颜色模式变更
 * @param {boolean} isGrayscale - 是否为黑白模式
 */
const handleColorModeChange = (isGrayscale) => {
  isGrayscaleMode.value = isGrayscale
}

/**
 * 处理模板默认值加载
 * @param {Object} defaults - 模板默认值
 */
const handleTemplateDefaultsLoaded = (defaults) => {
  console.log('🎨 接收到模板默认值:', defaults)
  templateDefaults.value = defaults
  
  // 使用模板默认值更新预览设置
  previewSettings.value = {
    ...previewSettings.value,
    margins: {
      top: defaults.marginTop || 60,
      bottom: defaults.marginBottom || 60,
      side: defaults.marginSide || 60
    },
    spacings: {
      module: defaults.moduleSpacing || 24,
      modulePadding: defaults.modulePadding || 16,
      item: defaults.itemSpacing || 16,
      itemPadding: defaults.itemPadding || 12,
      line: defaults.lineHeight || 1.6,
      paragraph: defaults.paragraphSpacing || 12
    }
  }
  
  console.log('🎯 预览设置已更新为模板默认值:', previewSettings.value)
}

/**
 * 处理模块顺序变更
 * @param {Array} newOrder - 新的模块顺序
 */
const handleModuleOrderChange = (newOrder) => {
  // 更新模块配置的顺序
  const updatedConfigs = editorStore.moduleConfigs.value.map((config, index) => ({
    ...config,
    order: newOrder.indexOf(config.id) !== -1 ? newOrder.indexOf(config.id) : index
  }))
  editorStore.moduleConfigs.value = updatedConfigs
}

/**
 * 处理模块删除
 * @param {string} moduleId - 要删除的模块ID
 */
const handleModuleDelete = (moduleId) => {
  editorStore.deleteModule(moduleId)
}

/**
 * 处理模块配置更新
 * @param {Array} updatedModules - 更新后的模块配置
 */
const handleModulesUpdate = (updatedModules) => {
  editorStore.moduleConfigs.value = updatedModules
  showModuleManager.value = false
}

/**
 * 处理保存
 */
const handleSave = async () => {
  console.log('保存简历功能 - 开发中')
  // TODO: 实现保存功能
}

/**
 * 处理下载
 * @param {Object} options - 下载选项
 * @param {string} options.format - 下载格式 (pdf/word/image)
 */
const handleDownload = async (options) => {
  const { format } = options
  
  console.log(`开始下载简历，格式: ${format}`)
  
  try {
    // 验证简历ID
    const currentResumeId = props.resumeId || editorStore.resumeData.value?.id || '1001' // 使用默认ID进行测试
    
    if (!currentResumeId) {
      throw new Error('简历ID不能为空，请先保存简历')
    }
    
    // 获取当前简历数据
    let resumeData = editorStore.resumeData.value
    
    if (!resumeData || Object.keys(resumeData).length === 0) {
      // 使用默认简历数据进行测试
      resumeData = {
        basicInfo: {
          name: '张三',
          title: '前端开发工程师',
          email: '<EMAIL>',
          phone: '13800138000'
        },
        workExperience: [
          {
            company: '火花简历',
            position: '高级前端工程师',
            duration: '2022-至今',
            description: '负责简历编辑器的开发和维护'
          }
        ]
      }
      
      console.warn('使用默认简历数据进行测试')
    }
    
    // 显示开始下载的消息
    globalMessage.showMessage({
      type: 'info',
      message: `正在生成${format.toUpperCase()}文档...`
    })
    
    // 调用下载服务
    const downloadService = useDownloadService()
    await downloadService.downloadResume({
      resumeId: currentResumeId,
      format: format,
      resumeData: resumeData,
      settings: previewSettings.value
    })
    
    console.log(`${format.toUpperCase()}下载完成`)
    
    // 显示成功消息
    globalMessage.showMessage({
      type: 'success',
      message: `${format.toUpperCase()}下载完成`
    })
    
  } catch (error) {
    console.error('下载失败:', error)
    globalMessage.showMessage({
      type: 'error',
      message: `下载失败: ${error.message}`
    })
  }
}

/**
 * 处理邮件发送
 */
const handleSendEmail = () => {
  // TODO: 实现邮件发送功能
  console.log('发送邮件')
}

/**
 * 处理标题变更
 * @param {string} title - 新标题
 */
const handleTitleChange = (title) => {
  editorStore.updateTitle(title)
}

/**
 * 处理登录请求
 */
const handleRequestLogin = () => {
  showLoginModal.value = true
}

/**
 * 处理键盘快捷键
 * @param {KeyboardEvent} event - 键盘事件
 */
const handleKeyboardShortcuts = (event) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 's':
        event.preventDefault()
        handleSave()
        break
      case 'z':
        event.preventDefault()
        if (event.shiftKey) {
          handleRedo()
        } else {
          handleUndo()
        }
        break
    }
  }
}
</script>

<style scoped>
.editor-container {
  @apply flex flex-col h-screen bg-gray-50;
  overflow: hidden;
}

.editor-main {
  @apply flex-grow;
  min-height: 0;
}

.editor-sidebar {
  @apply h-full bg-white flex flex-col;
}

.editor-preview {
  @apply h-full bg-gray-100 flex flex-col;
  transition: filter 0.3s ease-in-out;
}

.editor-preview.grayscale-mode {
  filter: grayscale(100%);
}
</style> 