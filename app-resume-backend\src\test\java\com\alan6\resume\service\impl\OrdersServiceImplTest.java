package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.entity.Orders;
import com.alan6.resume.mapper.OrdersMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 订单服务实现类测试
 * 
 * <AUTHOR>
 * @since 2025-06-22
 */
@SpringBootTest
@SpringJUnitConfig
@ExtendWith(MockitoExtension.class)
class OrdersServiceImplTest {

    @Mock
    private OrdersMapper ordersMapper;

    @InjectMocks
    private OrdersServiceImpl ordersService;

    private Orders testOrder;

    @BeforeEach
    void setUp() {
        // 创建测试订单数据
        testOrder = new Orders();
        testOrder.setId(1L);
        testOrder.setOrderNo("ORD20241201123456");
        testOrder.setUserId(1001L);
        testOrder.setOrderType((byte) 1);
        testOrder.setProductId(1L);
        testOrder.setProductName("月度会员");
        testOrder.setOriginalAmount(new BigDecimal("29.90"));
        testOrder.setDiscountAmount(BigDecimal.ZERO);
        testOrder.setFinalAmount(new BigDecimal("29.90"));
        testOrder.setPaymentMethod((byte) 1);
        testOrder.setPaymentPlatform("wechat");
        testOrder.setOrderStatus((byte) 1);
        testOrder.setIsDeleted((byte) 0);
        testOrder.setCreateTime(LocalDateTime.now());
        testOrder.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 测试创建会员购买订单 - 成功
     */
    @Test
    void testCreateMembershipOrder_Success() {
        // 准备数据
        when(ordersMapper.insert(any(Orders.class))).thenReturn(1);

        // 执行测试
        Orders result = ordersService.createMembershipOrder(
            1001L, 1L, "月度会员", new BigDecimal("29.90"), "wechat"
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(1001L, result.getUserId());
        assertEquals(1L, result.getProductId());
        assertEquals("月度会员", result.getProductName());
        assertEquals(new BigDecimal("29.90"), result.getFinalAmount());
        assertEquals((byte) 1, result.getPaymentMethod());
        assertEquals("wechat", result.getPaymentPlatform());
        assertEquals((byte) 1, result.getOrderStatus());
        assertNotNull(result.getOrderNo());
        assertTrue(result.getOrderNo().startsWith("ORD"));

        // 验证调用
        verify(ordersMapper, times(1)).insert(any(Orders.class));
    }

    /**
     * 测试创建会员购买订单 - 用户ID为空
     */
    @Test
    void testCreateMembershipOrder_NullUserId() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> ordersService.createMembershipOrder(null, 1L, "月度会员", new BigDecimal("29.90"), "wechat"));
        
        assertEquals("订单参数不能为空", exception.getMessage());

        // 验证没有调用数据库
        verify(ordersMapper, never()).insert(any(Orders.class));
    }

    /**
     * 测试创建会员购买订单 - 金额为零
     */
    @Test
    void testCreateMembershipOrder_ZeroAmount() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> ordersService.createMembershipOrder(1001L, 1L, "月度会员", BigDecimal.ZERO, "wechat"));
        
        assertEquals("订单金额必须大于0", exception.getMessage());

        // 验证没有调用数据库
        verify(ordersMapper, never()).insert(any(Orders.class));
    }

    /**
     * 测试创建会员购买订单 - 不支持的支付方式
     */
    @Test
    void testCreateMembershipOrder_UnsupportedPaymentMethod() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> ordersService.createMembershipOrder(1001L, 1L, "月度会员", new BigDecimal("29.90"), "unknown"));
        
        assertEquals("不支持的支付方式：unknown", exception.getMessage());

        // 验证没有调用数据库
        verify(ordersMapper, never()).insert(any(Orders.class));
    }

    /**
     * 测试创建会员购买订单 - 数据库插入失败
     */
    @Test
    void testCreateMembershipOrder_InsertFailed() {
        // 准备数据
        when(ordersMapper.insert(any(Orders.class))).thenReturn(0);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> ordersService.createMembershipOrder(1001L, 1L, "月度会员", new BigDecimal("29.90"), "wechat"));
        
        assertEquals("订单创建失败", exception.getMessage());

        // 验证调用
        verify(ordersMapper, times(1)).insert(any(Orders.class));
    }

    /**
     * 测试根据订单号获取订单 - 成功
     */
    @Test
    void testGetByOrderNo_Success() {
        // 准备数据
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testOrder);

        // 执行测试
        Orders result = ordersService.getByOrderNo("ORD20241201123456");

        // 验证结果
        assertNotNull(result);
        assertEquals(testOrder.getOrderNo(), result.getOrderNo());
        assertEquals(testOrder.getUserId(), result.getUserId());

        // 验证调用
        verify(ordersMapper, times(1)).selectOne(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试根据订单号获取订单 - 订单号为空
     */
    @Test
    void testGetByOrderNo_EmptyOrderNo() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> ordersService.getByOrderNo(""));
        
        assertEquals("订单号不能为空", exception.getMessage());

        // 验证没有调用数据库
        verify(ordersMapper, never()).selectOne(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试根据订单号获取订单 - 订单不存在
     */
    @Test
    void testGetByOrderNo_NotFound() {
        // 准备数据
        when(ordersMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> ordersService.getByOrderNo("ORD99999999999"));
        
        assertEquals("订单不存在", exception.getMessage());

        // 验证调用
        verify(ordersMapper, times(1)).selectOne(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试更新订单状态 - 成功
     */
    @Test
    void testUpdateOrderStatus_Success() {
        // 准备数据
        when(ordersMapper.selectById(1L)).thenReturn(testOrder);
        when(ordersMapper.updateById(any(Orders.class))).thenReturn(1);

        // 执行测试
        Boolean result = ordersService.updateOrderStatus(1L, (byte) 2, "TXN123456");

        // 验证结果
        assertTrue(result);

        // 验证调用
        verify(ordersMapper, times(1)).selectById(1L);
        verify(ordersMapper, times(1)).updateById(any(Orders.class));
    }

    /**
     * 测试更新订单状态 - 订单ID为空
     */
    @Test
    void testUpdateOrderStatus_NullOrderId() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> ordersService.updateOrderStatus(null, (byte) 2, "TXN123456"));
        
        assertEquals("订单ID和状态不能为空", exception.getMessage());

        // 验证没有调用数据库
        verify(ordersMapper, never()).selectById(any());
        verify(ordersMapper, never()).updateById(any(Orders.class));
    }

    /**
     * 测试更新订单状态 - 订单不存在
     */
    @Test
    void testUpdateOrderStatus_OrderNotFound() {
        // 准备数据
        when(ordersMapper.selectById(999L)).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> ordersService.updateOrderStatus(999L, (byte) 2, "TXN123456"));
        
        assertEquals("订单不存在", exception.getMessage());

        // 验证调用
        verify(ordersMapper, times(1)).selectById(999L);
        verify(ordersMapper, never()).updateById(any(Orders.class));
    }

    /**
     * 测试更新订单状态 - 更新失败
     */
    @Test
    void testUpdateOrderStatus_UpdateFailed() {
        // 准备数据
        when(ordersMapper.selectById(1L)).thenReturn(testOrder);
        when(ordersMapper.updateById(any(Orders.class))).thenReturn(0);

        // 执行测试
        Boolean result = ordersService.updateOrderStatus(1L, (byte) 2, "TXN123456");

        // 验证结果
        assertFalse(result);

        // 验证调用
        verify(ordersMapper, times(1)).selectById(1L);
        verify(ordersMapper, times(1)).updateById(any(Orders.class));
    }
} 