import{r as m,k as B,u as D,c as r,a as e,q as c,p as h,H as M,f as N,y as z,t as o,s as _,h as t,F,g as H,z as I,M as L,o as d}from"./CURHyiUL.js";import{_ as P}from"./DlAUqK2U.js";const U={class:"container mx-auto px-4 py-8"},q={class:"max-w-4xl mx-auto"},A={class:"bg-white rounded-lg shadow-md p-6 mb-8"},E={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},G={class:"inline-flex items-center"},J={class:"flex flex-wrap gap-3"},W=["disabled"],K=["disabled"],O=["disabled"],Q={class:"bg-gray-50 rounded-lg p-4 mb-6"},X={class:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm"},Y={class:"text-blue-600"},Z={key:0,class:"mt-3"},$={class:"text-gray-600"},ee={key:0,class:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6"},se={class:"text-red-700"},te={key:1,class:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6"},ae={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},oe={class:"text-gray-700"},le={class:"text-gray-700"},de={class:"text-gray-700"},ne={class:"text-gray-700"},re={key:0,class:"mt-3"},ie={class:"flex flex-wrap gap-2 mt-1"},ue={class:"bg-white rounded-lg shadow-md p-6"},me={key:0,class:"border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-[400px]"},pe={key:1,class:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500"},ce={__name:"template-test",setup(ge){const{loadTemplate:k,hasTemplate:b,templateMeta:n,loadingState:v,loadingProgress:w,loadingStep:x,error:f,clearCache:C}=useTemplateLoader(),a=m(""),g=m(!1),p=m(!1),i=m(null),T=m({basicInfo:{name:"张三",title:"前端开发工程师",email:"<EMAIL>",phone:"13800138000",location:"北京市朝阳区",avatar:"/images/avatar-sample.jpg"},summary:"具有5年前端开发经验，熟练掌握Vue.js、React等主流框架，擅长移动端开发和性能优化。",experience:[{company:"某科技公司",position:"高级前端开发工程师",duration:"2021.06 - 至今",description:"负责公司核心产品的前端开发，参与架构设计和技术选型。"},{company:"某互联网公司",position:"前端开发工程师",duration:"2019.03 - 2021.05",description:"负责移动端H5页面开发，优化页面性能，提升用户体验。"}],education:[{school:"某大学",degree:"计算机科学与技术 学士",duration:"2015.09 - 2019.06",description:"主修计算机科学与技术，GPA 3.8/4.0"}],skills:["Vue.js","React","JavaScript","TypeScript","Node.js","Webpack"]}),y=B(()=>a.value&&b(a.value)),S=async()=>{if(a.value){p.value=!0,i.value=null;try{const l=await k({templateCode:a.value,forceRefresh:g.value});l&&(i.value=l,console.log("模板加载成功:",l))}catch(l){console.error("模板加载失败:",l)}finally{p.value=!1}}},V=()=>{i.value=null,a.value&&C(a.value)},R=()=>{if(!a.value)return;const l=b(a.value),s=n.value;console.log("模板检查结果:",{templateCode:a.value,cached:l,meta:s})},j=l=>{switch(l){case"loading":return"text-blue-600";case"success":return"text-green-600";case"error":return"text-red-600";default:return"text-gray-600"}};return D({title:"Vue模板动态加载测试"}),(l,s)=>(d(),r("div",U,[e("div",q,[s[21]||(s[21]=e("h1",{class:"text-3xl font-bold text-gray-900 mb-8"},"Vue模板动态加载测试",-1)),e("div",A,[s[6]||(s[6]=e("h2",{class:"text-xl font-semibold mb-4"},"控制面板",-1)),e("div",E,[e("div",null,[s[3]||(s[3]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 模板代码 ",-1)),h(e("select",{"onUpdate:modelValue":s[0]||(s[0]=u=>a.value=u),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},s[2]||(s[2]=[N('<option value="" data-v-213da256>请选择模板</option><option value="modern-simple" data-v-213da256>现代简约</option><option value="creative-design" data-v-213da256>创意设计</option><option value="business-classic" data-v-213da256>商务经典</option><option value="tech-geek" data-v-213da256>技术极客</option><option value="fresh-literature" data-v-213da256>清新文艺</option>',6)]),512),[[M,a.value]])]),e("div",null,[s[5]||(s[5]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 强制刷新 ",-1)),e("label",G,[h(e("input",{type:"checkbox","onUpdate:modelValue":s[1]||(s[1]=u=>g.value=u),class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[z,g.value]]),s[4]||(s[4]=e("span",{class:"ml-2 text-sm text-gray-600"},"强制从服务器重新加载",-1))])])]),e("div",J,[e("button",{onClick:S,disabled:p.value||!a.value,class:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"},o(p.value?"加载中...":"加载模板"),9,W),e("button",{onClick:V,disabled:!i.value,class:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed"}," 清除模板 ",8,K),e("button",{onClick:R,disabled:!a.value,class:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"}," 检查模板 ",8,O)])]),e("div",Q,[s[11]||(s[11]=e("h3",{class:"text-lg font-medium mb-3"},"状态信息",-1)),e("div",X,[e("div",null,[s[7]||(s[7]=e("span",{class:"font-medium"},"加载状态:",-1)),e("span",{class:_(j(t(v)))},o(t(v)),3)]),e("div",null,[s[8]||(s[8]=e("span",{class:"font-medium"},"进度:",-1)),e("span",Y,o(t(w))+"%",1)]),e("div",null,[s[9]||(s[9]=e("span",{class:"font-medium"},"缓存状态:",-1)),e("span",{class:_(y.value?"text-green-600":"text-gray-600")},o(y.value?"已缓存":"未缓存"),3)])]),t(x)?(d(),r("div",Z,[s[10]||(s[10]=e("span",{class:"font-medium"},"当前步骤:",-1)),e("span",$,o(t(x)),1)])):c("",!0)]),t(f)?(d(),r("div",ee,[s[12]||(s[12]=e("h3",{class:"text-lg font-medium text-red-800 mb-2"},"错误信息",-1)),e("p",se,o(t(f)),1)])):c("",!0),t(n)?(d(),r("div",te,[s[18]||(s[18]=e("h3",{class:"text-lg font-medium text-blue-800 mb-3"},"模板信息",-1)),e("div",ae,[e("div",null,[s[13]||(s[13]=e("span",{class:"font-medium"},"模板名称:",-1)),e("span",oe,o(t(n).name),1)]),e("div",null,[s[14]||(s[14]=e("span",{class:"font-medium"},"版本:",-1)),e("span",le,o(t(n).version),1)]),e("div",null,[s[15]||(s[15]=e("span",{class:"font-medium"},"作者:",-1)),e("span",de,o(t(n).author),1)]),e("div",null,[s[16]||(s[16]=e("span",{class:"font-medium"},"描述:",-1)),e("span",ne,o(t(n).description),1)])]),t(n).features&&t(n).features.length>0?(d(),r("div",re,[s[17]||(s[17]=e("span",{class:"font-medium"},"功能特性:",-1)),e("div",ie,[(d(!0),r(F,null,H(t(n).features,u=>(d(),r("span",{key:u,class:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"},o(u),1))),128))])])):c("",!0)])):c("",!0),e("div",ue,[s[20]||(s[20]=e("h3",{class:"text-xl font-semibold mb-4"},"模板预览",-1)),i.value?(d(),r("div",me,[(d(),I(L(i.value),{"resume-data":T.value},null,8,["resume-data"]))])):(d(),r("div",pe,s[19]||(s[19]=[e("p",{class:"text-lg"},"请选择并加载一个模板",-1),e("p",{class:"text-sm mt-2"},'选择上方的模板代码，然后点击"加载模板"按钮',-1)])))])])]))}},xe=P(ce,[["__scopeId","data-v-213da256"]]);export{xe as default};
