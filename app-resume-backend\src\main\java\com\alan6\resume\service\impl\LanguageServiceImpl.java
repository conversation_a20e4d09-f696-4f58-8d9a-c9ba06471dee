package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.dto.system.LanguageConfigResponse;
import com.alan6.resume.dto.system.LanguageListResponse;
import com.alan6.resume.entity.SupportedLanguages;
import com.alan6.resume.mapper.SupportedLanguagesMapper;
import com.alan6.resume.service.ILanguageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 多语言支持服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LanguageServiceImpl implements ILanguageService {

    private final SupportedLanguagesMapper supportedLanguagesMapper;
    private final ObjectMapper objectMapper;

    /**
     * 获取支持的语言列表
     *
     * @return 语言列表响应
     */
    @Override
    public LanguageListResponse getSupportedLanguages() {
        log.info("获取支持的语言列表");
        
        try {
            // 查询所有语言，按排序顺序和启用状态排序
            LambdaQueryWrapper<SupportedLanguages> query = new LambdaQueryWrapper<>();
            query.orderByDesc(SupportedLanguages::getIsActive)
                 .orderByAsc(SupportedLanguages::getSortOrder)
                 .orderByAsc(SupportedLanguages::getLanguageCode);
            
            List<SupportedLanguages> languages = supportedLanguagesMapper.selectList(query);
            
            // 转换为响应DTO
            List<LanguageListResponse.LanguageInfo> languageInfoList = languages.stream()
                    .map(this::convertToLanguageInfo)
                    .collect(Collectors.toList());
            
            LanguageListResponse response = new LanguageListResponse();
            response.setLanguages(languageInfoList);
            
            log.info("获取支持的语言列表成功, 语言数量: {}", languageInfoList.size());
            return response;
            
        } catch (Exception e) {
            log.error("获取支持的语言列表失败: {}", e.getMessage(), e);
            throw new BusinessException("获取语言列表失败");
        }
    }

    /**
     * 获取指定语言的配置
     *
     * @param languageCode 语言代码
     * @return 语言配置响应
     */
    @Override
    public LanguageConfigResponse getLanguageConfig(String languageCode) {
        log.info("获取语言配置, languageCode: {}", languageCode);
        
        if (!StringUtils.hasText(languageCode)) {
            throw new BusinessException("语言代码不能为空");
        }
        
        try {
            // 查询语言信息
            LambdaQueryWrapper<SupportedLanguages> query = new LambdaQueryWrapper<>();
            query.eq(SupportedLanguages::getLanguageCode, languageCode);
            
            SupportedLanguages language = supportedLanguagesMapper.selectOne(query);
            if (language == null) {
                throw new BusinessException("语言不存在: " + languageCode);
            }
            
            // 构建响应
            LanguageConfigResponse response = new LanguageConfigResponse();
            response.setLanguageCode(language.getLanguageCode());
            response.setLanguageName(language.getLanguageName());
            response.setNativeName(language.getNativeName());
            response.setEnabled(language.getIsActive() == 1);
            response.setIsDefault(language.getIsDefault() != null && language.getIsDefault() == 1);
            response.setVersion(language.getConfigVersion());
            response.setLastUpdateTime(language.getUpdateTime());
            
            // 解析配置内容
            Map<String, Object> config = parseConfigContent(language.getConfigContent());
            response.setConfig(config);
            
            // 计算配置大小和完成度
            if (StringUtils.hasText(language.getConfigContent())) {
                response.setConfigSize((long) language.getConfigContent().getBytes().length);
                response.setCompleteness(calculateCompleteness(config));
            } else {
                response.setConfigSize(0L);
                response.setCompleteness(0.0);
            }
            
            log.info("获取语言配置成功, languageCode: {}, configSize: {}", languageCode, response.getConfigSize());
            return response;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取语言配置失败, languageCode: {}, error: {}", languageCode, e.getMessage(), e);
            throw new BusinessException("获取语言配置失败");
        }
    }

    /**
     * 更新语言配置
     *
     * @param languageCode 语言代码
     * @param config       配置内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLanguageConfig(String languageCode, String config) {
        log.info("更新语言配置, languageCode: {}", languageCode);
        
        if (!StringUtils.hasText(languageCode)) {
            throw new BusinessException("语言代码不能为空");
        }
        
        try {
            // 查询语言信息
            LambdaQueryWrapper<SupportedLanguages> query = new LambdaQueryWrapper<>();
            query.eq(SupportedLanguages::getLanguageCode, languageCode);
            
            SupportedLanguages language = supportedLanguagesMapper.selectOne(query);
            if (language == null) {
                throw new BusinessException("语言不存在: " + languageCode);
            }
            
            // 验证配置内容格式
            if (StringUtils.hasText(config)) {
                try {
                    objectMapper.readValue(config, Map.class);
                } catch (JsonProcessingException e) {
                    throw new BusinessException("配置内容格式不正确，必须是有效的JSON格式");
                }
            }
            
            // 更新配置
            language.setConfigContent(config);
            language.setConfigVersion(generateConfigVersion());
            language.setUpdateTime(LocalDateTime.now());
            
            int updated = supportedLanguagesMapper.updateById(language);
            if (updated > 0) {
                log.info("语言配置更新成功, languageCode: {}, version: {}", languageCode, language.getConfigVersion());
            } else {
                throw new BusinessException("更新语言配置失败");
            }
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新语言配置失败, languageCode: {}, error: {}", languageCode, e.getMessage(), e);
            throw new BusinessException("更新语言配置失败");
        }
    }

    /**
     * 启用/禁用语言
     *
     * @param languageCode 语言代码
     * @param enabled      是否启用
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleLanguage(String languageCode, boolean enabled) {
        log.info("切换语言状态, languageCode: {}, enabled: {}", languageCode, enabled);
        
        if (!StringUtils.hasText(languageCode)) {
            throw new BusinessException("语言代码不能为空");
        }
        
        try {
            // 查询语言信息
            LambdaQueryWrapper<SupportedLanguages> query = new LambdaQueryWrapper<>();
            query.eq(SupportedLanguages::getLanguageCode, languageCode);
            
            SupportedLanguages language = supportedLanguagesMapper.selectOne(query);
            if (language == null) {
                throw new BusinessException("语言不存在: " + languageCode);
            }
            
            // 检查是否为默认语言
            if (language.getIsDefault() != null && language.getIsDefault() == 1 && !enabled) {
                throw new BusinessException("默认语言不能被禁用");
            }
            
            // 更新状态
            language.setIsActive((byte) (enabled ? 1 : 0));
            language.setUpdateTime(LocalDateTime.now());
            
            int updated = supportedLanguagesMapper.updateById(language);
            if (updated > 0) {
                log.info("语言状态切换成功, languageCode: {}, enabled: {}", languageCode, enabled);
            } else {
                throw new BusinessException("切换语言状态失败");
            }
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("切换语言状态失败, languageCode: {}, error: {}", languageCode, e.getMessage(), e);
            throw new BusinessException("切换语言状态失败");
        }
    }

    /**
     * 转换为语言信息DTO
     *
     * @param language 语言实体
     * @return 语言信息DTO
     */
    private LanguageListResponse.LanguageInfo convertToLanguageInfo(SupportedLanguages language) {
        LanguageListResponse.LanguageInfo info = new LanguageListResponse.LanguageInfo();
        info.setId(language.getId());
        info.setCode(language.getLanguageCode());
        info.setName(language.getLanguageName());
        info.setNativeName(language.getNativeName());
        info.setEnabled(language.getIsActive() == 1);
        info.setIsDefault(language.getIsDefault() != null && language.getIsDefault() == 1);
        info.setSortOrder(language.getSortOrder());
        info.setCreateTime(language.getCreateTime());
        info.setUpdateTime(language.getUpdateTime());
        return info;
    }

    /**
     * 解析配置内容
     *
     * @param configContent 配置内容JSON字符串
     * @return 配置Map
     */
    private Map<String, Object> parseConfigContent(String configContent) {
        if (!StringUtils.hasText(configContent)) {
            return new HashMap<>();
        }
        
        try {
            return objectMapper.readValue(configContent, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.warn("解析配置内容失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 计算翻译完成度
     *
     * @param config 配置Map
     * @return 完成度百分比
     */
    private Double calculateCompleteness(Map<String, Object> config) {
        if (config == null || config.isEmpty()) {
            return 0.0;
        }
        
        // 简单实现：计算非空值的比例
        long totalKeys = countTotalKeys(config);
        long nonEmptyKeys = countNonEmptyKeys(config);
        
        if (totalKeys == 0) {
            return 0.0;
        }
        
        return (double) nonEmptyKeys / totalKeys * 100.0;
    }

    /**
     * 统计总键数（递归）
     *
     * @param map 配置Map
     * @return 总键数
     */
    private long countTotalKeys(Map<String, Object> map) {
        long count = 0;
        for (Object value : map.values()) {
            if (value instanceof Map) {
                count += countTotalKeys((Map<String, Object>) value);
            } else {
                count++;
            }
        }
        return count;
    }

    /**
     * 统计非空键数（递归）
     *
     * @param map 配置Map
     * @return 非空键数
     */
    private long countNonEmptyKeys(Map<String, Object> map) {
        long count = 0;
        for (Object value : map.values()) {
            if (value instanceof Map) {
                count += countNonEmptyKeys((Map<String, Object>) value);
            } else if (value != null && StringUtils.hasText(value.toString())) {
                count++;
            }
        }
        return count;
    }

    /**
     * 生成配置版本号
     *
     * @return 版本号
     */
    private String generateConfigVersion() {
        return "v" + System.currentTimeMillis();
    }
} 