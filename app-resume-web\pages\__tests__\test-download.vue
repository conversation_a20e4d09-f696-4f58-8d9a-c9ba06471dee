<template>
  <div class="test-download-page">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold mb-8">下载功能测试</h1>
      
      <!-- 下载下拉菜单测试 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">下载下拉菜单测试</h2>
        <DownloadDropdown
          :is-exporting="isExporting"
          :export-status="exportStatus"
          @download="handleDownload"
        />
      </div>

      <!-- 状态显示 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">下载状态</h2>
        <div class="bg-gray-100 p-4 rounded-lg">
          <p><strong>是否正在导出：</strong>{{ isExporting }}</p>
          <p><strong>导出状态：</strong>{{ exportStatus }}</p>
          <p><strong>下载进度：</strong>{{ downloadProgress }}%</p>
          <p><strong>当前任务ID：</strong>{{ currentJobId || '无' }}</p>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">直接测试</h2>
        <div class="space-x-4">
          <button
            @click="testPdfDownload"
            :disabled="isExporting"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            测试PDF下载
          </button>
          <button
            @click="testWordDownload"
            :disabled="isExporting"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            测试Word下载
          </button>
          <button
            @click="testImageDownload"
            :disabled="isExporting"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            测试图片下载
          </button>
        </div>
      </div>

      <!-- 日志显示 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">操作日志</h2>
        <div class="bg-black text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm">
          <div v-for="(log, index) in logs" :key="index" class="mb-1">
            <span class="text-gray-500">[{{ log.time }}]</span> {{ log.message }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * 下载功能测试页面
 * @description 用于测试下载下拉菜单和下载服务的功能
 */

// ================================
// 导入依赖
// ================================
import { ref, computed } from 'vue'
import DownloadDropdown from '~/components/editor/layout/DownloadDropdown.vue'
import { useDownloadService } from '~/composables/shared/useDownloadService'

// ================================
// 页面配置
// ================================
definePageMeta({
  layout: 'default'
})

useSeoMeta({
  title: '下载功能测试 - 火花简历',
  description: '测试简历下载功能'
})

// ================================
// 响应式数据
// ================================

// 下载服务
const downloadService = useDownloadService()

// 导出状态
const isExporting = ref(false)
const exportStatus = ref('准备就绪')

// 日志记录
const logs = ref([])

// ================================
// 计算属性
// ================================

// 从下载服务获取状态
const downloadProgress = computed(() => downloadService.downloadProgress.value)
const currentJobId = computed(() => downloadService.currentJobId.value)

// ================================
// 方法定义
// ================================

/**
 * 添加日志
 */
const addLog = (message) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  logs.value.push({ time, message })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.shift()
  }
  
  console.log(`[${time}] ${message}`)
}

/**
 * 处理下载请求
 */
const handleDownload = async (format) => {
  addLog(`用户选择下载格式: ${format}`)
  
  try {
    isExporting.value = true
    exportStatus.value = `正在生成${format.toUpperCase()}文档...`
    
    // 模拟简历数据
    const mockResumeData = {
      basicInfo: {
        name: '张三',
        title: '前端开发工程师',
        email: '<EMAIL>',
        phone: '13800138000'
      },
      workExperience: [
        {
          company: '阿里巴巴',
          position: '高级前端工程师',
          duration: '2020-2023',
          description: '负责电商核心业务系统的前端开发'
        }
      ]
    }
    
    const mockSettings = {
      fontFamily: 'system-ui',
      fontSize: '14px',
      textColor: '#333333',
      margins: { top: 60, bottom: 60, side: 60 }
    }
    
    // 调用下载服务
    await downloadService.downloadResume({
      resumeId: '1001', // 测试用的简历ID
      format: format,
      resumeData: mockResumeData,
      settings: mockSettings
    })
    
    addLog(`${format.toUpperCase()}下载成功`)
    exportStatus.value = '下载完成'
    
  } catch (error) {
    addLog(`下载失败: ${error.message}`)
    exportStatus.value = '下载失败'
  } finally {
    setTimeout(() => {
      isExporting.value = false
      exportStatus.value = '准备就绪'
    }, 2000)
  }
}

/**
 * 测试PDF下载
 */
const testPdfDownload = () => {
  handleDownload('pdf')
}

/**
 * 测试Word下载
 */
const testWordDownload = () => {
  handleDownload('word')
}

/**
 * 测试图片下载
 */
const testImageDownload = () => {
  handleDownload('image')
}

// ================================
// 生命周期
// ================================
onMounted(() => {
  addLog('下载功能测试页面已加载')
})
</script>

<style scoped>
.test-download-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
  max-width: 1200px;
}
</style> 