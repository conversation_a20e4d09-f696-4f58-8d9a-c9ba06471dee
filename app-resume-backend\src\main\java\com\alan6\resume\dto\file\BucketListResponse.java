package com.alan6.resume.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 存储桶列表响应DTO
 * 
 * 主要功能：
 * 1. 返回用户可访问的存储桶列表
 * 2. 包含存储桶的配置信息
 * 3. 提供存储桶管理所需的数据
 * 
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "存储桶列表响应", description = "存储桶列表信息")
public class BucketListResponse {

    /**
     * 存储桶列表
     */
    @Schema(description = "存储桶列表")
    private List<BucketInfo> buckets;

    /**
     * 存储桶信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "存储桶信息", description = "存储桶详细信息")
    public static class BucketInfo {

        /**
         * 存储桶名称
         */
        @Schema(description = "存储桶名称", example = "avatar-bucket")
        private String name;

        /**
         * 存储桶描述
         */
        @Schema(description = "存储桶描述", example = "用户头像存储桶")
        private String description;

        /**
         * 区域
         */
        @Schema(description = "区域", example = "us-east-1")
        private String region;

        /**
         * 是否启用版本控制
         */
        @Schema(description = "是否启用版本控制", example = "true")
        private Boolean versioning;

        /**
         * 加密类型
         */
        @Schema(description = "加密类型", example = "SSE-S3")
        private String encryption;

        /**
         * 存储类别
         */
        @Schema(description = "存储类别", example = "STANDARD")
        private String storageClass;

        /**
         * 访问策略
         */
        @Schema(description = "访问策略", example = "private")
        private String accessPolicy;

        /**
         * 对象数量
         */
        @Schema(description = "对象数量", example = "125")
        private Long objectCount;

        /**
         * 总大小（字节）
         */
        @Schema(description = "总大小（字节）", example = "1048576")
        private Long totalSize;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间", example = "2024-01-01 00:00:00")
        private LocalDateTime createTime;

        /**
         * 最后修改时间
         */
        @Schema(description = "最后修改时间", example = "2024-12-22 15:30:00")
        private LocalDateTime lastModified;

        /**
         * 格式化总大小为可读格式
         * 
         * @return 格式化后的大小字符串
         */
        public String getFormattedTotalSize() {
            if (totalSize == null) {
                return "未知";
            }
            
            if (totalSize < 1024) {
                return totalSize + " B";
            } else if (totalSize < 1024 * 1024) {
                return String.format("%.1f KB", totalSize / 1024.0);
            } else if (totalSize < 1024 * 1024 * 1024) {
                return String.format("%.1f MB", totalSize / (1024.0 * 1024.0));
            } else {
                return String.format("%.1f GB", totalSize / (1024.0 * 1024.0 * 1024.0));
            }
        }

        /**
         * 检查是否启用版本控制
         * 
         * @return true-启用，false-未启用
         */
        public boolean isVersioningEnabled() {
            return versioning != null && versioning;
        }

        /**
         * 检查是否为公开访问
         * 
         * @return true-公开访问，false-私有访问
         */
        public boolean isPublicAccess() {
            return accessPolicy != null && 
                   (accessPolicy.equals("public-read") || accessPolicy.equals("public-read-write"));
        }

        /**
         * 检查是否为空存储桶
         * 
         * @return true-空存储桶，false-非空存储桶
         */
        public boolean isEmpty() {
            return objectCount == null || objectCount == 0;
        }

        /**
         * 获取存储桶类型
         * 根据名称推断存储桶类型
         * 
         * @return 存储桶类型
         */
        public String getBucketType() {
            if (name == null) {
                return "unknown";
            }
            
            if (name.contains("avatar")) {
                return "avatar";
            } else if (name.contains("resume")) {
                return "resume";
            } else if (name.contains("template")) {
                return "template";
            } else if (name.contains("document")) {
                return "document";
            } else {
                return "general";
            }
        }
    }

    /**
     * 检查是否有存储桶
     * 
     * @return true-有存储桶，false-无存储桶
     */
    public boolean hasBuckets() {
        return buckets != null && !buckets.isEmpty();
    }

    /**
     * 获取存储桶数量
     * 
     * @return 存储桶数量
     */
    public int getBucketCount() {
        return buckets != null ? buckets.size() : 0;
    }

    /**
     * 根据名称查找存储桶
     * 
     * @param bucketName 存储桶名称
     * @return 存储桶信息，如果不存在返回null
     */
    public BucketInfo findBucketByName(String bucketName) {
        if (buckets == null || bucketName == null) {
            return null;
        }
        
        return buckets.stream()
                .filter(bucket -> bucketName.equals(bucket.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取启用版本控制的存储桶列表
     * 
     * @return 启用版本控制的存储桶列表
     */
    public List<BucketInfo> getVersioningEnabledBuckets() {
        if (buckets == null) {
            return List.of();
        }
        
        return buckets.stream()
                .filter(BucketInfo::isVersioningEnabled)
                .toList();
    }

    /**
     * 获取公开访问的存储桶列表
     * 
     * @return 公开访问的存储桶列表
     */
    public List<BucketInfo> getPublicAccessBuckets() {
        if (buckets == null) {
            return List.of();
        }
        
        return buckets.stream()
                .filter(BucketInfo::isPublicAccess)
                .toList();
    }

    /**
     * 创建空的存储桶列表响应
     * 
     * @return 空的存储桶列表响应
     */
    public static BucketListResponse empty() {
        return BucketListResponse.builder()
                .buckets(List.of())
                .build();
    }
} 