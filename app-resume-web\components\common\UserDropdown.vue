<template>
  <div class="relative" v-click-outside="closeDropdown">
    <!-- 触发按钮 -->
    <button
      @mouseenter="showDropdown = true"
      @click="showDropdown = !showDropdown"
      class="flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-secondary-700 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
    >
      <!-- 用户头像 -->
      <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
        <img 
          v-if="userInfo?.avatar" 
          :src="userInfo.avatar" 
          :alt="userInfo.nickname || '用户头像'"
          class="w-8 h-8 rounded-full object-cover"
        >
        <svg v-else class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
      </div>
      <span>个人中心</span>
      <svg 
        class="w-4 h-4 transition-transform duration-200"
        :class="{ 'rotate-180': showDropdown }"
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>

    <!-- 下拉菜单 -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 translate-y-1"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-1"
    >
      <div
        v-if="showDropdown"
        @mouseenter="showDropdown = true"
        @mouseleave="showDropdown = false"
        class="absolute right-0 mt-2 w-72 bg-white rounded-xl shadow-xl border border-secondary-100 overflow-hidden"
      >
        <!-- 用户信息部分 -->
        <div class="p-4 bg-gradient-to-br from-secondary-50 to-secondary-100">
          <div class="flex items-start space-x-3">
            <!-- 头像 -->
            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
              <img 
                v-if="userInfo?.avatar" 
                :src="userInfo.avatar" 
                :alt="userInfo.nickname || '用户头像'"
                class="w-12 h-12 rounded-full object-cover"
              >
              <svg v-else class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <!-- 用户信息 -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-secondary-900 truncate">
                {{ userInfo?.phone || '未设置手机号' }}
              </p>
              <p class="text-xs text-secondary-500">
                ID: {{ userInfo?.userId || userInfo?.id || '-' }}
              </p>
            </div>
          </div>

          <!-- 会员状态 -->
          <div class="mt-3 p-3 bg-white/80 rounded-lg">
            <!-- 免费用户 -->
            <div v-if="!isMember" class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-secondary-200 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <span class="text-sm text-secondary-600">免费用户</span>
              </div>
              <button
                @click="handleUpgradeVIP"
                class="px-3 py-1 text-xs font-medium bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-lg hover:from-amber-600 hover:to-orange-600 transition-all duration-200 shadow-sm hover:shadow"
              >
                升级VIP
              </button>
            </div>
            <!-- VIP用户 -->
            <div v-else class="flex items-center space-x-2">
              <div class="w-6 h-6 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center shadow-sm">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
              <span class="text-sm font-medium bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                VIP会员
              </span>
              <span v-if="memberExpireDays > 0" class="text-xs text-secondary-500">
                剩余{{ memberExpireDays }}天
              </span>
            </div>
          </div>
        </div>

        <!-- 菜单项部分 -->
        <div class="py-2">
          <NuxtLink
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            @click="closeDropdown"
            class="flex items-center space-x-3 px-4 py-3 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-150"
          >
            <component :is="item.icon" class="w-5 h-5 text-secondary-400" />
            <span>{{ item.label }}</span>
          </NuxtLink>

          <!-- 分割线 -->
          <div class="my-2 border-t border-secondary-100"></div>

          <!-- 退出登录 -->
          <button
            @click="handleLogout"
            class="w-full flex items-center space-x-3 px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span>退出登录</span>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { useAuthService } from '~/composables/auth/useAuthService'
import { $message } from '~/composables/shared/useGlobalMessage'

// Props
const props = defineProps({
  userInfo: {
    type: Object,
    default: () => ({})
  }
})

// 认证服务
const authService = useAuthService()

// 状态
const showDropdown = ref(false)

// 计算属性
const isMember = computed(() => {
  return props.userInfo?.membershipInfo?.isMember || false
})

const memberExpireDays = computed(() => {
  return props.userInfo?.membershipInfo?.remainDays || 0
})

// 菜单项配置
const menuItems = [
  {
    path: '/my-resumes',
    label: '我的简历',
    icon: h('svg', { fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' 
      })
    ])
  },
  {
    path: '/profile',
    label: '账户信息',
    icon: h('svg', { fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' 
      })
    ])
  }
]

// 方法
const closeDropdown = () => {
  showDropdown.value = false
}

const handleUpgradeVIP = () => {
  closeDropdown()
  // 跳转到会员购买页面
  navigateTo('/profile?tab=membership')
}

const handleLogout = async () => {
  closeDropdown()
  
  try {
    await authService.logout()
    // 跳转到首页
    await navigateTo('/')
  } catch (error) {
    console.error('退出登录失败:', error)
    $message.error('退出登录失败，请重试')
  }
}

// 自定义指令：点击外部关闭
const vClickOutside = {
  mounted(el, binding) {
    el.clickOutsideEvent = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value()
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}
</script>

<style scoped>
/* 添加一些微动画效果 */
.rotate-180 {
  transform: rotate(180deg);
}
</style> 