<template>
  <div>
    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-secondary-900">账户设置</h2>
      <p class="mt-1 text-sm text-secondary-600">管理您的个人信息和账户安全</p>
    </div>

    <!-- 设置内容 -->
    <div class="space-y-6">
      <!-- 基本信息 -->
      <div class="bg-white rounded-lg border border-secondary-200 p-6">
        <h3 class="text-lg font-medium text-secondary-900 mb-4">基本信息</h3>
        
        <div class="space-y-4">
          <!-- 头像 -->
          <div class="flex items-center space-x-4">
            <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
              <img 
                v-if="userInfo.avatar" 
                :src="userInfo.avatar" 
                alt="用户头像"
                class="w-20 h-20 rounded-full object-cover"
              >
              <svg v-else class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <div>
              <button class="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors duration-200">
                更换头像
              </button>
              <p class="mt-1 text-xs text-secondary-500">支持 JPG、PNG 格式，大小不超过 2MB</p>
            </div>
          </div>

          <!-- 用户名 -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-1">用户名</label>
            <input 
              v-model="userInfo.username"
              type="text" 
              class="w-full px-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
              placeholder="请输入用户名"
            >
          </div>

          <!-- 手机号 -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-1">手机号</label>
            <div class="flex items-center space-x-2">
              <input 
                :value="userInfo.phone"
                type="text" 
                class="flex-1 px-4 py-2 border border-secondary-300 rounded-lg bg-secondary-50 cursor-not-allowed"
                disabled
              >
              <button class="px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200">
                更换手机号
              </button>
            </div>
          </div>

          <!-- 邮箱 -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-1">邮箱</label>
            <div class="flex items-center space-x-2">
              <input 
                v-model="userInfo.email"
                type="email" 
                class="flex-1 px-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                placeholder="请输入邮箱"
              >
              <button 
                v-if="!userInfo.emailVerified"
                class="px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"
              >
                验证邮箱
              </button>
              <span v-else class="text-sm text-green-600 flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                已验证
              </span>
            </div>
          </div>
        </div>

        <div class="mt-6 flex justify-end">
          <button
            @click="handleSaveBasicInfo"
            class="px-6 py-2 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200"
          >
            保存修改
          </button>
        </div>
      </div>

      <!-- 账户安全 -->
      <div class="bg-white rounded-lg border border-secondary-200 p-6">
        <h3 class="text-lg font-medium text-secondary-900 mb-4">账户安全</h3>
        
        <div class="space-y-4">
          <!-- 修改密码 -->
          <div class="flex items-center justify-between py-3 border-b border-secondary-100">
            <div>
              <h4 class="text-sm font-medium text-secondary-900">登录密码</h4>
              <p class="text-sm text-secondary-500 mt-1">定期更换密码可以提高账户安全性</p>
            </div>
            <button
              @click="showPasswordModal = true"
              class="px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"
            >
              修改密码
            </button>
          </div>

          <!-- 两步验证 -->
          <div class="flex items-center justify-between py-3">
            <div>
              <h4 class="text-sm font-medium text-secondary-900">两步验证</h4>
              <p class="text-sm text-secondary-500 mt-1">开启后需要手机验证码才能登录</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input 
                v-model="userInfo.twoFactorEnabled"
                type="checkbox" 
                class="sr-only peer"
              >
              <div class="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </div>
      </div>

      <!-- 账户操作 -->
      <div class="bg-white rounded-lg border border-secondary-200 p-6">
        <h3 class="text-lg font-medium text-secondary-900 mb-4">账户操作</h3>
        
        <div class="space-y-4">
          <!-- 注销账户 -->
          <div class="flex items-center justify-between py-3">
            <div>
              <h4 class="text-sm font-medium text-secondary-900">注销账户</h4>
              <p class="text-sm text-secondary-500 mt-1">注销后所有数据将被永久删除且无法恢复</p>
            </div>
            <button
              @click="handleDeleteAccount"
              class="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 transition-colors duration-200"
            >
              注销账户
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { $message } from '~/composables/shared/useGlobalMessage'

// 模拟用户数据
const userInfo = reactive({
  avatar: null,
  username: 'user_123456',
  phone: '138****8888',
  email: '',
  emailVerified: false,
  twoFactorEnabled: false
})

// 状态
const showPasswordModal = ref(false)

// 保存基本信息
const handleSaveBasicInfo = () => {
  // TODO: 调用API保存信息
  $message.success('信息已保存')
}

// 删除账户
const handleDeleteAccount = () => {
  if (confirm('确定要注销账户吗？此操作不可恢复！')) {
    // TODO: 调用API删除账户
    $message.info('账户注销功能开发中...')
  }
}
</script>

<style scoped>
/* 自定义开关样式已在模板中使用Tailwind实现 */
</style> 