package com.alan6.resume.service.impl;

import com.alan6.resume.common.exception.BusinessException;
import com.alan6.resume.common.utils.RedisUtils;
import com.alan6.resume.dto.resume.ResumeExportRequest;
import com.alan6.resume.dto.resume.ResumeExportResponse;
import com.alan6.resume.entity.ResumeExportQueue;
import com.alan6.resume.entity.ResumeExportRecords;
import com.alan6.resume.entity.Resumes;
import com.alan6.resume.mapper.ResumeExportQueueMapper;
import com.alan6.resume.mapper.ResumeExportRecordsMapper;
import com.alan6.resume.mapper.ResumesMapper;
import com.alan6.resume.service.IResumeExportService;
import com.alan6.resume.service.IBrowserRenderService;
import com.alan6.resume.service.IMinioService;
import com.alan6.resume.service.IPdfExportService;
import com.alan6.resume.service.IResumeExportCacheService;
import com.alan6.resume.service.IResumeDataService;
import com.alan6.resume.document.ResumeData;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 简历导出服务实现类
 * 
 * @description 实现简历导出的核心业务逻辑
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeExportServiceImpl implements IResumeExportService {

    /**
     * 导出队列Mapper
     */
    private final ResumeExportQueueMapper exportQueueMapper;

    /**
     * 导出记录Mapper
     */
    private final ResumeExportRecordsMapper exportRecordsMapper;

    /**
     * 简历Mapper
     */
    private final ResumesMapper resumesMapper;

    /**
     * Redis工具类
     */
    private final RedisUtils redisUtils;

    /**
     * 浏览器渲染服务
     */
    private final IBrowserRenderService browserRenderService;

    /**
     * MinIO服务
     */
    private final IMinioService minioService;

    /**
     * PDF导出服务
     */
    private final IPdfExportService pdfExportService;

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;

    /**
     * 简历导出缓存服务
     */
    private final IResumeExportCacheService exportCacheService;

    /**
     * 简历数据服务
     */
    private final IResumeDataService resumeDataService;

    /**
     * 前端域名
     */
    @Value("${app.frontend.domain:http://localhost:3000}")
    private String frontendDomain;

    /**
     * 导出文件过期时间（小时）
     */
    @Value("${app.export.expire-hours:24}")
    private Integer exportExpireHours;

    /**
     * 提交导出任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResumeExportResponse submitExportJob(ResumeExportRequest request) {
        log.info("提交导出任务，简历ID：{}，格式：{}", request.getResumeId(), request.getFormat());

        try {
            // 1. 验证简历是否存在
            Resumes resume = resumesMapper.selectById(request.getResumeId());
            if (resume == null || resume.getIsDeleted() == 1) {
                throw new BusinessException("简历不存在或已删除");
            }

            // 2. 验证权限
            if (!resume.getUserId().equals(request.getUserId())) {
                throw new BusinessException("无权限导出此简历");
            }

            // 3. 确保简历数据已保存到MongoDB
            if (request.getExportData() != null) {
                // 将导出数据保存到MongoDB
                ResumeData resumeData = convertToResumeData(request.getResumeId(), request.getExportData());
                String mongoDocumentId = resumeDataService.saveResumeData(resumeData);
                
                // 更新简历记录的MongoDB文档ID和最后保存时间
                resume.setMongoDocumentId(mongoDocumentId);
                resume.setLastSaveTime(LocalDateTime.now());
                resumesMapper.updateById(resume);
                
                log.info("简历数据已保存到MongoDB，文档ID：{}", mongoDocumentId);
            }

            // 4. 生成任务ID
            String jobId = generateJobId();

            // 5. 创建导出队列记录
            ResumeExportQueue exportJob = new ResumeExportQueue();
            exportJob.setJobId(jobId);
            exportJob.setUserId(request.getUserId());
            exportJob.setResumeId(request.getResumeId());
            exportJob.setExportFormat(request.getFormat());
            exportJob.setPriority(request.getPriority() != null ? request.getPriority() : 5);
            exportJob.setStatus("queued");
            exportJob.setAttempts(0);
            exportJob.setMaxAttempts(3);

            exportQueueMapper.insert(exportJob);
            
            // 6. 缓存简历数据到Redis
            if (request.getExportData() != null) {
                exportCacheService.cacheResumeData(request.getResumeId(), 
                    objectMapper.valueToTree(request.getExportData()), 3600);
            }
            
            // 7. 缓存导出选项到Redis
            if (request.getOptions() != null) {
                exportCacheService.cacheExportOptions(jobId, 
                    objectMapper.valueToTree(request.getOptions()), 3600);
            }
            log.info("导出任务已加入队列，任务ID：{}", jobId);

            // 8. 添加到Redis队列
            String queueKey = "export_queue:" + exportJob.getPriority();
            redisUtils.set(queueKey + ":" + jobId, jobId, 3600);

            // 9. 异步处理导出任务
            processExportJobAsync(jobId);

            // 10. 返回响应
            ResumeExportResponse response = new ResumeExportResponse();
            response.setJobId(jobId);
            response.setStatus("queued");
            response.setMessage("导出任务已提交，请稍后查询状态");

            return response;

        } catch (Exception e) {
            log.error("提交导出任务失败", e);
            throw new BusinessException("提交导出任务失败：" + e.getMessage());
        }
    }

    /**
     * 异步处理导出任务
     */
    @Async
    public void processExportJobAsync(String jobId) {
        try {
            // 延迟1秒开始处理，避免事务未提交
            Thread.sleep(1000);
            processExportJob(jobId);
        } catch (Exception e) {
            log.error("异步处理导出任务失败，任务ID：{}", jobId, e);
        }
    }

    /**
     * 处理导出任务
     */
    @Override
    public void processExportJob(String jobId) {
        log.info("开始处理导出任务，任务ID：{}", jobId);

        ResumeExportQueue exportJob = null;
        try {
            // 1. 获取任务信息
            exportJob = exportQueueMapper.selectByJobId(jobId);
            if (exportJob == null) {
                log.error("导出任务不存在，任务ID：{}", jobId);
                return;
            }

            // 2. 检查任务状态
            if (!"queued".equals(exportJob.getStatus())) {
                log.info("任务状态不是queued，跳过处理，当前状态：{}", exportJob.getStatus());
                return;
            }

            // 3. 更新任务状态为处理中
            exportJob.setStatus("processing");
            exportJob.setStartedAt(LocalDateTime.now());
            exportQueueMapper.updateById(exportJob);
            
            // 4. 增加Redis中的重试次数
            int retryCount = exportCacheService.incrementRetryCount(jobId);
            log.info("任务重试次数：{}", retryCount);

            // 5. 根据导出格式选择处理方式
            String downloadUrl;
            long fileSize;
            
            if ("pdf".equals(exportJob.getExportFormat())) {
                // 使用PDF导出服务
                String filePath = processPdfExport(exportJob);
                fileSize = new File(filePath).length();
                
                // 生成下载URL指向我们的下载端点
                String fileName = Paths.get(filePath).getFileName().toString();
                downloadUrl = String.format("http://localhost:9311/resume/export/download/%s", fileName);
                
                log.info("PDF导出完成，文件路径：{}，下载URL：{}", filePath, downloadUrl);
            } else {
                // 使用浏览器渲染服务
                String previewUrl = buildPreviewUrl(exportJob);
                log.info("预览URL：{}", previewUrl);
                
                byte[] fileData = browserRenderService.render(previewUrl, exportJob.getExportFormat());
                
                // 上传到MinIO
                String fileName = generateFileName(exportJob);
                downloadUrl = minioService.uploadFile(fileData, fileName, exportJob.getExportFormat());
                fileSize = fileData.length;
            }
            
            // 6. 创建导出记录
            saveExportRecord(exportJob, downloadUrl, fileSize);

            // 7. 更新任务状态为完成
            exportJob.setStatus("completed");
            exportJob.setCompletedAt(LocalDateTime.now());
            exportQueueMapper.updateDownloadInfoByJobId(jobId, downloadUrl, fileSize);

            log.info("导出任务处理完成，任务ID：{}，下载链接：{}", jobId, downloadUrl);

        } catch (Exception e) {
            log.error("处理导出任务失败，任务ID：{}", jobId, e);
            handleExportError(exportJob, e);
        }
    }

    /**
     * 处理PDF导出
     * 
     * @param exportJob 导出任务
     * @return 文件路径
     */
    private String processPdfExport(ResumeExportQueue exportJob) {
        log.info("开始PDF导出处理，任务ID：{}", exportJob.getJobId());
        
        try {
            // 从Redis获取缓存的简历数据
            JsonNode resumeData = exportCacheService.getCachedResumeData(exportJob.getResumeId());
            if (resumeData == null) {
                // 如果Redis中没有数据，从MongoDB获取
                log.info("Redis中没有缓存数据，从MongoDB获取简历数据");
                ResumeData mongoData = resumeDataService.getResumeDataByResumeId(exportJob.getResumeId().toString());
                if (mongoData == null) {
                    throw new RuntimeException("简历数据不存在，请先保存简历");
                }
                resumeData = objectMapper.valueToTree(mongoData);
                
                // 重新缓存到Redis
                exportCacheService.cacheResumeData(exportJob.getResumeId(), resumeData, 3600);
            }
            
            // 构建导出请求
            ResumeExportRequest request = new ResumeExportRequest();
            request.setResumeId(exportJob.getResumeId());
            request.setUserId(exportJob.getUserId());
            request.setFormat(exportJob.getExportFormat());
            request.setExportData(resumeData);
            
            // 调用PDF导出服务
            String filePath = pdfExportService.exportToPdf(request);
            
            log.info("PDF导出成功，文件路径：{}", filePath);
            return filePath;
            
        } catch (Exception e) {
            log.error("PDF导出失败，任务ID：{}", exportJob.getJobId(), e);
            throw new RuntimeException("PDF导出失败：" + e.getMessage(), e);
        }
    }

    /**
     * 生成任务ID
     */
    private String generateJobId() {
        return "export_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 将导出数据转换为ResumeData对象
     */
    private ResumeData convertToResumeData(Long resumeId, Object exportData) {
        try {
            ResumeData resumeData = new ResumeData();
            resumeData.setResumeId(resumeId.toString());
            resumeData.setCreateTime(LocalDateTime.now());
            resumeData.setUpdateTime(LocalDateTime.now());
            resumeData.setLastSaved(LocalDateTime.now());
            resumeData.setVersion(1L);
            
            // 将导出数据转换为JsonNode，然后设置到ResumeData
            JsonNode dataNode = objectMapper.valueToTree(exportData);
            if (dataNode.isObject()) {
                // 如果导出数据包含modules字段，转换为ResumeModule列表
                if (dataNode.has("modules")) {
                    List<ResumeData.ResumeModule> modules = objectMapper.convertValue(
                        dataNode.get("modules"), 
                        objectMapper.getTypeFactory().constructCollectionType(List.class, ResumeData.ResumeModule.class)
                    );
                    resumeData.setModules(modules);
                }
                
                // 设置全局设置
                if (dataNode.has("globalSettings")) {
                    resumeData.setGlobalSettings(
                        objectMapper.convertValue(dataNode.get("globalSettings"), ResumeData.GlobalSettings.class)
                    );
                }
                
                // 设置其他字段
                if (dataNode.has("title")) {
                    resumeData.setTitle(dataNode.get("title").asText());
                }
                if (dataNode.has("language")) {
                    resumeData.setLanguage(dataNode.get("language").asText());
                }
                if (dataNode.has("templateId")) {
                    resumeData.setTemplateId(dataNode.get("templateId").asText());
                }
                if (dataNode.has("status")) {
                    resumeData.setStatus(dataNode.get("status").asInt());
                }
            }
            
            return resumeData;
            
        } catch (Exception e) {
            log.error("转换导出数据失败", e);
            throw new RuntimeException("转换导出数据失败：" + e.getMessage());
        }
    }

    /**
     * 构建预览URL
     */
    private String buildPreviewUrl(ResumeExportQueue exportJob) {
        return String.format("%s/resume/preview/%s?export=true&jobId=%s",
                frontendDomain,
                exportJob.getResumeId(),
                exportJob.getJobId());
    }

    /**
     * 生成文件名
     */
    private String generateFileName(ResumeExportQueue exportJob) {
        String format = exportJob.getExportFormat().toLowerCase();
        String extension = "";
        
        switch (format) {
            case "pdf":
                extension = ".pdf";
                break;
            case "word":
                extension = ".docx";
                break;
            case "image":
                extension = ".png";
                break;
            default:
                extension = ".pdf";
        }
        
        return String.format("resume_%s_%s%s",
                exportJob.getResumeId(),
                System.currentTimeMillis(),
                extension);
    }

    /**
     * 保存导出记录
     */
    private void saveExportRecord(ResumeExportQueue exportJob, String downloadUrl, long fileSize) {
        ResumeExportRecords record = new ResumeExportRecords();
        record.setJobId(exportJob.getJobId());
        record.setUserId(exportJob.getUserId());
        record.setResumeId(exportJob.getResumeId());
        record.setExportFormat(exportJob.getExportFormat());
        record.setDownloadUrl(downloadUrl);
        record.setFileSize(fileSize);
        record.setExportPlatform("web");
        record.setIsDeleted((byte) 0);
        
        exportRecordsMapper.insert(record);
    }

    /**
     * 处理导出错误
     */
    private void handleExportError(ResumeExportQueue exportJob, Exception e) {
        if (exportJob == null) {
            return;
        }

        try {
            // 从Redis获取重试次数
            int retryCount = exportCacheService.getRetryCount(exportJob.getJobId());
            
            // 检查是否需要重试
            if (retryCount < exportJob.getMaxAttempts()) {
                // 重新加入队列
                exportJob.setStatus("queued");
                exportJob.setErrorMessage("第" + retryCount + "次尝试失败：" + e.getMessage());
                exportQueueMapper.updateById(exportJob);
                
                // 延迟重试
                String queueKey = "export_queue:" + exportJob.getPriority();
                redisUtils.set(queueKey + ":" + exportJob.getJobId(), exportJob.getJobId(), 3600);
                
                log.info("导出任务将重试，任务ID：{}，已尝试次数：{}", exportJob.getJobId(), retryCount);
            } else {
                // 标记为失败
                exportQueueMapper.updateErrorByJobId(exportJob.getJobId(), e.getMessage());
                // 清理Redis中的重试计数
                exportCacheService.resetRetryCount(exportJob.getJobId());
                log.error("导出任务失败，已达最大重试次数，任务ID：{}", exportJob.getJobId());
            }
        } catch (Exception ex) {
            log.error("处理导出错误时发生异常", ex);
        }
    }

    /**
     * 获取导出状态
     */
    @Override
    public ResumeExportResponse getExportStatus(String jobId) {
        log.info("查询导出状态，任务ID：{}", jobId);

        try {
            // 1. 查询任务信息
            ResumeExportQueue exportJob = exportQueueMapper.selectByJobId(jobId);
            if (exportJob == null) {
                throw new BusinessException("导出任务不存在");
            }

            // 2. 构建响应
            ResumeExportResponse response = new ResumeExportResponse();
            response.setJobId(jobId);
            response.setStatus(exportJob.getStatus());
            response.setProgress(calculateProgress(exportJob));

            // 3. 根据状态返回不同信息
            switch (exportJob.getStatus()) {
                case "completed":
                    // 查询导出记录获取下载链接
                    ResumeExportRecords record = exportRecordsMapper.selectOne(
                            new LambdaQueryWrapper<ResumeExportRecords>()
                                    .eq(ResumeExportRecords::getJobId, jobId)
                    );
                    if (record != null) {
                        response.setDownloadUrl(record.getDownloadUrl());
                        // 导出记录表中没有过期时间字段，使用创建时间+过期小时数计算
                        response.setExpiresAt(record.getCreateTime().plusHours(exportExpireHours));
                    }
                    response.setMessage("导出完成");
                    break;
                    
                case "processing":
                    response.setMessage("正在处理中，请稍候...");
                    break;
                    
                case "failed":
                    response.setMessage("导出失败：" + exportJob.getErrorMessage());
                    response.setErrorMessage(exportJob.getErrorMessage());
                    break;
                    
                default:
                    response.setMessage("等待处理中");
            }

            return response;

        } catch (Exception e) {
            log.error("查询导出状态失败，任务ID：{}", jobId, e);
            throw new BusinessException("查询导出状态失败：" + e.getMessage());
        }
    }

    /**
     * 计算进度
     */
    private Integer calculateProgress(ResumeExportQueue exportJob) {
        switch (exportJob.getStatus()) {
            case "queued":
                return 10;
            case "processing":
                return 50;
            case "completed":
                return 100;
            case "failed":
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 取消导出任务
     */
    @Override
    public boolean cancelExportJob(String jobId) {
        log.info("取消导出任务，任务ID：{}", jobId);

        try {
            ResumeExportQueue exportJob = exportQueueMapper.selectByJobId(jobId);
            if (exportJob == null) {
                return false;
            }

            // 只能取消未开始处理的任务
            if ("queued".equals(exportJob.getStatus())) {
                exportJob.setStatus("cancelled");
                exportJob.setCompletedAt(LocalDateTime.now());
                exportQueueMapper.updateById(exportJob);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("取消导出任务失败，任务ID：{}", jobId, e);
            return false;
        }
    }

    /**
     * 清理过期的导出文件
     */
    @Override
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupExpiredExports() {
        log.info("开始清理过期的导出文件");

        try {
            // 查询过期的导出记录（根据创建时间+过期小时数判断）
            LocalDateTime expireTime = LocalDateTime.now().minusHours(exportExpireHours);
            List<ResumeExportRecords> expiredRecords = exportRecordsMapper.selectList(
                    new LambdaQueryWrapper<ResumeExportRecords>()
                            .lt(ResumeExportRecords::getCreateTime, expireTime)
                            .eq(ResumeExportRecords::getIsDeleted, 0)
            );

            for (ResumeExportRecords record : expiredRecords) {
                try {
                    // 删除MinIO中的文件
                    if (record.getDownloadUrl() != null) {
                        minioService.deleteFile(extractFileNameFromUrl(record.getDownloadUrl()));
                    }

                    // 标记记录为已删除
                    record.setIsDeleted((byte) 1);
                    exportRecordsMapper.updateById(record);

                } catch (Exception e) {
                    log.error("清理导出文件失败，记录ID：{}", record.getId(), e);
                }
            }

            log.info("清理过期导出文件完成，处理记录数：{}", expiredRecords.size());

        } catch (Exception e) {
            log.error("清理过期导出文件失败", e);
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < url.length() - 1) {
            return url.substring(lastSlashIndex + 1);
        }
        return url;
    }

    /**
     * 获取用户的导出历史
     */
    @Override
    public Object getExportHistory(Long userId, Integer limit) {
        List<ResumeExportRecords> records = exportRecordsMapper.selectList(
                new LambdaQueryWrapper<ResumeExportRecords>()
                        .eq(ResumeExportRecords::getUserId, userId)
                        .eq(ResumeExportRecords::getIsDeleted, 0)
                        .orderByDesc(ResumeExportRecords::getCreateTime)
                        .last("LIMIT " + (limit != null ? limit : 10))
        );
        return records;
    }
} 