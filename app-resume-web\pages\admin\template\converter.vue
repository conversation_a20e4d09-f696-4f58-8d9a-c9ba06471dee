<template>
  <div class="template-converter">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <Icon name="document" class="title-icon" />
          模板转换器
        </h1>
        <p class="page-description">
          将HTML简历模板转换为Vue组件，支持单个和批量转换
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="header-actions">
        <button
          v-if="hasUploadedFiles"
          @click="resetConverter"
          class="btn btn-outline"
          :disabled="isProcessing"
        >
          <Icon name="undo" />
          重新开始
        </button>
        
        <button
          v-if="state.sessionId"
          @click="showHelpModal = true"
          class="btn btn-outline"
        >
          <Icon name="info" />
          使用帮助
        </button>
      </div>
    </div>

    <!-- 转换步骤指示器 -->
    <div class="converter-steps">
      <div class="step-indicator">
        <div
          v-for="(step, index) in steps"
          :key="step.key"
          class="step-item"
          :class="{
            'active': currentStep === index,
            'completed': currentStep > index,
            'disabled': currentStep < index
          }"
        >
          <div class="step-number">
            <Icon
              v-if="currentStep > index"
              name="check"
              class="step-icon completed"
            />
            <span v-else class="step-num">{{ index + 1 }}</span>
          </div>
          <div class="step-content">
            <h3 class="step-title">{{ step.title }}</h3>
            <p class="step-desc">{{ step.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 转换内容区域 -->
    <div class="converter-content">
      <!-- 步骤1: 文件上传 -->
      <div v-if="currentStep === 0" class="step-panel upload-panel">
        <div class="panel-header">
          <h2 class="panel-title">上传HTML文件</h2>
          <p class="panel-subtitle">选择需要转换的HTML简历模板文件</p>
        </div>
        
        <div class="upload-area">
          <!-- 拖拽上传区域 -->
          <div
            class="dropzone"
            :class="{
              'dragover': isDragOver,
              'uploading': state.uploading
            }"
            @drop="handleDrop"
            @dragover.prevent="isDragOver = true"
            @dragleave="isDragOver = false"
            @dragend="isDragOver = false"
          >
            <div class="dropzone-content">
              <Icon name="plus" class="upload-icon" />
              <h3 class="upload-title">拖拽文件到此处</h3>
              <p class="upload-subtitle">或点击选择文件</p>
              <input
                ref="fileInput"
                type="file"
                multiple
                accept=".html"
                @change="handleFileSelect"
                class="file-input"
              />
              <button
                @click="$refs.fileInput.click()"
                class="btn btn-primary upload-btn"
                :disabled="state.uploading"
              >
                <Icon name="plus" />
                选择HTML文件
              </button>
            </div>
          </div>
          
          <!-- 上传进度 -->
          <div v-if="state.uploading" class="upload-progress">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: state.uploadProgress + '%' }"
              ></div>
            </div>
            <p class="progress-text">
              上传中... {{ state.uploadProgress }}%
            </p>
          </div>
        </div>
        
        <!-- 规范说明 -->
        <div class="specification-info">
          <div class="info-header">
            <h3 class="info-title">
              <Icon name="info" />
              HTML规范要求
            </h3>
            <NuxtLink to="/admin/template/html-specification" class="btn btn-outline btn-sm">
              <Icon name="document" />
              查看详细规范
            </NuxtLink>
          </div>
          <div class="info-content">
            <div class="info-grid">
              <div class="info-item">
                <Icon name="check-circle" class="info-icon success" />
                <div>
                  <h4>必需元素</h4>
                  <p>包含DOCTYPE、html、head、body标签</p>
                </div>
              </div>
              <div class="info-item">
                <Icon name="check-circle" class="info-icon success" />
                <div>
                  <h4>模块标识</h4>
                  <p>使用data-module属性标识简历模块</p>
                </div>
              </div>
              <div class="info-item">
                <Icon name="check-circle" class="info-icon success" />
                <div>
                  <h4>字段绑定</h4>
                  <p>使用data-field属性绑定数据字段</p>
                </div>
              </div>
              <div class="info-item">
                <Icon name="check-circle" class="info-icon success" />
                <div>
                  <h4>样式要求</h4>
                  <p>包含CSS样式，使用相对单位</p>
                </div>
              </div>
            </div>
            <div class="info-footer">
              <p class="info-note">
                <Icon name="info" class="note-icon" />
                以上只是基本要求概览，完整的开发规范请点击上方按钮查看详细文档，包含所有字段定义、示例代码和验证清单。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 验证结果 -->
      <div v-if="currentStep === 1" class="step-panel validation-panel">
        <div class="panel-header">
          <h2 class="panel-title">验证结果</h2>
          <p class="panel-subtitle">检查HTML文件是否符合转换规范</p>
        </div>
        
        <!-- 验证统计 -->
        <div class="validation-stats">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ state.statistics.totalFiles }}</div>
              <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-item success">
              <div class="stat-number">{{ state.statistics.validFiles }}</div>
              <div class="stat-label">验证通过</div>
            </div>
            <div class="stat-item error">
              <div class="stat-number">{{ state.statistics.invalidFiles }}</div>
              <div class="stat-label">验证失败</div>
            </div>
            <div class="stat-item warning">
              <div class="stat-number">{{ state.statistics.totalWarnings }}</div>
              <div class="stat-label">警告数量</div>
            </div>
          </div>
        </div>
        
        <!-- 文件验证列表 -->
        <div class="validation-results">
          <div
            v-for="(result, index) in state.validationResults"
            :key="index"
            class="validation-item"
            :class="{
              'valid': result.valid,
              'invalid': !result.valid
            }"
          >
            <div class="validation-header">
              <div class="file-info">
                <Icon
                  :name="result.valid ? 'check-circle' : 'exclamation-triangle'"
                  :class="result.valid ? 'success' : 'error'"
                />
                <span class="file-name">{{ state.uploadedFiles[index] }}</span>
              </div>
              <div class="validation-status">
                <span
                  class="status-badge"
                  :class="result.valid ? 'success' : 'error'"
                >
                  {{ result.valid ? '通过' : '失败' }}
                </span>
              </div>
            </div>
            
            <!-- 错误和警告详情 -->
            <div v-if="!result.valid || result.warningCount > 0" class="validation-details">
              <!-- 错误列表 -->
              <div v-if="result.errorCount > 0" class="error-list">
                <h4 class="detail-title error">
                  <Icon name="exclamation-triangle" />
                  错误信息 ({{ result.errorCount }})
                </h4>
                <div
                  v-for="(error, errorIndex) in result.errors"
                  :key="errorIndex"
                  class="error-item"
                >
                  <div class="error-message">{{ error.message }}</div>
                  <div class="error-suggestion">
                    <Icon name="info" />
                    {{ error.suggestion }}
                  </div>
                </div>
              </div>
              
              <!-- 警告列表 -->
              <div v-if="result.warningCount > 0" class="warning-list">
                <h4 class="detail-title warning">
                  <Icon name="warning" />
                  警告信息 ({{ result.warningCount }})
                </h4>
                <div
                  v-for="(warning, warningIndex) in result.warnings"
                  :key="warningIndex"
                  class="warning-item"
                >
                  <div class="warning-message">{{ warning.message }}</div>
                  <div class="warning-suggestion">
                    <Icon name="info" />
                    {{ warning.suggestion }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 验证操作 -->
        <div class="validation-actions">
          <button
            @click="revalidateFiles"
            class="btn btn-outline"
            :disabled="state.validating"
          >
            <Icon name="undo" />
            重新验证
          </button>
          
          <button
            @click="generateReport"
            class="btn btn-outline"
            :disabled="!hasValidationResults"
          >
            <Icon name="document" />
            生成报告
          </button>
          
          <button
            @click="proceedToConversion"
            class="btn btn-primary"
            :disabled="state.statistics.validFiles === 0"
          >
            <Icon name="arrow-down" />
            开始转换
          </button>
        </div>
      </div>

      <!-- 步骤3: 转换设置 -->
      <div v-if="currentStep === 2" class="step-panel conversion-panel">
        <div class="panel-header">
          <h2 class="panel-title">转换设置</h2>
          <p class="panel-subtitle">配置转换选项和参数</p>
        </div>
        
        <div class="conversion-options">
          <!-- 转换类型选择 -->
          <div class="option-group">
            <h3 class="option-title">转换类型</h3>
            <div class="radio-group">
              <label class="radio-item">
                <input
                  v-model="conversionType"
                  type="radio"
                  value="single"
                  :disabled="state.statistics.validFiles <= 1"
                />
                <span class="radio-label">单个转换</span>
                <span class="radio-desc">逐个转换文件，便于调试</span>
              </label>
              <label class="radio-item">
                <input
                  v-model="conversionType"
                  type="radio"
                  value="batch"
                />
                <span class="radio-label">批量转换</span>
                <span class="radio-desc">一次性转换所有文件</span>
              </label>
            </div>
          </div>
          
          <!-- 转换选项 -->
          <div class="option-group">
            <h3 class="option-title">转换选项</h3>
            <div class="checkbox-group">
              <label class="checkbox-item">
                <input
                  v-model="conversionOptions.validateHtml"
                  type="checkbox"
                />
                <span class="checkbox-label">HTML验证</span>
                <span class="checkbox-desc">转换前验证HTML规范</span>
              </label>
              <label class="checkbox-item">
                <input
                  v-model="conversionOptions.generateFullComponent"
                  type="checkbox"
                />
                <span class="checkbox-label">完整组件</span>
                <span class="checkbox-desc">生成完整的Vue组件结构</span>
              </label>
              <label class="checkbox-item">
                <input
                  v-model="conversionOptions.formatOutput"
                  type="checkbox"
                />
                <span class="checkbox-label">格式化输出</span>
                <span class="checkbox-desc">格式化生成的代码</span>
              </label>
              <label class="checkbox-item">
                <input
                  v-model="conversionOptions.preserveComments"
                  type="checkbox"
                />
                <span class="checkbox-label">保留注释</span>
                <span class="checkbox-desc">保留原HTML中的注释</span>
              </label>
            </div>
          </div>
          
          <!-- 高级选项 -->
          <div class="option-group">
            <h3 class="option-title">高级选项</h3>
            <div class="advanced-options">
              <div class="option-row">
                <label class="option-label">Vue版本</label>
                <select v-model="conversionOptions.targetVueVersion" class="option-select">
                  <option value="vue3">Vue 3</option>
                  <option value="vue2">Vue 2</option>
                </select>
              </div>
              <div class="option-row">
                <label class="option-label">CSS处理</label>
                <select v-model="conversionOptions.cssProcessing" class="option-select">
                  <option value="scoped">Scoped</option>
                  <option value="module">Module</option>
                  <option value="global">Global</option>
                </select>
              </div>
              <div class="option-row">
                <label class="option-label">严格模式</label>
                <label class="toggle-switch">
                  <input
                    v-model="conversionOptions.strictMode"
                    type="checkbox"
                    class="toggle-input"
                  />
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 转换操作 -->
        <div class="conversion-actions">
          <button
            @click="currentStep = 1"
            class="btn btn-outline"
          >
            <Icon name="arrow-up" />
            返回验证
          </button>
          
          <button
            @click="startConversion"
            class="btn btn-primary"
            :disabled="state.converting"
          >
            <Icon name="check" />
            {{ state.converting ? '转换中...' : '开始转换' }}
          </button>
        </div>
      </div>

      <!-- 步骤4: 转换结果 -->
      <div v-if="currentStep === 3" class="step-panel results-panel">
        <div class="panel-header">
          <h2 class="panel-title">转换结果</h2>
          <p class="panel-subtitle">查看转换结果和生成的文件</p>
        </div>
        
        <!-- 转换统计 -->
        <div v-if="state.conversionResults" class="conversion-stats">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ state.conversionResults.statistics.totalFiles }}</div>
              <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-item success">
              <div class="stat-number">{{ state.conversionResults.statistics.successCount }}</div>
              <div class="stat-label">转换成功</div>
            </div>
            <div class="stat-item error">
              <div class="stat-number">{{ state.conversionResults.statistics.failedCount }}</div>
              <div class="stat-label">转换失败</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ Math.round(state.conversionResults.statistics.successRate) }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </div>
        </div>
        
        <!-- 转换结果列表 -->
        <div v-if="state.conversionResults" class="conversion-results">
          <div
            v-for="(result, index) in state.conversionResults.results"
            :key="index"
            class="result-item"
            :class="{
              'success': result.status === 'SUCCESS',
              'failed': result.status === 'FAILED'
            }"
          >
            <div class="result-header">
              <div class="file-info">
                <Icon
                  :name="result.status === 'SUCCESS' ? 'check-circle' : 'exclamation-triangle'"
                  :class="result.status === 'SUCCESS' ? 'success' : 'error'"
                />
                <div class="file-names">
                  <span class="source-name">{{ result.sourceFileName }}</span>
                  <Icon name="arrow-down" class="arrow-icon" />
                  <span class="target-name">{{ result.targetFileName }}</span>
                </div>
              </div>
              <div class="result-status">
                <span
                  class="status-badge"
                  :class="result.status === 'SUCCESS' ? 'success' : 'error'"
                >
                  {{ result.status === 'SUCCESS' ? '成功' : '失败' }}
                </span>
              </div>
            </div>
            
            <!-- 转换详情 -->
            <div v-if="result.details" class="result-details">
              <div class="detail-grid">
                <div class="detail-item">
                  <span class="detail-label">模块数量</span>
                  <span class="detail-value">{{ result.details.moduleCount }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">字段数量</span>
                  <span class="detail-value">{{ result.details.fieldCount }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">代码行数</span>
                  <span class="detail-value">{{ result.details.vueCodeLines }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">处理时间</span>
                  <span class="detail-value">{{ result.processingTime }}ms</span>
                </div>
              </div>
            </div>
            
            <!-- 错误信息 -->
            <div v-if="result.error" class="result-error">
              <Icon name="exclamation-triangle" class="error-icon" />
              <span class="error-message">{{ result.error }}</span>
            </div>
          </div>
        </div>
        
        <!-- 结果操作 -->
        <div class="results-actions">
          <button
            @click="downloadResults"
            class="btn btn-primary"
            :disabled="!state.conversionResults || state.conversionResults.statistics.successCount === 0"
          >
            <Icon name="download" />
            下载结果
          </button>
          
          <button
            @click="viewResults"
            class="btn btn-outline"
            :disabled="!state.conversionResults"
          >
            <Icon name="eye" />
            查看详情
          </button>
          
          <button
            @click="resetConverter"
            class="btn btn-outline"
          >
            <Icon name="undo" />
            重新转换
          </button>
        </div>
      </div>
    </div>
    
    <!-- 帮助弹窗 -->
    <div v-if="showHelpModal" class="modal-overlay" @click="showHelpModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">使用帮助</h3>
          <button @click="showHelpModal = false" class="modal-close">
            <Icon name="close" />
          </button>
        </div>
        <div class="modal-body">
          <div class="help-content">
            <h4>HTML规范要求</h4>
            <ul>
              <li>文件必须包含完整的HTML文档结构</li>
              <li>使用data-module属性标识简历模块</li>
              <li>使用data-field属性绑定数据字段</li>
              <li>CSS样式建议使用相对单位</li>
            </ul>
            
            <h4>转换流程</h4>
            <ol>
              <li>上传HTML文件</li>
              <li>验证文件规范性</li>
              <li>配置转换选项</li>
              <li>执行转换并查看结果</li>
            </ol>
            
            <h4>常见问题</h4>
            <ul>
              <li>确保HTML文件编码为UTF-8</li>
              <li>检查data属性是否正确设置</li>
              <li>验证CSS选择器是否冲突</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useHtmlConverterService } from '~/composables/admin/useHtmlConverterService'
import { useMessage } from '~/utils/message'

// 页面元数据
definePageMeta({
  layout: 'admin',
  middleware: 'admin-simple'
})

// 服务和工具
const converterService = useHtmlConverterService()
const message = useMessage()

// 响应式状态
const { state, hasUploadedFiles, hasValidationResults, hasConversionResults, isProcessing } = converterService

// 页面状态
const currentStep = ref(0)
const isDragOver = ref(false)
const showHelpModal = ref(false)
const fileInput = ref(null)

// 转换配置
const conversionType = ref('batch')
const conversionOptions = reactive({
  validateHtml: true,
  generateFullComponent: true,
  preserveComments: false,
  formatOutput: true,
  generateTypes: false,
  targetVueVersion: 'vue3',
  cssProcessing: 'scoped',
  strictMode: false
})

// 步骤配置
const steps = [
  {
    key: 'upload',
    title: '上传文件',
    description: '选择HTML模板文件'
  },
  {
    key: 'validate',
    title: '验证规范',
    description: '检查文件规范性'
  },
  {
    key: 'configure',
    title: '转换设置',
    description: '配置转换选项'
  },
  {
    key: 'results',
    title: '查看结果',
    description: '获取转换结果'
  }
]

/**
 * 处理文件拖拽放置
 */
const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer.files
  if (files.length > 0) {
    handleFiles(files)
  }
}

/**
 * 处理文件选择
 */
const handleFileSelect = (event) => {
  const files = event.target.files
  if (files.length > 0) {
    handleFiles(files)
  }
}

/**
 * 处理文件上传
 */
const handleFiles = async (files) => {
  console.log('处理文件上传:', files.length)
  
  // 验证文件类型
  const validation = converterService.validateFileTypes(files)
  if (validation.invalidFiles.length > 0) {
    message.warning(validation.message)
  }
  
  if (validation.validFiles.length === 0) {
    message.error('没有有效的HTML文件')
    return
  }
  
  // 上传文件
  const result = await converterService.uploadHtmlFiles(validation.validFiles)
  
  if (result.success) {
    // 进入下一步
    currentStep.value = 1
  }
}

/**
 * 重新验证文件
 */
const revalidateFiles = async () => {
  if (!state.sessionId || !state.uploadedFiles.length) {
    message.error('没有文件需要验证')
    return
  }
  
  const result = await converterService.validateHtmlFiles(state.sessionId, state.uploadedFiles)
  
  if (result.success) {
    message.success('重新验证完成')
  }
}

/**
 * 生成验证报告
 */
const generateReport = async () => {
  if (!state.sessionId || !state.uploadedFiles.length) {
    message.error('没有验证结果可生成报告')
    return
  }
  
  const result = await converterService.generateValidationReport(state.sessionId, state.uploadedFiles)
  
  if (result.success) {
    // 创建下载链接
    const blob = new Blob([result.data], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `validation-report-${state.sessionId}.md`
    a.click()
    URL.revokeObjectURL(url)
    
    message.success('验证报告已生成')
  }
}

/**
 * 进入转换设置
 */
const proceedToConversion = () => {
  if (state.statistics.validFiles === 0) {
    message.error('没有通过验证的文件')
    return
  }
  
  currentStep.value = 2
}

/**
 * 开始转换
 */
const startConversion = async () => {
  if (!state.taskId || !state.uploadedFiles.length) {
    message.error('没有文件需要转换')
    return
  }
  
  // 只转换验证通过的文件
  const validFiles = state.uploadedFiles.filter((_, index) => {
    const validation = state.validationResults[index]
    return validation && validation.valid
  })
  
  if (validFiles.length === 0) {
    message.error('没有通过验证的文件可以转换')
    return
  }
  
  // 创建转换请求
  const request = converterService.createConversionRequest(
    conversionType.value,
    validFiles,
    conversionOptions
  )
  
  // 执行转换
  const result = await converterService.convertHtmlFiles(request)
  
  if (result.success) {
    // 进入结果页面
    currentStep.value = 3
  }
}

/**
 * 下载转换结果
 */
const downloadResults = async () => {
  if (!state.taskId) {
    message.error('没有转换结果可下载')
    return
  }
  
  // 使用服务中的下载方法
  await converterService.downloadResults(state.taskId)
}

/**
 * 查看转换结果详情
 */
const viewResults = async () => {
  if (!state.taskId) {
    message.error('没有转换结果可查看')
    return
  }
  
  const result = await converterService.getConversionResult(state.taskId)
  
  if (result.success) {
    // 显示结果详情
    console.log('转换结果详情:', result.data)
    message.info('请查看控制台获取详细信息')
  }
}

/**
 * 重置转换器
 */
const resetConverter = () => {
  converterService.resetState()
  currentStep.value = 0
  conversionType.value = 'batch'
  
  // 重置文件输入
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  
  message.info('转换器已重置')
}

// 页面初始化
onMounted(() => {
  console.log('模板转换器页面已加载')
})
</script>

<style scoped>
.template-converter {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.title-icon {
  color: #667eea;
  font-size: 2.5rem;
}

.page-description {
  color: #718096;
  font-size: 1.1rem;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* 步骤指示器 */
.converter-steps {
  margin-bottom: 2rem;
}

.step-indicator {
  display: flex;
  justify-content: space-between;
  background: white;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.step-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  position: relative;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -50%;
  top: 50%;
  width: 100%;
  height: 2px;
  background: #e2e8f0;
  transform: translateY(-50%);
  z-index: 1;
}

.step-item.completed:not(:last-child)::after {
  background: #48bb78;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: #e2e8f0;
  color: #718096;
  font-weight: 600;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.step-item.active .step-number {
  background: #667eea;
  color: white;
}

.step-item.completed .step-number {
  background: #48bb78;
  color: white;
}

.step-icon.completed {
  font-size: 1.25rem;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.step-desc {
  color: #718096;
  font-size: 0.9rem;
  margin: 0;
}

.step-item.active .step-title {
  color: #667eea;
}

.step-item.completed .step-title {
  color: #48bb78;
}

/* 转换内容区域 */
.converter-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.step-panel {
  padding: 2rem;
}

.panel-header {
  margin-bottom: 2rem;
}

.panel-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.panel-subtitle {
  color: #718096;
  font-size: 1rem;
  margin: 0;
}

/* 上传区域 */
.upload-area {
  margin-bottom: 2rem;
}

.dropzone {
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.dropzone:hover,
.dropzone.dragover {
  border-color: #667eea;
  background: #f7fafc;
}

.dropzone.uploading {
  border-color: #48bb78;
  background: #f0fff4;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 4rem;
  color: #667eea;
}

.upload-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.upload-subtitle {
  color: #718096;
  margin: 0;
}

.file-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.upload-btn {
  margin-top: 0.5rem;
}

.upload-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: #718096;
  font-size: 0.9rem;
  margin: 0.5rem 0 0 0;
}

/* 规范说明 */
.specification-info {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.info-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.info-footer {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.info-note {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  color: #718096;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.5;
}

.note-icon {
  color: #667eea;
  font-size: 1rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.info-icon {
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.info-icon.success {
  color: #48bb78;
}

.info-item h4 {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.info-item p {
  color: #718096;
  font-size: 0.85rem;
  margin: 0;
}

/* 验证结果 */
.validation-stats {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-item {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.stat-item.success {
  border-color: #48bb78;
  background: #f0fff4;
}

.stat-item.error {
  border-color: #f56565;
  background: #fffafa;
}

.stat-item.warning {
  border-color: #ed8936;
  background: #fffaf0;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.stat-item.success .stat-number {
  color: #48bb78;
}

.stat-item.error .stat-number {
  color: #f56565;
}

.stat-item.warning .stat-number {
  color: #ed8936;
}

.stat-label {
  color: #718096;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 验证项目 */
.validation-results {
  margin-bottom: 2rem;
}

.validation-item {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.validation-item.valid {
  border-color: #48bb78;
  background: #f0fff4;
}

.validation-item.invalid {
  border-color: #f56565;
  background: #fffafa;
}

.validation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.file-info .success {
  color: #48bb78;
}

.file-info .error {
  color: #f56565;
}

.file-name {
  font-weight: 500;
  color: #1a202c;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.success {
  background: #48bb78;
  color: white;
}

.status-badge.error {
  background: #f56565;
  color: white;
}

.validation-details {
  border-top: 1px solid #e2e8f0;
  padding: 1.5rem;
}

.detail-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.detail-title.error {
  color: #f56565;
}

.detail-title.warning {
  color: #ed8936;
}

.error-item,
.warning-item {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  border-left: 4px solid #f56565;
}

.warning-item {
  border-left-color: #ed8936;
}

.error-message,
.warning-message {
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.error-suggestion,
.warning-suggestion {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #718096;
  font-size: 0.9rem;
}

/* 转换选项 */
.conversion-options {
  margin-bottom: 2rem;
}

.option-group {
  margin-bottom: 2rem;
}

.option-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.radio-group,
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.radio-item,
.checkbox-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.radio-item:hover,
.checkbox-item:hover {
  background: #f7fafc;
}

.radio-item input,
.checkbox-item input {
  margin-top: 0.125rem;
}

.radio-label,
.checkbox-label {
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.radio-desc,
.checkbox-desc {
  color: #718096;
  font-size: 0.9rem;
}

.advanced-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.option-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
}

.option-label {
  font-weight: 500;
  color: #1a202c;
}

.option-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  color: #1a202c;
  font-size: 0.9rem;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e2e8f0;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-input:checked + .toggle-slider {
  background-color: #667eea;
}

.toggle-input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* 转换结果 */
.conversion-stats {
  margin-bottom: 2rem;
}

.conversion-results {
  margin-bottom: 2rem;
}

.result-item {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-item.success {
  border-color: #48bb78;
  background: #f0fff4;
}

.result-item.failed {
  border-color: #f56565;
  background: #fffafa;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
}

.file-names {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.source-name {
  color: #718096;
}

.arrow-icon {
  color: #cbd5e0;
}

.target-name {
  color: #1a202c;
  font-weight: 500;
}

.result-details {
  border-top: 1px solid #e2e8f0;
  padding: 1.5rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  color: #718096;
  font-size: 0.8rem;
  font-weight: 500;
}

.detail-value {
  color: #1a202c;
  font-weight: 600;
}

.result-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: #fed7d7;
  border-top: 1px solid #f56565;
}

.error-icon {
  color: #f56565;
}

.error-message {
  color: #c53030;
  font-weight: 500;
}

/* 操作按钮 */
.validation-actions,
.conversion-actions,
.results-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-outline {
  background: white;
  color: #667eea;
  border: 1px solid #667eea;
}

.btn-outline:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

/* 帮助弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f7fafc;
  color: #1a202c;
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
}

.help-content h4 {
  color: #1a202c;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
}

.help-content h4:first-child {
  margin-top: 0;
}

.help-content ul,
.help-content ol {
  color: #718096;
  padding-left: 1.5rem;
}

.help-content li {
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-converter {
    padding: 1rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .step-indicator {
    flex-direction: column;
    gap: 1rem;
  }
  
  .step-item::after {
    display: none;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .validation-actions,
  .conversion-actions,
  .results-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95%;
    margin: 1rem;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-panel {
  animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.dropzone.dragover {
  animation: pulse 1s infinite;
}
</style> 