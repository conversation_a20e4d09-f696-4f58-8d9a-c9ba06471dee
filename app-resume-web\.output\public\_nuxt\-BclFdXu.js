import{D}from"./iG63TLyk.js";import{u as h}from"./12cCxHYh.js";import{e as k,r as c,k as g,i as S,c as m,a as o,b as I,d,t as a,F as C,g as $,o as b}from"./CURHyiUL.js";import{_ as F}from"./DlAUqK2U.js";import"./D7AOVZt6.js";const P={class:"test-download-page"},B={class:"container mx-auto px-4 py-8"},E={class:"mb-8"},L={class:"mb-8"},N={class:"bg-gray-100 p-4 rounded-lg"},T={class:"mb-8"},V={class:"space-x-4"},z=["disabled"],J=["disabled"],M=["disabled"],R={class:"mb-8"},U={class:"bg-black text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm"},W={class:"text-gray-500"},j={__name:"test-download",setup(q){k({title:"下载功能测试 - 火花简历",description:"测试简历下载功能"});const p=h(),e=c(!1),l=c("准备就绪"),i=c([]),v=g(()=>p.downloadProgress.value),x=g(()=>p.currentJobId.value),r=s=>{const n=new Date().toLocaleTimeString();i.value.push({time:n,message:s}),i.value.length>100&&i.value.shift(),console.log(`[${n}] ${s}`)},u=async s=>{r(`用户选择下载格式: ${s}`);try{e.value=!0,l.value=`正在生成${s.toUpperCase()}文档...`;const t={basicInfo:{name:"张三",title:"前端开发工程师",email:"<EMAIL>",phone:"13800138000"},workExperience:[{company:"阿里巴巴",position:"高级前端工程师",duration:"2020-2023",description:"负责电商核心业务系统的前端开发"}]},n={fontFamily:"system-ui",fontSize:"14px",textColor:"#333333",margins:{top:60,bottom:60,side:60}};await p.downloadResume({resumeId:"1001",format:s,resumeData:t,settings:n}),r(`${s.toUpperCase()}下载成功`),l.value="下载完成"}catch(t){r(`下载失败: ${t.message}`),l.value="下载失败"}finally{setTimeout(()=>{e.value=!1,l.value="准备就绪"},2e3)}},w=()=>{u("pdf")},f=()=>{u("word")},_=()=>{u("image")};return S(()=>{r("下载功能测试页面已加载")}),(s,t)=>(b(),m("div",P,[o("div",B,[t[8]||(t[8]=o("h1",{class:"text-3xl font-bold mb-8"},"下载功能测试",-1)),o("div",E,[t[0]||(t[0]=o("h2",{class:"text-xl font-semibold mb-4"},"下载下拉菜单测试",-1)),I(D,{"is-exporting":e.value,"export-status":l.value,onDownload:u},null,8,["is-exporting","export-status"])]),o("div",L,[t[5]||(t[5]=o("h2",{class:"text-xl font-semibold mb-4"},"下载状态",-1)),o("div",N,[o("p",null,[t[1]||(t[1]=o("strong",null,"是否正在导出：",-1)),d(a(e.value),1)]),o("p",null,[t[2]||(t[2]=o("strong",null,"导出状态：",-1)),d(a(l.value),1)]),o("p",null,[t[3]||(t[3]=o("strong",null,"下载进度：",-1)),d(a(v.value)+"%",1)]),o("p",null,[t[4]||(t[4]=o("strong",null,"当前任务ID：",-1)),d(a(x.value||"无"),1)])])]),o("div",T,[t[6]||(t[6]=o("h2",{class:"text-xl font-semibold mb-4"},"直接测试",-1)),o("div",V,[o("button",{onClick:w,disabled:e.value,class:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"}," 测试PDF下载 ",8,z),o("button",{onClick:f,disabled:e.value,class:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"}," 测试Word下载 ",8,J),o("button",{onClick:_,disabled:e.value,class:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"}," 测试图片下载 ",8,M)])]),o("div",R,[t[7]||(t[7]=o("h2",{class:"text-xl font-semibold mb-4"},"操作日志",-1)),o("div",U,[(b(!0),m(C,null,$(i.value,(n,y)=>(b(),m("div",{key:y,class:"mb-1"},[o("span",W,"["+a(n.time)+"]",1),d(" "+a(n.message),1)]))),128))])])])]))}},Q=F(j,[["__scopeId","data-v-06afadd1"]]);export{Q as default};
